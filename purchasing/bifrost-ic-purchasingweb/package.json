{"name": "bifrost-ic", "version": "0.1.0", "private": true, "scripts": {"serve:assy": "set VUE_APP_MODEL=ASSY&& vue-cli-service serve", "build:assy": "set VUE_APP_MODEL=ASSY&& vue-cli-service build", "serve": "vue-cli-service serve", "serve:pa-micro": "cross-env VUE_APP_IS_PA_MICRO=YES VUE_APP_SYS_NAME=bifrost-ic VUE_APP_BASE_API= vue-cli-service serve", "build:pa-micro": "cross-env VUE_APP_IS_PA_MICRO=YES VUE_APP_SYS_NAME=bifrost-ic VUE_APP_BASE_API=/bifrost-ic vue-cli-service build", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "clear": "rimraf node_modules/.cache/"}, "dependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@form-create/element-ui": "^2.5.11", "@fortawesome/fontawesome-free": "^6.7.2", "@jiaminghi/data-view": "^2.10.0", "@packy-tang/vue-tinymce": "^1.1.2", "@tailwindcss/postcss7-compat": "^2.2.17", "@tinymce/tinymce-vue": "^3.2.8", "@wchbrad/vue-easy-tree": "^1.0.13", "@ztree/ztree_v3": "^3.5.46", "autoprefixer": "^9.8.8", "axios": "^0.18.0", "core-js": "^2.6.9", "d3": "^7.9.0", "docx-preview": "^0.1.14", "diff": "^8.0.2", "echarts": "^5.3.2", "echarts-wordcloud": "^2.1.0", "element-china-area-data": "^6.1.0", "element-resize-detector": "^1.2.3", "element-ui": "^2.15.14", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "isomorphic-fetch": "^2.2.1", "jquery": "^3.6.0", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "jsencrypt": "^3.0.0-rc.1", "jspdf": "^2.5.1", "jsplumb": "^2.15.6", "localforage": "^1.10.0", "lowdb": "^1.0.0", "luckyexcel": "^1.0.1", "mathjs": "^7.5.1", "moment": "^2.29.3", "nprogress": "^0.2.0", "ofd-view": "^0.1.57", "parser_x.js": "^1.1.5", "pdfjs-dist": "^2.2.228", "pinyin-pro": "^3.26.0", "postcss": "^7.0.39", "postcss-selector-namespace": "^3.0.1", "print-js": "^1.6.0", "qs": "^6.10.2", "sockjs-client": "^1.6.1", "sortablejs": "^1.15.2", "stompjs": "^2.3.3", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "tinymce": "^5.1.0", "vue": "^2.5.16", "vue-giant-tree": "^0.1.5", "vue-native-websocket": "^2.0.15", "vue-router": "3.1.6", "vue-svgicon": "^3.2.9", "vuedraggable": "^2.24.3", "vuescroll": "^4.16.0", "vuex": "^3.0.1", "vxe-table": "3.5.6", "vxe-table-plugin-element": "^1.11.2", "xe-utils": "3", "xlsx": "^0.16.8"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.18.6", "@vue/cli-plugin-babel": "^3.0.0-beta.15", "@vue/cli-plugin-eslint": "^3.0.0-beta.15", "@vue/cli-service": "^3.0.0-beta.15", "babel-polyfill": "^6.26.0", "cross-env": "^7.0.3", "es6-promise": "^4.2.8", "happypack": "^5.0.1", "hard-source-webpack-plugin": "^0.13.1", "less": "^3.0.4", "less-loader": "^4.1.0", "mockjs": "^1.1.0", "node-sass": "^4.11.0", "normalize.css": "^8.0.1", "optimize-css-assets-webpack-plugin": "^6.0.1", "progress-bar-webpack-plugin": "^2.1.0", "resize-observer-polyfill": "^1.5.1", "sass-loader": "^7.1.0", "script-loader": "^0.7.2", "semver": "^6.3.0", "svg-sprite-loader": "^4.1.3", "terser-webpack-plugin": "^4.2.3", "vue-template-compiler": "^2.5.16"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}