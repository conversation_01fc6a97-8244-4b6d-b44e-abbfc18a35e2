流程：
* 业务绑定
* 任务指派
* 版本管理
* 流程运行时（申请和审核列表）
* 参数设置
- 缩放
- 整体拖动
- 区域限制
- 标尺
- 连线名称
- 友好浮动提示
- 居中对齐
- 等宽
- 键盘热键 CTRL+Z等
- 当前流程图名称显示优化
- 绑定流程后，模拟检测正确性
- 简易流程，即实际的审核路径
- 未保存提示
- 统一代办
- 业务绑定树添加过滤


自定义表单：
#保存（设计，编辑，查看）
#单元格设置：数据绑定，合并单元格边框之类，可编辑设置，参照

* 网格线
* 单元格：设置可编辑，数据绑定（显示隐藏），注释，边框，合并单元格，参照
* 行高列宽
* 指标处理，费用标准
* 缩放
* 设置文本
* 条形码，二维码
* 要素定义别名，是否隐藏
- 要素默认值

- 前端列表显示列设置
- 更多设置：导出设置，导入模板设置
- 行高列宽
- 增加行列
- 对齐
- 自动换行
- 打印
- 导入导出（excel和自定义格式）
- 分页线（水平垂直）
- 设置固定行列
- 公式
- 图表
- 插入行列
- 追加行列
- 背景图
- 多页签
- 插入图片
- 设置行列个数（简易操作：点击某个单元格，直接按单元设置行列数）
- 版本支持，单元测时对多版本的相关处理，比如查询表单时候

？公示
？是否可以强制列数量




mybatis plus 执行sql
废话不多。

mapper

@Select("${sqlStr}")
List<MyTest> dynamicSql(@Param("sqlStr")String sql);


测试

复制代码
@Autowired
    MyTestMapper myTestMapper;

    @Test
    public void DynamicSql()
    {
        String sql="select * from myTest";
        List<MyTest> list =  myTestMapper.dynamicSql(sql);
    }
复制代码
