/*
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-15 11:53:42
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-05-08 14:12:40
 */
module.exports = {
  plugins: {
    autoprefixer: {},
    'postcss-selector-namespace': {
      namespace (css) {
        // element-ui的样式不需要添加命名空间
        if (
          css.includes("normalize.css") ||
          css.includes("element-ui") ||
          css.includes("elereset.scss") ||
          css.includes("Layout") ||
          css.includes("SvgIcon") ||
          css.includes("components") ||
          css.includes("common") ||
          css.includes("jz-vue-ui") ||
          css.includes("-dialog") ||
          css.includes("bifrost-ic.scss") ||
          css.includes("bifrost-ic-namespace.scss")
        )
          return "";
        // console.log(css)
        return `#bifrost-ic-app`
      }
      }
  }
}

