const path = require('path')
const webpack = require('webpack')
const { name } = require('./package')
const Happypack = require('happypack')
const ProgressBarPlugin = require("progress-bar-webpack-plugin");
const TerserPlugin = require('terser-webpack-plugin')
const chalk = require("chalk");
const HardSourceWebpackPlugin = require('hard-source-webpack-plugin')
function resolve(dir) {
  return path.join(__dirname, dir)
}

// vue.config.js
module.exports = {
  lintOnSave: false,
  parallel: require('os').cpus().length > 1, 
  publicPath: process.env.NODE_ENV === 'production' ? `/${name}` : '/',
  assetsDir: '',
  productionSourceMap: false, // 打包时去除.map文件 减少打包体积
  devServer: {
    port: 8555,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
    // proxy: { // 跨域设置 都在portal配置
    // }
    // open: true
  },
  css: {
    loaderOptions: {
      postcss: {
        plugins: [
            require("tailwindcss"), 
            require("autoprefixer")
        ],
      },
    },
  },
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
      // 自动加载模块，类似全局参数
      new webpack.ProvidePlugin({
        axios: 'axios',
        $: 'jquery',
        jquery: 'jquery',
        jQuery: 'jquery',
        'window.jQuery': 'jquery'
      }),
      // 进度条
      new ProgressBarPlugin({
        format: `  :msg [:bar] ${chalk.green.bold(":percent")} (:elapsed s)`,
      }),
      // new Happypack({
      //   loaders: ['babel-loader', 'vue-loader', 'url-loader'],
      //   cache: true,
      //   threads: 20
      // }),
      new HardSourceWebpackPlugin()
    ],
    output: {
      // 把子应用打包成 umd 库格式
      // library: `${name}-[name]`,
      library: `${name}`,
      libraryTarget: 'umd',
      jsonpFunction: `webpackJsonp_${name}`
    }
    // devtool: 'source-map'
  },

  chainWebpack: config => {
    // 移除 prefetch 插件 避免vue-cli3.0 的懒加载被prefetch影响失效
    config.plugins.delete('prefetch')
    config.plugins.delete('prefetch-index')
    config.plugins.delete('prefetch-admin')
    // 对svg的处理 具体实现 <svg-icon icon-class="nav"></svg-icon> 有样式要求：<svg-icon class-name="className"  icon-class="nav"></svg-icon>
    config.module.rules.delete('svg') // 重点:删除默认配置中处理svg,
    // const svgRule = config.module.rule('svg')
    // // svgRule.uses.clear()
    config.module
      .rule('svg-sprite-loader')
      .test(/\.svg$/)
      .include
    // .add(resolve('src/multi/icons')) //处理svg目录
      .add(resolve('src/icons')) // 处理svg目录
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
    config.module
      .rule('replace-dialog-class-loader')
      .test(/\.js$/)
      .use('./src/loader/replace-dialog-class-loader.js')
      .loader('./src/loader/replace-dialog-class-loader.js')
      .end()
    // 子应用图标处理
    const fontsRule = config.module.rule('fonts')
    fontsRule.uses.clear()
    fontsRule
      .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/i)
      .use('file-loader')
      .loader('url-loader')
      .options({
        fallback: {
          loader: 'file-loader',
          options: {
          // name: `${ process.env.NODE_ENV === 'production' ? name + '/' : name + '/'}fonts/[name].[hash:8].[ext]`
            name: `${name}'/fonts/[name].[hash:8].[ext]`
          }
        }
      })
    config.module
      .rule('font-awesome')
      .test(/\.svg$/)
      .exclude
      .add(resolve('src/icons'))
      .end()
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: 'img/[name].[hash:8].[ext]'
      })
    if (process.env.NODE_ENV === 'production') {
      config.optimization.usedExports = true;
      config.optimization.minimize = true;
      config.optimization.minimizer = [
        new TerserPlugin({
          terserOptions: {
            parse: {
              ecma: 8,
            },
            compress: {
              ecma: 5,
              warnings: false,
              comparisons: false,
              inline: 2,
            },
            mangle:true,
            output: {
              ecma: 5,
              comments: false,
              ascii_only: true,
            },
            extractComments: false,
          },
          parallel: true,
          cache: true,
          sourceMap: process.env.NODE_ENV !== 'production',
        }),
      ]
      // 公共代码抽离
      config.optimization.splitChunks({
        chunks: 'all', // initial async all
        maxInitialRequests: 3, // 最大初始化请求数
        maxAsyncRequests: 5, // 按需加载时最大的并行请求量
        minSize: 30000, // 形成一个新代码块最小的体积
        automaticNameDelimiter: '~', // 打包分隔符
        name: true,
        cacheGroups: {
          common: {
            chunks: 'all',
            test: /[\\/]src[\\/]js[\\/]/,
            name: 'common',
            minChunks: 2,
            priority: 60
          },
          vendors: {
            // vendor 是导入的 npm 包
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'initial',
            maxSize: 600000,
            maxInitialRequests: 20,
            priority: 2,
            reuseExistingChunk: true,
            enforce: true
          },
          vxeTable: {
            name: 'vxeTable',
            test: /[\\/]node_modules[\\/](vxe-table)[\\/]/,
            chunks: 'all',
            enforce: true,
            priority: 30
          },
          echarts: {
            name: 'echarts',
            test: /[\\/]node_modules[\\/](echarts)[\\/]/,
            chunks: 'all',
            enforce: true,
            priority: 30
          },
          elementUI: {
            name: 'elementUI',
            test: /[\\/]node_modules[\\/](element-ui)[\\/]/,
            chunks: 'all',
            enforce: true,
            priority: 30
          },
          xlsx: {
            name: 'xlsx',
            test: /[\\/]node_modules[\\/](xlsx)[\\/]/,
            chunks: 'all',
            enforce: true,
            priority: 30
          },
        }
      })
    }
  }
}
