<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>高紧急度火苗-动态</title>
    <defs>
        <radialGradient id="flame-gradient-high-outer" cx="50%" cy="60%" r="75%" fx="50%" fy="80%">
            <stop offset="0%" stop-color="#FF6E40" />
            <stop offset="60%" stop-color="#FF3D00" />
            <stop offset="100%" stop-color="#DD2C00" />
        </radialGradient>
        <radialGradient id="flame-gradient-high-middle" cx="50%" cy="55%" r="70%" fx="50%" fy="75%">
            <stop offset="0%" stop-color="#FF8A65" />
            <stop offset="60%" stop-color="#FF5722" />
            <stop offset="100%" stop-color="#E64A19" />
        </radialGradient>
        <radialGradient id="flame-gradient-high-inner" cx="50%" cy="50%" r="65%" fx="50%" fy="70%">
            <stop offset="0%" stop-color="#FFFF8D" />
            <stop offset="70%" stop-color="#FFEB3B" />
            <stop offset="100%" stop-color="#FBC02D" />
        </radialGradient>
        <filter id="glow-high" x="-30%" y="-30%" width="160%" height="160%">
            <feGaussianBlur stdDeviation="2" result="blur" />
            <feColorMatrix in="blur" type="matrix" values="1 0 0 0 1   0 0.5 0 0 0.3   0 0 0.5 0 0   0 0 0 0.6 0" result="glow"/>
            <feComposite in="SourceGraphic" in2="glow" operator="over" />
        </filter>
        <filter id="spark-filter" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.8" />
        </filter>
    </defs>
    <style>
        @keyframes flicker-high-outer {
            0% { transform: scale(1) rotate(0deg); opacity: 0.95; }
            10% { transform: scale(1.04) rotate(0.7deg); opacity: 1; }
            20% { transform: scale(0.96) rotate(-0.6deg); opacity: 0.9; }
            30% { transform: scale(1.03) rotate(0.5deg); opacity: 0.95; }
            40% { transform: scale(0.97) rotate(-0.5deg); opacity: 0.85; }
            50% { transform: scale(1.04) rotate(0.6deg); opacity: 1; }
            60% { transform: scale(0.96) rotate(-0.4deg); opacity: 0.9; }
            70% { transform: scale(1.03) rotate(0.5deg); opacity: 0.95; }
            80% { transform: scale(0.97) rotate(-0.5deg); opacity: 0.85; }
            90% { transform: scale(1.02) rotate(0.4deg); opacity: 0.95; }
            100% { transform: scale(1) rotate(0deg); opacity: 0.95; }
        }
        @keyframes flicker-high-middle {
            0% { transform: scale(1) rotate(0deg); opacity: 0.9; }
            15% { transform: scale(1.03) rotate(-0.5deg); opacity: 1; }
            30% { transform: scale(0.97) rotate(0.4deg); opacity: 0.85; }
            45% { transform: scale(1.02) rotate(-0.3deg); opacity: 0.95; }
            60% { transform: scale(0.98) rotate(0.3deg); opacity: 0.9; }
            75% { transform: scale(1.03) rotate(-0.4deg); opacity: 1; }
            90% { transform: scale(0.97) rotate(0.3deg); opacity: 0.85; }
            100% { transform: scale(1) rotate(0deg); opacity: 0.9; }
        }
        @keyframes flicker-high-inner {
            0% { transform: scale(1) translateY(0); opacity: 0.9; }
            20% { transform: scale(1.05) translateY(-0.5px); opacity: 1; }
            40% { transform: scale(0.95) translateY(0.5px); opacity: 0.8; }
            60% { transform: scale(1.04) translateY(-0.3px); opacity: 0.95; }
            80% { transform: scale(0.96) translateY(0.3px); opacity: 0.85; }
            100% { transform: scale(1) translateY(0); opacity: 0.9; }
        }
        @keyframes spark-animation {
            0% { opacity: 0; transform: translateY(0) scale(0); }
            20% { opacity: 0.8; transform: translateY(-5px) scale(1); }
            80% { opacity: 0.2; transform: translateY(-15px) scale(0.5); }
            100% { opacity: 0; transform: translateY(-20px) scale(0.2); }
        }
        .flame-high-outer {
            fill: url(#flame-gradient-high-outer);
            transform-origin: center bottom;
            animation: flicker-high-outer 1.5s infinite ease-in-out;
            filter: url(#glow-high);
        }
        .flame-high-middle {
            fill: url(#flame-gradient-high-middle);
            transform-origin: center bottom;
            animation: flicker-high-middle 1.8s infinite ease-in-out;
        }
        .flame-high-inner {
            fill: url(#flame-gradient-high-inner);
            transform-origin: center bottom;
            animation: flicker-high-inner 2s infinite ease-in-out;
        }
        .spark {
            fill: #FFFF00;
            filter: url(#spark-filter);
            r: 0.4;
            animation: spark-animation 1.5s infinite linear;
        }
    </style>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <path class="flame-high-outer" d="M12,21 C8.13400675,21 5,18.1796037 5,14.7329087 C5,12.8958045 5.93427391,10.993961 6.41880783,9.90643703 C6.41880783,9.90643703 6.76313842,9.23027434 7.12602068,9.65741615 C7.55730957,10.1741519 7.12602068,11.2964489 7.12602068,11.2964489 C7.12602068,11.2964489 6.30230221,13.6866295 6.84521108,14.8595703 C7.38811994,16.032511 7.83796051,16.5128174 8.05543297,16.8048396 C8.27290544,17.0968619 8.39946,17.0968619 8.56692669,16.8798485 C8.73439337,16.6628351 8.81094793,16.0824899 8.81094793,16.0824899 C8.81094793,16.0824899 9.02842039,14.1306553 9.90122308,13.1700428 C10.7740258,12.2094302 11.1982603,11.8424259 11.3657269,11.3304428 C11.533194,10.8184597 11.4058327,10.518348 11.2383662,10.1180048 C11.0709,9.71766162 10.9180267,9.69265277 10.9180267,9.69265277 C10.9180267,9.69265277 11.8416368,9.13897709 12.2150647,8.2533555 C12.5884927,7.36773392 12.5884927,6.19479323 12.5884927,6.19479323 C12.5884927,6.19479323 12.8059652,6.19479323 13.0743441,6.48155454 C13.3427231,6.76831586 13.5729444,6.39130261 13.6622922,6.19479323 C13.7516399,5.99828385 13.8410112,5.80168351 13.8410112,5.60508317 C13.9815067,5.60508317 14.3676512,6.22689407 14.8339582,7.01288599 C15.5946821,8.31647252 16,10.0060654 16,11.501291 C16,16.301434 13.8659932,21 12,21 Z" fill-rule="nonzero"></path>
        <path class="flame-high-middle" d="M12,19.5 C9.22121177,19.5 7,17.3455249 7,15.1197073 C7,13.9126072 7.62284927,12.5956407 7.94587189,11.8708963 C7.94587189,11.8708963 8.17542561,11.3996429 8.41828045,11.691944 C8.70456638,12.0404346 8.41828045,12.8176392 8.41828045,12.8176392 C8.41828045,12.8176392 7.86820147,14.4577543 8.2301407,15.2644468 C8.59207994,16.0711393 8.89197367,16.3759913 9.03696865,16.5794398 C9.18196362,16.7828883 9.26536,16.7828883 9.37781113,16.6328456 C9.49026225,16.4828028 9.54221862,16.0711393 9.54221862,16.0711393 C9.54221862,16.0711393 9.68721359,14.6955365 10.2675974,14.0227499 C10.8479812,13.3499633 11.1326735,13.0929507 11.2473846,12.7358785 C11.3620957,12.3788062 11.2776885,12.1753577 11.1629774,11.9003036 C11.0482662,11.6252495 10.9456847,11.6084185 10.9456847,11.6084185 C10.9456847,11.6084185 11.5610912,11.2261773 11.8100432,10.6022237 C12.0589951,9.9782701 12.0589951,9.13041552 12.0589951,9.13041552 C12.0589951,9.13041552 12.2039768,9.13041552 12.3828961,9.32793031 C12.5618153,9.5254451 12.7152963,9.26706007 12.7748614,9.13041552 C12.8344266,8.99377097 12.8939742,8.85707901 12.8939742,8.72038706 C12.9876711,8.72038706 13.2451008,9.14708605 13.5559722,9.68858865 C14.063122,10.5754484 14.3333333,11.7376903 14.3333333,12.7670941 C14.3333333,16.0711393 12.8659955,19.5 12,19.5 Z" fill-rule="nonzero"></path>
        <path class="flame-high-inner" d="M12,18 C10.1328871,18 9,16.5910498 9,15.1197073 C9,14.3253698 9.37370956,13.4973844 9.56752313,13.0425378 C9.56752313,13.0425378 9.70525537,12.7397857 9.85096827,12.9151664 C10.0228376,13.1393807 9.85096827,13.6605835 9.85096827,13.6605835 C9.85096827,13.6605835 9.52092088,14.6746526 9.73810442,15.1586681 C9.95528796,15.6426836 10.1350842,15.8255948 10.2221611,15.9476639 C10.3092381,16.0697329 10.356636,16.0697329 10.4262668,15.9797073 C10.4958976,15.8896818 10.5261117,15.6426836 10.5261117,15.6426836 C10.5261117,15.6426836 10.6131886,14.7973219 10.9432263,14.3733219 C11.2732639,13.9493219 11.4479041,13.7957704 11.5167908,13.5815271 C11.5856774,13.3672839 11.5350331,13.2452148 11.4661465,13.0729822 C11.3972599,12.9007497 11.3347108,12.890511 11.3347108,12.890511 C11.3347108,12.890511 11.7252548,12.6357064 11.8860259,12.2613342 C12.0467971,11.886962 12.0467971,11.3469932 12.0467971,11.3469932 C12.0467971,11.3469932 12.1338741,11.3469932 12.2483377,11.4675931 C12.3628013,11.588193 12.4558648,11.4266415 12.4915168,11.3469932 C12.5271689,11.2673448 12.5628045,11.1877206 12.5628045,11.1080723 C12.6195957,11.1080723 12.7834605,11.3576239 12.9718834,11.6932719 C13.2781146,12.2613342 13.4444444,12.9600345 13.4444444,13.6000565 C13.4444444,15.6426836 12.5975973,18 12,18 Z" fill-rule="nonzero"></path>
        
        <!-- 添加火花效果 -->
        <circle class="spark" cx="11" cy="10" style="animation-delay: 0s"></circle>
        <circle class="spark" cx="13" cy="9.5" style="animation-delay: 0.3s"></circle>
        <circle class="spark" cx="10.5" cy="11" style="animation-delay: 0.6s"></circle>
        <circle class="spark" cx="13.5" cy="10" style="animation-delay: 0.9s"></circle>
        <circle class="spark" cx="12" cy="9" style="animation-delay: 1.2s"></circle>
        <circle class="spark" cx="11.5" cy="9.5" style="animation-delay: 0.4s"></circle>
        <circle class="spark" cx="12.5" cy="10.5" style="animation-delay: 0.7s"></circle>
        <circle class="spark" cx="10.8" cy="10.2" style="animation-delay: 1s"></circle>
    </g>
</svg> 