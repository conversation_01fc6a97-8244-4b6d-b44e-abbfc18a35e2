import Vue from 'vue'
/** 引入自定义组件 */
import '@/components/register.js'
import 'normalize.css/normalize.css'
// 引入ofd预览插件
import OfdView from 'ofd-view'

import ElementUI, { Message } from 'element-ui'
// 引入form-create 表单生成器
import formCreate from '@form-create/element-ui'

import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
Vue.use(VXETable)
// 重写vxe-table，兼容eltable的方法
import '@/components/vxe-table/vxe-table.scss'
import VXE_TABLE from '@/components/vxe-table/vxe-table.js'
Vue.use(VXE_TABLE)

// 样式文件，需单独引入
import 'element-ui/lib/theme-chalk/index.css'

import { isAuth, $message } from '@/utils'
import '@/styles/common.scss'


import '@/styles/elereset.scss'
import APP from './App.vue'

import router from './router'
import store from './store'
import './public-path'
// 引入防止重复点击
import './utils/preventDbClick.js'
import './utils/dialogDrag/dialogDrag.js'
import websocket from './utils/websocket.js'
import selectLoadmore from "@/directive/el-select";
Vue.use(selectLoadmore);
Vue.use(websocket);

import VueNativeSock from 'vue-native-websocket'
import * as socketApi from '@/views/chatPage/js/wssocket.js';
import PopupManager from 'element-ui/lib/utils/popup/popup-manager';
Vue.prototype.$wsApi = socketApi;
Vue.prototype.$eventBus = new Vue(); // 全局事件
const userInfo = JSON.parse(window.sessionStorage.getItem('userInfo'))
if(userInfo && userInfo.id) {
  // todo 打包时这里地址需要切换回来
  Vue.use(VueNativeSock, `${process.env.VUE_APP_WS_MANAGEMENT_URL}/${userInfo.id}`, {
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 3000,
  })
}
import Promise from 'es6-promise'
import { name } from '../package'
// import '@/styles/bifrost-ic.scss'
// import '@/styles/bifrost-ic-namespace.scss'
import utils from './utils/utils' // 引入全局工具方法
import * as echarts from 'echarts'
import dataV from '@jiaminghi/data-view'
import 'echarts-wordcloud'
import { ImSdkEmbeddedManage } from "./imSdkEmbedded.js"
import { getUser } from '@/utils/auth'
Vue.use(dataV)
Promise.polyfill()

Vue.config.productionTip = false
// 使用ofd预览插件
Vue.use(OfdView);

Vue.use(ElementUI, { size: 'small' })
// 全局注入(挂载)form-create
Vue.use(formCreate)
Vue.prototype.isAuth = isAuth // 权限方法

// 重写element挂载方法
Vue.prototype.$message = $message

function render() {
  if (!window.systemStore) {
    window.systemStore = {}
  }
  if (window.systemStore[name]) {
    return
  }
  window.systemStore[name] = new Vue({
    router: router,
    store,
    render: h => h(APP)
  }).$mount('#app')
}
const accessToken = sessionStorage.getItem('token')
if (!window.__POWERED_BY_QIANKUN__) {
  render()
}

export async function bootstrap() {
  console.log('[vue] vue app bootstraped')
}

// 全局重写 element 的message 弹框事件
Vue.prototype.$message = function(msg) {
  ElementUI.Message(msg)
}
const messagearr = ['success', 'warning', 'info', 'error']
messagearr.forEach(function(type) {
  Vue.prototype.$message[type] = function(options) {
    let obj = { }
    // 动态计算垂直居中的偏移量
    const offset = window.innerHeight / 2 - 50; 
    if (typeof options === 'string') {
      obj = { dangerouslyUseHTMLString: true, message: options, center: true, offset: offset }
    } else {
      obj = { dangerouslyUseHTMLString: true, center: true, offset: offset, ...options }
    }
    return ElementUI.Message[type](obj)
  }
})

export async function mount(props) {
  console.log('props from main framework', props)
  if (process.env.VUE_APP_IS_PA_MICRO) {
    const getStyle = window.getComputedStyle
    // @ts-ignore
    window.getComputedStyle = (element, property) => {
      if (!element || element.nodeType === 9) return {}
      return getStyle(element, property)
    }
    Vue.prototype.parentProps = props
  }
  render()
}

export async function unmount() {
  Message.closeAll() // 关闭所有提示，否则切换其他应用后会导致message停留一段时间
}

// 自定义事件触发和绑定机制，通过定义一个全局的Vue实例，
// 然后通过这个实例来抛出事件和监听事件，结合utils中的全
// 局方法，就能实现全局的触发和监听事件的工具方法
window.$event = new Vue()
Vue.use(utils)
ImSdkEmbeddedManage.getImEmbedded().init({
        thirdUserInfo: {
          thirdUserId: getUser().userId,
          loginName: getUser().loginName,
          ucode: getUser().userCode,
          uname: getUser().userName,
          // mofDivId: getUser().mofDivId,
          mofDivCode: '440300000',
          // mofDivName: getUser().mofDivName,
          agencyId: getUser().mofDivId,
          agencyCode: getUser().orgCode,
          agencyName: getUser().orgName,
          mofDepCode: '',
          email: getUser().email,
          phone: getUser().phone,
          postCode: 'DEFAULT_STATION_440309000'
        },
        // mode: 'MICRO_PRO_DEV', 
        // 系统项目内使用
        acCode: 'pj',
        imBaseUrl: '/te-uni',
        dialogZIndexFn: () => {
          PopupManager.nextZIndex()
          return PopupManager.zIndex
        }
})
Vue.prototype.$echarts = echarts
export default Vue
