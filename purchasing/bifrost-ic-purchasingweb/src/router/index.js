import Vue from 'vue'
import Router from 'vue-router'
import { name } from '../../package'

// 路由自动化引入 Start

/** 使用规则
 * 文件结构：1 views/demo/helloworld/index.vue => 路由: http://localhost:{port}/demo/helloworld
 * 文件结构：2 views/demo/test.vue             => 路由: http://localhost:{port}/demo/test
 */
/**
 * @param  其组件目录的相对路径
 * @param  是否查询其子目录
 * @param  匹配基础组件文件名的正则表达式
 */
const requireComponent = require.context('@/views', true, /\.vue$/)
const pathArr = requireComponent.keys().map((pathInfo) => { // 自动化引入
  const haveIndexVue = pathInfo.includes('index.vue')
  const path = haveIndexVue ? pathInfo.replace(/^\./, '').replace(/\/index.vue$/, '') : pathInfo.replace(/^\./, '').replace(/\.vue$/, '')
  const filePath = pathInfo.replace(/^\./, '')
  return {
    path,
    component: (resolve) => require([`@/views${filePath}`], resolve),
    hidden: true // 是否渲染该路由入口
  }
})

const routes = [
  ...pathArr
]

// 路由自动化引入 End

Vue.use(Router)
export default new Router({
  base: window.__POWERED_BY_QIANKUN__ ? `/${name}` : '/',
  mode: process.env.VUE_APP_IS_PA_MICRO ? 'hash' : 'history', // 区分融合平台bifrost-portal-pa和bifrost-portal
  scrollBehavior: () => ({
    y: 0
  }),
  routes: process.env.VUE_APP_IS_PA_MICRO ? routes.map(item => {
    if (item.path !== '*') {
      const newItem = item
      newItem.path = '/micro-route/' + name + item.path // 集成平安智慧财政平台 加前缀
      return newItem
    }
    return item
  }) : routes
})
