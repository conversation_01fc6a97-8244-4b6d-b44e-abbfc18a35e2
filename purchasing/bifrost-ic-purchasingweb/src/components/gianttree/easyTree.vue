<template>
  <div class="easy-tree flex-column" style="overflow: hidden;height: 100%">
    <treeSearch ref="treeSearch" :btnSwitch="btnSwitch" @onClickButton="onClickButton"/>
    <div style="height: 10px"></div>
    <div class="flex-1">
      <vue-easy-tree
        highlight-current
        :default-expanded-keys="delExKeys"
        v-bind="$attrs"
        v-on="$listeners"
        ref="veTree"
        style="overflow: hidden;height: 100%"
        :node-key="composeTreeProps.id"
        height="100%"
        class="border-common"
        :data="treeData"
        :check-strictly="singeChoice"
        :item-size="32"
        :props="composeTreeProps"
        :filter-node-method="filterNode"
        :showCheckbox="showCheckbox"
        @node-click="nodeClick"
        @check="handleCheck"
      >
      <div class="custom-tree-node" slot-scope="{ data, node }">
        <span v-if="node.isLeaf" class="ico_docu"></span>
        <div class="custom-tree-node-span-container" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave" @dblclick="dblClickItem(data)">
          <span class="custom-tree-node-hidden-text">{{ data[composeTreeProps.label] }}</span>
          <span class="custom-tree-node-text" :style="dynamicStyle">{{ data[composeTreeProps.label] }}</span>
        </div>
      </div>
      </vue-easy-tree>
    </div>
  </div>
</template>

<script>
// 树插件(虚拟滚动)
import VueEasyTree from '@wchbrad/vue-easy-tree'
// import "@wchbrad/vue-easy-tree/src/assets/index.scss"

export default {
  name: 'easyTree',
  components: {
    VueEasyTree
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    // 任意选择节点
    singeChoice: {
      type: Boolean,
      default: false
    },
    // 多选
    multiple: {
      type: Boolean,
      default: true
    },
    isGetChild: {
      type: Boolean,
      default: false
    },
    showCheckbox: {
      type: Boolean,
      default: false
    },
    nodes: {
      type: Array,
      default: () => []
    },
    treeProps: {
      type: Object,
      default: () => ({})
    },
    btnSwitch: {
      type: Object,
      default: function() {
        return {
          showEdit: false,
          showRefresh: false,
          showExpandAll: false,
          clearCheck: false,
          checkEnable: false
        }
      }
    }
  },
  data() {
    return {
      isFiretInt: true,
      delExKeys: [],
      treeData: [],
      currentNodes: [],
      // id map
      idDataMap: {},
      // label map
      labelDataMap: {},
      // itemKey map
      itemKeyMap: {},
      // 设置鼠标移入到节点后 动画移动的距离
      calcuWidth: ''
    }
  },
  computed: {
    dynamicStyle() {
      return this.calcuWidth ? {
        '--calcu-width': this.calcuWidth
      } : {}
    },
    composeTreeProps() {
      const props = {
        label: 'label',
        children: 'children',
        idKey: 'id', // 选中的值
        id: 'id', // 用作匹配父子及级
        parentId: 'parentId'
      }
      return Object.assign(props, this.treeProps)
    }
  },
  created() {
  },
  watch: {
    nodes: {
      handler: function(nodes) {
        // 防止监听无故触发
        if (this.currentNodes === nodes) {
          return
        }
        this.currentNodes = this.nodes
        this.resetList()
      },
      deep: true,
      immediate: true
    },
    value: {
      handler() {
        this.$nextTick(() => {
          this.resetInit(this.value)
          if (this.isFiretInt) {
            this.delExKeys = []
          }
          if (this.isFiretInt && this.$isNotEmpty(this.value)) {
            this.resetDefExKey()
            this.isFiretInt = false
          }
        })
      },
      immediate: true
    }
  },
  methods: {
    handleMouseEnter(e) {
      if (e.target) {
        const nodeSpanContainer = e.target
        const textNode = e.target.querySelector('.custom-tree-node-text')
        if (nodeSpanContainer.clientWidth < textNode.clientWidth) {
          this.calcuWidth = -(textNode.clientWidth - nodeSpanContainer.clientWidth) + 'px'
        }
      }
    },
    handleMouseLeave() {
      this.calcuWidth = ''
    },
    // 设置默认展开的值
    resetDefExKey() {
      let defExMap = {}
      const defExKeys = []
      const addId = (id) => {
        const node = this.itemKeyMap[id] || {}
        const checkId = node[this.composeTreeProps.id]
        if (!checkId || !id) {
          return
        }
        if (!defExMap[checkId]) {
          defExKeys.push(checkId)
        }
        defExMap[checkId] = true
        if (node.parentId) {
          const parentNode = this.idDataMap[node.parentId]
          // 存在情况，有parentId但是没有对应的节点
          parentNode && addId(parentNode[this.composeTreeProps.idKey])
        }
      }
      this.value.map((id) => {
        addId(id)
      })
      defExMap = {}
      this.delExKeys = defExKeys
    },
    reset() {
      this.isFiretInt = true
      this.$refs.treeSearch?.reset?.()
      this.clearCheck()
    },
    dblClickItem(node) {
      if ((!node.isLeaf || node.children?.length > 0) && !this.singeChoice) {
        return false
      }
      this.$emit('dblclick', node)
    },
    filterNode(value, data) {
      if (!value) return true
      return data[this.composeTreeProps.label].indexOf(value) !== -1
    },
    // 回填勾选
    resetInit(values = []) {
      const checks = this.getCheckedNodes()
      const checkMap = {}
      checks.forEach(item => {
        if (!item?.children?.length || this.singeChoice) {
          checkMap[item[this.composeTreeProps.idKey]] = item
        }
      })
      values.forEach(id => {
        // 当前没选中,(不是父级 || 父子级不联动)时
        const node = this.itemKeyMap[id]
        if (node && !checkMap[id] && (!node?.children?.length || this.singeChoice)) {
          this.checkNode(node, true)
          this.$refs.veTree.setCurrentNode(node)
        }
        if (checkMap[id]) {
          delete checkMap[id]
        }
      })
      Object.values(checkMap).forEach(node => {
        this.checkNode(node, false)
      })
    },
    // 将扁平化数组改成有层级的
    resetList() {
      const list = []
      this.idDataMap = {}
      this.labelDataMap = {}
      this.itemKeyMap = {}
      // 重置map
      const resetMap = (nodes = []) => {
        if (!nodes) return
        const id = this.composeTreeProps.id
        const idKey = this.composeTreeProps.idKey
        const label = this.composeTreeProps.label
        const children = this.composeTreeProps.children
        nodes.forEach(node => {
          this.idDataMap[node[id]] = node
          this.labelDataMap[node[label]] = node
          if (node[idKey]) {
            this.itemKeyMap[node[idKey]] = node
          }
          if (node[children]) {
            resetMap(node[children])
          }
        })
      }
      resetMap(this.nodes)
      this.nodes.map(item => {
        if (!item[this.composeTreeProps.parentId]) {
          list.push(item)
        } else {
          const parentNode = this.idDataMap[item[this.composeTreeProps.parentId]]
          if (!parentNode) {
            list.push(item)
            return
          }
          if (!parentNode[this.composeTreeProps.children]) {
            parentNode[this.composeTreeProps.children] = []
          }
          // if (!this.singeChoice && !this.multiple) {
          //   this.$set(parentNode, 'disabled', true)
          // }
          let hasCurrentData = false
          parentNode[this.composeTreeProps.children].forEach(node => {
            const idKey = this.composeTreeProps.id
            if (item[idKey] === node[idKey]) {
              Object.assign(node, item)
              hasCurrentData = true
            }
          })
          if (!hasCurrentData) {
            parentNode[this.composeTreeProps.children].push(item)
          }
        }
      })
      this.treeData = list
      this.$nextTick(() => {
        // 兼容ztree
        this.$emit('onCreated', this)
        this.resetInit(this.value)
      })
    },
    handleCheck(node) {
      const checkList = this.$clone(this.value || [])
      const isCancel = this.value.includes(node[this.composeTreeProps.idKey])
      if (!this.multiple) {
        this.$refs.veTree.setCheckedAll(false)
        // 如果是父级,并且父子级联动,是选中时
        if (node.children?.length && !this.singeChoice) {
          // 回填
          this.resetInit(checkList)
          this.$message.warning('只能选择最末级!')
        } else if (!isCancel) {
          this.checkNode(node, true)
        }
      }
      this.resetValue()
    },
    // 更新v-model
    resetValue() {
      const checks = this.getCheckedNodes()
      this.updataValue(checks)
    },
    updataValue(checks = []) {
      const ids = checks.map(item => (item[this.composeTreeProps.idKey]))
      this.$emit('input', ids)
    },
    nodeClick(node) {
      const checks = this.getCheckedNodes()
      let isCheck = false
      checks.map(item => {
        if (item[this.composeTreeProps.idKey] === node[this.composeTreeProps.idKey]) {
          isCheck = true
        }
      })
      if (node.disabled) {
        return
      }
      if (!this.multiple) {
        if (node.children?.length && !this.singeChoice) {
          // this.$message.warning('只能选择最末级!')
          return
        } else {
          this.$refs.veTree.setCheckedAll(false)
        }
      }
      this.$refs.veTree.setChecked(node, !isCheck, true)
      this.resetValue()
    },
    onClickButton(emit, value) {
      if (emit === 'search') {
        this.$refs.veTree.filter(value.trim())
        this.$refs.treeSearch.isAllSelected = true
      } else if (emit === 'selectAll') {
        if (value) {
          const filterNodes = this.getNodesByFilter(node => node[this.composeTreeProps.label].includes(value))
          this.$refs.veTree.setCheckedNodes(filterNodes)
        } else {
          this.$refs.veTree.setCheckedAll(true)
        }
        this.resetValue()
      } else if (emit === 'clearCheck') {
        this.$refs.veTree.setCheckedAll(false)
        this.resetValue()
      }
    },
    clearCheck() {
      this.$refs.veTree.setCheckedAll(false)
      this.$refs.veTree.setCurrentKey(null)
    },
    // 兼容ztree方法
    getNodes() {
      return this.treeData
    },
    getNodesByParamFuzzy(key, value) {
      if (key === this.composeTreeProps.label) {
        const list = this.labelDataMap[value] ? [this.labelDataMap[value]] : []
        return list
      } else if (key === this.composeTreeProps.idKey) {
        const list = this.itemKeyMap[value] ? [this.itemKeyMap[value]] : []
        return list
      }
    },
    getNodesByParam(key, value) {
      return this.getNodesByParamFuzzy(key, value)
    },
    expandNode() {},
    updateNode(node) {
      this.$refs.veTree.updateKeyChildren(node[this.composeTreeProps.idKey], node)
    },
    checkAllNodes(flag = false) {
      this.$refs.veTree.setCheckedAll(flag)
    },
    selectNode(node) {
      if (!node) return
      this.$refs.veTree.setCurrentNode(node)
    },
    checkNode(node, checked) {
      this.$refs.veTree.setChecked(node, checked, !this.singeChoice)
    },
    getCheckedNodes() {
      let checks = []
      if (this.showCheckbox) {
        checks = this.$refs.veTree.getCheckedNodes(this.isGetChild, true)
      } else {
        const currentNode = this.$refs.veTree.getCurrentNode()
        checks = currentNode ? [currentNode] : []
      }
      return checks
    },
    getNodesByFilter(filter) {
      const treeData = Object.values(this.idDataMap)
      return treeData.filter(filter)
    },
    getCombiningLabel(node) {
      let label = node.label
      const parentId = node.parentId
      if (parentId && this.idDataMap[parentId]) {
        const parentNode = this.idDataMap[parentId]
        label = this.getCombiningLabel(parentNode) + '-' + label
      }
      return label
    },
    getParentNode(node) {
      const parentId = node.parentId
      return this.idDataMap[parentId]
    }
    // 兼容ztree方法 end
  }
}
</script>

<style lang="scss" scoped>
@keyframes scrollHorizontal {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(var(--calcu-width));
  }
}
.easy-tree {
  ::v-deep .vue-recycle-scroller {
    overflow-x: hidden;
  }
  // 图标图
  ::v-deep .el-tree {
    .el-tree-node__content {
      overflow: hidden;
      >.el-tree-node__expand-icon {
        padding: 7px 6px 6px 12px;
      }
    }
    .el-tree-node__expand-icon {
      font-family: "iconfont" !important;
      color: #8a8a8a;
    }
    .el-tree-node__expand-icon.is-leaf {
      color: transparent;
      // margin-left: -20px;
    }
    .el-tree-node__expand-icon.expanded {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    .el-icon-caret-right:before {
      font-size: 14px;
      content: "\e69b";
    }
    .el-tree-node__expand-icon.expanded.el-icon-caret-right:before{
      content: "\e69c";
      font-size: 14px;
    }
    .custom-tree-node {
      display: flex;
      align-items: center;
      user-select: none;
      overflow: hidden;
      &-span-container {
        position: relative;
        overflow: hidden;
      }
      &-hidden-text {
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
      }
      &-text {
        top: 0;
        left: 0;
        position: absolute;
        &:hover {
          animation: scrollHorizontal 3s linear infinite alternate; /* 设置动画 */
        }
      }
      .ico_docu {
        display: inline-block;
        width: 17px;
        height: 17px;
        min-width: 17px;
        min-height: 17px;
        max-width: 17px;
        max-height: 17px;
        margin-right: 5px;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAARklEQVQ4T2NkoBAwwvTv37//PylmOTo6gvWiGAATJGQQyLJBbgByeGDz1hDzAnKMwLwz6gUGBtqFAaE8gC1G4JmJFM3IagG1XnQRLFpm/AAAAABJRU5ErkJggg==') center center no-repeat;
        background-size: contain;
      }
    }
  }
}
</style>
