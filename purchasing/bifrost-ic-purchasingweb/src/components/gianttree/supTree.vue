<template>
    <div id="supTree">
        <div v-if="isPopover" style="height:100%; position: relative;" ref="outTreeContainer">
            <el-popover
                    ref="listPopover"
                    v-model="visible"
                    @show="visibleShow"
                    @hide="hidePopover"
                    placement="bottom-start"
                    trigger="click">
                <el-input v-model="filterText" @keyup.enter.native="search" placeholder="Enter搜索"  class="input-with-select">
                    <el-button slot="append" icon="el-icon-refresh" @click="refresh" title="刷新">刷新</el-button>
                    <el-button slot="prepend" @click="expandAll" :title="isExpandAll?'折叠':'展开'"   :icon="isExpandAll?'el-icon-remove-outline':'el-icon-circle-plus-outline'"/>
                    <el-button slot="append" style=" border-left:1px solid #DCDFE6;" v-if="setting.check.enable&&isAllSelected"   @click="selectAll">全选
                    </el-button>
                    <el-button slot="append" style=" border-left:1px solid #DCDFE6;" v-if="setting.check.enable&&!isAllSelected"  @click="clearCheck">取消全选
                    </el-button>
                </el-input>
                <input type="text" :value="value"
                       v-show="false">
                <div class="ztree topnav_box" style="height: 280px;width: auto;" :id="zTreeId" ref="ztree"></div>
            </el-popover>
            <el-input v-model="showValue" :readonly="true" :size="isFilterData ? 'mini' : ''" v-popover:listPopover @clear="clear"
                      :placeholder="placeholder"
                       :disabled="isDisabled"
                       v-on:mouseover.native="mouseover()"
                       v-on:mouseleave.native="mouseleave()">
                       <!-- @focus.self.once="universalFocus($event)" -->
                      </el-input>
            <div class="icon-div-class" @mousemove="move" @mouseleave="leave">
                <i class="el-icon-circle-close iconCustom" :id="iconCustomId" @click="clearCheck()"></i>
            </div>
        </div>
        <div v-else style="height:100%;display:flex;flex-direction: column; position: relative;" ref="outTreeContainer">
            <el-row>
                <el-input v-model="filterText" @keyup.enter.native="search" placeholder="Enter搜索" class="searchInputClass" clearable>
                    <el-button size="mini" v-if="btnSwitch.showRefresh" slot="append" icon="el-icon-refresh" @click="refresh" title="刷新" circle></el-button>
                    <el-button size="mini" v-if="btnSwitch.showEdit" slot="append"  type="success" icon="el-icon-circle-plus-outline"  @click="onClickButton('onClickAdd')" circle></el-button>
                    <el-button size="mini" v-if="btnSwitch.showEdit" slot="append"  icon="el-icon-edit" title="修改" @click="onClickButton('onClickEdit')" circle></el-button>
                    <el-button size="mini" v-if="btnSwitch.showEdit" slot="append" icon="el-icon-delete"   @click="onClickButton('onClickDel')" circle />
                    <el-button size="mini" v-if="btnSwitch.showCopy" slot="append" icon="el-icon-document-copy"   @click="onClickButton('onClickCopy')" circle />
                  <el-button size="mini" v-if="btnSwitch.clearCheck" slot="append" icon="el-icon-delete"   @click="clearCheck" circle />
                    <el-button size="mini" v-if="btnSwitch.showExpandAll" slot="prepend" @click="expandAll" :title="isExpandAll?'折叠':'展开'" :icon="isExpandAll?'el-icon-remove-outline':'el-icon-circle-plus-outline'" circle/>
                </el-input>
            </el-row>
            <div class="ztree topnav_box" style="height:100%">
              <div style="width: max-content;" :id="zTreeId" ref="ztree"></div>
            </div>
        </div>
    </div>
</template>

<script>
import $ from 'jquery'
import { intersectionBy, cloneDeep, uniqBy } from 'lodash'

// let $ = jquery
// if (window.jQuery) {
//   $ = window.jQuery
// } else {
//   window.jQuery = $
// }
// import '@ztree/ztree_v3/js/jquery.ztree.all'
// import '@ztree/ztree_v3/js/jquery.ztree.exhide.js'
require('@ztree/ztree_v3/js/jquery.ztree.all')
require('@ztree/ztree_v3/js/jquery.ztree.exhide.js')

export default {
  name: 'supTree',
  props: {
    isCForm: { type: Boolean, default: false }, // 是否表单设置
    treeHeight: {
      type: Number,
      default: 0
    },
    value: {
      require: false
    },
    placeholder: {
      default: '输入关键字'
    },
    // 是否只获取子集数据
    isGetChild: {
      type: Boolean,
      default: true
    },
    treeTitle: {
      type: String,
      default: ''
    },
    isReadonly: {
      type: Boolean,
      default: true
    },
    modKey: {
      type: String,
      default() {
        return undefined
      }
    },
    isPopover: {
      type: Boolean,
      default: true
    },
    /**
   * 开关选项
   * editEnable = 是否启用编辑功能
   */
    btnSwitch: {
      type: Object,
      require: false,
      default: function() {
        return {
          showEdit: false,
          showRefresh: false,
          showExpandAll: false,
          clearCheck: false
        }
      }
    },
    setting: {
      type: Object,
      require: false,
      default: function() {
        return { check: { enable: false }}
      }
    },
    nodes: {
      type: Array,
      require: true,
      default: function() {
        return []
      }
    },
    checkedValues: {
      type: Array,
      require: false,
      default: function() {
        return []
      }
    },
    // 禁用
    isDisabled: {
      type: Boolean,
      default: false
    },
    checkEnable: {
      type: Boolean,
      default: undefined
    },
    uncheckedWhenInit: {
      type: Boolean,
      default: false
    },
    // 由于遗留代码原因，后端TreeDataVo中有children属性，
    // 会导致该节点会被判定为非叶子节点。这个属性标识是否
    // 自动删除children属性。默认自动删除，然后类似参数弹框
    // 等场景是较早代码，单独针对这个情况进行了处理，删除
    // children会导致这些代码不能正常运行，所以这类场景需要
    // 将这个autoDeleteChildren设置为false
    autoDeleteChildren: {
      type: Boolean,
      default: true
    },
    isFilterData: {
      type: Boolean,
      default: false
    },
    hidePopoverFlag: {
      type: String,
      default: ''
    },
    // 同步查询参数
    syncSearch: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 同步搜索配置
    searchConfig: {
      type: Array,
      default: () => {
        return []
      }
    },
    needRequest: {
      type: Boolean,
      default: false
    },
    // 是否开启拖拽
    drag: {
      type: Boolean,
      default: false
    },
    // form元素有值时显示label(目前仅限searchForm)
    topLabel: {
      type: String,
      default: ''
    },
    multiple: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      zTreeId: 'ztree_' + parseInt(Math.random() * 1e10),
      iconCustomId: 'iconCustom_' + parseInt(Math.random() * 1e10),
      treeObj: null,
      list: [],
      filterText: '',
      showValue: '',
      hideValue: [],
      objValues: [],
      loading: true,
      isExpandAll: false,
      isExpandAllNode: true,
      doNotUncheckedWhenClick: true,
      zTreeSetting: {
        view: {
          showIcon: true,
          showLine: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'label'
          }
        },
        callback: {
          beforeCheck: this.beforeOperate,
          beforeClick: this.beforeOperate,
          onCheck: this.onCheck,
          onClick: this.onClick,
          beforeDrag: this.beforeDrag,
          beforeDragOpen: this.beforeDragOpen,
          onDrag: this.onDrag,
          beforeDrop: this.beforeDrop,
          onDrop: this.onDrop,
          onRemove: (...arg) => {
            this.$emit('onRemove', ...arg)
          },
          onRename: (...arg) => {
            this.$emit('onRename', ...arg)
          }
        }
      },
      visible: false,
      isAllSelected: true,
      mousemove: undefined,
      treeItemId: '',
      sort: 0,
      temp: [],
      pId: '',
      nameMap: {}
    }
  },
  watch: {
    hidePopoverFlag: function() {
      this.visible = false
      this.showValue = ''
    },
    needRequest: function(newV, oldV) {
      if (newV) {
        this.search()
      }
    },
    nodes: {
      handler: function(nodes) {
        // 防止监听无故触发
        if (this.list === nodes) {
          return
        }
        this.list = nodes
        if (this.list) {
          // 后端使用List<TreeDataVo>结构时，节点有children属性，
          // 会导致所有节点都是非叶子节点，这里需要删除这个属性，
          // 是否是叶子节点由parentId与id自动判断
          // 如果数据中存在至少一个节点的isChecked是true，则使用
          // 数据中的isChecked设置覆盖checkedValues
          var checkedData
          this.nameMap = {}
          this.list.forEach(n => {
            if (this.autoDeleteChildren && n.isLeaf !== false) {
              delete n.children
            }

            if (n.isChecked === true) {
              if (checkedData === undefined) {
                checkedData = []
              }
              checkedData.push(n.id)
            }
            let label = ''
            try { // 可选链需更改
              label = this.zTreeSetting.data.key.name || 'label'
            } catch (error) {
              label = 'label'
            }
            this.nameMap[n[label]] = n
          })
          if (checkedData) {
            this.checkedValues = checkedData
          }
        }

        // update tree
        if (this.treeObj) {
          this.treeObj.destroy()
        }
        this.$nextTick(() => {
          this.zTreeSetting = Object.assign({}, this.zTreeSetting, this.setting)
          if (this.checkEnable !== undefined) {
            this.zTreeSetting.check = this.zTreeSetting.check || {}
            this.zTreeSetting.check.enable = this.checkEnable
          }
          // 配置了允许拖拽才开启拖拽配置
          if (this.drag) {
            this.zTreeSetting.edit = {
              enable: true,
              showRenameBtn: false,
              showRemoveBtn: false,
              drag: {
                isCopy: false,
                isMove: true
              }
            }
          }
          this.treeObj = $.fn.zTree.init($('#' + this.zTreeId), this.zTreeSetting, this.checkedValue(this.checkedValues, this.list))
          !this.isCForm && this.drag && this.sortArr()
          this.zTreeSetting = this.treeObj.setting

          // 添加afterClicked和afterChecked
          var onCheckOld = this.zTreeSetting.callback.onCheck
          var onCheck = (evt, treeId, treeNode) => {
            if (onCheckOld) {
              onCheckOld(evt, treeId, treeNode)
            }
            this.$emit('afterChecked', evt, treeId, treeNode)
            this.treeObj.selectNode(treeNode, undefined, true)
          }
          this.zTreeSetting.callback.onCheck = onCheck

          this.zTreeSetting.callback = this.zTreeSetting.callback || {}
          var onClickOld = this.zTreeSetting.callback.onClick
          var onClick = (evt, treeId, treeNode) => {
            if (onClickOld) {
              onClickOld(evt, treeId, treeNode)
            }
            this.$emit('afterClicked', evt, treeId, treeNode)

            // 点击节点时，同步执行勾选或不勾选的操作
            if (this.doNotUncheckedWhenClick && treeNode.checked) {
              return
            }
            this.treeObj.checkNode(treeNode, !treeNode.checked, !treeNode.checked)
            onCheck(evt, treeId, treeNode)
          }
          this.zTreeSetting.callback.onClick = onClick

          // 拖拽配置所需回调函数
          if (this.drag) {
            const beforeDrag = (treeId, treeNodes) => {
              this.removeNoAllowDragClass()
              const clone = cloneDeep(this.treeObj.getNodes())
              const myMap = {}
              clone.forEach(node => {
                this.recursionFindChildren(node, treeNodes, myMap)
              })
              Object.keys(myMap).forEach(id => {
                if (!myMap[id]) {
                  document.querySelector(`#${id}_a`).classList.add('noAllowDrag')
                  document.querySelector(`#${id}_ico`).classList.add('noAllowDrag')
                  document.querySelector(`#${id}_span`).classList.add('noAllowDrag')
                }
              })
              return true
            }

            this.zTreeSetting.callback.beforeDrag = beforeDrag

            const onDrag = (event, treeId, treeNodes) => {
              // 克隆节点 用于跟随鼠标移动
              const cloneNode = event.target.parentNode.tagName === 'A' ? event.target.parentNode.cloneNode(true) : event.target.querySelector('a')?.cloneNode(true)
              for (var i = cloneNode.attributes.length - 1; i >= 0; i--) {
                var attribute = cloneNode.attributes[i]
                cloneNode.removeAttribute(attribute.name)
              }
              cloneNode.setAttribute('data-tree-id', treeNodes[0].id)
              this.$refs.ztree.appendChild(cloneNode)
              if (this.treeItemId !== treeNodes[0].id) {
                this.treeItemId = treeNodes[0].id
                // 这是保存鼠标移动的参数 用于销毁和监听
                this.mousemove = (e) => this.mouseMove(e, event, treeNodes)
              }
              this.$refs.ztree.addEventListener('mousemove', this.mousemove)
            }

            this.zTreeSetting.callback.onDrag = onDrag

            const beforeDragOpen = (treeId, treeNodes) => {
              // 默认拖拽都不展开有子项的列表 目前不用支持跨级拖拽
              return false
            }

            this.zTreeSetting.callback.beforeDragOpen = beforeDragOpen

            const beforeDrop = (treeId, treeNodes, targetNode, moveType) => {
              this.removeNoAllowDragClass()
              if (treeNodes[0].parentId === targetNode.parentId && moveType !== 'inner') {
                return true
              } else {
                this.$refs.ztree.removeEventListener('mousemove', this.mousemove)
                const findDom = document.querySelector(`[data-tree-id="${this.treeItemId}"]`)
                findDom?.remove?.()
                return false
              }
            }

            this.zTreeSetting.callback.beforeDrop = beforeDrop

            const onDrop = (event, treeId, treeNodes, targetNode, moveType) => {
              this.removeNoAllowDragClass()
              this.$refs.ztree.removeEventListener('mousemove', this.mousemove)
              const findDom = document.querySelector(`[data-tree-id="${this.treeItemId}"]`)
              findDom?.remove?.()
              if (!event) {
                return
              }
              if (targetNode) {
                this.pId = treeNodes[0].parentId
                this.treeObj.getNodes().forEach(treeNode => {
                  this.recursionFindNodeFromParentId(treeNode, treeNodes[0].parentId)
                })
                this.sortArr(true)
              }
            }

            this.zTreeSetting.callback.onDrop = onDrop
          }

          this.zTreeSetting.view['addHoverDom'] = (treeId, treeNode) => $(`#${treeNode.tId}_a`).css('background', '#e8e8e8')
          this.zTreeSetting.view['removeHoverDom'] = (treeId, treeNode) => $(`#${treeNode.tId}_a`).css('background', 'none')
          this.loading = false
          this.getNodeChecked(false)
          this.$emit('ifExpandAll')
          this.treeObj.expandAll(this.isExpandAllNode)
          this.$emit('onCreated', this.treeObj)
        })

        // 如果不是第一次初始化，树节点会保留上次勾选效果，这里需要手动清除
        if (this.uncheckedWhenInit === true) {
          setTimeout(() => {
            this.zTreeSetting.data = this.zTreeSetting.data || {}
            this.zTreeSetting.data.simpleData = this.zTreeSetting.data.simpleData || {}
            var key = this.zTreeSetting.data.simpleData.idKey
            if (this.$isEmpty(key)) {
              this.$message.error('找不到key定义')
              return
            }
            for (let i = 0; i < this.list.length; i++) {
              if (this.list[i].isChecked === false) {
                this.checkNode(this.list[i][key], false)
              }
            }
          }, 1000)
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    documentClientHeight() {
      return this.treeHeight
    }
  },
  methods: {
    beforeOperate(treeId, treeNode) {
      if (!this.multiple &&
        treeNode.children?.length > 0) {
        this.$message.error('只能选择最末级选项')
        return false
      }
      if (!this.multiple) {
        // 单选时，先取消之前勾选的节点
        const node = this.objValues[0] || {}
        if (node.id !== treeNode.id) {
          // 单选时，先取消之前勾选的节点
          this.treeObj.checkAllNodes(false)
        }
      }
      return true
    },
    getNode(label) {
      return this.nameMap[label]
    },
    // 删除所有noAllowDrag类名
    removeNoAllowDragClass() {
      const allA = document.querySelectorAll('.noAllowDrag')
      Array.from(allA).forEach(node => {
        node.classList.contains('noAllowDrag') && node.classList.remove('noAllowDrag')
      })
    },
    // 树节点数据添加sort排序字段 如果request为true则表示拖拽成功 并发起请求
    sortArr(request = false) {
      const arr = []
      this.sort = 0
      this.treeObj.getNodes().forEach(node => {
        this.recursionSort(node, arr)
      })
      const result = uniqBy(arr, 'id')
      this.list.forEach(node => {
        result.forEach(res => {
          if (res.id === node.id) {
            node.sort = res.sort
          }
        })
      })
      const hasPId = this.temp.map(node => node.id)
      const notPId = this.treeObj.getNodes().map(node => node.id)
      const params = {
        // 分两种情况
        // 1.拖拽最外侧的节点 传递做外层顶级节点(不包含顶级节点的子节点)
        // 2.拖拽非最外侧节点 传递与其同级的节点(不包含父节点)
        classifyTreeList: this.list.filter(node => this.pId ? hasPId.includes(node.id) : notPId.includes(node.id))
      }
      if (request) {
        const { classifyTreeList } = params
        this.$callApi('sortClassifyEntity',
          { classifyTreeList: classifyTreeList }, result => {
          // 请求成功后需要重新请求查询接口
            this.$emit('dragSuccess')
            this.temp = []
          })
      }
    },
    // 1.根据parentId 递归找到 this.treeObj.getNodes()中对应id的节点
    recursionFindNodeFromParentId(treeNode, id) {
      if (treeNode.id === id) {
        this.recursionPushInArray(treeNode)
      } else {
        if (this.$hasProperties(treeNode, ['children']) && this.$isNotEmpty(treeNode.children)) {
          treeNode.children.forEach(child => {
            return this.recursionFindNodeFromParentId(child, id)
          })
        }
      }
    },
    // 2.根据这个节点 递归遍历children push到数组中
    recursionPushInArray(treeNode) {
      if (this.$hasProperties(treeNode, ['children']) && this.$isNotEmpty(treeNode.children)) {
        treeNode.children.forEach(child => {
          this.temp.push(child)
          return this.recursionPushInArray(child)
        })
      }
    },
    // 递归排序
    recursionSort(node, arr) {
      if (!node.parentId) {
        this.sort = this.sort + 1
        node.sort = this.sort
        arr.push(node)
      }
      if (this.$isEmpty(node.children)) {
        return
      }
      node.children.forEach(child => {
        this.sort = this.sort + 1
        child.sort = this.sort
        arr.push(child)
        return this.recursionSort(child, arr)
      })
    },
    // 递归查找子节点
    recursionFindChildren(obj, treeNodes, myMap) {
      if (this.$hasProperties(obj, ['children']) && this.$isNotEmpty(obj.children)) {
        obj.children.forEach(child => {
          obj.canDrag = treeNodes[0].parentTId === obj.parentTId
          myMap[`${obj.tId}`] = obj.canDrag
          return this.recursionFindChildren(child, treeNodes, myMap)
        })
      } else {
        obj.canDrag = treeNodes[0].parentTId === obj.parentTId
        myMap[`${obj.tId}`] = obj.canDrag
      }
    },
    // 鼠标按下拖拽的移动事件
    mouseMove(e, event, treeNodes) {
      const findDom = document.querySelector(`[data-tree-id="${treeNodes[0].id}"]`)
      const nodeName = findDom.querySelector('.node_name')
      // 鼠标跟随移动的dom的样式
      const styles = [
        {
          dom: findDom,
          style: 'position',
          value: 'absolute'
        },
        {
          dom: findDom,
          style: 'display',
          value: 'flex'
        },
        {
          dom: findDom,
          style: 'position',
          value: 'absolute'
        },
        {
          dom: findDom,
          style: 'textDecoration',
          value: 'none'
        },
        {
          dom: findDom,
          style: 'backgroundColor',
          value: '#409EFF'
        },
        {
          dom: findDom,
          style: 'color',
          value: '#FFFFFF'
        },
        {
          dom: findDom,
          style: 'opacity',
          value: 0.5
        },
        {
          dom: nodeName,
          style: 'whiteSpace',
          value: 'nowrap'
        }
      ]
      styles.forEach(cond => {
        if (cond.dom.style[cond.style] !== cond.value) {
          cond.dom.style[cond.style] = cond.value
        }
      })
      !e.target.classList.contains('noAllowDrag') ? findDom.style.backgroundColor = '#409EFF' : findDom.style.backgroundColor = '#FF0000'
      findDom.style.left = `${e.clientX - this.$refs.outTreeContainer.getBoundingClientRect().left + (e.clientX ? 15 : 0)}px`
      findDom.style.top = `${e.clientY - this.$refs.outTreeContainer.getBoundingClientRect().top}px`
    },
    showSelectHidePopover() {
      this.visible = false
    },
    visibleShow() {
      if (this.isDisabled) {
        this.visible = false
      }
      // 2024.5.30 调整为默认不展开
      this.isExpandAll = false
      this.treeObj && this.treeObj.expandAll(false)
      // setTimeout(() => {
      //   this.isExpandAll = true
      //   this.treeObj && this.treeObj.expandAll(true)
      // }, 500)
    },
    hidePopover() {
      // this.isExpandAll = false
      // this.treeObj && this.treeObj.expandAll(false)
      if (this.isFilterData && this.$isNotEmpty(this.objValues)) {
        this.$emit('handleFilter')
      }
    },
    checkedValue(checkedValues, list) {
      let resultList = []
      const resultMap = new Map()
      if (checkedValues && checkedValues.length > 0) {
        list.forEach(data => {
          resultMap.set(data[this.zTreeSetting.data.simpleData.idKey], Object.assign(data, { checked: false }))
        })
        checkedValues.forEach(id => {
          if (resultMap.get(id) && resultMap.get(this.zTreeSetting.data.simpleData.idKey) !== '') {
            resultMap.set(id, Object.assign(resultMap.get(id), { checked: true }))
          }
        })
        resultMap.forEach(v => {
          resultList.push(v)
        })
      } else {
        resultList = list
      }
      return resultList
    },
    // 节点原先是勾选状态，此时如果需要手动高亮选中节点，
    // 并且触发afterChecked事件时，使用这个方法，
    // key是指定节点对于关键字的值，比如默认“id”，key可选
    selectAndCheckNode(keyValue, key) {
      if (key === undefined) {
        this.zTreeSetting.data = this.zTreeSetting.data || {}
        this.zTreeSetting.data.simpleData = this.zTreeSetting.data.simpleData || {}
        key = this.zTreeSetting.data.simpleData.idKey
        if (this.$isEmpty(key)) {
          this.$message.error('找不到key定义')
          return undefined
        }
      }

      var treeNode = this.treeObj.getNodeByParam(key, keyValue, null)
      if (treeNode) {
        this.treeObj.selectNode(treeNode, undefined, true)
        this.zTreeSetting.callback.onCheck(undefined, this.zTreeId, treeNode)
      }
      return treeNode
    },
    // 节点原先是勾选状态，此时如果需要手动高亮选中节点，
    // 并且触发afterChecked事件时，使用这个方法，
    // key是指定节点对于关键字的值，比如默认“id”，key可选
    checkNode(keyValue, isChecked, key) {
      if (key === undefined) {
        this.zTreeSetting.data = this.zTreeSetting.data || {}
        this.zTreeSetting.data.simpleData = this.zTreeSetting.data.simpleData || {}
        key = this.zTreeSetting.data.simpleData.idKey
        if (this.$isEmpty(key)) {
          this.$message.error('找不到key定义')
          return
        }
      }

      var treeNode = this.treeObj.getNodeByParam(key, keyValue, null)
      if (treeNode) {
        this.treeObj.checkNode(treeNode, isChecked)
      }
    },
    // 取消节点高亮，如果treeNode省略，则取消所有节点高亮
    cancelSelectedNode(treeNode) {
      this.treeObj.cancelSelectedNode(treeNode)
    },
    onClick: function(evt, treeId, treeNode) {
      this.treeObj.checkNode(treeNode, !treeNode.checked, !treeNode.checked)
      this.getNodeChecked(true)
    },
    onCheck: function(evt, treeId, treeNode) {
      this.getNodeChecked(true)
    },
    onClickButton(emit) {
      this.$emit(emit)
    },
    refresh() {
      this.filterText = ''
      this.search()
      this.$emit('refresh', this.filterText)
    },
    search() {
      this.filterText = this.filterText.trim()
      this.$emit('changeNeedReqest', false)
      // 如果配置了syncSearch参数 即左侧树组件同步更新
      let syncSearchFilter = []
      const syncTreeFliter = []
      this.searchConfig.forEach(config => {
        if (this.$isNotEmpty(this.syncSearch[config])) {
          syncTreeFliter.push(this.syncSearch[config])
          if (syncSearchFilter.length) {
            syncSearchFilter = intersectionBy(this.treeObj.getNodesByParamFuzzy(this.zTreeSetting.data.key.name, this.syncSearch[config]), syncSearchFilter, 'id')
          } else {
            syncSearchFilter = this.treeObj.getNodesByParamFuzzy(this.zTreeSetting.data.key.name, this.syncSearch[config])
          }
        }
      })
      const isSyncTree = syncTreeFliter.length > 0
      this.loading = true
      const showNode = isSyncTree ? [] : this.treeObj.getNodesByParamFuzzy(this.zTreeSetting.data.key.name, this.filterText)
      let hideNode = showNode

      function showParentNode(node) {
        if (node) {
          const parentNode = node.getParentNode()
          if (parentNode) {
            isSyncTree ? syncSearchFilter.push(parentNode) : showNode.push(parentNode)
          }
          showParentNode(parentNode)
        }
      }

      hideNode = this.treeObj.getNodesByFilter(node => {
        const fn = filter => {
          return filter.map(item => {
            return node[this.zTreeSetting.data.key.name].indexOf(item) === -1
          }).includes(true)
        }

        if (isSyncTree ? fn(syncTreeFliter) : node[this.zTreeSetting.data.key.name].indexOf(this.filterText) === -1) {
          return true
        } else {
          isSyncTree ? syncSearchFilter.push(node) : showNode.push(node)
          if (node.isParent) {
            node.children?.forEach(node => {
              if (node[this.zTreeSetting.data.key.name].indexOf(this.filterText) !== -1) {
                isSyncTree ? syncSearchFilter.push(node) : showNode.push(node)
              }
            })
          }
          showParentNode(node)
          return false
        }
      })
      this.treeObj.hideNodes(hideNode)
      this.treeObj.showNodes(isSyncTree ? syncSearchFilter : showNode)
      this.loading = false
    },
    selectAll() {
      this.isExpandAll = true
      this.isAllSelected = !this.isAllSelected
      this.treeObj.expandAll(true)
      this.treeObj.checkAllNodes(true)
      this.getNodeChecked(true)
      this.$emit('selectAll')
    },
    expandAll() {
      this.isExpandAll = !this.isExpandAll
      this.treeObj.expandAll(this.isExpandAll)
    },
    clear() {
      this.$emit('clearInput')
    },
    clearInputBox() {
      if (!this.treeObj) { return }
      this.treeObj.cancelSelectedNode()
      this.treeObj.checkAllNodes(false)
      this.showValue = ''
    },
    clearCheck() {
      if (!this.isDisabled && this.treeObj) {
        this.isExpandAll = true
        this.isAllSelected = true
        this.treeObj.expandAll(true)
        this.treeObj.checkAllNodes(false)
        this.treeObj.cancelSelectedNode()
        this.showValue = ''
        this.getNodeChecked(true)
        this.$emit('clearCheck')
        this.$emit('clearInput')
      }
    },
    // 这个方法返回当前勾选的节点列表
    // getNodeChecked方法是每次勾选节点时会调用的方法
    getCheckedNodes() {
      if (this.zTreeSetting.check.enable) {
        return this.treeObj.getCheckedNodes(true)
      }
      return []
    },
    getNodeChecked(isCreateEd) {
      let selectedNodes = new Set()
      const showValues = []
      this.hideValue = []
      this.objValues = []
      if (this.zTreeSetting.check.enable) {
        // selectedNodes = this.treeObj.getCheckedNodes(true)
        selectedNodes = this.treeObj.getNodesByFilter((node) => node.checked)
      } else {
        if (isCreateEd) {
          selectedNodes = this.treeObj.getSelectedNodes(true)
        } else {
          selectedNodes = this.treeObj.getCheckedNodes(true)
        }
        if (selectedNodes && selectedNodes.length > 0) {
          // 如果没有复选框只允许选中一个值
          selectedNodes = selectedNodes[0]
          this.treeObj.selectNode(selectedNodes)
          if (this.isGetChild) {
            if (!selectedNodes.children) {
              showValues.push(selectedNodes[this.zTreeSetting.data.key.name])
              this.hideValue.push(selectedNodes[this.zTreeSetting.data.simpleData.idKey])
              this.objValues.push(selectedNodes)
            }
          } else {
            showValues.push(selectedNodes[this.zTreeSetting.data.key.name])
            this.hideValue.push(selectedNodes[this.zTreeSetting.data.simpleData.idKey])
            this.objValues.push(selectedNodes)
          }
        }
      }
      for (let i = 0; i < selectedNodes.length && this.zTreeSetting.check.enable; i++) {
        if (this.isGetChild) {
          if (!selectedNodes[i].children) {
            showValues.push(selectedNodes[i][this.zTreeSetting.data.key.name])
            this.hideValue.push(selectedNodes[i][this.zTreeSetting.data.simpleData.idKey])
            this.objValues.push(selectedNodes[i])
          }
        } else {
          showValues.push(selectedNodes[i][this.zTreeSetting.data.key.name])
          this.hideValue.push(selectedNodes[i][this.zTreeSetting.data.simpleData.idKey])
          this.objValues.push(selectedNodes[i])
        }
      }
      this.showValue = showValues.join(',')
      if (isCreateEd) {
        this.getCheckValues()
        this.getCheckObjs()
      }
    },
    // 这个方法是返回高亮选中的节点集合
    getSelectedNodes() {
      if (this.treeObj) {
        return this.treeObj.getSelectedNodes(true)
      }
      return []
    },
    setShowValue(v) {
      this.showValue = v
    },
    getCheckValues() {
      if (this.zTreeSetting.check.enable === false) {
        this.hideValue = this.hideValue[0]
      }
      if (this.modKey) {
        this.$emit('getCheckValues', { 'key': this.modKey, 'value': this.hideValue })
      } else {
        this.$emit('getCheckValues', this.hideValue)
        this.$emit('input', this.hideValue)
      }
    },
    getCheckObjs() {
      this.$emit('getCheckObjs', this.objValues)
    },
    mouseover() {
      !this.isDisabled && $('#' + this.iconCustomId).css('display', 'block')
    },
    mouseleave() {
      !this.isDisabled && $('#' + this.iconCustomId).css('display', 'none')
    },
    move() {
      !this.isDisabled && $('#' + this.iconCustomId).css('display', 'block')
    },
    leave() {
      !this.isDisabled && $('#' + this.iconCustomId).css('display', 'none')
    }
    // universalFocus($event) {
    //   // 触发搜索组件universalFocus方法
    //   this.$emit('universalFocus', $event)
    // }
  }
}
</script>

<style scoped>
#supTree{height: 100%;}
    .searchInputClass{
        width: 100%;
        height: 28px;
        line-height: 28px;
    }
    .supTree .searchInputClass .el-button {
        /* width: 35px; */
        height: 28px;
        padding-left: auto;
    }
    /* 样式影响（打开平台的之后影响了树顶部按钮样式） */
    #supTree .searchInputClass ::v-deep .el-input-group__append .el-button {
      margin-left: 10px;
    }
    #supTree .searchInputClass ::v-deep .el-input-group__append .el-button:first-child {
      margin-left: -20px;
    }
    .searchInputClass >>> .el-input__inner {
      padding: 0 25px 0 5px;
    }
    /* 滚动条出现不出现 当摸个滚动条不想让出现是可以这样*/
    #content_right::-webkit-scrollbar {
        display: none;
    }

    /*样式改变代码-----------------------------------------------------*/
    /* 滚动条整体部分 */
    .topnav_box::-webkit-scrollbar {
        width: 7px;
        height: 7px;
        background-color: #ffffff;
    }

    /* scroll轨道背景 */
    .topnav_box::-webkit-scrollbar-track {
        -webkit-box-shadow: none;
        /*border-radius: 10px;*/
        /*background-color: #ffffff;*/
    }

    /* 滚动条中能上下移动的小块 */
    .topnav_box::-webkit-scrollbar-thumb {
        border-radius: 5px;
        -webkit-box-shadow: inset 0 0 6px rgba(158, 159, 161, 0.49);
        background-color: rgba(158, 159, 161, 0.49);
    }

    /*样式改变代码-----------------------------------------------------*/
    /* beauty ztree! */
    .ztree {
        text-align: left;
        font-size: 14px;
        margin-top: 5px;
        border-top: 0;
        background: white;
        width: 100%;
        height: 330px;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid rgb(238, 238, 238);
    }

    .ztree >>> li {
        list-style-type: none;
        white-space: nowrap;
        outline: none;
    }

    .ztree >>> li span.center_open + a,
    .ztree >>> li span.center_close + a,
    .ztree >>> li span.roots_close + a,
    .ztree >>> li span.roots_open + a,
    .ztree >>> li span.bottom_open + a,
    .ztree >>> li span.bottom_close + a {
      width: calc(100% - 20px)
    }

    .ztree >>> li ul {
        position: relative;
        padding: 0 0 0 20px;
        margin: 0;
    }

    .ztree >>> .line:before {
        position: absolute;
        top: 0;
        left: 10px;
        height: 100%;
        content: '';
        border-right: 1px dotted #dbdbdb;
    }

    .ztree >>> .roots_docu:before,
    .ztree >>> .roots_docu:after,
    .ztree >>> .center_docu:before,
    .ztree >>> .bottom_docu:before,
    .ztree >>> .center_docu:after,
    .ztree >>> .bottom_docu:after {
        position: absolute;
        content: '';
        border: 0 dotted #dbdbdb;
    }

    .ztree >>> .roots_docu:before {
        left: 10px;
        height: 50%;
        top: 50%;
        border-left-width: 1px;
    }

    .ztree >>> .roots_docu:after {
        top: 50%;
        left: 11px;
        width: 50%;
        border-top-width: 1px;
    }

    .ztree >>> .center_docu:before {
        left: 10px;
        height: 100%;
        border-left-width: 1px;
    }

    .ztree >>> .center_docu:after {
        top: 50%;
        left: 11px;
        width: 50%;
        border-top-width: 1px;
    }

    .ztree >>> .bottom_docu:before {
        left: 10px;
        height: 50%;
        border-left-width: 1px;
    }

    .ztree >>> .bottom_docu:after {
        top: 50%;
        left: 11px;
        width: 50%;
        border-top-width: 1px;
    }

    .ztree >>> li a {
        display: inline-flex;
        align-items: flex-start;
        /* line-height: 36px; */
        width: 100%;
        /* height: 36px; */
        margin: 0;
        padding: 7px 0;
        cursor: pointer;
        transition: none;
        vertical-align: middle;
        color: #555555;
        text-decoration: none;
    }

    .ztree >>> .node_name {
        /* overflow:hidden;
        white-space: nowrap;
        text-overflow:ellipsis; */
        display: inline-block;
        width: calc(100% - 19px);
        padding: 0 8px 0 4px;
        line-height: normal;
        /* word-break: break-all; */
        /* white-space: break-spaces; */
        border-radius: 4px;
    }

    .ztree >>> .curSelectedNode {
        background-color: #e5f1fe;
    }

    /* // 隐藏子部门线条 */
    .ztree >>> .center_docu,.bottom_docu {
      display: none;
    }

    .ztree >>> .curSelectedNode_Edit {
        height: 20px;
        opacity: 0.8;
        color: #000;
        border: 1px #6cc2e8 solid;
        background-color: #9dd6f0;
    }

    .ztree >>> .tmpTargetNode_inner {
        opacity: 0.8;
        color: #fff;
        background-color: #4fcbf0;
        filter: alpha(opacity=80);
    }

    .ztree >>> .rename {
        font-size: 12px;
        line-height: 22px;
        width: 80px;
        height: 22px;
        margin: 0;
        padding: 0;
        vertical-align: top;
        border: 0;
        background: none;
    }

    .ztree >>> .button {
        position: relative;
        display: inline-block;
        /* line-height: 36px; */
        /* height: 36px; */
        width: 19px;
        cursor: pointer;
        text-align: center;
        vertical-align: middle;
    }

    .ztree >>> .button.edit {
        background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA2ElEQVQ4T9XTPYoCQRAF4Fd9sZnHnGHVjQwMxEBQMBATzTYSA8VIDMx29wzDm4MJJS0oI446amTHVV93/bThzWOv5EtqkvyNuWdA0h+ArzvgCsDQzL7dfQegFZEy4AB4CyBZSOoA2IQQ2kmSROTiBUcgBlYhRVH03H3t7l0z25Lc1wYkDQAsAPRJriQtSfZrAZLGAH4AjEjOJaUARPJY/lUPyiXkeT4LIUzNbJKmaUTwNBCTsiybnfryFFDVzA8Aaq525RQerXLZ/ifZuBhjzZuvwl76jWXlAPY0hRHqnDC1AAAAAElFTkSuQmCC") 0 3px no-repeat;
    }

    .ztree >>> .button.remove {
        background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAtUlEQVQ4T8WTOxLCMAxElcId3MgFe4BwJYYrwQE2hW9EOhcwyjieyNjEDAUu9XlafTzIj28o80keROTe4I4A5q2vBjiJCEVkKiBqBwBjbwIAGB/JZxNAUitqhW/epHKWKiTXZAVhh5JjtJ0sM0FYSq8M2bTSBKS2rglwUblJbTdgCUyArKwc5icF/weYta4H1N1Ca5W7gMoJlyxz0nmIIYRjjPHWc4rOubP3/qGxb3+hB7CNeQEyr4QRJBKzEAAAAABJRU5ErkJggg==") 0 2px no-repeat;
    }

    .ztree >>> .button.chk {
        position: relative;
        width: 14px;
        height: 14px;
        margin: 0 0px 0 4px;
        border: 1px solid #d7dde4;
        border-radius: 2px;
        background: #fff;
    }

    .ztree >>> .chk.radio_true_full,
    .ztree >>> .chk.radio_false_full,
    .ztree >>> .chk.radio_true_full_focus,
    .ztree >>> .chk.radio_false_full_focus,
    .ztree >>> .chk.radio_false_disable,
    .ztree >>> .chk.radio_true_disable,
    .ztree >>> .chk.radio_true_part,
    .ztree >>> .chk.radio_false_part,
    .ztree >>> .chk.radio_true_part_focus,
    .ztree >>> .chk.radio_false_part_focus {
        border-radius: 8px;
    }

    .ztree >>> .button.chk:after {
        position: absolute;
        top: 1px;
        left: 4px;
        width: 4px;
        height: 8px;
        content: '';
        transition: -webkit-transform 0.2s ease-in-out;
        transition: transform 0.2s ease-in-out;
        transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
        -webkit-transform: rotate(0deg) scale(0);
        transform: rotate(0deg) scale(0);
        border-right: 2px solid #fff;
        border-bottom: 2px solid #fff;
    }

    .ztree >>> .button.checkbox_false_full_focus {
        border-color: #ccc;
    }

    .ztree >>> .button.checkbox_true_full,
    .ztree >>> .button.checkbox_true_full_focus,
    .ztree >>> .button.checkbox_true_part,
    .ztree >>> .button.checkbox_true_part_focus,
    .ztree >>> .button.checkbox_true_disable {
        border-color: #39f;
        background-color: #39f;
    }

    .ztree >>> .button.checkbox_true_full:after,
    .ztree >>> .button.checkbox_true_full_focus:after,
    .ztree >>> .button.checkbox_true_disable:after {
        -webkit-transform: rotate(45deg) scale(1);
        transform: rotate(45deg) scale(1);
    }

    .ztree >>> .button.checkbox_true_part:after,
    .ztree >>> .button.checkbox_true_part_focus:after {
        top: 5px;
        left: 2px;
        width: 10px;
        height: 1px;
        -webkit-transform: rotate(0deg) scale(1);
        transform: rotate(0deg) scale(1);
        border-right: 0;
    }

    .ztree >>> .button.radio_true_full,
    .ztree >>> .chk.radio_true_full_focus,
    .ztree >>> .chk.radio_true_part,
    .ztree >>> .chk.radio_true_part_focus {
        border-color: #39f;
    }

    .ztree >>> .button.radio_true_full:after,
    .ztree >>> .chk.radio_true_full_focus:after,
    .ztree >>> .chk.radio_true_part:after,
    .ztree >>> .chk.radio_true_part_focus:after {
        top: 3px;
        left: 3px;
        width: 8px;
        -webkit-transform: rotate(0deg) scale(1);
        transform: rotate(0deg) scale(1);
        border: 0;
        border-radius: 4px;
        background: #39f;
    }

    .ztree >>> .button.checkbox_true_disable,
    .ztree >>> .button.checkbox_false_disable,
    .ztree >>> .chk.radio_false_disable,
    .ztree >>> .chk.radio_true_disable {
        cursor: not-allowed;
    }

    .ztree >>> .button.checkbox_false_disable {
        background-color: #f3f3f3;
    }

    .ztree >>> .button.noline_close:before,
    .ztree >>> .button.noline_open:before,
    .ztree >>> .button.root_open:before,
    .ztree >>> .button.root_close:before,
    .ztree >>> .button.roots_open:before,
    .ztree >>> .button.roots_close:before,
    .ztree >>> .button.bottom_open:before,
    .ztree >>> .button.bottom_close:before,
    .ztree >>> .button.center_open:before,
    .ztree >>> .button.center_close:before {
        /* position: absolute;
        top: 5px;
        left: 5px;
        content: '';
        transition: -webkit-transform ease 0.3s;
        transition: transform ease 0.3s;
        transition: transform ease 0.3s, -webkit-transform ease 0.3s;
        -webkit-transform: rotateZ(0deg);
        transform: rotateZ(0deg);
        -webkit-transform-origin: 25% 50%;
        transform-origin: 25% 50%;
        border: 6px solid;
        border-color: transparent transparent transparent #666; */
    }

    .ztree >>> .button.noline_open:before,
    .ztree >>> .button.root_open:before,
    .ztree >>> .button.roots_open:before,
    .ztree >>> .button.bottom_open:before,
    .ztree >>> .button.center_open:before {
        /* -webkit-transform: rotateZ(90deg);
        transform: rotateZ(90deg); */
    }

    .ztree >>> .button.ico_loading {
        background: url('data:image/gif;base64,R0lGODlhEAAQAKIGAMLY8YSx5HOm4Mjc88/g9Ofw+v///wAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFCgAGACwAAAAAEAAQAAADMGi6RbUwGjKIXCAA016PgRBElAVlG/RdLOO0X9nK61W39qvqiwz5Ls/rRqrggsdkAgAh+QQFCgAGACwCAAAABwAFAAADD2hqELAmiFBIYY4MAutdCQAh+QQFCgAGACwGAAAABwAFAAADD1hU1kaDOKMYCGAGEeYFCQAh+QQFCgAGACwKAAIABQAHAAADEFhUZjSkKdZqBQG0IELDQAIAIfkEBQoABgAsCgAGAAUABwAAAxBoVlRKgyjmlAIBqCDCzUoCACH5BAUKAAYALAYACgAHAAUAAAMPaGpFtYYMAgJgLogA610JACH5BAUKAAYALAIACgAHAAUAAAMPCAHWFiI4o1ghZZJB5i0JACH5BAUKAAYALAAABgAFAAcAAAMQCAFmIaEp1motpDQySMNFAgA7') 0 center no-repeat;
    }

    .ztree >>> .button.ico_docu {
        width: 17px;
        height: 17px;
        margin: 2px 0 0 2px;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAARklEQVQ4T2NkoBAwwvTv37//PylmOTo6gvWiGAATJGQQyLJBbgByeGDz1hDzAnKMwLwz6gUGBtqFAaE8gC1G4JmJFM3IagG1XnQRLFpm/AAAAABJRU5ErkJggg==') center center no-repeat;
        background-size: contain;
    }

    .ztree >>> .button.ico_open {
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA80lEQVQ4T6XTO07DQBAG4FnLB6DgCFRJSQpaJM7AEUhtyfOvJRpoZ7elIA09pAgiZYpcINeghgvMICPFMtaSteRtd+abx2odTTxuYj79AiJiQ8jMHr33D7kCHQCg6ybGOFPVVzNb55Ak0FY9IkQ0T3VxLPgvcKr1duQkkNrFCWgP4Lq/xFsiugGwzC0uhHBPROfMXPWBNyLaAVjlABF5N7MX7/3mzzMWRbGo6/qQA0IIn2VZzqqq+uoDBwCLXHKM8VJVnwBctbF9YDVmfhG5c85dMDOGwHLk/M/OuS0zfwyBXPfdvaqeNU3z3QGjMxOBk3/jD81uexGwPysYAAAAAElFTkSuQmCC') 0 0px no-repeat;
    }

    .ztree >>> .button.ico_close {
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAZElEQVQ4T2NkoBAwUqifAWxAV1fXf3SD/v//31heXt5AyAK4AWVlZXDX9PT0aP3792/V////1xAyBKsBIFthhjAwMGjjcgXIUpwGEHI6yNujBgy/MCAU79jk4emAHM0wPRTnRgCdgGYRfafh3AAAAABJRU5ErkJggg==') 0 0px no-repeat;
    }

    .ztree >>> .tmpTargetzTree {
        opacity: 0.8;
        background-color: #2EA9DF;
        filter: alpha(opacity=80);
    }

    .ztree >>> .tmpzTreeMove_arrow {
        position: absolute;
        width: 18px;
        height: 18px;
        color: #4fcbf0;
    }

    .ztree >>> .ztree.zTreeDragUL {
        overflow: hidden;
        position: absolute;
        width: auto;
        height: auto;
        margin: 0;
        padding: 0;
        opacity: 0.8;
        border: 1px #176b53 dotted;
        background-color: #dbdbdb;
        filter: alpha(opacity=80);
    }

    .zTreeMask {
        position: absolute;
        z-index: 10000;
        opacity: 0.0;
        background-color: #cfcfcf;
        filter: alpha(opacity=0);
    }
    .icon-div-class {
      display: inline-block;
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translate(0, -50%);
    }
    .iconCustom {
      display: none;
      /* position: absolute;
      transform: translate(50%, 50%); */
    }

    .ztree >>> .tmpTargetNode_inner,
    .ztree >>> .tmpTargetNode_prev,
    .ztree >>> .tmpTargetNode_next {
      background: none;
      color: #555555;
    }
    .ztree >>> .tmpTargetNode_prev {
      border-top: 1px solid #409EFF;
    }
    .ztree >>> .tmpTargetNode_next {
      border-bottom: 1px solid #409EFF;
    }
    .ztree >>> li a {
      /* transition: margin .2s; */
    }
    .ztree >>> li a.noAllowDrag,
    .ztree >>> li a span.noAllowDrag {
      cursor: no-drop;
    }
    .ztree >>> li a.noAllowDrag.tmpTargetNode_prev {
      border-top: none;
    }
    .ztree >>> li a.noAllowDrag.tmpTargetNode_next {
      border-bottom: none;
    }
    .ztree >>> .tmpTargetNode_inner {
      opacity: 1;
    }

    .noAllowDrag {
      cursor: no-drop !important;
    }
    ::v-deep .el-input-group__append {
      padding: 0;
      overflow: hidden;
    }
    ::v-deep .el-input-group__append .el-button {
      margin: -10px 0 !important;
    }
</style>
