<template>
  <el-popover
    placement="bottom"
    width="330"
    trigger="click"
    v-model="visible"
    @show="popoverShow"
    >
    <easy-tree
      ref="easyTreeRef"
      v-bind="$attrs"
      v-on="$listeners"
      v-model="modelVal"
      style="height: 350px"
      :btnSwitch="composeBtnSwitch"
      :showCheckbox="composeShowCheckbox"
      :treeProps="composeTreeProps"
      @onCreated="onCreated"
      @node-click="handleNodeClick"
    />
    <el-input
      v-model="showValue"
      class="popoverValue"
      readonly
      slot="reference"
      :placeholder="placeholder"
      v-on:mouseover.native="mouseover()"
      v-on:mouseleave.native="mouseleave()">
      <i v-show="showIcon" slot="suffix" class="el-input__icon el-icon-circle-close" @click="handlClear"></i>
    </el-input>
  </el-popover>
</template>

<script>
export default {
  name: 'popoverEasyTree',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    treeProps: {
      type: Object,
      default: () => ({})
    },
    showCheckbox: {
      type: Boolean,
      default: undefined
    },
    setting: {
      type: Object,
      default: () => ({})
    },
    modKey: {
      type: String,
      default: ''
    },
    checkedValues: {
      type: Array,
      default: () => []
    },
    noModel: {
      type: Boolean,
      default: false
    },
    btnSwitch: {
      type: Object,
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: '请选择'
    }
  },
  data() {
    return {
      visible: false,
      showIcon: false,
      modelValue: [],
      selectNodes: [],
      easyTree: null,
      // 不适用composeTreeProps，这个是真实的
      realTreeProps: {}
    }
  },
  computed: {
    composeBtnSwitch() {
      if (this.$isNotEmpty(this.btnSwitch)) {
        return this.btnSwitch
      }
      return {
        checkEnable: this.setting.check?.enable
      }
    },
    modelVal: {
      get() {
        if (this.noModel) {
          return this.modelValue
        }
        return this.value
      },
      set(val) {
        if (this.noModel) {
          this.modelValue = val
          return
        }
        this.$emit('input', val)
      }
    },
    showValue() {
      const itemKeyMap = this.easyTree?.itemKeyMap || {}
      const { label } = this.realTreeProps
      const showVal = []
      this.modelVal?.forEach(val => {
        const node = itemKeyMap[val]
        if (node?.[label]) {
          showVal.push(node[label])
        }
      })
      return showVal.join(',')
    },
    composeTreeProps() {
      if (this.$isNotEmpty(this.treeProps)) {
        return this.treeProps
      }
      const { data = {}} = this.setting
      const { simpleData = {}, key = {}} = data
      const props = {}
      if (simpleData.idKey) {
        props.idKey = simpleData.idKey
        props.id = simpleData.idKey
      }
      if (simpleData.pIdKey) {
        props.parentId = simpleData.pIdKey
      }
      if (key.id) {
        props.idKey = key.id
      }
      if (key.name) {
        props.label = key.name
      } else {
        props.label = 'name'
      }
      if (key.children) {
        props.children = key.children
      }
      return props
    },
    composeShowCheckbox() {
      if (this.$isNotEmpty(this.showCheckbox)) {
        return this.showCheckbox
      }
      return this.setting?.check?.enable
    }
  },
  watch: {
    checkedValues() {
      this.resetCheck()
    }
  },
  methods: {
    // checkedValues兼容base-page的回填（使用v-model在预算对比表会卡顿）
    resetCheck() {
      if (this.$isNotEmpty(this.checkedValues) && this.easyTree && this.noModel) {
        this.easyTree.resetInit([...this.checkedValues, ...this.modelVal])
        this.easyTree.resetValue()
      }
    },
    onCreated(easyTree) {
      this.easyTree = easyTree
      this.realTreeProps = easyTree.composeTreeProps
      this.resetCheck()
      // search条件时需要当前实例
      this.$emit('onTreeCreated', this)
    },
    popoverShow() {
      this.visible = true
      const id = this.realTreeProps.id
      const itemKeyMap = this.easyTree.itemKeyMap || {}
      this.easyTree.delExKeys = this.modelVal.map(key => itemKeyMap[key][id])
    },
    clearCheck() {
      this.easyTree.$refs.veTree.setCheckedAll(false)
      this.easyTree.$refs.veTree.setCurrentKey(null)
    },
    handlClear() {
      this.modelVal = []
      this.clearCheck()
      // basepage的树没有v-model清除的时候有问题
      this.$emit('clear')
    },
    mouseover() {
      this.showIcon = true
    },
    mouseleave() {
      this.showIcon = false
    },
    handleNodeClick(nodeData, node, comp) {
      if (!this.composeShowCheckbox) {
        this.visible = false
      }
    },
    getCheckedNodes() {
      return this.easyTree.getCheckedNodes()
    },
    getParentNode(node) {
      return this.easyTree.getParentNode(node)
    },
    reset() {
      this.easyTree.reset()
    }
  }
}
</script>

<style lang="scss" scoped>
.popoverValue .el-icon-circle-close {
  cursor: pointer;
}
.popoverValue ::v-deep input.el-input__inner {
  padding-right: 25px!important;
}
</style>
