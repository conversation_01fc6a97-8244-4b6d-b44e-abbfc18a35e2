<template>
  <el-input v-model="filterText" @keyup.enter.native="onClickButton('search')" placeholder="Enter搜索" class="searchInputClass" clearable>
      <el-button size="mini" v-if="btnSwitch.showRefresh" slot="append" icon="el-icon-refresh" @click="onClickButton('refresh')" title="刷新" circle></el-button>
      <el-button size="mini" v-if="btnSwitch.showEdit" slot="append"  type="success" icon="el-icon-circle-plus-outline"  @click="onClickButton('onClickAdd')" circle></el-button>
      <el-button size="mini" v-if="btnSwitch.showEdit" slot="append"  icon="el-icon-edit" title="修改" @click="onClickButton('onClickEdit')" circle></el-button>
      <el-button size="mini" v-if="btnSwitch.showEdit" slot="append" icon="el-icon-delete"   @click="onClickButton('onClickDel')" circle />
      <el-button size="mini" v-if="btnSwitch.showCopy" slot="append" icon="el-icon-document-copy"   @click="onClickButton('onClickCopy')" circle />
      <el-button size="mini" v-if="btnSwitch.clearCheck" slot="append" icon="el-icon-delete"   @click="onClickButton('clearCheck')" circle />
      <el-button size="mini" v-if="btnSwitch.showExpandAll" slot="prepend" @click="expandAll" :title="isExpandAll?'折叠':'展开'" :icon="isExpandAll?'el-icon-remove-outline':'el-icon-circle-plus-outline'" circle/>
      <el-button slot="append" style=" border-left:1px solid #DCDFE6;" v-if="btnSwitch.checkEnable&&isAllSelected" @click="onClickButton('selectAll')">全选
      </el-button>
      <el-button slot="append" style=" border-left:1px solid #DCDFE6;" v-if="btnSwitch.checkEnable&&!isAllSelected" @click="onClickButton('clearCheck')">取消全选
      </el-button>
  </el-input>
</template>

<script>
export default {
  name: 'treeSearch',
  props: {
    btnSwitch: {
      type: Object,
      default: function() {
        return {
          showEdit: false,
          showRefresh: false,
          showExpandAll: false,
          clearCheck: false
        }
      }
    }
  },
  data() {
    return {
      filterText: '',
      isExpandAll: false,
      isAllSelected: true
    }
  },
  methods: {
    reset() {
      this.filterText = ''
    },
    onClickButton(emit) {
      if (emit === 'selectAll' || emit === 'clearCheck') {
        this.isAllSelected = !this.isAllSelected
      }
      this.$emit('onClickButton', emit, this.filterText)
    },
    expandAll() {
      this.isExpandAll = !this.isExpandAll
      this.onClickButton('expandAll', this.isExpandAll)
    }
  }
}
</script>

<style>

</style>