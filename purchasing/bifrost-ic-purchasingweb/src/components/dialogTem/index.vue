<template>
  <div class="eldg">
    <el-dialog
      :width="width"
      :height="height"
      :close-on-click-modal="closeByClickModal"
      :close-on-press-escape="closeOonClickEsc"
      :top="marginTop"
      v-el-drag-dialog
      v-if="isDrag && dialogTableVisible"
      :modal="modal"
      :custom-class="customClass"
      :fullscreen="fullscreen"
      :append-to-body="appendDialog"
      :show-close="showClose"
      :visible.sync="dialogTableVisible"
      :before-close="handleClose"
    >
      <div slot="title"><div style="font-size:16px;">{{ title }}</div></div>
      <div :style="{ padding: contentPadding }">
        <slot name="content"> </slot>
      </div>
      <div v-if="showFooter" slot="footer">
        <slot name="footer"> </slot>
      </div>
    </el-dialog>
    <el-dialog
      :width="width"
      :height="height"
      :close-on-click-modal="closeByClickModal"
      :close-on-press-escape="closeOonClickEsc"
      :top="marginTop"
      v-if="!isDrag && dialogTableVisible"
      :modal="modal"
      :fullscreen="fullscreen"
      :append-to-body="appendDialog"
      :show-close="showClose"
      :visible.sync="dialogTableVisible"
      :before-close="handleClose"
    >
    <div slot="title"><div style="font-size:16px;">{{ title }}</div></div>
      <div :style="{ padding: contentPadding, backgroundColor: '#ffffff' }">
        <slot name="content"> </slot>
      </div>
      <div v-if="showFooter" slot="footer">
        <slot name="footer"> </slot>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
//   import elDragDialog from "@/directive/el-dragDialog";
//   import storage from "@/utils/storage";
export default {
  name: "dialogTem",
  // directives: { elDragDialog },
  props: {
    customClass: {
      type: String,
    },
    iconClass: {
      type: String,
      default: "",
    },
    closeByClickModal: {
      type: Boolean,
      default: false,
    },
    closeOonClickEsc: {
      type: Boolean,
      default: true,
    },
    modal: {
      type: Boolean,
      default: true,
    },
    width: {
      type: String,
      default: "40%",
    },
    height: {
      type: String,
      default: "500px",
    },
    appendDialog: {
      type: Boolean,
      default: true,
    },
    title: {
      // 标题
      type: String,
      default: "提示",
    },
    marginTop: {
      type: String,
      default: "5vh",
    },
    contentPadding: {
      // 内容边距
      type: String,
      // default: "32px 40px",
      default: "0",
    },
    showFooter: {
      // 是否显示底部
      type: Boolean,
      default: true,
    },
    isDrag: {
      // 是否可以拖动
      type: Boolean,
      default: false,
    },
    showClose: {
      // 是否有关闭按钮
      type: Boolean,
      default: true,
    },
    isSureBtnClose: {
      type: Boolean,
      default: true,
    },
    sureBtnName: {
      type: String,
      default: "确 定",
    },
  },
  computed: {
    getIconClass() {
      return this.iconClass;
    },
  },
  data() {
    return {
      dialogTableVisible: false,
      fullscreen: false,
    };
  },
  watch: {
    dialogTableVisible(val) {
      if (val) {
        this.openEvent();
      } else {
        this.closeEvent();
      }
    },
  },
  methods: {
    openEvent() {
      this.$emit("openEvent");
    },
    closeEvent() {
      this.$emit("closeEvent");
    },
    handleClose(done) {
      this.$emit("handleClose");
      done();
    },
    closeDialog() {
      this.dialogTableVisible = false;
    },
    openDialog() {
      this.dialogTableVisible = true;
    },
    sure() {
      if (this.isSureBtnClose) {
        this.dialogTableVisible = false;
      }
      this.$emit("sureClose");
    },
    cancel() {
      this.dialogTableVisible = false;
      this.$emit("cancelClose");
    },
  },
};
</script>
  
  <style type="text/scss" lang="scss">
  .el-dialog .el-dialog__title {
    font-size: 16px;
    color: #333333;
    line-height: 26px;
    font-weight: 500;
  }
</style>
  