<template>
    <el-dialog ref="globalDialog"
        append-to-body :title="title" class="globalDialog" id="dynamicDlg-global"
        :visible.sync="globalDialogVisible" :close-on-click-modal='false'
        @close="onDlgClose">
        <page>
            <template #pageContent>
                <div class="globalDialogContent">
                    <div class="globalDialogHeader">
                        <component ref="globalDialogContent" :is="contentId"/>
                    </div>
                    <component ref="pagerContent" :is="pagerContentId"/>
                    <div slot="footer" class="globalDialogFooter" v-show="dlgFooterVisible">
                        <el-button class="btn-normal" @click="globalDialogVisible = false">取消</el-button>
                        <el-button class="btn-normal" ref="btOK" type="primary" :loading="loading" @click="ok">{{btOkText}}</el-button>
                    </div>
                    <loading ref="dlgLoading"/>
                </div>
            </template>
        </page>
    </el-dialog>
</template>

<script>
import $ from 'jquery'
import Loading from '../loading/index'
export default {
  name: 'dynamicDlg',
  provide() {
    return {
      setGlobalTitle: this.setGlobalTitle
    }
  },
  components: { Loading },
  data() {
    return {
      title: '',
      contentId: '', // 弹框内容组件ID
      btOkText: '',
      loading: false,
      pagerContentId: '',
      globalDialogVisible: false, // 是否显示弹框
      dlgFooterVisible: true,
      params: {},
      callbackOK: data => {} // 确认时的回调函数
    }
  },
  created() {
    this.$onEvent(this, {
      refTargetDbClick: data => { // 参照双击选择
        this.doOk(data)
      },
      refDataInitFailed: () => { // 初始化错误关闭弹框
        this.globalDialogVisible = false
      }
    })
  },
  methods: {
    setGlobalTitle(title) {
      this.title = title
    },
    show(params, callbackOK) {
      this.setTitle(params.exParams, params.contentId)
      this.params = params || {}
      this.callbackOK = callbackOK
      this.contentId = this.params.contentId
      this.globalDialogVisible = true
      this.dlgFooterVisible = true
      this.params.isOK = false
      this.pagerContentId = ''
      this.btOkText = params.btOkText || '确定'

      // 消除可能存在的行编辑样式影响
      $('#dynamicDlg-global').removeClass('dynamicDlgHasBaseList')
      $('#dynamicDlg-global').removeClass('dynamicDlgHasBaseListNoButton')

      if (this.$isNotEmpty(this.params.dlgFooterVisible)) {
        this.dlgFooterVisible = this.params.dlgFooterVisible
      }

      this.$setDlgSize(this, 'globalDialog', 0, 0)
      this.$nextTick(() => {
        this.loading = false
        this.$refs.globalDialogContent.init(this, this.params)
      })
    },
    setTitle(dlgTitle, contentId) { // 设置弹框标题
      if (!['dynamic-tab', 'form-tab-save'].includes(contentId)) { // 判断组件的类型是否带有tab页 带有tab页的需要去掉标题否则显示标题
        this.title = dlgTitle
      } else {
        this.title = ''
      }
      $('.globalDialog .el-dialog').removeClass('noDlgHeader')
      if (this.$isEmpty(this.title)) {
        $('.globalDialog .el-dialog').addClass('noDlgHeader')
      }
    },
    onDlgClose() {
      if (typeof this.$refs.globalDialogContent.onDlgClose === 'function') {
        this.$refs.globalDialogContent.onDlgClose(this.params)
      }
    },
    ok() {
      var data
      if (this.$refs.globalDialogContent.getDataOK) {
        data = this.$refs.globalDialogContent.getDataOK()
      }
      this.doOk(data)
    },
    doOk(data) {
      if (data && data.error) {
        this.$message.error(data.error)
      } else {
        this.loading = true
        this.params.isOK = true
        if (typeof this.callbackOK === 'function') {
          if (this.params.closeDlgByOutside) { // 由调用方负责关闭弹框
            this.callbackOK(data,
              () => { this.closeDlg() },
              () => { this.loading = false }
            )
          } else {
            this.callbackOK(data,
              () => { this.closeDlg() },
              () => { this.loading = false }
            )
            this.closeDlg()
          }
        } else {
          this.closeDlg()
        }
      }
    },
    closeDlg() {
      this.loading = false
      this.globalDialogVisible = false
    },
    setCallbackOK(cb) { // 设置点击确定按钮是调用的自定义函数
      this.callbackOK = cb
    }
  }
}
</script>

<style lang="scss">
.globalDialog .formCanvasRuntimeHasAssemblyFree #formFreeView {padding: 0px;margin-bottom: 0px;border: none; }
.globalDialog .el-tabs__nav-wrap::after{height: 1px;background-color: #EEEEEE;}
// .globalDialog .el-dialog__header { padding: 15px 20px 2px !important; }
.globalDialog .el-dialog__body {
    padding-bottom: 0px;height: calc(100% - 60px) !important; }
.globalDialog .el-dialog__headerbtn { z-index: 2001 }
.globalDialogContent { display: flex;flex-direction:column;height: 100%; }
.globalDialogHeader { flex: auto; overflow: auto; height: 100%; }
.globalDialogFooter { text-align: right;margin-top: 32px }

.globalDialog .el-tabs { display: flex;flex-direction: column;height: 100%; }
.globalDialog .el-tabs__content { flex: 1; }
.globalDialog .refTable .el-tabs__content { height: calc(100% - 60px); }
#dynamicDlg-global .common-page .column-bottom {height: calc(100% - 0px) !important;}
.globalDialog .common-page .buttons-normal{padding: 0;}
.globalDialog .el-tab-pane { height: 100%; overflow: auto; }
.globalDialog .el-tabs__content .mini-table {
    margin-top: 5px;height: calc(100% - 5px) !important; }
.globalDialog .el-tabs__item { padding: 0px 10px; }
.globalDialog .dynamic-tab .el-tabs__item { height: 32px;line-height: 32px; }
/*.globalDialog .dynamic-tab .el-tabs__active-bar { background: none; }*/
// .globalDialog .el-table thead.is-group th.el-table__cell {
    // border-right: 1px solid #bbb !important;}

// .globalDialog .noDlgHeader .el-dialog__body { padding-top: 0px; }
.globalDialog .noDlgHeader .el-dialog__header { padding: 0px !important;border: none; }
.globalDialog .noDlgHeader .el-dialog__body {
    padding-bottom: 0px;height: calc(100% - 30px) !important; }
.noDlgHeader .globalDialogFooter { text-align: right; margin-top: 15px; }
.globalDialog .refTablePager .el-pagination--small .el-pager li { font-size: 14px; }

#dynamicDlg-global.dynamicDlgHasBaseList .el-dialog__body { padding-top: 2px; }
#dynamicDlg-global.dynamicDlgHasBaseList .common-page .common-page .column-bottom { height: calc(100% - 30px) !important; }
#dynamicDlg-global.dynamicDlgHasBaseList.dynamicDlgHasBaseListNoButton .el-dialog__body { padding-top: 8px; }
#dynamicDlg-global.dynamicDlgHasBaseList.dynamicDlgHasBaseListNoButton .common-page .common-page .column-bottom { height: calc(100% - 0px) !important; }
</style>
