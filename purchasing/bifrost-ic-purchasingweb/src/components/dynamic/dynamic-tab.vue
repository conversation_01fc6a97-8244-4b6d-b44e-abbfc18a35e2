<template>
    <el-tabs class="dynamic-tab" v-model="activeTab" @tab-click="tabClick">
        <el-tab-pane
            :key="tab.name"
            :label="tab.label"
            :name="tab.name"
            v-for="tab in tabs">
            <keep-alive>
                <component ref="tabComponents" :is="tab.name"/>
            </keep-alive>
        </el-tab-pane>
    </el-tabs>
</template>
<script>
import $ from 'jquery'
export default {
  name: 'dynamic-tab',
  data() {
    return {
      dlg: {},
      params: {},
      activeTab: '',
      tabs: [],
      tabsIndex: {},
      tabNameNoIndexMap: {},
      tabVisibleData: {},
      tabNames: [],
      tabNamesHideByConfig: []
    }
  },
  methods: {
    init(dlg, params) {
      if (params.dynamicTabs) {
        if (params.dynamicTabs.length > 10) {
          this.$message.error('动态tab不能大于10个')
          return
        }

        this.tabs = []
        this.tabsIndex = {}
        this.dlg = dlg
        this.params = params
        this.params['dynamic-tab'] = this
        this.tabNameNoIndexMap = {}

        for (var i = 0; i < params.dynamicTabs.length; i++) {
          var tabStr = params.dynamicTabs[i]
          var label = this.$resolveTabTitle(tabStr)
          this.tabs.push({ 'name': tabStr, 'label': label })
          this.tabsIndex[tabStr] = i
          this.tabNames.push(tabStr)

          var keyNoIndex = this.$resolveTabNameNoIndex(tabStr)
          this.tabNameNoIndexMap[keyNoIndex] = tabStr
        }

        this.$nextTick(() => {
          this.tabClick(this.tabs[0])
          this.activeTab = this.tabs[0].name
        })
        const _this = this
        setTimeout(function() {
          _this.initAllTabs()
        }, 1000)
      }
    },
    // 支持动态设置tab的可见性，tabKey可以只传递不包含位置项的tab名称
    // 比如：AAA-BBB-2，调用这个方法时tabKey可以是AAA-BBB
    // isByConfig=true时，表示目前调用是根据系统参数处理tab隐藏
    setEditTabVisible(tabKey, isVisible, isByConfig) {
      var finalKey = tabKey
      var tabKeyByNoIndex = this.tabNameNoIndexMap[tabKey]
      if (tabKeyByNoIndex) {
        finalKey = tabKeyByNoIndex
      }

      // 如果是系统参数已经隐藏的tab，则不再响应setEditTabVisible的处理
      if (this.tabNamesHideByConfig.indexOf(finalKey) > -1) {
        return
      }

      // 记录系统参数决定隐藏的tab
      if (isVisible === false && isByConfig === true) {
        this.tabNamesHideByConfig.push(finalKey)
      }

      var $tabPane = $('#tab-' + finalKey)
      this.tabVisibleData[finalKey] = isVisible
      if (isVisible) {
        $tabPane.show()
      } else {
        $tabPane.hide()
      }
    },
    // 支持动态设置tab的标题，tabKey可以只传递不包含位置项的tab名称
    // 比如：AAA-BBB-2，调用这个方法时tabKey可以是AAA-BBB
    setEditTabLabel(tabKey, label) {
      var finalKey = tabKey
      var tabKeyByNoIndex = this.tabNameNoIndexMap[tabKey]
      if (tabKeyByNoIndex) {
        finalKey = tabKeyByNoIndex
      }
      this.tabs.forEach(tab => {
        if (tab.name === finalKey) {
          tab.label = label
        }
      })
    },
    onDlgClose() {
      for (var i = 0; i < this.$refs.tabComponents.length; i++) {
        if (typeof this.$refs.tabComponents[i].onDlgClose === 'function') {
          this.$refs.tabComponents[i].onDlgClose(this.params)
        }
      }
    },
    tabClick(tab) {
      var tabName = tab.name
      var index = this.tabsIndex[tabName]
      var isTabHide = (this.tabVisibleData[tabName] === false)
      if (index !== undefined && isTabHide !== true) {
        this.$refs.tabComponents[index].init(this.dlg, this.params)
      }
    },
    tabClickByLabel(tabLabel) {
      var tabName
      this.tabs.forEach(tab => {
        if (tab.label === tabLabel) {
          tabName = tab.name
        }
      })
      if (this.$isNotEmpty(tabName)) {
        this.tabClick({ name: tabName })
      }
    },
    initAllTabs() { // 调用除第一个tab之外的tab的init方法
      for (let i = 1; i < this.$refs.tabComponents.length; i++) {
        this.$refs.tabComponents[i].init(this.dlg, this.params)
      }
    }
  }
}
</script>

<style lang="scss">
</style>
