<!--
 * @Description:
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-15 11:53:43
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-09 17:27:25
-->
<template>
    <el-dialog ref="customMessageDlg"
        custom-class="custom-message-dlg"
        append-to-body :title="title"
        :visible.sync="customMessageDlgVisible"
        :close-on-click-modal='false'
        @close="onDlgClose">
        <div class="customMessageDlg">
            <div style="display: flex;">
                <div class="el-icon-warning"/>
                <div class="customMessageContent">
                    <div>{{message}}</div>
                    <div>
                        <component ref="customMessageContent" :is="contentId"/>
                    </div>
                </div>
            </div>
            <div slot="footer" class="footer" style="text-align: right;">
                <el-button ref="btCancel" @click="customMessageDlgVisible = false">取消</el-button>
                <el-button ref="btOK" type="primary" @click="ok">{{btOkText}}</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script>
export default {
  name: 'custom-message-dlg',
  data() {
    return {
      title: '提示',
      btOkText: '确定',
      message: '',
      contentId: '',
      customMessageDlgVisible: false, // 是否显示弹框
      callbackOK: undefined,
      callbackCancel: undefined
    }
  },
  methods: {
    show(width, height, message, contentId, callbackOK, callbackCancel, exParams) {
      this.message = message
      this.contentId = contentId
      this.btOkText = '确定'
      this.callbackOK = callbackOK
      this.callbackCancel = callbackCancel

      this.customMessageDlgVisible = true
      if (exParams?.title) {
        this.title = exParams.title
      }
      this.$setDlgSize(this, 'customMessageDlg', width, height)
      this.$nextTick(() => {
        this.setLoading(false)
        this.getComponent()?.init?.(exParams)
      })
    },
    onDlgClose() {
    },
    ok() {
      this.setLoading(true)
      this.$nextTick(() => {
        if (this.callbackOK) {
          this.callbackOK(this)
        } else {
          this.closeDlg()
        }
      })
    },
    setLoading(isLoading) {
      this.$refs.btOK.loading = isLoading
      this.$refs.btCancel.disabled = isLoading
      this.btOkText = isLoading ? '执行中...' : '确定'
    },
    closeDlg() {
      this.customMessageDlgVisible = false
    },
    getComponent() {
      return this.$refs.customMessageContent
    }
  }
}
</script>

<style lang="scss">
.customMessageDlg { padding-top: 10px; }
.customMessageDlg .blockFooter {position: absolute;right: 20px;bottom: 20px;}
.customMessageDlg .el-icon-warning { color: #E6A23C; font-size: 24px !important; }
.customMessageDlg .customMessageContent { padding: 2px 0px 0px 10px; }
</style>
