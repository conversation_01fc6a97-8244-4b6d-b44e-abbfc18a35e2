<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-06 17:10:12
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-21 17:20:55
-->
<template>
  <el-table ref="dynamicTable" :data="tableData" border v-bind="$attrs" style="width: 100%">
    <template v-for="column in columnsWithWidth">
      <column-renderer v-bind="column" :key="column.prop || column.label" />
    </template>
  </el-table>
</template>

<script>
// 递归渲染表头的组件
const ColumnRenderer = {
  name: '<PERSON>umn<PERSON><PERSON><PERSON>',
  functional: true,
  props: {
    prop: String,
    label: String,
    width: [String, Number],
    align: String,
    children: Array,
    formatter: String,
    type: String,
  },
  render(h, context) {
    const { props, parent } = context;
    if (props.children && props.children.length) {
      return h(
        'el-table-column',
        {
          props: {
            label: props.label,
            width: props.width,
            align: props.align,
            type: props.type,
          },
        },
        props.children.map(child => {
          return h(Column<PERSON><PERSON><PERSON>, {
            props: child,
            key: child.prop || child.label
          });
        })
      );
    } else {
      const columnProps = {
        prop: props.prop,
        label: props.label,
        width: props.width,
        align: props.align,
        type: props.type,
      }
      console.log(columnProps, 'columnProps')
      if (['index', 'selection'].includes(props.type)) {
        return h('el-table-column', { props: columnProps });
      }
      return h('el-table-column', {
        props: columnProps,
        scopedSlots: {
          default: scope => {
            if (props.formatter && parent?.$parent[props.formatter]) {
              return parent?.$parent[props.formatter](scope.row, props, scope.row[props.prop]);
            }
            return scope.row[props.prop];
          }
        }
      });
    }
  }
};

export default {
  name: 'DynamicTable',
  components: {
    ColumnRenderer
  },
  props: {
    columns: {
      type: Array,
      required: true
    },
    tableData: {
      type: Array,
      required: true
    }
  },
  computed: {
    columnsWithWidth() {
      return this.columns.map(column => {
        // 若没有设置 width，添加默认值
        return { ...column, width: column.width }; 
      });
    }
  },
  mounted() {
    this.$nextTick(() => {
      const tableBodyWrapper = this.$refs.dynamicTable.$el.querySelector('.el-table__body-wrapper');
      if (tableBodyWrapper) {
        tableBodyWrapper.addEventListener('scroll', this.handleTableScroll);
      }
    });
  },
  beforeDestroy() {
    const tableBodyWrapper = this.$refs.dynamicTable.$el.querySelector('.el-table__body-wrapper');
    if (tableBodyWrapper) {
      tableBodyWrapper.removeEventListener('scroll', this.handleTableScroll);
    }
  },
  methods: {
    handleTableScroll() {
      const tableBodyWrapper = this.$refs.dynamicTable.$el.querySelector('.el-table__body-wrapper');
      if (tableBodyWrapper) {
        // 检查是否滚动到最右边
        if (tableBodyWrapper.scrollLeft + tableBodyWrapper.clientWidth >= tableBodyWrapper.scrollWidth) {
          setTimeout(() => {
            this.$nextTick(() => {
              // 重置表格样式
              const tableEl = this.$refs.dynamicTable.$el;
              const originalWidth = tableEl.style.width;
              tableEl.style.width = '100%';
              this.$refs.dynamicTable?.doLayout();
              // 强制刷新表头
              const headerWrapper = this.$refs.dynamicTable.$el.querySelector('.el-table__header-wrapper');
              if (headerWrapper) {
                const originalDisplay = headerWrapper.style.display;
                headerWrapper.style.display = 'none';
                this.$nextTick(() => {
                  headerWrapper.style.display = originalDisplay;
                  // 手动同步表头和表体宽度
                  const bodyWrapper = this.$refs.dynamicTable.$el.querySelector('.el-table__body-wrapper');
                  if (bodyWrapper) {
                    bodyWrapper.style.width = `${headerWrapper.offsetWidth}px`;
                    // 同步滚动位置
                    const headerScroll = this.$refs.dynamicTable.$el.querySelector('.el-table__header-wrapper .el-scrollbar__wrap');
                    if (headerScroll) {
                      headerScroll.scrollLeft = tableBodyWrapper.scrollLeft;
                    }
                    this.$refs.dynamicTable.doLayout()
                    console.log('刷新')
                  }
                });
              }
              tableEl.style.width = originalWidth;
            });
          }, 300);
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep {
  .el-table--scrollable-y .el-table__body-wrapper.is-scrolling-right {
    width: calc(100% + 12px)!important; // 6px为竖向滚动条宽度
  }
}
</style>
