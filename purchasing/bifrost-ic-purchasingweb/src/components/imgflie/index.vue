<template>
  <div id="ImgFile">
    <el-form :model="imgFileForm" ref="imgFileForm" :rules="rulesFile" label-width="80px">
      <el-row v-if="!isView">
        <el-col :span="15" v-if="isSelectType">
          <el-form-item
            label="图片类型"
            :label-width="fileTypeLabelWidth"
            prop="imgFileType"
            style="margin-bottom: 5px"
          >
            <el-select
              v-model="imgFileForm.imgFileType"
              placeholder="请选择图片类型"
              clearable
              @change="imgTypechange"
            >
              <el-option
                v-for="item in fileTypeList"
                :key="item.name"
                :label="item.name"
                :value="item.name"
                :disabled='item.disabled'
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2" v-if="isSelectType">
          <el-button
            type="primary"
            class="box_btn"
            size="small"
            :style="!isSelectType ? '' : 'margin-left:16px'"
            @click="handleUpload"
            :disabled="isfilebtn === true ? isSelectType : !isSelectType"
            >{{ butText }}</el-button
          >
        </el-col>
        <el-col :span="2" v-if="isNotSelectType">
          <el-button
            class="box_btn"
            size="small"
            :style="!isSelectType ? '' : 'margin-left:16px'"
            @click="handleUpload"
            :disabled="isfilebtn === true ? isNotSelectType : !isNotSelectType"
            >{{ butText }}</el-button
          >
        </el-col>
        <el-col :span="2">
          <el-upload
            class="ElUpload"
            ref="upload"
            name="files"
            :show-file-list="false"
            :file-list="fileList"
            :limit="isFixdelimit ? limit : fileLength + limit"
            :multiple="isMultiple"
            :auto-upload="true"
            :http-request="submitUpload"
            :accept="acceptFileTypeList.join(',')"
            :before-upload="beforeUpload"
            :on-exceed="onExceed"
            action="customize"
          ></el-upload>
        </el-col>
      </el-row>
      <div v-if="!isView && isShowTip" class="el-upload__tip">
        只能上传{{ typeString }}文件，且不超过{{ maxFileSize }}M
      </div>
    </el-form>
    <el-table
      border
      class="table-border"
      style="width: 100%; margin-top: 10px"
      v-bind:[attribute]="300"
      ref="table"
      max-height="300"
      :data="temFileData"
    >
      <!-- <el-table-column
        type="index"
        label="序号"
        align="center"
        width="70"
      ></el-table-column> -->
      <el-table-column
        align="center"
        prop="fileName"
        label="附件名称"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        prop="condition"
        label="条件"
        show-overflow-tooltip
      >
      <template slot-scope="{ row }">
              <!-- <div class="textShow">
                {{ row[item] }}
              </div> -->
              <el-input :disabled="row.typeCode==='采购备案章'" v-model="row.condition" placeholder=""></el-input>
            </template>
      </el-table-column>
      <el-table-column
        v-if="isSelectType"
        align="center"
        prop="typeCode"
        label="附件类型"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.typeCode }}
        </template>
        <!-- <template slot-scope="scope">
          {{ flieType[scope.row.typeCode] }}
        </template> -->
      </el-table-column>
      <!-- <el-table-column
        align="center"
        prop="createTime"
        label="上传日期"
        show-overflow-tooltip
      ></el-table-column> -->
      <!-- <el-table-column
        align="center"
        prop="createUserName"
        label="上传人员"
        show-overflow-tooltip
      ></el-table-column> -->
      <el-table-column align="center" width="90" label="操作" prop="progress">
        <template slot-scope="scope">
          <!-- <el-progress
            v-if="scope.row.progress !== 100"
            :text-inside="true"
            :stroke-width="14"
            :percentage="scope.row.progress"
          ></el-progress> -->
          <el-button
            v-if="scope.row.progress === 100 && !isView"
            type="text"
            @click="handleDelete(scope.row, scope.$index)"
            >删除</el-button
          >
          <!-- <el-button
            v-if="isCanView && isView"
            type="text"
            @click.stop="handelViewFile(scope.row.id)"
            >预览</el-button
          > -->
          <!-- <el-button
            v-if="isCanDownload && isView"
            type="text"
            @click.stop="handelDownLoad(scope.row.id)"
            >下载</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { upload } from '@/api/file/file'
export default {
  name: 'ImgFile',
  props: {
    // 表格数据
    fileTableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 业务id
    bizId: {
      type: String
    },
    // 按钮名称
    butText: {
      type: String,
      default: '上传图片'
    },
    // 附件类型的labelWidth
    fileTypeLabelWidth: {
      type: String
    },
    // 是否查看
    isView: {
      type: Boolean,
      default: false
    },
    // 是否需选择类型
    isSelectType: {
      type: Boolean,
      default: false
    },
    // 是否需选择类型（单按钮）
    isNotSelectType: {
      type: Boolean,
      default: false
    },
    // 文件格式
    typeString: {
      type: String,
      default: 'jpg、png、jpeg、gif'
    },
    // 文件最多大小
    maxFileSize: {
      type: Number,
      default: 5
    },
    // 是否需提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    isFixdelimit: {
      type: Boolean,
      default: false
    },
    // 最大允许上传个数
    limit: {
      type: Number,
      default: 10
    },
    // 是否支持多选文件
    isMultiple: {
      type: Boolean,
      default: false
    },
    acceptFileTypeList: {
      type: Array,
      default: () => {
        return [
          'image/jpeg    JPEG',
          'image/png    Portable Network Graphics'
        ]
      }
    },
    attribute: {
      type: String,
      default: ''
    }
  },
  watch: {
    fileTableData: {
      immediate: true, // 这句重要
      handler(val) {
        this.imgFileForm.imgFileType = ''
        this.oldfileIds = val.map((item) => item.id)
        this.temFileData = val
        this.temFileData.forEach(item => {
          item.progress = 100
        })
        this.fileList = val.map((item) => {
          item.name = item.fileName
          return item
        })
      }
    },
    fileTypeList: {
      immediate: true, // 这句重要
      handler(val) {
        if (val) {
          val.forEach(item => {
            this.typeCodelist[item.CODE] = item.NAME
          })
        }
        if (val.length === 1) {
          this.imgFileForm.imgFileType = val[0].CODE
        } else {
          this.imgFileForm.imgFileType = ''
        }
      }
    },
    temFileData: {
      immediate: true, // 这句重要
      handler(val) {
        if (val) {
          this.temFileData.some((i, index) => {
            if (i.typeCode === '1') {
              // 二维码
              i.disabled = true
            }
            if (i.typeCode === '1') {
              // 条形码
              i.disabled = true
            }
          })
        }
      }
    }
  },
  data() {
    return {
      fileList: [], // 判断限制数量
      temFileData: [],
      isfilebtn: false,
      imgFileForm: {
        imgFileType: ''
      },
      rulesFile: {
        imgFileType: [
          { required: true, message: '请选择图片类型', trigger: 'blur' }
        ]
      },
      fileTypeList: [],
      fileLength: 0,
      typeCodelist: {},
      oldfileIds: [],
      imgUrl: '',
      ipUrl: '',
      ids: [], // 存放图片id
      condition: '',
      imgFileObj: [],
      dataInfoObj: []
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init(bizId) {
      // 切换表单更新bizId
      const params = {}
      params.formId = this.$isNotEmpty(bizId) ? bizId : this.bizId
      this.bizId = this.$isNotEmpty(bizId) ? bizId : this.bizId
      // 图片回显
      this.$callApiParams('selectPictureList', params, result => {
        if (result.data) {
          this.fileTableData = result.data || ''
          this.ids = []
          for (let i = 0; i < this.fileTableData.length; i++) {
            this.ids.push(this.fileTableData[i].pictureNo)
          }
        }
        return true
      })
      // 初始化下拉数据
      this.fileTypeList = []
      this.$callApiParams('selectPictureMeta',
        {}, result => {
          if (result.data) {
            this.fileTypeList = result.data
          }
          return true
        })
      // 初始化图片上传URL
      this.$callApiParams('getPictureUrl',
        {}, result => {
          if (result.data) {
            this.ipUrl = result.data
          }
          return true
        })
    },
    getVal() {
      return { temFileData: this.temFileData, bizId: this.bizId }
    },
    // 点击文件上传按钮进行附件表单的验证
    handleUpload() {
      this.$refs.imgFileForm.validate((valid) => {
        if (valid) {
          if (this.butText === '新增') {
            // 找到第一个匹配对象后立即停止搜索
            const fileType = this.fileTypeList.find(key => key.btText === '新增' && key.name === this.imgFileForm.imgFileType)
            if (fileType) {
              this.illustrate('', '', fileType)
            }
          } else {
            this.$refs.upload.$refs['upload-inner'].handleClick()
          }
        }
      })
    },
    // 文件上传:文件有序上传
    submitUpload(params) {
      const file = {
        fileName: params.file.name,
        typeCode: this.imgFileForm.imgFileType
      }
      const formData = new FormData()
      formData.append('file', params.file, params.file.name.trim())
      formData.append('path', '')// 文件存储路径
      formData.append('subId', '')// 业务系统编码
      formData.append('typeCode', '')// 类型编码
      formData.append('bizCode', 'img')// 模块编码
      formData.append('bizCodeName', '图片')// 模块名称
      // formData.append('bizTblName', this.bizTblName)// 业务表名称
      formData.append('bizId', this.bizId)// 业务记录编码,业务表主键ID
      formData.append('isEsSearch', 'true')// 是否全文检索
      formData.append('fileComment', '')// 附件描述
      upload(formData).then(({ data }) => {
        if (!data.success) return this.$message.error(data.msg)
        const dataInfo = data.data
        this.ids.push(dataInfo.id)
        file.id = dataInfo.id
        file.pictureNo = dataInfo.id
        file.imgSrc = this.ipUrl + dataInfo.storeUrl || ''
        this.fileTableData.push(file)
        this.imgUrl = dataInfo.storeUrl || ''
        // this.imgUrl ? this.illustrate(file, dataInfo) : ''
        this.imgFileObj.push(file)
        this.dataInfoObj.push(dataInfo)
        // // 处理select选项禁用 PS：文件上传成功之后，由于二维码条形码只能上传一张，对二维码及条形码继续禁用
        // this.optionControl(file)

        // Promise.resolve().then(() => {
        //   // 调用表单保存方法
        //   this.imgSave()
        // })
      })
    },
    // 文件上传之前
    beforeUpload(file) {
      let flag = false
      // 类型用后缀判断
      const fileType = file.name.slice(file.name.lastIndexOf('.') + 1).toLowerCase()
      if (this.typeString.indexOf(fileType) !== -1) {
        flag = true
      }
      if (!flag) {
        this.$message.warning(
          `不支持上传当前文件格式！请上传${this.typeString}`
        )
        return false
      }
      if (file.size > this.maxFileSize * 1024 * 1024) {
        this.$message.warning(`单次最多只能上传小于${this.maxFileSize}M的文件`)
        return false
      }
      // 判断同名
      const oldFileData = this.temFileData.filter((item) => {
        return item.uid && item.uid !== ''
      })
      for (const iterator of oldFileData) {
        if (iterator.fileName === file.name) {
          this.$message.warning(
            `${file.name}已存在同文件名文件，请更改文件名后重新提交！`
          )
          return false
        }
      }
      this.fileLength += 1
    },
    // 删除附件
    handleDelete(row, index) {
      const idList = []
      // 获取当前条id
      const currentId = this.$isNotEmpty(window.luckysheet.getImageOption()) ? Object.keys(window.luckysheet.getImageOption())[index] : ''
      if (this.$isEmpty(currentId)) {
        this.temFileData.splice(index, 1)
        this.ids.splice(index, 1)
      }
      idList.push(currentId)
      const params = {}
      params.bizid = row.bizid
      // 删除图片
      this.$callApiParams('deletePicture', params, result => {
        if (result.success) {
          this.$message.success('删除成功')
          // // 调用表单保存方法
          // this.imgSave()
          // 删除图片和清空表格数据以及ids
          window.luckysheet.deleteImage({ idList })
          this.temFileData.splice(index, 1)
          this.ids.splice(index, 1)
        } else {
          this.$message.error(result.msg)
        }
        return true
      })
    },
    // 文件超出个数限制时的钩子
    onExceed() {
      this.$message.warning(
        this.isFixdelimit
          ? `最多上传${this.limit}个文件`
          : `一次最多上传${this.limit}个文件`
      )
    },
    /**
     * 添加图片
     * file 文件
     * dataInfo 平台返回数据
     * result 资金系统返回数据
     */
    illustrate(file, dataInfo, imageChangeDate, callbackAfter) {
      // 添加图片至页面
      const signatureUrl = this.ipUrl + this.imgUrl // 拼接图片url
      const precinct = window.luckysheet.getRange() || '' // 获取选区坐标
      const imgPlace = { rowIndex: precinct[0].row[0], colIndex: precinct[0].column[0] } // 默认选中0,0 如选中非0,0及取所选中单位格坐标

      // 采购备案章
      if (this.$isNotEmpty(imageChangeDate)) {
        // 如果imgString有值，则是采购备案章
        const procurementChapter = {}
        procurementChapter.id = Math.random().toString(36).substr(2)
        procurementChapter.imgString = imageChangeDate.imageString
        procurementChapter.typeCode = imageChangeDate.name
        this.fileTableData.push(procurementChapter)
        window.luckysheet.insertImage(imageChangeDate.imageString, imgPlace)
      } else {
        window.luckysheet.insertImage(signatureUrl, imgPlace)
      }
      if (typeof callbackAfter === 'function') {
        callbackAfter()
      }
    },
    imgSave() {
      // 上传 删除同意保存表单 避免视图有图片表格没数据
      const cFormRefs = this.$parent.$parent.$parent.$parent.$parent.$parent
      // 调用表单保存方法
      cFormRefs.imgSave()
    },
    // change方法
    imgTypechange(val) {
      if (val) {
        this.fileTypeList.filter((e) => {
          if (e.name === val) {
            this.butText = e.btText
          }
        })
      } else {
        this.butText = '上传图片'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
#ImgFile{
  padding: 10px 0;
  .ElUpload{
    // 覆盖到按钮无法点击
    width: 0px;
    height: 0px;
  }
}
</style>
