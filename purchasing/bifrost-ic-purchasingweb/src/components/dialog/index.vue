<template>
  <el-dialog
    ref="commonDialog"
    class="commonDialog"
    v-el-drag-dialog="value"
    :append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="false"
    :title="title"
    :visible.sync="value"
    :width="composeWidth"
    :before-close="handleClose"
    v-bind="$attrs"
    v-on="$listeners">
    <slot name="dialogContent"></slot>
    <template #footer v-if="showFooter">
      <slot name="customFooter">
        <el-button @click="handleClose">{{ cancelText }}</el-button>
        <el-button :loading="submitLoading" type="primary" @click="handleSubmit">{{ submitText }}</el-button>
      </slot>
    </template>
  </el-dialog>
</template>
<script>
const SIZE_MAP = {
  'mini': '320px',
  'small': '640px',
  'medium': '960px',
  'large': '1440px'
}
export default {
  name: 'common-dialog',
  props: {
    title: {
      default: '弹窗标题',
      type: String
    },
    size: {
      default: 'medium', // small:640 medium:960 large:1440
      type: String
    },
    showFooter: {
      default: true,
      type: Boolean
    },
    cancelText: {
      default: '取消',
      type: String
    },
    submitText: {
      default: '确定',
      type: String
    },
    value: {
      default: false,
      type: Boolean
    },
    customWidth: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      width: '960px',
      submitLoading: false
    }
  },
  computed: {
    composeWidth() {
      if (this.customWidth) {
        return this.customWidth
      }
      return SIZE_MAP[this.size] || SIZE_MAP['small']
    }
  },
  methods: {
    handleClose() {
      this.$emit('closeDlg')
      this.$emit('input', false)
    },
    handleSubmit() {
      const setLoading = (loading = true) => {
        this.submitLoading = loading
      }
      this.$emit('submit', setLoading)
    }
  }
}
</script>

<style lang="scss" scoped>
.commonDialog {
  ::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    .el-dialog__body {
      height: 100% !important;
    }
  }
}
</style>

