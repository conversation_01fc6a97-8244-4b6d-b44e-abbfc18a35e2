<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-01-09 17:44:39
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-18 22:10:57
-->
<template>
  <div class="under-construction" :style="{backgroundImage: `url(${bg})`}">
    <div class="message">
      <h2>功能建设中，敬请期待！</h2>
      <!-- <p>我们正在紧急建设这个功能，请等待开放！</p> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'UnderConstruction',
  data() {
    return {
      bg: require('@/assets/page-bg.png'),
    };
  },
};
</script>

<style scoped>
.under-construction {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  text-align: center;
}

.icon-container {
  background-color: #1f7fff;
  color: white;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 48px;
  margin-right: 30px;
}

.message h2 {
  background: linear-gradient(315deg, #42d392, #647eff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 50px;
  height: 118px;
  line-height: 118px;
  font-weight: 700;
  margin-bottom: 10px;
}

.message p {
  color: rgba(60, 60, 60, .7);
  font-size: 24px;
}

.under-construction i {
  font-size: 60px;
}
</style>
