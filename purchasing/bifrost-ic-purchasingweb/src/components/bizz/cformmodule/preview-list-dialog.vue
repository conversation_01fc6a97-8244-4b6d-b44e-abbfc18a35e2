<template>
  <el-dialog
    :title="`预览组件:${moduleName}`"
    append-to-body
    :visible.sync="dialogVisible"
    width="960px"
    :before-close="handleClose">
    <b-curd style='height: 260px' ref="curdList"/>
  </el-dialog>

</template>
<script>
export default {
  name: 'preview-list',
  data() {
    return {
      dialogVisible: false,
      moduleName: ''
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    showPreview(pageData, moduleName) {
      pageData.columns.reverse()
      this.dialogVisible = true
      this.moduleName = moduleName
      this.$nextTick(() => {
        this.$refs.curdList.initEditRowForm(pageData, false)
      })
    }
  }
}
</script>
<style>
</style>
