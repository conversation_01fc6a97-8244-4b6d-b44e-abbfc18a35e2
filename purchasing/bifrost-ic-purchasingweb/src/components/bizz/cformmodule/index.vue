<template>
  <page ref="page">
    <template #pageContent>
      <LayoutTem isDbCol :isPageShow="false" :isNotPadding="true" :isFilterShow="false">
        <template #dbColLeft >
          <classify-ztree
            ref="classifytree"
            dataType="CformModule"
            :showNodeType="true"
            deleteApiKey="deleteClassifyTreeNode"
            :deleteExtraParams="{actionKey:'DELETE'}"
            :saveExtraParams="{actionKey:'SAVE'}"
            @nodeClick="nodeClick"
            @nodeDeleted="nodeDeleted"
            @nodeUpdated="nodeUpdated"
            @leafNodeAdded="leafNodeAdded"/>
        </template>
        <template #main>
          <div class="flex-column" style="width: 100%;height: 100%;box-sizing: border-box;">
            <div style="width: 100%;border: 1px solid #ddd;padding: 18px 150px 0px 150px;">
              <el-form ref="moduleForm"
                       :model="moduleForm"
                       label-width="77px"
                       size="mini"
                       :disabled="formDisabled"
                       :rules="rules">
                <el-row :gutter="100">
                  <el-col :span="12">
                    <el-form-item label="组件编号">
                      <el-input v-model="moduleForm.code" :disabled="true"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="组件名称" prop="name">
                      <el-input v-model="moduleForm.name" :disabled="true"/>
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row :gutter="100">
                  <el-col :span="12">
                    <el-form-item label="所属组件" prop="sourceModule">
                      <el-select v-model="moduleForm.sourceModule" filterable @change="moduleChange($event)" :disabled="sourceModuledisabled">
                        <el-option
                          v-for="item in sourceModuleList"
                          :key="item.sourceModule"
                          :label="item.name"
                          :value="item.sourceModule">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="是否启用" prop="isEnabled">
                      <el-radio-group v-model="moduleForm.isEnabled">
                        <el-radio label="是">是</el-radio>
                        <el-radio label="否">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="备注">
                      <el-input type="textarea" v-model="moduleForm.remarks"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div style="margin-top: 20px;width: 100%;flex: 1;overflow: hidden;">
              <b-curd ref="curdListCols"/>
              <preview-list ref="previewList"></preview-list>
            </div>
            <div style="padding-top: 20px;text-align: right">
              <el-button class="btn-normal" @click="oneClickUpdate()" :disabled="!sourceModuledisabled"> 一键更新</el-button>
              <el-button class="btn-normal" @click="preview()" :disabled="!sourceModuledisabled"> 预览</el-button>
              <el-button class="btn-normal" @click="refresh()" :disabled="!sourceModuledisabled"> 刷新</el-button>
              <el-button class="btn-normal" @click="resetting()"> 重置</el-button>
              <el-button
                class="btn-normal"
                type="primary"
                :loading="moduledialogloading"
                :disabled="formDisabled"
                @click="handleSumbit('moduleForm')"
              >
                保存
              </el-button>
            </div>
          </div>
        </template>
      </LayoutTem>
    </template>
  </page>
</template>
<script>
export default {
  name: 'cformmodule',
  data() {
    return {
      columnsOld: [],
      moduleForm: {
        isEnabled: '是'
      },
      formDisabled: true, // 表单禁用
      sourceModuledisabled: false,
      selectNodeId: '',
      selectLabel: '',
      currentId: '',
      sourceModuleList: [], // 源组件集合
      colTypes: [], // 数据类型列表
      modifyTypes: [],
      patentNames: [],
      existParentLevel: [],
      dataRefs: [], // 弹框参照类别数据
      dataRefDrop: [], // 下拉参照类别数据
      rules: {
        name: [
          { required: true, message: '请输入组件名称', trigger: 'blur' }
        ],
        sourceModule: [
          { required: true, message: '请选择所属组件', trigger: 'change' }
        ],
        isEnabled: [
          { required: true, message: '请选择是否启用', trigger: 'change' }
        ]
      },
      moduledialogloading: false,
      moduleFormOld: []
    }
  },
  mounted() {
    this.selectSourceModule() // 查询源组件
    this.selectColBaseData() // 查找数据列基础数据信息
    this.$nextTick(() => {
      /* this.$callApiParams('selectModule',
        { 'TREE_NODE_ID_eq': this.selectNodeId },
        result => {
          this.initModule(result)
          return true
        }, result => {

        })*/
      this.selectModule(this.selectNodeId)
    })
  },
  methods: {
    selectModuleList(selectNodeId) {
      return new Promise((resolve, reject) => {
        this.$callApiParams('selectModuleList', { selectNodeId: selectNodeId }, (result) => {
          if (this.$isNotEmpty(result.data)) {
            const pageDataPreview = result.data[0].pageData || {}
            resolve(pageDataPreview)
          } else {
            resolve({})
          }
          return true
        }, (result) => {
          reject({})
          return true
        })
      })
    },
    selectSourceModule() {
      this.$callApi('selectSourceModule'
        , {}, result => {
          if (result.success) {
            this.sourceModuleList = result.data
          }
          return true
        })
    },
    moduleChange(value) {
      // const colData = value.columns
      const colData = this.sourceModuleList.filter(item => {
        return value === item.sourceModule
      })
      const deepColData = this.$clone(colData)
      this.moduleForm.dataType = deepColData[0].dataType
      this.$call(this, 'baseList', 'loadTableData', deepColData[0].columns)
    },
    nodeClick(data, exData) {
      if (data.isLeaf) {
        this.formDisabled = false
      }
      exData = exData || {}
      this.selectNodeId = data.id
      this.selectLabel = data.label
      this.selectModule(data.id)
    },
    selectModule(dataId, isReset) {
      this.$callApiParams('selectModule',
        { 'TREE_NODE_ID_eq': dataId },
        result => {
          this.moduleFormOld = result.data
          this.columnsOld = result.data.columns
          this.patentNames = result.attributes.parentNames
          this.existParentLevel = result.attributes.existParentLevel
          this.initModule(result, isReset)
          if (isReset) {
            this.$refs.moduleForm.resetFields()
          }
          return true
        }, result => {

        })
    },
    nodeDeleted(node) {
      if (node.isLeaf && node.itemKey === this.currentId) {
        this.formDisabled = true
        this.moduleForm = { isEnabled: '是' }
        this.selectModule('')
        this.$refs.moduleForm.resetFields()
      }
    },
    nodeUpdated(node) {
      if (node.isLeaf && node.itemKey === this.currentId) {
        this.moduleForm.name = node.label
        this.handleSumbit('moduleForm')
      }
    },
    /**
     * @param isReset 是否重置操作
     */
    initModule(baseObj, isReset) {
      const param = {}
      param.customSelectOptionsSupplier = (scope, colItem) => {
        if (colItem.label === '修改设置') {
          return this.modifyTypes
        }
        if (colItem.label === '数据类型') {
          return this.colTypes
        }
        if (colItem.label === '参照数据') {
          return this.dataRefs
        }
        if (colItem.label === '父级名称') {
          const canSelectParentNames = this.patentNames.filter(item => item.value !== scope.row.labelAlias)
          return canSelectParentNames
        }
      }
      param.rowCheckedCallback = (rows) => {
        this.rowCheckedCallback(rows)
      }
      param.rowCellChanged = (scope, colItem, baseList) => {
        // 如果当前col已经设置了父级名称, 再次点击设置时先把前面的值去除
        const filterExistParentLevel = this.existParentLevel.filter(item => {
          return item.labelAlias !== scope.row.labelAlias
        })
        filterExistParentLevel.push({"labelAlias": scope.row.labelAlias, "parentName": scope.row.parentName})
        this.existParentLevel = filterExistParentLevel
        const params = {
          parentLevelData: this.existParentLevel,
        }
        this.$callApiParams('validateModuleParentName', params,
          () => {
            return true
          },
          () => {
            scope.row.parentName = ''
          })
      }
      param.hiddenButtons = ['新增', '删除', '复制']
      param.dbColLeftPadding = true // 左侧布局中需要padding
      // 是否展示和是否必填2个列的值设置为否
      const setValue = (scope) => {
        let row = scope
        if (scope.hasOwnProperty('row')) {
          row = scope.row
          let message = ''
          if (scope.column.property !== 'isEnabled' && row?.isEnabled === '否') {
            if (scope.column.property === 'isRequiredStr') {
              message = '请先将是否启用、是否展示设置为是'
            } else {
              message = '请先将是否启用设置为是'
            }
            this.$message.error(message)
          } else if (scope.column.property === 'isRequiredStr' && row?.isShow === '否') {
            message = '请先将是否展示设置为是'
            this.$message.error(message)
          }
        }
        if (row?.isEnabled === '否') {
          row.isShow = '否'
          row.isRequired = false
          row.isRequiredStr = '否'
        } else if (row?.isShow === '否') {
          row.isRequired = false
          row.isRequiredStr = '否'
        }
      }
      param.rowRadioCallback = (scope, colItem, columnsData, trueMap, rowsData) => {
        setValue(scope)
      }
      // 填充前把启用为否的行 是否展示和是否必填2个列的值设置为否
      param.reloadTableCallbackBeforeFillTable = result => {
        result?.data?.rows?.forEach(row => {
          setValue(row)
        })
      }
      this.$refs.moduleForm.resetFields()
      if (this.$isEmpty(baseObj.data.sourceModule)) {
        this.sourceModuledisabled = false
      } else {
        if (isReset) {
          this.sourceModuledisabled = false
        } else {
          this.sourceModuledisabled = true
        }
      }
      if (this.$isNotEmpty(baseObj.data.id)) {
        if (isReset) {
          baseObj.data.sourceModule = ''
          baseObj.data.pageData.rows = []
        }
        this.moduleForm = baseObj.data
      } else {
        this.moduleForm = { isEnabled: '是', name: this.selectLabel }
      }
      this.$refs.curdListCols.initEditRowForm(
        baseObj.data.pageData, this.isEdit, null, param)
    },
    leafNodeAdded(nodeId, itemKey) { // 新增叶子节点后，将其设置为当前节点
      this.setCurrentItem(nodeId, itemKey)
    },
    setCurrentItem(nodeId, itemKey) { // 设置当前对象
      this.currentId = (itemKey == null) ? '' : itemKey
      this.$refs.classifytree.setCurrentItem(nodeId, itemKey)
    },
    // 查找数据列基础数据信息
    selectColBaseData() {
      this.$callApiParams('selectColBaseData', {},
        result => {
          this.colTypes = result.data.colTypes
          this.modifyTypes = result.data.modifyTypes
          this.dataRefs = result.data.dataRef
          this.dataRefDrop = result.data.dataRefDrop
          return true
        })
    },
    handleSumbit(formName) {
      this.moduledialogloading = true
      var columnDetail = this.handleColumns(this.$refs.curdListCols.getBlistRowData())
      this.moduleForm.columns = columnDetail
      this.moduleForm.treeNodeId = this.selectNodeId
      this.moduleForm.pageData = null
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$callApi('saveModule', this.moduleForm, result => {
            if (result.success) {
              this.$message.success('保存成功!')
              this.selectModule(this.selectNodeId)
              this.moduledialogloading = false
            }
            return true
          }, resolve => {
            this.moduledialogloading = false
          })
        } else {
          this.moduledialogloading = false
          return false
        }
      })
    },
    handleColumns(bListRowData) {
      // 排序
      if (this.$isNotEmpty(bListRowData)) {
        bListRowData.forEach((row, index) => {
          row.order = index + 1
        })
      }
      return bListRowData
    },
    resetting() {
      // this.moduleForm = { isEnabled: '是' }
      // this.selectModule('')
      this.selectModule(this.selectNodeId, true)
      // this.$refs.moduleForm.resetFields()
    },
    oneClickUpdate() {
      this.$confirm('此操作将更新所有关联该区块的表单组件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.moduleFormOld.pageData = null
        this.$callApi('oneClickUpdate', this.moduleFormOld,
          result => {
          })
      }).catch(() => {

      })
    },
    async preview() {
      const pageDataPreview = await this.selectModuleList(this.selectNodeId)
      const moduleName = this.moduleForm.name
      this.$refs.previewList.showPreview(pageDataPreview, moduleName, '', '')
    },
    refresh() {
      const sourceModule = this.moduleForm.sourceModule
      this.$callApi('refreshColumns',
        { 'sourceModule': sourceModule,
          'columnsOld': this.columnsOld },
        result => {
          this.selectModule(this.selectNodeId, false)
          return true
        })
    },
    rowCheckedCallback(rows) {
      if (this.$isNotEmpty(rows) && this.$isNotEmpty(rows[0]) && this.$isEmpty(rows[0].id)) {
        this.$call(this, 'baseList', 'disableAllBts')
      }
    }
  }
}
</script>
