<template>
  <div class="basefile" id="baseFile">
    <b-curd ref="curdList"></b-curd>
    <filedialog ref="filedialog" :dialog.sync="isfiledialog" @reload="reload" @initFileList="initFileList"></filedialog>
    <file-view ref="fileView"></file-view>
  </div>
</template>

<script>
import FileView from '../../../components/fileview/file-view.vue'
import { batchDownload, downloadFile } from '@/api/file/file'

export default {
  name: 'basefile', // 附件
  components: { FileView },
  inject: {
    missingRecordFileValidate: { default: false },
    deleteData: { default: false }
  },
  data() {
    return {
      dataVo: {}, // 表单数据
      commonBizAttaVo: {},
      isfiledialog: false,
      fileList: [], // 上传的文件集合
      tableTempData: [], // 列表临时数据
      bizTblName: 'ATTACHMENT',
      bizDataId: '',
      btAddText: '上传',
      cusTomUploadText: '',
      btAddSecondText: '',
      btAddThirdText: '',  //btAddSecondText btAddThirdText 自定义标签按钮名
      delIds: [], // 上传前需要删除的附件id集合
      attList: [], // 已上传的附件集合
      fileIds: [], // 临时文件的平台附件信息id集合
      checkedRows: [], // 当前选中的rows
      isattsettings: true, // 是否设置了文件类型
      isNoAddDeleteBts: false,
      isNoDeleteBts:false, //显示不显示删除按钮
      isbrowse: true, // 操作日志查看附件时禁用新增,删除按钮
      source: '', // 附件来源
      batchDownloadName: '批量下载', // 批量下载文件名称
      currentUser: {},
      attTempId: '', // 附件临时id
      initRefDataVoFromId: '', // 主数据id
      isMultipleTabsTab: '', // 是否分tab
      rowsData: [], // 表格数据
      fkGuidArr: [], // 附件关联主键数组
      relationAttCformIdArr: [], // 已关联的附件表单id数组
      relationFormIdStr: '',
      isRefreshAttach: false, // 是否刷新附件
      attTypeTableName: 'ELE_BX_ATT_TYPE', // 附件类型表名 默认报销附件类型
      noNeedBooksetFilter:'',  //用于过滤套账
      isCform: true, // 是否是表单附件
      isBlueTable: false,
      deleteBulufujianValidateResult: true,
      addRowCallback: undefined, // 文件列表增加后的回调
      deleteRowCallback: undefined, // 文件列表删除后的回调
      appendixType:"",
      isHideUpload: false,
      buttons:[]   //上传新增的按钮
    }
  },
  mounted() {
    this.initData()// 初始化数据
  },
  created() {
    var this_ = this
    this_.$onEvent(this_, {
      getDataApiParams(params) { // 自定义查询参数
        if ('selectAttachmentPage'.indexOf(params.dataApiKey) > -1) {
          params.size = 10000
        }
      }
    })
  },
  methods: {
    refreshAttach(formIdArr) {
      formIdArr = formIdArr.filter(formId => {
        if (this.relationAttCformIdArr.indexOf(formId) === -1) {
          return formId
        }
      })
      this.relationFormIdStr = formIdArr.join(',')
      this.isRefreshAttach = true
      this.init()
    },
    initData() {
      this.tableTempData = []
      this.delIds = []
      this.fkGuidArr = []
      this.relationAttCformIdArr = []
      this.relationFormIdStr = ''
      this.isRefreshAttach = false
    },
    initCommonBiz(isEdit, attaVo, exParams) { // 普通业务附件初始化
      exParams = exParams || {}
      this.isNoAddDeleteBts = false
      if (isEdit !== undefined) {
        this.isNoAddDeleteBts = !isEdit
      }

      this.addRowCallback = exParams.addRowCallback
      this.deleteRowCallback = exParams.deleteRowCallback
      this.commonBizAttaVo = attaVo || {}
      this.commonBizAttaVo.attList = this.commonBizAttaVo.attList || []
      this.commonBizAttaVo.fkGuid = this.$isEmpty(this.commonBizAttaVo.fkGuid)
        ? new Date().getTime() : this.commonBizAttaVo.fkGuid

      var dataVo = this.commonBizAttaVo
      this.isCform = true
      if (this.commonBizAttaVo.data === undefined) {
        this.isCform = false
        dataVo = {
          data: { id: '', formType: '' },
          attList: this.commonBizAttaVo.attList,
          extData: {
            currentUser: '',
            attTempId: this.commonBizAttaVo.fkGuid,
            initRefDataVoFromId: undefined,
            batchDownloadName: '',
            attTypeTableName: '',
            bizDataId: '',
            '制单样式': '分tab'
          }
        }
      }

      this.btAddText = exParams.btAddText
      if (this.$isEmpty(this.btAddText)) {
        this.btAddText = '上传附件'
      }
      this.initMeta(dataVo)
    },
    //2024/1/19增加一个上传默认显示第几个所属类别，showIndex是下拉框的索引
    initMeta(dataVo,buttons,showIndex) {
      this.dataVo = dataVo
      this.buttons = buttons
      if (this.isCform) {
        if (this.$isNotEmpty(dataVo.extData.skipSource)) {
          this.source = dataVo.extData.skipSource
        } else {
          this.source = dataVo.meta ? dataVo.meta.formType : ''
        }
        this.initRefDataVoFromId = dataVo.extData ? dataVo.extData.initRefDataVoFromId : ''
        if (dataVo.extData[`不带出主数据附件`] === '是') {
          this.initRefDataVoFromId = ''
        }
        this.bizDataId = dataVo.data ? dataVo.data.id : ''
        this.isMultipleTabsTab = (dataVo.extData[`制单样式`] === '分tab')
      }
      this.attTypeTableName = dataVo.extData.attTypeTableName ? dataVo.extData.attTypeTableName : this.attTypeTableName
      this.appendixType = dataVo.extData.appendixType ? dataVo.extData.appendixType : this.appendixType
      this.attList = dataVo.attList ? dataVo.attList.filter(item => item.attType === '0') : []
      this.currentUser = dataVo.extData ? dataVo.extData.currentUser : {}
      this.attTempId = dataVo.extData ? dataVo.extData.attTempId : ''
      this.batchDownloadName = dataVo.extData ? dataVo.extData.batchDownloadName : '批量下载'
      this.noNeedBooksetFilter = dataVo.extData.noNeedBooksetFilter ? dataVo.extData.noNeedBooksetFilter : ''
      this.fileIds = []
      this.attList.forEach(item => {
        this.fileIds.push(item.attId)
        this.relationAttCformIdArr.push(item.relationAttCformId)
      })
      showIndex = showIndex ? showIndex : dataVo?.extData?.showIndex
      let appendixType = dataVo?.extData?.appendixType ? dataVo?.extData?.appendixType : null
      this.$nextTick(() => {
        // 初始化附件类型数据
        this.$refs.filedialog.attTypeTableName = this.attTypeTableName
        if (this.source) {
          this.$refs.filedialog.source = this.source
        }
        this.$refs.filedialog.selectAttTypeList(null,showIndex, appendixType)
      })

      if (this.$parent.attProj) {
        this.attProj = this.$parent.attProj
      }

      this.initData()
      this.init(buttons)
    },
    init(buttons) {
      this.fkGuidArr = []
      this.fkGuidArr.push(this.bizDataId)
      this.fkGuidArr.push(this.attTempId)
      this.fkGuidArr.push(this.initRefDataVoFromId)
      let uploadAction = this.cusTomUploadText ? { click: row => (this.fileUploadDialog(row)), icon: "el-icon-upload2" } : { upload: this.fileUpload, accept: '.doc,.docx,.docm,.xls,.xlsx,.ppt,.pptx,.pdf,.rar,.zip', icon: "el-icon-upload2" };
      this.$refs.curdList.clearRow()
      let initParams ={
        selectionColumnWidth: 55,
        params: {
          dataApiKey: 'selectAttachmentPage',
          FK_GUID_in: this.fkGuidArr.join(','),
          BIZID_notin: this.delIds.join(','),
          ATT_TYPE_eq: '0',
          tparentCode: '10011',
          ascs: 'APPENDIX_TYPE_CODE, ID, NUM',
          source: this.source,
          attProj: this.attProj,
          tableName: this.attTypeTableName,
          isMultipleTabsTab: this.isMultipleTabsTab,
          attTempId: this.attTempId,
          isRefreshAttach: this.isRefreshAttach,
          relationFormIdStr: this.relationFormIdStr, // 关联单据id字符串
          APPENDIX_TYPE_in: this.appendixType
        },
        showPager: false,
        tableExStyleClass: this.isBlueTable ? 'blue-table' : '',
        buttons: this.isattsettings
          ? [
            { text: '下载', icon: 'el-icon-download', enabledType: '1+', click: this.fileDownload },
            {
              text: this.btAddThirdText ? this.btAddThirdText :'预览文件', icon: 'el-icon-right', enabledType: '1+', click: this.fileView
            }
          ]
          : this.isHideUpload ? [
            { text: this.btAddSecondText ? this.btAddSecondText : '下载', icon: 'el-icon-download', enabledType: '1+', click: this.fileDownload },
            {
              text: this.btAddThirdText ? this.btAddThirdText : '预览文件', icon: 'el-icon-right', enabledType: '1+', click: this.fileView
            }
          ] : [
            // {
            //   text: !this.cusTomUploadText ? this.btAddText : this.cusTomUploadText, icon: 'el-icon-upload2', enabledType: this.isHideUpload ? '1' : '0',
            //   upload: this.fileUpload, accept: '.doc,.docx,.docm,.xls,.xlsx,.ppt,.pptx,.pdf,.rar,.zip'
            // },
            { text: !this.cusTomUploadText ? this.btAddText : this.cusTomUploadText, icon: 'el-icon-upload2', enabledType: this.isHideUpload ? '1' : '0', ...uploadAction },
            { text: this.btAddSecondText ? this.btAddSecondText : '下载', icon: 'el-icon-download', enabledType: '1+', click: this.fileDownload },
            {
              text: this.btAddThirdText ? this.btAddThirdText : '预览文件', icon: 'el-icon-right', enabledType: '1+', click: this.fileView
            }
          ],
        btAddClick: this.isattsettings
          ? { text: this.btAddText, icon: 'el-icon-upload2', click: row => (this.fileUploadDialog(row)) }
          : {},
        btAfters: { '删除': this.btAddText }, // 表明“删除”按钮放在“上传”后面
        hideCurdButton: this.isattsettings && this.isbrowse ? ['修改', '详情'] : ['新增', '修改', '详情','上传'],
        tableTempData: this.tableTempData,
        btDeleteClick: { click: bt => (this.btDeleteClick(bt)) },
        isbrowse: this.isbrowse,
        reloadTableCallback: result => {
          var dataMap = {}
          result.data.rows.forEach(row => {
            dataMap[row.id] = row
          })
          this.rowsData = result.data.rows
          this.attList = this.rowsData
          this.attList.forEach(item => {
            this.fileIds.push(item.attId)
          })
          this.$nextTick(() => {
            this.setCheckboxVisible(dataMap)
            if(this.isNoDeleteBts){
              var hiddenBts = this.isNoAddDeleteBts ? ['删除'] : []
            }else{
              var hiddenBts = this.isNoAddDeleteBts ? [this.btAddText, '删除'] : []
            }
            this.$refs.curdList?.reRenderBtns(hiddenBts)
          })
          if (this.$refs.fileView) {
            this.$refs.fileView.fileDownloadByFileIdExtData = result.attributes
          }

          if (this.addRowCallback) {
            this.addRowCallback(this)
          }
        },
        callbackSuccessDelete: (result, ids) => {
          this.callbackSuccessDelete(result, ids)
        },
        callbackTableTempDataDelete: (result, ids) => {
          this.callbackTableTempDataDelete(result, ids)
        },
        rowCheckedCallback: (rows) => {
          this.rowCheckedCallback(rows)
        }
      }
      if(buttons){
        initParams.buttons=initParams.buttons.concat(buttons)
        let btnName = this.btAddText? this.btAddText:'上传'
        initParams.btAfters = {[btnName]:buttons[0].text,'删除': this.btAddText,'下载':'删除','预览文件':'下载'}
      
      }
      //过滤账套
      if(this.noNeedBooksetFilter){
        initParams.params.noNeedBooksetFilter=this.noNeedBooksetFilter
      }
      this.$refs.curdList.init(initParams)
    },
    setCheckboxVisible(dataMap) { // 设置附件是否可勾选
      if (this.$isNotEmpty(dataMap)) {
        var this_ = this
        const rows = this.$refs.curdList?.getBlistRowData()
        // var $rows = $('#baseFile').find('tr[rowid]')
        $.each(rows, function(i, row) {
          var rowId = this_.$getRowId(row)
          var rowData = dataMap[rowId]
          if (this_.$isNotEmpty(rowData)) {
            if (this_.source === rowData.source && rowData.appendixTypeCode === '999') {
              if (rowData.id === '审核依据') {
                rowData.source = ''
                this_.$refs.curdList.changeRowTar('cannotChecks', row)
              }
            }
            if (this_.dataVo.extData.hasOwnProperty('签署备案') && rowData.source !== '签署备案') {
              this_.$refs.curdList.changeRowTar('cannotChecks', rowData)
            }
          }
        })
      }
    },
    btDeleteClick(bt) {
      const rowIds = bt.getRowIds()
      if (this.missingRecordFileValidate) {
        // 缺票删除逻辑
        const ids = rowIds.join(',')
        this.$callApi('blFileDeleteValide&fileIds=' + ids, {}, result => {
          this.deleteFunc(rowIds)
          return true
        })
      } else {
        this.deleteFunc(rowIds)
      }
    },
    deleteFunc(rowIds) {

      if (this.deleteData) {
        const message = `确认要删除 ${rowIds.length} 条数据吗？`
        this.$callApiParamsConfirm(message,
          null, 'deleteAttachment', { 'ids': rowIds.join(',') },
          result => {
            this.init()
          })
      } else {
        for (var i = this.rowsData.length - 1; i >= 0; i--) {
          if (rowIds.indexOf(this.rowsData[i].id) > -1) {
            this.rowsData.splice(i, 1)
          }
        }

        if (this.deleteRowCallback) {
          this.$nextTick(() => this.deleteRowCallback(this))
        }

        this.delIds = this.delIds.concat(this.delIds, rowIds)
        this.fileIds = this.fileIds.filter(item => {
          if (this.delIds.indexOf(item) === -1) {
            return item
          }
        })
        this.attList = this.rowsData
      }
      this.setCommonBizAttaVoAttList()
    },
    rowCheckedCallback(rows) {
      this.fileIds = []
      this.checkedRows = rows

      if (this.$isNotEmpty(rows)) {
        var this_ = this
        rows.forEach(item => {
          this.fileIds.push(item.attId)
          if (item.appendixTypeCode === '999') {
            this_.$refs.curdList.setBtProperty('删除', 'disabled', true)
          }
          if (this.missingRecordFileValidate) {
            if (this.missingRecordFileIds) {
              this.missingRecordFileIds().forEach(id => {
                if (item.id === id) {
                  this_.$refs.curdList.setBtProperty('删除', 'disabled', true)
                }
              })
            }
          }
        })
      } else {
        this.attList.forEach(item => {
          this.fileIds.push(item.attId)
        })
        this.tableTempData.forEach(item => {
          this.fileIds.push(item.id)
        })
      }
    },
    callbackTableTempDataDelete(result, ids) {
      if (this.$isNotEmpty(ids)) {
        this.delIds = this.delIds.concat(this.delIds, ids)
        this.fileIds = this.fileIds.filter(item => {
          if (this.delIds.indexOf(item) === -1) {
            return item
          }
        })
      }
    },
    callbackSuccessDelete(result, ids) {
      if (result.success) {
        this.delIds = this.delIds.concat(this.delIds, ids)
        this.fileIds = this.fileIds.filter(item => {
          if (this.delIds.indexOf(item) === -1) {
            return item
          }
        })
        this.attList = this.attList.filter(item => {
          if (this.delIds.indexOf(item.id) === -1) {
            return item
          }
        })
      }
    },
    fileView() {
      if (this.$isNotEmpty(this.fileIds)) {
        if (this.fileIds.length === 1 && this.$isNotEmpty(this.checkedRows)) {
          this.$refs.curdList.handlePreView(this.checkedRows[0])
        } else {
          this.$refs.fileView.open({ fileIds: this.fileIds, bizDataId: null })
        }
      }
    },
    reload() {
      this.isfiledialog = false
      this.init(this.buttons)
    },
    updateBeforeSave() {
      return {
        attList: this.attList
      }
    },
    initFileList(attList) { // 上传成功后回调
      this.isfiledialog = false
      // 平台附件信息集合
      this.attList = this.attList.concat(attList)
      attList.forEach(item => {
        this.fileIds.push(item.attId)
      })
      this.setCommonBizAttaVoAttList()
      this.reload()
    },
    setCommonBizAttaVoAttList() {
      if (this.commonBizAttaVo) {
        this.commonBizAttaVo.attList = this.attList

        const fIds = []
        if (this.$isNotEmpty(this.attList)) {
          this.attList.forEach(item => {
            fIds.push(item.id)
          })
        }
        this.commonBizAttaVo.fileIdsStr = fIds.join(',')
      }
    },
    fileUploadDialog(checkedRows) {
      this.$refs.filedialog.bizTblName = this.bizTblName
      this.$refs.filedialog.bizDataId = this.attTempId
      this.$refs.filedialog.attList = this.attList
      this.$refs.filedialog.tableTempData = this.tableTempData
      this.isfiledialog = true
    },
    fileUpload(result, file, fileList, uploadRef) {
      // 清理缓存文件
      uploadRef.forEach(upload => {
        upload.clearFiles()
      })
      // 获取上传文件大小
      const isGt100M = Number(file.size / 1024 / 1024) > 100
      if (isGt100M) {
        this.$msgbox({
          title: '',
          message: '文件大小不能超过100MB，请压缩后重新上传！',
          type: 'warning'
        })
        return
      }
      if (fileList) {
        this.fileList = this.fileList.concat(fileList)
        var params = {}
        params.typeCode = '' // 类型编码
        params.bizCode = '' // 模块编码
        params.bizCodeName = '' // 模块名称
        params.bizTblName = this.bizTblName // 业务表名称
        params.bizId = this.attTempId // 业务记录编码,业务表主键ID
        params.attType = '0' // 附件类别
        params.appendixType = '普通附件' // 附件类型
        params.appendixTypeCode = '' // 附件类型编码
        params.source = '本地上传' // 来源
        params.methodName = 'initFileList'
        this.$uploadAttachment(this, params) // 上传附件
      }
    },
    isHasFile(fileName) {
      const tempData = this.tableTempData.filter(item => item.attName === fileName)
      const attData = this.attList.filter(item => item.attName === fileName)
      if (!this.$isEmpty(tempData) || !this.$isEmpty(attData)) {
        return false
      }
      return true
    },
    fileDownload(obj) {
      var checkedRows = obj.params.rows
      // 批量下载
      const attachIdArr = Array.from(checkedRows, ({ attId }) => attId)
      const attachIds = attachIdArr.join(',')
      if (checkedRows.length === 1) {
        downloadFile(attachIds).then((res) => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件信息不存在')
          }
        })
      } else {
        batchDownload({ attachIds: attachIds, fileName: this.batchDownloadName }).then(res => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件不存在！')
          }
        }).catch(err => {
          console.log(err)
          this.$message({
            type: 'error',
            message: '文件不存在！'
          })
        })
      }
    },
    handleFileDownloadRes(res, str1) {
      if (!res.data) {
        this.$message.error('文件信息不存在')
        return
      }
      var filename = str1 || undefined
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // 检测是否在IE浏览器打开
        window.navigator.msSaveOrOpenBlob(new Blob([res.data]), filename)
      } else {
        // 谷歌、火狐浏览器
        let url = ''
        if (
          window.navigator.userAgent.indexOf('Chrome') >= 1 ||
          window.navigator.userAgent.indexOf('Safari') >= 1
        ) {
          url = window.webkitURL.createObjectURL(new Blob([res.data]))
        } else {
          url = window.URL.createObjectURL(new Blob([res.data]))
        }
        const link = document.createElement('a')
        const iconv = require('iconv-lite')
        iconv.skipDecodeWarning = true // 忽略警告
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', filename)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      }
    },
    getTable() {
      return this.$refs.curdList.getTable()
    }
  }

}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__content {
  height: 100%;
}
</style>
<style lang="scss">
#baseFile {
  .single-main {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
