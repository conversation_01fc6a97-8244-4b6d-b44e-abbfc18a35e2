<template>
    <el-dialog
        append-to-body
        :title="'审核依据'"
        :visible.sync="isAttachAuditDialog"
        width="800px"
        :close-on-click-modal='false'
        @close="handleClose">
      <div id="attachAuditDialog">
        <b-curd ref="curdList"></b-curd>
      </div>
      <!-- <template #footer>
        <el-button @click="handleClose()"> 取消</el-button>
      </template> -->
    </el-dialog>
</template>
<script>

export default {
  name: 'attach-audit-dialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isAttachAuditDialog = bool
      if (this.isAttachAuditDialog) {
        this.$nextTick(() => {
          this.init(this.billId)
        })
      }
    }
  },
  data() {
    return {
      billId: '',
      isAudit: '1',
      isAttachAuditDialog: this.dialog
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.isAttachAuditDialog) {
        this.init(this.billId)
      }
    })
  },
  methods: {
    init(billId) {
      this.$refs.curdList.init({
        params: {
          dataApiKey: 'selectAttachForAuditPageData',
          deleteApiKey: 'deleteAttachment',
          FK_GUID_eq: billId,
          isAudit: 1,
          APPENDIX_TYPE_eq: '审核依据'
        },
        showPager: false,
        hideCurdButton: ['新增', '修改', '详情'],
        callbackSuccessDelete: (result, ids) => {
          this.callbackSuccessDelete(result, ids)
        }
      })
    },
    callbackSuccessDelete(result, ids) {
      if (result.success) {
        this.$parent.$parent.$parent.$parent.reload()
      }
    },
    handleClose() {
      this.isAttachAuditDialog = false
      this.$emit('update:dialog', false)
      this.$parent.$parent.$parent.$parent.reload()
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep .buttons-normal{padding: 0px !important;}
  #attachAuditDialog{height: 500px;}
</style>
