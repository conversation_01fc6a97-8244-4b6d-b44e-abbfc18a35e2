<template>
  <div class="base-attachment">
    <basefile ref="baseFile"></basefile>
  </div>
</template>

<script>
export default {
  name: 'base-attachment', // 附件
  props: {
    attPro: { type: String, default: '' },
    isBlueTable: { type: Boolean, default: false }
  },
  data() {
    return {
      attProj: this.attPro
    }
  },
  mounted() {
    if (this.$refs.baseFile) {
      this.$refs.baseFile.isBlueTable = this.isBlueTable
    }
  },
  methods: {
    initCommonBiz(isEdit, attaVo, exParams) {
      this.$refs.baseFile.initCommonBiz(isEdit, attaVo, exParams)
    },
    setButtonNormalNoPaddingTop(isNoPaddingTop) {
      this.$refs.baseFile.$refs.curdList.setButtonNormalNoPaddingTop(isNoPaddingTop)
    },
    setButtonBarVisible(visible, isNoPaddingTop) {
      this.$refs.baseFile.$refs.curdList.setButtonBarVisible(visible, isNoPaddingTop)
    },
    getTable() {
      return this.$children[0].getTable()
    },
  }
}

</script>

<style lang="scss" scoped>
.base-attachment{height: 100%;}
.base-attachment .basefile{height: 100%;}
.base-attachment ::v-deep .el-table{height: 100%;}
</style>
