<template>
  <div id="filedialog">
    <el-dialog
      append-to-body
      :title="titleText"
      :visible.sync="isfiledialog"
      width="640px"
      :close-on-click-modal='false'
      @close="handleClose">
      <el-form
        ref="fileForm"
        :model="filedForm"
        label-width="100px">
        <el-row>
          <el-form-item label="所属类别" required>
            <el-select v-model="filedForm.recAcctType">
              <el-option
                v-for="item in recAcctTypeOptions"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row style="padding: 10px 0px 0px 0px;">
          <el-form-item label="文件名称" required style="margin-bottom: 0px;">
            <el-upload
              ref="upload"
              :file-list="fileList"
              :on-change="handleChange"
              :action="uploadUrl"
              :show-file-list="true"
              :on-success="onSuccess"
              :on-error="onError"
              :auto-upload="false"
              :multiple="true"
              :on-exceed="masterFileMax"
              :on-remove="handleRemove"
              :accept="acceptType">
              <el-button slot="trigger" size="mini">选择文件</el-button>
              <div slot="tip" class="el-upload__tip">
                支持上传的附件格式：{{acceptType}}，且不超过100MB
              </div>
            </el-upload>
          </el-form-item>
        </el-row>
      </el-form>
      <template #footer>
        <el-button class="btn-normal" type="primary" @click="handlefiledSumbit('fileForm')" :loading="disabledUpload">确定</el-button>
        <el-button class="btn-normal" @click="handleClose('fileForm')"> 取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'filedialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    titleText: {
      type: String,
      default: '上传附件'
    }
  },
  inject: {
    missingRecordFileValidate: { default: false },
  },
  watch: {
    dialog(bool) {
      this.isfiledialog = bool
    }
  },
  data() {
    return {
      isfiledialog: this.dialog,
      isfiledialogUpload: false,
      acceptType:".doc,.docx,.docm,.xls,.xlsx,.ppt,.pptx,.pdf,.rar,.zip,.png,.jpg,.jpeg,.ofd",
      filedForm: {
        bizId: '',
        recAcctType: '',
        fileName: '',
        fileComment: ''
      },
      recAcctTypeOptions: [],
      fileList: [],
      uploadUrl: '',
      bizTblName: '', // 业务表名称
      bizDataId: '', // 业务记录编码,业务表主键ID
      source: '本地上传',
      tableTempData: [],
      attList: [],
      attTypeTableName: 'ELE_BX_ATT_TYPE', // 附件类型表名 默认报销附件类型
      limit: 3,
      disabledUpload: false,
      missingRecordFileValidateResult: false
    }
  },
  methods: {
    selectAttTypeList(selectRecAcctTypeOptions,showIndex, appendixType=null) {
      if (selectRecAcctTypeOptions) {
        var recAcctTypeOptionArr = selectRecAcctTypeOptions.split(',')
        if (recAcctTypeOptionArr) {
          this.recAcctTypeOptions = []
          recAcctTypeOptionArr.forEach(recAcctTypeOption => {
            var ele = recAcctTypeOption.split(':')
            var eleName = ele[0]
            if (ele && ele.length > 1) {
              eleName = ele[1]
            }
            this.recAcctTypeOptions.push({ eleCode: ele[0], eleName: eleName })
          })
          this.filedForm.recAcctType = this.recAcctTypeOptions[0].eleCode
        }
      } else {
        console.log(appendixType,'appendixType')
        this.$callApi('getAccSetEle&tableName=' + this.attTypeTableName, {}, result => {
          if (result.success) {
            this.recAcctTypeOptions = result.data
            if(appendixType) {
              this.filedForm.recAcctType = this.recAcctTypeOptions.find(item => item.eleName === appendixType)?.eleCode
            } else {
              this.filedForm.recAcctType = this.recAcctTypeOptions[showIndex?showIndex:0]?.eleCode
            }
          }
          return true
        },() => {} ,{ isSave:false })
      }
    },
    masterFileMax(files, fileList) {
      this.$message.error(`请最多上传 ${this.limit} 个文件！`)
    },
    clearUpload() { //  清理上传的文件
      // 清理缓存文件
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    handleClose() {
      this.isfiledialog = false
      this.disabledUpload = false
      this.clearUpload()
      this.$emit('update:dialog', false)
      this.$emit('reload')
    },
    onSuccess(res) {
      this.$alert(res.data, '提示', {
        confirmButtonText: '确定',
        callback: action => {
          this.clearUpload()
          console.log('上传成功')
          this.disabledUpload = false
        }
      })
    },
    onError(res) {
      this.$alert('创建失败', '提示', {
        confirmButtonText: '确定',
        callback: action => {
          console.log('上传失败')
          this.disabledUpload = false
        }
      })
    },
    handleChange(file, fileList, scope) {
      // 文件是否已存在上传列表中
      const isExists = this.fileList.some((f) => f.name === file.name)
      if (isExists) {
        this.$message.error('文件：' + file.name + '已存在，请勿重复上传!')
        // 文件展示列表是将新添加的文件放在数组末尾
        fileList.pop()
        this.disabledUpload = false
        return
      }
      const isGt100M = Number(file.size / 1024 / 1024) > 100
      if (isGt100M) {
        const currIdx = fileList.indexOf(file)
        fileList.splice(currIdx, 1)
        this.$message.error('文件大小不能超过100MB，请压缩后重新上传！')
        this.disabledUpload = false
        return
      }
      if (file) {
        this.filedForm.attName = file.name
        this.fileList.push(file)
      }
    },
    handlefiledSumbit() {
      if (this.$isNotEmpty(this.recAcctTypeOptions)) {
        if (this.$isNotEmpty(this.fileList)) {
          if (!this.isfiledialogUpload) {
            const formData = new FormData()
            this.fileList.map(item => {
              // 判断附件是否已上传 没上传才上传
              if (this.isHasFile(item.name) && item.raw) {
                const typeOption = this.recAcctTypeOptions.filter(option => option.eleCode === this.filedForm.recAcctType)
                item.tcode = typeOption[0].eleCode
                item.tname = typeOption[0].eleName
                formData.append('files', item.raw, item.name)
                formData.append('path', '')// 文件存储路径
                formData.append('subId', '')// 业务系统编码
                formData.append('typeCode', item.tcode)// 类型编码
                formData.append('bizCode', '')// 模块编码
                formData.append('bizCodeName', '')// 模块名称
                formData.append('bizTblName', this.bizTblName)// 业务表名称
                formData.append('bizId', this.bizDataId)// 业务记录编码,业务表主键ID
                formData.append('isEsSearch', 'true')// 是否全文检索
                formData.append('fileComment', '')// 附件描述
                formData.append('attType', '0')// 附件类别
                formData.append('appendixType', item.tname)// 附件类型
                formData.append('appendixTypeCode', item.tcode)// 附件类型编码
                formData.append('source', this.source)// 来源
              } else {
                this.$message.error('文件：' + item.name + '已存在，请勿重复上传!')
                this.disabledUpload = false
              }
            })
            if (formData.has('files')) {
              this.disabledUpload = true
              this.$callApi('uploadAttachment', formData, result => {
                if (result.success) {
                  if (this.$isNotEmpty(result.data)) {
                    this.$emit('initFileList', result.data.attList)
                  }
                }
              }, result => {
                this.disabledUpload = false
              },{ isSave:false })
            }
          } else {
            if (this.filedForm.recAcctType) {
              var eleName = ''
              this.recAcctTypeOptions.forEach(option => {
                if (option.eleCode === this.filedForm.recAcctType) {
                  eleName = option.eleName
                }
              })
              const callbackFailed = result => {
                this.disabledUpload = false
              }
              this.disabledUpload = true
              this.$emit('filedialogUpload', this.fileList, this.filedForm.recAcctType, eleName, callbackFailed)
            } else {
              this.$message.error('请设置上传附件类型')
            }
          }
        } else {
          this.$message.error('请选择文件，然后再执行上传')
        }
      } else {
        this.$message.error('请设置上传附件类型')
      }
    },
    isHasFile(fileName) {
      const tempData = this.tableTempData.filter(item => item.attName === fileName)
      const attData = this.attList.filter(item => item.attName === fileName)
      if (!this.$isEmpty(tempData) || !this.$isEmpty(attData)) {
        this.isfiledialog = false
        return false
      }
      return true
    },
    handleRemove(file, fileList) {
      const currIdx = this.fileList.indexOf(file)
      this.fileList.splice(currIdx, 1)
    },
    uploadBlFileValidate(accountId,fileNames) {
      this.$callApi('blFillUploadValide&accountId=' + accountId + "&fileNames=" +fileNames, {}, result => {
        this.missingRecordFileValidateResult = true
        return true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep .el-list-enter-active,
  ::v-deep .el-list-leave-active {
    transition: none;
  }
  ::v-deep .el-list-enter,
  ::v-deep .el-list-leave-active {
    opacity: 0;
  }
  ::v-deep .el-upload-list {
    height: 40px;
  }
</style>
