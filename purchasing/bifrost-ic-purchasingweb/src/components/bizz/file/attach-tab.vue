<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-15 11:53:43
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-30 11:30:21
-->
<template>
  <div class="common-page multipleTabsAttachment">
    <base-attachment ref="baseAttachment" :isBlueTable="isBlueTable"/>
  </div>
</template>

<script>
import BaseAttachment from '../file/attachment'

export default {
  name: 'attach-tab',
  components: { BaseAttachment },
  props: {
    isBlueTable: { type: Boolean, default: false }
  },
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      attList: [], // 第一次加载的附件数据
      isError: false // 是否发生过错误
    }
  },
  methods: {
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.initAssembly(dataVo, {}, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      this.$fillAtt(this, dataVo)
      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
    },
    initAssembly(meta, mode, formFormat) {
      this.$refs.baseAttachment.$children[0].btAddText = '上传附件'
      this.$refs.baseAttachment.$children[0].initMeta(meta)

      // 附件区块按钮栏顶部隐藏默认的padding
      this.$nextTick(() => {
        this.$refs.baseAttachment.setButtonNormalNoPaddingTop(true)
      })
      // 初始化数据
      this.attList = []
      this.isError = false
    }
  }
}
</script>
<style>
.multipleTabsAttachment .basefile {
  height: 100%;
  padding-left: 0px;
}
</style>
