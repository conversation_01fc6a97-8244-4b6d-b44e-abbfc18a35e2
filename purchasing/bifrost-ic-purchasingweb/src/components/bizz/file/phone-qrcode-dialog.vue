<template>
  <el-dialog
    title="二维码"
    append-to-body
    :visible.sync="visible"
    width="420px"
    :before-close="handleClose">
    <div class="qrcode"
         v-loading="qrcodeLoading"
         element-loading-text="拼命加载中"
         element-loading-spinner="el-icon-loading"
         >
      <img
        v-if="!isExpire"
        :src="'data:image/jpg;base64,'+ qrcodeUrl"
        alt=""
        style="width: 300px;height: 300px"
        @load="imageLoad"
        @error="imageError"/>
      <div v-else class="qrcode-expire flex-column" style="width: 300px;height: 300px">
        <i class="el-icon-warning"></i>
        <p>二维码已过期，<span class="qrcode-expire-refresh" @click="refreshQrcode">刷新</span></p>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getToken, getUser, getCurrentContext } from '@/utils/auth'
import debounce from 'lodash/debounce'

export default {
  name: 'phone-qrcode',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 单据临时附件id
    attTempId: {
      type: String,
      default: ''
    },
    // 请求附件类型
    attTypeTableName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      qrcodeUrl: '',
      timer: null,
      // 二维码是否过期
      isExpire: false,
      debGetStatus: null,
      qrcodeLoading: false
    }
  },
  watch: {
    visible: {
      handler() {
        if (this.visible) {
          this.qrcodeUrl = ''
          this.isExpire = false
          this.init()
        }
      },
      immediate: true
    },
    attTempId: {
      handler() {
        this.qrcodeUrl = ''
      },
      immediate: true
    }
  },
  created() {
    this.debGetStatus = debounce(this.getStatus, 1000)
  },
  methods: {
    init() {
      clearTimeout(this.timer)
      if (this.attTempId && !this.qrcodeUrl && !this.isExpire) {
        this.qrcodeLoading = true
        const params = {
          formTempId: this.attTempId,
          token: getToken(),
          userId: getUser()?.userId,
          attTypeTableName: this.attTypeTableName,
          accountSetId: getCurrentContext()?.booksetId
        }
        this.$callApi('generatePhoneAppQrCode', params, (result) => {
          if (result.success) {
            this.qrcodeUrl = result.data || ''
            this.debGetStatus()
          }
          this.qrcodeLoading = false
          return true
        }, () => {
          this.qrcodeLoading = false
          return true
        })
      }
    },
    refreshQrcode() {
      this.isExpire = false
      this.init()
    },
    getStatus() {
      if (!this.qrcodeUrl || this.isExpire || !this.visible) {
        return
      }
      this.timer = setTimeout(() => {
        const params = {
          formTempId: this.attTempId,
          userId: getUser()?.userId
        }
        this.$callApi('validatePhoneAppQrCodeTime', params, (result) => {
          if (result.success) {
            this.debGetStatus()
            if (result.data?.includes('已同步')) {
              this.$emit('refresh')
            }
          }
          return true
        }, () => {
          this.isExpire = true
          this.qrcodeUrl = ''
          return true
        }, {
          isSave: false
        })
      }, 1000)
    },
    handleClose() {
      this.$callApi('closeQrcodeDlg', {
        formTempId: this.attTempId,
        userId: getUser()?.userId
      }, () => true, () => true)
      this.$emit('refresh')
      this.$emit('update:visible', false)
    },
    imageLoad() {
    },
    imageError() {
    }
  }
}
</script>

<style lang="scss" scoped>
.qrcode {
  display: flex;
  justify-content: center;
  &-expire {
    align-items: center;
    justify-content: center;
    border: 1px solid #dddddd;
    .el-icon-warning {
      font-size: 40px;
      color: #eebf43;
      margin-bottom: 30px;
    }
    &-refresh {
      color: #478bfe;
      cursor: pointer;
    }
  }
}
</style>
