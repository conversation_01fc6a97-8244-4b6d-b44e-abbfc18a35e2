<template>
  <div class="outUploadFile">
    <el-form
      ref="fileForm"
      class="upload-file"
      :style="`${isVertical ? '': 'display: flex'}`"
      :model="filedForm"
      :disabled="formDisabled"
      :label-width="isVertical ? '100px' : '80px'">
      <el-form-item v-if="!recAcctName" :label="isVertical ? '所属类别' : '附件'" :required="isRequired" :style="isVertical ? '' : 'flex: 1'">
        <el-select v-model="filedForm.recAcctType" :disabled="recAcctTypeDisabled" :placeholder="isVertical ? '请选择附件类别' : '请选择'">
          <el-option
            v-for="item in recAcctTypeOptions"
            :key="item.eleName"
            :label="item.eleName"
            :value="item.eleCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="isVertical ? '文件名称' : ''" :required="isRequired" :label-width="isVertical ? '100px' : '12px'">
        <el-upload
          ref="upload"
          style="word-break: break-all;"
          :file-list="fileList"
          :on-change="handleChange"
          :action="uploadUrl"
          :on-preview="handlePreview"
          :show-file-list="showFileList"
          :on-success="onSuccess"
          :on-error="onError"
          :auto-upload="false"
          :multiple="true"
          :on-exceed="masterFileMax"
          :on-remove="handleRemove"
          :accept="accept">
          <el-button
            slot="trigger"
            :loading="loading"
            :type="isVertical ? '' : 'primary'"
            :size="isVertical ? 'mini' : 'small'"
          >{{ loading ? '上传中' : '选择文件' }}</el-button>
          <span v-show="showTip">&nbsp;&nbsp;上传文件格式只能是 {{ accept }}，且不超过100MB</span>
        </el-upload>
      </el-form-item>
      <!-- <el-form-item v-if="autoUpload" style="margin-bottom: 0px;">
        <el-button type="primary" @click="()=>handlefiledSumbit()">确定</el-button>
      </el-form-item> -->
      <file-view ref="fileView"></file-view>
    </el-form>
    <div class="fileTable loading-size" v-if="!showFileList">
      <el-table
        ref="uploadFileTable"
        v-loading="tableLoading"
        element-loading-spinner="el-icon-loading"
        row-key="id"
        :data="fileList"
        border
        stripe
        height="160px"
        style="width: 100%">
        <el-table-column
          v-for="item in tableData.header"
          :key="item.id"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :align="item.align ? item.align : 'center'">
          <template slot-scope="scope">
            <div v-if="item.prop === 'option'">
              <el-button  @click="handleRemove(scope.row)" type="text" size="small">{{scope.row[item.prop]}}</el-button>
            </div>
            <div v-else>
              {{scope.row[item.prop]}}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import debounce from 'lodash/debounce'

  export default {
    name: 'uploadFile',
    props: {
      props: {
        type: Object,
        default: () => ({
          name: 'attName',
          uid: 'attId'
        })
      },
      accept: {
        type: String,
        default: '.doc,.docx,.docm,.xls,.xlsx,.ppt,.pptx,.pdf,.rar,.zip,.png,.jpg,.jpeg,.ofd'
      },
      files: {
        type: Array,
        default: () => ([])
      },
      limit: {
        type: Number,
        default: 3
      },
      hasAttType: {
        type: Boolean,
        default: false
      },
      recAcctName: {
        type: String,
        default: ''
      },
      source: {
        type: String,
        default: '本地上传'
      },
      attTypeTableName: {// 附件类型表名 默认报销附件类型
        type: String,
        default: 'ELE_BX_ATT_TYPE'
      },
      bizTblName: {// 业务表名称
        type: String,
        default: ''
      },
      bizDataId: {// 业务记录编码,业务表主键ID
        type: String,
        default: ''
      },
      autoUpload: {// 是否自动上传
        type: Boolean,
        default: true
      },
      showFileList: { // true显示文件列表 则不显示表格
        type: Boolean,
        default: true
      },
      showTip: {
        type: Boolean,
        default: true
      },
      layout: { // 布局 Horizontal横向布局 vertical纵向布局
        type: String,
        default: 'vertical'
      },
      tableData: { // 表格数据 {header: [], content: []} 表头和内容要分开传 content 要有id name属性
        type: Object,
        default: () => ({})
      },
      formDisabled: {
        type: Boolean,
        default: false
      },
      tableLoading: {
        type: Boolean,
        default: true
      },
      needUploadMessage: {
        type: Boolean,
        default: true
      },
      sameType: {
        type: Boolean,
        default: true
      },
      isDialog: {
        type: Boolean,
        default: false
      },
      isRequired: {
       type: Boolean,
       default: false
    }
    },
    data() {
      return {
        filedForm: {
          bizId: '',
          recAcctType: '',
          fileName: '',
          fileComment: ''
        },
        recAcctTypeOptions: [],
        fileList: [],
        uploadUrl: '',
        debounceSubmit: null,
        fileMap: {},
        loading: false,
        recAcctTypeDisabled: false,
        removeIds: []
      }
    },
    watch: {
      files: {
        handler() {
          const list = []
          this.files.forEach(file => {
            const name = file[this.getPropName] ? file[this.getPropName] : file['name']
            const uid = file[this.getPropUid] ? file[this.getPropUid] : file['uid']
            const status = file[this.getPropName] ? 'success' : 'ready'
            list.push({ ...file, name, uid, status })
          })
          this.fileList = list
          if (!this.showFileList) {
            // 滚动到表格底部
            const table = this.$refs.uploadFileTable
            if (table && table.$refs.bodyWrapper.children) {
              this.$nextTick(() => {
                table.$refs.bodyWrapper.scrollTop = table.$refs.bodyWrapper.children[0].scrollHeight
              })
            }
          }
          this.resetFileMap()
        },
        immediate: true
      }
    },
    computed: {
      getPropName() {
        return this.props.name
      },
      getPropUid() {
        return this.props.uid
      },
      isVertical() {
        return this.layout === 'vertical'
      }
    },
    created() {
      if (!this.hasAttType) {
        this.selectAttTypeList()
      }
      this.debounceSubmit = debounce(this.handlefiledSumbit, 500)
    },
    methods: {
      hasFile(file) {
        const fileIndex = this.fileMap[file[this.getPropName]]
        return fileIndex || fileIndex === 0
      },
      selectAttTypeList(selectRecAcctTypeOptions, selectDisabled = false) {
        if (selectRecAcctTypeOptions) {
          this.recAcctTypeDisabled = selectDisabled
          var recAcctTypeOptionArr = selectRecAcctTypeOptions.split(',')
          if (recAcctTypeOptionArr) {
            this.recAcctTypeOptions = []
            recAcctTypeOptionArr.forEach(recAcctTypeOption => {
              var ele = recAcctTypeOption.split(':')
              var eleName = ele[0]
              if (ele && ele.length > 1) {
                eleName = ele[1]
              }
              this.recAcctTypeOptions.push({ eleCode: ele[0], eleName: eleName })
            })
            this.filedForm.recAcctType = this.recAcctTypeOptions[0].eleCode
          }
        } else {
          this.$callApi('getAccSetEle&tableName=' + this.attTypeTableName, {}, result => {
            if (result.success) {
              this.recAcctTypeOptions = result.data
              this.filedForm.recAcctType = this.recAcctTypeOptions[0].eleCode
            }
            return true
          })
        }
      },
      masterFileMax(files, fileList) {
        this.$message.error(`请最多上传 ${this.limit} 个文件！`)
      },
      clearUpload() { //  清理上传的文件
        // 清理缓存文件
        this.$refs.upload.clearFiles()
        this.fileList = []
      },
      onSuccess(res) {
        this.$alert(res.data, '提示', {
          confirmButtonText: '确定',
          callback: action => {
            console.log('上传成功')
          }
        })
      },
      onError(res) {
        this.$alert('创建失败', '提示', {
          confirmButtonText: '确定',
          callback: action => {
            console.log('上传失败')
          }
        })
      },
      handleChange(file, fileList, scope) {
        // 文件是否已存在上传列表中
        const isExists = this.fileList.some((f) => f.name === file.name)
        if (isExists) {
          this.$message.error('文件：' + file.name + '已存在，请勿重复上传!')
          // 文件展示列表是将新添加的文件放在数组末尾
          fileList.pop()
          return
        }
        const isGt100M = Number(file.size / 1024 / 1024) > 100
        if (isGt100M) {
          const currIdx = fileList.indexOf(file)
          fileList.splice(currIdx, 1)
          this.$message.error('文件大小不能超过100MB，请压缩后重新上传！')
          return
        }
        if (!this.hasFile(file)) {
          this.filedForm.attName = file.name
          this.fileList.push(file)
        }
        if (this.autoUpload) {
          this.loading = true
        }
        this.debounceSubmit(true)
      },
      handlefiledSumbit(isChange) {
        if (!this.$isNotEmpty(this.recAcctTypeOptions)) {
          return this.$message.error('请设置上传附件类型')
        }
        if (isChange && !this.autoUpload) {
          const typeOption = this.recAcctTypeOptions.filter(option => option.eleCode === this.filedForm.recAcctType)
          this.$emit('initFileList', [...this.fileList], typeOption)
          return
        }
        if (!this.$isNotEmpty(this.fileList)) {
          return this.$message.error('请选择文件，然后再执行上传')
        }
        this.loading = true
        const formData = new FormData()
        this.fileMap = {}
        this.fileList.map((item, index) => {
          // 判断附件是否已上传 没上传才上传
          this.fileMap[item.name] = index
          if (item.raw) {
            const typeObj = {
              key: 'eleCode',
              value: this.filedForm.recAcctType
            }
            if (this.recAcctName) {
              typeObj.key = 'eleName'
              typeObj.value = this.recAcctName
            }
            if (this.sameType) {
              const typeOption = this.recAcctTypeOptions.filter(option => option[typeObj.key] === typeObj.value)
              item.tcode = typeOption[0].eleCode
              item.tname = typeOption[0].eleName
            }
            formData.append('files', item.raw, item.name)
            formData.append('path', '')// 文件存储路径
            formData.append('subId', '')// 业务系统编码
            formData.append('source', this.source)// 来源
            formData.append('isEsSearch', 'true')// 是否全文检索
            formData.append('fileComment', '')// 附件描述
            formData.append('attType', '0')// 附件类别
            formData.append('bizTblName', this.bizTblName)// 业务表名称
            formData.append('bizId', this.bizDataId)// 业务记录编码,业务表主键ID
            if (this.isDialog) {
              // 如果下拉框是disabled的证明是审核依据
              formData.append('typeCode', '审核依据')// 类型编码
              formData.append('bizCode', 'BX')// 模块编码
              formData.append('bizCodeName', '报销')// 模块名称
              formData.append('appendixType', '审核依据')// 附件类型
              formData.append('appendixTypeCode', '999')// 附件类型编码
              formData.append('appendixClass', item.tname)// 附件类型
              formData.append('appendixClassCode', item.tcode)// 附件类型编码
            } else {
              formData.append('typeCode', item.tcode)// 类型编码
              formData.append('bizCode', '')// 模块编码
              formData.append('bizCodeName', '')// 模块名称
              formData.append('appendixType', item.tname)// 附件类型
              formData.append('appendixTypeCode', item.tcode)// 附件类型编码
            }
          }
        })
        let requestStatus = false
        if (formData.has('files')) {
          this.loading = false
          this.$callApi('uploadAttachment', formData, result => {
            if (result.success && this.$isNotEmpty(result.data)) {
              requestStatus = true
              this.resetFileList(result.data.attList)
              // sessionStorage.setItem('fileList', JSON.stringify(result.data.attList))
              const typeOption = this.recAcctTypeOptions.filter(option => option.eleCode === this.filedForm.recAcctType)
              this.$emit('initFileList', [...this.fileList], typeOption)
              this.loading = false
            }
            return !this.needUploadMessage
          }, result => {
            requestStatus = false
            this.resetFileList()
            this.loading = false
          },{ isSave:false })
        } else {
          this.loading = false
        }
        return requestStatus
      },
      resetFileList(attList = []) {
        attList.forEach(file => {
          const index = this.fileMap[file[this.getPropName]]
          this.fileList[index].status = 'success'
          this.fileList[index] = { ...file, name: file[this.getPropName], uid: file[this.getPropUid], raw: '' }
        })
        for (let i = this.fileList.length - 1; i >= 0; i--) {
          const file = this.fileList[i]
          if (file.raw) {
            this.fileList.splice(i, 1)
          }
        }
        this.resetFileMap()
      },
      handleRemove(file, fileList) {
        if (!file.raw && !this.removeIds.includes(file.id)) {
          this.removeIds.push(file.id)
        }
        const currIdx = this.fileMap[file.name]
        this.fileList.splice(currIdx, 1)
        this.resetFileMap()
        this.$emit('initFileList', [...this.fileList])
      },
      resetRemoveFileIds() {
        this.removeIds = []
      },
      handleRemoveFromServer() {
        if (this.removeIds.length === 0) {
          return
        }
        // 从服务器中删除附件
        const params = {
          ids: this.removeIds.join(',')
        }
        this.$callApiParams('deleteAttachment', params, (result) => {
          return true
        })
      },
      resetFileMap() {
        this.fileMap = {}
        this.fileList.forEach((item, index) => {
          this.fileMap[item.name] = index
        })
      },
      getFile(name) {
        const index = this.fileMap[name]
        return this.fileList[index]
      },
      handlePreview(file) {
        const tarFile = this.getFile(file.name)
        if (!tarFile || (tarFile && tarFile.raw)) {
          return this.$message.warning('请先上传该附件')
        }
        const fileIds = [tarFile[this.getPropUid]]
        this.$refs.fileView.open({ fileIds, bizDataId: null })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .outUploadFile {
    display: flex;
    flex-direction: column;
    .upload-file {
      flex: 1;
      /deep/.el-upload-list__item {
        transition: none;
      }
    }
    .fileTable {
      flex: 1;
    }
  }
</style>

