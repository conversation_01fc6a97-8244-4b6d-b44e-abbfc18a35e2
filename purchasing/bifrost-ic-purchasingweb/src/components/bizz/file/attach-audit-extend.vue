<template>
  <b-page ref="basePageFiles" id="attach-audit-extent">
    <template #mainContent>
      <div class="el-container hide-checkbox-if-disabled attachAuditExtend">
        <div class="el-table-fix">
          <el-table :size="tableSize" border :data="attData" :class="tableExClass"
                    :span-method="arraySpanMethod"
                    @selection-change="handleSelectionChange">
            <el-table-column class-name="column-index" type="selection" align="center" width="25"
                             :selectable="selectable"/>
            <el-table-column class-name="column-index" show-overflow-tooltip prop="index" align="center" width="30"/>
            <el-table-column prop="" label="文件名称" show-overflow-tooltip>
              <template slot-scope='{row}'>
                <div v-show="row.isOne">
                  <b>{{ row.附件名称 }}</b>
                </div>
                <div v-show="!row.isOne">
                  <a @click="clickPreviewFile(row)">{{ row.附件名称 }}</a>
                </div>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="类别" label="类别" :width="typeWidth"/>
            <el-table-column show-overflow-tooltip prop="来源" label="来源" :width="sourceWidth"/>
            <el-table-column show-overflow-tooltip prop="上传时间" label="上传时间" :width="uploadTimeWidth"/>
            <el-table-column show-overflow-tooltip prop="上传人" label="上传人" :width="uploadWidth"/>
            <!-- <el-table-column prop="" label="备注" :width="remaekWidth">
              <template slot-scope='{row}'>
                <div v-show="!row.isInvoice"/>
                <div v-show="row.isInvoice"
                    :title="`开票金额：${$formatMoney(row.开票金额)} 开票日期：${row.开票日期}`">
                  {{$formatMoney(row.开票金额)}} ({{row.开票日期}})
                </div>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
      <attach-audit-dialog ref="attachAuditDialog" :dialog.sync="isAttachAuditDialog"
                           @reload='reload'></attach-audit-dialog>
      <filedialog ref="filedialog" :dialog.sync="isfiledialog" @reload="reload"
                  @filedialogUpload="filedialogUpload"></filedialog>
      <file-view ref="fileView"></file-view>
    </template>
  </b-page>
</template>

<script>
import FileView from '../../../components/fileview/file-view.vue'
import { upload, batchDownload, downloadFile } from '@/api/file/file'

export default {
  // 这个组件的name被使用在审核界面中，修改会导致错误
  name: 'attach-audit-extend',
  components: { FileView },
  data() {
    return {
      dataVo: { extData: {}},
      attData: [],
      fileList: [], // 上传的文件集合
      bizTblName: 'ATTACHMENT',
      bizDataId: '',
      nodeName: '审核', // 节点名称
      showUploadButton: false,
      isAttachAuditDialog: false,
      fileIds: [],
      isFormDetail: false, // 是否是表单详情
      typeWidth: 96,
      sourceWidth: 80,
      uploadWidth: 95,
      uploadTimeWidth: 95,
      remaekWidth: 120,
      fileName: '批量下载', // 批量下载文件名称
      isEnableDelAttachForAudit: false, // 是否启用删除审核依据
      checkedRow: [],
      isfiledialog: false,
      tableSize: '',
      attTypeTableName: '', // 附件类型表名
      isCustomFile: false,
      // 另一个审核附件控件，使用多tab时，审核界面有两个审核附件控件
      anotherAuditFileObj: undefined,
      isReloadByAnotherAuditFileObj: false,
      setFileTabLabel: undefined, // 这个方法设置tab的标题，使其含有附件个数
      isBlueTable: false,
      addRowCallback: undefined, // 文件列表增加后的回调
      deleteRowCallback: undefined // 文件列表删除后的回调
    }
  },
  computed: {
    tableExClass() {
      return this.isBlueTable ? 'blue-table' : ''
    }
  },
  methods: {
    init(dataVo, nodeName, showUploadButton) {
      this.setFileTabLabel = undefined
      this.dataVo = dataVo
      if (dataVo) {
        this.attData = []
        var index = 1
        if (this.$isNotEmpty(dataVo.extData.attData)) {
          dataVo.extData.attData.forEach((v, k) => {
            if (v.isAnnex === true || this.$isEmpty(v.ID)) { // v.ID为空的数据是区分类别的空数据
              if (v.index) {
                v.index = index
                index = index + 1
              }
              this.attData.push(v)
            }
          })
        }
        if (this.attData.length === 1 &&
          this.$isEmpty(this.attData[0].ID)) { // 当只有区分类别的空数据时，应该把attData置空
          this.attData = []
        }
        if (dataVo &&
          dataVo.extData) {
          if (dataVo.extData.setEditTabLabel) {
            // dataVo填充有设置页签标题的函数
            // this.$parent类似audit-file-tab.vue这些组件
            var tabName = this.$getComponentName(this.$parent)
            if (this.$isNotEmpty(tabName)) {
              this.setFileTabLabel = (fileCount) => {
                let eIeInvoiceCount = 0
                if (typeof dataVo.eInvoiceCount === 'function') {
                  // 发票列表total数（针对form-detail-tab-attach-bx的上下双列表，此时附件数为两个列表的数据之和）
                  eIeInvoiceCount = dataVo.eInvoiceCount()
                }
                fileCount = fileCount + eIeInvoiceCount
                dataVo.extData.setEditTabLabel(tabName, `附件(${fileCount})`)
              }
            }
          }

          if (dataVo.extData.setFileTabLabel) {
            this.setFileTabLabel = dataVo.extData.setFileTabLabel
          }
        }
        this.attTypeTableName = dataVo.extData.attTypeTableName
      }
      this.nodeName = nodeName
      this.showUploadButton = showUploadButton

      var buttons = []
      if (dataVo) {
        this.bizDataId = dataVo.data.id
      }
      this.$refs.fileView.fileDownloadByFileIdExtData = this.getDataVoExtData()

      this.$callApiParams('getAllWfNodesByWfMetaId',
        { 'wfMetaIds': dataVo.data.wfMetaId, 'nodeName': this.nodeName },
        result => {
          var wfNodes = result.data
          if (!this.isFormDetail) {
            if (this.showUploadButton) {
              if (result.success && wfNodes && wfNodes[0] && wfNodes[0].selWfNodeSettingStr.indexOf('必须上传附件') !== -1 &&
                this.$isNotEmpty(wfNodes[0].selectRecAcctTypeOptions)) {
                buttons.push({
                  text: '上传审核依据',
                  icon: 'el-icon-upload2',
                  enabledType: '0',
                  click: row => (this.fileUploadDialog(row))
                })
                this.$refs.filedialog.selectAttTypeList(wfNodes[0].selectRecAcctTypeOptions)
                this.isCustomFile = true
              } else if (dataVo.extData['审核依据是否可选类别'] === '是') {
                buttons.push({
                  text: '上传审核依据',
                  icon: 'el-icon-upload2',
                  enabledType: '0',
                  click: row => (this.fileUploadDialog(row))
                })
                if (this.$isNotEmpty(dataVo.extData.attTypeTableName)) {
                  this.$refs.filedialog.attTypeTableName = dataVo.extData.attTypeTableName
                }
              } else {
                buttons.push({ text: '上传审核依据', icon: 'el-icon-upload2', enabledType: '0', upload: this.fileUpload })
              }
            }
            buttons.push({ text: '删除审核依据', icon: 'el-icon-delete', enabledType: '-1', click: this.delAttachForAudit })
            buttons.push({ text: '下载文件', icon: 'el-icon-download', enabledType: '-1', click: this.fileDownload })
            buttons.push({ text: '预览文件', icon: 'el-icon-notebook-2', enabledType: '-1', click: this.previewFile })
          } else {
            buttons.push({ text: '下载文件', icon: 'el-icon-download', enabledType: '-1', click: this.fileDownload })
            buttons.push({ text: '预览文件', icon: 'el-icon-notebook-2', enabledType: '-1', click: this.previewFile })
          }
          this.$refs.basePageFiles.init({ buttons: buttons })

          const customizeButtons = []
          this.fileIds = []
          if (this.$isNotEmpty(this.attData)) {
            this.attData.forEach(att => {
              if (this.$isEmpty(att.index) && att.附件名称 === '审核附件') {
                this.isEnableDelAttachForAudit = true
              }
              if (this.$isNotEmpty(att.attId)) {
                this.fileIds.push(att.attId)
              }
            })
            if (this.isEnableDelAttachForAudit) {
              customizeButtons.push('删除审核依据')
            }
          }

          // 设置附件页签上的附件个数
          if (this.setFileTabLabel) {
            this.setFileTabLabel(this.fileIds.length)
          }

          this.$nextTick(() =>
            this.$refs.basePageFiles.rowChecked([], this.$isEmpty(this.attData)))
          this.$nextTick(() =>
            this.$refs.basePageFiles.customizeButtons(customizeButtons))

          this.$refs.basePageFiles.setButtonBarVisible(true, false)
          this.$refs.basePageFiles.setNoPager()
          return true
        })
    },
    previewFile() {
      var checkedRows = this.checkedRow
      if (this.$isEmpty(checkedRows)) {
        checkedRows = this.attData
      }

      if (checkedRows.length === 1) {
        this.clickPreviewFile(checkedRows[0])
      } else if (checkedRows.length > 1) {
        this.$refs.fileView.open({ fileIds: this.fileIds, bizDataId: null })
      }
    },
    clickPreviewFile(row) {
      this.$fileDownloadByFileId(
        this.$refs.fileView, row['附件名称'], row.attId, this.getDataVoExtData())
    },
    getDataVoExtData() {
      return this.dataVo ? this.dataVo.extData : {}
    },
    fileUploadDialog(checkedRows) {
      if (!this.isCustomFile) {
        this.$refs.filedialog.attTypeTableName = this.attTypeTableName
        this.$refs.filedialog.selectAttTypeList()
      }
      this.$refs.filedialog.bizTblName = this.bizTblName
      this.$refs.filedialog.bizDataId = this.bizDataId
      this.$refs.filedialog.isfiledialogUpload = true
      this.isfiledialog = true
    },
    handleSelectionChange(rows) {
      this.checkedRow = rows
      this.fileIds = []
      if (this.$isNotEmpty(this.checkedRow)) {
        this.checkedRow.forEach(att => {
          if (this.$isNotEmpty(att.attId)) {
            this.fileIds.push(att.attId)
          }
        })
      } else {
        this.attData.forEach(att => {
          if (this.$isNotEmpty(att.attId)) {
            this.fileIds.push(att.attId)
          }
        })
      }
      if (this.$isNotEmpty(this.fileIds)) { // 启用下载文件按钮
        this.$refs.basePageFiles.customizeButtons(['下载文件'])
      } else {
        this.$refs.basePageFiles.rowChecked([], this.$isEmpty(this.attData))
      }
    },
    selectable(row, index) {
      return row.index
    },
    getTable() {
      const table = {
        data: this.attData
      }
      return table
    },
    initCommonBiz(dataVo, exParams) {
      exParams = exParams || {}
      this.addRowCallback = exParams.addRowCallback
      this.deleteRowCallback = exParams.deleteRowCallback
      if (this.addRowCallback) {
        this.addRowCallback(this)
      }
      if (exParams.mode === '详情') {
        this.initFormDetail(dataVo)
      } else {
        this.showUploadButton = true
        this.nodeName = dataVo.data.currentNode
        this.init(dataVo, this.nodeName, this.showUploadButton)
      }
    },
    initFormDetail(dataVo) {
      this.setTableSizeMedium() // 详情附件统一使用中等字号
      this.isFormDetail = true
      this.init(dataVo, this.nodeName, this.showUploadButton)
    },
    setTableSizeMedium() {
      // this.tableSize = 'medium'
      this.sourceWidth = 130
      this.typeWidth = 130
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.index === '' && columnIndex === 2) {
        return [1, 4]
      }
      return [1, 1]
    },
    handlePreView(row) { // 预览单个文件
      const fileIds = []
      fileIds.push(row.attId)
      this.$refs.fileView.open({ fileIds: fileIds, bizDataId: null })
    },
    delAttachForAudit() {
      this.$refs.attachAuditDialog.billId = this.bizDataId
      this.$refs.attachAuditDialog.isAudit = 1
      this.isAttachAuditDialog = true
    },
    reload() {
      this.isfiledialog = false
      this.isAttachAuditDialog = false
      this.$callApiParams('selectAttachForAudit',
        { billId: this.bizDataId, source: this.dataVo.meta.formType }, // lzj：this.nodeName
        result => {
          if (result.success) {
            this.dataVo.extData.attData = result.data
            this.init(this.dataVo, this.nodeName, this.showUploadButton)

            // 多tab审核时，存在两个审核附件的组件，这里处理两个组件的数据同步
            if (this.anotherAuditFileObj &&
              this.isReloadByAnotherAuditFileObj === false) {
              this.$nextTick(() => {
                this.anotherAuditFileObj.isReloadByAnotherAuditFileObj = true
                this.anotherAuditFileObj.reload()
              })
            }
            this.isReloadByAnotherAuditFileObj = false
            if (this.addRowCallback) {
              this.addRowCallback(this)
            }
          }
          return true
        })
    },
    filedialogUpload(fileList, eleCode, eleName, callbackFailed) {
      this.fileList = this.fileList.concat(fileList)
      var params = {}
      params.typeCode = '审核依据' // 类型编码
      params.bizCode = 'BX' // 模块编码
      params.bizCodeName = '报销' // 模块名称
      params.bizTblName = this.bizTblName // 业务表名称
      params.bizId = this.bizDataId // 业务记录编码,业务表主键ID
      params.attType = '0' // 附件类别
      params.appendixType = '审核依据' // 附件类型
      params.appendixTypeCode = '999' // 附件类型编码
      params.appendixClass = eleName // 附件类型
      params.appendixClassCode = eleCode // 附件类型编码
      params.source = this.nodeName // 来源
      params.methodName = 'reload'
      params.isClearFileList = true // 是否清理附件数据
      this.$uploadAttachment(this, params, callbackFailed) // 上传附件
    },
    fileUpload(result, file, fileList, uploadRef) {
      // 清理缓存文件
      uploadRef.forEach(upload => {
        upload.clearFiles()
      })
      // 获取上传文件大小
      const isGt100M = Number(file.size / 1024 / 1024) > 100
      if (isGt100M) {
        this.$msgbox({
          title: '',
          message: '文件大小不能超过100MB，请压缩后重新上传！',
          type: 'warning'
        })
        return
      }
      if (fileList) {
        this.filedialogUpload(fileList, '', '')
      }
    },
    isHasFile(fileName) {
      const attData = this.attData.filter(item => item['附件名称'] === fileName)
      if (!this.$isEmpty(attData)) {
        return false
      }
      return true
    },
    fileDownload() {
      var checkedRows = this.checkedRow
      if (this.$isEmpty(checkedRows)) {
        checkedRows = this.attData
      }
      // 批量下载
      const attachIdArr = Array.from(checkedRows, ({ attId }) => attId)
      const attachIds = attachIdArr.join(',')
      if (checkedRows.length === 1) {
        downloadFile(attachIds).then((res) => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件信息不存在')
          }
        })
      } else {
        batchDownload({ attachIds: attachIds, fileName: this.fileName }).then(res => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件不存在！')
          }
        }).catch(err => {
          console.log(err)
          this.$message({
            type: 'error',
            message: '文件不存在！'
          })
        })
      }
    },
    handleFileDownloadRes(res, str1) {
      if (!res.data) {
        this.$message.error('文件信息不存在')
        return
      }
      var filename = str1 || undefined
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // 检测是否在IE浏览器打开
        window.navigator.msSaveOrOpenBlob(new Blob([res.data]), filename)
      } else {
        // 谷歌、火狐浏览器
        let url = ''
        if (
          window.navigator.userAgent.indexOf('Chrome') >= 1 ||
          window.navigator.userAgent.indexOf('Safari') >= 1
        ) {
          url = window.webkitURL.createObjectURL(new Blob([res.data]))
        } else {
          url = window.URL.createObjectURL(new Blob([res.data]))
        }
        const link = document.createElement('a')
        const iconv = require('iconv-lite')
        iconv.skipDecodeWarning = true // 忽略警告
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', filename)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      }
    }
  }
}

</script>

<style lang="scss">
.attach-audit-extend {
  height: 100%;
}

#attach-audit-extent .el-table-container {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
}

#attach-audit-extent .el-table-fix {
  // position: absolute;
  width: 100%;
  height: 100%;
}

// #attach-audit-extent .column-bottom {
//   height: calc(100% - 39px) !important;
// }

</style>
<style lang="scss" scoped>
.attachAuditExtend {
  position: relative;
  height: 100%;
}
</style>
