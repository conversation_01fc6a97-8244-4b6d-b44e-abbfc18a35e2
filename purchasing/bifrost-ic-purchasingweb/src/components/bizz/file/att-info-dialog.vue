<template>
  <div id="attInfoDialog">
    <el-dialog
      append-to-body
      :title="'修改附件名称'"
      :visible.sync="isAttInfoDialog"
      width="600px"
      :close-on-click-modal='false'
      @close="handleClose">
      <el-form
        ref="fileForm"
        :model="fileForm"
        label-width="100px"
        :rules="rules">
        <el-row>
          <el-form-item label="附件名称" prop="tempAttName">
            <el-input v-model="fileForm.tempAttName" placeholder="请输入附件名称" maxlength="20"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <template #footer>
        <el-button class="btn-normal" type="primary" @click="handlefiledSumbit('fileForm')" :loading="disabledUpload">确定</el-button>
        <el-button class="btn-normal" @click="handleClose('fileForm')"> 取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'att-info-dialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isAttInfoDialog = bool
    }
  },
  data() {
    return {
      isAttInfoDialog: this.dialog,
      isAttInfoDialogUpload: false,
      fileForm: {
        bizId: '',
        recAcctType: '',
        fileName: '',
        fileComment: ''
      },
      disabledUpload: false,
      rules: {
        tempAttName: [
          { required: true, message: '请输入附件名称', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleClose() {
      this.isAttInfoDialog = false
      this.disabledUpload = false
      this.$emit('update:dialog', false)
      this.$emit('reload')
    },
    handlefiledSumbit() {
      this.disabledUpload = true
      this.$callApi('saveAttachment', this.fileForm, result => {
        if (result.success) {
          this.$emit('reload')
        }
      }, result => {
        this.disabledUpload = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-list-enter-active,
/deep/ .el-list-leave-active {
  transition: none;
}
/deep/ .el-list-enter,
/deep/ .el-list-leave-active {
  opacity: 0;
}
</style>
