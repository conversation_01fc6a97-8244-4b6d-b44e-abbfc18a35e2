<template>
  <div id="invoice-edit-dialog">
    <file-view ref="fileView"/>
    <el-dialog
      append-to-body
      title="电子发票编辑"
      :visible.sync="iseditinvoicedialog"
      width="66%"
      :close-on-click-modal='false'
      @close="handleClose">
        <el-form
          ref="fileForm"
          :model="filedForm"
          label-width="140px"
          :rules="rules">
          <el-row>
            <el-col :span="8">
              <el-form-item label="发票类型" prop="invoiceType">
                <el-select v-model="filedForm.invoiceType">
                  <el-option
                    v-for="item in invoiceTypeOptions"
                    :key="item.eleName"
                    :label="item.eleName"
                    :value="item.eleName">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发票附件名称" prop="attName">
                <el-input v-model="filedForm.attName" maxlength="300" readonly disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="二维码信息">
                <el-input v-model="filedForm.remark" maxlength="64" placeholder="请输入二维码信息"></el-input>
              </el-form-item>
            </el-col>
<!--            <el-col :span="8">-->
<!--                <el-form-item label="上传时间">-->
<!--                  <el-date-picker v-model="filedForm.uploadTime" type="date" placeholder="选择上传时间"></el-date-picker>-->
<!--                </el-form-item>-->
<!--            </el-col>-->
          </el-row>
          <el-row>
            <el-col :span="8">
                <el-form-item label="发票代码" prop="invoiceCode">
                  <el-input v-model="filedForm.invoiceCode" maxlength="64" placeholder="请输入发票代码"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="发票号码" prop="invoiceNumber">
                  <el-input v-model="filedForm.invoiceNumber" maxlength="64" placeholder="请输入发票号码"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开票日期" prop="invoiceDate">
                <el-date-picker v-model="filedForm.invoiceDate" type="date" placeholder="选择开票日期" value-format="yyyy年MM月dd日"></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="校验码">
                <el-input v-model="filedForm.checkCode" maxlength="64" placeholder="请输入校验码"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="受票方名称">
                <el-input v-model="filedForm.draweePartyName" maxlength="64" placeholder="请输入受票方名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="受票方纳税人识别号">
                <el-input v-model="filedForm.draweePartyTariff" maxlength="64" placeholder="请输入受票方纳税人识别号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="受票方地址、电话">
                <el-input v-model="filedForm.draweePartyPhone" maxlength="64" placeholder="请输入受票方地址、电话"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="受票方开户行及帐号">
                <el-input v-model="filedForm.draweePartyAccount" maxlength="64" placeholder="请输入受票方开户行及帐号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="密码区">
                <el-input v-model="filedForm.passwordBlock" maxlength="64" placeholder="请输入密码区"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="货物或对应税劳务服务名称">
                <el-input v-model="filedForm.goodsServiceName" maxlength="64" placeholder="请输入货物或对应税劳务服务名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="规格型号">
                <el-input v-model="filedForm.model" maxlength="64" placeholder="请输入规格型号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单位">
                <el-input v-model="filedForm.unitName" maxlength="64" placeholder="请输入单位"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="数量">
                <el-input v-model="filedForm.number" maxlength="9" placeholder="请输入数量"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="不含税金额(元)" prop="excludingTaxAmount">
                <el-input v-model="filedForm.excludingTaxAmount" class="right-aligned-input"
                          maxlength="12" placeholder="请输入不含税金额"
                          @blur="formateMoney('excludingTaxAmount')"
                          @focus="unformateMoney('excludingTaxAmount')"
                          @keyup.native="proving('excludingTaxAmount')"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="税金(元)" prop="invoiceTax">
                <el-input v-model="filedForm.invoiceTax" class="right-aligned-input"
                          maxlength="12" placeholder="请输入税金"
                          @blur="formateMoney('invoiceTax')"
                          @focus="unformateMoney('invoiceTax')"
                          @keyup.native="proving('invoiceTax')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="价税合计(大写)" prop="invoiceMoneyUppercase">
                <el-input v-model="filedForm.invoiceMoneyUppercase"
                          readonly="readonly" disabled style="background-color:#cccccc" maxlength="64"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="价税合计(小写)" prop="invoiceMoney">
                <el-input v-model="filedForm.invoiceMoney" class="right-aligned-input"
                          maxlength="12" placeholder="请输入价税合计(小写)"
                          @blur="formateMoney('invoiceMoney')"
                          @focus="unformateMoney('invoiceMoney')"
                          @keyup.native="proving('invoiceMoney')"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="销售方名称">
                <el-input v-model="filedForm.sellerName" maxlength="64" placeholder="请输入销售方名称"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="销售方纳税人识别号">
                <el-input v-model="filedForm.sellerTariff" maxlength="64" placeholder="请输入销售方税号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="销售方地址、电话">
                <el-input v-model="filedForm.sellerPhone" maxlength="64" placeholder="请输入销售方地址、电话"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="销售方开户行及帐号">
                <el-input v-model="filedForm.sellerAccount" maxlength="64" placeholder="请输入销售方开户行、帐号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input v-model="filedForm.caption" maxlength="64" :rows="2" type="textarea"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="发票附件">
                <a @click="previewFile(filedForm.attId)">{{filedForm.attName}}</a>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="" prop="isPermit">
                <span style="color: red;float: left;line-height: 20px;">*</span>
                <el-checkbox v-model="filedForm.isPermit">
                  <span style="font-size: 19px;">本人承诺所录入发票信息均真实有效，如若因发票真实性问题带来任何法律和财务上的损失，一切后果由本人自行承担。</span>
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      <template #footer>
        <el-button
          type="primary"
          @click="handlefiledSumbit('fileForm')"
          v-preventDbClick
        >
          确定
        </el-button>
        <el-button @click="handleClose('fileForm')"> 取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'invoice-edit-dialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.iseditinvoicedialog = bool
    }
  },
  data() {
    return {
      iseditinvoicedialog: this.dialog,
      filedForm: {
        fkGuid: '',
        attName: '',
        invoiceType: '',
        invoiceCode: '',
        invoiceNumber: '',
        invoiceMoney: '',
        remark: '',
        invoiceDate: '',
        checkCode: '',
        capitalMoney: '',
        excludingTaxAmount: '',
        draweePartyName: '',
        draweePartyTariff: '',
        draweePartyPhone: '',
        draweePartyAccount: '',
        sellerName: '',
        sellerTariff: '',
        sellerPhone: '',
        sellerAccount: '',
        uploadTime: null,
        caption: '',
        passwordBlock: '',
        goodsServiceName: '',
        model: '',
        unitName: '',
        number: '',
        invoiceTax: '',
        invoiceMoneyUppercase: '',
        isPermit: ''
      },
      invoiceTypeOptions: [],
      isTempData: true, // 是否是临时数据
      attTypeTableName: 'ELE_BX_INV_TYPE', // 附件类型表名 默认报销发票类型
      rules: {
        invoiceType: [
          { required: true, message: '请选择发票类型', trigger: 'change' }
        ],
        attName: [
          { required: true, message: '请输入发票名称', trigger: 'blur' }
        ],
        invoiceNumber: [
          { required: true, message: '请输入发票号码', trigger: 'blur' }
        ],
        invoiceDate: [
          { required: true, message: '请输入开票日期', trigger: 'blur' }
        ],
        excludingTaxAmount: [
          { required: true, message: '请输入不含税金额', trigger: 'blur' }
        ],
        invoiceTax: [
          { required: true, message: '请输入税金', trigger: 'blur' }
        ],
        invoiceMoney: [
          { required: true, message: '请输入价税合计(小写)', trigger: 'blur' }
        ],
        invoiceMoneyUppercase: [
          { required: true, message: '请输入价税合计(大写)', trigger: 'blur' }
        ],
        isPermit: [
          { required: true, message: '请勾选承诺', trigger: 'change' }
        ]
      },
      fileList: [],
      uploadUrl: ''
    }
  },
  methods: {
    selectAttTypeList() {
      this.$callApi('getAccSetEle&tableName=' + this.attTypeTableName, {}, result => {
        if (result.success) {
          this.invoiceTypeOptions = result.data
        }
        return true
      })
    },
    proving(key) { // 只能输⼊数字且只有二位⼩数
      // 先把⾮数字的都替换掉，除了数字和.
      this.filedForm[key] = this.filedForm[key].replace(/[^\d.]/g, '')
      // 必须保证第⼀个为数字⽽不是.
      this.filedForm[key] = this.filedForm[key].replace(/^\./g, '')
      // 保证只有出现⼀个.⽽没有多个.
      this.filedForm[key] = this.filedForm[key].replace(/\.{2,}/g, '')
      // 保证.只出现⼀次，⽽不能出现两次以上
      this.filedForm[key] = this.filedForm[key].replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      let index = -1
      for (const i in this.filedForm[key]) {
        if (this.filedForm[key][i] === '.') {
          index = i
        }
        if (index !== -1) {
          if (i - index > 2) {
            this.filedForm[key] = this.filedForm[key].substring(0, this.filedForm[key].length - 1)
          }
        }
      }
      if (key === 'invoiceMoney') {
        this.filedForm['invoiceMoneyUppercase'] = this.$digitUppercase(this.filedForm[key])
      }
    },
    formateMoney(key) {
      if (this.filedForm[key]) {
        this.filedForm[key] = this.$formatMoney(this.filedForm[key])
      }
    },
    unformateMoney(key) {
      if (this.filedForm[key]) {
        this.filedForm[key] = this.$unFormatMoney(this.filedForm[key])
      }
    },
    handleClose(formName) {
      this.iseditinvoicedialog = false
      this.$emit('update:dialog', false)
      this.$emit('reload')
      this.fileList = []
    },
    handlefiledSumbit(formName) {
      if (!this.filedForm['isPermit']) {
        this.filedForm['isPermit'] = ''
      }
      const amountKeys = { excludingTaxAmount: '不含税金额',
        invoiceTax: '税金',
        invoiceMoney: '价税合计(小写)' }
      for (const key in amountKeys) {
        if (this.$isNotEmpty(this.filedForm[key])) {
          const val = parseFloat(this.$unFormatMoney(this.filedForm[key]))
          if (val > *********.99) {
            this.$message.error(amountKeys[key] + '已超过最大金额999,999,999.99,请修改')
            return
          }
          this.filedForm[key] = val
        }
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          for (var key in this.filedForm) {
            if (this.$isEmpty(this.filedForm[key])) {
              if (key === 'excludingTaxAmount') {
                this.filedForm[key] = 0
              } else {
                this.filedForm[key] = ''
              }
            }
          }
          this.$callApi('saveAttachment', this.filedForm, result => {
            if (result.success) {
              this.handleClose()
              this.$message.success('保存成功!')
            }
            return true
          })
        }
      })
    },
    previewFile(attId) {
      this.$refs.fileView.open({ fileIds: [attId], bizDataId: null })
    }
  }
}
</script>

<style lang="scss" scoped>
  .upload-demo {
      display: flex;
    }
    /deep/ .el-list-enter-active,
    /deep/ .el-list-leave-active {
      transition: none;
    }
    /deep/ .el-list-enter,
    /deep/ .el-list-leave-active {
      opacity: 0;
    }
    /deep/ .el-upload-list {
      height: 40px;
    }
    /deep/ .right-aligned-input .el-input__inner {
      text-align: right;
    }
</style>
