<template>
  <div class="baseinvoice" id="baseInvoice">
    <b-curd ref="curdList"></b-curd>
    <einvoice-add-dialog ref="addEinvoicedialog" :dialog.sync="isaddeinvoicedialog"
                         @reload="reload"></einvoice-add-dialog>
    <invoice-add-dialog ref="addinvoicedialog" :dialog.sync="isaddinvoicedialog" @reload="reload"
                        @initFileList="initFileList"></invoice-add-dialog>
    <invoice-edit-dialog ref="editinvoicedialog" :dialog.sync="iseditinvoicedialog" @reload="reload"
                         @editFiledForm="editFiledForm"></invoice-edit-dialog>
    <file-view ref="fileView"></file-view>
  </div>
</template>

<script>
import FileView from '../../../components/fileview/file-view.vue'
import { batchDownload, downloadFile } from '@/api/file/file'
import $ from 'jquery'
import uniqBy from 'lodash/uniqBy'
import InvoiceAddDialog from '@/components/bizz/invoice/invoice-add-dialog.vue'

export default {
  name: 'baseinvoice', // 发票
  components: { InvoiceAddDialog, FileView },
  data() {
    return {
      dataVo: {}, // 表单数据
      columns: [],
      tableData: this.data,
      fileList: [],
      tableTempData: [],
      isaddinvoicedialog: false,
      iseditinvoicedialog: false,
      bizTblName: 'ATTACHMENT',
      bizDataId: '',
      delIds: [], // 上传前需要删除的附件id集合
      attList: [], // 已上传的附件集合
      fileIds: [], // 临时文件的平台附件信息id集合
      source: '', // 附件来源
      batchDownloadName: '批量下载', // 批量下载文件名称
      invoiceTypeOptions: [],
      attTempId: '', // 附件临时id
      rowsData: [], // 表格数据
      attTypeTableName: 'ELE_BX_INV_TYPE', // 附件类型表名 默认报销发票类型
      isaddeinvoicedialog: false,
      // isEnableEinvoiceFolder: false, // 是否启用发票夹
      isEnableEinvoiceType: false, // 上传发票是否选择类型
      isCform: true, // 是否是表单发票
      einvoiceAttIds: [], // 发票夹附件ids
      fkGuids: [],
      isDetail: false,
      fileExtInfos: {}, // 发票预览时显示的额外信息
      isNoAddDeleteBts: false,
      commonBizAttaVo: {}
    }
  },
  created() {
    const this_ = this
    this_.$onEvent(this_, {
      getDataApiParams(params) { // 自定义查询参数
        if ('selectAttachmentPage'.indexOf(params.dataApiKey) > -1) {
          params.size = 10000
        }
      }
    })
    window.$event.$on('blockFormLoaded', this.blockFormLoaded)
  },
  beforeDestroy() {
    window.$event.$off('blockFormLoaded', this.blockFormLoaded)
  },
  methods: {
    blockFormLoaded() {
      this.$refs.curdList?.reInitLayout?.()
    },
    initData() {
      this.tableTempData = []
      this.fkGuids = []
      this.delIds = []
      this.fileIds = []
      this.einvoiceAttIds = []
    },
    initCommonBiz(isEdit, attaVo, exParams) { // 普通业务附件初始化
      exParams = exParams || {}
      this.isNoAddDeleteBts = false
      if (isEdit !== undefined) {
        this.isNoAddDeleteBts = !isEdit
      }

      this.commonBizAttaVo = attaVo || {}
      this.commonBizAttaVo.attList = this.commonBizAttaVo.attList || []
      this.commonBizAttaVo.fkGuid = this.$isEmpty(this.commonBizAttaVo.fkGuid)
        ? new Date().getTime() : this.commonBizAttaVo.fkGuid

      var dataVo = this.commonBizAttaVo
      this.isCform = true
      if (this.commonBizAttaVo.data === undefined) {
        this.isCform = false
        dataVo = {
          data: { id: '', formType: '' },
          attList: this.commonBizAttaVo.attList,
          extData: {
            currentUser: '',
            attTempId: this.commonBizAttaVo.fkGuid,
            initRefDataVoFromId: undefined,
            batchDownloadName: '',
            attTypeTableName: '',
            bizDataId: '',
            '制单样式': '分tab'
          }
        }
      }

      this.btAddText = '上传发票'
      this.initMeta(dataVo, exParams)
    },
    getTable() {
      return this.$refs.curdList.getTable()
    },
    initCommonBizDetails(dataVo, exParams) {
      exParams = exParams || {}
      if (exParams.mode === '详情' || exParams.mode === '审核') {
        this.isDetail = true
        this.initMeta(dataVo)
      }
    },
    updateBeforeSave() {
      const files = this.commonBizAttaVo.attList || []
      this.commonBizAttaVo.attList = uniqBy([...files, ...this.attList], 'id')
    },
    initMeta(dataVo, exParams = {}) {
      this.dataVo = dataVo
      if (this.isCform) {
        this.bizDataId = dataVo.data ? dataVo.data.id : ''
        this.source = dataVo.meta ? dataVo.meta.formType : ''
      }
      this.attList = dataVo.attList ? dataVo.attList.filter(item => item.attType === '1') : []
      this.attTempId = dataVo.extData ? dataVo.extData.attTempId : ''
      this.batchDownloadName = dataVo.extData ? dataVo.extData.batchDownloadName : '批量下载'
      // this.isEnableEinvoiceFolder = (dataVo.extData[`是否启用发票夹`] === '是')
      this.isEnableEinvoiceType = (dataVo.extData[`上传发票是否选择类型`] === '是')
      this.initData()
      this.fkGuids.push(this.bizDataId, this.attTempId)
      // 行表单时，添加额外数据
      if (exParams?.addAttaTemData) {
        this.addTemData()
      }
      this.init(exParams)
    },
    addTemData() {
      this.attList.forEach(item => {
        this.einvoiceAttIds.push(item.id)
      })
    },
    init(exParams) {
      const initParams = {
        selectionColumnWidth: 35,
        params: {
          dataApiKey: 'selectAttachmentPage',
          // deleteApiKey: 'deleteAttachment',
          FK_GUID_in: this.fkGuids.join(','),
          BIZID_notin: this.delIds.join(','),
          ATT_TYPE_eq: '1',
          tparentCode: '10011',
          ascs: 'CREATE_TIME',
          tableName: this.attTypeTableName,
          einvoiceAttIds: this.einvoiceAttIds.join(',')
        },
        showPager: false,
        buttons: this.isDetail
          ? [
            { text: '下载发票', icon: 'el-icon-download', enabledType: '-1', click: this.fileDownload },
            { text: '预览文件', icon: 'el-icon-right', enabledType: '-1', click: this.fileView }
          ]
          : [
            (this.isEnableEinvoiceType
              ? { text: '上传发票', icon: 'el-icon-upload2', enabledType: '0', click: bt => (this.uploadEinvoice(bt)) }
              : {
                text: '上传发票', icon: 'el-icon-upload2', enabledType: '0', type: 'primary',
                upload: this.fileUpload, fileLimit: 10, accept: '.pdf,.ofd'
              }),
            { text: '发票录入', icon: 'el-icon-document-add', enabledType: '0', click: this.addInvoice },
            { text: '下载', icon: 'el-icon-download', enabledType: '1+', click: this.fileDownload },
            { text: '发票修改', icon: 'el-icon-edit', enabledType: '1', click: this.editInvoice },
            {
              text: '预览文件', icon: 'el-icon-right', enabledType: '-1', click: this.fileView
            },
            {
              text: '从发票夹上传', icon: 'el-icon-upload2', enabledType: '0', click: this.refTableEinvoice
            }
          ],
        btAfters: { '删除': '发票录入' }, // 表明“删除”按钮放在“发票录入”后面
        hideCurdButton: this.isDetail ? ['新增', '修改', '详情', '删除'] : ['新增', '修改', '详情'],
        // hiddenButtons: this.isEnableEinvoiceFolder ? [] : ['从发票夹上传'],
        tableTempData: this.tableTempData,
        btDeleteClick: { click: bt => (this.btDeleteClick(bt)) },
        reloadTableCallback: result => {
          const dataMap = {}
          result.data.rows.forEach(row => {
            dataMap[row.id] = row
          })
          this.rowsData = result.data.rows
          this.attList = this.rowsData
          this.fileExtInfos = {} // 发票额外设置初始值
          this.attList.forEach(item => {
            this.fileIds.push(item.attId)
            // 发票预览时需组装发票额外显示的数据
            const ext = item.attName ? item.attName.substring(item.attName.lastIndexOf('.') + 1) : ''
            // 签章信息不为空时才组装数据 暂时只支持pdf
            if (this.$isNotEmpty(item.signatureInfo) && (ext === 'pdf' || ext === 'PDF')) {
              this.fileExtInfos[item.attId] = [item.signatureInfo]
            }
          })
          // 处理电子签章校验不正确的样式
          if (this.$isNotEmpty(dataMap)) {
            const rows = this.$refs.curdList.getBlistRowData()
            const this_ = this
            // const $rows = $('#baseInvoice').find('tr[rowid]')
            $.each(rows, function(i, row) {
              const rowId = this_.$getRowId(row)
              const rowData = dataMap[rowId]
              if (this_.$isNotEmpty(rowData)) {
                const ext = rowData.attName ? rowData.attName.substring(rowData.attName.lastIndexOf('.') + 1) : ''
                const signatureInfo = rowData.signatureInfo
                if (signatureInfo === '发票电子签章验证不通过！' && (ext === 'pdf' || ext === 'PDF')) {
                  // $(row).attr('style', 'color: rgb(255, 43, 43)')
                  // $(row).find('a').eq(0).siblings().remove() // 先删除同级的元素 避免重复添加
                  // const signatureInfoDiv = '<div style="color: #ff5c00;font-size: 8px;">' + signatureInfo + '</div>'
                  // $(row).find('a').eq(0).after(signatureInfoDiv)
                  this_.$refs.curdList.changeRowTar('errList', rowData)
                  this_.$refs.curdList.changeRowTar('errTip', rowData, false, signatureInfo)
                }
              }
            })
          }
        },
        callbackSuccessDelete: (result, ids) => {
          this.callbackSuccessDelete(result, ids)
        },
        callbackTableTempDataDelete: (result, ids) => {
          this.callbackTableTempDataDelete(result, ids)
        },
        rowCheckedCallback: (rows) => {
          this.rowCheckedCallback(rows)
        },
        getSummaries(param, $table) {
          const { columns, data } = param
          const sums = []
          columns.forEach((column, index) => {
            if (index === 1) {
              sums[index] = '合计'
              return
            }
            if (column.label === '发票金额' || column.label === '税金' ||
              column.label === '不含税金额' || column.label === '本次报销金额') {
              const values = data.map(item => Number(item[column.property]))
              if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                  const value = Number(curr)
                  if (!isNaN(value)) {
                    return prev + curr
                  } else {
                    return prev
                  }
                }, 0)
                sums[index] = this_.$formatMoney(sums[index])
              } else {
                sums[index] = ''
              }
            } else {
              sums[index] = ''
            }
          })
          return sums
        }
      }
      this.$refs.curdList.init(Object.assign(initParams, exParams))
      const this_ = this
      setTimeout(function() {
        const tds = $('#baseInvoice').find('.el-table__footer-wrapper tr>td')
        if (this_.$isNotEmpty(tds) && tds.length > 0) {
          tds[0].colSpan = 5
          tds[0].style.textAlign = 'right'
          tds[1].style.display = 'none'
          tds[2].style.display = 'none'
          tds[3].style.display = 'none'
          tds[4].style.display = 'none'
        }
      }, 1000)
    },
    refTableEinvoice() {
      // 发票夹参照中过滤掉已存在的列表
      if (this.attList) {
        this.attList.forEach(item => {
          if (this.einvoiceAttIds.indexOf(item.id) === -1) {
            this.einvoiceAttIds.push(item.id)
          }
        })
      }
      const refData = {
        colType: '弹框',
        dataRef: '从发票夹上传'
      }
      this.$refData(undefined, refData,
        () => {
        },
        () => {
        },
        selectedData => {
          this.einvoiceAttIds = []
          selectedData.list.forEach(item => {
            if (this.einvoiceAttIds.indexOf(item.attachmentId) === -1) {
              this.einvoiceAttIds.push(item.attachmentId)
            }
            if (this.fileIds.indexOf(item.attData.attId) === -1) {
              item.attData.expenseMoney = item.canExpenseMoney
              this.rowsData.push(item.attData)
              this.fkGuids.push(item.attData.fkGuid)
            }
          })
          this.attList = this.rowsData
          this.attList.forEach(item => {
            this.fileIds.push(item.attId)
            if (this.einvoiceAttIds.indexOf(item.id) === -1) {
              this.einvoiceAttIds.push(item.id)
            }
          })
        }, selectedData => {
          this.savePayee(selectedData)
        }, { CAN_EXPENSE_MONEY_gt: '0.00', einvoiceAttIds: this.einvoiceAttIds.join(',') })
    },
    // 根据发票信息生成收款人
    savePayee(selectedData) {
      if (selectedData && selectedData.list) {
        const attIds = selectedData.list.map(item => item.attachmentId).toString()
        const data = { 'attIds': attIds, 'metaId': this.dataVo.meta.bizid }
        this.$callApiParams('savePayeeListFromAttach', data, result => {
          window.$event.$emit('addPayees', result)
          return true
        })
      }
    },
    btDeleteClick(bt) {
      const rowIds = bt.getRowIds()
      const tempFkGuids = []
      for (let i = this.rowsData.length - 1; i >= 0; i--) {
        const rowData = this.rowsData[i]
        if (rowData && rowIds.indexOf(rowData.id) > -1) {
          this.rowsData.splice(i, 1)
          if (tempFkGuids.indexOf(rowData.fkGuid) === -1) {
            tempFkGuids.push(rowData.fkGuid)
          }
        }
      }
      this.$removeTargetArr(this.fkGuids, tempFkGuids)
      // 处理智能填报上传发票后删除，再上传会查询显示全部发票
      if (this.$isNotEmpty(this.bizDataId) && this.fkGuids.indexOf(this.bizDataId) === -1) {
        this.fkGuids.push(this.bizDataId)
      }
      if (this.$isNotEmpty(this.attTempId) && this.fkGuids.indexOf(this.attTempId) === -1) {
        this.fkGuids.push(this.attTempId)
      }
      this.delIds = this.delIds.concat(this.delIds, rowIds)
      this.fileIds = this.fileIds.filter(item => {
        if (this.delIds.indexOf(item) === -1) {
          return item
        }
      })
      // 临时的发票夹id参数
      const tempEinvoiceAttIds = this.$clone(this.einvoiceAttIds)
      // 发票夹附件里的已删除的id需要移除
      this.$removeTargetArr(this.einvoiceAttIds, this.delIds)
      // 从发票夹上传的发票需要从delIds 移除
      this.$removeTargetArr(this.delIds, tempEinvoiceAttIds)
      this.attList = this.rowsData
      const files = this.commonBizAttaVo.attList || []
      for (let i = files.length - 1; i >= 0; i--) {
        if (this.delIds.includes(files[i].id)) {
          files.splice(i, 1)
        }
      }
    },
    rowCheckedCallback(rows) {
      this.fileIds = []
      if (this.$isNotEmpty(rows)) {
        rows.forEach(item => {
          this.fileIds.push(item.attId)
        })
      } else {
        this.attList.forEach(item => {
          this.fileIds.push(item.attId)
        })
        this.tableTempData.forEach(item => {
          this.fileIds.push(item.attId)
        })
      }
    },
    callbackTableTempDataDelete(result, ids) {
      if (this.$isNotEmpty(ids)) {
        this.delIds = this.delIds.concat(this.delIds, ids)
        this.fileIds = this.fileIds.filter(item => {
          if (this.delIds.indexOf(item) === -1) {
            return item
          }
        })
      }
    },
    callbackSuccessDelete(result, ids) {
      if (result.success) {
        this.delIds = this.delIds.concat(this.delIds, ids)
        this.fileIds = this.fileIds.filter(item => {
          if (this.delIds.indexOf(item) === -1) {
            return item
          }
        })
      }
    },
    reload() {
      this.isaddinvoicedialog = false
      this.iseditinvoicedialog = false
      this.init()
    },
    editFiledForm(filedForm) {
      const index = this.tableTempData.findIndex((item) => item.id === filedForm.id)
      this.tableTempData[index] = filedForm
      this.reload()
    },
    initFileList() {
      this.isaddinvoicedialog = false
      this.reload()
    },
    fileView() {
      this.$refs.fileView.open({ fileIds: this.fileIds, bizDataId: null, fileExtInfos: this.fileExtInfos })
    },
    uploadEinvoice(bt) {
      this.$refs.addEinvoicedialog.selectAttTypeList()
      this.$refs.addEinvoicedialog.bizDataId = this.bizDataId
      this.$refs.addEinvoicedialog.attTempId = this.attTempId
      this.$refs.addEinvoicedialog.delIds = this.delIds
      this.$refs.addEinvoicedialog.source = this.source
      this.isaddeinvoicedialog = true
    },
    fileUpload(result, file, fileList, uploadRef) {
      // 清理缓存文件
      uploadRef.forEach(upload => {
        upload.clearFiles()
      })
      // 获取上传文件大小
      const isGt100M = Number(file.size / 1024 / 1024) > 100
      if (isGt100M) {
        this.$msgbox({
          title: '',
          message: '文件大小不能超过100MB，请压缩后重新上传！',
          type: 'warning'
        })
        return
      }
      this.fileList = this.fileList.concat(fileList)
      if (fileList) {
        const formData = new FormData()
        fileList.map((item) => {
          // 判断附件是否已上传 没上传才上传
          if (item.raw) {
            formData.append('files', item.raw, item.name)
            formData.append('id', item.uid)
            formData.append('path', '')// 文件存储路径
            formData.append('subId', '')// 业务系统编码
            formData.append('typeCode', '')// 类型编码
            formData.append('bizCode', '')// 模块编码
            formData.append('bizCodeName', '')// 模块名称
            formData.append('bizTblName', this.bizTblName)// 业务表名称
            formData.append('bizId', this.bizDataId)// 业务记录编码,业务表主键ID
            formData.append('isEsSearch', 'true')// 是否全文检索
            formData.append('fileComment', '')// 附件描述
            formData.append('attType', '1')// 附件类别
            formData.append('appendixType', '')// 附件类型
            formData.append('appendixTypeCode', '')// 附件类型编码
            formData.append('source', this.source)// 来源
            formData.append('attTempId', this.attTempId)// 临时id
            formData.append('delIds', this.delIds.join(','))// 已删除发票id
          }
        })
        if (formData.has('files')) {
          this.$refs.curdList.$refs.baseList.loading = true
          this.$refs.curdList.setBtProperty('上传发票', 'disabled', true)
          this.$refs.curdList.setBtProperty('发票录入', 'disabled', true)
          this.$refs.curdList.setBtProperty('从发票夹上传', 'disabled', true)
          this.$callApi(this.isEnableEinvoiceType ? 'uploadEinvoice' : 'parseInvoice', formData, result => {
            if (result.success) {
              this.$message.success('上传成功')
              if (this.$isNotEmpty(result.data)) {
                this.initFileList(result.data)
              }
            }
            this.$refs.curdList.setBtProperty('上传发票', 'disabled', false)
            this.$refs.curdList.setBtProperty('发票录入', 'disabled', false)
            this.$refs.curdList.setBtProperty('从发票夹上传', 'disabled', false)
            this.$refs.curdList.$refs.baseList.loading = false
            return true
          }, () => {
            this.$refs.curdList.setBtProperty('上传发票', 'disabled', false)
            this.$refs.curdList.setBtProperty('发票录入', 'disabled', false)
            this.$refs.curdList.setBtProperty('从发票夹上传', 'disabled', false)
            this.$refs.curdList.$refs.baseList.loading = false
          })
        }
      }
    },
    addInvoice(checkedRows) {
      this.$refs.addinvoicedialog.filedForm = {
        fkGuid: this.attTempId,
        attName: '',
        invoiceType: '',
        invoiceCode: '',
        invoiceNumber: '',
        invoiceMoney: '',
        remark: '',
        invoiceDate: null,
        checkCode: '',
        capitalMoney: '',
        excludingTaxAmount: '',
        draweePartyName: '',
        draweePartyTariff: '',
        draweePartyPhone: '',
        draweePartyAccount: '',
        sellerName: '',
        sellerTariff: '',
        sellerPhone: '',
        sellerAccount: '',
        uploadTime: null,
        caption: '',
        passwordBlock: '',
        goodsServiceName: '',
        model: '',
        unitName: '',
        number: '',
        taxes: '',
        invoiceMoneyUppercase: '',
        isPermit: ''
      }
      this.$refs.addinvoicedialog.isEnableEinvoiceType = true
      this.$refs.addinvoicedialog.bizTblName = this.bizTblName
      this.$refs.addinvoicedialog.bizDataId = this.attTempId
      this.$refs.addinvoicedialog.attList = this.attList
      this.$refs.addinvoicedialog.selectAttTypeList()
      this.isaddinvoicedialog = true
    },
    editInvoice(obj) {
      this.$refs.editinvoicedialog.filedForm = obj.params.rows[0]
      if (obj.params.rows[0].isPermit) {
        this.$refs.editinvoicedialog.filedForm.isPermit = JSON.parse(obj.params.rows[0].isPermit)
      }
      if (obj.params.rows[0].invoiceMoney) {
        this.$refs.editinvoicedialog.filedForm.invoiceMoneyUppercase = this.$digitUppercase(obj.params.rows[0].invoiceMoney)
      }
      this.$refs.editinvoicedialog.selectAttTypeList()
      this.iseditinvoicedialog = true
    },
    fileDownload(obj) {
      let checkedRows = obj.params.rows
      if (this.$isEmpty(checkedRows)) {
        checkedRows = this.rowsData
      }
      // 批量下载
      const attachIdArr = Array.from(checkedRows, ({ attId }) => attId)
      const attachIds = attachIdArr.join(',')
      if (checkedRows.length === 1) {
        downloadFile(attachIds).then((res) => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件信息不存在')
          }
        })
      } else {
        batchDownload({ attachIds: attachIds, fileName: this.batchDownloadName }).then(res => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件不存在！')
          }
        }).catch(err => {
          console.log(err)
          this.$message({
            type: 'error',
            message: '文件不存在！'
          })
        })
      }
    },
    handleFileDownloadRes(res, str1) {
      if (!res.data) {
        this.$message.error('文件信息不存在')
        return
      }
      const filename = str1 || undefined
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // 检测是否在IE浏览器打开
        window.navigator.msSaveOrOpenBlob(new Blob([res.data]), filename)
      } else {
        // 谷歌、火狐浏览器
        let url = ''
        if (
          window.navigator.userAgent.indexOf('Chrome') >= 1 ||
          window.navigator.userAgent.indexOf('Safari') >= 1
        ) {
          url = window.webkitURL.createObjectURL(new Blob([res.data]))
        } else {
          url = window.URL.createObjectURL(new Blob([res.data]))
        }
        const link = document.createElement('a')
        const iconv = require('iconv-lite')
        iconv.skipDecodeWarning = true // 忽略警告
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', filename)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      }
    }
  }

}
</script>

<style lang="scss" scoped>
/deep/ .el-tabs__content {
  height: 100%;
}
</style>
<style lang="scss">
#baseInvoice {
  .single-main {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
