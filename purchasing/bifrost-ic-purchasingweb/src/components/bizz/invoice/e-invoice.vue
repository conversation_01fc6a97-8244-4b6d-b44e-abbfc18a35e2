<template>
  <div class="e-invoice" id="e-invoice" ref="eInvoice">
    <baseinvoice ref="baseinvoice"></baseinvoice>
  </div>
</template>

<script>
export default {
  name: 'e-invoice', // 电子发票,
  data() {
    return {}
  },
  methods: {
    getTable() {
      return this.$refs.baseinvoice.getTable()
    },
    initCommonBiz(isEdit, attaVo, exParams) {
      if (this.$isNotEmpty(exParams) && this.$isNotEmpty(exParams.mode)) {
        this.mode = exParams.mode
      }
      this.$refs?.baseinvoice?.initCommonBiz(isEdit, attaVo, exParams)
    },
    initCommonBizDetails(attaVo, exParams) {
      this.$refs?.baseinvoice?.initCommonBizDetails(attaVo, exParams)
    },
    updateBeforeSave() {
      return this.$refs.baseinvoice.updateBeforeSave()
    }
  }
}

</script>

<style lang="scss" scoped>
.e-invoice{height: 100%;}
.e-invoice .baseinvoice{height: 100%;}
.e-invoice /deep/ .el-table{height: 100%;}
</style>
