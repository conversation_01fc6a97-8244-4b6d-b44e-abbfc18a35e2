<template>
  <b-curd ref="curdList"></b-curd>
</template>

<script>
export default {
  name: 'logdetaildialog',
  data() {
    return {
      dlgTitle: '日志详情',
      logId: ''
    }
  },
  mounted() {
    // this.init()
  },
  methods: {
    init(dlg, params) {
      this.logId = params.logId
      this.$setDlgSize(dlg, 'globalDialog', 700, 500)
      this.$nextTick(() => {
        this.$refs.curdList.init({
          params: {
            dataApiKey: 'selectOperLogDetailPageData',
            LOG_ID_eq: this.logId
          },
          hideCurdButton: ['新增', '修改', '详情', '删除']
        })
      })
    },
    reload() {
      this.$reInit(this)
    }
  }
}
</script>

<style scoped>

</style>
