<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-08 17:41:05
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-07-15 11:35:50
-->
<template>
  <el-dialog ref="logdialog"
             :title='dlgTitle'
             append-to-body
             class="globalDialog"
             :close-on-click-modal="false"
             :visible.sync="islogdialog"
             @close="handleClose">
    <b-curd class="operLog" ref="operLog"></b-curd>
  </el-dialog>
</template>

<script>
export default {
name: 'logdialog',
props: {
  showDlg: { type: Function, default: () => {} },
  isShowHtml: { type: Boolean, default: () => true } // 是否显示超链接
},
data() {
  return {
    dlgTitle: '操作日志',
    islogdialog: false,
    dataId: '',
    logdialogloading: false
  }
},
mounted() {
  // this.init()
},
methods: {
  init() {
    this.$refs.operLog.init({
      params: {
        dataApiKey: 'selectOperLogPageData',
        DATA_ID_eq: this.dataId,
        isShowHtml: this.isShowHtml
      },
      showDlg: this.showDlg,
      hideCurdButton: ['新增', '修改', '详情', '删除']
    })
  },
  reload() {
    this.$reInit(this)
  },
  show(isDialog) {
    this.islogdialog = isDialog
    this.$setDlgSize(this, 'logdialog', 1070, 700)
    this.$nextTick(() => {
      this.init()
    })
  },
  handleClose() {
    this.islogdialog = false
  }
}
}
</script>

<style lang="scss" scoped>
.operLog{
  ::v-deep .el-pagination{
    padding-top: 15px;
  }
}
</style>
