<template>
    <page ref="page">
        <template #pageContent>
            <LayoutTem isDbCol :isPageShow="false" :isFilterShow="false">
                <template #dbColLeft >
                    <classify-ztree
                            ref="classifytree"
                            dataType="Nvg"
                            :showNodeType="true"
                            :showSourceType="true"
                            deleteApiKey="deleteClassifyTreeNode"
                            :deleteExtraParams="{actionKey:'DELETE'}"
                            :saveExtraParams="{actionKey: 'SAVE'}"
                            @nodeClick="nodeClick"
                            @leafNodeAdded="leafNodeAdded"/>
                </template>
                <template #main>
                <div style="width: 100%;height: 100%">
                    <div class="tableListSettingInPage tableListSetting mini-table">
                      <!--外边框-->
                      <div class="dbcol-leftsfirst padding">
                        <div class="tableListSettingColSelected">
                          <div style="width: 100%; height: 60%">
                            <div class="colSettingToolbar">
                              <el-input placeholder="查找" prefix-icon="el-icon-search"
                                        v-model="tableListSettingColAllSearchText" class="colSettingFilterInput"
                                        style="margin-right: 0px;width: 170px;"/>
                              <el-row class="colSettingToolbarButtonContainer">
                                <el-button icon="el-icon-circle-plus-outline" title="新增配置" @click="btAddClick" circle/>
                                <el-button icon="el-icon-edit" title="修改配置" @click="btEditClick" circle :disabled="btDeleteDisabled"/>
                                <el-button icon="el-icon-delete" title="删除配置" @click="btDeleteClick"
                                           circle :loading="isDeleting" :disabled="btDeleteDisabled"/>
                              </el-row>
                            </div>
                            <el-table
                              ref="tableListSettingColAll"
                              :data="tableListSettingColAllShows"
                              border
                              @selection-change="tableListSettingColAllChange"
                              @row-click='tableListSettingColAllClick'
                              style="width: 381px;height: 100%;">
                              <el-table-column type="selection" width="25"></el-table-column>
                              <el-table-column label="指引名称" prop="nvgName"></el-table-column>
                              <el-table-column label="序号" width="90" prop="nvgOrder"></el-table-column>
                            </el-table>
                          </div>
                        </div>

                        <div style="height: 200px"></div>
                        <div style="margin: 10px 0;">
                          {{'注意事项：'}}
                        </div>
                        <div>
                          <el-input
                            ref="tableListAttThingAll"
                            type="textarea"
                            :autosize="{ minRows: 6, maxRows: 8}"
                            placeholder="请输入内容"
                            show-word-limit
                            v-model="colItemText.nvgAttDescription"
                            maxlength="1000"
                          >
                          </el-input>
                          <div style="margin: 10px 0;"></div>
                          <el-button class="btn-normal right-btn" type="primary" @click="btTableListSettingSaveCols">保存</el-button>
                        </div>

                      </div>

                      <!--指引配置明细-->
                        <div class="dbcol-lefts padding">
                          <div class="dbcol-lefttwo padding">
                            <!--一行-->
                            <el-row>
                              <el-col :span="17" >
                                <el-form label-position="left" label-width="110px">
                                  <el-form-item label="填单URL1：">
                                    <el-input style = "width: 100%" v-model="colItem.nvgUrlOne"
                                              :disabled="!isTableListSettingColEditing"/>
                                  </el-form-item>
                                  <el-form-item label="填单URL2：">
                                    <el-input style = "width: 100%" v-model="colItem.nvgUrlTwo"
                                              :disabled="!isTableListSettingColEditing"/>
                                  </el-form-item>
                                </el-form>
                              </el-col>

                              <el-col :span="7" >
                                <el-form label-position="right" label-width="80px">
                                  <el-form-item label="按钮名称：">
                                    <el-input style = "width: 100%" v-model="colItem.nvgButtonOne"
                                              :disabled="!isTableListSettingColEditing"/>
                                  </el-form-item>
                                  <el-form-item label="按钮名称：">
                                    <el-input style = "width: 100%" v-model="colItem.nvgButtonTwo"
                                              :disabled="!isTableListSettingColEditing"/>
                                  </el-form-item>
                                </el-form>
                              </el-col>
                            </el-row>

                            <el-row>
                            <el-form label-position="left" label-width="110px">
                              <el-form-item label="序号：">
                                <el-input v-model="colItem.nvgOrder"
                                          :disabled="!isTableListSettingColEditing"/>
                              </el-form-item>
                              <el-form-item label="单位补充信息：">
                                <el-input v-model="colItem.nvgTypeDescription"
                                          :disabled="!isTableListSettingColEditing"
                                          maxlength="200"
                                          show-word-limit/>
                              </el-form-item>

                              <el-col :span="17" >
                              <el-form-item label="图片名称：">
                                <el-input v-model="colItem.nvgPicName"
                                          :disabled="!isTableListSettingColEditing"/>
                              </el-form-item>
                              </el-col>

                              <el-col :span="7" >
                                <el-form label-position="right" label-width="80px" size="mini">
                                  <el-button class= "right-btn" type="primary"
                                             @click="picVisible = true">选择图片</el-button>
                                </el-form>
                              </el-col>
                            </el-form>
                            </el-row>

                          </div>

                          <el-footer style="text-align: right;">
                            <el-button class="btn-normal" > 返回</el-button>
                            <el-button class="btn-normal" type="primary" @click="btTableListSettingSaveCol"> 确定</el-button>
                          </el-footer>
                        </div>

                      <div class="tableListSettingColEdit colSetting">
                    </div>
                    </div>
                </div>

                  <!--指引配置弹框-->
                  <el-dialog
                    width="450px"
                    :title='`${btEditType}配置`'
                    append-to-body
                    :close-on-click-modal="false"
                    :visible.sync="addNodeDlgVisible">
                    <!--弹框列表-->
                    <el-form label-width="100px" size="mini">
                      <el-form-item label="表单类别" prop="formType">
                        <el-select v-model="colItem.formType" @change="formTypeChange" >
                          <el-option
                            v-for="(item, index) in formTypeLists"
                            :key="index"
                            :label="item"
                            :value="item"/>
                        </el-select>
                      </el-form-item>
                      <el-form-item label="选择表单" prop="nvgTypeName">
                        <el-select v-model="colItem.nvgTypeName">
                          <el-option
                            v-for="(item, index) in selectformList"
                            :key="index"
                            :label="item"
                            :value="item"/>
                        </el-select>
                      </el-form-item>
                        <el-form-item label="指引名称" prop="nvgName">
                          <el-input v-model="colItem.nvgName"/>
                        </el-form-item>
                      </el-form>

                    <div slot="footer" class="dialog-footer">
                      <el-button @click="addNodeDlgVisible = false">取消</el-button>
                      <el-button type="primary" v-lockButton
                                 @click="btTableListSettingSaveCol">{{btEditType}}</el-button>
                    </div>
                  </el-dialog>

                  <el-dialog
                    title="可选图标:"
                    :visible.sync="picVisible"
                    width="50%"
                    append-to-body>
                    <div id="inc-apply">
                      <el-card class="box-card" body-style="max-height: 400px;overflow: scroll;overflow-x: hidden;">
                        <span class="box-content">
                          <el-row>
                            <el-col style="text-align: center;height: 134px" :span="4"
                                    @click.native="nvgbtn(item, index)" :class="{ active: isActive == index }"
                                    v-for="(item, index) in contentData" :key="index">
                              <div class="box-row">
                                <img id="nvgImg" :src="require('@/assets/image/' + item.nvgPicName)"
                                     alt="静态图片">
                                <div class="contentboxs">
                                  <div>{{ item.nvgPicName}}</div>
                                </div>
                              </div>
                            </el-col>
                          </el-row>
                        </span>
                      </el-card>
                      <div slot="footer"  style="text-align: right;margin-top:20px" class="dialog-footer">
                         <el-button class="btn-normal" @click="picVisible = false">取 消</el-button>
                         <el-button class="btn-normal" type="primary" @click="btnvgClick()">确 定</el-button>
                      </div>
                    </div>
                  </el-dialog>
            </template>
            </LayoutTem>
        </template>
    </page>
</template>

<script>
export default {
  name: 'nvg',
  data() {
    return {
      textarea: '', // 注意事项
      tableListSettingColAllSearchText: '', // 指引配置列表搜索文本
      tableListSettingColAllSearchText2: '', // 注意事项列表搜索文本
      isDeleting: false, // 是否正在删除配置
      btDeleteDisabled: true, // 删除列按钮是否可用
      isTableListSettingColEditing: false, // 是否正在编辑配置设置
      isTableListSettingColSaving: false, // 是否表格列设置正在保存
      colItem: {}, // 当前编辑的场景类别数据
      colItems: {}, // 当前编辑的注意事项数据
      colItemText: {}, // 注意事项文本
      colItemNvgAttThing: {}, // 注意事项
      columnData: {}, // 列数据
      selectNodeId: '',
      tableListSettingColAll: [], // 指引配置列表
      tableListAttThingAll: [], // 注意事项列表
      formTypeLists: [], // 表单类型
      selectformList: [], // 类别表单下的所有表单
      fileTableData: [], // 表格数据 临时存储
      selectformData: [], // 下拉框数据
      showPublicInfoAmount: false,
      addNodeDlgVisible: false, // 弹框是否显示
      addNodeDlgVisible2: false, // 注意事项弹框
      picVisible: false, // 弹框显示图片
      btEditType: '',
      btEditType2: '',
      contentData: [], // 展示指引配置数据列表
      tempData: {}, // 临时存储图片数据
      isActive: false
    }
  },
  computed: {
    tableListSettingColAllShows() { // 指引配置列表实际显示的数据
      var items = []
      if (this.$isNotEmpty(this.tableListSettingColAllSearchText)) {
        items = this.tableListSettingColAll.filter(item => {
          return String(item.label.toLowerCase())
            .match(this.tableListSettingColAllSearchText.toLowerCase())
        })
      } else {
        items = this.tableListSettingColAll
      }
      const nvgOrderTem = Math.max.apply(Math, items.map(item => { return item.nvgOrder })) + 1
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.nvgOrder = this.$isEmpty(items) ? 0 : nvgOrderTem
      return this.$sortColumnList(items)
    },

    tableListSettingColAllShows2() { // 注意事项实际显示的数据
      var items = []
      if (this.$isNotEmpty(this.tableListSettingColAllSearchText2)) {
        items = this.tableListAttThingAll.filter(item => {
          return String(item.label.toLowerCase())
            .match(this.tableListSettingColAllSearchText2.toLowerCase())
        })
      } else {
        items = this.tableListAttThingAll
      }
      const nvgOrderTem = Math.max.apply(Math, items.map(item => { return item.nvgOrder })) + 1
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.nvgOrder = this.$isEmpty(items) ? 0 : nvgOrderTem
      return this.$sortColumnList(items)
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.page.commonPageClassEx =
        this.$refs.page.commonPageClassEx + ' column-top-hide'
    })

    this.init()
  },
  methods: {
    init() {
      this.$callApiParams('selectNvgPicList', {}, result => {
        if (result.data) {
          this.contentData = result.data
          return true
        }
      })
    },
    nodeClick(data, exData) { // needToReloadFreeJson表明是通过手动点击打开
      exData = exData || {}
      exData.needToReloadFreeJson = true
      this.selectNodeId = data.id
      this.$selectNvgNavigationList(this.selectNodeId,
        result => {
          this.tableListSettingColAll = result.data
          this.isTableListSettingColSaving = false
        }, result => { this.isTableListSettingColSaving = false })

      this.$selectNvgAttentionThingList(this.selectNodeId,
        result => {
          if (result.data.length) {
            this.$set(this.colItemText, 'nvgAttDescription', result.data[0].nvgAttDescription)
          } else {
            this.$set(this.colItemText, 'nvgAttDescription', '')
          }
          // this.colItemText.nvgAttDescription =  result.data[0].nvgAttDescription
          this.tableListAttThingAll = result.data
          this.isTableListSettingColSaving = false
        }, result => { this.isTableListSettingColSaving = false })

      this.selectFormTypeByLabel(data)
      this.showPublicInfoAmount = false
    },
    leafNodeAdded(nodeId, itemKey) { // 新增叶子节点后，将其设置为当前节点
      this.setCurrentItem(nodeId, itemKey)
    },
    setCurrentItem(nodeId, itemKey) { // 设置当前对象
      this.currentId = (itemKey == null) ? '' : itemKey
      this.$refs.classifytree.setCurrentItem(nodeId, itemKey)
    },

    btAddClick() {
      if (this.$isEmpty(this.selectNodeId)) {
        this.$message.error('请先选择分类节点！')
        return
      }
      this.btEditType = '新增'
      this.addNodeDlgVisible = true
      // this.isTableListSettingColEditing = true

      // 假数据
      this.colItem = {
        nvgPicName: 'image4.png',
        nvgTypeName: '',
        nvgTypeDescription: '', // 单位补充信息
        // nvgUrl: 'http://127.0.0.1:9888/#/micro-route/bifrost-ic/bizz/nvg',
        nvgUrlOne: '', // URL路径1
        nvgUrlTwo: '', // URL路径2
        nvgUrlThree: '', // URL路径3
        nvgOrder: this.nvgOrder, // 序号
        nvgSceneId: this.selectNodeId,
        formType: '', // 表单类别
        nvgName: '', // 指引名称
        nvgButtonOne: '', // 按钮名称1
        nvgButtonTwo: '' // 按钮名称2
      }
      this.$refs.tableListSettingColAll.clearSelection()
    },

    btEditClick(checkedRows) {
      this.btEditType = '修改'
      this.addNodeDlgVisible = true
    },

    btEditClick2(checkedRows) {
      this.btEditType2 = '修改'
      this.addNodeDlgVisible2 = true
    },

    btDeleteClick() { // 删除表单配置类型
      this.deleteColItems(this.$refs.tableListSettingColAll)
    },

    btDeleteClick2() { // 删除注意事项
      this.deleteColItems2(this.$refs.tableListAttThingAll)
    },

    // 表单类型数据回显
    tableListSettingColAllChange(checkedRows) {
      this.btDeleteDisabled = true
      this.isTableListSettingColEditing = false
      this.colItem = {}

      if (checkedRows.length === 1) {
        this.btDeleteDisabled = false
        this.colItem = checkedRows[0]
        this.isTableListSettingColEditing = true
      }

      if (checkedRows.length > 1) {
        this.$refs.tableListSettingColAll.clearSelection()
        this.$refs.tableListSettingColAll.toggleRowSelection(checkedRows.pop())
      } else {
        this.multipleSelection = checkedRows
      }
    },

    // 注意事项数据回显
    tableListSettingColAllChange2(checkedRows) {
      this.btDeleteDisabled = true
      this.isTableListSettingColEditing = false
      this.colItems = {}

      if (checkedRows.length === 1) {
        this.btDeleteDisabled = false
        this.colItems = checkedRows[0]
        this.isTableListSettingColEditing = true
      }

      if (checkedRows.length > 1) {
        this.$refs.tableListAttThingAll.clearSelection()
        this.$refs.tableListAttThingAll.toggleRowSelection(checkedRows.pop())
      } else {
        this.multipleSelection = checkedRows
      }
    },

    tableListSettingColAllClick(row) {
      this.$refs.tableListSettingColAll.toggleRowSelection(row)
    },
    deleteColItems($table) { // 删除表单名称
      this.isDeleting = true
      this.$deleteNvgNavigation(
        $table,
        result => {
          this.selectNvgNavigationList(re => {
            this.isDeleting = false
            this.$selectNvgNavigationList(this.selectNodeId,
              result => {
                this.tableListSettingColAll = result.data
                this.isTableListSettingColSaving = false
              }, result => { this.isTableListSettingColSaving = false })
          })
        }
      )
    },

    // 注意事项
    tableListSettingColAllClick2(row) {
      this.$refs.tableListAttThingAll.toggleRowSelection(row)
    },
    tableListSettingColAllDblclick2(row, column, event) { // 双击表格列表行，则删除该注意事项
      var $table = { selection: [{ id: row.id }] } // 构造模拟的table
      this.deleteColItems2($table)
    },
    deleteColItems2($table) { // 删除注意事项
      this.isDeleting = true
      this.$deleteNvgAttentionThing(
        $table,
        result => {
          this.selectNvgAttentionThingList(re => {
            this.isDeleting = false
            this.$selectNvgAttentionThingList(this.selectNodeId,
              result => {
                this.tableListAttThingAll = result.data
                this.isTableListSettingColSaving = false
              }, result => { this.isTableListSettingColSaving = false })
          })
        }
      )
    },

    selectNvgNavigationList(callback) { // 刷新表格列列表
      this.tableListSettingColAllChange([]) // 清除原先编辑数据
      this.$selectNvgNavigationList(this.getCurrentDataType(),
        result => {
          this.tableListSettingColAll = result.data
          this.isTableListSettingColSaving = false
          if (callback) {
            callback(result)
          }
        }, result => { this.isTableListSettingColSaving = false })
    },

    selectNvgAttentionThingList(callback) { // 刷新注意事项列表
      this.tableListSettingColAllChange([]) // 清除原先编辑数据
      this.$selectNvgAttentionThingList(this.getCurrentDataType(),
        result => {
          this.tableListAttThingAll = result.data
          this.isTableListSettingColSaving = false
          if (callback) {
            callback(result)
          }
        }, result => { this.isTableListSettingColSaving = false })
    },

    getCurrentDataType() {
      return '表格列:' + this.tableListSettingCurrentFormType
    },

    // 新增注意事项
    btAddClick2() {
      this.btEditType2 = '新增'
      this.addNodeDlgVisible2 = true
      this.colItems = {
        nvgAttDescription: '',
        nvgSceneId: this.selectNodeId
      }
    },

    // 新增注意事项
    btTableListSettingSaveCols(returnValue, callback) {
      if (this.$isEmpty(this.selectNodeId)) {
        this.$message.error('请先选择分类节点！')
        return
      }
      this.colItems = {
        nvgAttDescription: this.colItemText.nvgAttDescription,
        nvgSceneId: this.selectNodeId
      }
      this.saveCols2(this.colItems, returnValue, callback)
    },
    saveCols2(cols2, returnValue, callback) {
      this.isTableListSettingColSaving = true
      this.$saveNvgAttentionThing(cols2, resut => {
        this.selectNvgAttentionThingList(rt => {
          this.isTableListSettingColEditing = false
          if (typeof (callback) === 'function') {
            callback(rt)
          }
          this.$selectNvgAttentionThingList(this.selectNodeId,
            result => {
              this.tableListAttThingAll = result.data
              this.isTableListSettingColSaving = false
            }, result => { this.isTableListSettingColSaving = false })
        })
        this.addNodeDlgVisible2 = false
        return returnValue
      }, result => { this.isTableListSettingColSaving = false })
    },

    // 新增指引配置
    btTableListSettingSaveCol(returnValue, callback) {
      this.saveCols(this.colItem, returnValue, callback)
    },

    saveCols(cols, returnValue, callback) {
      this.isTableListSettingColSaving = true
      this.$saveNvgNavigation(cols, resut => {
        this.selectNvgNavigationList(rt => {
          this.isTableListSettingColEditing = false
          if (typeof (callback) === 'function') {
            callback(rt)
          }
          this.$selectNvgNavigationList(this.selectNodeId,
            result => {
              this.tableListSettingColAll = result.data
              this.isTableListSettingColSaving = false
            }, result => { this.isTableListSettingColSaving = false })
        })
        this.addNodeDlgVisible = false
        return returnValue
      }, result => { this.isTableListSettingColSaving = false })
    },

    // 回调，根据表单类型查询该类型下所有表单
    formTypeChange(val) {
      if (val) {
        this.$callApiParams('selectFormTypeVo', { 'FORM_TYPE_eq': val }, result => {
          if (result.data) {
            this.selectformData = result.data.dropdownMenus || ''
            this.selectformList = []
            for (let i = 0; i < this.selectformData.length; i++) {
              this.selectformList.push(this.selectformData[i].text)
            }
            this.colItem.nvgTypeName = this.selectformList[0]
            return true
          }
        })
      }
    },

    // 根据下拉树配置的表单类别查询出指引配置的表单类别，用于配置时添加
    selectFormTypeByLabel(val) {
      const params = {}
      this.$callApiParams('selectFormTypeDataList', params, result => {
        if (result.data) {
          this.fileTableData = result.data.rows || ''
          this.formTypeLists = []
          for (let i = 0; i < this.fileTableData.length; i++) {
            const name = val.label.split('：')[0]
            if (this.fileTableData[i].name === name) {
              this.formTypeLists.push(this.fileTableData[i].name)
            }
          }
          return true
        }
      })
    },
    nvgbtn(item, index) {
      this.isActive = index
      this.tempData = item
    },
    btnvgClick() {
      this.picVisible = false
      this.colItem.nvgPicName = this.tempData.nvgPicName
    }
  }
}
</script>

<style lang="scss" scoped>
  $border-color: #dcdfe6;
    .click-el-card-border{
        border: 1px solid #5C96BC !important;
    }
    .tableListSettingColAll, .tableListSettingColSelected { margin-right: 10px;}
    .tableListSettingColEditButton { margin-bottom: 7px; }
    .tableListSetting { display: flex; }
    .tableListSettingColEditForm {
        border: 1px solid #DDDDDD;
        height: calc(100% - 39px);
        width: 290px;
        padding: 10px;
        position: relative;
    }
    .common-page /deep/ .tableListSettingInPage { height: 100%; }
    .common-page /deep/ .tableListSettingInPage .tableListSettingColSelected .el-table { height: calc(100% - 37px) !important; }
    .common-page /deep/ .tableListSettingInPage .el-icon-search { display: none; }
    thead .el-table-column--selection .cell {
        display: none!important;
    }

    .right-btn {
      float: right;    // 靠右
      height: 100%;
    }

  .dbcol-leftsfirst {
    // margin: 0px 0 0px 10px;
    width: 400px;
    // min-width: 250px;
    /*height: 50%;*/
    box-sizing: border-box;
    background: #fff;
    border: 1px solid $border-color;
    overflow: auto;
    transition: width 0.1s ease;
    .el-input {
      margin-bottom: 5px;
    }
    &.padding {
      padding: 10px 5px;
    }
    &.collapsed {
      transition: width 0.5s ease;
      width: 20px !important;
    }
  }

    .dbcol-lefts {
      margin: 0px 0 0px 20px;
      width: 600px;
      // min-width: 250px;
      // height: 100%;
      box-sizing: border-box;
      background: #fff;
      border: 1px solid $border-color;
      overflow: auto;
      transition: width 0.1s ease;
      .el-input {
        margin-bottom: 5px;
      }
      &.padding {
        padding: 10px 5px;
      }
      &.collapsed {
        transition: width 0.5s ease;
        width: 20px !important;
      }
    }

  .dbcol-lefttwo {
    margin: 5px 0 5px 10px;
    width: 570px;
    // min-width: 250px;
    // height: 100%;
    box-sizing: border-box;
    background: #fff;
    border: 1px solid $border-color;
    overflow: auto;
    transition: width 0.1s ease;
    .el-input {
      margin-bottom: 5px;
    }
    &.padding {
      padding: 10px 5px;
    }
    &.collapsed {
      transition: width 0.5s ease;
      width: 20px !important;
    }
  }

  #inc-apply{
    .box-card{
      ::v-deep .el-card__body{
        border: none !important;
        height: 100%;
      }
      .box-content{
        height: 100%;
        .box-row{
          vertical-align: middle;
          /*display: flex;*/
          .el-icon-money{
            font-size: 40px;
          }
          .contentboxs{
            padding: 0 0 0 10px;
            font-weight: bold;
            font-size: 12px;
            width: 100%;
            .btnbox{
              padding: 5px;
            }
            .remark{
              color: #5c6663;
              width: 80%;
              font-size: 14px;
              font-weight: normal;
              word-wrap: break-word;
              overflow-wrap: break-word;
            }
            div{
              margin:  10px 0 10px 0;
            }
          }
          #nvgImg{
            height: 96px;
            border-radius: 5%;
          }
        }
      }
    }
    .wordProcessing{
      // 处理文字超出
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .textbold{
      font-weight: bold;
      font-size: 17px;
    }
    .textnormal{
      font-weight: normal;
      font-size: 15px;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
    .textboldtitle{
      font-weight: bold;
      font-size: 20px;
    }
  }

  .active{
    border: 1px solid;
    border-radius: 5%;
  }

</style>
