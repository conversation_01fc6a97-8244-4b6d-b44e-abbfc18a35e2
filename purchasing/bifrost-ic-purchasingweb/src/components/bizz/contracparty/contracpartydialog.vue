<template>
  <div id="contracpartydialog">
    <el-dialog
      append-to-body
      :title="'合约方'"
      :visible.sync="isdialog"
      width="960px"
      :close-on-click-modal='false'
      @close="handleClose"
      @closed='handleCloseed'
    >
      <page>
        <template #pageContent>
          <el-form
            ref="contracForm"
            :model="contracForm"
            label-width="150px"
            :disabled="contracdetails"
            :rules="rules"
            style="border: 1px solid #ccc;padding: 20px 20px 0 0;">
            <el-row class="row-marginBottom">
              <el-col :span="12">
                <el-form-item label="合约方编码">
                  <el-input v-model="contracForm.code" placeholder="系统自动生成" :disabled="true"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="法人类型" prop="legalPersonType">
                  <el-select v-model="contracForm.legalPersonType" @change="lptChange" filterable>
                    <el-option
                      v-for="item in legalPersonTypeList"
                      :key="item.eleName"
                      :label="item.eleName"
                      :value="item.eleName">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom">
              <el-col :span="12">
                <el-form-item label="合约方状态" prop="status">
                  <el-radio-group v-model="contracForm.status">
                    <el-radio label="停用">停用</el-radio>
                    <el-radio label="启用">启用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="合约方类别" prop="category">
                  <el-select v-model="contracForm.category" filterable>
                    <el-option
                      v-for="item in contracFormCategoryList"
                      :key="item.eleName"
                      :label="item.eleName"
                      :value="item.eleName">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom">
              <el-col :span="12">
                <el-form-item label="合约方等级" prop="level">
                  <el-select v-model="contracForm.level" filterable>
                    <el-option
                      v-for="item in contracFormLevelList"
                      :key="item.eleName"
                      :label="item.eleName"
                      :value="item.eleName">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否预选" prop="isPreselection">
                  <el-radio-group v-model="contracForm.isPreselection">
                    <el-radio label="是">是</el-radio>
                    <el-radio label="否">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom" v-if="!isNaturalPerson">
              <el-col :span="12">
                <el-form-item label="合约方名称" prop="contracpartyName">
                  <el-input v-model="contracForm.contracpartyName" placeholder="请输入合约方名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
                  <el-input v-model="contracForm.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom" v-if="!isNaturalPerson">
              <el-col :span="12">
                <el-form-item label="法人代表" prop="corporation">
                  <el-input v-model="contracForm.corporation" placeholder="请输入法人代表"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="法人代表联系电话" prop="corporationTel">
                  <el-input v-model="contracForm.corporationTel" placeholder="请输入法人代表联系电话"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom" v-if="!isNaturalPerson">
              <el-col :span="12">
                <el-form-item label="地址" prop="address">
                  <el-input v-model="contracForm.address" placeholder="请输入地址"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否中小企业" prop="isMinorEnterprises">
                  <el-radio-group v-model="contracForm.isMinorEnterprises">
                    <el-radio label="是">是</el-radio>
                    <el-radio label="否">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom" v-if="!isNaturalPerson">
              <el-col :span="12">
                <el-form-item label="联系人">
                  <el-input v-model="contracForm.contacts" placeholder="请输入联系人"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人电话">
                  <el-input v-model="contracForm.contactsTel" placeholder="请输入联系人电话"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom" v-if="isNaturalPerson">
              <el-col :span="12">
                <el-form-item label="姓名" prop="contracpartyName">
                  <el-input v-model="contracForm.contracpartyName" placeholder="请输入姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="身份证号码" prop="identityCard">
                  <el-input v-model="contracForm.identityCard" placeholder="请输入身份证号码"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom">
              <el-col :span="12" v-if="!isNaturalPerson">
                <el-form-item label="合约方类型" prop="type">
                  <el-select v-model="contracForm.type" filterable>
                    <el-option
                      v-for="item in contracFormTypeList"
                      :key="item.eleName"
                      :label="item.eleName"
                      :value="item.eleName">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="row-marginBottom">
              <el-col :span="24">
                <el-form-item label="备注">
                  <el-input type="textarea" v-model="contracForm.remark"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="mini-table">
            <ba-payee-list
              ref="baPayeelist"
              :isShowBtn="!contracdetails"
              :isBaseaAplication=true
              :isPayeeDataCanRepeat=false
              :showBatchAssociationIndicators=false
              style="padding: 10px 0px 0px 0px">
            </ba-payee-list>
          </div>

        </template>

      </page>
      <template #footer v-if="!contracdetails">
        <el-button class="btn-normal" @click="handleClose('contracForm')"> 取消</el-button>
        <el-button
          class="btn-normal"
          type="primary"
          :loading="contracpartydialogloading"
          @click="handleSumbit('contracForm')"
        >
          确定
        </el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
export default {
  name: 'contracpartydialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.contracdetails = bool
    }
  },
  mounted() {
    this.selectContracFormType() // 查询合约方类型
    this.selectContracFormLevel() // 查询合约方等级
    this.selectLegalPersonType() // 查询法人类型
    this.selectContracFormCategory() // 查询合约方类别
  },
  data() {
    return {
      isdialog: this.dialog,
      contracdetails: this.isDetails,
      contracForm: {
        bizId: '',
        code: '',
        legalPersonType: '',
        status: '',
        type: '',
        level: '',
        isPreselection: '',
        contracpartyName: '',
        unifiedSocialCreditCode: '',
        corporation: '',
        corporationTel: '',
        address: '',
        isMinorEnterprises: '',
        contacts: '',
        contactsTel: '',
        identityCard: '',
        category: '',
        remark: ''
      },
      contracFormTypeList: [],
      contracFormLevelList: [],
      legalPersonTypeList: [],
      contracFormCategoryList: [],
      isNaturalPerson: false,
      rules: {
        contracpartyName: [
          { required: true, message: '请输入合约方名称/姓名', trigger: 'blur' }
        ],
        corporation: [
          { required: true, message: '请输入法人代表', trigger: 'blur' }
        ]
      },
      contracpartydialogloading: false
    }
  },
  methods: {
    selectContracFormLevel() {
      this.$callApi('getAccSetEle&tableName=ELE_CON_PARTY_LEVEL'
        , {}, result => {
          if (result.success) {
            this.contracFormLevelList = result.data
          }
          return true
        })
    },
    selectContracFormType() {
      this.$callApi('getAccSetEle&tableName=ELE_CON_PARTY_TYPE'
        , {}, result => {
          if (result.success) {
            this.contracFormTypeList = result.data
          }
          return true
        })
    },
    selectContracFormCategory() {
      this.$callApi('getAccSetEle&tableName=ELE_SUPPLY_CATEGORY'
        , {}, result => {
          if (result.success) {
            this.contracFormCategoryList = result.data
          }
          return true
        })
    },
    selectLegalPersonType() {
      this.$callApi('getAccSetEle&tableName=ELE_LEGAL_PERSON_TYPE'
        , {}, result => {
          if (result.success) {
            this.legalPersonTypeList = result.data
          }
          return true
        })
    },
    handleCloseed() {
      this.contracpartydialogloading = false
    },
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(formName) {
      this.contracpartydialogloading = true
      var extData = this.$refs.baPayeelist.extData
      this.contracForm.payeeList = extData.incPayeeData
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$callApi('saveContracparty', this.contracForm, result => {
            if (result.success) {
              this.handleClose()
              this.$parent.reload(result.data)
              this.$message.success('保存成功!')
            }
            this.$emit('flushRowData', result.data)
            return true
          }, resolve => {
            this.contracpartydialogloading = false
          })
        } else {
          this.contracpartydialogloading = false
          return false
        }
      })
    },
    setValue() {
      if (this.contracForm.legalPersonType === '自然人') {
        this.isNaturalPerson = true
      } else {
        this.isNaturalPerson = false
      }

      if (this.$refs.baPayeelist && this.$refs.baPayeelist.extData) {
        this.$refs.baPayeelist.extData.incPayeeData = this.contracForm.payeeList
      }
    },
    resetForm() {
      if (this.$refs['contracForm']) { // 清空form数据
        this.$nextTick(() => {
          this.$refs['contracForm'].clearValidate()
        })
      }
      if (this.$refs.baPayeelist && this.$refs.baPayeelist.extData) { // 清空收款人列表数据
        this.$refs.baPayeelist.extData.incPayeeData = []
      }
    },
    /**
       * 法人类型值改变事件
       */
    lptChange(val) {
      this.$refs['contracForm'].clearValidate()
      if (val === '自然人') {
        if (!this.isNaturalPerson) {
          this.contracForm.contracpartyName = ''
          this.contracForm.identityCard = ''
          this.contracForm.type = ''
        }
        this.isNaturalPerson = true
      } else {
        if (this.isNaturalPerson) {
          this.contracForm.contracpartyName = ''
          this.contracForm.unifiedSocialCreditCode = ''
          this.contracForm.corporation = ''
          this.contracForm.corporationTel = ''
          this.contracForm.isMinorEnterprises = ''
          this.contracForm.contacts = ''
          this.contracForm.contactsTel = ''
        }
        this.isNaturalPerson = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  /deep/ .el-dialog__body {
    /*margin-left: -245px;*/
  }

  /deep/ .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }

  /deep/ .el-table {
    height: 100%;
  }
</style>
<style lang="scss">
  .row-marginBottom {
    margin-bottom: 10px;
  }

  .ba-payee-list {
    height: 250px !important;
  }
</style>
