<template>
  <div>
    <b-curd ref="curdList"></b-curd>
    <contracpartydialog ref="contracpartydialog"
                        :dialog.sync="isDialog"
                        :isDetails='isDetails'
                        @reload="reload">
    </contracpartydialog>
  </div>
</template>
<script>
export default {
  name: 'contracparty',
  data() {
    return {
      isDialog: false,
      isDetails: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const searchForm = [
        '合约方编码:CODE_like:文本',
        '合约方名称:CONTRACPARTY_NAME_like:文本'
      ]
      var initParams = {}
      this.$saveInitParams(this, initParams)
      var param = {
        dataApiKey: 'selectContracpartyPage',
        deleteApiKey: 'deleteContracparty'
      }
      initParams.params = param
      initParams.buttons = [
        { text: '导入', icon: 'el-icon-aliiconshangchuan', enabledType: '0', click: this.showExcelImp }
      ]
      initParams.searchForm = searchForm
      initParams.exportExcelName = '合约方信息'
      initParams.btAddClick = { text: '新增', click: row => (this.showDialog(row, '新增')) }
      initParams.btModifyClick = { text: '修改', click: row => (this.showDialog(row, '修改')) }
      initParams.btDetailClick = { click: row => (this.btDetailsClick(row)) }
      this.$refs.curdList.init(initParams)
    },
    reload() {
      this.$reInit(this)
    },
    showDialog(row, text) {
      if (text === '修改') {
        this.$refs.contracpartydialog.contracForm = JSON.parse(JSON.stringify(row))
      } else {
        this.$refs.contracpartydialog.contracForm = {
          code: '',
          legalPersonType: '法人',
          status: '启用',
          type: '',
          level: '',
          isPreselection: '否',
          contracpartyName: '',
          unifiedSocialCreditCode: '',
          corporation: '',
          corporationTel: '',
          address: '',
          isMinorEnterprises: '是',
          contacts: '',
          contactsTel: '',
          identityCard: '',
          remark: '',
          payeeList: []
        }
        this.$refs.contracpartydialog.resetForm()
      }
      this.isDialog = true
      this.isDetails = false
      this.$nextTick(() => {
        this.$refs.contracpartydialog.setValue()
      })
    },
    btDetailsClick(row) {
      this.$refs.contracpartydialog.contracForm = row
      this.isDialog = true
      this.isDetails = true
      this.$nextTick(() => {
        this.$refs.contracpartydialog.setValue()
      })
    },
    showExcelImp() { // 导入
      var apiKey = 'template/合约方模板.xls'
      var fileName = '合约方模板.xls'
      var tableColumn = []
      this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
        onSuccess: () => {
          this.init()
        } })
    }
  }
}
</script>
