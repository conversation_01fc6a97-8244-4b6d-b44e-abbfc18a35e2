<template>
  <div class="contracparty-list">
    <div class="top-btns" v-if="isShowBtn && !mode">
      <el-button size="small" @click="chooseBtn" plain icon="el-icon-circle-check">选择合约方</el-button>
      <el-button size="small" @click="addBtn" :disabled='isPuCreateCm' plain icon="el-icon-circle-plus-outline">新增合约方</el-button>
      <el-button size="small" @click="updateBtn" :disabled='checkedRow.length !== 1' plain icon="el-icon-edit">修改合约方</el-button>
      <el-button size="small" @click="deleteBtn" :disabled='checkedRow.length === 0 ' plain icon="el-icon-delete">删除</el-button>
    </div>
    <div class="bottom-table">
      <el-table border :data="extData.contracparty" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column v-if="isShowBtn" type="selection" align="center" width="30"></el-table-column>
        <el-table-column align="left" prop="setModeCode" label="合约方" width="110" style="padding:5px 5px">
          <template slot-scope='{row}'>
            <el-select clearable v-model="row.contractParty" v-if="isShowBtn" :disabled="!disabledArr.includes('合约方') && mode" placeholder="请选择" filterable>
              <el-option
                v-for="item in contracFormTypeList"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName">
              </el-option>
            </el-select>
            <span v-else>{{row.contractParty}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="contracpartyName" label="合约方名称" width="350"/>
        <el-table-column align="left" prop="corporation" label="法人代表" width="100"/>
        <el-table-column align="left" prop="corporationTel" label="联系方式"/>
      </el-table>
    </div>
    <contracpartydialog ref="contracpartydialog" :dialog.sync="isDialog" @reload='reload'></contracpartydialog>
  </div>
</template>

<script>
export default {
  name: 'contracparty-list',
  props: {
    isShowBtn: { type: Boolean, default: true },
    disabledArr: { type: Array, default: () => [] }, // 审核模式下显示可修改的配置项
    mode: { type: Boolean, default: false } // 是否是审核模式
  },
  data() {
    return {
      extData: {
        contracparty: []
      },
      checkedRow: [],
      isDialog: false,
      isChange: false,
      contracFormTypeList: [],
      changeItemId: null,
      dataVo: {},
      isPuCreateCm: false // 是否是采购生成合同
    }
  },
  mounted() {
    this.$callApi('getAccSetEle&tableName=ELE_CONTRACT_PARTY'
      , {}, result => {
        if (result.success) {
          this.contracFormTypeList = result.data
        }
        return true
      })
  },
  methods: {
    chooseBtn() {
      const refData = {
        colType: '弹框',
        dataRef: '选择合约方'
      }
      const refParams = { STATUS_eq: '启用' }
      if (this.dataVo?.extData?.puCreateCmId) {
        refParams.采购ID = this.dataVo?.extData?.puCreateCmId
        refParams.不需要查询中标供应商 = ''
      }
      if (this.dataVo?.extData?.contracpartyIds) {
        // 过滤出没选择的合约方
        const ids = this.dataVo.extData.contracpartyIds.filter(item => !this.dataVo.extData.selectContracpartyIds.includes(item))
        if (this.$isNotEmpty(ids)) {
          refParams.BIZID_in = ids.join(',')
        }
      }
      this.$refData(undefined, refData, () => {}, () => {}, selectedData => {
        this.backFill(selectedData)
      }, () => {}, refParams)
    },
    backFill(selectedData) {
      var _this = this
      if (_this.$isEmpty(_this.extData.contracparty)) {
        _this.extData.contracparty = []
      }
      var ids = _this.extData.contracparty.map(item => {
        return item.id
      })
      var formatSelected = []
      selectedData.list.forEach(function(value, index, arr) {
        if (ids.indexOf(value.id) === -1) {
          _this.$set(value, 'contractParty', '乙方')
          formatSelected.push(value)
          // 选择的合约方id
          _this.dataVo?.extData?.selectContracpartyIds?.push(value.id)
        }
      })
      _this.extData.contracparty = _this.extData.contracparty.concat([], formatSelected)
      if (_this.$parent.reloadContracparty) {
        _this.$parent.reloadContracparty(_this.extData.contracparty)
      }
    },
    addBtn() {
      this.$refs.contracpartydialog.contracForm = {
        code: '',
        legalPersonType: '法人',
        status: '启用',
        type: '',
        level: '',
        isPreselection: '否',
        contracpartyName: '',
        unifiedSocialCreditCode: '',
        corporation: '',
        corporationTel: '',
        address: '',
        isMinorEnterprises: '是',
        contacts: '',
        contactsTel: '',
        identityCard: '',
        remark: '',
        payeeList: []
      }
      this.$refs.contracpartydialog.resetForm()
      this.isDialog = true
      this.$nextTick(() => {
        this.$refs.contracpartydialog.setValue()
      })
    },
    updateBtn() {
      this.isDialog = true
      const cloneData = JSON.parse(JSON.stringify(this.checkedRow[0]))
      this.$refs.contracpartydialog.contracForm = cloneData
      this.$nextTick(() => {
        this.$refs.contracpartydialog.setValue()
      })
      this.isChange = true
      this.changeItemId = this.checkedRow[0].id
    },
    deleteBtn() {
      if (this.$parent.fillPlanContracpartyName) {
        this.$parent.fillPlanContracpartyName()
      }
      this.extData.contracparty = this.extData.contracparty.filter(item => this.checkedRow.every(i => item.id !== i.id))
      if (this.dataVo?.extData?.selectContracpartyIds) {
        this.dataVo.extData.selectContracpartyIds =
          this.dataVo.extData.selectContracpartyIds.filter(item => this.checkedRow.every(i => item !== i.id))
      }
      if (this.$parent.reloadContracparty) {
        this.$parent.reloadContracparty(this.extData.contracparty)
      }
    },
    reload(data) {
      if (!this.isChange) {
        if (this.$isEmpty(this.extData.contracparty)) {
          this.extData.contracparty = []
        }
        this.$set(data, 'contractParty', '乙方')
        this.extData.contracparty = this.extData.contracparty.concat([], data)
      } else {
        this.extData.contracparty.forEach(item => {
          if (item.id === this.changeItemId) {
            item = Object.assign(item, data)
          }
        })
      }
      if (this.$parent.reloadContracparty) {
        this.$parent.reloadContracparty(this.extData.contracparty)
      }
      this.isChange = false
    },
    handleSelectionChange(rows) {
      this.checkedRow = rows
    }
  }
}
</script>

<style lang="scss" scoped>
  .contracparty-list{
    padding: 10px 0px 10px 0px;
    /*width: calc(100% - 10px);*/
    // height: 50%;
    display: flex;
    flex-direction: column;
    .total-amt{margin-left: 10px;font-size: 14px;}
    .top-btns{
      padding-bottom: 10px;
      margin-left: 0px;
      width: 27%;
      display: flex;
      .el-input{
        margin-left: 5px;
      }
    }
    .bottom-table{ overflow: hidden;flex: 1;}
    /deep/ .el-table .cell{
      padding: 0px 5px !important
    }
    /deep/ .el-table .warning-row {
      background-color: rgb(255, 43, 43) !important;
    }
    /deep/ .el-table--border .el-table__cell:first-child .cell{
      padding: 0px 5px !important
    }
  }
</style>
