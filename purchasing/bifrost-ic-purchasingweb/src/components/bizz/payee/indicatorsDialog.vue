<!--
 * @Description: 批量关联指标弹窗
 * @version:
 * @Author: zhangshaowu <EMAIL>
 * @Date: 2024-01-31 16:11:36
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-09 17:02:46
-->
<template>
  <el-dialog
    title="批量关联指标"
    :visible.sync="dialogVisible"
    width="350px"
    append-to-body
    :destroy-on-close="true"
  >
    <el-form
      ref="indicatorsForm"
      :model="indicatorsForm"
      label-width="50px">
      <el-row style="padding-top: 20px">
          <el-form-item label="指标" prop="baId">
            <el-select v-model="indicatorsForm.baId" placeholder="请选择">
              <el-option
                v-for="item in data"
                :key="item.baId"
                :label="item.baTitle"
                :value="item.baId">
                <el-popover
                  placement="left-start"
                  width="300"
                  trigger="hover">
                  <p style="margin: 5px 0px 5px 0px">可用金额：{{ item.amountEnable }} 元</p>
                  <p v-if="item.hasEconomicClassification" style="margin: 5px 0px 5px 0px">
                    经济分类：{{ item.economicClassification }}</p>
                  <span slot="reference">{{item.baTitle}}</span>
                </el-popover>
              </el-option>
            </el-select>
          </el-form-item>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'indicatorsDialog',
  props: {
    // 指标数据
    data: {
      type: Array,
      default() {
        return []
      }
    },
    // 选中行
    checkedItem: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      indicatorsForm: {
        baId: ''
      }
    }
  },
  methods: {
    handleClose() {
      // 关闭弹窗清除选项
      this.indicatorsForm.baId = ''
      this.changeDialogVisible(false)
    },
    handleOk() {
      const { baId } = this.indicatorsForm
      const data = {
        ids: [],
        baId
      }
      if (this.$isNotEmpty(this.checkedItem)) {
        this.checkedItem.forEach(item => {
          const { id } = item
          data.ids.push(id)
        })
      }
      this.$emit('setIndicators', data)
      this.indicatorsForm.baId = ''
      this.changeDialogVisible(false)
    },
    changeDialogVisible(visible) {
      this.dialogVisible = visible
    }
  }
}
</script>

<style></style>
