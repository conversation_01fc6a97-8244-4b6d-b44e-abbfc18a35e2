<template>
  <div>
    <b-curd ref="curdList"></b-curd>
    <payeedialog
      ref="payeedialog"
      :dialog.sync="ispayeedialog"
      :request="request"
      :basePageThis="basePageThis"
      :isBasepayee="isBasepayee"
      @setBankCodeList="setBankCodeList"
      @setAccTypeList="setAccTypeList"
      @reload="reload"/>
    <logdialog ref="logdialog" :dialog.sync="islogdialog" @reload="reload"></logdialog>
    <el-dialog
      append-to-body
      ref="dlgExcelImp"
      title='导入Excel'
      @close="uploadClose"
      :close-on-click-modal="false"
      :visible.sync="dlgVisible">
      <div>
        <div>
          <div>
            <p>进行数据导入操作，请按照以下步骤进行：<br/>
              1. 请下载导入模板：<a @click="downloadTemplate">模板文件</a><br/>
              2. 请按照模板格式，填充好数据并保存<br/>
              3. 选取文件上传<br/>
            </p>
          </div>
          <el-upload
            ref="upload"
            :action='`/api/ic/bifrost/cloud/api.do?apiKey=${apiKey}`'
            :limit="1"
            accept=".xls,.xlsx"
            style="text-align: left;"
            :on-remove="onRemove"
            :on-change="onChange"
            :file-list="fileList"
            align="center"
            :auto-upload="false">
            <el-button @click="clearFiles">选取文件</el-button>
          </el-upload>
        </div>
        <div slot="footer" class="dialog-footer"
             style="padding: 25px 5px 0px 0px;text-align: right;">
          <el-button @click="dlgVisible = false" class="btn-normal">取消</el-button>
          <el-button ref="btUpload" type="primary" class="btn-normal" @click="fileUpload">确认上传</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'basepayee', // 收款人
  provide() {
    return {
      basePageInited: this.basePageInited
    }
  },
  // 收款人
  data() {
    return {
      ispayeedialog: false,
      islogdialog: false,
      file: undefined,
      fileList: [],
      apiKey: 'importPayeeExcel',
      dlgVisible: false,
      fileName: '',
      treeJson: [
        {
          value: '是',
          label: '是'
        },
        {
          value: '否',
          label: '否'
        }
      ],
      bankCodeList: [],
      accTypeList: [],
      request: false,
      basePageThis: undefined,
      fundTraobjTypeList: [],
      treesetting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'name'
          }
        }
      },
      isBasepayee: true // 判断是否是收款人页面 收款人页面 不执行收款人弹窗的mounted
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.$refs.curdList.init({
        params: {
          dataApiKey: 'selectPayeePageData',
          deleteApiKey: 'deletePayee'
        },
        buttons: [
          { text: '设置启用', icon: 'el-icon-lock', enabledType: '1+', click: this.setEnabled },
          { text: '设置禁用', icon: 'el-icon-lock', enabledType: '1+', click: this.setDisEnabled },
          { text: '导入', icon: 'el-icon-aliiconshangchuan', enabledType: '0', click: this.openDialog },
          { text: '操作日志', icon: 'el-icon-lock', enabledType: '1', click: row => (this.openLog(row)) }
        ],
        exportExcelName: '收款人信息表',
        searchForm: [
          '收款人全称:PAYEE_ACCT_NAME_like:文本',
          '收款人账号:PAYEE_ACCT_NO_like:文本',
          '收款银行行别:PAYEE_ACCT_BANK_NAME_eq:下拉:#' + JSON.stringify(this.bankCodeList),
          '收款人开户银行:PAYEE_OPEN_ACCT_BRANCH_BANK_NAME_like:文本',
          '收款账户类型:REC_ACCT_TYPE_eq:下拉:#' + JSON.stringify(this.accTypeList),
          '是否启用:IS_ENABLED_eq:下拉:#' + JSON.stringify(this.treeJson),
          '资金往来对象:FUND_TRAOBJ_TYPE_NAME_eq:树:#' + JSON.stringify(this.fundTraobjTypeList) + ':##' + JSON.stringify(this.treesetting)
        ],
        searchFormNum: 3,
        btAddClick: { text: '新增', click: row => (this.showPayeeDialog(row, '新增')) },
        btModifyClick: { text: '修改', click: row => (this.showPayeeDialog(row, '修改')) },
        btDetailClick: { click: row => (this.btDetailsClick(row)) }
      })
    },
    reload() {
      this.init()
    },
    showPayeeDialog(row, text) {
      if (text === '修改') {
        this.$refs.payeedialog.payeedForm = JSON.parse(JSON.stringify(row))
      } else {
        this.$refs.payeedialog.payeedForm = {
          payeeAcctName: '',
          payeeAcctNo: '',
          payeeAcctBankName: '',
          payeeOpenAcctBankName: '',
          recAcctType: '',
          fundTraobjTypeCode: '',
          fundTraobjTypeName: '',
          isEnabled: '是'
        }
      }
      this.ispayeedialog = true
      this.$nextTick(() => {
        this.$refs.payeedialog.setTree()
      })
    },
    btDetailsClick(row) {
      this.$refs.payeedialog.payeedForm = row
      this.$refs.payeedialog.payeedetails = true
      this.ispayeedialog = true
      this.$nextTick(() => {
        this.$refs.payeedialog.setTree()
      })
    },
    setEnabled(obj) {
      var checkedRows = obj.params.rows
      this.$confirm(`此操作将修改所选的数据为启用状态, 是否继续？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          checkedRows.forEach(row => {
            row.isEnabled = '是'
          })
          this.$callApi('savePayeeListVo', { payeeList: checkedRows }, result => {
            if (result.success) {
              this.$message.success('操作成功!')
              this.ispayeedialog = false
              this.reload()
            }
            return true
          })
        })
        .catch(() => {})
    },
    setDisEnabled(obj) {
      var checkedRows = obj.params.rows
      this.$confirm(`此操作将修改所选的数据为禁用状态, 是否继续？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          checkedRows.forEach(row => {
            row.isEnabled = '否'
          })
          this.$callApi('savePayeeListVo', { payeeList: checkedRows }, result => {
            if (result.success) {
              this.$message.success('操作成功!')
              this.ispayeedialog = false
              this.reload()
            }
            return true
          })
        })
        .catch(() => {})
    },
    uploadClose() {
      this.dlgVisible = false
      this.clearFiles()
    },
    clearFiles() {
      this.$refs.upload.clearFiles()
      this.fileList = []
      this.file = undefined
    },
    downloadTemplate() {
      var params = { }
      this.$fileDownloadBykey('downloadTemplate', params)
    },
    onRemove() {
      this.clearFiles()
    },
    onChange(file, fileList) {
      this.file = file
      this.fileList = fileList
    },
    fileUpload() {
      if (this.$isEmpty(this.file)) {
        this.$message.info('请先选取文件')
        return
      }

      const formData = new FormData()
      formData.append('file', this.file.raw)

      this.$refs.btUpload.loading = true
      this.$fileUpload(this.apiKey, formData)
        .then(({ data }) => {
          this.$refs.btUpload.loading = false
          if (data.success) {
            this.$message.success('导入成功')
            this.fileData = data.data
            this.uploadClose()
            this.reload()
          } else {
            this.clearFiles()
            this.$message.error(
              { dangerouslyUseHTMLString: true,
                message: '导入失败' + data.resultMessage })
          }
        })
        .catch((_error) => {
          this.$refs.btUpload.loading = false
          this.clearFiles()
        })
    },
    openDialog() {
      this.dlgVisible = true
    },
    setBankCodeList(arr) {
      this.bankCodeList = arr
    },
    setAccTypeList(arr) {
      this.accTypeList = arr
    },
    setFundTraobjTypeList(arr) {
      this.fundTraobjTypeList = arr
    },
    basePageInited(_this) {
      this.request = true
      this.basePageThis = _this
    },
    openLog(row) {
      this.$refs.logdialog.dataId = row.getRowId()
      this.$refs.logdialog.show(true)
    }
  }

}
</script>

<style lang="scss">
/deep/ .el-tabs__content{
  height: 100%;
}
</style>
