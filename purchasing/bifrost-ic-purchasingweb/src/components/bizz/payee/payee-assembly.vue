<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-27 18:27:58
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-09 17:03:16
-->
<template>
    <div :class="isCformPath?'cformDetailsAssembly':'payeeAssembly'">
        <ba-payee-list ref="baPayeelist"></ba-payee-list>
        <base-attachment ref="baseAttachment" ></base-attachment>
        <e-invoice ref="eInvoice" ></e-invoice>
        <payee-assembly-func ref="payeeAssemblyFunc"/>
    </div>
</template>

<script>
import BaPayeeList from './payee-list'
import PayeeAssemblyFunc from './payee-assembly-func'
export default {
  name: '报销单+借款单',
  components: { PayeeAssemblyFunc, BaPayeeList },
  data() {
    return {
      isCformPath: window.location.href.includes('/cform')
    }
  },
  methods: {
    initAssembly(meta, mode, formFormat) {
      if (this.$refs.payeeAssemblyFunc === undefined ||
        this.$refs.baPayeelist === undefined) {
        this.$message.error('收款人对象为空')
        return
      }
      if (this.$refs.payeeAssemblyFunc.objs.baPayeelist === undefined) {
        this.$refs.payeeAssemblyFunc.fillObjects(
          this.$refs.baPayeelist,
          this.$refs.baseAttachment,
          this.$refs.eInvoice)
      }

      this.$refs.payeeAssemblyFunc.initAssembly(meta, mode, formFormat)
    },
    showError(result) {
      return this.$refs.payeeAssemblyFunc.showError(result)
    },
    syncBaToPayees(bas, amountSum) {
      this.$refs.payeeAssemblyFunc.syncBaToPayees(bas, amountSum)
    },
    getExtData() {
      return this.$refs.payeeAssemblyFunc.getExtData()
    },
    fillAtt(dataVo) {
      return this.$refs.payeeAssemblyFunc.fillAtt(dataVo)
    }
  }
}
</script>

<style scoped lang="scss">
  .ba-payee-list {
    height: calc((100% - 20px) / 3);
    padding: 0px 0px 0px;
  }
  .e-invoice {
    padding: 0px 0px 0px;
      flex: 1;
  }
  /deep/ .base-attachment {
      height: calc((100% - 20px) / 3 + 10px) !important;
      padding: 0px 0px 0px;
  }
  .payeeAssembly {
    height: 100%;
    width: 100%;
    padding: 0px 0px 0px 0px;
    display: flex;
    flex-direction: column;
  }
  .cformDetailsAssembly{
    height: 100%;
    width: 100%;
    border: none;
    padding-left: 10px;
    /deep/.is-scrollable{padding: 0;}
  }
  .el-tabs .el-tabs--top {
    height: 100%;
  }
  /deep/.el-tabs__content {
    height: calc(100% - 52px) !important;
  }
  .payeeAssembly .el-tabs__content .el-tab-pane {
    height: 100%;
  }
  /deep/ .bottom-table{
    flex: 1;
    overflow: overlay;
  }
  /deep/ .el-table .cell{
    padding: 0px 5px !important;
    font-size: 12px;
  }
</style>
