<template>
  <el-table border :data="incPayeeData">
    <el-table-column class-name="column-index" type="index" align="center" width="20"/>
    <el-table-column class-name="padding-right0" align="left" label="收款人/开户银行">
      <template slot-scope='{row}'>
        <div class="payeeAcctName">{{row.payeeAcctName}}</div>
        <div class="payeeOpenAcctBankName">{{row.payeeMergeName}}</div>
      </template>
    </el-table-column>
    <el-table-column class-name="padding-right0" align="left" label="结算方式/账号" :width="settleModeWidth">
      <template slot-scope='{row}'>
        <div class="settleMode">{{row.settleMode}}</div>
        <div class="payeeAcctNo">{{row.payeeAcctNo}}</div>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="payeeAcctNo" label="支付方式" :width="90">
      <template slot-scope='{row}'>
        <div class="payeeAcctNo">{{row.payType}}</div>
      </template>
    </el-table-column>
    <el-table-column align="right" prop="payeeAcctNo" label="刷卡时间" :width="payeeAcctNoWidth">
      <template slot-scope='{row}'>
        <div class="settleMode">{{swipeAmt(row.swipeAmt)}}</div>
        <div class="payeeAcctNo">{{row.swipeTime}}</div>
      </template>
    </el-table-column>
    <el-table-column align="right" :label="showBa ? '收款金额/指标' : '收款金额'"
      class-name="payeeAmount" :width="payeeAmountAndBaWidth">
      <template slot-scope='{row}'>
        <div class="payeeAmount pr-8">{{$formatMoney(row.amount)}}</div>
        <div class="payeeBa pr-8" v-show="showBa" :title="row.baName">{{row.baCode}}</div>
      </template>
    </el-table-column>
    <el-table-column align="right" :label="taxRowName" v-if="useTaxRow"
                     class-name="payeeAmount" :width="payeeAmountAndBaWidth">
      <template slot-scope='{row}'>
        <div class="payeeAmount">{{$formatMoney(row.amountBeforeTax)}}</div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'payee-audit-extend',
  data() {
    return {
      showBa: true, // 是否显示指标信息
      useTaxRow: false,
      taxRowName: '收款金额(税前)',
      incPayeeData: [],
      settleModeWidth: 118,
      payeeAcctNoWidth: 78,
      payeeAmountAndBaWidth: 90
    }
  },
  methods: {
    init(showBa, incPayeeData, times) {
      if (this.$isNotEmpty(times)) {
        this.settleModeWidth = 118 * times
        this.payeeAcctNoWidth = 78 * times
        this.payeeAmountAndBaWidth = 90 * times
        this.payeeAmountAndBaWidth = this.showBa ? 90 * times : 80 * times
      } else {
        this.payeeAmountAndBaWidth = this.showBa ? 108 : 80
      }
      this.showBa = showBa
      this.incPayeeData = incPayeeData
      this.useTaxRow = false
      // 坝光税金特殊逻辑加了税前(后)金额，因无法读到formFormat直接用返回的收款人信息判断
      if (this.$isNotEmpty(incPayeeData) && this.incPayeeData[0].hasOwnProperty('amountBeforeTax') &&
        this.$isNumber(this.incPayeeData[0].amountBeforeTax) &&
        parseFloat(this.incPayeeData[0].amountBeforeTax) !== parseFloat('0')) {
        this.useTaxRow = true
        this.$callApiParams(
          'getAccSetParValApi'
          , { 'paramsKey': 'incBillFromLogic' } // 决定逻辑走向,详见组织级参数说明
          , result => { // 2.3期逻辑才会有收款人的税前金额列
            if (result.data !== '2') {
              this.taxRowName = '收款金额(税后)'
            }
            return true // 防止弹出操作成功提示
          }, result => {
            this.taxRowName = '收款金额(税后)'
            return true // 防止弹出操作成功提示
          })
      }
    },
    swipeAmt(amt) {
      var amount = this.$formatMoney(amt)
      if (parseFloat(amount) * 1 > 0.00) { // 刷卡金额是零时不显示
        return amount
      }
      return ''
    }
  }
}
</script>

<style lang='scss' scoped>
.pr-8 {
  padding-right: 8px;
}
</style>
