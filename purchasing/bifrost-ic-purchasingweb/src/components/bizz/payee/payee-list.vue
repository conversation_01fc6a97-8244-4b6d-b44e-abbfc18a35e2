<template>
    <div class="ba-payee-list">
      <div class="top-btns" v-if="(isShowBtn && disabledArr.length > 0) || (!mode && isShowBtn)">
        <el-button size="small" @click="choosePayeeBtn" type="primary" icon="el-icon-circle-check" v-show="showCrudBtnFlag" :disabled="isPayeeDisabled"> 选择收款人 </el-button>
        <el-button size="small" @click="addPayeeBtn" plain icon="el-icon-circle-plus-outline" v-show="showCrudBtnFlag" :disabled="isPayeeDisabled"> 新增收款人</el-button>
        <el-button size="small" @click="batchAssociationIndicators" plain icon="el-icon-set-up" v-show="indicatorsShow" :disabled="indicatorsDisabled">批量关联指标</el-button>
        <el-button size="small" @click="importPayeeBtn" plain icon="el-icon-circle-plus-outline" v-show="showCrudBtnFlag" :disabled="isPayeeDisabled"> 导入</el-button>
        <el-button size="small" @click="exportPayeeBtn" plain icon="el-icon-circle-plus-outline" v-show="showCrudBtnFlag" :disabled="isPayeeDisabled || checkedPayeed.length === 0"> 导出</el-button>
        <el-button size="small" @click="updatePayeeBtn" :disabled='isPayeeDisabled || checkedPayeed.length !== 1' v-show="showCrudBtnFlag" plain icon="el-icon-edit"> 修改</el-button>
        <el-button size="small" @click="deletePayeeBtn(true)" :disabled='isPayeeDisabled || checkedPayeed.length === 0' plain icon="el-icon-delete"> 删除</el-button>

        <el-input size="small" clearable class="payee-count mleft-6"
                  :disabled="isPayeeDisabled"
                  :value="`${extData.incPayeeData.length}个`"
                  :readonly="true" v-if="!baseaAplication">
          <template slot="prepend">{{ payeeType }}</template>
        </el-input>
        <el-input size="small" clearable class="total-amount mleft-6"
                  :disabled="isPayeeDisabled"
                  :value="`${amountTotal} 元`"
                  :readonly="true" v-if="!baseaAplication">
          <template slot="prepend">总金额</template>
        </el-input>
        <el-input size="small" clearable class="amount-difference mleft-6"
                  :disabled="isPayeeDisabled"
                  :value="`${amountDiff} 元`"
                  :readonly="true" v-if="!baseaAplication">
          <template slot="prepend">差额</template>
        </el-input>
      </div>
      <div class="bottom-table payee-container">
        <el-table border :data="extData.incPayeeData"
                  @row-dblclick="rowDblclickCols"
                  @selection-change="handleSelectionChange"
                  style="width: 100%"
                  ref="payeeTable">
          <el-table-column v-if="isShowBtn" type="selection" align="center" width="25"/>
          <el-table-column align="left" :label="`${payeeType} / 开户银行`"
                           :style="{width:payeeNameAndBankWidth}"
                           show-overflow-tooltip v-if="isNameAndBankInOneCol">
            <template slot-scope='{row}'>
              <div class="payeeAcctName">{{row.payeeAcctName}}</div>
              <div class="payeeMergeName">{{row.payeeMergeName ?
                row.payeeMergeName : row.payeeOpenAcctBankName ?
                  row.payeeOpenAcctBankName.concat(row.payeeOpenAcctBranchBankName) : row.payeeOpenAcctBranchBankName}}</div>
            </template>
          </el-table-column>
          <el-table-column :label="payeeType" prop="payeeAcctName" v-if="!isNameAndBankInOneCol"/>
          <el-table-column label="开户银行" prop="payeeOpenAcctBankName" v-if="!isNameAndBankInOneCol"/>
          <el-table-column label="开户银行支行" prop="payeeOpenAcctBranchBankName" v-if="!isNameAndBankInOneCol"/>

        <el-table-column align="left" prop="payeeAcctNo" show-overflow-tooltip label="账号" :width="payeeAcctNoWidth"/>

                <el-table-column align="left" prop="payType"  label="支付方式" :width="settleModeWidth" v-if="isNeedPayType">
                  <template #header><span style="color:#f56c6c;">*</span>支付方式</template>
                  <template slot-scope='{row, $index}'>
                    <el-select clearable v-model="row.payType"
                              :disabled="!disabledArr.includes('支付方式') && mode"
                               style="width: 100%"
                               @change="hideError($index,row)"
                               placeholder="选择方式" v-if="isShowBtn && !isAmountDisabled">
                      <el-option
                        v-for="item in payTypeList"
                        :key="item.eleName"
                        :label="item.eleName"
                        :value="item.eleName"
                        :disabled="isOptionDisabled(item, row)"
                        style="font-size: 12px">
                      </el-option>
                    </el-select>
                    <span v-else>{{row.payType}}</span>
                  </template>
                </el-table-column>

                <el-table-column align="left" prop="settleMode"  label="结算方式" :width="settleModeWidth" v-if="!baseaAplication">
                 <template #header><span style="color:#f56c6c;">*</span>结算方式</template>
                    <template slot-scope='{row, $index}'>
                        <el-select clearable v-model="row.settleMode"
                                  :disabled="!disabledArr.includes('结算方式') && mode"
                                   style="width: 100%"
                                   @change="hideError($index)"
                                   placeholder="选择方式" v-if="isShowBtn && !isAmountDisabled">
                            <el-option
                                    v-for="item in setModeList"
                                    :key="item.eleName"
                                    :label="item.eleName"
                                    :value="item.eleName"
                                    style="font-size: 12px">
                            </el-option>
                        </el-select>
                        <span v-else>{{row.settleMode}}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="swipeTime"
                      show-overflow-tooltip label="刷卡时间" :width="swipeTimeWidth" v-if="!baseaAplication">
                  <template slot-scope="{row, $index}">
                    <div v-if="row.payType != '公务卡支付'">
                      <el-date-picker
                      :disabled="!disabledArr.includes('刷卡时间') && mode"
                        style="width:100%"
                        v-if="isShowBtn && !isAmountDisabled"
                        v-model="row.swipeTime"
                        type="date"
                        :editable="false"
                        @change="hideError($index)"
                        value-format="yyyy-MM-dd"
                        placeholder="选择日期">
                      </el-date-picker>
                      <span v-else>{{row.swipeTime}}</span>
                    </div>
                    <div v-if="row.payType === '公务卡支付'">
                      <el-input
                        style="width:100%"
                        prefix-icon="el-icon-search"
                        v-model="row.businessCardValue"
                        @click.native="refTableBusinessData(row)"
                      >
                      </el-input>
                    </div>
                  </template>
                </el-table-column>
                <!-- :width="payeeAmountWidth" -->
                <el-table-column label="收款金额" show-overflow-tooltip  v-if="!baseaAplication">
                  <template #header><span style="color:#f56c6c;">*</span>收款金额</template>
                  <template slot-scope='{row, $index}'>
                     <el-input  v-if="isShowBtn" style="width: 100%" class="payeeAmount"
                        :disabled="(!disabledArr.includes('收款金额') && mode) || isAmountDisabled"
                        v-model="row.amount"
                        oninput="value=value.replace(/[^0-9.]/g,'')"
                        @blur="formFormatItem(row)"
                        @input="syncWhenAmountInput($index)"
                        placeholder="输入金额"/>
                     <span v-else>{{row.amount}}</span>
                  </template>
                </el-table-column>
                <el-table-column :label="taxRowName" width="120" show-overflow-tooltip  v-if="useTaxRow">
                  <template #header><span style="color:#f56c6c;">*</span>{{taxRowName}}</template>
                  <template slot-scope='{row, $index}'>
                    <el-input  v-if="isShowBtn" style="width: 99%" class="payeeAmount"
                               v-model="row.amountBeforeTax"
                               oninput="value=value.replace(/[^0-9.]/g,'')"
                               @blur="formFormatItem2(row)"
                               @input="hideError($index)"
                               :disabled="isAmountDisabled"
                               :placeholder="'输入'+taxRowName"/>
                    <span v-else>{{row.amountBeforeTax}}</span>
                  </template>
                </el-table-column>
                <!-- :width="baTitleWidth" -->
                <el-table-column  prop="baTitle" align="center"
                    label="指标" v-if="isShowBaColumn">
                  <template #header><span style="color:#f56c6c;">*</span>指标</template>
                  <template slot-scope="{row}">
                    <el-select v-model="row.baId" style="width: 100%" :disabled="!disabledArr.includes('指标') && mode" placeholder="选择指标" @change="givePayType" v-if="isShowBtn">
                      <div v-for="(item, index) in bas" :key="index">
                        <el-option
                            :key="item.baId"
                            :label="item.baTitle"
                            :value="item.baId">
                          <el-popover
                            placement="left-start"
                            width="300"
                            trigger="hover">
                            <p style="margin: 5px 0px 5px 0px">可用金额：{{ item.amountEnable }} 元</p>
                            <p v-if="item.hasEconomicClassification" style="margin: 5px 0px 5px 0px">
                              经济分类：{{ item.economicClassification }}</p>
                            <span slot="reference">{{item.baTitle}}</span>
                          </el-popover>
                        </el-option>
                      </div>
                    </el-select>
                    <span v-else :title="row.baTitle">{{row.baTitle}}</span>
                  </template>
                </el-table-column>
          </el-table>
          </div>
          <payeedialog ref="payeedialog" :dialog.sync="ispayeedialog" :disabledArr="disabledArr" :mode="mode" @reload='reload' :showIsEnabledItem="false"/>
          <payeeImportDialog ref="dlgExcelImp" @concatExtData="concatExtData" />
          <indicatorsDialog ref="indicatorsDialog" :data="bas" :checkedItem="checkedPayeed" @setIndicators="setIndicators"/>
      </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'ba-payee-list',
  props: {
    isShowBtn: { type: Boolean, default: true },
    isBaseaAplication: { type: Boolean, default: false },
    isPayeeDataCanRepeat: { type: Boolean, default: true },
    isTabAplication: { type: Boolean, default: false },
    isUseTaxRow: { type: Boolean, default: false },
    disabledArr: { type: Array, default: () => [] }, // 审核模式下显示可修改的配置项
    mode: { type: Boolean, default: false }, // 是否是审核模式
    showBatchAssociationIndicators: { type: Boolean, default: true } // 是否展示批量关联指标
  },
  provide() {
    return {
      reload: (data) => this.reload(data)
    }
  },
  data() {
    return {
      isNameAndBankInOneCol: true, // 收款人和银行名称在同一列
      isPayeeDisabled: false, // 单据是否不需要收款人
      isAmountDisabled: false, // 收款金额是否禁用(禁用了也同时说明收款人不能添加了)
      ispayeedialog: false,
      setModeList: [], // 结算方式枚举
      payTypeList: [], // 支付方式枚举
      extData: {
        incPayeeData: [] // 收款人数据
      },
      checkedPayeed: [],
      amountTotal: '0.00', // 当前收款人总金额
      applyAmount: 0.00, // 实际总申请金额
      amountDiff: '0.00', // 差额
      isChange: false,
      changeItemId: null,
      bas: [],
      baColItems: [],
      baseaAplication: this.isBaseaAplication,
      useTaxRow: this.isUseTaxRow,
      taxRowName: '收款金额(税前)',
      payeeDataCanRepeat: this.isPayeeDataCanRepeat, // 收款人数据是否可以重复
      isShowBaColumn: false,
      payeeNameAndBankWidth: 0,
      payeeAmountWidth: 0,
      incPayeeDataIndex: 0,
      isClickSaveBtn: false,
      formFormat: undefined,
      hasEconomicClassifications: [],
      economicClassifications: [],
      baCodes: [],
      payeeType: '收款人', // 列名文字显示为收款人,当为还款单的时候显示为还款人
      showCrudBtnFlag: true, // 隐藏crud按钮
      amountEnables: [],
      baNameAndPayTypes: [],
      isNeedPayType: false // 是否需要支付方式
    }
  },
  computed: {
    indicatorsShow() {
      return this.showCrudBtnFlag && this.showBatchAssociationIndicators
    },
    indicatorsDisabled() {
      return this.isPayeeDisabled || this.checkedPayeed.length <= 1
    },
    payeeAcctNoWidth() {
      if (!this.isNameAndBankInOneCol) {
        return 260
      }
      if (this.isTabAplication) {
        return 500
      }
      return !this.baseaAplication ? 160 : 300
    },
    settleModeWidth() {
      if (!this.isNameAndBankInOneCol) {
        return 150
      }
      if (this.isTabAplication) {
        return 200
      }
      return 100
    },
    swipeTimeWidth() {
      if (!this.isNameAndBankInOneCol) {
        return 150
      }
      if (this.isTabAplication) {
        return 250
      }
      return 85
    },
    baTitleWidth() {
      return 0
    }
  },
  watch: {
    'extData.incPayeeData': {
      handler() {
        this.computedTotal()
      },
      deep: true
    },
    'applyAmount': {
      handler() {
        this.computedTotal()

        // 申请金额变化且只有一个收款人，则收款人的金额与申请金额同步
        if (this.extData.incPayeeData.length === 1) {
          this.extData.incPayeeData[0].amount = this.applyAmount
          !this.isClickSaveBtn && this.formatPayeesMoney()
          if (this.useTaxRow) {
            // 由于坝光的特殊逻辑，申请金额显示的是税前总和，这里实际扣减应该要计算指标申请金额总和
            let amountLeft = parseFloat('0')
            for (let i = 1; i <= 20; i++) {
              const prefix = (i === 1) ? '' : '' + i
              const baApplyAmountItem = this.formFormat.labelOriginColItemMap['指标申请金额' + prefix]
              if (this.$isNotEmpty(baApplyAmountItem)) {
                const value = baApplyAmountItem.dataValue
                const amount = this.$isNumber(value) ? this.$unFormatMoney(value) : 0
                amountLeft += parseFloat(amount)
              }
            }
            this.extData.incPayeeData[0].amount = amountLeft
          }
        }
      }
    }
  },
  mounted() {
    window.$event.$on('removePayee', data => {
      const loanId = data.loanId.split('[tab]')[0]
      const baId = data.baId.split('[tab]')[0]
      this.checkedPayeed = this.extData.incPayeeData.filter(incPayee => loanId === incPayee.loanId && baId === incPayee.baId)
      this.deletePayeeBtn()
    })
  },
  methods: {
    isOptionDisabled(item, row) {
      if (row.payType === '政府投资支付') {
        if (item.eleName !== '政府投资支付') {
          return true
        }
      }
      if (row.payType === '政府采购支付') {
        if (item.eleName !== '政府采购支付') {
          return true
        }
      }
      if (row.payType === '国库普通支付' ||
            row.payType === '公务卡支付' ||
            row.payType === '自有资金支付') {
        if (item.eleName === '政府采购支付' || item.eleName === '政府投资支付') {
          return true
        }
      }
    },
    syncPayeeTableColumnWidths() {
      if (!this.isNameAndBankInOneCol) {
        if (this.isTabAplication) {
          this.payeeAmountWidth = 300
        } else {
          this.payeeAmountWidth = 150
        }
      } else {
        this.isShowBaColumn = (this.bas.length > 1)
        if (this.isShowBaColumn) {
          this.payeeNameAndBankWidth = 210
          this.payeeAmountWidth = 90
        } else {
          this.payeeNameAndBankWidth = this.isTabAplication ? 443 : 270
          this.payeeAmountWidth = this.isTabAplication ? 270 : 0
        }
      }
    },
    formFormatItem(item) {
      item.amount = this.$formatMoney(item.amount)
    },
    formFormatItem2(item) {
      // 如果放到formFormatItem里面先输入收款金额会导致该行的税前金额列不可填
      // item.amountBeforeTax = this.$formatMoney(item.amountBeforeTax)
      if (this.$isNotEmpty(item.amountBeforeTax) &&
        this.$isNumber(item.amountBeforeTax.replace(/[^0-9.]/g, ''))) {
        item.amountBeforeTax = parseFloat(item.amountBeforeTax.replace(/[^0-9.]/g, ''))
          .toFixed(2)
      } else {
        item.amountBeforeTax = ''
      }
    },
    init(meta, mode, formFormat) {
      this.baColItems = meta.colItems
      var isNeedPayType = meta.extData['收款人是否需要支付方式']
      if (isNeedPayType === '是') {
        this.isNeedPayType = true
      }
      this.isAmountDisabled = false
      this.initJustPayee(meta, formFormat)

      // 注册表单要素值变化回调函数
      formFormat.addColItemModifiedCallbacks({
        '收款人同步申请金额': (theColItem) => {
          if (theColItem.labelOrigin === '申请金额') {
            this.applyAmount = theColItem.dataValue
          }
          if (theColItem.labelOrigin.indexOf('关联采购申请') > -1) {
            this.applyAmount = formFormat.getValue('申请金额')
          }
        },
        '收款人同步指标': (theColItem) => {
          if (this.$isBaItem(theColItem) ||
            theColItem.labelOrigin.indexOf('借款申请单') !== -1 ||
            theColItem.labelOrigin.indexOf('关联采购申请') > -1) {
            this.$nextTick(() => {
              this.syncBas(theColItem, formFormat)
              this.applyAmount = formFormat.getValue('申请金额')
            })
          }
        } })

      formFormat.addShowErrorCallbacks({
        '收款人错误提示': errorItems => {
          if (this.$isNotEmpty(errorItems)) {
            var payeeIndexes = []
            var keys = Object.keys(errorItems)
            keys.forEach(key => {
              payeeIndexes.push(key.replace('incPayee', ''))
            })
            this.showPayeeError(payeeIndexes)
          }
        }
      })
      this.isClickSaveBtn = false
      this.useTaxRow = false
      if (this.$isNotEmpty(meta.colItems) &&
        this.$isNotEmpty(meta.meta) &&
        this.$isNotEmpty(meta.meta.formType) &&
        (meta.meta.formType === '报销单')) {
        this.isShowBeforeAmount(meta.colItems)
      }

      if (this.$isNotEmpty(formFormat.labelColItemMap['还款单类型'])) {
        if (formFormat.getValue('还款单类型', true) === '还款') {
          this.doBiz4Repay()
        }
      }
    },
    /**
     * 是否显示收款人的税前金额列
     * 目前该逻辑只有报销单使用（事前单暂无收款人）
     * @param colItems 要素集合
     */
    isShowBeforeAmount(colItems) {
      const colItemsMap = {}
      colItems.forEach(col => {
        colItemsMap[col.label] = col
      })
      if (this.$isNotEmpty(colItemsMap['税前金额']) &&
        this.$isNotEmpty(colItemsMap['税率']) &&
        this.$isNotEmpty(colItemsMap['税金']) &&
        this.$isNotEmpty(colItemsMap['税后金额']) &&
        this.$isNotEmpty(colItemsMap['票据类型'])) { // 是否有票据单据的前置要素
        this.$callApiParams(
          'getAccSetParValApi'
          , { 'paramsKey': 'incBillFromLogic' } // 决定逻辑走向,详见组织级参数说明
          , result => { // 2.3期逻辑才会有收款人的税前金额列
            if (this.$isNotEmpty(result.data) && result.data === '1') {
              this.useTaxRow = false
            } else { // 空也默认为逻辑3
              if (result.data !== '2') {
                this.taxRowName = '收款金额(税后)'
              }
              this.useTaxRow = true
            }
            return true // 防止弹出操作成功提示
          }, result => {
            this.useTaxRow = true // 有前置要素的情况下，如果调用失败沿用默认逻辑
            this.taxRowName = '收款金额(税后)'
            return true // 防止弹出操作成功提示
          })
      }
    },
    syncPayees(billIds) {
      // 当还款单选择了借款申请单参照的时候,要把借款申请单对应的收款人查询出来,放在还款单的还款人中
      if (this.$isNotEmpty(billIds)) {
        this.$callApiParams('selectBasePayeesByBillId', { BILL_ID_in: billIds, DATA_TYPE_in: '收款人,单据收款人', 'PAY_STATUS_eq': '已支付' },
          result => {
            // 此处需要对基础收款人特殊处理,记录收款人和借款单指标之间的关系,这样删除借款单指标的时候可以同步删除还款人
            if (this.$isNotEmpty(result.data)) {
              result.data.forEach(item => {
                item.id = item.basePayeeId
                item.amount = null
                item.loanId = item.billId
              })
              this.addPayees(result.data, true)
            }
            return true
          })
      }
    },
    // 当是还款单的时候,需要处理收款人组件的按钮的显示和文字等 借款人改成还款人
    doBiz4Repay() {
      this.payeeType = '还款人'
      this.showCrudBtnFlag = false
    },
    initJustPayee(meta, formFormat) {
      if (this.$isNotEmpty(meta.extData)) {
        this.bas = meta.extData.bas
        // 把指标金额格式化
        this.bas.forEach(v => {
          v.amountUsable = this.$formatMoney(v.amountUsable)
          v.amountUsed = this.$formatMoney(v.amountUsed)
          v.amountEnable = this.$formatMoney(v.amountEnable)
        })
        this.applyAmount = meta.extData.applyAmount
        this.extData.incPayeeData = meta.extData.incPayeeData
      }
      this.selectSetModeList()
      this.selectPayTypeList()
      this.syncPayeeTableColumnWidths()
      this.formFormat = formFormat

      this.computedTotal()
      this.formatPayeesMoney()
    },
    syncFormatObjAfterPayeeChanged() {
      // 收款人变化时，同步表单数据
      if (this.formFormat &&
        this.formFormat.formExtend &&
        this.formFormat.formExtend.syncFormatObjAfterPayeeChanged) {
        this.formFormat.formExtend.syncFormatObjAfterPayeeChanged(
          this.formFormat, this.extData.incPayeeData)
      }
    },
    showPayeeError(payeeIndexes) {
      if (this.$isNotEmpty(payeeIndexes)) {
        var $payeeAcctNames = $('.payee-container .payeeAcctName')
        $.each($payeeAcctNames, (index, item) => {
          if (payeeIndexes.indexOf(index + '') > -1) {
            $(item).parent().addClass('payeeError')
          }
        })
      }
    },
    hideError(rowIndex, row) {
      if (row) {
        if (row.payType && row.payType === '公务卡支付') {
          row.swipeTime = ''
        } else {
          // row.businessCardId = ''
          row.businessCardValue = ''
        }
      }
      var $rows = $('.payee-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.payeeError').removeClass('payeeError')
        }
      })
    },
    givePayType() {
      this.extData.incPayeeData.forEach((row) => {
        const baId = row.baId
        const baPayType = this.getBaPayType(baId)
        const formPayType = this.formFormat?.getValue('支付方式')
        // 支付方式的取值优先级：指标的支付方式 > 本单据中的支付方式 > 默认'国库普通支付'
        const value = this.$isNotEmpty(baPayType)
          ? baPayType : this.$isNotEmpty(formPayType)
            ? formPayType : '国库普通支付'
        this.$set(row, 'payType', value)
      })
    },
    getBaPayType(baId) {
      if (this.baNameAndPayTypes.length === 1) {
        return this.baNameAndPayTypes[0].baPayTypeItem
      }
      for (const payType of this.baNameAndPayTypes) {
        if (payType.baIdItem === baId && this.$isNotEmpty(payType.baPayTypeItem)) {
          return payType.baPayTypeItem
        }
      }
      return null // 如果未找到匹配的 baPayType，可以返回一个默认值
    },
    async syncBas(colItem, formFormat) {
      this.$clearArray(this.bas)
      var baIds = []
      var baTitles = []
      var baAmounts = []
      this.baNameAndPayTypes = []
      for (var i = 1; i <= 20; i++) { // 依次轮询可能存在的指标
        var prefix = (i === 1) ? '' : '' + i
        var labelOrigin = '指标' + prefix
        var payTypeItem = '指标支付方式' + prefix
        var baId = '指标' + prefix + 'ID'
        var baItem = formFormat.labelOriginColItemMap[labelOrigin]
        var baPayTypeItem = formFormat.labelOriginColItemMap[payTypeItem]
        var baIdItem = formFormat.labelOriginColItemMap[baId]
        var baNameAndPayTypeMap = {}
        let baIdItemValue = ''
        if (this.$isNotEmpty(baPayTypeItem) && this.$isNotEmpty(baIdItem.dataValue)) {
          if (baIdItem.dataValue.indexOf('[tab]') > -1) {
            tokens = baIdItem.dataValue.split('[tab]')
            baIdItemValue = tokens[0]
          }
          baNameAndPayTypeMap.baIdItem = baIdItemValue
          baNameAndPayTypeMap.baPayTypeItem = baPayTypeItem.dataValue
          this.baNameAndPayTypes.push(baNameAndPayTypeMap)
        }
        if (this.$isNotEmpty(baItem)) { // 找到指标
          // 指标ID的格式是：ID[tab]选择部门指标，此处分解出名称
          let baId = formFormat.getValue(labelOrigin + 'ID')
          if (this.$isEmpty(baId)) { // 指标ID为空，表明该指标要素还没有设置
            continue
          }

          if (baId.indexOf('[tab]') > -1) {
            tokens = baId.split('[tab]')
            baId = tokens[0]
          }
          if (baId.indexOf(',') > 1) { // 指标可能是逗号分隔的情况
            tokens = baId.split(',')
            tokens.forEach(bi => baIds.push(bi))
          } else {
            baIds.push(baId)
          }

          // 处理指标标题
          var tls = []
          var tokens
          var baTitle = formFormat.getValue(labelOrigin)
          var isRefNoCode = false
          if (formFormat.formExtend) {
            isRefNoCode = formFormat.formExtend.isRefNoCode(
              labelOrigin, formFormat, baItem.dataRef)
          }

          if (baTitle.indexOf(',') > -1) { // 处理多指标标题使用逗号分隔的情况
            tokens = baTitle.split(',')
            tokens.forEach(tk => { tls.push(tk) })
          } else {
            tls.push(baTitle)
          }
          tls.forEach(tl => { // 标题可能是[编码 标题的情况]
            if (tl.indexOf(' ') > -1) {
              tokens = tl.split(' ')
              var baTitleIndex = isRefNoCode ? 0 : 1
              baTitles.push(tokens[baTitleIndex])
            } else {
              baTitles.push(tl)
            }
          })
          var baAmountItem = formFormat.labelOriginColItemMap['指标申请金额' + prefix]
          var baAmount = this.$unFormatMoney(baAmountItem.dataValue)
          baAmount = (baAmount === undefined) ? '' : baAmount + ''
          if (baAmount.indexOf(',') > -1) { // 处理多指标标题使用逗号分隔的情况（注：数字没有indexOf方法）
            tokens = baAmount.split(',')
            tokens.forEach(tk => { baAmounts.push(tk) })
          } else {
            baAmounts.push(baAmount)
          }
        }
      }

      if (baIds.length !== baTitles.length) {
        this.$message.error('指标ID和指标标题个数不相同')
        return
      }
      if (baIds.length !== baAmounts.length) {
        this.$message.error('指标ID和指标申请金额个数不相同')
        return
      }
      var baIdMap = {}
      for (let i = 0; i < baIds.length; i++) {
        baIdMap[baIds[i]] = baIds[i]
        // if (this.$isNotEmpty(baIdMap[baIds[i]])) {
        //   this.$message.error('指标不能重复')
        //   return
        // } else {
        //   baIdMap[baIds[i]] = baIds[i]
        // }
      }
      await this.selectEconomicClassifications(baIds)
      this.bas = []
      for (let i = 0; i < baIds.length; i++) {
        this.bas.push({
          baId: baIds[i],
          baTitle: baTitles[i],
          baAmount: this.$unFormatMoney(this.$formatMoney(baAmounts[i])),
          amountEnable: this.amountEnables[i],
          economicClassification: this.economicClassifications[i],
          hasEconomicClassification: this.hasEconomicClassifications[i]
        })
      }
      const uniqueData = this.bas.reduce((acc, curr) => {
        const existingItem = acc.find(item => item.baId === curr.baId)
        if (!existingItem) {
          acc.push(curr)
        }
        return acc
      }, [])
      for (let i = 0; i < uniqueData.length; i++) {
        uniqueData[i].baTitle = (i + 1) + '.' + this.baCodes[i] + ' ' + uniqueData[i].baTitle
      }
      this.bas = uniqueData
      this.syncPayeeTableColumnWidths()

      // 指标有修改，则要检查收款人之前绑定的指标是否要修正
      this.extData.incPayeeData.forEach((item, index) => { // 金额需要去除千分位
        if (baIds.indexOf(item.baId) < 0) {
          item.baId = ''
        }
      })
      this.givePayType()
    },
    selectEconomicClassifications(baIds) {
      // 根据指标ID查询指标的ID, 经济分类, 指标编码, 可用金额
      return new Promise((resolve) => {
        var baIdsStr = baIds.join(',')
        this.baCodes = []
        this.economicClassifications = []
        this.amountEnables = []
        this.$callApiParams('selectBaCodeAndEc', { ids: baIdsStr }, result => {
          if (this.$isNotEmpty(result.data)) {
            result.data.forEach(str => {
              var arr = str.split(',')
              if (arr.length > 3) {
                this.baCodes.push(arr[1])
                this.amountEnables.push(arr[2])
                this.economicClassifications.push(arr[3])
                this.hasEconomicClassifications.push(true)
              } else {
                this.baCodes.push(arr[1])
                this.amountEnables.push(arr[2])
                this.hasEconomicClassifications.push(false)
              }
            })
          }
          resolve()
          return true
        })
      })
    },
    selectPayTypeList() { // 支付方式
      if (this.isNeedPayType && this.$isEmpty(this.payTypeList)) {
        this.$callApi('selectPayTypeList', {}, result => {
          if (result.success) {
            this.payTypeList = result.data.slice(1)
          }
          return true
        })
      }
    },
    selectSetModeList() { // 结算方式
      if (!this.baseaAplication && this.$isEmpty(this.setModeList)) {
        this.$callApi('selectSetModeList', {}, result => {
          if (result.success) {
            this.setModeList = result.data
          }
          return true
        })
      }
    },
    syncWhenAmountInput(rowIndex) {
      this.hideError(rowIndex)
      this.payeeAmountChange(rowIndex)
    },
    // 如果现在操作的是倒数第二个收款人，自动设置最后一个收款人的金额等于剩余金额
    payeeAmountChange(payeeIndex) {
      if (this.useTaxRow && this.taxRowName === '收款金额(税前)') {
        return
      }
      var count = this.extData.incPayeeData.length
      if (count > 1 && payeeIndex === count - 2) {
        var sum = 0
        var payeeData = this.extData.incPayeeData
        for (let i = 0; i < payeeData.length - 1; i++) {
          const amt = payeeData[i].amount ? this.$unFormatMoney(payeeData[i].amount) : 0
          sum += amt * 1
        }
        var leftAmount = this.applyAmount - sum
        if (leftAmount > 0) {
          this.extData.incPayeeData[count - 1].amount = this.$formatMoney(leftAmount)
          this.computedTotal()
        }
      }
    },
    syncBaToPayees(baList, amountSum) { // 更新指标信息
      const flag = {}
      const filterData = baList.reduce((item, curIem) => {
        if (curIem.baId) {
          curIem.baId = curIem.baId.split('[tab]')[0]
        }

        if (flag[curIem.baId] === undefined || flag[curIem.baId] === false) {
          flag[curIem.baId] = true
          if (this.$isNotEmpty(curIem.baId)) {
            item.push(curIem)
          }
        }

        // curIem.baId = curIem.baId && curIem.baId.split('[tab]')[0]
        // flag[curIem.baId] ? false : flag[curIem.baId] = true && this.$isNotEmpty(curIem.baId) && item.push(curIem)
        return item
      }, [])
      if (filterData.length === 1) {
        this.extData.incPayeeData.forEach(item => {
          item = Object.assign(item, filterData[0])
        })
      } else {
        this.extData.incPayeeData.forEach(item => {
          item.baTitle = ''
          item.baId = ''
        })
      }
      this.bas = filterData
      this.applyAmount = this.$isNumber(amountSum)
        ? this.$unFormatMoney(amountSum) : 0.00
      this.computedTotal()
    },
    rowDblclickCols(row, column, event) {
      if (this.mode) { return }
      if (!this.isShowBtn || this.isAmountDisabled) {
        return
      }
      const refData = {
        colType: '弹框',
        dataRef: '选择收款人'
      }
      this.$refData(undefined, refData,
        () => {},
        () => {},
        selectedData => {
          const data = selectedData.list[0]
          this.extData.incPayeeData.forEach(item => {
            if (row.id === item.id) {
              item = Object.assign(item, data)
              item.basePayeeId = item.id
            }
          })
        }, selectedData => {
        }, { IS_ENABLED_eq: '是' })
    },
    deletePayeeBtn(btnEvent) {
      for (let i = 0; i <= this.extData.incPayeeData.length - 1; i++) {
        this.checkedPayeed.forEach(item => {
          // 还款单使用借款单参照,还款人不能增加,所以删除按钮不能删除最后一条,但是通过删除借款单是可以的
          if (this.payeeType === '还款人' && this.extData.incPayeeData.length === 1 && btnEvent === true) {
            this.$message.error('最后一条还款人信息，不可删除!')
            return
          }
          const data = this.extData.incPayeeData[i]
          if (this.$isEmpty(item.index)) {
            if (item.id === data.id) {
              this.extData.incPayeeData.splice(i, 1)
            }
          } else {
            if (item.id === data.id && item.index === data.index) {
              this.extData.incPayeeData.splice(i, 1)
            }
          }
        })
      }

      if (this.extData.incPayeeData.length === 1) {
        this.computedTotal()
        this.extData.incPayeeData[0].amount = this.applyAmount
        this.formatPayeesMoney()
      }
      this.syncFormatObjAfterPayeeChanged()
    },
    reload(data) {
      var defaultSettleMode = this.$isEmpty(this.setModeList)
        ? '' : this.setModeList[0].eleName
      const formatData = { ...data, ...this.initPayeeBa(), basePayeeId: data.id, settleMode: defaultSettleMode }
      if (!this.isChange) {
        // 其金额默认取剩余的金额
        var amountLeft = this.$unFormatMoney(this.amountDiff)
        formatData.amount = amountLeft
        this.extData.incPayeeData = this.extData.incPayeeData.concat([], formatData)
      } else {
        this.extData.incPayeeData.forEach(item => {
          if (item.id === this.changeItemId) {
            item = Object.assign(item, formatData)
            item.basePayeeId = item.id
          }
        })
      }
      this.ispayeedialog = false
      this.isChange = false
      this.syncFormatObjAfterPayeeChanged()
    },
    computedTotal() { // 动态计算金额
      var payeeData = []
      if (this.extData && this.extData.incPayeeData) {
        payeeData = this.extData.incPayeeData
      }
      var sum = payeeData.reduce((total, item) => {
        const amt = item.amount ? this.$unFormatMoney(item.amount) : 0
        return total + amt * 1
      }, 0)

      this.amountDiff = this.$formatMoney(this.applyAmount - sum)
      this.amountTotal = this.$formatMoney(sum)
      if (this.useTaxRow && this.taxRowName === '收款金额(税前)') {
        var sumTaxBefore = payeeData.reduce((total, item) => {
          const amt = item.amountBeforeTax ? this.$unFormatMoney(item.amountBeforeTax) : 0
          return total + amt * 1
        }, 0)
        this.amountDiff = this.$formatMoney(this.applyAmount - sumTaxBefore)
        this.amountTotal = this.$formatMoney(sumTaxBefore)
      }
      return sum
    },
    addPayeeBtn() {
      this.$refs.payeedialog.payeedForm = {
        payeeAcctName: '',
        payeeAcctNo: '',
        payeeAcctBankName: '',
        payeeOpenAcctBankName: '',
        payeeOpenAcctBranchBankName: '',
        recAcctType: '',
        isEnabled: '是'
      }
      this.ispayeedialog = true
      this.$refs.payeedialog.ispayeedialog = true
    },
    handleSelectionChange(rows) { this.checkedPayeed = rows },
    updatePayeeBtn() {
      var cloneData = JSON.parse(JSON.stringify(this.checkedPayeed[0]))
      cloneData.bizid = cloneData.basePayeeId
      this.$refs.payeedialog.payeedForm = cloneData
      this.isChange = true
      this.changeItemId = this.checkedPayeed[0].id
      this.ispayeedialog = true
      this.$refs.payeedialog.ispayeedialog = true
      this.$nextTick(() => {
        this.$refs.payeedialog.setTree()
      })
    },
    getPayeesToSave() { // 保存表单时获取收款人数据
      // 如果当前只有一个指标，则收款人自动关联这个指标
      var defaultBaId = ''
      if (this.$isNotEmpty(this.bas) && this.bas.length === 1) {
        defaultBaId = this.bas[0].baId
      }
      this.extData.incPayeeData = this.extData.incPayeeData.map((item, index) => { // 金额需要去除千分位
        this.hideError(index)
        item.amount = this.$unFormatMoney(item.amount)
        if (this.$isNotEmpty(defaultBaId)) {
          item.baId = defaultBaId
        }
        return item
      })
      return this.extData.incPayeeData
    },
    formatPayeesMoney() { // 格式化所有收款人，不含千分位
      if (this.$isNotEmpty(this.extData.incPayeeData)) {
        this.extData.incPayeeData.forEach(item => {
          item.amount = this.$formatMoney(item.amount)
        })
      }
    },
    initPayeeBa() {
      if (this.$isNotEmpty(this.bas) && this.bas.length === 1) {
        return this.bas[0]
      }
      return { baId: '', baTitle: '', baAmount: 0.00, amount: 0.00 }
    },
    choosePayeeBtn() {
      const refData = { colType: '弹框', dataRef: '选择收款人' }
      this.$refData(undefined, refData,
        () => {}, () => {},
        selectedData => { this.addPayees(selectedData.list) },
        selectedData => {},
        { IS_ENABLED_eq: '是', multiple: true })
    },
    addPayees(payees, clearBaFlag) { // 参照后新添加收款人到单据的收款人列表
      var defaultSettleMode = this.$isEmpty(this.setModeList)
        ? '' : this.setModeList[0].eleName
      const selectedPayees = payees.map(item => {
        var amount = item.amount || ''
        const payee = {
          amountBeforeTax: '',
          ...item,
          basePayeeId: item.id,
          amount: amount,
          settleMode: defaultSettleMode,
          index: null
        }
        if (clearBaFlag !== true) {
          payee.baId = ''
        }
        return payee
      })
      // 如果只新增一个收款人，则其金额默认取剩余的金额
      if (selectedPayees.length === 1 && this.$isEmpty(selectedPayees[0].amount)) {
        var amountLeft = this.$unFormatMoney(this.amountDiff)
        if (this.useTaxRow && this.taxRowName === '收款金额(税前)') {
          // 由于坝光的特殊逻辑，申请金额显示的是税前总和，这里实际扣减应该要计算指标申请金额总和
          amountLeft = parseFloat('0')
          for (let i = 1; i <= 20; i++) {
            const prefix = (i === 1) ? '' : '' + i
            const baApplyAmountItem = this.formFormat.labelOriginColItemMap['指标申请金额' + prefix]
            if (this.$isNotEmpty(baApplyAmountItem)) {
              const value = baApplyAmountItem.dataValue
              const amount = this.$isNumber(value) ? this.$unFormatMoney(value) : 0
              amountLeft += parseFloat(amount)
            }
          }
        }
        selectedPayees[0].amount = amountLeft
      }

      var dataIds = []
      if (this.$isEmpty(this.extData.incPayeeData)) {
        this.extData.incPayeeData = []
      } else if (!this.payeeDataCanRepeat) {
        this.extData.incPayeeData.forEach(function(item, index) {
          dataIds.push(item.id)
        })
      }

      selectedPayees.forEach(item => {
        item.index = this.incPayeeDataIndex++
        if (dataIds.indexOf(item.id) > -1) {
          this.extData.incPayeeData[dataIds.indexOf(item.id)] = item
        } else {
          this.extData.incPayeeData.push(item)
        }
      })

      this.formatPayeesMoney()
      this.syncFormatObjAfterPayeeChanged()
      this.givePayType()
    },
    clearPayees() { // 清空收款人
      this.extData.incPayeeData = []
      this.computedTotal()
      this.syncFormatObjAfterPayeeChanged()
    },
    importPayeeBtn() {
      this.$refs.dlgExcelImp.handleOpen()
    },
    exportPayeeBtn() {
      const payeeIds = []
      this.checkedPayeed.forEach(item => {
        payeeIds.push(item.id)
      })
      var params = {
        doExcelExport: 'doExcelExport',
        exportExcelName: '收款人明细.xls',
        payeeIds: payeeIds.join(',')
      }
      this.$fileDownloadBykey('exportRefTablePayee', params)
    },
    // 批量关联指标
    batchAssociationIndicators() {
      this.$refs.indicatorsDialog.changeDialogVisible(true)
    },
    // 设置对应选中行的指标
    setIndicators(res) {
      this.extData.incPayeeData.forEach(data => {
        if (res.ids.includes(data.id)) {
          data.baId = res.baId
        }
      })
    },
    // 拼接表格数据
    concatExtData(data) {
      this.extData.incPayeeData = this.extData.incPayeeData.concat(data)
    },
    refTableBusinessData(row) {
      const payeeAcctName = row.payeeAcctName
      const payeeAcctNo = row.payeeAcctNo
      const businessCardId = row.businessCardId
      const refData = {
        colType: '弹框',
        dataRef: '选择公务卡消费明细'
      }
      const params = { payeeAcctName: payeeAcctName, payeeAcctNo: payeeAcctNo, businessCardId: businessCardId }
      params.callbackBeforeRefComplete = (selectedData, params, callbackCloseRefDlg, setBtnUnLoad) => {
        callbackCloseRefDlg()
      }
      this.$refData(undefined, refData,
        () => {
        },
        (label, value) => {
        },
        (selectedData, setBtnUnLoad) => {
          // if (row.amount < selectedData.list[0].consumeMoney) {
          //   setBtnUnLoad()
          //   this.$message.error('报销金额不能大于消费金额')
          //   throw new Error('报销金额不能大于消费金额')
          // }
        }, selectedData => {
          row.businessCardId = selectedData.list[0]['ID']
          // v-model不生效 $set更新businessCardValue值
          this.$set(row, 'businessCardValue', selectedData.list[0]['transactionDate'])
          this.$set(row, 'amount', selectedData.list[0]['consumeMoney'])
          params.businessCardId = row.businessCardId
        }, params)
    }
  }
}
</script>

<style lang="scss" scoped>
.ba-payee-list{
  padding: 0px 0px 0px 10px;
  width: 100%;
  height: 50%;
  display: flex;
  flex-direction: column;
  .total-amt{margin-left: 10px;font-size: 14px;}
  .top-btns{
    padding-bottom: 10px;
    margin-left: 0px;
    width: 27%;
    display: flex;
  }
  .bottom-table{ flex: 1;}
  .payee-container /deep/ .el-table .cell{
    padding: 0px 2px !important;
  }
  /deep/ .el-table .warning-row {
    background-color: rgb(255, 43, 43) !important;
  }
  /deep/ .el-table--border .el-table__cell:first-child .cell{
    padding: 0px 5px !important
  }
}
</style>
<style lang="scss">
    .mini-table .ba-payee-list .el-table .el-table-column--selection .cell {padding: 0px 0px !important;}
    .mini-table .ba-payee-list .el-table .cell {padding: 0px 3px;}
    .mini-table .ba-payee-list .el-table input {padding: 0px 3px;}
    .mini-table .ba-payee-list .el-table .el-input__suffix {right: -4px;}
    .mini-table .ba-payee-list .el-table .el-select .el-input .el-select__caret {font-size: 12px;}
    .mini-table .ba-payee-list .el-table .cell .el-date-editor .el-input__prefix { display: none; }
    .payee-count .el-input-group__prepend,
    .amount-difference .el-input-group__prepend,
    .total-amount .el-input-group__prepend {padding: 0px 5px !important;}
    .payee-count input.el-input__inner {width: 55px;padding: 0px 5px;}
    .total-amount input.el-input__inner {width: 130px;padding: 0px 5px;}
    .amount-difference input.el-input__inner{width: 122px;padding: 0px 5px;}
    .payee-container .payeeAcctName,
    .payee-container .payeeMergeName {height: 16px;line-height: 16px;}
    .payee-container input.el-input__inner {font-size: 12px;}
    .payee-container .payeeAmount input.el-input__inner {text-align: right;padding-right: 5px;}
    .payee-container .payeeError {color: #ff5c00 !important;}
</style>
