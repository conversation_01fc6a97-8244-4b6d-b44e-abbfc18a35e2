<template>
    <span/>
</template>

<script>
export default {
  name: 'payee-assembly-func',
  data() {
    return {
      objs: {},
      isneedpayee: true, // 是否需要收款人 默认需要收款人
      attList: [], // 第一次加载的附件数据
      isError: false // 是否发生过错误
    }
  },
  methods: {
    fillObjects(baPayeelist, baseAttachment, eInvoice) {
      this.objs['baPayeelist'] = baPayeelist
      this.objs['baseAttachment'] = baseAttachment
      this.objs['eInvoice'] = eInvoice
    },
    showError(result) {
      const attributes = result.attributes
      var incPayeeErrorNum = Object.keys(attributes).filter(i => i.indexOf('incPayee-') > -1).length
      if (incPayeeErrorNum > 0) {
        Object.entries(attributes).forEach(attribute => {
          const errorKey = attribute[0]
          if (errorKey.indexOf('incPayee-') > -1) {
            const erroeKyArr = errorKey.split('-')
            this.objs.baPayeelist.isError = true
            this.objs.baPayeelist.errorIndex = parseInt(erroeKyArr[2])
            this.objs.baPayeelist.errorInfo = attribute[1]
          }
        })
      }
      this.isError = true
      return incPayeeErrorNum
    },
    initAssembly(meta, mode, formFormat) {
      this.isneedpayee = true // 默认需要收款人

      var labelOriginColItemMap = {}
      for (let i = 0; i < meta.colItems.length; i++) {
        var item = meta.colItems[i]
        labelOriginColItemMap[item.labelOrigin] = item
      }

      // 判断是否含有劳务费要素
      var hasLabour = this.$hasLabour(labelOriginColItemMap, false)
      if (hasLabour) {
        this.objs.baPayeelist.isAmountDisabled = true // 禁用收款金额输入框
      }

      if (meta.cformSettings) {
        meta.cformSettings.forEach(settins => {
          if (settins.optionName === '不需要收款人') {
            this.isneedpayee = false
          }
        })
      }

      this.objs.baPayeelist.isPayeeDisabled = !this.isneedpayee || hasLabour
      if (this.isneedpayee && meta) {
        this.objs.baPayeelist.init(meta, mode, formFormat)
      } else if (meta) {
        // 此时也需要初始化收款人，避免遗留上次制单的收款人
        this.objs.baPayeelist.initJustPayee(meta, formFormat)
        this.objs.baPayeelist.isAmountDisabled = true
      }

      // formFormat实际上是form-free.vue
      // 放入exData中的payeeListObj会在form-extend-inc-base.vue中使用
      if (formFormat && formFormat.exData) {
        formFormat.exData['payeeListObj'] = this.objs.baPayeelist
      }

      this.objs.baseAttachment.$children[0].btAddText = ' 上传附件'
      this.objs.baseAttachment.$children[0].attTypeTableName = 'ELE_BX_ATT_TYPE'
      this.objs.baseAttachment.$children[0].initMeta(meta)

      this.objs.eInvoice.$children[0].btAddText = ' 上传发票'
      this.objs.eInvoice.$children[0].attTypeTableName = 'ELE_BX_INV_TYPE'
      this.objs.eInvoice.$children[0].initMeta(meta)

      // 初始化数据
      this.attList = []
      this.isError = false
    },
    syncBaToPayees(bas, amountSum) {
      this.objs.baPayeelist.syncBaToPayees(bas, amountSum)
    },
    getExtData() {
      var extData = {}
      if (this.isneedpayee) {
        extData.incPayeeData = this.objs.baPayeelist.getPayeesToSave()
      }
      return extData
    },
    fillAtt(dataVo) {
      // 如果保存返回过错误信息 则还原数据
      if (this.isError) {
        dataVo.attList = this.attList
      } else {
        this.attList = dataVo.attList
      }
      // 列表附件
      const attListForFile = this.objs.baseAttachment.$children[0].attList
      const attListForInvoice = this.objs.eInvoice.$children[0].attList
      // 需要删除的附件id
      const attdelIdsForFile = this.objs.baseAttachment.$children[0].delIds
      const attdelIdsForInvoice = this.objs.eInvoice.$children[0].delIds
      const delIds = [].concat(attdelIdsForFile, attdelIdsForInvoice)
      dataVo.attList = [].concat(attListForFile, attListForInvoice)
      dataVo.attList = dataVo.attList.filter(att => {
        if (delIds.indexOf(att.id) === -1 && att.appendixTypeCode !== '999') {
          return att
        }
      })
    }
  }
}
</script>
