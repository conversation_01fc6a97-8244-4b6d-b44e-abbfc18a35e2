<template>
  <div class="common-page multipleTabsAttachmentEinvoice flex-column" id="multipleTabsAttachmentEinvoice">
    <base-attachment class="flex-1" ref="baseAttachment"/>
    <e-invoice class="flex-1" ref="eInvoice" style="margin-top: 20px"/>
  </div>
</template>

<script>
import BaseAttachment from '../file/attachment'
import EInvoice from '../invoice/e-invoice'

export default {
  name: 'attach-einvoice-tab',
  components: { EInvoice, BaseAttachment },
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      attList: [], // 第一次加载的附件数据
      isError: false // 是否发生过错误
    }
  },
  methods: {
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.initAssembly(dataVo, {}, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      // 如果保存返回过错误信息 则还原数据
      if (this.isError) {
        dataVo.attList = this.attList
      } else {
        this.attList = dataVo.attList
      }
      // 列表附件
      const attListForFile = this.$refs.baseAttachment.$children[0].attList
      const attListForInvoice = this.$refs.eInvoice.$children[0].attList

      // 需要删除的附件id
      const attdelIdsForFile = this.$refs.baseAttachment.$children[0].delIds
      const attdelIdsForInvoice = this.$refs.eInvoice.$children[0].delIds
      const delIds = [].concat(attdelIdsForFile, attdelIdsForInvoice)

      dataVo.attList = [].concat(attListForFile, attListForInvoice)
      dataVo.attList = dataVo.attList.filter(att => {
        if (delIds.indexOf(att.id) === -1 && (att.isRelationAtt === '是' || att.appendixTypeCode !== '999')) {
          return att
        }
      })
      // 平台附件数据
      const allAttInfoList = this.$refs.baseAttachment.$children[0].allAttInfoList
      dataVo.extData.allAttInfoList = allAttInfoList
      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
    },
    refreshAttach(formIdArr) { // 刷新附件信息
      this.$refs.baseAttachment.$children[0].refreshAttach(formIdArr)
    },
    initAssembly(meta, mode, formFormat) {
      this.$refs.baseAttachment.$children[0].btAddText = ' 上传附件'
      this.$refs.baseAttachment.$children[0].initMeta(meta)

      this.$refs.eInvoice.$children[0].btAddText = ' 上传发票'
      this.$refs.eInvoice.$children[0].attTypeTableName = 'ELE_BX_INV_TYPE'
      this.$refs.eInvoice.$children[0].initMeta(meta)

      // 初始化数据
      this.attList = []
      this.isError = false
    }
  }
}
</script>
<style>
#multipleTabsAttachmentEinvoice .buttons-normal {
  padding: 0 9px 0 0;
}

</style>
