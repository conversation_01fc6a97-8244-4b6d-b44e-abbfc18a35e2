<template>
  <el-dialog
    append-to-body
    title='导入Excel'
    @close="handleClose"
    :close-on-click-modal="false"
    :visible.sync="dlgVisible">
    <div>
      <div style="padding: 30px 30px 0px 30px;height: 196px;">
        <div>
          <p>进行数据导入操作，请按照以下步骤进行：<br/>
            1. 请下载导入模板：<a @click="downloadTemplate">模板文件</a><br/>
            2. 请按照模板格式，填充好数据并保存<br/>
            3. 选取文件上传<br/>
          </p>
        </div>
        <el-upload
          ref="upload"
          :action='`/api/ic/bifrost/cloud/api.do?apiKey=${apiKey}`'
          :limit="1"
          accept=".xls,.xlsx"
          style="text-align: left;"
          :on-remove="onRemove"
          :on-change="onChange"
          :file-list="fileList"
          align="center"
          :auto-upload="false">
          <el-button @click="clearFiles">选取文件</el-button>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer"
            style="padding: 25px 5px 0px 0px;text-align: right;">
        <el-button @click="dlgVisible = false"
                    style="padding: 9px 32px;">取消</el-button>
        <el-button ref="btUpload" type="primary" @click="fileUpload" :loading="submitBtnLoading">确认上传</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'payeeImportDialog',
  props: {
    isBlock: { type: Boolean, default: true }
  },
  inject: {
    reload: { default: undefined }
  },
  data() {
    return {
      file: undefined,
      fileList: [],
      apiKey: 'importIncPayeeData',
      dlgVisible: false,
      submitBtnLoading: false,
      cformModuleId: ''
    }
  },
  methods: {
    handleOpen(row) {
      if (row) {
        this.cformModuleId = row.cformModuleId
      }
      this.dlgVisible = true
    },
    handleClose() {
      this.dlgVisible = false
      this.clearFiles()
    },
    clearFiles() {
      this.$refs.upload.clearFiles()
      this.fileList = []
      this.file = undefined
    },
    downloadTemplate() {
      var params = { 'isBlock': this.isBlock, 'cformModuleId': this.cformModuleId }
      this.$fileDownloadBykey('downloadIncPayeeTemplate', params)
    },
    onRemove() {
      this.clearFiles()
    },
    onChange(file, fileList) {
      this.file = file
      this.fileList = fileList
    },
    fileUpload() {
      if (this.$isEmpty(this.file)) {
        this.$message.info('请先选取文件')
        return
      }

      const formData = new FormData()
      formData.append('file', this.file.raw)

      this.submitBtnLoading = true
      this.$fileUpload(this.apiKey, formData)
        .then(({ data }) => {
          this.submitBtnLoading = false
          if (data.success) {
            this.$message.success('导入成功')
            const list = data.data?.map(item => ({ ...item, id: item.basePayeeId, ID: item.basePayeeId })) || []
            this.$emit('concatExtData', list)
            this.handleClose()
            this.reload && this.reload()
          } else {
            // this.clearFiles()
            this.$message.error(
              { dangerouslyUseHTMLString: true,
                message: '导入失败' + data.resultMessage })
          }
        })
        .catch((_error) => {
          this.submitBtnLoading = false
          // this.clearFiles()
        })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
