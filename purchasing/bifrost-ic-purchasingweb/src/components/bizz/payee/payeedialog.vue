<template>
  <div id="payeedialog">
    <el-dialog
      :title="'收款人'"
      :visible.sync="ispayeedialog"
      width="640px"
      append-to-body
      :close-on-click-modal='false'
      @close="handleClose(false)">
        <el-form
          ref="payeeForm"
          :model="payeedForm"
          label-width="170px"
          :disabled="payeedetails"
          :rules="rules">
          <el-row style="padding-top: 10px">
              <el-form-item label="收款人全称" prop="payeeAcctName">
                <el-input  :disabled="!disabledArr.includes('收款人') && mode" v-model="payeedForm.payeeAcctName" maxlength="50" placeholder="请输入收款人全称"></el-input>
              </el-form-item>
          </el-row>
          <el-row>
              <el-form-item label="收款人账号" prop="payeeAcctNo">
                <el-input v-model="payeedForm.payeeAcctNo" maxlength="30" placeholder="请输入收款人账号"
                          @input="confirmPayeeAcctNo"></el-input>
              </el-form-item>
          </el-row>
          <el-row>
              <el-form-item label="收款银行行别" prop="payeeAcctBankName" >
                <el-select v-model="payeedForm.payeeAcctBankName" @change="payeeAcctBankNameChange" filterable
                :disabled="!disabledArr.includes('开户银行') && mode"
                           placeholder="请选择收款银行行别">
                  <el-option
                    v-for="item in bankCodeDialogList"
                    :key="item.eleName"
                    :label="item.eleName"
                    :value="item.eleName">
                  </el-option>
                </el-select>
              </el-form-item>
          </el-row>
          <el-row>
            <el-col :span="14">
              <el-form-item label="收款人开户银行" prop="payeeOpenAcctBankName">
                <el-input v-model="payeedForm.payeeOpenAcctBankName" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item prop="payeeOpenAcctBranchBankName" label-width="0px">
                <el-input v-model="payeedForm.payeeOpenAcctBranchBankName" maxlength="30" placeholder="请输入收款人开户支行"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="收款账户类型" prop="recAcctType">
              <el-select v-model="payeedForm.recAcctType" placeholder="请选择收款账户类型">
                <el-option
                  v-for="item in accTypeDialogList"
                  :key="item.eleName"
                  :label="item.eleName"
                  :value="item.eleName">
                </el-option>
              </el-select>
          </el-form-item>

          <el-form-item label="资金往来对象类别" prop="fundTraobjTypeCode">
            <sup-tree :setting="setting"
                      ref="supTree"
                      :is-get-child="true"
                      :isDisabled="payeedetails"
                      :nodes="fundTraobjTypeDialogList"
                      v-model="payeedForm.fundTraobjTypeCode"
                      @getCheckObjs="checkObj"
                      :checked-values="payeedForm.fundTraobjTypeCode?[payeedForm.fundTraobjTypeCode]:[]">
            </sup-tree>
          </el-form-item>

          <el-form-item label="可见范围类型" prop="visibleRange">
            <el-select v-model="payeedForm.visibleRange" placeholder="请选择" @change="visibleRangeChange('optionChange')">
              <el-option
                v-for="(item, index) in visibleRangeOptions"
                :key="index"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="payeedForm.visibleRange == '部门可见' || payeedForm.visibleRange == '指定人员可见'" :label="payeedForm.visibleRange">
            <sup-tree
              ref='classParentTree'
              :isDisabled="payeedetails"
              :setting="treeData.treeSetting"
              :btnSwitch="treeData.btnSwitch"
              :is-get-child="false"
              :nodes="treeData.optionsData"
              :checked-values="checkedValues"
              :isFilterData="true"
              placeholder="选择过滤条件"
              @getCheckObjs="checkClassParent"
            ></sup-tree>
          </el-form-item>

          <el-form-item label="是否启用" v-if="isEnabledItem" class="mbottom-0">
            <el-radio-group v-model="payeedForm.isEnabled">
              <el-radio label='是'>是</el-radio>
              <el-radio label='否'>否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      <template #footer v-if="!payeedetails">
        <el-button
          class="btn-normal"
          type="primary"
          @click="handlepayeedSumbit('payeedForm')"
          >
          确定
        </el-button>
        <el-button class="btn-normal" @click="handleClose(false)"> 取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getUser } from '@/utils/auth'

export default {
  name: 'payeedialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    showIsEnabledItem: {
      type: Boolean,
      default: true
    },
    request: {
      type: Boolean,
      default: false
    }, // 控制是否发起请求
    basePageThis: {
      type: Object,
      default() {
        return {}
      }
    }, // 收款人管理页面会把basepage的this传进来 用于调用basepage的updateTree更新树的数据
    isBasepayee: {
      type: Boolean,
      default: false
    }, // 判断是否是收款人管理页面
    disabledArr: {
      type: Array,
      default: () => []
    },
    mode: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.ispayeedialog = bool
      if (bool) {
        if (this.payeedForm.visibleRange === '部门可见' || this.payeedForm.visibleRange === '指定人员可见') {
          this.visibleRangeChange()
          // this.checkedValues = this.payeedForm.visibleRangeValue.split(',')
          const codeArr = this.payeedForm.visibleRangeValue.split(',')
          const codeObj = {}
          const checkedValues = []
          if (this.$isNotEmpty(codeArr)) {
            codeArr.forEach(code => {
              codeObj[code] = true
            })
            if (this.$isNotEmpty(this.treeData.optionsData)) {
              this.treeData.optionsData.forEach(data => {
                if (this.payeedForm.visibleRange === '部门可见') {
                  if (codeObj[data.code]) {
                    checkedValues.push(data.id)
                  }
                }
                if (this.payeedForm.visibleRange === '指定人员可见') {
                  if (codeObj[data.id]) {
                    checkedValues.push(data.id)
                  }
                }
              })
            }
          }

          this.checkedValues = checkedValues
        }
        if (this.$refs.payeeForm) {
          this.$nextTick(() => {
            this.$refs.payeeForm.clearValidate()
          })
        }
      } else {
        this.treeData = {}
        // this.payeedForm.visibleRangeValue = ''
        this.checkedValues = []
      }
    },
    request: function(newV, oldV) {
      if (newV) {
        this.selectBankCodeAndAccTypeList(this.basePageThis) // 查询收款银行与账户类型
      }
    }
  },
  mounted() {
    // 不是收款人管理页面才执行
    if (!this.isBasepayee) {
      this.selectBankCodeAndAccTypeList() // 查询收款银行与账户类型
    }
    if (this.$store) {
      // 初始化时请求系统人员和部门 用于申请人和申请部门的下拉数据
      this.$store.dispatch('getApplicant')
      this.$store.dispatch('getDept')
    }
  },
  data() {
    return {
      ispayeedialog: this.dialog,
      payeedetails: false,
      isEnabledItem: this.showIsEnabledItem,
      payeedForm: {
        id: '',
        payeeAcctName: '',
        payeeAcctNo: '',
        payeeAcctBankName: '',
        payeeOpenAcctBankName: '',
        payeeOpenAcctBranchBankName: '',
        recAcctType: '',
        fundTraobjTypeCode: '',
        fundTraobjTypeName: '',
        isEnabled: '是',
        visibleRange: '',
        visibleRangeValue: '',
        visibleRangeMessage: ''
      },
      checkedValues: [],
      visibleRangeOptions: ['本人可见', '全部可见', '部门可见', '指定人员可见'],
      bankCodeDialogList: [],
      accTypeDialogList: [],
      treeData: {},
      fundTraobjTypeDialogList: [],
      setting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'eleId',
            pIdKey: 'parentId'
          },
          key: {
            name: 'eleName'
          }
        }
      },
      rules: {
        payeeAcctName: [
          { required: true, message: '请输入收款人全称', trigger: 'blur' }
        ],
        payeeAcctNo: [
          { required: true, message: '请输入收款人账号', trigger: 'blur' }
        ],
        payeeAcctBankName: [
          { required: true, message: '请选择收款银行行别', trigger: 'blur' }
        ],
        payeeOpenAcctBankName: [
          { required: true, message: '请输入收款人开户银行', trigger: 'blur' }
        ],
        payeeOpenAcctBranchBankName: [
          { required: true, message: '请输入收款人开户支行', trigger: 'blur' }
        ],
        recAcctType: [
          { required: true, message: '请选择收款账户类型', trigger: 'blur' }
        ],
        visibleRange: [
          { required: true, message: '请选择可见范围类型', trigger: 'blur' }
        ],
        fundTraobjTypeCode: [
          { required: true, message: '请选择资金往来对象类别', trigger: 'change' }
        ]
      },
      treesetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            // name: 'name'
            id: 'id'
          }
        }
      }
    }
  },
  computed: {
    ...mapState({ applicant: (store) => store.user.applicant, applicationDept: (store) => store.user.applicationDept })
  },
  methods: {
    checkObj(nodes) {
      if (this.$isNotEmpty(nodes)) {
        this.$refs.supTree.visible = false
      }
    },
    confirmPayeeAcctNo: function() {
      this.payeedForm.payeeAcctNo = this.payeedForm.payeeAcctNo.replace(/[\u4e00-\u9fa5]/ig, '')
    },
    selectBankCodeAndAccTypeList(_this = undefined) {
      this.$callApi('selectBankCodeAndAccTypeList', '', result => {
        var accTypeList = []
        var bankCodeList = []
        var fundTraobjTypeList = []
        result.attributes.accTypeList.forEach(item => {
          accTypeList.push({
            value: item.eleName,
            label: item.eleName
          })
        })
        result.attributes.bankCodeList.forEach(item => {
          bankCodeList.push({
            value: item.eleName,
            label: item.eleName
          })
        })
        result.attributes.fundTraobjTypeList.forEach(item => {
          fundTraobjTypeList.push({
            id: item.eleId,
            name: item.eleName,
            parentId: item.parentId,
            code: item.eleCode
          })
        })
        if (this.$parent.bankCodeList instanceof Array) {
          // 如果是数组 则向上抛出方法更新数组
          this.$emit('setBankCodeList', bankCodeList)
          _this.updateTree && _this.updateTree(['收款银行行别:PAYEE_ACCT_BANK_NAME_eq:下拉:#' + JSON.stringify(bankCodeList)])
        }
        if (this.$parent.accTypeList instanceof Array) {
          // 如果是数组 则向上抛出方法更新数组
          this.$emit('setAccTypeList', accTypeList)
          _this.updateTree && _this.updateTree(['收款账户类型:REC_ACCT_TYPE_eq:下拉:#' + JSON.stringify(accTypeList)])
        }
        if (this.$parent.fundTraobjTypeList instanceof Array) {
          // 如果是数组 则向上抛出方法更新数组
          this.$emit('setFundTraobjTypeList', fundTraobjTypeList)
          _this.updateTree && _this.updateTree(['资金往来对象:FUND_TRAOBJ_TYPE_NAME_eq:树:#' + JSON.stringify(fundTraobjTypeList) + ':##' + JSON.stringify(this.treesetting)])
        }
        // this.$parent.bankCodeList = bankCodeList
        // this.$parent.accTypeList = accTypeList
        this.bankCodeDialogList = result.attributes.bankCodeList
        this.accTypeDialogList = result.attributes.accTypeList
        this.fundTraobjTypeDialogList = result.attributes.fundTraobjTypeList
        return true
      })
    },
    handleClose(reloadPayeeListFlag) {
      if (this.$refs.payeeForm) {
        this.$nextTick(() => {
          this.$refs.payeeForm.clearValidate()
        })
      }
      this.$refs.supTree.clearInputBox()
      this.ispayeedialog = false
      this.payeedetails = false
      this.isEnabledItem = true
      if (reloadPayeeListFlag) {
        // 多次执行this.$parent.reload可能会出错,此处加一个只执行一次的判断
        this.$parent.reload(this.payeedForm)
      }
      this.$emit('update:dialog', false)
    },
    handlepayeedSumbit() {
      if (this.$refs.supTree.treeObj.getSelectedNodes()[0]) {
        this.payeedForm.fundTraobjTypeCode = this.$refs.supTree.treeObj.getSelectedNodes()[0].eleCode
        this.payeedForm.fundTraobjTypeName = this.$refs.supTree.treeObj.getSelectedNodes()[0].eleName
      } else {
        this.$message.error('资金往来对象类别不能为空')
        return
      }

      this.$refs.payeeForm.validate((valid) => {
        if (valid) {
          if (this.$isEmpty(this.payeedForm.payeeOpenAcctBranchBankName)) {
            this.$confirm(`银行开户行需具体到支行`, '提示', {
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'warning'
            })
              .then(() => {
              })
              .catch(() => {
              })
          } else {
            if (this.payeedForm.visibleRange === '本人可见') {
              this.payeedForm.visibleRangeValue = getUser().userId
            }
            if (this.payeedForm.visibleRange === '部门可见' && !this.payeedForm.visibleRangeValue) {
              this.$message.error('请选择部门!')
              return
            }
            if (this.payeedForm.visibleRange === '指定人员可见' && !this.payeedForm.visibleRangeValue) {
              this.$message.error('请选择指定人员!')
              return
            }
            this.$callApi('savePayee', this.payeedForm, result => {
              if (result.success) {
                this.$message.success('保存成功!')
                this.$emit('savePayeeInfo', result.data)
                this.payeedForm.id = result.data.id
                this.handleClose(true)
              }
              return true
            })
          }
        }
      })
    },
    setTree() {
      this.$nextTick(() => {
        if (this.$refs.supTree && this.$isEmpty(this.$refs.supTree.showValue)) {
          const node = this.$refs.supTree.treeObj.getNodeByParam('eleCode', this.payeedForm.fundTraobjTypeCode, null)
          this.$refs.supTree.treeObj.selectNode(node)
          this.$refs.supTree.showValue = this.payeedForm.fundTraobjTypeName
        }
      })
    },
    payeeAcctBankNameChange() {
      this.payeedForm.payeeOpenAcctBankName = this.payeedForm.payeeAcctBankName
    },
    visibleRangeChange(value) {
      // 切换申请人 和 申请部门不同的下拉数据
      if (value === 'optionChange') {
        this.payeedForm.visibleRangeValue = ''
      }
      const toggleData = {
        '指定人员可见': {
          optionsData: this.applicant
        },
        '部门可见': {
          optionsData: this.applicationDept
        }
      }
      if (Object.keys(toggleData).includes(this.payeedForm.visibleRange)) {
        this.handleTreeData({
          optionsData: toggleData[this.payeedForm.visibleRange].optionsData,
          isDept: this.payeedForm.visibleRange === '部门可见' // 判断是否是部门
        })
      }
    },
    handleTreeData(data) {
      const { optionsData } = data
      const treeData = {
        treeSetting: {
          check: {
            enable: true
          },
          data: {
            simpleData: {
              enable: true,
              idKey: 'id',
              pIdKey: 'parentId'
            },
            key: {
              name: 'name'
            }
          }
        },
        btnSwitch: {
          showEdit: false,
          showRefresh: false,
          showExpandAll: false
        },
        optionsData: this.$clone(optionsData) // applicationDept || applicant
      }
      this.treeData = treeData
    },
    checkClassParent(nodes) {
      // 目前只有2种情况 '指定人员可见' || '部门可见'
      this.payeedForm.visibleRangeValue = nodes.filter(node => !node.isParent)
        .map(item => this.payeedForm.visibleRange === '指定人员可见' ? item.id : item.code).toString()
      this.payeedForm.visibleRangeMessage = nodes.filter(node => !node.isParent)
        .map(item => this.payeedForm.visibleRange === '指定人员可见' ? item.name : item.name?.split(' ')[1]).toString()
    }
  }
}
</script>

<style lang="scss" scoped>
  .mbottom-0 {
    margin-bottom: 0!important;
  }
  /deep/ .el-dialog__body {
    // margin-left: -38px;
  }
</style>
