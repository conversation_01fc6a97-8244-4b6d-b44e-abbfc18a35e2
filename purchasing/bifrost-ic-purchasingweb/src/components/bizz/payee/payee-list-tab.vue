<template>
  <div class="common-page multipleTabsPayees" id="multipleTabsPayees">
    <div class="baseListTable">
      <ba-payee-list :isTabAplication=true ref="baPayeelist"/>
    </div>
  </div>
</template>

<script>
import BaPayeeList from './payee-list'
export default {
  name: 'payee-list-tab',
  components: { BaPayeeList },
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      formFormat: {},
      isneedpayee: true
    }
  },
  mounted() {
    // 绑定全局添加收款人事件,在发票组件中有调用
    window.$event.$on('addPayees', result => {
      if (result.success) {
        if (result.data) {
          this.$refs.baPayeelist.payeeDataCanRepeat = false
          // 同步收款人信息
          this.$refs.baPayeelist.addPayees(result.data)
        }
        if (result.attributes) {
          // 同步发票的单据摘要信息等
          this.setValue(result.attributes)
        }
      }
    })
  },
  methods: {
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.formFormat = formCanvasObj.$refs.formFormat
      this.initAssembly(dataVo, baseListFormObj, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      if (this.isneedpayee) {
        dataVo.extData.incPayeeData = this.$refs.baPayeelist.getPayeesToSave()
      }
      return dataVo
    },
    // 给表单赋值
    setValue(data) {
      for (const key in data) {
        if (this.formFormat.labelColItemMap && this.formFormat.labelColItemMap.hasOwnProperty(key)) {
          const newVal = (this.formFormat.getValue(key) && this.formFormat.getValue(key).length > 0)
            ? this.formFormat.getValue(key) + ';' + data[key] : data[key]
          if (newVal.length < 255) {
            this.formFormat.setValue(key, newVal)
          } else {
            this.formFormat.setValue(key, data[key])
          }
        }
      }
    },
    showError(result) { // 后端校验本组件错误
      const attributes = result.attributes
      var incPayeeErrorNum = Object.keys(attributes).filter(i => i.indexOf('incPayee-') > -1).length
      if (incPayeeErrorNum > 0) {
        Object.entries(attributes).forEach(attribute => {
          const errorKey = attribute[0]
          if (errorKey.indexOf('incPayee-') > -1) {
            const erroeKyArr = errorKey.split('-')
            this.$refs.baPayeelist.isError = true
            this.$refs.baPayeelist.errorIndex = parseInt(erroeKyArr[2])
            this.$refs.baPayeelist.errorInfo = attribute[1]
          }
        })
      }
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors' && result.attributes[key]) {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
    },
    syncBaToPayees(bas, amountSum) {
      this.$refs.baPayeelist.syncBaToPayees(bas, amountSum)
    },
    initAssembly(meta, mode, formFormat) {
      this.isneedpayee = true // 默认需要收款人

      var labelOriginColItemMap = {}
      for (let i = 0; i < meta.colItems.length; i++) {
        var item = meta.colItems[i]
        labelOriginColItemMap[item.labelOrigin] = item
      }

      // 判断是否含有劳务费要素
      var hasLabour = this.$hasLabour(labelOriginColItemMap, false)
      if (hasLabour) {
        this.$refs.baPayeelist.isAmountDisabled = true // 禁用收款金额输入框
      }

      if (meta.cformSettings) {
        meta.cformSettings.forEach(settins => {
          if (settins.optionName === '不需要收款人') {
            this.isneedpayee = false
          }
        })
      }

      this.$refs.baPayeelist.isPayeeDisabled = !this.isneedpayee || hasLabour
      if (this.isneedpayee && meta) {
        this.$refs.baPayeelist.init(meta, mode, formFormat)
      } else if (meta) {
        // 此时也需要初始化收款人，避免遗留上次制单的收款人
        this.$refs.baPayeelist.initJustPayee(meta, formFormat)
        this.$refs.baPayeelist.isAmountDisabled = true
      }

      // formFormat实际上是form-free.vue
      // 放入exData中的payeeListObj会在form-extend-inc-base.vue中使用
      if (formFormat && formFormat.exData) {
        formFormat.exData['payeeListObj'] = this.$refs.baPayeelist
      }
    }
  }
}
</script>
<style scoped>
.multipleTabsPayees .el-table--small { font-size: 14px; height: calc(100% - 0px); }
#multipleTabsPayees .baseListTable .ba-payee-list { height: 100% !important; padding: 0px;}
</style>
