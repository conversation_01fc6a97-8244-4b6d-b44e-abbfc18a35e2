<template>
  <div id="payeelistdialog">
    <el-dialog
      append-to-body
      :title="'报账单支付'"
      :visible.sync="ispayeelistdialog"
      width="40%"
      :close-on-click-modal='false'
    >
      <div class="bottom-table">
            <el-table border :data="incPayeeData" @selection-change="handleSelectionChange" style="width: 100%">
                <!-- <el-table-column  show-overflow-tooltip type="selection" align="center"></el-table-column> -->
                <el-table-column show-overflow-tooltip align="center" prop="payeeAcctName" label="收款人"></el-table-column>
                <el-table-column show-overflow-tooltip align="center" prop="payeeOpenAcctBankName" label="开户银行"></el-table-column>
                <el-table-column show-overflow-tooltip align="center" prop="payeeAcctNo" label="账号"></el-table-column>
                <el-table-column show-overflow-tooltip  align="center" prop="amt" label="金额" width="110"></el-table-column>
          </el-table>
          </div>
      <template #footer>
        <el-button v-if="incBillPay.payStatus === '未支付'"
          type="primary"
          @click="handlepayeedSumbit('payeedForm')"
        >
          确认支付
        </el-button>
        <el-button @click="handleClose('payeedForm')"> 取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'payeelistdialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    incBillPayData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    dialog(bool) {
      this.ispayeelistdialog = bool
    },
    incBillPayData(newVal, oldVal) {
      this.incBillPay = newVal
      this.selectIncPayeeList(this.incBillPay.billId)
    }
  },
  data() {
    return {
      ispayeelistdialog: this.dialog,
      incBillPay: {},
      incPayeeData: []
    }
  },
  methods: {
    handleSelectionChange(rows) {

    },
    handleClose() {
      this.ispayeelistdialog = false
      this.$emit('update:dialog', false)
    },
    selectIncPayeeList(applyBillId) {
      this.$callApiParams('selectIncPayeeList', { APPLY_BILL_ID_eq: applyBillId }, result => {
        if (result.success) {
          this.incPayeeData = result.data
        }
        return true
      })
    },
    handlepayeedSumbit() {
      this.incBillPay.payAmount = this.incBillPay.leftAmount
      this.incBillPay.leftAmount = 0
      this.incBillPay.payStatus = '已支付'
      this.$callApi('saveIncBillPay', this.incBillPay, result => {
        if (result.success) {
          this.$message.success('支付成功!')
          this.ispayeelistdialog = false
          this.$parent.reload()
        }
        return true
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
