<template>
  <el-dialog
    append-to-body
    :title="'授权记录'"
    :visible.sync="isEinvoiceAuthRecorcDialog"
    width="45%"
    :close-on-click-modal='false'
    @close="handleClose">
    <div id="einvoiceAuthRecorcDialog">
      <b-curd ref="einvoiceAuthRecorcList"></b-curd>
    </div>
  </el-dialog>
</template>
<script>

export default {
  name: 'einvoice-auth-record-dialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isEinvoiceAuthRecorcDialog = bool
      if (this.isEinvoiceAuthRecorcDialog) {
        this.$nextTick(() => {
          this.init(this.billId)
        })
      }
    }
  },
  data() {
    return {
      billId: '',
      isEinvoiceAuthRecorcDialog: this.dialog
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.isEinvoiceAuthRecorcDialog) {
        this.init(this.billId)
      }
    })
  },
  methods: {
    init(billId) {
      this.$refs.einvoiceAuthRecorcList.init({
        params: {
          dataApiKey: 'selectEinvoiceAuthRecordPageData',
          EINVOICE_FOLDER_ID_eq: billId
        },
        showPager: false,
        noSelection: true,
        orderNumber: { label: '', isShow: true },
        hideCurdButton: ['新增', '删除', '修改', '详情']
      })
    },
    handleClose() {
      this.isEinvoiceAuthRecorcDialog = false
      this.$emit('update:dialog', false)
      this.$parent.reload()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .buttons-normal {
  padding: 0px !important;
}

#einvoiceAuthRecorcDialog {
  height: 500px;
  ::v-deep.main-border {
    padding-top: 0;
  }
}
</style>
