<template>
  <!-- 用于放金额的输入框 -->
  <el-input
    ref="inputMoney"
    v-model="displayMoney"
    autocomplete="off"
    :class="
      isShowUnit
        ? `money-input isShowUnit ${unitName.length === 2 ? 'two' : ''}`
        : 'money-input'
    "
    :disabled="disabled"
    :readonly="readonly"
    :clearable="clearable"
    :placeholder="placeholder"
    @change="handleCg"
    @clear="handleClear"
    @blur="inputBlur"
    @focus="inputfocus"
    @input="handleInput"
  >
    <template v-if="isUnitShow" slot="append">{{ unitName }}</template>
  </el-input>
</template>
<script>
export default {
  name: 'InputMoney',
  props: {
    // 双向绑定的值
    value: {
      type: [String, Number],
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 是否能清空
    clearable: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ''
    },
    // 保留N位小数
    toFixed: {
      type: Number,
      default: 2
    },
    // 是否显示单位
    isShowUnit: {
      type: Boolean,
      default: true
    },
    // 是否显示单位solt
    isUnitShow: {
      type: Boolean,
      default: true
    },
    // 金额单位名
    unitName: {
      type: String,
      default: '元'
    },
    // 是否能输入负数
    isMinus: {
      type: Boolean,
      default: false
    },
    // 最大输入长度
    maxLength: {
      type: Number,
      default: 14
    },
    // 是否显示0
    isEmtryShowZero: {
      type: Boolean,
      default: false
    },
    // 对齐方式
    textAlign: {
      type: String,
      default: 'right'
    },
    // 是否显示默认值
    isEmtryShowDefault: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      displayMoney: '',
      oldValue: '',
      isInput: false // 是否正在输入
    }
  },
  mounted() {
    // 处理对齐方式
    const inputInner = document.getElementsByClassName('money-input')[0].children[0]
    inputInner.style.textAlign = this.textAlign
  },
  computed: {
    isDispMix: {
      get() {
        return this.displayMoney && this.displayMoney[0] === '-'
      },
      set(val) {
        this.isDispMix = val
      }
    }
  },
  methods: {
    /**
     * Parse the Money to string 千位符+小数点两位数
     * @param {(string|number)} num
     * @returns {string}
     */
    parseMoney(num) {
      const resNum = num ? (+num).toFixed(this.toFixed) : '0.00'
      return (
        resNum &&
        resNum.toString().replace(/(\d)(?=(\d{3})+\.)/g, ($1, $2) => {
          return $2 + ','
        })
      )
    },
    inputfocus(event) {
      this.oldValue = this.value
      this.$emit('focus')
      event.currentTarget.select()
    },
    inputBlur() {
      this.$emit('blur', this.oldValue, this.value ? this.value : this.isEmtryShowDefault ? this.value = '0.00' : '')
    },
    focus() {
      this.$refs.inputMoney.focus()
    },
    handleClear() {
      this.$emit('input', '')
      this.$emit('clear')
    },
    handleCg(val) {
      if (val === '') return // 空字符就不格式化
      // 失去焦点的时候 格式化一遍
      const res = isNaN(val.replace(/,/g, '')) ? 0 : val.replace(/,/g, '')
      this.displayMoney = this.parseMoney(res)
      // this.isInput = true
      this.$emit('input', this.displayMoney.replace(/,/g, '')) // 去除逗号，返回正常的数字
    },
    handleInput(val) {
      const startPos = this.$refs.inputMoney.$el.children[0].selectionStart // input 第0个字符到选中的字符
      // const endPos = this.$refs.inputMoney.$el.children[0].selectionEnd // 选中的字符到最后的字符
      if (val === '') {
        // 空字符就另做处理
        this.displayMoney = ''
        this.$emit('input', this.displayMoney.replace(/,/g, ''))
        return
      }
      if (val === '.') {
        this.displayMoney = '0.'
        this.$emit('input', this.displayMoney.replace(/,/g, ''))
        return
      }
      if (this.isMinus && (val === '0-' || val === '-')) {
        this.displayMoney = '-'
        this.$emit('input', this.displayMoney.replace(/,/g, ''))
        return
      }
      if (this.isMinus && val === '-0') {
        this.displayMoney = '-0'
        this.$emit('input', this.displayMoney.replace(/,/g, ''))
        return
      }
      if ((val + '').split('.').length - 1 > 1) {
        const inputIntVal = (val + '').split('.')[0] // inputIntVal 小数点前的整数
        if (inputIntVal.length > this.maxLength + (this.isDispMix ? 1 : 0)) {
          this.displayMoney =
            inputIntVal.slice(0, this.maxLength + (this.isDispMix ? 1 : 0)) +
            '.' +
            (val + '').split('.')[1].slice(0, this.toFixed) // 保留N位小数
        } else {
          this.displayMoney =
            (val + '').split('.')[0] +
            '.' +
            (val + '').split('.')[1].slice(0, this.toFixed) // 保留N位小数
        }
      } else if ((val + '').split('.').length - 1 === 1) {
        const inputIntVal = (val + '').split('.')[0].replace(/,/g, '') // inputIntVal 小数点前的整数
        if (inputIntVal.length > this.maxLength + (this.isDispMix ? 1 : 0)) {
          this.displayMoney =
            inputIntVal
              .slice(0, this.maxLength + (this.isDispMix ? 1 : 0))
              .replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') +
            '.' +
            (!isNaN(+(val + '').split('.')[1])
              ? (val + '').split('.')[1].slice(0, this.toFixed)
              : '') // 保留N位小数 + 去除非数字
        } else {
          this.displayMoney =
            (val + '')
              .split('.')[0]
              .replace(/,/g, '')
              .replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') +
            '.' +
            (!isNaN(+(val + '').split('.')[1])
              ? (val + '').split('.')[1].slice(0, this.toFixed)
              : '') // 保留N位小数 + 去除非数字
        }
      } else {
        const inputIntVal = (val + '').replace(/,/g, '')
        if (inputIntVal.length > this.maxLength + (this.isDispMix ? 1 : 0)) {
          const res = !isNaN(
            +inputIntVal.slice(0, this.maxLength + (this.isDispMix ? 1 : 0))
          )
            ? +inputIntVal.slice(0, this.maxLength + (this.isDispMix ? 1 : 0))
            : this.value // 去除非数字
          this.displayMoney = (res + '').replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
        } else {
          const res = !isNaN(+(val + '').replace(/,/g, ''))
            ? +(val + '').replace(/,/g, '')
            : this.value // 去除非数字
          this.displayMoney = (res + '').replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
        }
      }
      if (!this.isMinus) {
        this.displayMoney = this.displayMoney.replace(/-/g, '')
      }
      this.isInput = true
      // this.$emit('input', this.displayMoney.replace(/,/g, '')) // 去除逗号，返回正常的数字
      const resValue = isNaN(+(this.displayMoney.replace(/,/g, '') + ''))
        ? 0
        : this.displayMoney.replace(/,/g, '')
      this.$emit('input', resValue) // 去除逗号，返回正常的数字
      // 重新定光标
      this.$nextTick(() => {
        const add = this.displayMoney.length - val.length
        this.$refs.inputMoney.$el.children[0].selectionStart = startPos + add
        this.$refs.inputMoney.$el.children[0].selectionEnd = startPos + add
      })
    }
  },
  watch: {
    value: {
      handler(val, oldVal) {
        if (!this.isInput) {
          if (val === '0-' || val === '-') {
            this.displayMoney = '-'
            return
          }
          if (val === '-0') {
            this.displayMoney = '-0'
            return
          }
          if (!val) {
            this.displayMoney = this.isEmtryShowZero
              ? this.parseMoney(+val)
              : ''
            return
          }
          this.displayMoney = this.parseMoney(+val)
        }
        this.isInput = false
      },
      immediate: true
    }
    // value: {
    //   handler(val, old) {
    //     console.log(val, this.isInput, '????')
    //     if (!this.isInput) {
    //       this.displayMoney = this.parseMoney(+val)
    //     }
    //     this.isInput = false
    //   },
    //   immediate: true,
    //   deep: true
    // }
  }
}
</script>
<style lang="scss">
.money-input {
  .el-input__suffix-inner {
    height: 100%;
    display: inline-block;
    vertical-align: top;
  }
  .el-input__inner {
    text-align: right;
    &::-webkit-input-placeholder {
      /* WebKit browsers */
      text-align: left;
    }

    &::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      text-align: left;
    }

    &:-ms-input-placeholder {
      /* Internet Explorer 10+ */
      text-align: left;
    }
  }
  .suffix-font {
    position: relative;
    height: 100%;
    box-sizing: border-box;
    padding: 8px 0;
    display: inline-block;
    color: #84878e;
    line-height: 100%;
    top: 0;
    vertical-align: top;
  }
  &.isShowUnit {
    .el-input__inner {
      padding-right: 5px;
    }
    .el-input__clear {
      width: 15px;
    }
  }
  &.isShowUnit.two {
    .el-input__inner {
      padding-right: 48px;
    }
  }
}
// .el-form-item__content .money-input .suffix-font {
//   padding: 0;
// }
</style>
