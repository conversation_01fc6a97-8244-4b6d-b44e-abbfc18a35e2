<template>
  <div class="refTablePager">
    <el-pagination
            background
            small
            layout="prev, pager, next, sizes, total, jumper"
            :page-sizes="[20, 50]"
            :page-size="size"
            :pager-count="5"
            :total="dataTotal"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"/>
  </div>
</template>
<script>
export default {
  name: 'ref-table-pager',
  data() {
    return {
      current: 1,
      size: 20,
      dataTotal: 0,
      refDataObj: undefined
    }
  },
  methods: {
    init(refDataObj, dataTotal) {
      this.dataTotal = dataTotal
      this.refDataObj = refDataObj
    },
    handleCurrentChange(cpage) {
      this.current = cpage
      this.refreshData()
    },
    handleSizeChange(psize) {
      this.size = psize
      this.refreshData()
    },
    refreshData() {
      this.refDataObj.refreshData()
    }
  }
}
</script>
<style lang="scss">
</style>
