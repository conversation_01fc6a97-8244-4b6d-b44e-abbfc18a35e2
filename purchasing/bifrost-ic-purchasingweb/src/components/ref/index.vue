<template>
  <div class="refContent">
    <ref-table ref="refTable" v-if="refType === 'table'"/>
    <ref-tree ref="refTree" v-if="refType === 'tree'"/>
  </div>
</template>

<script>
import RefTable from './ref-table'
import RefTree from './ref-tree'
export default {
  name: 'ref-data',
  components: { RefTree, RefTable },
  data() {
    return {
      refType: '', // 参照类别：table/tree
      params: {}, // 参数对象
      dlg: {}, // 对话框
      multiple: false // 是否可以多选
    }
  },
  created() {
    this.$onEvent(this, {
      refTableRefreshData: (tab, filterText) => {
        // 表格关键字查询或者切换页签触发重新查询时，将页码改为第一页
        this.params.tab = tab
        this.params.filterText = filterText
        this.refreshData(() => {
          this.params.tab = ''
          this.params.filterText = ''
        }, undefined, { current: 1 })
      }
    })
  },
  methods: {
    init(dlg, params) {
      this.dlg = dlg
      this.params = params
      this.dlg.pagerContentId = 'ref-table-pager'

      this.refreshData(undefined, true)
    },
    refreshData(callback, isFirstInit, exParams) {
      exParams = exParams || {}
      var ps = this.$clone(this.params)
      ps = Object.assign(ps, exParams)

      // 如果明确是表格参照时，处理分页参数和关键字
      var size = 20
      var current = 1
      if (this.$isNotEmpty(this.refType) && !this.isTree()) {
        if (this.$isEmpty(ps.filterText)) {
          ps.filterText = this.getRefObj().filterText
        }

        if (this.$isNotEmpty(this.dlg.$refs.pagerContent)) {
          size = this.dlg.$refs.pagerContent.size
          current = this.dlg.$refs.pagerContent.current
        }
      }

      if (this.$isEmpty(ps.size)) {
        ps.size = size
      }
      if (this.$isEmpty(ps.current)) {
        ps.current = current
      }
      if (this.$isNotEmpty(ps.checkedData) && isFirstInit) {
        ps.checkIds = ps.checkedData.join(',')
      }

      this.$callApiParams(this.params.apiKey, ps,
        result => {
          this.refType = this.$isEmpty(result.attributes.columns) ? 'tree' : 'table'
          this.$nextTick(() => {
            var dlgWidth = result.attributes.width
            var dlgHeight = result.attributes.height
            this.$setDlgSize(this.dlg, 'globalDialog', dlgWidth, dlgHeight)

            var exParams = result.attributes.exParams || {}
            var paramsAll = Object.assign(this.params, exParams)
            var isNoPager = result.attributes.isNoPager
            paramsAll.dlgWidth = dlgWidth
            paramsAll.dlgHeight = dlgHeight
            this.getRefObj().init(result, paramsAll, this.dlg, isFirstInit)

            // 树数据或者后端数据没有分页时，不显示分页
            if (this.isTree() || (result.data && !result.data.page) || isNoPager) {
              this.dlg.pagerContentId = ''
              if (this.isTree()) {
                this.getRefObj().initSelector()
              }
            } else {
              if (exParams.selectPageAsync === '1') {
                // 使用了分页查询异步的处理
                this.selectPageCountAsync(ps)
              } else {
                this.$nextTick(() => {
                  this.dlg.$refs.pagerContent.init(this, result.data.page.total)
                })
              }
            }

            if (callback) {
              callback(result)
            }
          })
          return true
        }, result => {
          if (isFirstInit) {
            this.$event(this, 'refDataInitFailed')
          }
        })
    },
    // 对于异步请求获取总页数的处理,此处的后台接口只返回总页数,不会查询每页的记录的详细信息
    selectPageCountAsync(reqParams) {
      this.$callApiParams(
        this.params.apiKey,
        Object.assign(reqParams, { 'selectCount': '1' }),
        (result) => {
          this.$nextTick(() => {
            this.dlg.$refs.pagerContent.init(this, result.data.page?.total || 0)
          })
          return true
        },
        (result) => {
          this.loading = false
        }
      )
    },
    isTree() {
      return (this.refType === 'tree')
    },
    getRefObj() {
      return this.isTree() ? this.$refs.refTree : this.$refs.refTable
    },
    onDlgClose(params) {
      if (this.getRefObj() &&
              typeof this.getRefObj().onDlgClose === 'function') {
        this.getRefObj().onDlgClose(params)
      }
    },
    getDataOK() {
      var data = this.getRefObj().getDataOK()
      var info = data.errorItemName
      if (this.$isEmpty(this.params.isPrompt) &&
        (this.$isEmpty(data) || this.$isEmpty(data.list) || this.$isNotEmpty(info))) {
        var errorItemName = this.$isEmpty(data.errorItemName)
          ? '数据' : data.errorItemName
        var error = this.params.multiple ||
          (this.$isNotEmpty(this.params.isMultipleChoice) &&
            this.params.isMultipleChoice.indexOf(data.tab) > -1)
          ? '请选择至少一条' : '请选择一条'
        return { error: error + errorItemName }
      }
      return data
    }
  }
}
</script>
<style lang="scss">
  .refContent { height: 100%; }
</style>
