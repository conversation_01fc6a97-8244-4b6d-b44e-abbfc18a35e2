<template>
  <div class="refTable classifytree-filter-input">
    <el-input
      ref="searchInput"
      v-show="result.attributes.search"
      :placeholder="filterPlaceholder"
      prefix-icon="el-icon-search"
      v-model="filterText"
      clearable
      class="refTableSearch"
      @change="changeSearch"/>
    <p class="hiddenPlaceholder" ref="hiddenPlaceholder">{{ filterPlaceholder }}</p>
    <el-tabs ref="refTab" v-model="activeTab" @tab-click="tabClick">
      <el-tab-pane :label="tab" :name="tab" :key="tabIndex" v-for="(tab, tabIndex) in result.attributes.tabs">
        <!-- class="mini-table" mini表格 -->
        <el-table
          ref="refTable"
          :data="result.attributes.rows"
          :style="refMainTableStyle"
          :row-class-name="tableRowClassName"
          height="100%"
          @selection-change="rowChecked"
          @row-dblclick="rowDblclick"
          @row-click='(row) => clickRow(row, tabIndex)'
          border

          style="width: 100%">
          <el-table-column type="selection" width="35"/>
          <el-table-column
            v-if="showIndex"
            label="序号"
            type="index"
            width="50">
          </el-table-column>
          <el-table-column
            v-for="(item, index) in composeColumns"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
            :align="item.align"
            :sortable="item.sortable"
            header-align="center"
            show-overflow-tooltip
            :key='index'>
            <el-table-column
              v-for="(item, index) in item.children"
              :label="item.label"
              :prop="item.prop"
              :width="item.width"
              :align="item.align"
              :sortable="item.sortable"
              header-align="center"
              show-overflow-tooltip
              :key='index'>
            </el-table-column>
            <template slot-scope="scope">
              <slot name="tableRowSlot" :slotScope="scope">
                {{formatColData(scope.row, scope.column)}}
              </slot>
            </template>
          </el-table-column>
        </el-table>
        <div :style="refExtendStyle">
          <component ref="refExtend" :singleChoice="singleChoice" :is="refExtendId"/>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import $ from 'jquery'
import uniqBy from 'lodash/uniqBy'

export default {
  name: 'ref-table',
  data() {
    return {
      filterText: '', // 查询关键字
      filterPlaceholder: '',
      activeTab: '', // 当前页签
      dataTab: '', // 表格数据对应的页签
      checkedRow: undefined, // 当前勾选的记录，单选时使用
      theInitCheckData: [], // 记录初始的勾选数据，提供给扩展功能使用，比如合同参照
      multiple: false, // 是否可以多选
      isMultipleChoice: [], // 可多选的tab
      result: this.resetResult(),
      columnTypes: {},
      refTableExtendObj: undefined,
      refExtendId: '',
      refMainTableStyle: '',
      refExtendStyle: '',
      tableSelectMap: {},
      singleChoice: true,
      hiddenPlaceholderWidth: 0 // 提示字体宽度
    }
  },
  computed: {
    composeColumns() {
      return this.result.attributes.columns?.filter(item => item.label !== '序号')
    },
    showIndex() {
      return this.result.attributes.columns?.filter(item => item.label === '序号').length
    },
    composeActiveIndex() {
      if (this.result.attributes.tabs) {
        return this.result.attributes.tabs.findIndex(item => item === this.activeTab)
      }
      return ''
    }
  },
  methods: {
    addSelectList() {
      let selects = this.tableSelectMap[this.activeTab] || []
      const itemMap = {
        '选择部门指标': '部门指标',
        '选择事前': '事前申请单'
      }
      selects = selects.filter((item) => item.formType === itemMap[this.activeTab])
      const list = uniqBy([...selects, ...this.result.attributes.rows], 'ID')
      this.result.attributes.rows = list
    },
    retainSelect(list) {
      let checkedRows = []
      if (!list) {
        const table = this.getTable()
        checkedRows = this.$getTableSelection(table)
      } else {
        checkedRows = list
      }
      this.tableSelectMap[this.activeTab] = checkedRows
    },
    tableRowClassName({ row }) {
      if (this.$getTableCheckedIds(this.$refs.refTable[this.composeActiveIndex]).includes(this.$getRowId(row))) {
        return 'checkedTR'
      }
      return ''
    },
    reset() {
      this.result = this.resetResult()
    },
    resetResult() { // 重置数据对象
      return {
        attributes: {
          tabs: [''],
          columns: []
        },
        data: [],
        current: 1,
        size: 20,
        dataTotal: 0,
        dlg: undefined,
        params: undefined
      }
    },
    init(result, params, dlg, isFirstInit) {
      this.dlg = dlg
      dlg.setTitle('') // 表格参照不显示弹框标题
      params = params || {}
      this.params = params

      var checkedData = params.checkedData || []
      var tab = params.tab
      this.multiple = params.multiple
      if (this.params['多对多模式'] !== undefined) { // 多对多模式模式时肯定可以多选
        this.multiple = true
      }

      this.isMultipleChoice = params.isMultipleChoice || []
      this.result = this.resetResult()
      this.theInitCheckData = checkedData

      // 处理参照内容扩展
      this.refExtendId = ''
      this.refMainTableStyle = ''
      this.refExtendStyle = ''
      if (params.getRefTableExtend) {
        this.refTableExtendObj = params.getRefTableExtend()
        // 扩展的表格 是否支持多选 有singleChoice值按照singleChoice值来 没有默认是单选
        this.singleChoice = params.singleChoice === undefined ? true : params.singleChoice
        if (this.refTableExtendObj) {
          this.refExtendId = this.$getComponentName(this.refTableExtendObj)
          this.$nextTick(() => {
            var refExtend = this.getRefExtend()
            if (refExtend) {
              if (params?.exParams) {
                refExtend?.handleExParams?.(params?.exParams)
              }
              if (refExtend.initRef) {
                refExtend.initRef(this)
              }
            }
            this.refreshExtendContent()
          })
        }
      }

      this.$nextTick(() => {
        var $tabBar = $('.globalDialog .el-tabs__nav-scroll')
        var $search = $('.globalDialog .refTableSearch')
        var filterWidth = params.filterWidth || '160px'
        this.filterPlaceholder = params.filterPlaceholder || 'Enter搜索'

        this.$nextTick(() => {
          // 自适应placeholder宽度
          this.hiddenPlaceholderWidth = this.$refs.hiddenPlaceholder?.getBoundingClientRect()?.width
          const noUnitFilterWidth = params.filterWidth?.includes('px') ? params.filterWidth?.replace('px', '') : params.filterWidth
          if (!isNaN(noUnitFilterWidth)) {
            if (parseFloat(noUnitFilterWidth) < this.hiddenPlaceholderWidth) {
              $search.css('width', this.hiddenPlaceholderWidth + 'px')
            }
          }
        })

        $search.css('float', 'left')
        $search.css('width', filterWidth)
        $search.css('margin-left', '25px')
        $search.css('height', '40px')
        $search.css('line-height', '40px')
        $search.appendTo($tabBar)

        this.result = result
        this.result.attributes.rows.forEach(row => { // 补齐ID或id
          var value = this.$getRowId(row)
          row.ID = value
          row.id = value
        })

        this.activeTab = this.$isEmpty(tab) ? result.attributes.tabs[0] : tab
        this.dataTab = this.activeTab
        if (isFirstInit) { // 初始化时，初始化tableSelectMap
          const attributes = result.attributes || {}
          const checkedRows = attributes.checkedRows || []
          this.tableSelectMap = {}
          this.retainSelect(checkedRows)
        }
        this.addSelectList()
        const oldChecks = this.tableSelectMap[this.activeTab] || []
        let checkedIds = oldChecks.map(item => item.ID)
        checkedIds = Array.from(new Set([...checkedIds, ...checkedData]))
        if (checkedIds.length > 0) { // 处理回填
          var dataMap = {}
          this.result.attributes.rows.forEach(row => { dataMap[row.ID] = row })
          this.$nextTick(() => {
            var table = this.getTable()
            var refExtend = this.getRefExtend()
            checkedIds.forEach(id => {
              if (refExtend && refExtend.resolveDataId) {
                id = refExtend.resolveDataId(id)
              }
              if (this.$isNotEmpty(dataMap[id])) {
                table.toggleRowSelection(dataMap[id], true)
              }
            })
          })
        }

        this.columnTypes = {}
        if (result.attributes && result.attributes.columns) {
          result.attributes.columns.forEach(col => {
            this.columnTypes[col.prop] = col.colType
            this.columnTypes[col.label] = col.colType
          })
        }

        this.focusSearchInput()
      })
    },
    focusSearchInput() {
      this.$nextTick(() => {
        this.$refs.searchInput.focus()
      })
    },
    formatColData(row, column) {
      const colValue = row[column.property] // 列原始值
      if (this.columnTypes[column.property] === '金额') {
        return this.$formatMoney(colValue)
      }
      return colValue
    },
    tabClick(tab) {
      if (tab.name !== this.dataTab) { // 点击不同的tab，加载相应数据
        this.tableSelectMap = {}
        this.filterText = ''
        this.$event(this, 'refTableRefreshData', tab.name, this.filterText)
      }
    },
    getTable() {
      for (let i = 0; i < this.result.attributes.tabs.length; i++) {
        var tab = this.result.attributes.tabs[i]
        if (tab === this.activeTab) {
          return this.$refs.refTable[i]
        }
      }
    },
    rowChecked(rows) {
      var table = this.getTable()
      var checkedRows = this.$getTableSelection(this.getTable())
      var theSelectedRow = (checkedRows.length > 0) ? checkedRows[0] : undefined
      if (!this.multiple && !(this.isMultipleChoice.indexOf(this.activeTab) > -1)) { // 单选处理
        if (this.checkedRow && checkedRows.length > 1) {
          var newCheckedRow = (checkedRows[0].ID === this.checkedRow.ID)
            ? checkedRows[1] : checkedRows[0]
          table.clearSelection()
          table.toggleRowSelection(newCheckedRow, true)
          theSelectedRow = newCheckedRow
        } else {
          this.checkedRow = checkedRows[0]
          if (checkedRows.length > 1) {
            table.clearSelection()
            table.toggleRowSelection(this.checkedRow, true)
            theSelectedRow = this.checkedRow
          }
        }
      } else {
        theSelectedRow = checkedRows
      }
      this.refreshExtendContent(theSelectedRow)
      this.focusSearchInput()
      this.retainSelect()
    },
    refreshExtendContent(theSelectedRow) {
      this.$nextTick(() => {
        var refExtend = this.getRefExtend()
        if (refExtend && refExtend.refreshExtendContent) {
          refExtend.refreshExtendContent(theSelectedRow, this)

          this.refMainTableStyle = `height: calc(100% - ${refExtend.height + 20}px) !important`
          this.refExtendStyle = `height: ${refExtend.height + 2}px`
        }
      })
    },
    getRefExtend() {
      if (this.$refs.refExtend) {
        var refExtend = this.$refs.refExtend
        if (refExtend instanceof Array) {
          refExtend = refExtend[0]
        }
        return refExtend
      }
    },
    rowDblclick(row) {
      // 扩展内容决定忽略主参照列表的双击处理
      var refExtend = this.getRefExtend()
      if (refExtend && refExtend.ignoreMainListDblclick === true) {
        return
      }
      this.fireRefTargetDbClick([row])
    },
    fireRefTargetDbClick(list) { // 触发双击参照处理
      this.$event(this, 'refTargetDbClick', this.makeSelectedData(list))
    },
    clickRow(row, tabIndex) {
      this.$refs.refTable[tabIndex].toggleRowSelection(row)
    },
    changeSearch() {
      this.$event(this, 'refTableRefreshData', this.activeTab, this.filterText)
    },
    onDlgClose(params) {
      this.filterText = ''
      if (params && params.onDlgClose) {
        params.onDlgClose(params)
      }
    },
    getDataOK() {
      return this.makeSelectedData()
    },
    makeSelectedData(list) {
      list = this.$isEmpty(list) ? this.$getTableSelection(this.getTable()) : list
      if (list && list.length > 1) {
        // 多选时list的顺序是按勾选的顺序，需要按照列表的顺序重新整理
        var listCopy = {}
        list.forEach(row => {
          listCopy[row.ID] = row
        })

        list = []
        var dataRows = this.result.attributes.rows
        dataRows.forEach(row => {
          if (listCopy[row.ID] !== undefined) {
            list.push(listCopy[row.ID])
          }
        })
      }

      if (list && list.length > 0 && this.$isNotEmpty(this.params['多对多模式'])) {
        list[0]['多对多模式'] = this.params['多对多模式']
        list[0]['columnTypes'] = this.columnTypes
      }
      var data = { tab: this.activeTab, list: list }

      // 提供给扩展机制处理选择的参照内容
      var refExtend = this.getRefExtend()
      if (refExtend && refExtend.wrapSelectedData) {
        var params = this.params
        refExtend.wrapSelectedData(data, params)
      }
      return data
    }
  }
}
</script>
<style lang="scss">
.refTable { height: 100%;position: relative; }
.globalDialog .refTable .el-tabs__nav-wrap::after { background-color: unset !important; }
.globalDialog .refTable .el-tabs__header { margin: 0 0 0px !important; }
</style>
<style lang="scss" scoped>
.hiddenPlaceholder {
  position: absolute;
  visibility: hidden;
}
</style>
