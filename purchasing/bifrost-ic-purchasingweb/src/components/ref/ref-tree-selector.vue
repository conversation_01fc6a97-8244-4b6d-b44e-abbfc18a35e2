<template>
  <div class="refTreeSelector">
    <el-button size="mini" round @click="selectAll">全选</el-button>
    <el-button size="mini" round @click="selectNone">全不选</el-button>
  </div>
</template>
<script>
export default {
  name: 'ref-tree-selector',
  data() {
    return {
      refTreeObj: undefined
    }
  },
  methods: {
    init(refTreeObj) {
      this.refTreeObj = refTreeObj
    },
    selectAll() {
      this.refTreeObj.selectAll()
    },
    selectNone() {
      this.refTreeObj.selectNone()
    }
  }
}
</script>
<style lang="scss">
.refTreeSelector { position: absolute; bottom: 10px; }
.common-page .refTreeSelector .el-button--mini { padding: 5px 7px 6px 7px !important; }
</style>
