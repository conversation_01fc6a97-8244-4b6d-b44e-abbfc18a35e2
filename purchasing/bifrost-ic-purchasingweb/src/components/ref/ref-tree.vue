<template>
    <div class="refTree">
        <sup-tree ref="supTree"
            :setting="setting"
            :nodes="result.data"
            :is-popover="false"
            :tree-title="result.attributes.tabs[0]"
            :btnSwitch="{showExpandAll: true}"
            :autoDeleteChildren = "false"
            @onCreated="handleCreated"
            @ifExpandAll="ifExpand"/>
    </div>

</template>

<script>
import SupTree from '../gianttree/supTree'
export default {
  name: 'ref-tree',
  components: { SupTree },
  props: ['treeData'],
  data() {
    return {
      dlg: undefined,
      multiple: false, // 是否可以多选
      treeAnySingeChoice: false, // 是否树节点任意单选（选择父节点时不联动子节点）
      result: this.resetResult(),
      filterText: '',
      showNodeCode: true,
      defaultExpandKey: [],
      checkedDataIds: [],
      filterNodes: [],
      ztreeObj: null,
      firstNode: true,
      expandNode: [],
      exParams: null,
      isExpandAllNode: true,
      setting: {
        check: {
          enable: true,
          chkboxType: { 'Y': 'ps', 'N': 'ps' }
        },
        data: {
          key: {
            name: 'label'
          },
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          render: {
            /* name: this.renderName*/
          }
        },
        view: {
          showIcon: false,
          fontCss: function(treeId, treeNode) {
            return (treeNode.searchNode) ? { 'color': '#A60000', 'font-weight': 'bold' } : ''
          }
        },
        callback: {
          onCheck: this.checkChange,
          onClick: this.onclick,
          beforeClick: this.beforeClick,
          beforeCheck: this.beforeCheck,
          beforeExpand: this.zTreeBeforeExpand,
          onDblClick: this.zTreeOnDblClick
        }
      }
    }
  },
  watch: {
    filterText(val) {
      // this.$refs.dataTree.filter(val)
      // this.ztreeObj.refresh()

    }
  },
  methods: {
    focusSearch() {

    },
    selectAll() {
      this.$refs.supTree.selectAll()
    },
    selectNone() {
      this.$refs.supTree.clearCheck()
    },
    searchEnterFun() {
      var value = this.filterText
      if (this.$isEmpty(value.trim())) {
        this.ztreeObj.refresh()
        return
      }
      if (value) {
        this.ztreeObj.refresh()
        let nodeList = []
        nodeList = this.ztreeObj.getNodesByParamFuzzy('label', value) // 模糊搜索
        if (this.expandNode.length > 0) {
          for (const j in this.expandNode) {
            this.closeParentNode(this.expandNode[j])
          }
        }

        this.expandNode = []
        const timeout = setTimeout(() => {
          clearTimeout(timeout)
          for (const i in nodeList) {
            this.firstNode = true
            this.getParentNode(nodeList[i])
          }
        }, 300)
      }
    },
    closeParentNode(node) { // 关闭之前展开的节点
      if (node) {
        const parentNode = node.getParentNode()
        if (parentNode) {
          this.ztreeObj.expandNode(parentNode, false, false, false) // 关闭节点
          this.closeParentNode(parentNode)
        }
        this.ztreeObj.expandNode(node, false, false, false)// 关闭节点
      }
    },
    getParentNode(node) {
      const parentNode = node.getParentNode()
      this.expandNode.push(parentNode) // 保存展开节点
      if (this.firstNode) {
        this.firstNode = false
        node.searchNode = 'searchNode'
        this.ztreeObj.expandNode(parentNode, true, false, false)// 展开节点
        this.ztreeObj.updateNode(node)
        node.searchNode = ''
      }
    },

    reset() {
      this.result = this.resetResult()
    },
    resetResult() { // 重置数据对象
      return {
        attributes: {
          tabs: ['']
        },
        data: []
      }
    },
    init(result, params, dlg) {
      this.dlg = dlg
      params = params || {}
      var checkedData = params.checkedData || []
      this.multiple = params.multiple
      this.treeAnySingeChoice = params.treeAnySingeChoice || false
      this.result = result
      this.exParams = params.exParams
      this.isExpandAllNode = this.$isEmpty(params.isExpandAllNode) ? true : params.isExpandAllNode
      if (this.$isNotEmpty(params.showNodeCode)) {
        this.showNodeCode = params.showNodeCode
      }
      if (this.showNodeCode) {
        this.setting.data.key.name = 'codeLabel'
      } else {
        this.setting.data.key.name = 'label'
      }
      this.$refs.supTree.doNotUncheckedWhenInit = true

      // TODO:待优化
      this.result.data.forEach(function(value, index) {
        value.codeLabel = value.itemKey + ' ' + value.label
        value.children = ''
      })
      this.checkedDataIds = checkedData
      if (this.treeAnySingeChoice) {
        this.setting.check.chkboxType = { 'Y': '', 'N': '' }
      }
    },
    initSelector() { // 只有多选时才显示“全选”和“全不选”
      if (this.dlg && this.multiple) {
        this.dlg.pagerContentId = 'ref-tree-selector'
        this.$nextTick(() => {
          this.dlg.$refs.pagerContent.init(this)
        })
      }
    },
    beforeCheck(treeId, treeNode) {
      if (this.$isNotEmpty(treeNode.dbClick)) { // 表明是双击选择
        treeNode.dbClick = null
        if (!treeNode.isLeaf ||
          (treeNode.children && treeNode.children.length > 0)) {
          if (!this.treeAnySingeChoice) {
            return false
          }
        }
        this.$event(this, 'refTargetDbClick',
          { tab: '', list: [this.wrapSelectedNode(treeNode)] })
        return false
      }

      if (!this.multiple &&
        treeNode.children.length > 0 &&
        !this.treeAnySingeChoice) {
        this.$message.error('只能选择最末级选项')
        return false
      }
      if (!this.multiple || this.treeAnySingeChoice) {
        // 单选时，先取消之前勾选的节点
        this.ztreeObj.checkAllNodes(false)
      }
      return true
    },
    checkChange(event, treeId, treeNode) {
      this.defaultExpandKey = treeNode.itemKey
      this.ztreeObj.selectNode(treeNode)
    },
    beforeClick(treeId, treeNode) {
      if (!this.multiple &&
        treeNode.children.length > 0 &&
        !this.treeAnySingeChoice) {
        this.$message.error('只能选择最末级选项')
        return false
      }
    },
    onclick(event, treeId, treeNode) {
      if (!this.multiple &&
        treeNode.children.length > 0 &&
        !this.treeAnySingeChoice) {
        this.$message.error('只能选择最末级选项')
      } else {
        this.ztreeObj.checkNode(treeNode, true, true, true)
      }
    },
    getDataOK() {
      var list = []
      var itemKeyList = []
      var nodes = null
      if (this.multiple) {
        nodes = this.ztreeObj.getCheckedNodes(true)
      } else {
        nodes = this.ztreeObj.getNodesByParam('itemKey', this.defaultExpandKey)
      }

      nodes.forEach(node => {
        this.setTreeLable(node)
      })
      if (this.treeAnySingeChoice && nodes.length > 0) {
        list.push(this.wrapSelectedNode(nodes[0]))
      } else {
        this.addLeafNode(list, nodes, itemKeyList)
      }

      return { tab: '', list: list }
    },
    wrapSelectedNode(node) {
      var selectedData = {}
      var exData = node.exData || {}
      selectedData = Object.assign(selectedData, exData)
      return Object.assign(selectedData, node)
    },
    onDlgClose() {
      const treeRef = this.$refs.supTree
      if (treeRef) {
        treeRef.refresh()
      }
    },
    addLeafNode(list, nodes, itemKeyList) { // 只取末级树节点
      nodes.forEach(n => {
        if (this.$isEmpty(n.children)) {
          if (itemKeyList.indexOf(n.itemKey) === -1) {
            list.push(this.wrapSelectedNode(n))
            itemKeyList.push(n.itemKey)
          }
        } /* else {
          this.addLeafNode(list, n.children, itemKeyList)
        } */
      })
    },
    handleCreated(ztreeObj) {
      this.ztreeObj = ztreeObj
      if (this.exParams === '支出功能分类科目') {
        var nodeAll = ztreeObj.getNodes()
        if (this.$isNotEmpty(nodeAll)) {
          ztreeObj.expandNode(nodeAll[0].children[0], true, false, false)
          ztreeObj.expandNode(nodeAll[0].children[1], true, false, false)
        }
      }

      if (this.checkedDataIds.length > 0) {
        this.defaultExpandKey = this.checkedDataIds[0]
        var _this = this
        // eslint-disable-next-line no-inner-declarations
        function filter(node) {
          if (_this.checkedDataIds.indexOf(node.itemKey) > -1) {
            return true
          }
          return false
        }
        var nodes = this.ztreeObj.getNodesByFilter(filter)
        if (nodes.length > 0) {
          nodes.forEach(function(item, index) {
            ztreeObj.checkNode(item, true, true)
          })
          ztreeObj.refresh() // 定位节点前刷新整棵树，不然会有bug
          ztreeObj.selectNode(nodes[0])
        }
      }
    },
    zTreeBeforeExpand(treeId, treeNode) {
      treeNode.children.forEach(function(value, index) {
        if (value.children.length === 0) {
          value.isParent = false
        }
      })
    },
    zTreeOnDblClick(event, treeId, treeNode) {
      treeNode.dbClick = true
      this.setTreeLable(treeNode)
      this.ztreeObj.checkNode(treeNode, null, true, true)
    },
    setTreeLable(node) {
      if (this.showNodeCode) {
        node.treeLable = node.itemKey + ' ' + node.label
      } else {
        node.treeLable = node.label
      }
    },
    ifExpand() {
      this.$refs.supTree.isExpandAllNode = this.exParams !== '支出功能分类科目' && this.isExpandAllNode
    }
  }
}
</script>

<style lang="scss">
    .refTree { height: 100%;display: flex;flex-direction: column}
    .refTree .refTreeTitle{ padding-top: 5px;padding-bottom: 10px; }
    .refTree .el-input-group__prepend { border: 1px solid #DDDDDD; border-right: none; }
    .refTree .refTreeContent {
        border: 1px solid #ddd;
        padding: 10px;
        flex: auto;
        overflow: auto;
        display: flex;
        flex-direction: column;
    }
    .refTree .el-tree {
      flex: auto;
      overflow: auto;
    }
    .refTree .ztree {
      padding: 10px;
      border: 1px solid #DDDDDD !important;
    }
</style>
