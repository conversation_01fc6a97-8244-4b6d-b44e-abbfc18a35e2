<template>
  <div id='ofdContainer' dir='ltr'>
    <!-- 目前不显示工具栏 -->
    <div class='ofdControlToolbar' v-if='false'>
      <el-button icon='el-icon-zoom-out' id='zoomOut' />
      <el-button icon='el-icon-zoom-in' id='zoomIn' />
      <el-button icon='el-icon-refresh-right' id='pageRotateCw' />
      <el-button icon='el-icon-refresh-left' id='pageRotateCcw' />
    </div>
    <ofd-view
      :file='ofdUrl'
      :mem='ofd'
      :signature-viewer-force-check='true'
      :sidebar-force-open='false'
    ></ofd-view>
  </div>
</template>
<script>
export default {
  name: 'ofd',
  props: {
    ofdObj: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      ofdUrl: {},
      ofd: require('parser_x.js')
    }
  },
  watch: {
    ofdObj: {
      handler: function(newV, oldV) {
        this.ofdUrl = newV.file
        this.$nextTick(() => {
          const removeDomArr = [
            '#toolbarViewerLeft',
            '#toolbarViewerRight',
            '#sidebarContainer',
            '#secondaryToolbar',
            '#errorWrapper',
            '#mainContainer .toolbar',
            '#dialogContainer',
            '#fileInput'
          ]
          removeDomArr.forEach((dom) => {
            const ele = document.querySelector(dom)
            ele && ele.remove()
          })
          this.init()
        })
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  methods: {
    // 处理水印和渲染错误
    handleWatermarkAndRenderErrors(pages) {
      // 发票居中
      const mask = document.querySelectorAll('.mask_div')
      const images = document.querySelectorAll('image')
      const svg = document.querySelectorAll('svg')
      const path = document.querySelectorAll('path')
      const seal_img_div_0 = document.getElementsByName('seal_img_div_0')
      // 删除水印
      Array.from(path).forEach((node) => {
        if (node.getAttribute('ID') === '81') {
          node.parentNode.remove()
        }
      })
      Array.from(svg).forEach((node, index) => {
        if (node.id && node.style.top === '0px' && node.style.left === '0px') {
          node.remove()
        }
      })
      Array.from(mask).forEach((node) => {
        node.remove()
      })
      // 微调中间签章显示错位
      const container = pages[0].querySelector('div')
      const lastDiv = container.querySelector('div')
      if (lastDiv) {
        const svgInLastDiv = lastDiv.children
        const svgCountInLastDiv = svgInLastDiv.length
        // 只有一个不处理 大于1个 处理
        if (svgCountInLastDiv > 1) {
          Array.from(svgInLastDiv).forEach((node, index) => {
            const left = +node.style.left.replace('px', '') * 0.35 + 2 + 'px'
            const top = +node.style.top.replace('px', '') * 0.35 + 'px'
            if (
              index === svgCountInLastDiv - 1 ||
              index === svgCountInLastDiv - 2
            ) {
              node.style.setProperty('display', 'none')
              node.style.setProperty('left', left)
              node.style.setProperty('top', top)
              const path = node.querySelector('svg path')
              path.style.setProperty('transform', 'scale(0.35)')
              node.style.setProperty('display', 'block')
            }
          })
        }
      }
      // 签章不可点
      // 原理: 克隆节点移除原来节点绑定的事件
      if (seal_img_div_0.length) {
        const clonedElement = seal_img_div_0[0].cloneNode(true)
        seal_img_div_0[0].parentNode.replaceChild(
          clonedElement,
          seal_img_div_0[0]
        )
        clonedElement.style.setProperty('cursor', 'default')
      }
      // 处理右下角渲染2个重复签章
      Array.from(images).forEach((node, index) => {
        const href = node.getAttribute('href')
        let count = 0
        Array.from(images).forEach((image) => {
          if (image.getAttribute('href') === href) {
            count++
            // 如果存在重复image则删除一个
            if (count === 2) {
              image.remove()
            }
          }
        })
      })
      setTimeout(() => {
        if (pages?.length > 0) {
          Array.from(pages)?.forEach(page => {
            page.style.setProperty('opacity', 1)
          })
        }
      }, 0)
    },
    init() {
      const viewer = document.querySelector('#viewer')
      const pages = viewer.getElementsByClassName('page')
      const timer = setInterval(() => {
        if (pages.length) {
          clearInterval(timer)
          this.handleWatermarkAndRenderErrors(pages)
        }
      }, 5)
    }
  }
}
</script>

<style lang='scss' scoped>
#ofdContainer {
  height: 100%;
  #outerContainer {
    margin-top: 30px;
  }
  /deep/#viewer {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    .page {
      opacity: 0;
    }
  }
  /deep/path {
    cursor: default;
    // 水印不可见
    &[ID='81'] {
      stroke: transparent;
    }
  }
  // 水印不可见
  .mask_div {
    color: transparent;
  }
  .ofdControlToolbar {
    display: flex;
    justify-content: center;
    gap: 12px;
  }
}
</style>
