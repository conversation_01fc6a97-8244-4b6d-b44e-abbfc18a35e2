<template>
  <div class="svg-container">
    <img class="svg-bg" :src="url" />
    <div class="svg-title" :style="{color, border}">
      {{ status }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'bidSvg',
  props: {
    status: String
  },
  watch: {
    status() {
      this.refreshStatus()
    }
  },
  data() {
    return {
      url: undefined,
      color: undefined,
      border: undefined,
    }
  },
  methods: {
    refreshStatus() {
      if(["待开标", "开标中", "评标中", "暂停评标"].includes(this.status)) {
        this.url = require('@/assets/svg/bidding-blue.svg');
        this.color = "#1890ff"
        this.border = "1px solid #1890ff"
      }
      if(["结束评标"].includes(this.status)) {
        this.url = require('@/assets/svg/bidding-green.svg');
        this.color = "#70b603"
        this.border = "1px solid #70b603"
      }
      if(["流标", "废标"].includes(this.status)) {
        this.url = require('@/assets/svg/bidding-red.svg');
        this.color = "#ec808d"
        this.border = "1px solid #ec808d"
      }
    }
  },
  mounted() {
    this.refreshStatus()
  }
}
</script>

<style lang="scss"  scoped>
.svg-container {
  position: relative; 
  width: 80px;
  height: 80px;
}

.svg-bg {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  top: 0px; 
  right: 0px;
  height: 60px;
}

.svg-title {
  position: absolute;
  width: 75px;
  left: 50%;
	top: 50%;
	transform: translate(-50%,-50%);
  font-size: 16px;
  text-align: center;
  background: white;
}
</style>