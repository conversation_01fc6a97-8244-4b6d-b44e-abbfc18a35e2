<template>
  <div class="bidCardBox">
    <div class="bg-title"></div>
    <div class="bid-card" :style="{ height: cardHeight + 'px' }">
      <span class="bid-checkbox"  @click="timeoutTip">
        是否自动开标
        <el-checkbox v-model="checked" @change="checkboxChange" :disabled="disabled"></el-checkbox>
      </span>
      <bidStatus :status="bid.bidProjectState" class="bid-svg"></bidStatus>
      <div class="bid-form">
        <ul style="width: 450px;">
          <li>招标编号：<span>{{bid.biddingNo}}</span></li>
          <li>采购项目名称：<span>{{bid.projectInfoName}}</span></li>
          <li>采购方式：<span>{{bid.purchaseMethod}}</span></li>
          <li>评审方法：<span>{{bid.reviewMethod}}</span></li>
          <li>开标时间：<span>{{bid.openTime}}</span></li>
          <li>由谁做资格性审查：<span>{{bid.examiner}}</span></li>
          <li v-if="!isExpert&&!isSupplier">开标主持人：<span>{{bid.compereName}}</span></li>
          <li v-if="!isExpert&&isSupplier">签到状态：<span>{{bid.signinState}}</span></li>
        </ul>
        <ul class="ulRight">
          <li>评标室：<span>{{bid.biddingRoom}}</span></li>
          <li v-if="!isExpert&&!isSupplier">报名有效供应商：<span>{{bid.registrationCount || 0}}家</span></li>
          <li v-if="!isExpert&&!isSupplier">专家组长：<span>{{bid.expertTeamLeader}}</span></li>
          <li v-if="!isExpert&&isSupplier">解密状态：<span>{{bid.decodeState}}</span></li>
        </ul>
      </div>
      <div class="bid-button">
      <div v-if="bid.bidProjectState == '待开标'">
        <div v-if="!isExpert&&!isSupplier">
          <el-button @click="handleClick('开标')">开标</el-button>
          <el-button @click="handleClick('废标')">废标</el-button>
          <el-button @click="handleClick('查看有效报名供应商')">查看有效报名供应商</el-button>
        </div>
      </div>
      <div v-if="['开标中', '评标中', '暂停评标'].includes(bid.bidProjectState)">
        <div v-if="!isExpert&&!isSupplier">
          <el-button @click="handleClick('进入评标')">进入评标</el-button>
          <el-button @click="handleClick('查看有效报名供应商')">查看有效报名供应商</el-button>
          <el-button v-if="'暂停评标' == bid.bidProjectState" @click="handleClick('恢复评标')">恢复评标</el-button>
          <el-button v-else @click="handleClick('暂停评标')">暂停评标</el-button>
        </div>
        <div v-if="isExpert">
          <el-button @click="handleClick('评标代表资格性审查')">评标代表资格性审查</el-button>
          <el-button @click="handleClick('进入评标')">进入评标</el-button>
        </div>
        <div v-if="isSupplier">
          <el-button @click="handleClick('签到')">签到</el-button>
          <el-button @click="handleClick('进入')">进入</el-button>
        </div>

      </div>
      <div v-if="bid.bidProjectState == '结束评标'">
        <div v-if="!isExpert&&!isSupplier">
          <el-button @click="handleClick('查看有效报名供应商')">查看有效报名供应商</el-button>
          <el-button @click="handleClick('查看/签章评审文件')">查看/签章评审文件</el-button>
          <el-button @click="handleClick('重新评审')">重新评审</el-button>
          <el-button @click="handleClick('历史评审记录')">历史评审记录</el-button>
        </div>
        <div v-if="isExpert">
          <el-button @click="handleClick('查看/签章评审文件')">查看/签章评审文件</el-button>
        </div>
      </div>
      <div v-if="bid.bidProjectState == '流标'">
        <div v-if="!isExpert&&!isSupplier">
          <el-button @click="handleClick('查看有效报名供应商')">查看有效报名供应商</el-button>
          <el-button @click="handleClick('查看/签章评审文件')">查看/签章评审文件</el-button>
          <el-button @click="handleClick('历史评审记录')">历史评审记录</el-button>
          <el-button @click="handleClick('流标原因')">流标原因</el-button>
        </div>
      </div>
      <div v-if="bid.bidProjectState == '废标'">
        <div v-if="!isExpert&&!isSupplier">
          <el-button @click="handleClick('查看有效报名供应商')">查看有效报名供应商</el-button>
          <el-button @click="handleClick('查看/签章评审文件')">查看/签章评审文件</el-button>
          <el-button @click="handleClick('历史评审记录')">历史评审记录</el-button>
          <el-button @click="handleClick('废标原因')">废标原因</el-button>
        </div>
      </div>
      <div v-if="bid.bidProjectState == '待评标'">
        <div v-if="isExpert">
          <el-button @click="handleClick('签到')">签到</el-button>
        </div>
      </div>
      </div>
    </div>

  </div>
</template>

<script>
import bidStatus from './bid-svg'
  export default {
    name: 'bidCard',
    props: {
      bid: Object,
      isExpert: { type: Boolean, default: false },
      isSupplier: { type: Boolean, default: false },
      cardHeight:{ type: Number, default: '285'}
     },
    components: { bidStatus },
    data() {
      return {
        checked: false
      };
    },
    computed: {
      disabled() {
        return new Date(this.bid.openTime) < new Date()
      }
    },
    mounted() {
      if(this.bid.autoBidUserBizid) {
        this.checked = true
      }
    },
    methods: {
      timeoutTip() {
        if(this.disabled) {
          this.$alert('已过开标时间，不可再变更设置！', {
            confirmButtonText: '确定',
            type: "warning"
          });
        }
      },
      handleClick(type) {
        this.$emit("handleClick", type, this.bid)
      },
      checkboxChange(val) {
        if(val) {
          setTimeout(() => {
            this.checked = false
          })
          this.$confirm(`确定之后，此项目到达开标时间时，系统自动确认开标，
操作者即是担任此项目的开标主持人，请确定您是此
评标项目的开标主持人，才可做此操作！`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // this.$callApiParams('setAutoBidOpening', {
            //   borId: this.bid.borId,
            //   autoFlag: String(val)
            // }, res => {
            //   this.checked = true
            //   this.$message({
            //     type: 'success',
            //     message: '操作成功!'
            //   });
            // })
          })
        } else {
          setTimeout(() => {
            this.checked = true
          })
          this.$confirm(`确定要取消此项目的自动开标设置？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // this.$callApiParams('setAutoBidOpening', {
            //   borId: this.bid.borId,
            //   autoFlag: String(val)
            // }, res => {
            //   this.checked = false
            //   this.$message({
            //     type: 'success',
            //     message: '操作成功!'
            //   });
            // })
          })
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
.bidCardBox{
  position: relative;
  margin: 30px 20px;
}
.bid-card {
  padding: 30px;
  margin-left: 10px;
  position: relative;
  width: 490px;
  background-color: #f1f7fd;
  padding: 15px;
  box-shadow: 1px 8px 5px rgba(0, 0, 0, 0.34901960784313724);
  z-index: 5;
}

.bid-checkbox {
  position: absolute;
  top: 10px;
  right: 10px;
}

.bid-svg {
  height: 60px;
  position: absolute;
  top: 39px;
  right: 50px;
}

.bid-form{
  font-size: 14px;
  color: #015478;
  .ulRight{
    position:absolute;
    top:127px;
    right:90px;
  }
  li{
    line-height: 28px;
  }
  line-height: 2em;
}

.bid-form span {
  color: black;
}

.bg-title {
  background: linear-gradient(0deg, rgba(121, 188, 246, 1) 47%, rgba(255, 255, 255, 1) 120%);
  border-radius: 15px;
  height: 19px;
  position: absolute;
  left: 0px;
  top: -10px;
  width: 513px;
}

.bid-button{
  margin-top: 20px;
  .el-button{
    padding: 9px 10px;
  }
}
</style>
