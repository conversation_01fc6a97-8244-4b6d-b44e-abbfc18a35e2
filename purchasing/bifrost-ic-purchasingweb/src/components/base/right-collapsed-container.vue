<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-15 11:53:43
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-05-02 10:17:50
-->
<template>
  <page class="no-padding-right">
    <template #pageContent>
      <div id="right-collapsed-container" :class="rightCollapsed ? 'collapsed' : ''" :style="{width: `${width}px`}">
        <div class="retract-dom retract-right retract-block"
             style="cursor:pointer;
               border-radius:none;
               width: 10px;
               height: 69px;
               line-height: 59px;"
             @click="clickCollapsed">
          <i class="el-icon-arrow-left" v-if="rightCollapsed" style="font-size:13px;cursor:pointer;"></i>
          <i class="el-icon-arrow-right" v-else style="font-size:13px;cursor:pointer;"></i>
        </div>
        <div class="collapsed-content" :style="{width: `${width - 10}px`}">
          <slot name='content'></slot>
        </div>
      </div>
    </template>
  </page>
</template>
<script>
export default {
  name: 'right-collapsed-container',
  props: {
    width: { type: String, default: '770' }
    // isRightCollapsed: { type: Boolean, default: true }
  },
  data() {
    return {
      rightCollapsed: true
    }
  },
  methods: {
    clickCollapsed() {
      this.rightCollapsed = !this.rightCollapsed
      this.$emit('clickCollapsed', this.rightCollapsed)
    }
  }
}
</script>
<style lang="scss" scoped>
  #right-collapsed-container {
    overflow: hidden;
    height: 100%;
    position: relative;
    border: none;
    transition: width 0.5s ease;
    margin-left: 0px;

    .retract-dom {
      z-index: 888;
      display: inline-block;
      height: 30px;
      position: absolute;
      border: 1px solid #dcdfe6;
      top: 46%;
      left: 0px;
      padding: 4px 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
    }

    .collapsed-content {
      margin: 0px 10px;
      height: 100%;
      border: none;
      border-top: none;
      padding-top: 0px;
      border-bottom: none;
      margin-right: 0px;
      margin-left: 10px;
      border-right: none;

      .ba-middle, .ba-bottom {
        height: 32%;
        margin: 10px;
        border: 1px solid #bbb;
      }
    }
  }

  .collapsed {
    transition: width 0.5s ease;
    width: 10px !important;
  }

  .no-padding-right {
    padding-right: 0px !important;
  }
</style>
