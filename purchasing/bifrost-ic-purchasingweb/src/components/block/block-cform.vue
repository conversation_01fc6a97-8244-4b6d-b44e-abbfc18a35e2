<template>
  <div :id="blockViewBlockId" class="blockViewBlock blockViewBlockCform">
    <form-canvas ref="formCanvas" :hideColSetting="true" :hideSilder="true"/>
  </div>
</template>
<script>
import FormCanvas from '../cform/form-canvas'
export default {
  name: 'block-表单',
  components: { FormCanvas },
  data() {
    return {
      isCformBlock: true,
      params: {},
      block: {}
    }
  },
  computed: {
    blockViewBlockId() {
      return 'blockViewBlockId' + this.params.mode + new Date().getTime() + ''
    }
  },
  methods: {
    nameChange(name) {
      this.$refs.formCanvas.title = name
    },
    init(params, block) {
      this.params = params
      this.block = block

      params.clearRefLabelValues = (label) => {
        this.$refs.formCanvas.$refs.formFormat.clearRefLabelValues(label)
      }
      params.setFormDisabled = (label, isDisabled) => {
        this.$refs.formCanvas.$refs.formFormat.setDisabled(label, isDisabled)
      }
      params.setFormValue = (label, value) => {
        this.$refs.formCanvas.$refs.formFormat.setColValue(label, value)
        this.$refs.formCanvas.$refs.formFormat.setValue(label, value)
      }
      params.getFormValue = (label, isHideItem) => {
        return this.$refs.formCanvas?.$refs?.formFormat?.getValue(label, isHideItem)
      }
      params.setColRequired = (label, isRequired) => {
        return this.$refs.formCanvas?.$refs?.formFormat?.setColRequired(label, isRequired)
      }
      params.getFcRulePropColMap = (label) => {
        return this.$refs.formCanvas?.$refs?.formFormat?.getFcRulePropColMap(label)
      }

      if (this.params.isDesignMode) {
        this.params.meta.main.isBlockForm = true
        // 兼容在表单设计里的预览制单详情
        const model = this.params.isPreviewDetail ? '详情' : '设计'
        this.$refs.formCanvas.initMeta(model, this.params.meta)
        return
      }
      params.dataVo = block.data
      params.dataVo.fkGuid = params?.dataVo?.extData?.attTempId
      params.blockObjMap[`表单-${params.dataVo.formType}`] = this

      params.getBillId = () => {
        return params.dataVo.data.id
      }

      // 获取表单对象CformDataVo的extData的数据
      params.getCformExtData = (name) => {
        if (params?.dataVo?.extData?.[name]) {
          return params.dataVo.extData[name]
        }
      }

      params.isRefNoCode = (labelOrigin, dataRef) => { // 参照结果是否不含编码
        return this.isRefNoCode(labelOrigin, dataRef)
      }

      params.hasOption = (optionName) => {
        return this.hasOption(optionName)
      }

      // 区块支持 修改参照参数 blockParams
      var initFormExData = { showEditButtons: false, blockParams: params }
      // 区块支持 表单初始化前回调
      initFormExData.initMetaBefore = (dataVo) => {
        if (params.initMetaBefore) {
          params.initMetaBefore(dataVo)
        }
      }

      // 区块支持 表单初始化后回调
      initFormExData.initMetaAfter = (fcModel, item, formFormat, initData, meta) => {
        if (params.initMetaAfter) {
          params.initMetaAfter(fcModel, item, formFormat, initData, meta)
        }
      }

      const callbackBeforeRefComplete = params.callbackBeforeRefComplete
      initFormExData.blockParams.callbackBeforeRefComplete = (formFormat, item, selectedData, refParams, callbackCloseRefDlg, setBtnUnLoad) => {
        callbackBeforeRefComplete?.(formFormat, item, selectedData, refParams, callbackCloseRefDlg, setBtnUnLoad)
        this.callbackBeforeRefComplete(formFormat, item, selectedData, refParams, callbackCloseRefDlg, setBtnUnLoad, params)
      }

      initFormExData.callbackAfterFormLoaded = (dataVo) => {
        this.$nextTick(() => {
          // 这里主要是为了实现与规则表单统一处理跳转制单场景的一致
          // 一般在callbackAfterFormLoaded中设置要素可用性
          const callbackAfterFormLoadedInExtData = dataVo.extData?.callbackAfterFormLoaded
          if (callbackAfterFormLoadedInExtData) {
            callbackAfterFormLoadedInExtData(dataVo, this.$refs.formCanvas.$refs.formFormat)
          }

          if (params.callbackAfterFormLoaded) {
            params.callbackAfterFormLoaded(dataVo, this.$refs.formCanvas.$refs.formFormat)
          }

          // 注册表单要素值变化回调函数
          // selectedData 弹框选择时会传递当前行选择的数据
          this.$refs.formCanvas.$refs.formFormat.addColItemModifiedCallbacks({
            '区块制单表单要素值响应函数': (theColItem, selectedData = []) => {
              if (params.formCellChanged) {
                params.formCellChanged(theColItem, selectedData)
              }
            } })
        })
      }
      if (initFormExData.baseListFormObj === undefined) {
        initFormExData['baseListFormObj'] = params
      }
      // 跳转制单场景额外数据初始化
      this.$fillDataVoFromJumpToSaveFormData(
        params.dataVo, this.params.jumpToSaveFormData)
      if (params.callbackBeforeFormLoaded) {
        params.callbackBeforeFormLoaded(initFormExData)
      }
      if (params.isInBlockTab) {
        params.dataVo.meta = params.dataVo.main
        params.dataVo.version = params.dataVo.instance
      }
      this.$refs.formCanvas.initByDataVo(
        params.dataVo,
        params.mode,
        undefined,
        false,
        undefined,
        initFormExData)
    },
    callbackBeforeRefComplete(formFormat, item, selectedData, refParams, callbackCloseRefDlg, setBtnUnLoad, params) {
      // 指标
      const ba = 'RelevantBaEntity'
      const hasBa = params?.getRformPageData(ba)?.rows?.length

      // 合同
      const cmPerformPlan = 'ConCreaPerformPlan' // 履行计划
      const hasCmPerformPlan = params?.getRformPageData(cmPerformPlan)?.rows?.length

      // 借款或者报销单选择采购或者合同
      const incCmInfo = 'IncCmInfoEntity' // 报销单合同
      const hasIncCmInfo = params?.getRformPageData(incCmInfo)?.rows?.length
      const incCmDetail = 'IncCmDetailEntity' // 报销单合同明细
      const hasIncCmDetail = params?.getRformPageData(incCmDetail)?.rows?.length

      const incPuInfo = 'IncPuInfoEntity' // 报销单合同
      const hasIncPuInfo = params?.getRformPageData(incPuInfo)?.rows?.length
      const incPuDetail = 'IncPuDetailEntity' // 报销单合同明细
      const hasIncPuDetail = params?.getRformPageData(incPuDetail)?.rows?.length

      // 采购
      const puDemand = 'PurDemandEntity' // 采购需求
      const puDetail = 'PurDetailEntity' // 采购明细
      const hasPuDemand = params?.getRformPageData(puDemand)?.rows?.length
      const hasPuDetail = params?.getRformPageData(puDetail)?.rows?.length

      // 收款人
      const incPayee = 'IncPayeeEntity'
      const hasIncPayee = params?.getRformPageData(incPayee)?.rows?.length

      // 该单区块匹配的行表单keys
      const blockRformExtendKeys = []
      // 需要清空的表单要素
      const needClearColItem = []
      // 需要清空指定区块的列 {RelevantBaEntity: 'baName, xxx, yyy', IncCmInfoEntity: 'baName'}
      const colsData = {}
      // 单据类型
      const formType = params?.dataVo?.formType

      const showMessage = (msg) => {
        if (msg) {
          message = `修改部门会清空${msg}，确定要修改吗?`
        }
      }
      /**
       * 提示信息处理
       * @param {String} msg 提示信息
       * @param {String} key 行表单key
       */
      const infoFunction = (msg, key) => {
        if (!key) {
          // 表单 或者 列操作
          showMessage(msg)
        } else {
          // 如果对应区块有数据
          if (params?.getRformPageData(key)?.rows?.length) {
            showMessage(msg)
            if (key && !blockRformExtendKeys.includes(key)) {
              blockRformExtendKeys.push(key)
            }
          }
        }
      }

      // const handleClear = () => {
      //   console.log('params.blockObjMap', params.blockObjMap)
      //   // 遍历单据的每一个扩展 获取扩展中的清空配置
      //   for (const [key, extendVueInstance] of Object.entries(params.blockExtendObjMap)) {
      //     console.log(1111, params.getRformPageData(key))
      //     console.log(key, extendVueInstance)
      //     const clearFromCformOriginLabels = extendVueInstance?.clearFromCformOriginLabels
      //     if (clearFromCformOriginLabels) {
      //       if (Array.isArray(clearFromCformOriginLabels)) {
      //         // 如果配置是数组
      //         console.log('clearFromCformOriginLabels', clearFromCformOriginLabels)
      //         if (clearFromCformOriginLabels.includes(item.labelOrigin)) {
      //           // 且切换的
      //           const pageData = params.getRformPageData(key)
      //           const rows = pageData?.rows || []
      //           if (rows.length) {
      //             console.log('清空', params.blockObjMap?.[key]?.$refs.curdList.clearTableData())
      //           }
      //         }
      //       } else {
      //         this.$message.error('切换参照清除行表单数据的配置必须是数组')
      //       }
      //     }
      //   }
      // }
      // handleClear()

      // 切换部门
      // 合同 中有对应采购申请
      // 报销单采购  采购明细
      let message = ''
      if (item.labelOrigin === '部门') {
        // 有指标
        infoFunction('指标数据', ba)

        if (formType === '采购申请单') {
          // 采购申请
          const puMsg = hasBa ? '指标和采购数据' : '采购数据'
          if (hasPuDemand) {
            // 有需求 (采购需求 采购品目清空)
            infoFunction(puMsg, puDemand)
            if (hasPuDetail) {
              infoFunction(puMsg, puDetail)
            }
          }
        } else if (formType === '采购需求单' && hasBa) {
          // 采购需求 (采购品目中的指标)
          const puMsg = hasBa ? '指标和采购数据' : '采购数据'
          colsData[puDetail] = 'baName'
          infoFunction(puMsg)
        } else if (formType === '合同') {
          // 有合同
          // 有指标 (指标 履行计划中的指标 表单中的[对应采购申请, 关联合同])
          const cmMsg = hasBa ? '指标' : ''
          if (hasCmPerformPlan) {
            colsData[cmPerformPlan] = 'baName'
          }
          infoFunction(cmMsg)

          // 表单有有对应采购申请 关联合同
          const clearFormColItems = ['对应采购申请', '关联合同']
          // 处理合同提示信息
          let handleCmMsg = cmMsg
          for (let i = 0; i < clearFormColItems.length; i++) {
            handleCmMsg = handleCmMsg ? `${handleCmMsg}和` : ''
            const item = clearFormColItems[i]
            if (formFormat.getValue(item)) {
              needClearColItem.push(item)
              handleCmMsg += item
              infoFunction(handleCmMsg)
            }
          }
        } else if (formType === '借款单' || formType === '报销单') {
          let loanMsg = hasBa ? '指标' : ''
          // 有报销单合同
          if (hasIncCmInfo) {
            loanMsg += hasBa ? '和合同数据' : '合同数据'
            infoFunction(loanMsg, incCmInfo)
          }
          // 有报销单合同明细
          if (hasIncCmDetail) {
            loanMsg += hasBa ? '和合同明细数据' : '合同明细数据'
            infoFunction(loanMsg, incCmDetail)
          }

          // 有报销单合同
          if (hasIncPuInfo) {
            loanMsg += hasBa ? '和采购数据' : '采购数据'
            infoFunction(loanMsg, incPuInfo)
          }
          // 有报销单合同明细
          if (hasIncPuDetail) {
            loanMsg += hasBa ? '和采购明细数据' : '采购明细数据'
            infoFunction(loanMsg, incPuDetail)
          }
          if (hasIncPayee) {
            loanMsg += hasBa ? '和收款人数据' : '收款人数据'
            infoFunction(loanMsg, incPayee)
          }
        }
      } else if (item.labelOrigin === '合同') {
        // 有指标
        infoFunction('指标数据', ba)
      }
      if (message) {
        this.$confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 清空数据
          const types = {
            cformColItems: needClearColItem,
            blockRformExtendKeys,
            colsData
          }
          this.clearBlockData(formFormat, params, types)
          callbackCloseRefDlg()
        }).catch(() => {
          callbackCloseRefDlg(true)// 仅仅是关闭参照弹窗
        })
      } else {
        callbackCloseRefDlg()
      }
    },
    /**
     * 清空区块数据
     * @param {Object} formFormat 表单实例
     * @param {Object} params 区块params
     * @param {Object} types 类型 {cformColItems: [], blockRformExtendKeys: [], colsData: {}}
     */
    clearBlockData(formFormat, params, types) {
      const { cformColItems, blockRformExtendKeys, colsData } = types
      const itemMap = formFormat.allColItemsMap
      // 清空对应表单要素
      cformColItems.map(colItem => {
        formFormat.clearRefData(itemMap[colItem], true)
      })
      // 清空区块数据
      blockRformExtendKeys?.map(key => {
        if (params?.getRformPageData(key)?.rows?.length) {
          params.blockObjMap?.[key]?.$refs.curdList.clearTableData()
        }
      })
      // 清空对应区块列数据
      Object.keys(colsData)?.map(key => {
        let cols = colsData[key]?.replace(/\s/g, '')
        // 如果有多列需清空 则用逗号分割
        if (cols.includes(',')) {
          cols = cols.split(',')
        }
        if (Array.isArray(cols)) {
          params?.getRformPageData(key)?.rows?.map(row => {
            cols.map(col => {
              row[col] = ''
            })
          })
        } else {
          params?.getRformPageData(key)?.rows?.map(row => {
            row[colsData[key]] = ''
          })
        }
      })
    },
    isRefNoCode(labelOrigin, dataRef) {
      if (this.$isBaItem({ labelOrigin: labelOrigin })) {
        return this.hasOption('指标参照无编码')
      }

      if (labelOrigin) {
        if (labelOrigin.indexOf('经济分类') > -1) {
          return this.hasOption('经济分类参照无编码')
        }
        if (labelOrigin.indexOf('功能分类') > -1) {
          return this.hasOption('功能分类参照无编码')
        }
        if (labelOrigin === '部门') {
          return this.hasOption('部门参照无编码')
        }
        if (labelOrigin === '申请人' || dataRef === '选择系统人员') {
          return this.hasOption('人员参照无编码')
        }
        if (labelOrigin === '合同') {
          return this.hasOption('合同参照无编码')
        }
        if (labelOrigin === '预算项目' || dataRef === '选择预算项目') {
          return this.hasOption('预算项目参照无编码')
        }
      }
      return false
    },
    hasOption(optionName) {
      return this.$refs.formCanvas?.hasFormOption(optionName)
    },
    updateBeforeSave(isDraft, updateAfter) {
      var dataVoOld = this.params.dataVo
      var dataVo = this.$refs.formCanvas.getDataToSave(updateAfter)
      dataVo.extData = dataVo.extData || {}
      if (dataVoOld) {
        dataVo.attList = dataVoOld.attList
      }
      this.block.data = dataVo
      this.params.dataVo = dataVo
    },
    showError(result) {
      var errorHitContainer = {}
      this.$refs.formCanvas.showError(result, undefined, errorHitContainer)
      var errorCount = errorHitContainer['hitCountInt']
      if (parseInt(errorCount) > 0) { // 表单内部已经弹错误提示，外部不要再重复弹出
        return true
      }
    }
  }
}
</script>
<style scoped lang="scss">
</style>
