<template>
  <div :class="`blockViewBlock blockViewBlockFile ${blockViewBlockErrorClass}`">
    <span class="blockViewBlockErrorSpan">{{ blockError }}</span>
    <e-invoice ref="eInvoice"/>
  </div>
</template>
<script>
// import BaseAttachment from '../bizz/file/attachment'
import EInvoice from '../bizz/invoice/e-invoice'
export default {
  name: 'block-电子发票',
  components: { EInvoice },
  data() {
    return {
      blockError: '',
      block: {},
      isApply: false,
      params: {}
    }
  },
  computed: {
    blockViewBlockErrorClass() {
      if (this.$isNotEmpty(this.blockError)) {
        return 'blockViewBlockError'
      }
      return ''
    },
    showEInvoice() {
      return this.isApply || this.params.isInBlockTab
    }
  },
  methods: {
    init(params, block) {
      this.params = params
      if (params.mode === '制单' || params.mode === '设计') {
        this.isApply = true
      }
      this.block = block
      if (!params.isDesignMode) {
        params.blockObjMap[`电子发票-${params.dataVo.formType}`] = this
      }
      var exParams = {
        mode: params.mode,
        isRformBlock: params.isRformBlock,
        addAttaTemData: !!params.addAttaTemData, // 判断是否添加临时数据
        blockformName: '发票信息' // 区块组件名
      }
      if (this.showEInvoice) {
        this.$refs.eInvoice.initCommonBiz(params.isEdit, params.dataVo, exParams)
      } else {
        this.$refs.eInvoice.initCommonBizDetails(params.dataVo, exParams)
      }
    },
    updateBeforeSave() {
      return this.$refs.eInvoice.updateBeforeSave()
    }
  }
}
</script>
<style scoped lang="scss">
</style>
