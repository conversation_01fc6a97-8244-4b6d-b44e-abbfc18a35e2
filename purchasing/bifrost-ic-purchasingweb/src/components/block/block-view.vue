<template>
  <div :id="blockViewDivId" style="height: 100%;">
    <div id="formBlockView"
         v-show="isLoadingData"
         v-loading="isLoadingData"
         element-loading-spinner="el-icon-loading"
         element-loading-text="加载中..."
         style="height: 100%"></div>
    <div :class="`blockView ${blockViewExClass}`" v-show="!isLoadingData">
      <div class="blockContent">
        <component v-if="emptyExtendName" v-show="false" ref="blockEmptyExtend" :is="`blockEmptyExtend-${emptyExtendName}`"/>
        <div class="blockContentLeft" ref="blockContentLeft">
          <el-tabs
            v-model="activeTab"
            ref="tabs"
            type="card"
            :class="{
              tabPaneTextAlign: tabs.length === 1
            }"
            :editable="tabEditable"
            :before-leave="handleBeforeLeave"
            @tab-click="handleTabClick"
            @edit="handleTabsEdit"
            @keydown.native.capture.stop.self
            >
            <el-tab-pane
              :key="tab.id"
              :label="tab.label"
              :name="tab.name"
              v-for="(tab, index) in tabs">
              <template slot="label">
                <span
                  v-show="!tab.edit"
                  :ref="'tabLabel' + index"
                  class="tabLabel"
                  @click="handleTabSpanClick"
                  @dblclick="handleTabDbClick(tab, index)">
                  <i :class="selfTabLabelIcon"></i>{{ tab.label }}
                </span>
                <input
                  v-show="tab.edit"
                  v-model="tab.label"
                  :ref="'tabInput' + index"
                  class="tabInput"
                  :style="{ width: tabInputWidth }"
                  @blur="handleEditTabBlur(tab, index)"
                  @keyup.enter="handleEditTabBlur(tab, index)"/>
              </template>
              <keep-alive>
                <block-container
                  ref="tabComponents"
                  :nodes="nodes"
                  :filterNodes="filterNodes"
                  :getNodeIdFromItemKey="getNodeIdFromItemKey"
                  :associationForm="associationForm[originTabs[index].label]"
                  :activeTab="activeTab"
                  :firstTabAssociationForm="firstTabAssociationForm"
                  :getTableData="getTableData"
                  :getTableIndexData="getTableIndexData"
                  :getAssociationForm="getAssociationForm"
                  :setAssociationForm="setAssociationForm"
                  :initResetData="initResetData"
                  @handleRadioChange="handleRadioChange"
                  />
              </keep-alive>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="blockContentRight" v-if="haveRightContent">

        </div>
      </div>
      <footer class="blockFooter el-footer" v-if="showBottomBts">
        <el-button
          size="small"
          plain
          icon="el-icon-d-arrow-left"
          class="btn-normal"
          :disabled="backDisabled"
          @click="doBtEditBack">返回</el-button>
        <el-button
          plain
          size="small"
          icon="el-icon-tickets"
          class="btn-normal"
          :loading="draftLoading"
          :disabled="handleDraftDisabled"
          @click="doBtEditDraft">存草稿</el-button>
        <el-button
          size="small"
          type="primary"
          icon="el-icon-edit"
          class="btn-normal"
          :loading="saveLoading"
          :disabled="saveDisabled"
          @click="doBtEditSave">保存</el-button>
      </footer>
    </div>
  </div>
</template>
<script>
import { getUUID } from '@/utils'
import omit from 'lodash/omit'
export default {
  name: 'block-view',
  provide() {
    return {
      changeBlockLoading: (loading) => this.changeLoading(loading),
      changeCurrentBlockId: (id) => this.changeCurrentBlockId(id),
      getBlockView: () => this.getBlockView()
    }
  },
  inject: {
    changeToggleDisabled: { default: undefined },
    setGlobalTitle: { default: undefined },
    changeShowMask: { default: undefined }
  },
  props: {
    saveBefore: { type: Function, default: undefined },
    nodes: { type: Array, default: () => [] },
    filterNodes: { type: Function, default: function() {} },
    getNodeIdFromItemKey: { type: Function, default: function() {} },
    isInBlockTab: { type: Boolean, default: false }
  },
  data() {
    return {
      isNew: false,
      blockViewDivId: '',
      isLoadingData: true,
      blockView: undefined,
      haveRightContent: false,
      params: {},
      doBtEditBack: undefined,
      activeTab: '基本信息',
      activeTabIndex: 0,
      tabs: [{ id: getUUID(), label: '基本信息', name: '基本信息', edit: false }],
      originTabs: [{ id: getUUID(), label: '基本信息', name: '基本信息', edit: false }],
      emptyExtendName: '',
      oldParams: {},
      backDisabled: false,
      draftLoading: false,
      saveLoading: false,
      draftDisabled: false,
      saveDisabled: false,
      selfTabLabelIcon: '',
      tabInputWidth: '',
      associationForm: {}, // 除第一个tab外其他tab关联表单信息
      firstTabAssociationForm: '', // 第一个tab关联的表单
      leave: false,
      isShowTabs: false
    }
  },
  computed: {
    tabEditable() {
      return this.params.mode === '设计'
    },
    handleDraftDisabled() {
      if (typeof this.params.isRef === 'boolean' && this.params.isRef) {
        return false
      }
      return this.draftDisabledComputed || this.draftDisabled
    },
    blockViewExClass() {
      let exClass = ''
      if ((this.tabs.length <= 1 && this.params.mode !== '设计') || !this.isShowTabs) exClass += ' blockViewHideTab'
      if (this.params.mode === '设计') exClass += ' designMode'
      if (this.params.mode === '制单') exClass += ' fillMode'
      if (this.params.mode === '详情') exClass += ' detailMode'
      if (this.params.mode === '审核') exClass += ' auditMode'
      return exClass
    },
    showBottomBts() {
      return this.params.isEdit && !this.params.isDesignMode && !this.params.isAuditMode && !this.isInBlockTab
    },
    draftDisabledComputed() {
      return this.blockView?.exData?.['不显示存草稿按钮'] === '是'
    },
    showMask() {
      // 其他tab 且tab的类型是关联表单 要禁止点击右侧col-setting
      const show = this.activeTabIndex > 0 && this.associationForm[this.activeTab]?.type === 'form'
      return show || false
    }
  },
  methods: {
    getBlockView() {
      return this.blockView
    },
    handleRadioChange(type) {
      this.$set(this.associationForm[this.activeTab], 'type', type)
      this.changeFormCanvasShowMask()
      if (type === 'form' && this.associationForm[this.activeTab].formType === 'free') {
        this.$nextTick(window.luckysheet?.resize)
      }
    },
    /**
     * 设置关联表单参数
     * @param {*} formObj 关联参数
     */
    setAssociationForm(formObj = {}) {
      const {
        type = 'form', // 关联类型
        blockIds = [], // 关联区块
        formType = '', // 表单类型
        loaded = false, // 是否加载
        itemKey = '', // 关联表单itemKey 目前表单绑定的id是itemKey
        label = '', // 关联表单名称
        isEdit = false, // 是否可编辑表单
        // 下面的参数不需要放到form中
        tabName = '', // tab名称
        updateTabName = '', // 修改后的tab名称
        isDelete = false // 是否删除
      } = formObj
      const form = {
        type,
        formType,
        loaded,
        itemKey,
        label,
        isEdit
      }
      if (type !== 'form') {
        form.blockIds = blockIds
      }
      const ignoreKeys = ['tabName', 'updateTabName', 'isDelete']
      // 如果tabname 存在 则将formObj传的参数替换到对应的tabname对象中
      if (tabName) {
        // 删除tab
        if (isDelete) {
          delete this.associationForm[tabName]
          return
        }
        // 修改tabs名时 要把原有的tab关联的参数删除
        if (updateTabName) {
          this.$set(this.associationForm, updateTabName, this.associationForm[tabName])
          this.$delete(this.associationForm, tabName)
          return
        }
        if (this.associationForm[tabName]) {
          Object.assign(this.associationForm[tabName], omit(formObj, ignoreKeys))
        } else {
          this.$set(this.associationForm, tabName, form)
        }
      }
    },
    setColItemXY(colItem) {
      this.$call(this, 'formFormat', 'setColItemXY', colItem)
    },
    removeColItem(colItem) {
      this.$call(this, 'formFormat', 'removeColItem', colItem)
    },
    colSetcheckedSelectClick(selection, row) {
      this.$call(this, 'formFormat', 'colSetcheckedSelectClick', selection, row)
    },
    editingColItemChanged(isEditing, colItem) {
      this.$call(this.$refs.tabComponents, 'formFormat', 'editingColItemChanged', isEditing, colItem)
    },
    colItemsChange(colItems, isOpeningCanvas) {
      this.$call(this, 'formFormat', 'colItemsChange', colItems, isOpeningCanvas)
    },
    getFormatJson() {
      return this.$call(this, 'formFormat', 'getFormatJson')
    },
    resetColSetting() {
      this.$call(this, 'colSetting', 'resetColSetting')
    },
    changeLoading(loading) {
      this.isLoadingData = loading
      if (!this.isLoadingData) {
        // 如果加载完成 触发区块表单加载完成的 事件总线 用于让rform更新按钮
        window.$event.$emit('blockFormLoaded')
      }
    },
    changeCurrentBlockId(id = '') {
      this.params.currentBlockId = id
    },
    getAllData() {
      const allData = {}
      if (this.$isNotEmpty(this.$refs.tabComponents)) {
        allData.labels = []
        if (this.$refs.tabComponents.length === 1) {
          allData.labels.push(this.tabs[0].label)
          allData[this.tabs[0].label] = this.$refs.tabComponents[0].blocks
        } else {
          this.$refs.tabComponents.forEach((tabComp, index) => {
            const tabsLabel = this.tabs[index].label
            allData.labels.push(tabsLabel)
            if (!index) {
              // 第一个tab
              allData[tabsLabel] = tabComp.blocks
            } else {
              const tabsData = this.params.isNew ? this.params.newBlockView?.[index] : this.params.blockView?.containers?.[index]
              const handleData = {}
              const blocks = tabsData?.blocks
              if (blocks) {
                blocks?.forEach(block => {
                  if (blocks.length === 1) {
                    if (block.dataType === '关联表单') {
                      handleData.dataType = block.dataType
                      handleData.cformModuleId = block?.data?.main?.id
                      handleData.cformModuleName = block?.data?.main?.name || ''
                      handleData.relate = block.dataType
                      handleData.name = tabsLabel || ''
                    } else {
                      // 关联区块的tab
                    }
                  } else {
                    // 关联区块的tab
                  }
                })
              }
              allData[tabsLabel] = handleData
            }
          })
        }
      }
      return allData
    },
    // 初始化数据重置
    initResetData() {
      this.params.newBlockView = {}
      this.activeTab = '基本信息'
      this.activeTabIndex = 0
      this.associationForm = {}
      this.firstTabAssociationForm = ''
      this.changeFormCanvasShowMask()
    },
    init(dlg, params, isNew) {
      this.initResetData()
      this.params = params
      this.isShowTabs = this.params.mode === '设计' ? true : this.params.isShowTabs || false
      this.params.isInBlockTab = this.isInBlockTab
      this.params.isRformBlock = true
      this.params.isNew = isNew
      this.isNew = isNew
      this.params.isPrint = false
      this.params.blockObjMap = {} // 区块对象，比如block-rform.vue
      this.params.blockObjMapById = {} // 区块对象，比如block-rform.vue
      this.params.blockExtendObjMap = {} // 区块扩展对象，比如block-rform-extend-ba.vue
      this.params.isEditing = this.params.isEdit
      this.doBtEditBack = params.doBtEditBack
      this.compInTabObj = {}

      // 如果是跳转制单，可能是修改，需要设置单据ID
      if (this.params.jumpToSaveFormData &&
          this.$isNotEmpty(this.params.jumpToSaveFormData.formId)) {
        this.params.dataId = this.params.jumpToSaveFormData.formId
      }

      if (this.$isNotEmpty(this.params.jumpToSaveFormData) &&
        this.$isNotEmpty(this.params.jumpToSaveFormData.selectCformVoParams)) {
        Object.assign(this.params, this.params.jumpToSaveFormData.selectCformVoParams)
      }

      if (dlg) {
        this.params.isEdit = false
        this.params.mode = '详情'
        if (this.params.exInitData) {
          Object.assign(this.params, this.params.exInitData)
        }
        this.$setDlgSize(dlg, 'globalDialog', 1440, 900)
      }

      const getPropFromPageData = (pageData, labelOrProp) => {
        if (pageData) {
          for (let i = 0; i < pageData.columns; i++) {
            const header = pageData.columns[i]
            if (header.prop === labelOrProp ||
              header.label === labelOrProp) {
              return header.prop
            }
          }
        }
      }

      // 设置区块是否显示
      this.params.setBlockVisible =
        (rformExtendKey, isVisible) => {
          if (this.params.blockObjMap[rformExtendKey] &&
            this.params.blockObjMap[rformExtendKey].setBlockVisible) {
            this.params.blockObjMap[rformExtendKey].setBlockVisible(isVisible)
          }
        }

      // 设置区块数据是否可以为空
      this.params.setBlockCanEmpty =
        (rformExtendKey, canEmpty) => {
          if (this.params.blockObjMap[rformExtendKey] &&
            this.params.blockObjMap[rformExtendKey].setBlockCanEmpty) {
            this.params.blockObjMap[rformExtendKey].setBlockCanEmpty(canEmpty)
          }
        }

      // 设置行表单某个区块的某个单元格的值
      // rformExtendKey = 行表区块的扩展关键字，比如PurDetailEntity，RelevantBaEntity
      this.params.setCellValue =
        (rformExtendKey, rowIdOrRowIndex, propOrLabel, value) => {
          if (this.params.blockObjMap[rformExtendKey] &&
              this.params.blockObjMap[rformExtendKey].setCellValue) {
            this.params.blockObjMap[rformExtendKey].setCellValue(
              rowIdOrRowIndex, propOrLabel, value)
          }
        }

      // 设置行表单某个区块的某个单元格是否可用
      this.params.setCellDisable =
        (rformExtendKey, rowIdOrRowIndex, label, disabled) => {
          if (this.params.blockObjMap[rformExtendKey] &&
            this.params.blockObjMap[rformExtendKey].setCellDisable) {
            this.params.blockObjMap[rformExtendKey].setCellDisable(
              rowIdOrRowIndex, label, disabled)
          }
        }

      // 获取区块对象
      this.params.getBlockObj = (targetClassName) => {
        return this.params.blockObjMap[targetClassName]
      }

      // 获取行表单区块的扩展对象，比如block-rform-extend-ba.vue
      this.params.getBlockExtendObj = (targetClassName) => {
        return this.params.blockExtendObjMap[targetClassName]
      }

      // 判断当前是否有指定的行表单区块
      this.params.hasBlockExtend = (targetClassName) => {
        return (this.params.getBlockExtendObj(targetClassName) !== undefined)
      }

      // 这个方法处理行表单通用模式：B区块的数据依赖A区块的数据，依赖关系由外键决定
      // 这时，如果A模块相应的行删除后，B中关联到A中的对应数据需要删除
      this.params.deleteBlockRowsByFk = (extendKeyA, fkA, extendKeyB, fkB) => {
        if (fkA === undefined) {
          this.$message.error('必须指定fkA')
          return
        }
        fkB = fkB || fkA // fkB不指定时，取fkB = fkA

        let pageData = this.params.getRformPageData(extendKeyA)
        if (pageData) {
          const fkAIds = []
          pageData.rows.forEach(row => {
            if (row[fkA]) {
              fkAIds.push(row[fkA])
            }
          })

          pageData = params.getRformPageData(extendKeyB)
          if (pageData) {
            for (let i = pageData.rows.length - 1; i >= 0; i--) {
              const row = pageData.rows[i]
              const fkBId = this.$getRealValueRefID(row[fkB])
              if (fkAIds.indexOf(fkBId) < 0) {
                pageData.rows.splice(i, 1)
              }
            }
          }
        }
      }

      this.params.getRformPageData = (targetClassName) => {
        if (this.params.blockObjMap[targetClassName]) {
          const rformBlockObj = this.params.blockObjMap[targetClassName]
          return rformBlockObj.block.pageData
        }
      }
      this.params.setRformCell = (targetClassName, rowIndex, colName, value) => {
        const pageData = this.params.getRformPageData(targetClassName)
        const prop = getPropFromPageData(pageData, colName)
        if (prop && pageData.rows.length > rowIndex) {
          pageData.rows[rowIndex][prop] = value
        }
      }
      this.params.getRformCell = (targetClassName, rowIndex, colName) => {
        const pageData = this.params.getRformPageData(targetClassName)
        const prop = getPropFromPageData(pageData, colName)
        if (prop && pageData.rows.length > rowIndex) {
          return pageData.rows[rowIndex][prop]
        }
      }
      this.params.getBlockViewDivId = () => { return this.blockViewDivId }
      this.params.hasBlock = (targetClassName) => {
        for (let i = 0; i < this.blockView.containers.length; i++) {
          const container = this.blockView.containers[i]
          for (let j = 0; j < container.blocks.length; j++) {
            const block = container.blocks[j]
            if (block.targetClassName === targetClassName) {
              return true
            }
          }
        }
        return false
      }

      // 初始化时设置区块隐藏的按钮
      this.params.setBlockHiddenButtons = (targetClassName, hiddenButtons) => {
        hiddenButtons = hiddenButtons || []
        this.params[targetClassName + '_隐藏按钮'] = hiddenButtons
      }

      // 获取区块隐藏按钮的设置
      this.params.getBlockHiddenButtons = (targetClassName) => {
        let hiddenButtons = this.params[targetClassName + '_隐藏按钮']
        hiddenButtons = hiddenButtons || []
        return hiddenButtons
      }

      this.params.setBlockHideAllButtons = (targetClassName, hideAllButtons) => {
        this.params[targetClassName + '_隐藏所有按钮'] = hideAllButtons
      }

      // 获取区块隐藏按钮的设置
      this.params.getBlockHideAllButtons = (targetClassName) => {
        let hideAllButtons = this.params[targetClassName + '_隐藏所有按钮']
        if (hideAllButtons === undefined) {
          hideAllButtons = false
        }
        return hideAllButtons
      }
      this.oldParams = this.$clone(this.params)
      this.isLoadingData = true
      if (this.params.isDesignMode) {
        this.tabs = [{ label: '基本信息', name: '基本信息', edit: false }]
        this.originTabs = [{ label: '基本信息', name: '基本信息', edit: false }]

        this.blockViewDivId = `blockViewDivId${this.params.mode}${new Date().getTime()}`

        if (!this.isInBlockTab) {
          this.params?.blockView?.containers?.map((container, index) => {
            this.setInfo(container, index)
          })
        }
        this.$nextTick(() => {
          this.changeFormCanvasShowMask()
          const comps = this.$refs.tabComponents
          // comps[0].init(this.params, 0)
          for (let i = 0; i < comps.length; i++) {
            comps[i].init(this.params, i)
          }
          this.initEmptyExtend(this.params)
        })
      } else {
        this.tabs = [{ label: '基本信息', name: '基本信息', edit: false }]
        this.originTabs = [{ label: '基本信息', name: '基本信息', edit: false }]
        this.selectCformVo()
      }
    },
    setInfo(container, index) {
      // 如果不是第一个container
      if (index) {
        const name = container.name ? container.name : container.blocks[0].cformModuleName
        this.tabs.push({ id: getUUID(), label: name, name: name })
        this.originTabs.push({ id: getUUID(), label: name, name: name })
        let formType = 'block'
        let type = 'form'
        let itemKey = ''
        const blocks = container?.blocks
        if (blocks) {
                // 有blocks则不是新增 是后端返回
                blocks?.forEach(block => {
                  if (blocks?.length === 1) {
                    if (block?.dataType === '关联表单') {
                      // 关联表单
                      type = 'form'
                      const main = block?.data?.main ? block?.data?.main : block?.data?.meta
                      formType = main?.isFreedomForm ? 'free' : main?.isBlockForm ? 'block' : 'regular'
                      itemKey = main?.id
                    } else {
                      // console.log('关联区块', block)
                    }
                  } else {
                    // console.log('关联区块', block)
                  }
                })
        }
        this.setAssociationForm({
          type,
          formType,
          itemKey,
          label: name,
          isEdit: false,
          tabName: name
        })
      } else {
        this.firstTabAssociationForm = container?.blocks?.[0]?.data?.main?.id
      }
    },
    initEmptyExtend(params = {}) { // 初始化没有行表单时的组件
      if (this.params.isNew) return
      const blocks = params.blockView?.containers?.[0]?.blocks || []
      this.hasRform = false
      this.emptyExtendName = blocks?.[0]?.data?.main?.formType
      if (!this.emptyExtendName && blocks?.[0]?.data?.formType) {
        this.emptyExtendName = blocks?.[0]?.data?.formType
      }
      const dinamicComp = `blockEmptyExtend-${this.emptyExtendName}`
      // 如果不存在组件 则不渲染
      if (!window.$viewNames?.[dinamicComp]) {
        this.emptyExtendName = ''
      }

      const wrapFormCellChanged = (params) => {
        // selectedData 弹框选择时会传递当前行选择的数据， 注意其他情况为空
        const formCellChanged = params.formCellChanged
        params.formCellChanged = (colItem, selectedData) => {
          formCellChanged?.(colItem, selectedData)
          this.$refs.blockEmptyExtend?.formCellChanged?.(colItem, params, selectedData)
        }
      }

      const wrapRefDataAfter = (params) => {
        const refDataAfter = params.refDataAfter
        params.refDataAfter = (item, selectedData, fcModel) => {
          refDataAfter?.(item, selectedData, fcModel)
          this.$refs.blockEmptyExtend?.refDataAfter?.(item, selectedData, fcModel, params)
        }
      }

      wrapFormCellChanged(params)
      wrapRefDataAfter(params)
      this.$nextTick(() => {
        this.$refs.blockEmptyExtend?.wrapCallback?.(params)
        params.blockExtendObjMap[this.emptyExtendName] = this.$refs.blockEmptyExtend
        this.$refs.blockEmptyExtend?.handleInitParams?.(params)
        params.formTypeExtend = this.$refs.blockEmptyExtend
      })
    },
    selectCformVo() {
      const callBack = (result = {}) => {
        this.blockView = result
        if (this.params.isRef) {
          this.blockView.containers[0].blocks[0].data.extData['isRef'] = this.params.isRef
        }
        this.params.blockView = this.blockView
        this.blockViewDivId = `blockViewDivId${this.params.mode}${this.blockView.id}`

        let firstTab
        if (!this.isInBlockTab) {
          this.blockView.containers.map((container, index) => {
            this.setInfo(container, index)
            const name = this.$isNotEmpty(container.blocks[0].cformModuleName) ? container.blocks[0].cformModuleName : '基本信息'
            if (this.$isEmpty(firstTab)) {
              firstTab = name
            }
          })
          // for (let i = 0; i < this.blockView.containers.length; i++) {
          //   const container = this.blockView.containers[i]
          //   // container.name = '基本信息'
          //   // const name = this.$isEmpty(container.name) ? '页签' + i : container.name
          //   const name = this.$isNotEmpty(container.blocks[0].cformModuleName) ? container.blocks[0].cformModuleName : '基本信息'
          //   const tab = { id: getUUID(), label: name, name: name }
          //   this.tabs.push(tab)
          //   this.originTabs.push(tab)
          //   if (this.$isEmpty(firstTab)) {
          //     firstTab = name
          //   }
          //   this.setInfo(container, index)
          // }
        }

        this.$nextTick(() => {
          this.initEmptyExtend(this.params)

          const comps = this.$refs.tabComponents
          comps[0].init(this.params, 0)
          // if (this.isInBlockTab) {
          //   this.params.isInBlockTab = true
          //   // tabs下的区块只加载第一个
          //   comps[0].init(this.params, 0)
          // } else {
          //   for (let i = 0; i < comps.length; i++) {
          //     comps[i].init(this.params, i)
          //   }
          // }

          if (this.$isNotEmpty(firstTab)) {
            this.activeTab = firstTab
            this.isLoadingData = false
          }
        })
      }
      const cFromVo = this.params.jumpToSaveFormData?.cFormVo
      // const cFromVo = this.params.jumpToSaveFormData?.cFormVo || (this.isInBlockTab ? this.$clone(blockEmptyTabsData) : '')
      if (this.$isNotEmpty(cFromVo)) { // 添加临时数据（附件和发票）
        this.params.addAttaTemData = true
        callBack(cFromVo)
        return
      }
      const params = { ...this.params, refFormSelectedData: null }
      this.$callApiParams('selectCformVo', params,
        (result) => {
          this.setGlobalTitle?.(result.data.containers[0].blocks[0].data.meta.name)
          this.$emit('modifyTabs', !!result.attributes.auditScreenMetaId, result)
          // result.data.containers.push(mockData)
          callBack(result.data)
          return true
        })
    },
    doBtEditDraft() {
      this.doDraftSave(true)
    },
    doBtEditSave() {
      this.doDraftSave(false)
    },
    doDraftSave(isDraft) {
      const callbackFailed = (result) => {
        this.showError(result, isDraft)
      }

      const callbackSaveSuccess = (result) => {
        this.setLoading(isDraft, false)
        this.params.callbackSaveSuccess(result)
        this.$refreshCount(this)
      }

      let saveConfirmMessage = this.params.saveConfirmMessage
      saveConfirmMessage = saveConfirmMessage || '确定执行保存吗?'
      if (isDraft) {
        saveConfirmMessage = '#不需要确认#'
      }

      const callbackBeforeCallApi = () => this.setLoading(isDraft, true)
      const callback = (dataVo, blockView) => {
        this.saveBefore?.(dataVo)
        if (typeof this.params?.customSaveAction === 'function') {
          // 区块自定义调用保存动作
          const saveAction = (noConfirm, extra) => {
            const message = noConfirm ? '#不需要确认#' : '确定执行保存吗?'
            const apiKey = 'saveBlockData'
            const customConfirmSetting = dataVo?.extData?.['customConfirmSetting']
            if (customConfirmSetting) {
              customConfirmSetting.message = message
              this.$callApiConfirmCustom(customConfirmSetting, apiKey, blockView, callbackSaveSuccess, callbackFailed, extra)
            } else {
              const callbackBeforeCallApi = () => {
                this.saveDisabled = true
              }
              this.$callApiConfirm(message, callbackBeforeCallApi, apiKey, blockView, callbackSaveSuccess, callbackFailed, extra)
            }
          }

          this.params.customSaveAction(saveAction, dataVo, this.params?.refFormSelectedData, { isDraft })
        } else {
          this.$callApiConfirm(
            saveConfirmMessage, callbackBeforeCallApi,
            'saveBlockData',
            blockView, callbackSaveSuccess, callbackFailed)
        }
      }
      this.wrapDataAndAction(isDraft, callback)
    },
    wrapDataAndAction(isDraft, callback) {
      if (isDraft === undefined) {
        isDraft = false
      }

      const comps = this.$refs.tabComponents
      this.blockView.exData['是否草稿'] = isDraft ? '是' : '否'
      const updateAfter = () => { // 规则数据更新后的回调
        this.$nextTick(() => {
          // 由于this.blockView与页面存在双向绑定，所以需要
          // 先拷贝一个备份，然后再处理数组对象转为字符串，涉及的场景有多选和默认值
          const blockView = this.$clone(this.blockView)
          blockView.containers.forEach(con => {
            con.blocks.forEach(bk => {
              if (bk.pageData) {
                this.$transferCheckboxData(bk.pageData, true)
              }
            })
          })

          if (callback) {
            let dataVo
            blockView.containers.forEach(con => {
              con.blocks.forEach(block => {
                if (block.handlerKey === '表单') {
                  dataVo = block.data
                } else {
                  // 检测是否行表单内
                  const rows = block?.pageData?.rows
                  const handleRows = this.checkEmptyRow(rows) || []
                  block.pageData.rows = handleRows
                }
              })
            })
            callback(dataVo, blockView)
          }
        })
      }
      for (let i = 0; i < comps.length; i++) {
        if (comps[i].updateBeforeSave) {
          comps[i].updateBeforeSave(isDraft, updateAfter)
        }
      }
    },
    checkEmptyRow(rows) {
      if (!rows.length) return []
      const arr = []
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i]
        if (!Object.keys(row).length) {
          break
        }
        const cloneRow = this.$clone(row)
        if (cloneRow.id) {
          delete cloneRow.id
        }
        const values = Object.values(cloneRow)
        const isEmptyRow = values.every(item => !item)
        if (!isEmptyRow) {
          arr.push(row)
        }
      }
      return arr
    },
    getCformBlock() {
      const comps = this.$refs.tabComponents
      for (let i = 0; i < comps.length; i++) {
        const cformBlock = comps[i].getCformBlock()
        if (cformBlock) {
          return cformBlock
        }
      }
    },
    showError(result, isDraft) {
      let errorReturn

      if (isDraft !== undefined) {
        this.setLoading(isDraft, false)
      }
      const comps = this.$refs.tabComponents
      for (let i = 0; i < comps.length; i++) {
        if (comps[i].showError) {
          const retTemp = comps[i].showError(result)
          if (retTemp !== undefined && errorReturn === undefined) {
            errorReturn = retTemp
          }
        }
      }
      return errorReturn
    },
    setLoading(isDraft, isLoading) {
      this.draftLoading = false
      this.saveLoading = false

      this.backDisabled = false
      this.draftDisabled = this.draftDisabledComputed
      this.saveDisabled = false

      if (isLoading) {
        this.backDisabled = true
        if (isDraft) {
          this.saveDisabled = true
        } else {
          this.draftDisabled = true
        }
      }

      this.$nextTick(() => {
        if (isDraft) {
          this.draftLoading = isLoading
        } else {
          this.saveLoading = isLoading
        }
      })
    },
    changeFormCanvasShowMask() {
      this.changeShowMask?.(this.showMask)
    },
    handleTabSpanClick(e) {
      if (e.type === 'dblclick') {
        e.preventDefault()
        e.stopPropagation()
      }
    },
    // 双击修改tab
    handleTabDbClick(tab, index) {
      if (!index || this.params.mode !== '设计') return // 第一个不响应 || 除了设计模式
      const tabLabelWidth = this.$refs[`tabLabel${index}`][0].getBoundingClientRect().width
      this.tabInputWidth = tabLabelWidth + 'px'

      this.$set(tab, 'edit', true)
      setTimeout(() => {
        this.$refs[`tabInput${index}`][0].focus()
      })
    },
    handleEditInputClick(tab, index) {
      // 该事件在编辑tab时点击input会阻止tabClick
    },
    handleEditTabBlur(tab, index) {
      if (!tab.label) {
        this.handleTabsEdit(this.originTabs[index].name, 'remove')
        return
      }
      const isRepeat = this.tabs.map(t => t.label).filter((label, key) => key !== index).includes(tab.label)
      if (isRepeat) {
        this.$message.error('页签名称不能重复, 请重新命名')
        this.tabs = this.originTabs
        return
      }
      // 如果修改的名字不一样才进行替换
      if (this.originTabs[index].label !== tab.label) {
        this.setAssociationForm({
          tabName: this.originTabs[index].label,
          updateTabName: tab.label
        })
      }
      this.$set(tab, 'edit', false)
      this.$set(tab, 'name', tab.label)
      this.activeTab = tab.label
      this.activeTabIndex = index
      this.originTabs = this.$clone(this.tabs)
    },
    handleTabClick(e) {
      this.loadTabsContent(+e.index)
    },
    /**
     * 加载对应tab内容
     * @param {number} index tab下标
     * @param {boolean} isDelete 是否是删除tab
     */
    loadTabsContent(index, isDelete = false) {
      if (this.leave) {
        this.leave = false
        // 设置当前选中的下标
        this.activeTabIndex = index
        // 第一个主tab || 当前处于编辑状态 || 删除tab且关闭后只剩下一个tab
        const unnecessaryLoad = !index || this.tabs[index]?.edit || (isDelete && this.tabs.length === 1)
        // 切换到第一个tab 把切换按钮置为可点击
        if (!index) {
          this.changeToggleDisabled?.(true, this.activeTabIndex)
        }
        // 禁止其他tab切换右侧col-setting
        this.changeFormCanvasShowMask()
        if (unnecessaryLoad) return
        const currentIndex = (index === this.tabs.length) ? index - 1 : index
        const currentTabInfo = this.associationForm[this.tabs[currentIndex].label]
        // 切换tab关联表单还是区块
        this.$refs.tabComponents[currentIndex]?.changeData('association', currentTabInfo.type)
        this.changeToggleDisabled?.(currentTabInfo.type === 'form', this.activeTabIndex)
        const isFreeForm = this.params.isNew ? this.params.newBlockView?.[currentIndex]?.blocks[0]?.data?.main?.isFreedomForm : this.params.blockView?.containers?.[currentIndex]?.blocks[0]?.data?.main?.isFreedomForm
        if (!currentTabInfo.loaded) {
          currentTabInfo.loaded = true
          setTimeout(() => {
            this.$refs.tabComponents[currentIndex].init(this.params, currentIndex)
          })
        } else if (isFreeForm) {
          // 自由表单初始化后再次进入需要resize
          this.$nextTick(window.luckysheet?.resize)
        }
      }
    },
    handleBeforeLeave(activeTab, oldTab) {
      this.leave = true
    },
    /**
     * 增删tab
     * @param {Object} targetName tab名称
     * @param {String} action 操作方式 add | remove
     */
    handleTabsEdit(targetName, action) {
      if (action === 'add') {
        let tabIndex = this.tabs.length
        // const newTabName = ++this.tabIndex + ''
        const newTabName = ++tabIndex + ''
        const newTab = {
          label: '页签' + newTabName,
          name: '页签' + newTabName,
          edit: false
        }
        this.tabs.push(newTab)
        this.setAssociationForm({ tabName: newTab.name })
        this.activeTab = newTab.label
        this.activeTabIndex = this.tabs.length - 1
        this.changeToggleDisabled?.(true, this.activeTabIndex)
      } else if (action === 'remove') {
        const tabs = this.tabs
        let activeName = this.activeTab
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              const nextTab = tabs[index + 1] || tabs[index - 1]
              if (nextTab) {
                activeName = nextTab.name
                this.activeTabIndex = index
              }
            }
          })
        }
        this.activeTab = activeName
        this.tabs = tabs.filter((tab, index) => {
          if (tab.name === targetName) {
            const isFreeForm = this.params.blockView?.containers?.[index]?.main?.isFreedomForm
            // 如果是自由表单 要释放
            if (isFreeForm) this.$nextTick(window.luckysheet?.destroy)
            this.params.blockView?.containers?.splice(index, 1)
          }
          return tab.name !== targetName
        })
        this.setAssociationForm({ tabName: targetName, isDelete: true })
      }
      this.$nextTick(() => {
        this.loadTabsContent(this.activeTabIndex, action === 'remove')
      })
      this.originTabs = this.$clone(this.tabs)
    },
    getTableData() {
      return this.tabs
    },
    getTableIndexData() {
      return this.activeTabIndex
    },
    getAssociationForm() {
      return this.associationForm
    },
    /**
     * 处理表单组件点击
     * @param {Object} rForm 当前选择的表单组件
     */
    handleCompItemClick(rForm) {
      const tabComps = this.$refs?.tabComponents
      if (Array.isArray(tabComps)) {
        // 多tab
        for (let i = 0; i < tabComps?.length; i++) {
          const tabComp = tabComps[i]
          if (this.activeTabIndex === i) {
            tabComp?.addNewRform(rForm)
          }
        }
      } else {
        tabComps?.addNewRform(rForm)
      }
    },
    resetBlockTabsConfig() {
      this.activeTab = '基本信息'
      this.activeTabIndex = 0
      Object.values(this.associationForm)?.forEach(formValue => {
        formValue.loaded = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
  * {margin: 0 0;padding: 0 0;}
  .blockView {
    display: flex;
    flex-direction: column;
    height: 100%;
    /deep/.el-tab-pane {
      height: 100%;
    }
    .blockContent {
      flex: 1 1 0%;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      .blockContentLeft {
        position: relative;
        flex: 1;
        display:flex;
        flex-direction: column;
        overflow-y: auto;
        .el-tabs {
          height: 100%;
        }
        .el-tab-pane {
          &:first-child {
            height: auto;
          }
        }
        .tabPaneTextAlign {
          /deep/ .el-tabs__item {
            padding-left: 12px;
          }
        }
      }
    }
  }
  .blockView .blockContent .blockContentRight { width: 280px;overflow-y: auto; }
  .blockView .blockFooter { height: 46px; padding: 12px 0px 0px; text-align: right; }

  .blockView /deep/ .blockContentLeft .el-tabs__content { padding-right: 2px !important; }
  .blockView /deep/ .blockContentLeft .blockViewBlockCform .el-tabs__content { padding-right: 0px !important; }
  // .blockView /deep/ .el-tabs__header {
  //   &.is-top {
  //     width: calc(100% - 70px);
  //   }
  // }
  .blockViewHideTab /deep/ .el-tabs__header { display: none; }

  /*表单第一样式*/
  /*#bifrost-ic-app .blockView/deep/ .formRegularContainer .formCommonCols .el-form-item--small.el-form-item,*/
  /*.blockView/deep/ .formCommonCols .form-create { background: #f0f5ff !important; }*/
  /*.blockView/deep/ .formCommonCols .el-form-itemm--mini.el-form-item,*/
  /*.blockView/deep/ .formCommonCols .el-form-item--small.el-form-item { border: 1px solid #ddd !important; }*/
  /*.blockView/deep/ .formCommonCols .el-form-item--mini.el-form-item,*/
  /*.blockView/deep/ .formCommonCols .el-form-item--small.el-form-item { border: 1px solid #ddd !important; }*/
  /*.blockView/deep/ .formCommonCols .el-form-item__content { border-left: 1px solid #ddd; }*/
  /*.blockView/deep/ .formRegularColbottomBorber { border-bottom: 1px solid #ddd; }*/

  /*表单第二样式*/
  #bifrost-ic-app .blockView /deep/ .formRegularContainer .formCommonCols .el-form-item--small.el-form-item,
  .blockView /deep/ .formCommonCols .form-create { background: none !important; border: none; }
  .blockView /deep/ .formCommonCols .el-form-itemm--mini.el-form-item,
  .blockView /deep/ .formCommonCols .el-form-item--small.el-form-item { border: none !important; }
  .blockView /deep/ .formCommonCols .el-form-item--mini.el-form-item,
  .blockView /deep/ .formCommonCols .el-form-item--small.el-form-item { border: none !important; }
  .blockView /deep/ .formCommonCols .el-form-item__content { border-left: none !important; background: #f0f5ff; }
  .blockView /deep/ .formRegularColbottomBorber { border-bottom: none !important;background: #f0f5ff;padding: 7px 8px;border-radius: 2px; }
  .blockView /deep/ .formRegularColbottomBorber.formRegularNotYPadding { padding: 0 0; }
  .blockView /deep/ .formCommonCols .el-select input,
  .blockView /deep/ .formCommonCols .colitem-button input,
  .blockView /deep/ .formCommonCols textarea,
  .blockView /deep/ .formCommonCols .el-input__inner { border: 1px solid #ddd !important; }
  .blockView /deep/ .formCanvasRuntimeRegularEdit .formCommonCols textarea[disabled='disabled'] { background-color: #efefef !important; }

  .blockView /deep/ .el-col { margin: 3px 0px; padding-left: 1px; }
  .blockView /deep/ .el-input-number__decrease,
  .blockView /deep/ .el-input-number__increase { top: 2px !important; }
  .blockView /deep/ .el-table.el-table-no-bottom { border-bottom: none !important; }
  /*.blockView/deep/ .blockViewBlock .el-table.last-block-table { border-bottom: 1px solid #ddd !important; }*/
  /*.blockView/deep/ .blockViewBlock > div:last-child { flex: 1 !important; min-height: 150px !important; }*/

  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .form-create .el-radio__inner,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .form-create .el-textarea__inner,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .form-create .el-checkbox__inner,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .form-create .el-input__inner,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .form-create .el-input-group__append {
    border: 1px solid #ddd !important;
  }

  .blockView /deep/ .formCommonCols .el-col .el-error .el-input__inner,
  .blockView /deep/ .formCommonCols .el-col .el-error .el-textarea__inner,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .form-create .el-error .el-input__inner,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .form-create .el-error .el-textarea__inner {
    border: 1px solid #ff5c00 !important;
  }
  .wf-audit-content.wf-audit-content-multiple-tabs .blockView /deep/ .common-page .receptacle-border .column-top {
    position: relative !important;}

  .blockView /deep/ #formFreeView { padding: 0px !important; }
  .blockView /deep/ .formCommonHeader { height: 35px !important; padding-top: 0px !important; margin-bottom: 5px; }
  .blockView /deep/ .formCommonHeader .formCommonTitle span span { padding: 0 25px; }
  .blockView /deep/ .formCommonTitle { height: 35px !important; }
  .blockView /deep/ .formCommonTitle span { border-bottom: none; }
  .blockView /deep/ .formCommonTitle span span { border-bottom: none; }
  .blockView /deep/ .el-table__empty-block { min-height: 50px !important; }
  .blockView /deep/ .common-page .main-border { padding-top: 10px !important; }
  .blockView /deep/ .common-page .buttons-normal { padding-top: 0px !important; }
  .blockView /deep/ .blockViewBlockHideAllButtons .common-page .buttons-normal { padding-top: 0px !important; }
  .blockView /deep/ .common-page .el-table--small.editing-table .el-table__cell { padding: 2px 0px !important; }
  .blockView /deep/ .formRegularContainer .formCommonCols .el-form-item--small.el-form-item { background: #f0f5ff !important; }

  .blockView /deep/ .el-button.is-disabled.is-plain,
  .blockView /deep/ .el-button.is-disabled.is-plain:focus,
  .blockView /deep/ .el-button.is-disabled.is-plain:hover,
  .blockView /deep/ .el-button.is-disabled.is-plain:active {
    // background-color: #FFF !important;
    // border-color: #EBEEF5 !important;
    // color: #C0C4CC !important;
  }

  .blockView /deep/ .blockViewBlock { height: 100%; }
  .blockView /deep/ .blockViewBlock .formCommonCols .colItemLabel {
    border: 1px solid transparent;
  }
  .blockView /deep/ .blockViewBlock .formCommonCols .colItemLabel:hover {
    border: 1px solid #6eb7fb;
  }
  .blockView /deep/ .blockViewBlockError { position: relative; }
  .blockView /deep/ .blockViewBlockError .common-page .el-table--group,
  .blockView /deep/ .blockViewBlockError .common-page .el-table--border { border: 1px solid #ff5c00 !important; }
  .blockView /deep/ .blockViewBlockError .blockViewBlockErrorSpan {
    position: absolute;
    top: 37px;
    right: 1px;
    z-index: 10000;
    color: #ff5c00;
    font-size: 12px;
    line-height: 12px;
    height: 12px; }

  .blockView.fillMode /deep/ .el-date-editor .el-input__prefix,
  .blockView.designMode /deep/ .el-date-editor .el-input__prefix { top: -3px !important; }
  .blockView.detailMode /deep/ .el-input__prefix,
  .blockView.auditMode /deep/ .el-input__prefix { display: none; }
  .blockView.detailMode /deep/ .el-date-editor .el-input__inner,
  .blockView.auditMode /deep/ .el-date-editor .el-input__inner { padding-left: 5px; }
  .blockView.detailMode /deep/ .blockViewBlockRform .el-table .cell,
  .blockView.auditMode /deep/ .blockViewBlockRform .el-table .cell { line-height: 32px; }

  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .formRegularCompact .formCommonCols .el-input__inner.auditCanEdit,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .formCommonCols .el-input__inner.auditCanEdit,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .formRegularCompact .formCommonCols .el-textarea__inner.auditCanEdit,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .formCommonCols .el-textarea__inner.auditCanEdit,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .customDom .customSelect.auditCanEdit .el-input--small .el-input__inner,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .customDom .customRadio.auditCanEdit .el-radio__label,
  #wf-audit-content .wf-audit-detail .common-page .blockView /deep/ .customDom .customInput .el-input__inner.auditCanEdit {
    color: #606266 !important;
    border: 2px solid #2fbefc !important; }

  .blockView.auditMode /deep/ .blockViewBlockFile .el-table .el-table__cell .cell,
  .blockView.detailMode /deep/ .blockViewBlockFile .el-table .el-table__cell .cell{
    height: 28px;
    line-height: 28px;
  }

  .blockView /deep/ .el-radio__input.is-checked .el-radio__inner {
    border-color: #409EFF !important;
  }
  .blockView /deep/.form_settings {
    height: 650px;
  }
  .blockView .blockContent /deep/ .el-button.el-button--text {
    padding: 0 !important;
    color: #606266;
    &:hover,
    &:focus {
      color: #606266 !important;
      background-color: #fff !important;
    }
  }
  .blockView /deep/ {
    .blockContentLeft {
      > .el-tabs {
        .el-tabs__header {
          $primaryColor: #1F7FFF;
          $height: 32px;
          .el-tabs__new-tab {
            border: 1px solid $primaryColor;
            background: $primaryColor;
            color: #FFF;
            margin: 0 9px 0;
            width: 32px;
            height: $height;
            .el-icon-plus {
              line-height: $height;
            }
          }
          .el-tabs__item {
            &:not(:first-child) {
              .tabLabel {
                padding: 6px 0;
              }
            }
            .tabInput {
              outline: none;
              border: none;
              color: #333333;
            }
            &:first-child {
              .el-icon-close {
                display: none;
              }
            }
          }
        }
        .el-tab-pane {
          .formCommonCols {
            &.formFreeView {
              .el-input__inner {
                border: none !important;
              }
            }
          }
        }
      }
    }
    .summaryClass {
      border-bottom: none !important;
    }
    .noSummaryClass {
      .el-table__body-wrapper {
        tr {
          &:last-of-type {
            td {
              border-bottom: none !important;
            }
          }
        }
      }
    }
  }
</style>
