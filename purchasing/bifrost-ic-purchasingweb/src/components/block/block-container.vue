<template>
  <div class="blockTab">
    <el-radio-group v-show="showAssociationRadio" v-model="association" @input="handleRadioChange">
      <el-radio label="form">关联表单</el-radio>
      <el-radio label="block">关联区块</el-radio>
    </el-radio-group>
    <div class="associationForm" :class="{
      'mt-10': showAssociationRadio
    }">
      <div v-show="showAssociationCtrl" class="choiceForm">
        <el-button type="primary" size="mini" @click="changeDlgShow(true)">选择关联表单</el-button>
        <el-link
          v-show="assoForm"
          class="tip"
          type="primary"
          :underline="false"
          @click="jumpForm">已关联表单: {{ assoForm }}</el-link>
      </div>
      <div v-if="showForm" class="form" :class="{
        'mt-10': showForm
      }">
        <form-canvas v-if="!isBlockForm" ref="formCanvas" />
        <block-view v-else ref="blockForm" :isInBlockTab="true"/>
      </div>
    </div>
    <div v-show="showBlockRform" :class="{
      'mt-10': showAssociationCtrl || showForm
    }">
      <div class="blockViewBlock">
        <draggable
          v-model="blocks"
          handle=".dragIcon"
          v-bind="dragOptions"
          :move="blockMove"
          @start="dragStart"
          @end="dragEnd"
        >
          <transition-group type="transition" :name="!drag ? 'flip-list' : null">
            <div v-for="(bk, index) in blocks"
              :key="bk.id"
              :data-index="rowFormIndex(bk, index)"
              :id="`block${params.mode}${bk.id}`"
              class="blockViewBlockReal"
              :class="{ settingBorder: index === currentBlock }"
              :style="{ marginTop: index !== 0 ? '10px' : '' }"
              @mouseenter="mouseenter(index)"
              @mouseleave="mouseleave">
              <component
                ref="blockComponents"
                :is="`block-${bk.handlerKey}`"/>
              <div class="settingIcon" v-show="showIcon(index)">
                <el-tooltip content="组件管理" placement="top">
                  <el-button size="mini" type="primary" v-show="bk.handlerKey === '行表单'" icon="el-icon-setting" circle @click="() => rformSetting(index, bk)"></el-button>
                </el-tooltip>
                <el-tooltip content="删除组件" placement="top">
                  <el-button size="mini" type="danger" icon="el-icon-delete" circle @click="() => delRform(index, bk)"></el-button>
                </el-tooltip>
                <el-tooltip content="拖动组件" placement="top">
                  <el-button class="dragIcon" size="mini" type="primary" icon="el-icon-rank" circle></el-button>
                </el-tooltip>
              </div>
            </div>
          </transition-group>
        </draggable>
      </div>
    </div>
    <block-association-cform-dialog
      ref="associationCformDlg"
      :show="showAssociationCformDlg"
      :nodes="nodes"
      @changeDlgShow="changeDlgShow"
    />
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { mixins } from '@/mixin'
export default {
  name: 'block-container',
  mixins: [mixins],
  components: {
    draggable
  },
  props: {
    nodes: { type: Array, default: () => [] }, // 关联表单节点
    filterNodes: { type: Function, default: function() {} }, // 过滤关联表单节点的方法
    getNodeIdFromItemKey: { type: Function, default: function() {} }, // 根据itemKey获取节点id
    associationForm: { type: Object, default: () => {} }, // 当前tab关联表单的信息
    activeTab: { type: String, default: '' }, // 当前选中tab
    firstTabAssociationForm: { type: String, default: '' }, // 第一个tab关联的表单
    getTableData: { type: Function, default: function() {} }, // 获取信息
    getTableIndexData: { type: Function, default: function() {} }, // 获取信息
    getAssociationForm: { type: Function, default: function() {} }, // 获取信息
    setAssociationForm: { type: Function, default: function() { } }, // 设置关联表单信息
    initResetData: { type: Function, default: function() { } } // 初始化数据重置
  },
  data() {
    return {
      params: undefined,
      blocks: [],
      currentBlock: null,
      drag: false,
      headerLevel: 0,
      association: 'form', // 选择关联区块还是关联表单
      showAssociationCformDlg: false, // 关联表单弹窗开关
      formType: '', // 表单类型
      currentTabParams: {},
      // mode: '制单', // 详情
      mode: '详情', // 详情
      isEdit: false, // 是否可编辑
      isDesignMode: false, // 是否是设计模式
      clearRformSelected: true, // 是否清除表单组件选中边框
      isFreeForm: false,
      assoForm: ''
    }
  },
  provide() {
    return {
      clearCurrentRFormTable: () => this.clearCurrentRFormTable()
    }
  },
  inject: {
    handleElement: { default: undefined },
    setSelectedComp: { default: undefined },
    changeElementConfigDisabled: { default: undefined },
    hideElementArea: { default: undefined },
    changeCurrentBlockId: { default: undefined },
    clearCurrentSelectedRForm: { default: undefined },
    changeRFormTableHeaderLabel: { default: undefined },
    changeToggleDisabled: { default: undefined },
    getFormId: { default: undefined },
    changeBlockLoading: { default: undefined }
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        disabled: false
      }
    },
    showForm() {
      return this.associationForm?.itemKey && this.associationForm?.label
    },
    showLabel() {
      return this.associationForm ? this.associationForm.label : ''
    },
    isBlockForm() {
      return this.formType === 'blockForm'
    },
    showAssociationRadio() {
      return this.activeTab !== '基本信息' && this.params?.mode === '设计'
    },
    showBlockRform() {
      return !this.showAssociationRadio || this.association === 'block'
    },
    showAssociationCtrl() {
      return this.showAssociationRadio && this.association === 'form'
    }
  },
  methods: {
    init(params, index) {
      this.params = params
      if (index === 0) {
        this.handleMainTab(index)
      } else if (index > 0) {
        this.handleOtherTab(index)
      }
    },
    /**
     * 解决顺序问题 如果出现顺序问题 在该方法中加上一个before方法 在需要使用的区块扩展中加上xxxBefore方法进行操作
     */
    wrapEventBefore() {
      // this.params.xxxx = (curdListObj, rowIds, rows, blockRformExtendKey) => {
      //   this.params.blockExtendObjMap[blockRformExtendKey].xxxxBefore(curdListObj, rowIds, rows)
      // }
      this.params.deleteRowEx = (curdListObj, rowIds, rows, blockRformExtendKey) => {
        this.params.blockExtendObjMap[blockRformExtendKey]?.deleteRowExBefore?.(curdListObj, rowIds, rows)
      }
      this.params.rowCellChanged = (scope, colItem, baseListObj, blockRformExtendKey) => {
        this.params.blockExtendObjMap[blockRformExtendKey]?.rowCellChangedBefore?.(scope, colItem, baseListObj)
      }
    },
    /**
     * 行表单下标
     * @param {Object} bk rForm对象
     * @param {Number} index block下标
     */
    rowFormIndex(bk, index) {
      return bk.handlerKey === '行表单' ? index : ''
    },
    /**
     * 用于加载前清空block
     */
    clearBlocks() {
      if (this.$isNotEmpty(this.blocks)) {
        this.blocks = []
      }
    },
    /**
     * 处理 rForm 组件管理配置
     * @param index 下标
     * @param rform rForm对象
     */
    rformSetting(index, rform) {
      this.changeElementConfigDisabled?.(false)
      this.changeCurrentBlockId?.(rform.id)
      this.params.blockObjMapById[rform.id]?.handleRformSetting(rform)
    },
    /**
     * 设置当前区块配置
     * @param rform rForm对象
     * @param isSameConfig 是否配置相同
     */
    setRformConfig(rform, isSameConfig) {
      // 配置相同不执行保存
      if (isSameConfig) {
        return
      }
      this.changeSaveBlockRformConfig(!isSameConfig)
      this.params.blockObjMapById[rform.id].init(this.params, rform)
    },
    changeSaveBlockRformConfig(value) {
      this.params.isSaveBlockRformConfig = value
    },
    showIcon(index) {
      return index === this.currentBlock && (this.getTableData?.()?.length > 1 ? true : index !== 0) && this.params.isDesignMode
    },
    blockMove(evt, orginEvent) {
      return evt.relatedContext.element.handlerKey !== '表单'
    },
    dragStart() {
      this.drag = true
    },
    dragEnd() {
      this.drag = false
    },
    mouseenter(index) {
      let isCform = (index === 0)
      // 多tab时 其他tab都要显示 控制栏
      if (this.getTableIndexData?.()) {
        isCform = false
      }
      // 预览 非设计模式 设计模式主表单第一个cform不显示 表单组件控制栏
      if (this.params.isBlockPreview || !this.params.isDesignMode || isCform || this.drag) return
      this.currentBlock = index
    },
    mouseleave(e) {
      if (!this.params.isDesignMode || this.drag) return
      this.currentBlock = null
    },
    /**
     * 判断是否需要在删除前重置表单组件和组件管理的数据
     * @param rForm rForm对象
     */
    needResetData(rform) {
      if (this.$isNotEmpty(this.params.currentBlockId) && this.params.currentBlockId === rform.id) {
        this.changeElementConfigDisabled?.(true)
        // 清空组件管理配置项
        this.clearRformConfig()
        this.hideElementArea()
        // 清空选中当前操作的是哪个表格 及 清空表头选中
        this.changeRFormTableHeaderLabel()
        this.changeCurrentBlockId?.()
        this.clearCurrentSelectedRForm()
        this.handleElement()
      }
    },
    /**
     * 增加rForm
     * @param rForm rForm对象
    */
    addNewRform(rForm) {
      const compCformModuleId = this.blocks.map(block => block.cformModuleId)
      if (compCformModuleId.includes(rForm.cformModuleId)) {
        const delIndex = compCformModuleId.indexOf(rForm.cformModuleId)
        this.delRform(delIndex)
        const allRforms = document.querySelectorAll('.blockViewBlockRform')
        const eleToArr = Array.from(allRforms)
        for (let i = 0; i < eleToArr.length; i++) {
          if (eleToArr[i].querySelector('.currentSelectedTable')) {
            if (i + 1 === compCformModuleId.indexOf(rForm.cformModuleId)) {
              this.handleElement()
            }
            break
          }
        }
        return
      }
      const newIndex = this.blocks.length
      rForm.handlerKey = rForm.handlerKey || '行表单'
      rForm.targetClassName = rForm.sourceModule
      const cloneRForm = this.$clone(rForm)
      this.blocks.push(cloneRForm)
      this.$nextTick(() => {
        this.params.blockObjMapById[this.blocks[newIndex].id] = this.$refs.blockComponents[newIndex]
        this.$refs.blockComponents[newIndex].init(this.params, this.blocks[newIndex])
      })
    },
    /**
     * 删除rForm
     * @param index 需要删除的blocks下标(第0个是cform 从1开始)
    */
    delRform(index, rform) {
      this.blocks.splice(index, 1)
      // 判断是否需要在删除后重置表单组件和组件管理的数据
      if (this.$isNotEmpty(rform)) {
        this.$nextTick(() => {
          const bizid = rform.cformModuleId ? rform.cformModuleId : rform.bizid
          // if (this.clearRformSelected) {
          // 默认时清除 除非手动设置了不清除选中高亮
          this.setSelectedComp(bizid)
          // }
        })
        setTimeout(() => {
          this.needResetData(rform)
        }, 50)
      }
    },
    /**
     * 更新rForm
     * @param index 需要更新的blocks下标(第0个是cform 从1开始)
     * @param updateParams 更新参数(labels: 需要改变的表头数组, type：移入|移出)
    */
    updateRform(index, updateParams) {
      // 处理Rform数据
      const handleData = (col, attr) => {
        const colAttr = attr ? col[attr] : col
        if (colAttr.isEnabled !== (updateParams.type === '移入' ? '是' : '否')) {
          let isEnabled
          if (updateParams.type === '移入') {
            isEnabled = updateParams.labels.includes(col.label) ? '是' : '否'
          } else {
            isEnabled = updateParams.labels.includes(col.label) ? '否' : '是'
          }
          colAttr.isEnabled = isEnabled
        }
      }
      // 移入移出
      const move = () => {
        this.blocks[index].columns.forEach(col => {
          handleData(col, '')
        })
        this.blocks[index].pageData.columns.forEach(col => {
          handleData(col, 'columnEntity')
        })
      }
      move()
      this.$nextTick(() => {
        const params = {
          isUpdate: true,
          index
        }
        this.params.blockObjMapById[this.blocks[index].id]?.init(this.params, this.blocks[index], params)
      })
    },
    updateBeforeSave(isDraft, updateAfter) {
      var comps = this.$refs.blockComponents
      for (let i = 0; i < comps?.length; i++) {
        if (comps[i].updateBeforeSave) {
          comps[i].updateBeforeSave(isDraft, updateAfter)
        }
      }
    },
    showError(result) {
      var errorReturn
      var comps = this.$refs.blockComponents
      for (let i = 0; i < comps?.length; i++) {
        if (comps[i].showError) {
          var retTemp = comps[i].showError(result)
          if (retTemp !== undefined && errorReturn === undefined) {
            errorReturn = retTemp
          }
        }
      }
      return errorReturn
    },
    getCformBlock() {
      var comps = this.$refs.blockComponents
      for (let i = 0; i < comps?.length; i++) {
        if (comps[i].isCformBlock) {
          return comps[i]
        }
      }
    },
    clearCurrentRFormTable() {
      for (let i = 0; i < this.$refs.blockComponents.length; i++) {
        if (i !== 0) {
          const comp = this.$refs.blockComponents[i]
          this.$isNotEmpty(comp.$refs.curdList) && comp.$refs.curdList.clearCurrentRFormTable()
        }
      }
    },
    /**
     * 清空组件管理配置项
     */
    clearRformConfig() {
      for (let i = 0; i < this.$refs.blockComponents.length; i++) {
        const comp = this.$refs.blockComponents[i]
        if (comp?.block?.handlerKey === '行表单') {
          comp.params.clearCurrentRformBlock?.()
        }
      }
    },
    handleMainTab(index) {
      // this.params = params
      this.params.changeSaveBlockRformConfig = (value) => this.changeSaveBlockRformConfig(value)
      if (this.params.isNew) {
        this.blocks = [
          {
            id: new Date().getTime(),
            handlerKey: '表单',
            isCform: true
          }
        ]
      } else {
        var container = this.params.blockView.containers[index]
        this.blocks = container.blocks
      }

      this.$nextTick(() => {
        this.wrapEventBefore()
        for (let i = 0; i < this.$refs.blockComponents.length; i++) {
          var comp = this.$refs.blockComponents[i]
          this.params.blockObjMapById[this.blocks[i].id] = comp
          comp.init(this.params, this.blocks[i])
        }
      })
    },
    handleOtherTab(index) {
      // this.params = params
      // if (!index) return // 第一个表单不处理
      const tabParams = this.params.isNew ? this.params.newBlockView?.[index] : this.params.blockView.containers?.[index]
      if (!tabParams) return
      let formData = {}
      const blocks = tabParams?.blocks
      if (this.$isEmpty(blocks)) return
      blocks?.forEach(block => {
        if (blocks.length === 1) {
          if (block.dataType === '关联表单') {
            // 关联表单
            formData = block.data
          }
        } else {
          // console.log('关联区块', block)
        }
      })
      if (!formData) return
      formData.main = formData.main ? formData.main : formData.meta
      this.isFreeForm = formData.main?.isFreedomForm
      this.currentTabParams = formData
      formData.itemKey = formData.main?.id
      if (!formData.itemKey) {
        // 如果itemKey是空的 则证明没绑定表单
        return
      }
      this.assoForm = formData.main?.name
      if (formData.hasOwnProperty('containers')) {
        this.formType = 'blockForm'
      } else {
        this.formType = 'formCanvas'
      }

      this.$nextTick(() => {
        // if (this.isFreeForm || this.isRegularForm) {
        //   this.$refs[this.formType]?.$refs?.formEditDetail?.changeData('showButtons', false) // 不显示底部按钮
        // }
        this.$refs[this.formType].mode = this.mode
        this.$refs[this.formType].meta = formData
        this.$refs[this.formType].title = formData?.main ? formData?.main?.name : formData?.name
        if (formData.instance) {
          this.$refs[this.formType].headerPaddingTop = formData.instance.headerPaddingTop
          this.$refs[this.formType].headerHeight = formData.instance.headerHeight
        } else {
          formData.instance = formData.version // 添加version数据
          this.$refs[this.formType].headerPaddingTop = formData.version?.headerPaddingTop
          this.$refs[this.formType].headerHeight = formData.version?.headerHeight
        }
        this.$refs[this.formType].cformSettings = formData.cformSettings
        this.$refs[this.formType].dataVo = formData
        if (this.isBlockForm) {
          const blockParams = {
            isEdit: this.isEdit,
            mode: this.mode,
            meta: formData?.containers?.[0]?.blocks?.[0]?.data,
            isPreviewDetail: false,
            isDesignMode: this.mode === '设计',
            isBlockPreview: false
          }
          if (formData) {
            blockParams.blockView = this.$clone(formData)
          }
          this.$refs[this.formType].init(undefined, blockParams)
        } else {
          // 之前表单类型可能用到的扩展
          // this.$refs[this.formType].setExtDataAssembly(formData?.formType)
          // 规则和自由表单 设计时 展示的是制单
          // const initFormExData = {
          //   showEditButtons: false // 不显示下方操作按钮
          // }
          // this.$refs[this.formType].initByDataVo(formData, this.mode, undefined, false, undefined, initFormExData)
          this.$refs[this.formType].initMeta(this.mode, formData, false)
        }
      })
    },
    jumpForm() {
      // 跳转前初始化数据重置
      this.initResetData?.()
      // 跳转时隐藏tab
      this.changeBlockLoading?.(true)
      const exData = {}
      exData.needToReloadFreeJson = true
      this.getFormId?.(this.associationForm.itemKey, this.isFreeForm)
    },
    handleRadioChange(type) {
      if (type === 'block') {
        this.association = 'form'
        this.$message.error('暂不支持关联区块')
        return
      }
      this.changeToggleDisabled?.(false, this.getTableIndexData?.())
      this.$emit('handleRadioChange', type)
    },
    isAuditCanEdit(itemLabel) {
      return false
    },
    // 选中关联表单
    checkedAssociationForm() {
      const otherTabItemKey = []
      Object.values(this.getAssociationForm?.())?.forEach(item => {
        item.itemKey && otherTabItemKey.push(item.itemKey)
      })
      const filterCurrTabItemKey = otherTabItemKey?.filter(itemKey => itemKey !== this.associationForm.itemKey)
      const checkNodeId = this.getNodeIdFromItemKey(this.associationForm.itemKey)
      if (checkNodeId) {
        this.$refs.associationCformDlg?.changeData('checkedNodes', [checkNodeId])
      }

      if (this.associationForm.itemKey) {
        setTimeout(() => {
          this.$refs.associationCformDlg?.$refs?.supTree?.selectAndCheckNode(this.associationForm.itemKey, 'itemKey')
        })
      }
      // 需要过滤主表 (有可能还要过滤其他tab已关联的表单)
      const filterNodesArr = this.firstTabAssociationForm ? [this.firstTabAssociationForm, ...filterCurrTabItemKey] : [...filterCurrTabItemKey]
      this.filterNodes(filterNodesArr)
    },
    changeDlgShow(show, data = {}) {
      const { status, checkedNodes } = data
      if (show) {
        // 获取弹窗的ztree实例 如果是空的证明是第一次打开
        if (!this.$refs.associationCformDlg.ztreeObj) {
          // 打开之前初始化一些参数
          this.settingInitParams()
        } else {
          this.checkedAssociationForm()
        }
      } else {
        // const { status, checkedNodes } = data
        // 关闭弹窗
        if (status === 'submit') {
          if (checkedNodes?.[0]?.actualKey) {
            this.$message.error('暂不支持关联区块表单')
            return
          }
          // 确定
          // 当选中的关联表单和当前渲染的表单itemKey不一致的时候才重新渲染
          const reRender = checkedNodes.length && checkedNodes[0].itemKey !== this.associationForm?.itemKey
          if (reRender) {
            this.setAssociationForm?.({
              itemKey: checkedNodes[0].actualKey ? checkedNodes[0].actualKey : checkedNodes[0].itemKey,
              label: checkedNodes[0].label,
              tabName: this.activeTab
            })
            // 请求接口渲染关联表单
            const id = checkedNodes[0].actualKey ? checkedNodes[0].actualKey : checkedNodes[0].itemKey
            const isBlock = this.$isNotEmpty(checkedNodes[0].actualKey)
            this.renderAssocicationCform(id, isBlock)
          }
        } else if (status === 'cancel') {
          // 取消
          this.filterNodes([])
        }
      }
      this.showAssociationCformDlg = show
      // if (!show && status === 'cancel') {
      //   this.filterNodes([])
      // }
    },
    /**
     * 渲染关联表单
     * @param {string} id 请求的表单id
     * @param {boolean} isBlock 是否是区块表单
     */
    renderAssocicationCform(id, isBlock) {
      let apiKey = ''
      const params = { ids: id }
      if (isBlock) {
        // 渲染区块表单
        apiKey = 'selectBlockMeta'
        params
      } else {
        // 渲染非区块表单
        apiKey = 'versionActionCformMetaEntity'
        params.actionKey = 'QUERY'
      }
      this.$callApiParams(
        apiKey,
        params,
        result => {
          const index = this.getTableIndexData?.()
          // 要把数据填充到blockView的containers中的对应下标中
          if (index) {
            const data = {
              name: result.data?.main?.name,
              // itemKey: result.data?.main?.id,
              itemKey: this.associationForm?.itemKey,
              type: 'form',
              ...result.data
            }
            if (this.params.isNew) {
              if (this.params.newBlockView === undefined) {
                this.params.newBlockView = {
                  [index]: {
                    blocks: [{
                      data,
                      dataType: '关联表单'
                    }]
                  }
                }
              } else {
                Object.assign(this.params.newBlockView, {
                  [index]: {
                    blocks: [{
                      data,
                      dataType: '关联表单'
                    }]
                  }
                })
              }
            } else {
              this.params.blockView.containers[index] = {
                blocks: [{
                  data,
                  dataType: '关联表单'
                }]
              }
            }
            this.init(this.params, index)
          } else {
            this.$message.error('渲染表单有误')
          }
          return true
        },
        error => {
          console.log(error)
        })
    },
    settingInitParams() {
      // 配置是否树节点任意单选（选择父节点时不联动子节点）
      if (this.$refs.associationCformDlg?.treeAnySingeChoice) {
        this.$refs.associationCformDlg?.changeData(['setting', 'check', 'chkboxType'], { 'Y': '', 'N': '' })
      }
      this.checkedAssociationForm()
    },
    setBlocks(type = 'equal', data) {
      const typeObj = {
        push: (data) => {
          this.blocks?.push(data)
        },
        slice: (index) => {
          this.blocks?.slice(index)
        },
        equal: (data) => {
          this.blocks = data
        }
      }
      typeObj[type](data)
    }
  }
}
</script>
<style scoped lang="scss">
  @mixin flex {
    display: flex;
    flex-direction: column;
  }
  .mt-10 {
    margin-top: 10px;
  }
  .blockTab {
    @include flex;
    height: 100%;
    .associationForm {
      @include flex;
      flex: 1;
      height: 100%;
      overflow: hidden;
      .choiceForm {
        // height: 75px;
        // display: flex;
        // flex-wrap: wrap;
        .tip {
          // width: 100%;
          // display: inline-block;
          margin-left: 10px;
          text-decoration: none;
        }
      }
      .form {
        flex: 1;
        border: 1px solid #1877e1;
        overflow: auto;
      }
    }
  }
  .blockViewBlock {
    height: 100%; display: flex; flex-direction: column;
    .blockViewBlockReal {
      position: relative;
      border: 1px solid transparent;
      &.settingBorder {
        border: 1px solid #6eb7fb;
      }
      .settingIcon {
        position: absolute;
        top: 0;
        right: 0;
      }
      .dragIcon {
        .el-button {
          cursor: move;
        }
      }
    }
  }
</style>
