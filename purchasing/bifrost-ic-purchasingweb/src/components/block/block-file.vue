<!--
 * @Description: 附件行表单
 * @version:
 * @Author: zhangshaowu <EMAIL>
 * @Date: 2024-02-18 15:58:22
 * @LastEditors: zhangshaowu <EMAIL>
 * @LastEditTime: 2024-03-21 14:16:08
-->
<template>
  <div :class="`blockViewBlock blockViewBlockFile ${blockViewBlockErrorClass}`">
    <span class="blockViewBlockErrorSpan">{{ blockError }}</span>
    <base-attachment ref="baseAttachment" v-show="isApply"/>
    <attach-audit-extend ref="attachAudiExtend" v-show="!isApply"/>
  </div>
</template>
<script>
import BaseAttachment from '../bizz/file/attachment'

export default {
  name: 'block-附件',
  components: { BaseAttachment },
  data() {
    return {
      blockError: '',
      block: {},
      isApply: false,
      params: {}
    }
  },
  computed: {
    blockViewBlockErrorClass() {
      if (this.$isNotEmpty(this.blockError)) {
        return 'blockViewBlockError'
      }
      return ''
    },
    showBaseAttachment() {
      return this.isApply || this.params.isInBlockTab
    }
  },
  methods: {
    init(params, block) {
      this.params = params
      if (params.mode === '制单' || params.mode === '设计') {
        this.isApply = true
      }
      this.block = block
      if (!params.isDesignMode) {
        params.blockObjMap[`附件-${params.dataVo.formType}`] = this
      }
      const exParams = {
        mode: params.mode,
        isRformBlock: params.isRformBlock,
        addAttaTemData: !!params.addAttaTemData, // 判断是否添加临时数据
        blockformName: '附件信息', // 区块组件名
        initRefDataVoFromId: params.initRefDataVoFromId
      }
      if (this.showBaseAttachment) {
        this.$refs.baseAttachment.initCommonBiz(params.isEdit, params.dataVo, exParams)
      } else {
        this.$refs.attachAudiExtend.initCommonBiz(params.dataVo, exParams)
      }
    },
    updateBeforeSave() {
      this.$fillAtt(this, this.params.dataVo)
    }
  }
}
</script>
<style scoped lang="scss">
</style>
