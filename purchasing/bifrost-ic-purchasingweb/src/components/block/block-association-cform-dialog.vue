<template>
  <el-dialog
    title="选择关联表单"
    :visible.sync="show"
    width="30%"
    append-to-body
    :close-on-click-modal="false"
    :before-close="handleClose">
    <div style="height: 500px">
      <sup-tree
        ref='supTree'
        :isPopover="false"
        :is-get-child="false"
        :setting="setting"
        :nodes="nodes"
        :checked-values="checkedNodes"
        @onCreated="onCreated"
        />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose('cancel')">取 消</el-button>
      <el-button type="primary" @click="handleClose('submit')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { mixins } from '@/mixin'
export default {
  name: 'block-association-cform-dialog',
  mixins: [mixins],
  props: {
    show: { type: Boolean, default: false },
    nodes: { type: Array, default: () => [] }
  },
  data() {
    return {
      ztreeObj: null,
      multiple: false, // 是否可以多选
      treeAnySingeChoice: false, // 是否树节点任意单选（选择父节点时不联动子节点）
      checkedNodes: [],
      isShowError: false,
      setting: {
        check: {
          enable: true,
          chkboxType: { 'Y': '', 'N': 's' }
        },
        callback: {
          beforeCheck: this.beforeCheck,
          beforeClick: this.beforeClick,
          beforeDblClick: this.beforeDblClick,
          onCheck: this.onCheck,
          onClick: this.onClick,
          onDblClick: this.onDblClick
        }
      }
    }
  },
  mounted() {},
  computed: {},
  methods: {
    onCreated(ztreeObj) {
      this.ztreeObj = ztreeObj
    },
    beforeClick(treeId, treeNode) {
      this.isShowError = false
      return this.commonLogic(treeNode)
    },
    onClick(event, treeId, treeNode) {
      console.log(treeNode)
      const clickFunc = () => {
        this.ztreeObj.checkNode(treeNode, true, true, true)
      }
      this.commonLogic(treeNode, false, clickFunc)
    },
    beforeCheck(treeId, treeNode) {
      this.isShowError = false
      if (this.$isNotEmpty(treeNode.dbClick)) { // 表明是双击选择
        treeNode.dbClick = null
        if (!treeNode?.isLeaf || treeNode?.isParent) {
          if (!this.treeAnySingeChoice) {
            return false
          }
        }
        return false
      }

      if (this.commonLogic(treeNode) === false) {
        return this.commonLogic(treeNode)
      }

      if (!this.multiple || this.treeAnySingeChoice) {
        // 单选时，先取消之前勾选的节点
        this.ztreeObj.checkAllNodes(false)
      }
      return true
    },
    onCheck(event, treeId, treeNode) {
      this.checkedNodes = this.ztreeObj.getCheckedNodes(true)
      this.ztreeObj.selectNode(treeNode)
    },
    beforeDblClick(treeId, treeNode) {
      return !this.isShowError
    },
    onDblClick(event, treeId, treeNode) {
      if (treeNode) {
        treeNode.dbClick = true
        this.ztreeObj.checkNode(treeNode, null, true, true)
        this.handleClose('submit')
      }
    },
    /**
     * 通用逻辑
     * @param {Object} treeNode 树节点
     * @param {Boolean} needReturn 是否需要return
     * @param {Function} callback 回调方法
     */
    commonLogic(treeNode, needReturn = true, callback) {
      if (!this.multiple && treeNode?.isParent && !this.treeAnySingeChoice) {
        this.$message.error('只能选择最末级选项')
        this.isShowError = true
        if (needReturn) {
          return false
        }
      } else {
        callback?.()
      }
      return true
    },
    handleClose(status) {
      if (this.isShowError) return
      this.$refs.supTree?.refresh()
      const data = {
        status,
        checkedNodes: this.checkedNodes
      }
      this.$emit('changeDlgShow', false, data)
    }
  }
}
</script>
<style scoped lang="scss">
</style>
