<template>
  <div :class="blockViewBlockClass">
    <component ref="blockRformExtend" :is="blockRformExtendName" />
    <span class="blockViewBlockErrorSpan">{{blockError}}</span>
    <b-curd ref="curdList" :isVxe="false"/>
  </div>
</template>

<script>
import $ from 'jquery'
import omit from 'lodash/omit'
import BCurd from '../page/base-curd'
export default {
  name: 'block-行表单',
  components: { BCurd },
  provide() {
    return {
      blockConfig: () => this.block.config
    }
  },
  inject: {
    hideElementArea: { default: undefined },
    rFormSetElementList: { default: undefined },
    setEditingColItem: { default: undefined },
    rFormTableHeaderLabel: { default: undefined },
    clearCurrentRFormTable: { default: undefined },
    handleElement: { default: undefined },
    handleCurrentRformBlock: { default: undefined },
    changeCurrentBlockId: { default: undefined },
    blockParentCheckDataFun: { default: undefined }
  },
  data() {
    return {
      params: undefined,
      blockError: '',
      blockRformExtendKey: '',
      block: {},
      blockCanEmpty: '未设置',
      isBlockViewBlockHideAllButtons: false,
      isMergeSummaries: false
    }
  },
  created() {
    window.$event.$on('blockFormLoaded', this.blockFormLoaded)
  },
  beforeDestroy() {
    window.$event.$off('blockFormLoaded', this.blockFormLoaded)
  },
  computed: {
    blockViewBlockErrorClass() {
      if (this.$isNotEmpty(this.blockError)) {
        return 'blockViewBlockError'
      }
      return ''
    },
    blockViewBlockHideAllButtonsClass() {
      if (this.isBlockViewBlockHideAllButtons) {
        return 'blockViewBlockHideAllButtons'
      }
      return ''
    },
    blockRformExtendName() {
      if (this.$isNotEmpty(this.blockRformExtendKey)) {
        return `blockRformExtend-${this.blockRformExtendKey}`
      }
      return ''
    },
    blockViewBlockClass() {
      return `blockViewBlock blockViewBlockRform ${this.blockViewBlockErrorClass} ${this.blockViewBlockHideAllButtonsClass}`
    }
  },
  methods: {
    blockFormLoaded() {
      this.$refs.curdList?.reInitLayout?.()
    },
    init(params, block, updateParams) {
      // updateParams { isUpdate: 是否是更新操作, index: 当前需要更新的rform下标 }
      this.params = params
      this.block = block

      if (this.$isNotEmpty(this.params.currentBlockId)) {
        this.$refs.curdList.$refs.baseList?.setCurrentRFormTable?.()
      }

      var syncCellDisabled = () => {
        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.syncCellDisabled) {
          this.$nextTick(() => { this.$refs.blockRformExtend.syncCellDisabled(this, params) })
        }
      }

      var reloadTableCallback = (result, table, baseListObj) => {
        syncCellDisabled()

        if (params.reloadTableCallback) {
          params.reloadTableCallback(result, table, baseListObj)
          // 区块加载表格后改变分页参数
          this.$refs.curdList?.blockPagination?.(block?.config)
        }
      }

      const tableTempData = []
      const reloadTableCallbackBeforeFillTable = (result, table, baseListObj) => {
        const upperLimit = block?.config?.upperLimit
        const defaultLine = block?.config?.defaultLine
        const rows = result.data.rows
        // 上限数限制
        // 如果上限数大于0 且改区块数据大于上限数 则从后往前删除超出行
        if (upperLimit > 0 && rows?.length > upperLimit) {
          for (let i = upperLimit; i <= rows?.length; i++) {
            rows.pop()
          }
        }
        // 添加默认行
        if (defaultLine >= 0) {
          // 处理填充数据
          const cloneRow = {}
          const headers = result.data.columns
          for (let i = 0; i < headers.length; i++) {
            const hd = headers[i]
            if (this.$isNotEmpty(hd.columnEntity)) {
              if (hd.columnEntity.colType === '多选') {
                const value = hd.columnEntity.defaultValue.replace(/，/g, ',')
                hd.columnEntity.defaultValue = value.split(',')
              }
              cloneRow[hd.prop] = hd.columnEntity.defaultValue
            } else {
              cloneRow[hd.prop] = ''
            }
          }
          // 清空表格
          this.$refs.curdList?.clearTableData?.()
          if (defaultLine > 0 && rows?.length < defaultLine) {
            const rowCount = (defaultLine - rows?.length)
            // 如果 默认行 - 表格中的行数 > 0 则添加对应数量的默认行
            if (rowCount > 0) {
              for (let i = 0; i < rowCount; i++) {
                tableTempData.push({
                  id: 'newRow' + new Date().getTime() + i,
                  ...cloneRow
                })
              }
            }
          }
        }
      }

      var addRowEx = (newRow, curdListObj) => {
        params.addRowEx(newRow, curdListObj)
        reloadTableCallback(undefined, curdListObj.getTable(), curdListObj)
      }

      var rowCopyEX = (row, baseListObj) => {
        reloadTableCallback(undefined, baseListObj.getTable(), baseListObj)
      }

      // 设置表单单元格数值，这方法在设置值之后，会触发单元格值变化事件
      params.setRowValue = (row, prop, value) => {
        this.$set(row, prop, value)
        this.$refs.curdList.rowCellChangedFireManually(row, prop)
      }

      // 运行时动态设置行表单按钮可见性
      this.params.resetBlockButtonVisible = (hiddenButtons) => {
        this.$refs.curdList.reRenderBtns(hiddenButtons)
      }

      // 清空行表单区块的行数据
      params.clearRow = (blockRformExtendKey) => {
        params.blockObjMap[blockRformExtendKey]?.$refs?.curdList?.clearRow()
      }

      const callbackHeaderClick = (data, e) => {
        this.clearCurrentRFormTable()
        let clickElement = e.target
        while (clickElement !== null) {
          if (clickElement.tagName === 'TH') {
            if (clickElement.className.indexOf('el-table-column--selection') === -1) {
              // 设置区块配置
              this.$refs.curdList.$refs.baseList?.setCurrentRFormTable?.()
              // 设置当前选中blockid
              this.changeCurrentBlockId?.(this.block.id)
              // 点击表头 设置所有要素和已选要素
              setElement(data)
              // 设置要素
              this.setEditingColItem(data.label)
              this.hideElementArea()
              break
            }
          }
          clickElement = clickElement.parentNode
        }
      }

      const setElement = (data) => {
        const notEnabled = [] // 区块左侧所有要素
        const enabled = [] // 区块右侧已选要素
        const propMap = {} // 已选要素的prop: label映射
        this.block.columns.forEach(col => {
          if (col.isEnabled === '是') {
            enabled.push(col)
            if (col.isEnabled === '是') {
              propMap[col.prop] = col.label
            }
          }
          if (col.isEnabled === '否') {
            notEnabled.push(col)
          }
        })

        // 过滤出指定属性的数据
        const filteredData = this.block?.pageData?.columns.filter(item => enabled.map(col => col.prop).includes(item.prop))

        let level = 1
        const filterParentName = []
        // 递归处理 过滤后的数据
        function recursion(item, currProp, lv = 1) {
          if (currProp === item.prop || filterParentName.includes(item.prop)) {
            if (item.children.length > 0) {
              lv++
              if (lv > level) {
                level = lv
              }
              for (const child of item.children) {
                if (!filterParentName.includes(child.prop)) {
                  filterParentName.push(child.prop)
                }
                recursion(child, currProp, lv)
              }
            }
          }
          // 有子项
          if (item.children.length > 0) {
            for (const child of item.children) {
              recursion(child, currProp, lv)
            }
          }
        }

        for (const fdata of filteredData) {
          recursion(fdata, data?.property)
        }

        const exData = {
          filterParentName,
          level
        }
        this.rFormSetElementList(notEnabled, enabled)
        this.blockParentCheckDataFun(exData)
      }

      const callbackHeaderCellClassName = ({ row, column, rowIndex, columnIndex }) => {
        if (column.type !== 'selection' && column.label === this.rFormTableHeaderLabel()) {
          return 'rFromHeaderBorder'
        }
        return ''
      }

      const getSummaries = (param, $table) => {
        const moneyToNumber = (money) => parseFloat(this.$unFormatMoney(money))
        const ratioToNumber = (ratio) => parseFloat(ratio.slice(0, -1))
        const typeWhiteMap = { '整数': true, '小数': true, '金额': true, '百分比': true } // 区块的合计行目前参与计算的要素类型
        const moneyTypeMap = {}
        const ratioTypeMap = {}
        // 找出参与计算的列的prop
        const colType = this.block?.pageData?.columns?.filter(col => typeWhiteMap[col.colType])
        const colProp = colType?.map(col => {
          if (col.colType === '金额') {
            moneyTypeMap[col.columnEntity?.prop] = col?.columnEntity?.prop
          } else if (col.colType === '百分比') {
            ratioTypeMap[col.columnEntity?.prop] = col?.columnEntity?.prop
          }
          return col?.columnEntity?.prop
        })

        const columns = param?.columns
        const data = param?.data
        const sums = []
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计'
            return
          }
          const property = colProp.indexOf(column.property) > -1 ? column.property : undefined
          const values = data.map(item => {
            if (moneyTypeMap[property]) return moneyToNumber(item[property])
            if (ratioTypeMap[property]) return ratioToNumber(item[property])
            return Number(item[property])
          })
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              let value = 0
              if (moneyTypeMap[property]) {
                value = moneyToNumber(curr)
              } else {
                value = Number(curr)
              }
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            let result = sums[index].toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            if (ratioTypeMap[column.property]) {
              result += '%'
            }
            sums[index] = result
          } else {
            sums[index] = ''
          }
        })
        this.mergeSummaries()
        return sums
      }
      let checkButton = false
      let fold = Boolean
      if (params.mode === '审核') { // 审核状态下的行表单编辑控制
        fold = true
        block.pageData.columns.map((item, index) => {
          if (item.editable) {
            checkButton = true
            return
          }
        })
      } else {
        checkButton = false
        fold = typeof block?.fold === 'boolean' ? block?.fold : true
      }
      const exParams = {
        rformParams: this.params,
        block: this.block,
        isRformBlock: this.params.isRformBlock, // 传递给base-page的initParams 用于base-page判断是否是区块
        supportRowUpAndDown: false,
        isBlockViewEdit: params.isEdit,
        checkButton: checkButton, // 审核状态下的按钮设置
        blockExtendObjMap: this.params.blockExtendObjMap,
        targetClassName: this.block.targetClassName,
        isAuditMode: params.isAuditMode,
        tableTempData,
        isSaveBlockRformConfig: this.params.isSaveBlockRformConfig || false,
        changeSaveBlockRformConfig: (value) => this.params.changeSaveBlockRformConfig(value),
        addRowEx: addRowEx,
        rowCopyEX: rowCopyEX,
        reloadTableCallback: reloadTableCallback,
        reloadTableCallbackBeforeFillTable,
        jumpToSaveFormData: params.jumpToSaveFormData, // 传递给base-list的initParams
        customSelectOptionsSupplier: (scope, colItem) => {
          return params.customSelectOptionsSupplier(scope, colItem)
        },
        rowCellChanged: (scope, colItem, baseListObj) => {
          params.rowCellChanged(scope, colItem, baseListObj)
          syncCellDisabled()
        },
        deleteRowBefore: (curdListObj, rowIds, deleteData) => {
          return params.deleteRowBefore(curdListObj, rowIds, deleteData)
        },
        deleteRowEx: (curdListObj, rowIds, deleteData) => {
          reloadTableCallback(undefined, undefined, curdListObj)
          params.deleteRowEx(curdListObj, rowIds, deleteData)
        },
        getFormValue(label, isHideItem) {
          return params.getFormValue(label, isHideItem)
        }
      }
      if (params.dataVo && this.$isNotEmpty(params.dataVo.extData)) {
        const extData = params.dataVo.extData || {}
        const remindInfoAll = extData.blockRemindInfo || {}
        exParams.remindInfo = remindInfoAll[this.block.id] || {}
      }

      if (params.isDesignMode) {
        exParams.callbackHeaderClick = callbackHeaderClick
        exParams.callbackHeaderCellClassName = callbackHeaderCellClassName
      }
      if (this.$isEmpty(block.config)) {
        let required
        if (this.$isNotEmpty(block?.canEmpty)) {
          required = block?.canEmpty === '否'
        } else {
          required = true
        }
        block.config = {
          mode: this.params.mode, // 当前表单模式 用于判断分页切换
          name: block?.name, // 组件名称
          cformModuleName: block?.cformModuleName,
          upperLimit: block?.upperLimit || 0, // 上限数
          upperLimitLine: block?.upperLimitLine || 0, // 上限显示行数
          defaultLine: block?.defaultLine || 0, // 默认行
          hiddenButton: block?.hiddenButton || '', // 隐藏按钮
          required, // 必填
          hasSummaries: block?.hasSummaries || false, // 合计行
          indexColumn: block?.indexColumn || false, // 序号列
          fold: fold, // 默认是展开
          paging: block?.paging || false // 默认是不启用分页
        }
      }
      exParams.blockformName = this.$isNotEmpty(block.config?.name) ? block.config?.name : block?.name
      // 隐藏按钮
      params.setBlockHiddenButtons(block.targetClassName, block.config?.hiddenButton)
      // 必填
      this.setBlockCanEmpty(!block?.config?.required)
      // 合计行
      exParams.showSummary = block.config.hasSummaries
      this.$call(this, 'baseList', 'changeShowSummary', block?.config?.hasSummaries)
      if (block?.config?.hasSummaries) {
        exParams.getSummaries = getSummaries
      }
      // 序号列
      exParams.orderNumber = { label: '序号', isShow: block?.config?.indexColumn }
      // 展开
      const isFold = this.params.isPreviewDetail || block?.config?.fold
      this.$refs.curdList?.$refs?.baseList?.$refs?.basePage?.changeIsFold?.(isFold)
      exParams.headerLevel = params.headerLevel

      this.recordData(params, block)
      this.wrapAddRowEx(params)
      this.wrapDeleteRowBefore(params)
      this.wrapDeleteRowEx(params)
      this.wrapRowCellChanged(params)
      this.wrapFormCellChanged(params)
      this.wrapCallbackAfterFormLoaded(params)
      this.wrapCallbackBeforeFormLoaded(params)
      this.wrapReloadTableCallback(params)
      this.wrapCustomSelectOptionsSupplier(params)

      this.$transferCheckboxData(this.block.pageData, false)
      this.$nextTick(() => {
        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.handleInitParams) {
          this.$refs.blockRformExtend.handleInitParams(exParams, params)
          const rowCellClickBefore = exParams.rowCellClickBefore

          exParams.rowCellClickBefore = (scope, colItem, refParams) => {
            if (rowCellClickBefore) {
              rowCellClickBefore(scope, colItem, refParams)
            }
            refParams.isRefNoCode = params.isRefNoCode
          }
        }
        const ADD = '新增'
        const DEL = '删除'
        const addBtnText = exParams.btTexts?.[ADD] ? exParams.btTexts?.[ADD] : ADD
        const delBtnText = exParams.btTexts?.[DEL] ? exParams.btTexts?.[DEL] : DEL
        // 如果初始化后 隐藏了新增和删除按钮 不展示勾选列
        if (block?.config?.hiddenButton?.includes(addBtnText) && block?.config?.hiddenButton?.includes(delBtnText)) {
          exParams.noSelection = true
        }
        // 初始化时传递隐藏按钮的设置，一般是其他区块设置了当前区块按钮的隐藏
        exParams.hiddenButtons =
          params.getBlockHiddenButtons(block.targetClassName)

        // 重写handleAfterBlockRef,handleAfterBlockRef方法，在handleInitParams之后，handleInitParams可能会有当前函数
        // 重写主要处理的是行多选逻辑
        const handleAfterBlockRef = exParams.handleAfterBlockRef
        const afterBlockRef = (refData = {}, row, selectedData, columnsData) => {
          // 如果区块中设置了 splitRow=true 则需要在多选的时候拆分行
          if (selectedData.list?.length > 1 && this.$refs.blockRformExtend.splitRow) {
            const curdList = this.$refs.curdList
            // 删除id
            curdList.deleteTableDataByField('id', row.id)
            // 处理数据生成新行
            this.composeNewRows(selectedData, row)
          }
          handleAfterBlockRef?.(refData, row, selectedData, columnsData)
        }
        exParams.handleAfterBlockRef = afterBlockRef
        // const handlebeforeFilldataBlockRef = exParams.handlebeforeFilldataBlockRef
        // const beforeFilldataBlockRef = (selectedData = {}, row, setBtnUnLoad, refData) => {
        //   const rows = this.block.pageData?.rows || []
        //   const labelOriginMap = {}
        //   rows.forEach(item => {
        //     labelOriginMap[item[refData.labelOrigin + 'Id']] = true
        //   })
        //   let existIndex = ''
        //   const hasExist = selectedData.list?.some((item, index) => {
        //     if (labelOriginMap[item.id]) {
        //       existIndex = index
        //     }
        //     return labelOriginMap[item.id]
        //   })
        //   if (hasExist) {
        //     setBtnUnLoad()
        //     this.$message({
        //       message: `已选择的第${existIndex + 1}条数据已存在`,
        //       type: 'warning'
        //     })
        //     throw new Error(`已选择的第${existIndex + 1}条数据已存在`)
        //   }
        //   handlebeforeFilldataBlockRef?.(selectedData, row, setBtnUnLoad, refData)
        // }
        // exParams.handlebeforeFilldataBlockRef = beforeFilldataBlockRef

        this.$refs.curdList.initEditRowForm(
          block.pageData, params.isEdit, undefined, exParams)

        const isBlockHideAllButtons = true
        this.isBlockViewBlockHideAllButtons = isBlockHideAllButtons
        this.$refs.curdList.setButtonNormalNoPaddingTop(isBlockHideAllButtons)
      })

      // 如果是修改rform 需要重新设置rform要素列表(所有要素 已选要素)
      if (this.$isNotEmpty(updateParams) && updateParams.isUpdate) {
        const notEnabled = this.block.columns.filter(r => r.isEnabled === '否')
        const enabled = this.block.columns.filter(r => r.isEnabled === '是')
        this.rFormSetElementList(notEnabled, enabled)
        if (this.$isEmpty(enabled)) {
          setTimeout(() => {
            // 设置空表头的表格 暂无数据 的宽度撑满容器
            const hasSelectedTable = document.querySelector('.currentSelectedTable')
            if (hasSelectedTable) {
              const emptyBlock = hasSelectedTable.querySelector('.el-table__empty-block')
              emptyBlock.style.width = '100%'
            }
          }, 0)
        }
      }
    },
    // 合并合计行的列
    mergeSummaries() {
      // 第一次展示合计行进行 进行合并 后续不用合并
      if (!this.isMergeSummaries) {
        this.$nextTick(() => {
          const id = `block${this.params.mode}${this.block.id}`
          const tds = document.querySelectorAll(`#${id} .el-table__footer-wrapper tr>td`)
          if (tds) {
            const tdsArr = Array.from(tds)
            let index = 0
            for (let i = 0; i < tdsArr.length; i++) {
              const td = tdsArr[i]
              if (i && td.children?.[0]?.innerHTML) {
                // 取得需要合并的列数
                index = i
                this.isMergeSummaries = true
                break
              }
            }
            // 遍历合并的列数
            for (let i = 0; i < index; i++) {
              // 除第一列外 其他列隐藏 第一列合并相应列数
              if (i) {
                tds[i].style.display = 'none'
              } else {
                tds[i].colSpan = index
                tds[i].style.textAlign = 'center'
              }
            }
          }
        })
      }
    },
    // 生成新的行
    composeNewRows(selectedData, row) {
      const curdList = this.$refs.curdList
      // 过滤掉id
      const filteredObject = omit(row, 'id')

      const result = [] // 处理完的行数据
      const tempObj = {} // 用于存储需要处理的键值对
      for (const [key, value] of Object.entries(filteredObject)) {
        let handleValue = ''
        if (typeof value === 'string' && value.includes(',')) {
          handleValue = value.split(',')
          tempObj[key] = handleValue
          delete filteredObject[key]
        }
      }
      for (let i = 0; i < selectedData?.list?.length; i++) {
        for (let j = 0; j < Object.keys(tempObj).length; j++) {
          const key = Object.keys(tempObj)[j]
          filteredObject[key] = tempObj[key][i]
        }
        result.push(this.$clone(filteredObject))
      }

      for (let i = 0; i < result.length; i++) {
        const handleData = result[i]
        handleData.id = 'newRow' + new Date().getTime() + i
        curdList.addRowsData(handleData)
      }
      // const resultArrays = filteredValues.reduce((result, item) => {
      //   // item 为每一列的值 有可能是拼接的值 有可能除了文本框其他表单元素(这种值不用处理 一般没拼接)
      //   debugger
      //   if (typeof item === 'string' && item?.includes(',')) {
      //     let values = item.split(',')
      //     if (values.length !== selectedData?.list?.length) {
      //       // 如果每一项逗号分隔后长度和选中的数据的数量不一致 则证明是空字符 则填充和采购数一致的空字符
      //       values = Array(selectedData?.list?.length).fill('')
      //     }
      //     values.forEach((value, index) => {
      //       if (!result[index]) {
      //         result[index] = []
      //       }
      //       result[index].push(value)
      //     })
      //   } else {

      //   }
      //   console.log('result', result)
      //   return result
      // }, [])

      // if (selectedData?.list?.length === resultArrays?.length) {
      //   resultArrays.forEach((resultArray, index) => {
      //     let handleData = {}
      //     Object.keys(filteredObject)?.forEach((k, i) => {
      //       if (i === 0) {
      //         handleData.id = 'newRow' + new Date().getTime() + index
      //       }
      //       // 处理数据
      //       handleData = {
      //         ...handleData,
      //         [k]: resultArray.flat()[i]
      //       }
      //     })
      //     curdList.addRowsData(handleData)
      //   })
      // }
    },
    handleRformSetting(block) {
      // 当两次点击一个区块的表头单元格时 不执行修改组件要素配置
      const isDifferBlock = this.params?.currentRformBlock?.id !== block?.id
      this.params.currentRformBlock = block

      // 修改组件要素配置
      if (isDifferBlock &&
          this.handleCurrentRformBlock &&
          this.$isNotEmpty(this.params.currentRformBlock)) {
        const block = this.params?.currentRformBlock
        const initParams = this.$refs.curdList.$refs.baseList?.getInitParams?.()
        const extraParams = {
          buttons: initParams?.buttons, // 当前baseList按钮
          currRformParams: this.params // 当前区块rform的params
        }
        this.handleCurrentRformBlock(block, this.params?.currentRformBlock?.config, extraParams)
      }
    },
    clearCurrentRformBlock() {
      this.params.currentRformBlock = null
    },
    setEditable(isEditable) { // 动态改变行表单是否可编辑
      this.$refs.curdList.setEditable(isEditable)
    },
    recordData(params, block) { // 记录相关数据
      var baseListExData = this.$refs.curdList.getBaseListExData()
      baseListExData['blockRformExtendKey'] = block.targetClassName

      this.blockRformExtendKey = block.targetClassName
      params.blockObjMap[this.blockRformExtendKey] = this
      this.$nextTick(() => {
        params.blockExtendObjMap[this.blockRformExtendKey] = this.$refs.blockRformExtend
      })
    },
    setCellDisable(rowIdOrRowIndex, label, disabled) {
      this.$refs.curdList.setCellDisable(rowIdOrRowIndex, label, disabled)
    },
    setRformCellValue(rowIdOrRowIndex, propOrLabel, value) {
      this.$refs.curdList.setCellValue(rowIdOrRowIndex, propOrLabel, value)
    },
    getRformCellValue(rowIdOrRowIndex, propOrLabel) {
      return this.$refs.curdList.getCellValue(rowIdOrRowIndex, propOrLabel)
    },
    getRformRowsData() {
      return this.$refs.curdList.rowsData
    },
    getRformTableSelection() {
      return this.$getTableSelection(this.$refs.curdList.getTable())
    },
    setBlockVisible(isVisible) { // 设置区块是否显示，如果不显示则会同步设置区块可以为空
      if (typeof isVisible !== 'boolean') {
        this.$message.error('isVisible必须是true或false')
        return
      }
      // 这个ID在block-container.vue中设置
      const bkViewId = `#block${this.params.mode}${this.block.id}`
      if (isVisible) {
        if (this.blockCanEmpty !== '未设置') { // 恢复之前的值
          this.block.canEmpty = this.blockCanEmpty
        }
        $(bkViewId).show()
      } else {
        // 隐藏这个区块，必须设置这个区块的数据可以是空
        this.setBlockCanEmpty(true)
        $(bkViewId).hide()
      }

      if (this.blockCanEmpty === '未设置') { // 设置可见时记录原始的值
        this.blockCanEmpty = this.block.canEmpty
        if (this.blockCanEmpty) {
          this.blockCanEmpty = '否'
        }
      }
    },
    setBlockCanEmpty(canEmpty) { // 设置区块数据是否可以为空
      if (typeof canEmpty !== 'boolean') {
        this.$message.error('canEmpty必须是true或false')
        return
      }
      this.block.canEmpty = canEmpty ? '是' : '否'
    },
    wrapAddRowEx(params) { // 包装新增行响应
      var addRowEx = params.addRowEx
      params.addRowEx = (newRow, curdListObj) => {
        if (addRowEx) {
          addRowEx(newRow, curdListObj)
        }

        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.addRowEx) {
          var baseListExData = curdListObj.getBaseListExData()
          this.$refs.blockRformExtend.addRowEx(
            newRow, curdListObj, params, baseListExData['blockRformExtendKey'])
        }
      }
    },
    wrapDeleteRowBefore(params) { // 包装删除行之前响应
      params.deleteRowBefore = (curdListObj, rowIds, rows) => {
        const baseListExData = curdListObj.getBaseListExData()
        return this.$refs.blockRformExtend?.deleteRowBefore?.(
          curdListObj, rowIds, rows, params, baseListExData['blockRformExtendKey'])
      }
    },
    wrapDeleteRowEx(params) { // 包装删除行响应
      var deleteRowEx = params.deleteRowEx
      params.deleteRowEx = (curdListObj, rowIds, rows) => {
        const baseListExData = curdListObj.getBaseListExData()
        if (deleteRowEx) {
          deleteRowEx(curdListObj, rowIds, rows, baseListExData['blockRformExtendKey'])
        }

        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.deleteRowEx) {
          this.$refs.blockRformExtend.deleteRowEx(
            curdListObj, rowIds, rows, params, baseListExData['blockRformExtendKey'])
        }
      }
    },
    wrapRowCellChanged(params) { // 包装行表单单元格值变化响应
      var rowCellChanged = params.rowCellChanged
      params.rowCellChanged = (scope, colItem, baseListObj) => {
        const baseListExData = baseListObj.getBaseListExData()
        if (rowCellChanged) {
          rowCellChanged(scope, colItem, baseListObj, baseListExData['blockRformExtendKey'])
        }

        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.rowCellChanged) {
          this.$refs.blockRformExtend.rowCellChanged(
            scope, colItem, baseListObj, params, baseListExData['blockRformExtendKey'])
        }
      }
    },
    wrapFormCellChanged(params) { // 包装表单要素变化响应
      var formCellChanged = params.formCellChanged
      params.formCellChanged = (colItem, selectedData) => {
        if (formCellChanged) {
          formCellChanged(colItem, selectedData)
        }

        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.formCellChanged) {
          this.$refs.blockRformExtend.formCellChanged(colItem, params, selectedData)
        }
      }
    },
    wrapCallbackAfterFormLoaded(params) { // 包装表单初始化之后回调响应
      var callbackAfterFormLoaded = params.callbackAfterFormLoaded
      params.callbackAfterFormLoaded = (dataVo) => {
        if (callbackAfterFormLoaded) {
          callbackAfterFormLoaded(dataVo)
        }

        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.callbackAfterFormLoaded) {
          this.$refs.blockRformExtend.callbackAfterFormLoaded(dataVo, params)
        }
      }
    },
    wrapCallbackBeforeFormLoaded(params) { // 包装表单初始化之前回调响应
      var callbackBeforeFormLoaded = params.callbackBeforeFormLoaded
      params.callbackBeforeFormLoaded = (initFormExData) => {
        if (callbackBeforeFormLoaded) {
          callbackBeforeFormLoaded(initFormExData)
        }

        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.callbackBeforeFormLoaded) {
          this.$refs.blockRformExtend.callbackBeforeFormLoaded(initFormExData, params)
        }
      }
    },
    wrapReloadTableCallback(params) { // 包装行表单初始化之后回调响应
      var reloadTableCallback = params.reloadTableCallback
      params.reloadTableCallback = (result, table, baseListObj) => {
        if (reloadTableCallback) {
          reloadTableCallback(result, table, baseListObj)
        }

        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.reloadTableCallback) {
          var baseListExData = baseListObj.getBaseListExData()
          this.$refs.blockRformExtend.reloadTableCallback(
            result, table, baseListObj, params, baseListExData['blockRformExtendKey'])
        }
      }
    },
    wrapCustomSelectOptionsSupplier(params) { // 包装自定义下拉框数据源
      var customSelectOptionsSupplier = params.customSelectOptionsSupplier
      params.customSelectOptionsSupplier = (scope, colItem) => {
        var returnData
        if (customSelectOptionsSupplier) {
          returnData = customSelectOptionsSupplier(scope, colItem)
          if (returnData) {
            return returnData
          }
        }

        if (this.$refs.blockRformExtend &&
          this.$refs.blockRformExtend.customSelectOptionsSupplier) {
          returnData = this.$refs.blockRformExtend.customSelectOptionsSupplier(
            scope, colItem, params)
          if (returnData) {
            return returnData
          }
        }
      }
    },
    updateBeforeSave(isDraft) {
      this.blockError = ''
      this.$refs.curdList.clearError()
    },
    showError(result, isBLock = true) {
      // 区块错误类似：
      // 区块错误={区块ID={行索引={错误key=错误信息}}}}
      // 比如区块全局错误为：
      // 区块错误={1701136242824007682={-1={区块全局错误=不能是空数据}}}}
      var blockErrorDataAll = result.attributes['区块错误']
      if (this.$isNotEmpty(blockErrorDataAll)) {
        var blockErrorDataCurrent = blockErrorDataAll[this.block.id]
        if (this.$isNotEmpty(blockErrorDataCurrent)) {
          var blockErrorGlobal = blockErrorDataCurrent['-1']
          if (this.$isNotEmpty(blockErrorGlobal)) {
            this.blockError = blockErrorGlobal['区块全局错误']
          }

          this.$refs.curdList.showError(blockErrorDataCurrent, isBLock)
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .blockViewBlock /deep/ .el-table .el-table__cell .cell {
    height: 32px;
    line-height: 32px;
  }
  .blockViewBlock /deep/ .el-table .el-table__cell.rFromHeaderBorder {
    border: 1px solid #006bcc !important;
  }
</style>
