<template>
  <div style="height: 100%">
    <b-list ref="baseListAssembleData" @handleSearch="handleSearch"/>
  </div>
</template>

<script>
import BList from '../page/base-list'
export default {
  name: 'template-assemble-data',
  components: { BList },
  data() {
    return {
      templateTypeTreeData: undefined,
      booksetDropData: undefined
    }
  },
  methods: {
    refreshData(queryParams) {
      queryParams = queryParams || {}
      var templateKey = queryParams.templateKey
      if (this.$isEmpty(templateKey)) {
        this.$message.error('templateKey不能为空')
        return
      }
      this.booksetDropData = []
      var noBooksetFilterTitle = queryParams.noBooksetFilterTitle || '单位'
      var templateTypeRootTitle = queryParams.templateTypeRootTitle
      var searchFormStyle = ''
      var isShowDefaultVal = ''

      var refreshList = () => {
        var params = Object.assign({
          dataApiKey: 'selectPageDataTemplateAssemble' }, queryParams)

        // 调用handleSearch获取默认的模板父级编码参数
        params.searchParam = params.searchParam || {}
        this.handleSearch(params.searchParam)

        var exportExcelName = queryParams.exportExcelName || ''
        var searchForm = queryParams.searchForm || []
        if (this.$isNotEmpty(templateTypeRootTitle)) {
          var treeSetting = {
            check: {
              enable: false
            },
            data: {
              simpleData: {
                enable: true,
                idKey: 'id',
                pIdKey: 'parentId'
              },
              key: {
                name: 'name'
              }
            }
          }

          if (queryParams.noBooksetFilter !== true &&
              this.$isNotEmpty(this.booksetDropData)) {
            searchForm.push(noBooksetFilterTitle +
              ':ACCOUNT_SET_ID_in:下拉:#' + JSON.stringify(this.booksetDropData))
            searchFormStyle = noBooksetFilterTitle + '#height:auto;width:250px;,'
            isShowDefaultVal = noBooksetFilterTitle + ':' + this.booksetDropData[0].value + ','
            params.searchParam['ACCOUNT_SET_ID_in'] = this.booksetDropData[0].value
          }
          searchForm.push(
            templateTypeRootTitle + ':模板父级节点_eq:树:#' +
              JSON.stringify(this.templateTypeTreeData) +
              ':##' + JSON.stringify(treeSetting))
          searchFormStyle += templateTypeRootTitle + '#height:auto;width:250px;'
          isShowDefaultVal += templateTypeRootTitle + ':' + params.searchParam['模板父级节点名称']
        }

        this.$refs.baseListAssembleData.init({
          showPager: false,
          searchForm: searchForm,
          searchFormStyle: searchFormStyle,
          isShowDefaultVal: isShowDefaultVal,
          exportExcelName: exportExcelName,
          params: params,
          listContentSubId: params.listContentSubId
        })
      }
      this.initTemplateTypeTreeData(queryParams, refreshList)
    },
    initTemplateTypeTreeData(queryParams, callback) {
      if (this.templateTypeTreeData) {
        callback()
        return
      }

      this.$callApiParams('selectRowTemplateCommonData',
        queryParams,
        result => {
          var treeDataAll = result.attributes.treeData
          this.templateTypeTreeData = []
          this.booksetDropData = result.attributes['账套列表']
          var treeNodeIdLabels = {}
          treeDataAll.forEach(node => { treeNodeIdLabels[node.id] = node.label })
          treeDataAll.forEach(re => {
            // 父节点空时是总节点，如“预算项目模板”，不显示总节点；同时这里也不显示模板本身
            // 这里的树id和label的值设置成相同的值，使得传递给后端的查询值就是节点的label
            if (this.$isNotEmpty(re.parentId) && !this.isTempateNode(re)) {
              this.templateTypeTreeData.push({
                id: treeNodeIdLabels[re.id],
                name: re.label,
                parentId: treeNodeIdLabels[re.parentId],
                code: treeNodeIdLabels[re.id] })
            }
          })
          callback()
          return true
        })
    },
    isTempateNode(treeNode) {
      return (treeNode &&
        treeNode.exData &&
        treeNode.exData.isTemplate)
    },
    handleSearch(searchParam) {
      // 如果当前查询没有指定模板父级，则取第一个
      searchParam = searchParam || {}
      var currentTemplateName = searchParam['模板父级节点_eq']
      if (this.$isEmpty(currentTemplateName)) {
        currentTemplateName = this.getDefaultTemplateCode()
      }
      if (this.$isNotEmpty(currentTemplateName)) {
        searchParam['模板父级节点名称'] = currentTemplateName
      }
    },
    getDefaultTemplateCode() {
      if (this.$isNotEmpty(this.templateTypeTreeData)) {
        return this.templateTypeTreeData[0].name
      }
      return ''
    }
  }
}
</script>
