<template>
  <div style="height: 100%" :class="{'ptop-10': tabsType === 'card'}">
    <el-tabs v-model="activedTab" ref="tabs" :type="tabsType" @tab-click="tabClick">
      <el-tab-pane
        :key="tab.tabName"
        :label="tab.tabLabel"
        :name="tab.tabName"
        v-for="tab in tabs">
        <component ref="tabContents" :is="tab.componentName"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getTabsType } from '@/utils'

export default {
  name: 'row-form-level3-detail',
  data() {
    return {
      parentRowId: '',
      parentBaseListObj: undefined,
      activedTab: '',
      tabs: [],
      pageDataMap: undefined,
      pageDataCurrent: undefined,
      pageDataKeyPrefix: undefined,
      pageDataKeyCurrent: undefined,
      level3EditDetailCols: undefined,
      params: '',
      tabsType: ''
    }
  },
  watch: {
    parentRowId(val) {
      this.$nextTick(() => { this.resetCurrentTabPageData(val) })
    }
  },
  mounted() {
    this.tabsType = getTabsType(this.$refs.tabs.$el)
  },
  methods: {
    init(params) {
      this.params = params
      params = params || {}
      params.level3EditDetailCols = params.level3EditDetailCols || []
      if (this.$isEmpty(params.level3EditDetailCols)) {
        return
      }

      var tabIndex = 0
      this.tabs = []
      this.activedTab = ''
      this.level3EditDetailCols = params.level3EditDetailCols
      var tabRowFormPageDataMap = Object.keys(params.originParams.dataVo.extData.tabRowFormPageDataMap)
      for (let i = 0; i < tabRowFormPageDataMap.length; i++) {
        if (tabRowFormPageDataMap[i].includes('_')) {
          var tabName = tabRowFormPageDataMap[i].split('_')
          if (tabName[1] === params.originParams.tabName) {
            params.originParams.metaIdTab = tabName[0]
          }
        }
      }
      this.pageDataKeyPrefix = this.$makeRowFormKey(
        params.originParams.metaIdTab,
        params.originParams.tabName)
      this.wrapPageDataMapToUi(params)

      params.level3EditDetailCols.forEach(col => {
        var tab = {}
        tab.tabName = col.prop
        if (col.prop?.includes?.('子项目测算明细')) {
          tab.tabLabel = col.label
        } else {
          tab.tabLabel = col.prop
        }

        tab.tableHeader = col
        tab.columnEntity = col.columnEntity

        var level3Type = col.exData['三级明细类别']
        if (level3Type === '三级明细数据列') {
          level3Type = '列表编辑'
        }
        tab.componentName = 'form-tab-save-' + level3Type

        tab.params = {}
        tab.params.originParams = params.originParams
        tab.params.dataVo = params.originParams.dataVo
        tab.params.rowsDataInit = params.rowsData
        tab.params.formId = params.originParams.formId
        tab.params.formIdTabName = this.pageDataKeyPrefix
        tab.params.tabIndex = tabIndex++
        tab.params.metaIdTab = col.prop // 这个是3级明细列，初始化时明细行是空行
        tab.params.tabName = ''
        tab.params.isEdit = params.isEdit
        tab.params.getFormExtend = params.originParams.getFormExtend
        tab.params.getDataToSave = params.originParams.getDataToSave
        tab.params.tableExStyleClass = 'blue-table'
        tab.params.parentBaseListObj = this.parentBaseListObj
        tab.params.deleteRowEx = () => { this.syncTabLevel3ColValue() }
        tab.params.rowCellChanged = () => { this.syncTabLevel3ColValue() }
        tab.params.afterRowCalculation = () => { this.syncTabLevel3ColValue() }
        this.tabs.push(tab)
      })

      if (this.$isNotEmpty(this.tabs)) {
        this.$nextTick(() => {
          // 初始化所有tab
          var count = params.level3EditDetailCols.length
          for (var i = 0; i < count; i++) {
            this.showTabContent(i)
          }

          this.parentRowId = undefined
          this.activedTab = this.tabs[0].tabName
        })
      }
    },
    wrapPageDataMapToUi(params) {
      // 初始化时加工当前dataVo中的pageData数据，便于在3级明细数据显示
      // 主要意图是将页签主列表中的每一行，以其rowId为关键字，索引到每个
      // 3级明细列中的每个明细数据。即将后端存储使用的key转换为前端key：
      // formId_页签_3级明细列_rowIndex -> formId_页签_3级明细列_rowId
      // 之所以需要这样转换，是因为页签主列表支持行的上下移动，导致行的索引
      // 并不固定，索引为了数据不错误，使用固定的rowId作为与3级明细数据的索引
      var originParams = params.originParams
      var rowsData = params.rowsData
      var level3EditDetailCols = params.level3EditDetailCols

      this.pageDataMap = {}
      var dataVo = originParams.dataVo
      var tabRowFormPageDataMap = dataVo.extData.tabRowFormPageDataMap

      // 记录明细行是空行时列表的格式对象PageData
      level3EditDetailCols.forEach(col => {
        this.pageDataMap[col.prop] = tabRowFormPageDataMap[col.prop]
      })

      if (this.$isNotEmpty(rowsData)) {
        var indexFlagToRowIdMap = {}
        for (let i = 0; i < rowsData.length; i++) {
          var rowId = this.$getRowId(rowsData[i])
          indexFlagToRowIdMap['_' + i] = '_' + rowId
        }

        var keys = Object.keys(tabRowFormPageDataMap)
        keys.forEach(key => {
          if (key.indexOf(this.pageDataKeyPrefix) > -1) {
            var pageData = tabRowFormPageDataMap[key]
            var indexFlags = Object.keys(indexFlagToRowIdMap)
            indexFlags.forEach(indexFlag => {
              var rowIdFlag = indexFlagToRowIdMap[indexFlag]
              var newKey = key.replace(indexFlag, rowIdFlag)
              this.pageDataMap[newKey] = pageData
            })
          }
        })
      }
    },
    fillDataVoBeforeSave(dataVo, funcAddPageData) {
      var rowsData = this.parentBaseListObj.rowsData
      this.level3EditDetailCols.forEach(col => {
        if (col.exData['三级明细类别'] === '三级明细数据列') {
          var rowIndex = 0
          rowsData.forEach(row => {
            // 将前端key转换为后端存储使用的key：
            // formId_页签_3级明细列_rowId -> formId_页签_3级明细列_rowIndex
            // 需要这样转换给后端，是因为后端行表单机制中
            // 并没有rowId这个概念，使用的是rowIndex
            var rowId = this.$getRowId(row)
            var pageDataKey = this.pageDataKeyPrefix + '_' + col.prop + '_' + rowId
            var pageDataKeyToSave = this.pageDataKeyPrefix + '_' + col.prop + '_' + rowIndex
            var pageData = this.pageDataMap[pageDataKey]
            if (pageData) {
              funcAddPageData(dataVo, pageData, pageDataKeyToSave)
            }
            rowIndex++
          })
        }
      })

      this.$refs.tabContents.forEach(tc => {
        tc.clearError?.()
        if (tc.fillDataVoBeforeSave &&
            tc.tabData.componentName !== 'form-tab-save-列表编辑') {
          tc.fillDataVoBeforeSave(dataVo, funcAddPageData, this.parentBaseListObj)
        }
      })
    },
    syncTabLevel3ColValue() {
      // 由明细数据计算得到并设置页签对应3级明细单元格的值
      var pageDataKeyPrefix = this.pageDataKeyPrefix + '_' + this.activedTab + '_'
      if (this.$isNotEmpty(this.pageDataKeyCurrent)) {
        var level3DetailColValue = 0
        var rowId = this.pageDataKeyCurrent.replace(pageDataKeyPrefix, '')
        if (this.$isNotEmpty(rowId)) {
          if (this.pageDataCurrent) {
            this.pageDataCurrent.rows.forEach(row => {
              var cellValue = row[this.activedTab]
              if (this.$isNumber(cellValue)) {
                level3DetailColValue += parseFloat(this.$unFormatMoney(cellValue))
              }
            })
          }
          this.parentBaseListObj.setCellValue(rowId, this.activedTab, this.$fixMoney(level3DetailColValue))
        }
        var scope = {}
        scope.$index = rowId
        var statusBarPurAmountValue = this.params.originParams.dataVo.extData['状态栏采购金额取值']
        this.parentBaseListObj.$parent.$refs.baseList.formula(scope, '', statusBarPurAmountValue)
        if (statusBarPurAmountValue === '政府采购' && Number(level3DetailColValue) > 0) {
          this.parentBaseListObj.$parent.$refs.baseList.aotuPmAmountMethod()
        }
        this.updataStatusText()
      }
    },
    updataStatusText() {
      if (this.params.originParams.getFormExtend) {
        var formExtend = this.params.originParams.getFormExtend()

        var tabRowFormPageDataMap = this.params.originParams.dataVo.extData.tabRowFormPageDataMap
        var currentYear = parseInt(this.params.originParams.dataVo.extData.currentYear) + 1
        var statusBarPurAmountValue = this.params.originParams.dataVo.extData.状态栏采购金额取值
        const isAutoTotal = this.params.originParams.dataVo.extData.isAutoTotal
        const isShowConifcide = this.params.originParams.dataVo.extData.isShowConifcide
        // 不是自动化计算并且隐藏年度计划时档自动化计算处理
        const autoTotal = isAutoTotal || (!isShowConifcide && !isAutoTotal)
        var tabRowFormPageDataMapKey = Object.keys(tabRowFormPageDataMap)
        var countNum = 0
        var declareNum = 0
        var declareSum = 0
        var notPurNum = 0
        var purNum = 0
        // 其它年度申报数
        var otherNum = 0
        for (var i = 0; i < tabRowFormPageDataMapKey.length; i++) {
          if (tabRowFormPageDataMapKey[i].includes('_')) {
            var tabName = tabRowFormPageDataMapKey[i].split('_')

            if (tabName[1] === '预算信息' &&
              this.$isNotEmpty(Object.values(tabRowFormPageDataMap)[i].rows)) {
              var rows = Object.values(tabRowFormPageDataMap)[i].rows
              for (var j = 0; j < rows.length; j++) {
                var rowsYear = parseInt(rows[j].年度)
                if (rowsYear === currentYear) {
                  declareSum = parseFloat(this.$unFormatMoney(declareSum)) + parseFloat(this.$unFormatMoney(rows[j]['申报数']))
                  if (this.$isNotEmpty(rows[j][statusBarPurAmountValue])) {
                    purNum = parseFloat(this.$unFormatMoney(purNum)) + parseFloat(this.$unFormatMoney(rows[j][statusBarPurAmountValue]))
                  }
                } else {
                  countNum = parseFloat(this.$unFormatMoney(countNum)) + parseFloat(this.$unFormatMoney(rows[j].申报数))
                }
              }
            }

            if (tabName[1] === '政府采购预算信息' &&
              this.$isNotEmpty(Object.values(tabRowFormPageDataMap)[i].rows)) {
              var govRows = Object.values(tabRowFormPageDataMap)[i].rows
              for (let k = 0; k < govRows.length; k++) {
                var govRowsYear = parseInt(govRows[k].年度)
                if (govRowsYear === currentYear) {
                  declareSum = parseFloat(this.$unFormatMoney(declareSum)) + parseFloat(this.$unFormatMoney(govRows[k].申报数))
                  purNum = parseFloat(this.$unFormatMoney(purNum)) + parseFloat(this.$unFormatMoney(govRows[k][statusBarPurAmountValue]))
                } else {
                  countNum = parseFloat(this.$unFormatMoney(countNum)) + parseFloat(this.$unFormatMoney(govRows[k].申报数))
                }
              }
            }

            if (tabName[1] === '非政府采购预算信息' &&
              this.$isNotEmpty(Object.values(tabRowFormPageDataMap)[i].rows)) {
              var unGovRows = Object.values(tabRowFormPageDataMap)[i].rows
              for (let n = 0; n < unGovRows.length; n++) {
                var unGovYear = parseInt(unGovRows[n].年度)
                if (unGovYear === currentYear) {
                  declareSum = parseFloat(this.$unFormatMoney(declareSum)) + parseFloat(this.$unFormatMoney(unGovRows[n].申报数))
                } else {
                  countNum = parseFloat(this.$unFormatMoney(countNum)) + parseFloat(this.$unFormatMoney(unGovRows[n].申报数))
                }
              }
            }
          } else {
            var reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
            if (!reg.test(tabRowFormPageDataMapKey[i]) && !autoTotal) {
              var row = Object.values(tabRowFormPageDataMap)[i].rows
              row.forEach(item => {
                var year = parseInt(item.年度)
                if (year === currentYear) {
                  declareNum = item.申报数
                } else {
                  otherNum = parseFloat(this.$unFormatMoney(item.申报数))
                }
              })
            }
          }
        }
        notPurNum = declareSum - purNum
        if (!autoTotal) {
          countNum = this.$formatMoney(otherNum - countNum)
        } else {
          // 开启自动合计默认为0.00
          countNum = '0.00'
        }
        declareNum = this.$formatMoney(declareNum)
        declareSum = this.$formatMoney(declareSum)
        notPurNum = this.$formatMoney(notPurNum)
        purNum = this.$formatMoney(purNum)
        formExtend.setStatusText('当年项目计划申报数:' + declareNum + '元' + '\u3000' +
          '当年项目预算合计数:' + declareSum + '元' + '\u3000' +
          '当年非采购预算数:' + notPurNum + '元' + '\u3000' +
          '当年采购预算数:' + purNum + '元' + '\u3000' +
          '其他年度预算剩余数:' + countNum + '元', 'color: red;')
      }
    },
    clearTabPageData() {
      const resetData = (dataMap) => {
        const resetKeys = ['政府采购', '购买服务', '资产配置', '三级明细富文本', '子项目测算']
        const pageKeys = Object.keys(dataMap) || []
        pageKeys.forEach(key => {
          const keyIndex = resetKeys.findIndex(item => key.includes(item))
          if (key.includes('_') && keyIndex !== -1) {
            delete dataMap[key]
          }
        })
      }
      // 清除map缓存
      resetData(this.pageDataMap)
      const dataVo = this.params.originParams?.getDataToSave?.()
      const tabRowFormVoMap = dataVo.extData?.tabRowFormVoMap || {}
      const extData = dataVo.extData || {}
      // 清除tabRowFormVoMap缓存（'政府采购','购买服务','资产配置'）
      resetData(tabRowFormVoMap)
      // 清除extData缓存（'三级明细富文本', '子项目测算'）
      resetData(extData)
      this.$refs.tabContents.forEach(tc => {
        if (tc.tabData?.componentName !== 'form-tab-save-列表编辑') {
          tc.clearData?.()
        } else {
          tc.clearRow?.()
        }
      })
    },
    // 子项目测算是否有挑选功能
    submeaGetEstMode() {
      this.$refs.tabContents.forEach(tc => {
        if (tc.tabData?.componentName === 'form-tab-save-子项目测算') {
          tc.submeaGetEstMode?.()
        }
      })
    },
    resetCurrentTabPageData(rowId) { // 重新设置当前页签的表格行，设置按钮，和记录当前数据
      var tabContent
      this.pageDataKeyCurrent = undefined
      this.$refs.tabContents.forEach(tc => {
        if (tc.tabData?.tabName === this.activedTab) {
          tabContent = tc
        }

        if (tc.tabData?.componentName !== 'form-tab-save-列表编辑') {
          var theParentRowId = this.$isEmpty(rowId) ? this.parentRowId : rowId
          tc.parentRowId = theParentRowId
        }
      })

      if (tabContent &&
        tabContent.tabData.componentName === 'form-tab-save-列表编辑') {
        var rowIds = this.parentBaseListObj.getTableCheckedIds()
        if (rowId) {
          rowIds = [rowId]
        }

        if (rowIds.length === 1) {
          tabContent.disableAllBtsResume()
          this.pageDataKeyCurrent =
            this.pageDataKeyPrefix + '_' + this.activedTab + '_' + rowIds[0]
        } else {
          setTimeout(() => tabContent.disableAllBts(), 0)
          this.pageDataKeyCurrent = this.activedTab
        }

        var pageData = this.pageDataMap[this.pageDataKeyCurrent]
        if (pageData) {
          if (this.$isNotEmpty(pageData.columns)) {
            pageData.columns.forEach(col => { col.isLevel3DetailCol = false })
          }
          tabContent.fillTable(pageData)
          this.pageDataCurrent = pageData
        }
      }
    },
    tabClick(tab) {
      this.$refs.tabContents.forEach(item => {
        // 切换页签时，需要记录页签当前的编辑数据
        if (item.recordValues && item.tabData &&
          item.tabData.params.isEdit) {
          item.recordValues(item.parentRowId)
        }
      })

      this.showTabContent(parseInt(tab.index))
      this.resetCurrentTabPageData()
    },
    showTabContent(tabIndex) {
      var tabCompnent = this.$refs.tabContents[tabIndex]
      if (tabCompnent) {
        var tab = this.tabs[tabIndex]
        tabCompnent.tabData = tab
        tab.params.isCurrentTheLevel3Detail = true
        tabCompnent.showTabContent(tab.params)
        return tabCompnent
      }
    },
    showError(result) {
      this.$refs.tabContents.forEach(item => {
        if (item.showError) {
          item.showError(result)
        }
      })
    },
    parentAddRow(newRow, copyRow) {
      // 页签主列表新增一行时，明细列表所有的tab添加一个对应的pageData
      var rowId = this.$getRowId(newRow)
      const copyRowId = this.$getRowId(copyRow)
      this.level3EditDetailCols.forEach(col => {
        let cloneRow = this.pageDataMap[col.prop]
        const copyKey = this.pageDataKeyPrefix + '_' + col.prop + '_' + copyRowId
        const copyData = this.pageDataMap[copyKey]
        if (copyRowId && copyData) {
          cloneRow = copyData
        }
        var pageDataClone = this.$cloneDeep(cloneRow)
        var newKey = this.pageDataKeyPrefix + '_' + col.prop + '_' + rowId
        this.pageDataMap[newKey] = pageDataClone
      })
      // 如果是复制行
      if (copyRow) {
        this.$refs.tabContents.forEach(tc => {
          if (tc.tabData.componentName !== 'form-tab-save-列表编辑') {
            tc.recordValues?.(rowId)
          }
        })
      }
    },
    parentDeleteRow(baseCurdObj, rowIdsDeleted) {
      rowIdsDeleted.forEach(rowId => {
        this.level3EditDetailCols.forEach(col => {
          var pageDataKey = this.pageDataKeyPrefix + '_' + col.prop + '_' + rowId
          delete this.pageDataMap[pageDataKey]
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ptop-10 {
  padding-top: 10px;
}
</style>
