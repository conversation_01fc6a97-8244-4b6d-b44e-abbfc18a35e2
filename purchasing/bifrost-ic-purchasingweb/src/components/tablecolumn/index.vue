<template>
    <page ref="page">
        <template #pageContent>
            <LayoutTem isDbCol :isPageShow="false" :isFilterShow="false">
                <template #dbColLeft >
                    <classify-ztree
                            ref="classifytree"
                            dataType="TableColumn"
                            :showNodeType="true"
                            deleteApiKey="deleteClassifyTreeNode"
                            :deleteExtraParams="{actionKey:'DELETE'}"
                            :drag="true"
                            :saveExtraParams="{ actionKey: 'SAVE' }"
                            @nodeClick="nodeClick"
                            @leafNodeAdded="leafNodeAdded"/>
                </template>
                <template #main>
                <div style="width: 100%;height: 100%;box-sizing: border-box;">
                    <div class="tableListSettingInPage tableListSetting mini-table">
                        <div class="tableListSettingColSelected">
                            <div class="table-btns">
                              <el-input placeholder="查找" prefix-icon="el-icon-search"
                                        v-model="tableListSettingColAllSearchText" class="colSettingFilterInput"
                                        style="margin-right: 0px;width: 130px;"/>
                              <el-button style="margin-left: 0px;" icon="el-icon-zoom-in" title='新增列' size="small" @click="btAddClick"></el-button>
                              <el-button style="margin-left: 0px;" icon="el-icon-delete" title='删除列' size="small" @click="btDeleteClick"
                                         :loading="isDeleting" :disabled="btDeleteDisabled"></el-button>
                              <el-button style="margin-left: 0px;" icon="el-icon-document-copy" title="复制列" @click="btCopyClick"
                                         :loading="isCopying" :disabled="btDeleteDisabled"/>
                              <el-button style="margin-left: 0px;" icon="el-icon-upload2" title="导入列" @click="btExcelImportData"
                                         />
                            </div>
                            <el-table
                                    ref="tableListSettingColAll"
                                    :data="tableListSettingColAllShow"
                                    border
                                    @selection-change="tableListSettingColAllChange"
                                    @row-click='tableListSettingColAllClick'
                                    style="width: 280px;height: 100%;">
                                <el-table-column type="selection" width="25"></el-table-column>
                                <el-table-column label="表格列" prop="label"></el-table-column>
                                <el-table-column label="序号" width="40" prop="order"></el-table-column>
                                <el-table-column label="类型" width="70" prop="colType"></el-table-column>
                            </el-table>
                        </div>
                        <div class="tableListSettingColEdit colSetting">
                        <div class="table-btns">
                            <el-button  size="small" @click="btTableListSettingSaveCol"
                                       :type="isTableListSettingColEditing? 'primary' : 'plain'"
                                       :disabled="!isTableListSettingColEditing"
                                       :loading="isTableListSettingColSaving">{{btTableListSettingColText}}</el-button>
<!--                            <el-button @click="btSaveRowFrom" type="plain" size="mini">测试行编辑表单</el-button>-->
                        </div>
                        <div class="tableListSettingColEditForm colSettingSelectedSettingEdit">
                            <el-form label-width="70px" size="mini">
                                <el-form-item label="表格列名">
                                    <el-input v-model="colItem.label" :disabled="!isTableListSettingColEditing"/>
                                </el-form-item>
                                <el-form-item label="要素名称">
                                    <el-input v-model="colItem.prop" :disabled="!isTableListSettingColEditing"/>
                                </el-form-item>
                                <el-form-item label="数据类型">
                                    <el-select v-model="colItem.colType"
                                              @change="colTypeChange" :disabled="!isTableListSettingColEditing">
                                        <el-option
                                                v-for="(item, index) in commonData.colTypes"
                                                :key="index"
                                                :label="item"
                                                :value="item"/>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="列序号">
                                    <el-input v-model="colItem.order" :disabled="!isTableListSettingColEditing"
                                        onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
                                </el-form-item>
                                <el-form-item label="列宽度">
                                    <el-input v-model="colItem.width" :disabled="!isTableListSettingColEditing"
                                        onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
                                </el-form-item>
                                <el-form-item label="对齐方式">
                                    <el-select v-model="colItem.align" :disabled="!isTableListSettingColEditing">
                                        <el-option key="left" value="left" label="靠左对齐"/>
                                        <el-option key="right" value="right" label="靠右对齐"/>
                                        <el-option key="center" value="center" label="居中对齐"/>
                                    </el-select>
                                </el-form-item>
                              <el-form-item :label="minText">
                                <el-input v-model="colItem.min" :disabled="!isTableListSettingColEditing"
                                          onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
                              </el-form-item>
                              <el-form-item :label="maxText">
                                <el-input v-model="colItem.max" :disabled="!isTableListSettingColEditing"
                                          onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
                              </el-form-item>
                              <el-form-item label="父级名称">
                                <el-input v-model="colItem.parentName" :disabled="!isTableListSettingColEditing"/>
                              </el-form-item>
                                <el-form-item label="是否排序">
                                    <div style="display: flex;">
                                        <div style="flex: 2;width: 65px;display: flex;">
                                            <el-radio v-model="colItem.sortable" :label="true"
                                                      :disabled="!isTableListSettingColEditing">是</el-radio>
                                            <el-radio v-model="colItem.sortable" :label="false"
                                                      :disabled="!isTableListSettingColEditing">否</el-radio>
                                        </div>
                                    </div>
                                </el-form-item>
                                <el-form-item label="是否固定">
                                    <div style="display: flex;">
                                        <div style="flex: 2;width: 65px;display: flex;">
                                            <el-radio v-model="colItem.fixable" :label="true"
                                                      :disabled="!isTableListSettingColEditing">是</el-radio>
                                            <el-radio v-model="colItem.fixable" :label="false"
                                                      :disabled="!isTableListSettingColEditing">否</el-radio>
                                        </div>
                                    </div>
                                </el-form-item>
                              <el-form-item label="是否合并">
                                <div style="display: flex;">
                                  <div style="flex: 2;width: 65px;display: flex;">
                                    <el-radio v-model="colItem.isMerge" :label="true"
                                              :disabled="!isTableListSettingColEditing">是</el-radio>
                                    <el-radio v-model="colItem.isMerge" :label="false"
                                              :disabled="!isTableListSettingColEditing">否</el-radio>
                                  </div>
                                </div>
                              </el-form-item>
                                <el-form-item label="是否导出">
                                    <div style="display: flex;">
                                        <div style="flex: 2;width: 65px;display: flex;">
                                            <el-radio v-model="colItem.exportable" :label="true"
                                                      :disabled="!isTableListSettingColEditing">是</el-radio>
                                            <el-radio v-model="colItem.exportable" :label="false"
                                                      :disabled="!isTableListSettingColEditing">否</el-radio>
                                        </div>
                                    </div>
                                </el-form-item>
                              <el-form-item label="是否必填">
                                <div style="display: flex;">
                                  <div style="flex: 2;width: 65px;display: flex;">
                                    <el-radio v-model="colItem.isRequired" :label="true"
                                              :disabled="!isTableListSettingColEditing">是</el-radio>
                                    <el-radio v-model="colItem.isRequired" :label="false"
                                              :disabled="!isTableListSettingColEditing">否</el-radio>
                                  </div>
                                </div>
                              </el-form-item>
                              <el-form-item label="公示金额" v-show="showPublicInfoAmount">
                                <div style="display: flex;">
                                  <div style="flex: 2;width: 65px;display: flex;">
                                    <el-radio v-model="colItem.isSystem" :label="true"
                                              :disabled="!isTableListSettingColEditing">是</el-radio>
                                    <el-radio v-model="colItem.isSystem" :label="false"
                                              :disabled="!isTableListSettingColEditing">否</el-radio>
                                  </div>
                                </div>
                              </el-form-item>
                            </el-form>
                        </div>
                    </div>
                    </div>
                </div>
            </template>
            </LayoutTem>
        </template>
    </page>
</template>

<script>
export default {
  name: 'tablecolumnn',
  data() {
    return {
      tableListSettingColAllSearchText: '', // 表格列列表搜索文本
      isDeleting: false, // 是否正在删除列
      isCopying: false,
      btDeleteDisabled: true, // 删除列按钮是否可用
      isTableListSettingColEditing: false, // 是否正在编辑表格列设置
      isTableListSettingColSaving: false, // 是否表格列设置正在保存
      colItem: {}, // 当前编辑的列
      commonData: this.newCommonData(),
      btTableListSettingColText: '新增表格列', // 表格列设置保存按钮文本
      columnData: {}, // 列数据
      selectNodeId: '',
      tableListSettingColAll: [], // 表格列列表
      showPublicInfoAmount: false
    }
  },
  computed: {
    tableListSettingColAllShow() { // 表格列定义实际显示的数据
      var items = []
      if (this.$isNotEmpty(this.tableListSettingColAllSearchText)) {
        items = this.tableListSettingColAll.filter(item => {
          return String(item.label.toLowerCase())
            .match(this.tableListSettingColAllSearchText.toLowerCase())
        })
      } else {
        items = this.tableListSettingColAll
      }
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      const orderTem = Math.max.apply(Math, items.map(item => { return item.order })) + 1
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.order = this.$isEmpty(items) ? 0 : orderTem
      return this.$sortColumnList(items)
    },
    minText() {
      return (this.colItem.colType === '文本') ? '最小长度' : '最小值'
    },
    maxText() {
      return (this.colItem.colType === '文本') ? '最大长度' : '最大值'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.page.commonPageClassEx =
        this.$refs.page.commonPageClassEx + ' column-top-hide'
      this.$selectColSettingCommonData(result => {
        this.commonData.colTypes = result.attributes.colTypes
      })
    })
  },
  methods: {
    btSaveRowFrom() {
      this.$showRowFrom({
        isEdit: true,
        title: '公车使用公示',
        bizzId: '测试001',
        treeNodeLabel: '选择合同'
      })
    },
    colTypeChange() {
      if (this.colItem.colType === '文本') {
        this.colItem.align = 'left'
      } else {
        this.colItem.align = 'right'
      }
    },
    nodeClick(data, exData) { // needToReloadFreeJson表明是通过手动点击打开
      exData = exData || {}
      exData.needToReloadFreeJson = true
      this.selectNodeId = data.id
      this.$selectTableColumnList(this.selectNodeId,
        result => {
          this.tableListSettingColAll = result.data
          this.isTableListSettingColSaving = false
        }, result => { this.isTableListSettingColSaving = false })

      this.showPublicInfoAmount = false
      if (data.label.indexOf('公示：') > -1 || data.label.indexOf('公示:') > -1) {
        this.showPublicInfoAmount = true
      }
    },
    leafNodeAdded(nodeId, itemKey) { // 新增叶子节点后，将其设置为当前节点
      this.setCurrentItem(nodeId, itemKey)
    },
    setCurrentItem(nodeId, itemKey) { // 设置当前对象
      this.currentId = (itemKey == null) ? '' : itemKey
      this.$refs.classifytree.setCurrentItem(nodeId, itemKey)
    },
    btAddClick() {
      if (this.$isEmpty(this.selectNodeId)) {
        this.$message.error('请先选择分类节点！')
        return
      }
      this.isTableListSettingColEditing = true
      this.colItem = {
        dataType: this.selectNodeId,
        label: '新增表格列',
        prop: '',
        order: this.order,
        align: 'left',
        colType: '文本',
        sortable: false,
        fixable: false,
        exportable: true,
        isRequired: false,
        isSystem: false,
        isMerge: false,
        min: 0,
        max: 30
      }
    },
    btDeleteClick() { // 删除表格列
      this.deleteColItems(this.$refs.tableListSettingColAll)
    },
    btCopyClick() { // 复制表格列
      this.copyColItems(this.$refs.tableListSettingColAll)
    },
    btExcelImportData() { // 导入
      if (this.$isEmpty(this.selectNodeId)) {
        this.$message.error('请先选择分类节点！')
        return
      }
      var apiKey = 'template/列定义导入模板.xls'
      var fileName = '列定义导入模板.xls'
      var tableColumn = []
      this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
        DATA_TYPE: this.selectNodeId,
        columnExcelType: '1',
        onSuccess: () => {
          this.selectTableColumnList()
        } })
    },
    tableListSettingColAllChange(checkedRows) {
      this.btDeleteDisabled = true
      this.isTableListSettingColEditing = false
      this.btTableListSettingColText = '新增表格列'
      this.colItem = {}

      if (checkedRows.length === 1) {
        this.btDeleteDisabled = false
        this.colItem = checkedRows[0]
        this.isTableListSettingColEditing = true
        this.btTableListSettingColText = '修改表格列'
      }

      if (checkedRows.length > 1) {
        this.$refs.tableListSettingColAll.clearSelection()
        this.$refs.tableListSettingColAll.toggleRowSelection(checkedRows.pop())
      } else {
        this.multipleSelection = checkedRows
      }
    },
    tableListSettingColAllClick(row) {
      this.$refs.tableListSettingColAll.toggleRowSelection(row)
    },
    tableListSettingColAllDblclick(row, column, event) { // 双击表格列表行，则删除该表格列
      var $table = { selection: [{ id: row.id }] } // 构造模拟的table
      this.deleteColItems($table)
    },
    deleteColItems($table) { // 删除表格列
      this.isDeleting = true
      this.$deleteTableColumn(
        $table,
        result => {
          this.selectTableColumnList(re => {
            this.isDeleting = false
            this.$selectTableColumnList(this.selectNodeId,
              result => {
                this.tableListSettingColAll = result.data
                this.isTableListSettingColSaving = false
              }, result => { this.isTableListSettingColSaving = false })
          })
        }
      )
    },
    copyColItems($table) { // 复制表格列
      this.$copyTableColumn(
        $table,
        result => {
          this.selectTableColumnList(result => {
            this.$selectTableColumnList(this.selectNodeId,
              result => {
                this.tableListSettingColAll = result.data
                this.isTableListSettingColSaving = false
              }, result => { this.isTableListSettingColSaving = false })
          })
        }
      )
    },
    selectTableColumnList(callback) { // 刷新表格列列表
      this.tableListSettingColAllChange([]) // 清除原先编辑数据
      this.$selectTableColumnList(this.getCurrentDataType(),
        result => {
          this.tableListSettingColAll = result.data
          this.isTableListSettingColSaving = false
          if (callback) {
            callback(result)
          }
        }, result => { this.isTableListSettingColSaving = false })
    },
    getCurrentDataType() {
      return '表格列:' + this.tableListSettingCurrentFormType
    },
    btTableListSettingSaveCol(returnValue, callback) {
      // 如果labelOrigin等于空，表明是新增表格列，此时设置labelOrigin=label
      if (this.$isEmpty(this.colItem.labelOrigin) ||
        this.btTableListSettingColText === '新增表格列') {
        this.colItem.labelOrigin = this.colItem.label
      } else {
        // 否则表明是修改，设置labelAlias=label，这样能保证新增之后labelOrigin保持不变
        this.colItem.labelAlias = this.colItem.label
      }
      this.saveCols([this.colItem], returnValue, callback)
    },
    saveCols(cols, returnValue, callback) {
      this.isTableListSettingColSaving = true
      this.$saveTableColumn({ cols: cols }, resut => {
        this.selectTableColumnList(rt => {
          this.isTableListSettingColEditing = false
          if (typeof (callback) === 'function') {
            callback(rt)
          }
          this.$selectTableColumnList(this.selectNodeId,
            result => {
              this.tableListSettingColAll = result.data
              this.isTableListSettingColSaving = false
            }, result => { this.isTableListSettingColSaving = false })
        })
        return returnValue
      }, result => { this.isTableListSettingColSaving = false })
    },
    newCommonData() {
      return {
        cformCols: [], // 表单要素列表
        colTypes: [] // 表格列类型
      }
    }
  }
}
</script>

<style lang="scss" scoped>
    .click-el-card-border{
        border: 1px solid #5C96BC !important;
    }
    .tableListSettingColAll, .tableListSettingColSelected { margin-right: 10px;}
    .tableListSettingColEditButton { margin-bottom: 20px; }
    .tableListSetting { display: flex; }
    .tableListSettingColEditForm {
        border: 1px solid #DDDDDD;
        height: calc(100% - 52px);
        width: 290px;
        padding: 10px;
        position: relative;
    }
    .common-page /deep/ .tableListSettingInPage { height: 100%; }
    .common-page /deep/ .tableListSettingInPage .tableListSettingColSelected .el-table {
      height: calc(100% - 49px) !important;
      font-size: 14px;
    }
    .common-page /deep/ .tableListSettingInPage .el-icon-search { display: none; }
    thead .el-table-column--selection .cell {
        display: none!important;
    }
    .colSettingFilterInput {
      /deep/.el-input__inner {
        padding: 6px 12px !important;
      }
    }
    .table-btns .el-button {
      padding: 9px;
    }
</style>
