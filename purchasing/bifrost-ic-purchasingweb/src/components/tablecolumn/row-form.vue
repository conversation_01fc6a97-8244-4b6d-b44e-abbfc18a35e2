<template>
  <b-curd class="row-form" ref="baseCurdRowForm"/>
</template>

<script>
import $ from 'jquery'
export default{
  name: 'row-form',
  data() {
    return {
      isEdit: false,
      bizzId: '',
      treeNodeLabel: '',
      cloneRow: {},
      rowsData: [],
      headerIndexes: {},
      callbackSaved: undefined,
      callbackBeforeSave: undefined,
      isDetailDlg: undefined,
      formType: undefined
    }
  },
  methods: {
    init(dlg, params) {
      this.bizzId = params.bizzId
      this.treeNodeLabel = params.treeNodeLabel
      this.isEdit = params.isEdit
      this.callbackSaved = params.callbackSaved
      this.callbackBeforeSave = params.callbackBeforeSave
      this.isDetailDlg = params.isDetailDlg || false
      this.formType = params.formType

      var dlgTokens = this.$resolvePublicInfosDlgTokens(params.treeNodeLabel)
      this.$setDlgSize(dlg, 'globalDialog', dlgTokens[1], dlgTokens[2])

      if (dlg) { // 设置弹框保存按钮的响应函数
        dlg.setCallbackOK(this.saveRowFrom)
      }
      this.markErrors(undefined, true)

      this.$refs.baseCurdRowForm.init({
        showPager: false,
        hideCurdButton: ['修改', '详情'],
        params: {
          dataApiKey: 'selectRowFromPageData',
          isEdit: this.isEdit,
          bizzId: this.bizzId,
          treeNodeLabel: this.treeNodeLabel },
        buttons: [
          { text: '上移', icon: 'el-icon-top', enabledType: '1',
            click: bt => { this.$rowUpOrDown(bt, true, this.rowsData) } },
          { text: '下移', icon: 'el-icon-bottom', enabledType: '1',
            click: bt => { this.$rowUpOrDown(bt, false, this.rowsData) } }
        ],
        rowCheckedCallback(realRows, tableObj, basePageObj) {
          // 额外控制上移下移按钮的可用性
          if (realRows && realRows.length === 1) {
            var rowId = this.$getRowId(realRows[0])
            var isFirstRow = false
            var isLastRow = false
            for (let i = 0; i < this.rowsData.length; i++) {
              if (this.$getRowId(this.rowsData[i]) === rowId) {
                isFirstRow = (i === 0)
                isLastRow = (i === this.rowsData.length - 1)
              }
            }
            basePageObj.setBtProperty('上移', 'disabled', isFirstRow)
            basePageObj.setBtProperty('下移', 'disabled', isLastRow)
          }
        },
        reloadTableCallback: result => {
          this.$refs.baseCurdRowForm.getTable().clearSelection()

          var headers = result.data.columns
          this.rowsData = result.data.rows
          this.cloneRow = {}
          this.headerIndexes = {}
          for (let i = 0; i < headers.length; i++) {
            var hd = headers[i]
            this.cloneRow[hd.label] = ''
            this.headerIndexes[hd.label] = i + 1 // 第一列是checkbox，需+1
          }

          // 处理按钮栏显示隐藏
          setTimeout(() => {
            var $btBar = $('#dynamicDlg-global .globalDialogContent .common-page .column-top')
            if (this.isEdit) {
              this.$refs.baseCurdRowForm.setButtonBarVisible(true, false)
              $btBar.show()
              $('#dynamicDlg-global').removeClass('dynamicDlgHasBaseListNoButton')
            } else {
              this.$refs.baseCurdRowForm.setButtonBarVisible(false, true)
              $btBar.hide()
              $('#dynamicDlg-global').addClass('dynamicDlgHasBaseListNoButton')
            }
          }, 300)
        },
        btAddClick: { click: bt => {
          var newRow = this.$clone(this.cloneRow)
          newRow.id = new Date().getTime()
          this.rowsData.push(newRow)
        } },
        btDeleteClick: { click: bt => { // 删除时需要从后往前处理
          var rowIds = bt.getRowIds()
          for (var i = this.rowsData.length - 1; i >= 0; i--) {
            if (rowIds.indexOf(this.rowsData[i].id) > -1) {
              this.rowsData.splice(i, 1)
            }
          }
        } }
      })

      $('#dynamicDlg-global').addClass('dynamicDlgHasBaseList')
    },
    saveRowFrom(data, funcCloseDlg, funcResetLoading) {
      if (this.isEdit) {
        this.markErrors(undefined, true)

        // 构造RowFormVo
        var rowFormVo = {
          bizzId: this.bizzId,
          treeNodeLabel: this.treeNodeLabel,
          rows: this.rowsData
        }

        if (this.callbackBeforeSave) {
          this.callbackBeforeSave(rowFormVo)
        }

        this.$callApi(
          'saveRowFrom', rowFormVo,
          () => {
            if (this.callbackSaved) {
              this.callbackSaved(rowFormVo)
            }
            funcCloseDlg()
            return true
          }, result => {
            funcResetLoading()
            if (this.$isNotEmpty(result.attributes)) {
              this.markErrors(result)
              return true
            }
          })
      } else {
        funcCloseDlg()
      }
    },
    onDlgClose(params) {
      if (this.isDetailDlg) {
        // 表单详情模式打开公示弹框，弹框与表单详情弹框都是全局弹框
        // 所以详情弹框会被替换公示弹框，所以在公示弹框关闭后需要重新
        // 打开表单详情弹框
        this.$showDetail(this.bizzId, this.formType)
      }
    },
    markErrors(result, isResetError) {
      // this.addErrorMessageSpans()

      if (isResetError) {
        // $('.rowFormError').text('')
        this.$refs.baseCurdRowForm.showBlistError(null, null, null, true)
      } else {
        var keys = Object.keys(result.attributes)
        if (this.$isNotEmpty(keys)) {
          // var errorMap = {}
          for (let i = 0; i < keys.length; i++) {
            var tokens = keys[i].split(':')
            this.$refs.baseCurdRowForm.showBlistError(tokens[0], tokens[1], result.attributes[keys[i]])

            // var rowIndex = parseInt(tokens[0])
            // var columnIndex = this.headerIndexes[tokens[1]]
            // errorMap['row' + rowIndex + '_col' + columnIndex] = result.attributes[keys[i]]
          }

          // var errorCount = 0
          // var this_ = this
          // var $rows = $('#dynamicDlg-global.dynamicDlgHasBaseList').find('tr.vxe-body--row')
          // $.each($rows, function(rIndex) {
          //   var $tds = $(this).find('td')
          //   $.each($tds, function(cIndex) {
          //     var errorKey = 'row' + rIndex + '_col' + cIndex
          //     if (this_.$isNotEmpty(errorMap[errorKey])) {
          //       $(this).find('.rowFormError').text(errorMap[errorKey])
          //       errorCount++
          //     }
          //   })
          // })

          // if (errorCount > 0) {
          //   this.$message.error('检查发现 ' + errorCount + ' 个错误')
          // }
        }
      }
    },
    addErrorMessageSpans() {
      if (this.isEdit) {
        var $spanContainers = $('#dynamicDlg-global.dynamicDlgHasBaseList')
          .find('div.tableEditInput')
        $.each($spanContainers, function() {
          var $span = $(this).find('span.rowFormError')
          if ($span.length === 0) {
            $(this).append('<span class="rowFormError"/>')
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
.rowFormError {
  color: #F56C6C;
  position: absolute;
  top: 2px;
  right: 3px;
  font-size: 12px;
  height: 12px;
  line-height: 12px;}
.tableEditInputNumber .rowFormError { left: 3px; right: unset; }
</style>

<style lang="scss" scoped>
.row-form {
  padding-top: 30px;
}
</style>
