<template>
  <el-dialog
    append-to-body
    :title="'模板权限配置'"
    :visible.sync="isdialog"
    width="720px"
    :close-on-click-modal='false'
    @close="handleClose">
    <div style="height: 100%">
      <el-row>
        <el-col :span="12">
          <span>模板列表</span>
        </el-col>
        <el-col :span="12">
          <span style="margin-left: 1px">用户列表</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div style="float: left;border: #DDDDDD solid 1px;height: 500px;padding: 5px; width: 310px;">
            <sup-tree :setting="setting"
                      ref="supTree"
                      :nodes="treeData"
                      :is-popover="false"
                      :edit-enable="true"
                      :autoDeleteChildren="false">

            </sup-tree>
          </div>
        </el-col>
        <el-col :span="12">
          <div style="float: right;border: #DDDDDD solid 1px;height: 500px;padding: 5px; width: 330px;">
            <sup-tree :setting="userSetting"
                      ref="userSupTree"
                      :nodes="userData"
                      :is-popover="false"
                      :edit-enable="true">

            </sup-tree>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <el-button class="btn-normal" @click="handleClose()" v-if="!contracdetails"> 取消</el-button>
      <el-button
        class="btn-normal"
        type="primary"
        @click="handleSumbit()">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'rowFormTemplatePermissions',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.contracdetails = bool
    }
  },
  data() {
    return {
      isdialog: this.dialog,
      contracdetails: this.isDetails,
      templateId: '',
      formType: '',
      userDatas: [],
      treeData: [],
      templateKey: '预算模板',
      setting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'label'
          }
        },
        view: {
          selectedMulti: false, // 按住ctrl是否可以多选
          showIcon: true,
          showLine: true,
          fontCss: function(treeId, treeNode) {
            return (treeNode.searchNode) ? { 'color': '#A60000', 'font-weight': 'bold' } : ''
          }
        },
        callback: {
          onClick: this.formClick,
          onCheck: this.formCheckChange
        }
      },
      userData: [],
      deptData: [],
      userSetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'userCode',
            pIdKey: 'parentCode'
          },
          key: {
            name: 'userName',
            id: 'bizId'
          }
        },
        view: {
          showIcon: true,
          showLine: true
        },
        callback: {
          onCheck: this.userCheckChange
        }
      },
      nodeId: '',
      nodeName: '',
      metaId: '',
      metaVersionId: '',
      WfUserDeptVo: {},
      userDept: [],
      currUserCode: '', // 当前选中用户编码
      currUserName: '', // 当前选中用户名称
      rowFormTemplatePermissions: [],
      templateName: '',
      rowFormTemplatePermissionsVo: {}
    }
  },
  mounted() {
    this.selectClassifyList()
    this.getAllUser()
  },
  methods: {
    show() {
      this.isdialog = true
    },
    handleClose() {
      this.rowFormTemplatePermissions = []
      this.selectClassifyList()
      this.getAllUser()
      this.isdialog = false
    },
    handleSumbit() {
      var rowFormTemplatePermissionsVo = {}
      rowFormTemplatePermissionsVo.rowFormTemplatePermissionsVo = this.rowFormTemplatePermissionsVo
      this.$callApi('saveUserTemplatePermissions',
        rowFormTemplatePermissionsVo, result => {
          this.isdialog = false
        })
    },
    // 用户选择发生改变事件
    userCheckChange(event, treeId, treeNode) {
      if (treeNode.checked) {
        if (Array.isArray(treeNode.children) && treeNode.children.length) {
          treeNode.children.forEach(item => {
            this.userDatas.push({ id: item.id, userCode: item.userCode })
          })
        } else {
          this.userDatas.push({ id: treeNode.id, userCode: treeNode.userCode })
        }
        if (this.$isNotEmpty(this.templateId)) {
          var rowFormTemplatePermissionsEntity = {}
          rowFormTemplatePermissionsEntity.templateId = this.templateId
          rowFormTemplatePermissionsEntity.templateName = this.templateName
          rowFormTemplatePermissionsEntity.userId = treeNode.id
          rowFormTemplatePermissionsEntity.userCode = treeNode.userCode
          this.rowFormTemplatePermissions.push(rowFormTemplatePermissionsEntity)

          this.rowFormTemplatePermissions = this.rowFormTemplatePermissions.filter(
            (obj, index) =>
              this.rowFormTemplatePermissions.findIndex(
                (item) => item.templateId === obj.templateId && item.userId === obj.userId && item.userCode === obj.userCode
              ) === index
          )
        }
        this.deleteRowFormTemplatePermissions(this.rowFormTemplatePermissions)
      } else {
        var deleteIndex
        var deleteId
        var userId
        if (Array.isArray(treeNode.children) && treeNode.children.length) {
          this.userDatas = this.userDatas.filter(item => {
            const obj = treeNode.children.find(child => item.id === child.id)
            if (!obj) return item
          })

          for (let i = 0; i < this.rowFormTemplatePermissions.length; i++) {
            for (let j = 0; j < treeNode.children.length; j++) {
              if (this.rowFormTemplatePermissions[i].userId === treeNode.children[j].id &&
                this.rowFormTemplatePermissions[i].userCode === treeNode.children[j].userCode &&
                this.rowFormTemplatePermissions[i].templateId === this.templateId) {
                deleteIndex = i
                deleteId = this.rowFormTemplatePermissions[i].templateId
                userId = this.rowFormTemplatePermissions[i].userId
              }
            }
            this.deleteRowFormTemplatePermissions(this.rowFormTemplatePermissions, deleteIndex, deleteId, userId)
          }
        } else {
          this.userDatas = this.userDatas.filter(item => treeNode.id !== item.id)

          for (let i = 0; i < this.rowFormTemplatePermissions.length; i++) {
            if (this.rowFormTemplatePermissions[i].userId === treeNode.id &&
              this.rowFormTemplatePermissions[i].userCode === treeNode.userCode &&
              this.rowFormTemplatePermissions[i].templateId === this.templateId) {
              deleteIndex = i
              deleteId = this.rowFormTemplatePermissions[i].templateId
              userId = this.rowFormTemplatePermissions[i].userId
            }
          }
          this.deleteRowFormTemplatePermissions(this.rowFormTemplatePermissions, deleteIndex, deleteId, userId)
        }
      }
    },
    deleteRowFormTemplatePermissions(rowFormTemplatePermissions, deleteIndex, deleteId, userId) {
      const keyObs = {}
      rowFormTemplatePermissions.forEach(item => {
        if (!this.$isNotEmpty(keyObs[item.templateId])) {
          keyObs[item.templateId] = []
        }
        keyObs[item.templateId].push(item)
      })

      if (this.$isNotEmpty(deleteIndex)) {
        const keys = Object.keys(keyObs)
        keys.forEach((key) => {
          if (deleteId === key) {
            keyObs[key] = keyObs[key].filter(item => item.userId !== userId)
          }
        })

        let newArr = []
        Object.values(keyObs).forEach(item => {
          newArr = [...newArr, ...item]
        })
        this.rowFormTemplatePermissions = newArr
      }
      this.rowFormTemplatePermissionsVo = keyObs
    },
    // 表单点击发生改变事件
    formClick(event, treeId, treeNode) {
      this.templateId = treeNode.id
      this.templateName = treeNode.label
      this.getTemplatemUser(this.templateId)
    },
    selectUser(templateId, rowFormTemplatePermissions, addUserCode) {
      const userCode = []
      for (let i = 0; i < rowFormTemplatePermissions.length; i++) {
        if (rowFormTemplatePermissions[i].templateId === templateId) {
          userCode.push(rowFormTemplatePermissions[i].userCode)
        }
      }
      this.$refs.userSupTree.treeObj.checkAllNodes(false)
      var newUserCode = userCode.concat(addUserCode)
      if (this.$isNotEmpty(newUserCode)) {
        for (var i = 0; i < newUserCode.length; i++) {
          const node = this.$refs.userSupTree.treeObj.getNodeByParam('userCode', newUserCode[i], null)
          this.$refs.userSupTree.treeObj.checkNode(node, true)
          var rowFormTemplatePermissionsEntity = {}
          rowFormTemplatePermissionsEntity.templateId = templateId
          rowFormTemplatePermissionsEntity.templateName = node.userName
          rowFormTemplatePermissionsEntity.userId = node.id
          rowFormTemplatePermissionsEntity.userCode = node.userCode
          this.rowFormTemplatePermissions.push(rowFormTemplatePermissionsEntity)

          this.rowFormTemplatePermissions = this.rowFormTemplatePermissions.filter(
            (obj, index) =>
              this.rowFormTemplatePermissions.findIndex(
                (item) => item.templateId === obj.templateId && item.userId === obj.userId && item.userCode === obj.userCode
              ) === index
          )
        }
      }
    },
    getTemplatemUser(templateId) {
      this.$callApiParams('selectTemplatemUser',
        { templateId: templateId }, result => {
          if (result.success) {
            const userCode = result.data
            if (this.$isNotEmpty(userCode)) {
              this.$refs.userSupTree.treeObj.checkAllNodes(false)
              for (var i = 0; i < userCode.length; i++) {
                const node = this.$refs.userSupTree.treeObj.getNodeByParam('userCode', userCode[i], null)
                this.$refs.userSupTree.treeObj.checkNode(node, true)
              }
            }
            this.selectUser(this.templateId, this.rowFormTemplatePermissions, userCode)
          }
          return true
        })
    },
    isTempateNode(treeNode) {
      return (treeNode &&
        treeNode.exData &&
        treeNode.exData.isTemplate)
    },
    selectClassifyList() {
      this.$callApiParams('selectRowTemplateCommonData',
        { templateKey: this.templateKey }, result => {
          this.treeData = result.attributes.treeData
          this.treeData.forEach(node => {
            if (this.isTempateNode(node)) {
              // 数据模板节点显示为树的叶子节点
              delete node.children
            }
          })
          return true
        })
    },
    getAllUser() {
      this.$callApiParams('getAllUser', {},
        result => {
          this.userData = result.data
          return true
        })
    }
  }
}
</script>

<style scoped>

</style>
