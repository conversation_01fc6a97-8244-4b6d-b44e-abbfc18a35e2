<template>
  <div style="height: 100%" id="row-form-template-setting">
    <page ref="page">
      <template #pageContent>
        <LayoutTem isDbCol :isPageShow="false" :isFilterShow="false">
          <template #dbColLeft>
            <div style="height: 100%;">
              <b-page ref="basePageLeftTree">
                <template #mainContent>
                  <sup-tree :setting="setting"
                            ref="supTree"
                            :nodes="treeData"
                            :is-popover="false"
                            :edit-enable="true"
                            :autoDeleteChildren="false"/>
                </template>
              </b-page>
            </div>
          </template>
          <template #main>
            <div class="rowTemplateMainContent">
              <b-page ref="basePageRowTemplateMainContent">
                <template #mainContent>
                  <row-form-template-save ref="rowFormTemplateSave"/>
                </template>
              </b-page>
            </div>
          </template>
        </LayoutTem>
      </template>
    </page>
    <row-form-template-permissions ref="rowFormTemplatePermissions"
                                   :dialog.sync="isDialog" :isDetails='isDetails'/>
    <row-form-setting ref="rowFormSetting"/>
    <row-form-template-dlg ref="rowFormTemplateDlg"/>
  </div>
</template>

<script>
import RowFormTemplateDlg from './row-form-template-dlg'
import RowFormTemplateSave from './row-form-template-save'
import RowFormSetting from './row-form-setting'
import RowFormTemplatePermissionsList from '../cform/form-permissions-list'
export default {
  name: 'row-form-template-setting',
  components: { RowFormTemplatePermissionsList, RowFormSetting, RowFormTemplateSave, RowFormTemplateDlg },
  props: {
    templateKey: { type: String, default: '' },
    treeExButtons: { type: Array, default: undefined }
  },
  data() {
    return {
      treeData: undefined,
      templateTypes: undefined,
      currentTreeNode: undefined,
      mainExButtons: [], // 主内容区域额外的按钮
      setting: {
        callback: {
          onClick: this.treeNodeClick
        }
      },
      isDialog: false,
      isDetails: false
    }
  },
  mounted() {
    this.$nextTick(() => { this.init() })
  },
  methods: {
    init() {
      if (!this.treeData) {
        var buttons = [
          { text: '新增模板', enabledType: '1',
            click: (bt) => { this.showSaveTemplateDlg() } },
          { text: '修改', enabledType: '1',
            click: (bt) => { this.showSaveTemplateDlg() } },
          { text: '删除', enabledType: '1',
            click: (bt) => { this.deleteTemplate() } }
        ]
        if (this.$isNotEmpty(this.treeExButtons)) {
          this.treeExButtons.forEach(bt => { buttons.push(bt) })
        }
        this.$refs.basePageLeftTree.init({
          buttons: buttons
        })

        var buttonsMainContent = this.buttonsMainContent()
        this.mainExButtons.forEach(bt => { buttonsMainContent.push(bt) })
        this.$refs.basePageRowTemplateMainContent.init({ buttons: buttonsMainContent })

        this.$nextTick(() => {
          this.$refs.page.commonPageClassEx =
            this.$refs.page.commonPageClassEx + ' column-top-hide'
          this.$refs.basePageLeftTree.setButtonNormalNoPaddingTop(true)
          this.$refs.basePageRowTemplateMainContent.setButtonNormalNoPaddingTop(true)
        })

        this.initTree()
      }
      this.showEmptyTemplate()
    },
    showEmptyTemplate() { // 初始化灰色主设计区域
      this.$nextTick(() => { // 初始化主区域
        this.treeNodeClick(undefined, undefined, undefined)
      })
    },
    initTree(selectThisNodeIdAfterInit) {
      this.$callApiParams('selectRowTemplateCommonData',
        { 'templateKey': this.templateKey },
        result => {
          this.templateTypes = result.attributes.templateTypes
          this.treeData = result.attributes.treeData
          this.treeData.forEach(node => {
            if (this.isTempateNode(node)) {
              // 数据模板节点显示为树的叶子节点
              delete node.children
            }
          })

          if (this.$isNotEmpty(selectThisNodeIdAfterInit)) {
            setTimeout(() => { // 高亮选中这个树节点
              var treeNode = this.$refs.supTree.selectAndCheckNode(selectThisNodeIdAfterInit)
              if (treeNode) {
                this.treeNodeClick(undefined, undefined, treeNode)
              }
            }, 1000)
          }
          return true
        })
    },
    treeNodeClick(event, treeId, treeNode) {
      this.$nextTick(() => {
        this.currentTreeNode = treeNode
        var rowsTree = (treeNode &&
          treeNode.exData &&
          treeNode.exData.canAddTemplateNode) ? ['1'] : []
        this.$refs.basePageLeftTree.rowChecked(rowsTree, false, this)

        var template = this.getTemplate()
        // var isUpdateDeleteBtDisabled = this.$isEmpty(template.id)
        // this.$refs.basePageLeftTree.setBtProperty('修改', 'disabled', isUpdateDeleteBtDisabled)
        // this.$refs.basePageLeftTree.setBtProperty('删除', 'disabled', isUpdateDeleteBtDisabled)
        this.showRowFormTemplateSave(template)
        var buttonsMainContent = this.buttonsMainContent()
        if (template.templateType === '1级项目预算填报模板') {
          buttonsMainContent.push(
            { text: '同步项目', icon: 'el-icon-refresh', enabledType: '1', click: (bt) => { this.synPmTemplate() } })
        }
        this.$refs.basePageRowTemplateMainContent.init({ buttons: buttonsMainContent })
        var rowsMain = this.isTempateNode(treeNode) ? ['1'] : []
        this.$refs.basePageRowTemplateMainContent.rowChecked(rowsMain, false)
      })
    },
    rowCheckedCallback(row) {
      var template = this.getTemplate()
      var isUpdateDeleteBtDisabled = this.$isEmpty(template.id)
      this.$refs.basePageLeftTree.setBtProperty('修改', 'disabled', isUpdateDeleteBtDisabled)
      this.$refs.basePageLeftTree.setBtProperty('删除', 'disabled', isUpdateDeleteBtDisabled)
    },
    showRowFormTemplateSave(template, isEditingTemplate) {
      isEditingTemplate = isEditingTemplate || false
      this.$refs.rowFormTemplateSave.showContent({
        isEdit: true, // 行表单本身处于编辑状态
        template: template,
        isDesigningTemplate: true,
        isEditingTemplate: isEditingTemplate, // 模板设计器初始化为非编辑状态
        btBackText: '退出模板编辑',
        btSaveText: '保存模板数据',
        btEditSave: this.saveTemplateData,
        isShowingTemplateContentWhenDesign: '占位符',
        btEditBack: () => { this.setEditTemplateStatus(false) },
        setEditTemplateStatusTrue: () => { this.setEditTemplateStatus(true) }
      })
    },
    setEditTemplateStatus(isEditingTemplate) { // 设置模板是否是编辑数据的状态
      this.showRowFormTemplateSave(this.getTemplate(), isEditingTemplate)
    },
    isTempateNode(treeNode) {
      return (treeNode &&
        treeNode.exData &&
        treeNode.exData.isTemplate)
    },
    getTemplate() {
      var dataType = this.currentTreeNode ? this.currentTreeNode.id : '占位符'
      var dataTypeName = this.currentTreeNode ? this.currentTreeNode.label : '占位符'
      return this.isTempateNode(this.currentTreeNode)
        ? this.currentTreeNode.exData.templateEntity
        : {
          dataType: dataType,
          dataTypeName: dataTypeName,
          templateType: '',
          name: '',
          formatType: '固定行数',
          templateKey: this.templateKey,
          auditKey: '',
          assembleKey: ''
        }
    },
    showSaveTemplateDlg() {
      this.$refs.rowFormTemplateDlg.show({
        template: this.getTemplate(),
        templateTypes: this.templateTypes,
        refreshTreeData: (selectThisNodeIdAfterInit) => {
          this.initTree(selectThisNodeIdAfterInit)
        }
      })
    },
    deleteTemplate() {
      var template = this.getTemplate()
      var ids = [template.id]
      var message = '确定要删除模板 [' + template.name + '] 吗?'
      this.$callApiParamsConfirm(
        message, undefined,
        'deleteRowFormTemplate', { ids: ids.join(',') },
        result => {
          this.initTree()
          this.showEmptyTemplate()
        })
    },
    saveTemplateData(e, dataVo) {
      var template = this.getTemplate()
      if (dataVo &&
        dataVo.extData &&
        dataVo.extData.tabRowFormVoMap &&
        dataVo.extData.tabRowFormVoMap[template.id]) {
        var rowFormVo = dataVo.extData.tabRowFormVoMap[template.id]
        rowFormVo.bizzId = template.id // 模板数据的bizzId就是模板的id
        this.$callApi('saveRowFrom', rowFormVo, result => {
          this.showRowFormTemplateSave(template)
        }, undefined, {
          getExParamsCallApiSave: () => {
            return `&treeNodeLabelUseAsDataType=占位符`
          }
        })
      }
    },
    showFormTabRowEditSetting() {
      this.$refs.rowFormSetting.show({
        formId: this.getTemplate().id,
        subTitle: this.getTemplate().name,
        refreshAfterSaved: () => {
          this.showRowFormTemplateSave(this.getTemplate())
        }
      })
    },
    showRowFormTemplatePermissions() {
      this.$refs.rowFormTemplatePermissions.show()
    },
    buttonsMainContent() {
      var buttonsMainContent = [
        { text: '设置模板格式', icon: 'el-icon-tickets', enabledType: '1', click: (bt) => {
          this.showFormTabRowEditSetting()
        } },
        { text: '编辑模板数据', icon: 'el-icon-edit', enabledType: '1', click: (bt) => {
          this.$refs.rowFormTemplateSave.setEditTemplateStatus(true)
        } },
        { text: '模板权限配置', icon: 'el-icon-setting', enabledType: '0', click: (bt) => {
          this.showRowFormTemplatePermissions()
        } }
      ]
      return buttonsMainContent
    },
    synPmTemplate() {
      this.$message.success('开始同步项目')
      var template = this.getTemplate()
      this.$callApi('synPmTemplate', template, result => {
        var templateId = result.data
        this.initTree(templateId)
      })
    }
  }
}
</script>

<style>
.rowTemplateMainContent {width: 100%;height: 100%;border: 1px solid #DDDDDD;padding: 10px;}
</style>

