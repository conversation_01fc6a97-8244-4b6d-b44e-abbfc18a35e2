<template>
  <div class="rowTemplateSave">
    <div :style="`display: flex;flex-direction:column;height: ${contentHeight};`">
      <div style="flex: 1;">
        <form-tab-save-列表编辑 ref="formTabSaveRowEdit"/>
      </div>
      <div style="height: 40px;line-height: 40px;width:100%;" v-show="!isDetailOrAudit">
        <div style="float: left;">
          <div style="height: 20px; width: 100px;"/>
          <div :style="`font-size: 14px;height: 20px; line-height: 20px;${statusTextStyle}`">{{statusText}}</div>
        </div>
        <div style="float: right;">
          <el-button size="medium"  icon="el-icon-d-arrow-left"
                     @click="doBtEditBack" :disabled="!isBtEnabledBack">{{btBackText}}</el-button>
          <el-button ref="btDraft" size="medium"  icon="el-icon-tickets"
                     @click="doBtEditDraft" :disabled="!isBtEnabledDraft">存草稿</el-button>
          <el-button size="medium"  type="primary" icon="el-icon-edit"
                     @click="doBtEditSave" :disabled="!isBtEnabledSave">{{btSaveText}}</el-button>
        </div>
      </div>
      <div :style="`font-size: 14px;height: 14px; line-height: 14px;${statusTextStyle}`"
        v-show="isDetailOrAudit">{{statusText}}</div>
    </div>
    <component ref="rowTemplateSaveExtend" :is="rowTemplateSaveExtendHandler"/>
  </div>
</template>

<script>

import FormTabSaveRowEdit from '../cform/tab/form-tab-save-row-edit'
export default {
  name: 'row-form-template-save',
  components: { FormTabSaveRowEdit },
  data() {
    return {
      isEdit: true, // 是否处于编辑状态
      statusText: '',
      statusTextStyle: '',
      btBackText: '',
      btSaveText: '',
      btEditBack: undefined,
      btEditDraft: undefined,
      btEditSave: undefined,

      // undefined=表明是运行时，true=设计时正在编辑模板，false = 设计时非编辑
      isEditingTemplate: undefined,
      isDetailOrAudit: false, // 当前是否是详情弹框
      rowTemplateSaveExtendHandler: '',
      params: undefined
    }
  },
  computed: {
    contentHeight() {
      return this.isDetailOrAudit ? ' calc(100% - 5px)' : '100%'
    },
    isRuntime() {
      return (this.isEditingTemplate === undefined)
    },
    isBtEnabledBack() {
      return this.isBtEnabled(this.btEditBack)
    },
    isBtEnabledDraft() {
      return (this.btEditDraft !== undefined)
    },
    isBtEnabledSave() {
      if (this.isBtEnabled(this.btEditSave)) {
        // 当前模板id不为空时，保存按钮才可用
        if (this.params &&
          this.params.template &&
          this.$isNotEmpty(this.params.template.id)) {
          return true
        }
      }
      return false
    }
  },
  methods: {
    init(dlg, params) { // 详情弹框时，初始化调用这个方法
      this.isDetailOrAudit = true
      this.isEdit = false
    },
    showContent(params) {
      this.params = params || {}
      if (this.params.extendKey) {
        this.rowTemplateSaveExtendHandler = `row-form-template-save-${this.params.extendKey}`
      }
      this.$nextTick(() => {
        if (this.$refs.rowTemplateSaveExtendHandler &&
              this.$refs.rowTemplateSaveExtendHandler.exHandleParams) {
          this.$refs.rowTemplateSaveExtendHandler.exHandleParams(this, this.params)
        }

        var templateId = this.params.template.id
        var isEmptyTemplateId = this.$isEmpty(templateId)
        this.params.dataVo = {
          extData: {
            tabRowFormPageDataMap: {}
          }
        }
        this.params.formId = '占位符'
        this.params.editableAny = this.params.isEditingTemplate
        this.params.getDataToSave = () => { return this.params.dataVo }
        this.params.getDataVoDataAll = () => { return this.params.dataVo }
        this.params.getFormExtend = () => { return this.$refs.rowTemplateSaveExtendHandler }
        this.params.setStatusText = (text, style) => {
          this.statusText = text
          this.statusTextStyle = style
        }
        this.isEditingTemplate = this.params.isEditingTemplate

        this.btBackText = '返回'
        if (this.$isNotEmpty(this.params.btBackText)) {
          this.btBackText = this.params.btBackText
        }
        this.btSaveText = '保存'
        if (this.$isNotEmpty(this.params.btSaveText)) {
          this.btSaveText = this.params.btSaveText
        }

        this.btEditBack = this.params.btEditBack
        this.btEditDraft = this.params.btEditDraft
        this.btEditSave = this.params.btEditSave

        var showTemplateContent = (pageData) => {
          this.params.dataVo.extData.tabRowFormPageDataMap[templateId] = pageData
          this.$refs.formTabSaveRowEdit.isNoData = undefined // 触发强制重新加载
          this.$refs.formTabSaveRowEdit.showTabContentIfTemplate(this.params)
        }
        if (isEmptyTemplateId) {
          showTemplateContent()
        } else {
          var queryParams = {
            isEdit: this.params.isEdit,
            treeNodeLabelUseAsDataType: '占位符',
            bizzId: this.params.template.id,
            treeNodeLabel: this.params.template.id }
          if (this.$isNotEmpty(params.isShowingTemplateContentWhenDesign)) {
            queryParams.isShowingTemplateContentWhenDesign = '占位符'
          }

          this.$callApiParams('selectRowFromPageData',
            queryParams, result => {
              showTemplateContent(result.data)
              return true
            })
        }
      })
    },
    showError(result) {
      if (this.$refs.formTabSaveRowEdit.showError) {
        this.$refs.formTabSaveRowEdit.showError(result)
      }
      return true
    },
    setEditTemplateStatus(isEditingTemplate) {
      // 设计时设置是否是编辑状态，此时不管是否自由行数都显示按钮栏
      if (this.params.setEditTemplateStatusTrue) {
        this.params.setEditTemplateStatusTrue()
      }
    },
    isBtEnabled(btHandler) {
      if (btHandler !== undefined) {
        // 是运行时，或者设计时已进入编辑状态
        if (this.isRuntime || this.isEditingTemplate === true) {
          return true
        }
      }
      return false
    },
    doBtEditBack() {
      this.$confirm('确认返回吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.btEditBack) {
          this.btEditBack()
        }
      }).catch(() => {

      })
    },
    doBtEditDraft() {
      if (this.btEditDraft) {
        this.btEditDraft()
      }
    },
    doBtEditSave(e) {
      if (this.btEditSave) {
        this.$refs.formTabSaveRowEdit.fillDataVoBeforeSave(this.params.dataVo)
        this.btEditSave(e, this.params.dataVo)
      }
    }
  }
}
</script>

<style lang="scss">
  .rowTemplateSave {width: 100%;height: 100%; }
  .rowTemplateSave .el-tabs__header { margin-bottom: 3px; }
</style>
