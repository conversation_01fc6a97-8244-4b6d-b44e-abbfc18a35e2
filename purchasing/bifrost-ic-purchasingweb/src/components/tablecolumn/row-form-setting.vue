<template>
  <el-dialog ref="formTabRowEditDlg"
       :title='dlgTitle'
       append-to-body
       class="formTabRowEditDlg"
       :close-on-click-modal="false"
       :visible.sync="formTabRowEditDlgVisible">
    <page>
      <template #pageContent>
        <div style="height: calc(100% - 40px); width: 100%;display: flex;overflow: hidden">
          <div style="height: 100%;flex: 1;overflow: hidden">
            <b-curd ref="baseCurdCols"/>
          </div>
          <div :class="`colPropertyBlock ${colPropertyBlockExStyle}`">
            <el-form label-width="70px" size="mini">
              <div style="display: flex;">
                <div style="flex: 1;">
                  <el-form-item label="表格列名">
                    <el-input v-model="colItem.label" :disabled="!isColPropertyContentEnabled"/>
                  </el-form-item>
                </div>
                <div style="flex: 1;" :title="level3ColsTip">
                  <el-form-item label="数据字段">
                    <el-input v-model="colItem.prop" :disabled="!isColPropertyContentEnabled"/>
                  </el-form-item>
                </div>
              </div>
              <div style="display: flex;">
                <div style="flex: 1;">
                  <el-form-item label="数据类型">
                    <el-select v-model="colItem.colType"
                               @change="colTypeChanged(colItem)" :disabled="!isColPropertyContentEnabled">
                      <el-option
                        v-for="(item, index) in colTypes"
                        :key="index"
                        :label="item"
                        :value="item"/>
                    </el-select>
                  </el-form-item>
                </div>
                <div style="flex: 1;">
                  <el-form-item :label="dataRefText">
                    <el-input v-if="!(colItem.colType === '下拉框' || colItem.colType === '弹框')" v-model="colItem.dataRef"
                              :disabled="!isColPropertyContentEnabled
                              || colItem.colType === '文本' || colItem.colType === '日期'"
                              :placeholder="colItem.colType==='单选'||colItem.colType==='多选'?'选项+逗号隔开如A,B':''"/>
                    <el-select v-if="colItem.colType === '下拉框'"
                               filterable
                               v-model="colItem.dataRef" :disabled="!isColPropertyContentEnabled">
                      <el-option
                        v-for="(item, index) in dataRefDrop"
                        :key="index"
                        :label="item"
                        :value="item"/>
                    </el-select>
                    <el-select v-if="colItem.colType === '弹框'"
                               filterable
                               v-model="colItem.dataRef" :disabled="!isColPropertyContentEnabled">
                      <el-option
                        v-for="(item, index) in dataRef"
                        :key="index"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div style="display: flex;">
                <div style="flex: 1;">
                  <el-form-item label="列宽度">
                    <el-input v-model="colItem.width" :disabled="!isColPropertyContentEnabled"
                              onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
                  </el-form-item>
                </div>
                <div style="flex: 1;">
                  <el-form-item label="默认值">
                    <el-input v-model="colItem.defaultValue" :disabled="!isColPropertyContentEnabled"/>
                  </el-form-item>
                </div>
              </div>
              <div style="display: flex;">
                <div style="flex: 1;">
                  <el-form-item label="对齐方式">
                    <el-select v-model="colItem.align" :disabled="!isColPropertyContentEnabled">
                      <el-option key="left" value="left" label="靠左对齐"/>
                      <el-option key="right" value="right" label="靠右对齐"/>
                      <el-option key="center" value="center" label="居中对齐"/>
                    </el-select>
                  </el-form-item>
                </div>
                <div style="flex: 1;">
                  <el-form-item label="读写类别">
                    <el-select v-model="colItem.modifyType" :disabled="!isColPropertyContentEnabled">
                      <el-option
                        v-for="(item, index) in modifyTypes"
                        :key="index"
                        :label="item"
                        :value="item"/>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div style="display: flex;">
                <div style="flex: 1;">
                  <el-form-item :label="minText">
                    <el-input v-model="colItem.min" ref="minInput" :disabled="!isColPropertyContentEnabled
                     || colItem.colType !== '金额' && colItem.colType !== '数值' && colItem.colType !== '文本'"
                              @input="minTextFunc"/>
                  </el-form-item>
                </div>
                <div style="flex: 1;">
                  <el-form-item :label="maxText">
                    <el-input v-model="colItem.max" :disabled="!isColPropertyContentEnabled
                    || colItem.colType !== '金额' && colItem.colType !== '数值' && colItem.colType !== '文本'"
                              onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
                  </el-form-item>
                </div>
              </div>
              <div style="display: flex">
                <div style="flex: 1;">
                  <el-form-item label="父级名称">
                    <el-input v-model="colItem.parentName" :disabled="!isColPropertyContentEnabled"/>
                  </el-form-item>
                </div>
                <div style="flex: 1;"></div>
              </div>
              <el-form-item label="是否启用">
                <div style="display: flex;">
                  <div style="flex: 2;width: 65px;display: flex;">
                    <el-radio v-model="colItem.isEnabled" label="是"
                              :disabled="!isColPropertyContentEnabled">是</el-radio>
                    <el-radio v-model="colItem.isEnabled" label="否"
                              :disabled="!isColPropertyContentEnabled">否</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="是否排序">
                <div style="display: flex;">
                  <div style="flex: 2;width: 65px;display: flex;">
                    <el-radio v-model="colItem.sortable" :label="true"
                              :disabled="!isColPropertyContentEnabled">是</el-radio>
                    <el-radio v-model="colItem.sortable" :label="false"
                              :disabled="!isColPropertyContentEnabled">否</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="是否固定">
                <div style="display: flex;">
                  <div style="flex: 2;width: 65px;display: flex;">
                    <el-radio v-model="colItem.fixable" :label="true"
                              :disabled="!isColPropertyContentEnabled">是</el-radio>
                    <el-radio v-model="colItem.fixable" :label="false"
                              :disabled="!isColPropertyContentEnabled">否</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="是否导出">
                <div style="display: flex;">
                  <div style="flex: 2;width: 65px;display: flex;">
                    <el-radio v-model="colItem.exportable" :label="true"
                              :disabled="!isColPropertyContentEnabled">是</el-radio>
                    <el-radio v-model="colItem.exportable" :label="false"
                              :disabled="!isColPropertyContentEnabled">否</el-radio>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="是否必填">
                <div style="display: flex;">
                  <div style="flex: 2;width: 65px;display: flex;">
                    <el-radio v-model="colItem.isRequired" :label="true"
                              :disabled="!isColPropertyContentEnabled">是</el-radio>
                    <el-radio v-model="colItem.isRequired" :label="false"
                              :disabled="!isColPropertyContentEnabled">否</el-radio>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div style="text-align: right;padding-top: 10px;">
          <el-button class="btn-normal" @click="formTabRowEditDlgVisible = false">关闭</el-button>
          <el-button class="btn-normal" type="primary" @click="save">{{btSaveText}}</el-button>
        </div>
      </template>
    </page>
  </el-dialog>
</template>

<script>

import BCurd from '../page/base-curd'
export default {
  name: 'row-form-setting',
  components: { BCurd },
  data() {
    return {
      dlgTitle: '设置列表',
      formTabRowEditDlgVisible: false,
      formId: '',
      tabName: '',
      baseListObj: undefined,
      btSaveText: '保存',
      isColPropertyContentEnabled: false,
      colItem: this.newColItem(),
      colTypes: [],
      modifyTypes: [],
      dataRef: [], // 弹框参照类别数据
      dataRefDrop: [], // 下拉款数据
      refreshAfterSaved: undefined,
      isDataRefShow: false,
      level3Cols: []
    }
  },
  computed: {
    colPropertyBlockExStyle() {
      return this.isColPropertyContentEnabled ? '' : 'colPropertyBlockEmpty'
    },
    minText() {
      return (this.colItem.colType === '文本') ? '最小长度' : '最小值'
    },
    maxText() {
      return (this.colItem.colType === '文本') ? '最大长度' : '最大值'
    },
    dataRefText() {
      return (this.colItem.colType === '数值' || this.colItem.colType === '金额') ? '计算公式' : '参照数据'
    },
    level3ColsTip() {
      if (this.$isNotEmpty(this.level3Cols)) {
        return '可使用的三级明细列：' + this.level3Cols.join(',')
      }
      return ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$isEmpty(this.modifyTypes)) {
        this.$selectColSettingCommonData(result => {
          this.colTypes = result.attributes.colTypesMore
          this.modifyTypes = result.attributes.modifyTypes
          this.dataRef = result.attributes.dataRef
          this.dataRefDrop = result.attributes.dataRefDrop
        })
      }
    })
  },
  methods: {
    minTextFunc(e) {
      // 文本只允许输入整数 最小值允许输入负数 小数
      const REG = this.colItem.colType === '文本' ? /^\d+$/g : /^-?(\d+|\d*)(\.\d*)?$/g
      if (!REG.test(e)) {
        // 获取当前光标位置
        const cursorPos = this.$refs.minInput.$el.children[0].selectionStart
        // 如果光标位置不在最后 则替换当前光标所在位置-1 的数据
        if (cursorPos !== e.length) {
          e = e.replace(e[cursorPos - 1], '')
        } else {
          e = e.substring(0, e.length - 1)
        }
        // 去除中文
        const zhCN = /[\u4e00-\u9fa5]+/
        if (e.match(zhCN)) {
          e = e.replace(e.match(zhCN)[0], '')
        }
        this.colItem.min = e
      }
    },
    show(params) {
      this.formTabRowEditDlgVisible = true
      this.formId = params.formId
      this.tabName = params.tabName
      this.refreshAfterSaved = params.refreshAfterSaved
      this.dlgTitle = '设置格式'
      if (this.$isNotEmpty(this.tabName)) {
        this.dlgTitle += ` (${this.tabName})`
      }
      if (this.$isNotEmpty(params.subTitle)) {
        this.dlgTitle += ` (${params.subTitle})`
      }

      this.$setDlgSize(this, 'formTabRowEditDlg', 900, 700)

      this.$nextTick(() => {
        this.$refs.baseCurdCols.init({
          isEditRowForm: true,
          isEdit: false,
          params: {
            formId: this.formId,
            tabName: this.tabName,
            dataApiKey: 'selectCformTabCols',
            ascs: 'COL_ORDER'
          },
          rowCheckedCallback: this.rowChecked,
          addRowEx: () => {
            this.colItem = this.newColItem()
            this.colItem.label = this.getAddColItemLabel()
            return this.colItem
          },
          deleteRowEx: () => { this.emptyColItem() },
          reloadTableCallback: (result, table, baseListObj) => {
            this.baseListObj = baseListObj
            this.isColPropertyContentEnabled = this.$isNotEmpty(this.baseListObj.rowsData)
            this.level3Cols = result.attributes['三级明细数据列']
          }
        })
        this.$refs.baseCurdCols.setButtonNormalNoPaddingTop(true)
      })
    },
    colTypeChanged(colItem) {
      if (this.$isNotEmpty(colItem.dataRef)) {
        colItem.dataRef = ''
      }
    },
    newColItem() {
      return {
        label: '',
        width: 100,
        align: 'left',
        min: 0,
        max: 10000000,
        colType: '文本',
        modifyType: '读写',
        sortable: false,
        fixable: false,
        exportable: true,
        isRequired: false,
        isEnabled: '是'
      }
    },
    emptyColItem() {
      this.colItem = this.newColItem()
      this.colItem.label = ''
      this.colItem.width = ''
      this.colItem.align = ''
      this.colItem.min = ''
      this.colItem.max = ''
      this.colItem.colType = ''
      this.colItem.sortable = ''
      this.colItem.fixable = ''
      this.colItem.exportable = ''
      this.colItem.isRequired = ''
      this.colItem.isEnabled = ''
    },
    getAddColItemLabel() {
      var allLabels = []
      for (let i = 0; i < this.baseListObj.rowsData.length; i++) {
        allLabels.push(this.baseListObj.rowsData[i].label)
      }
      var index = 1
      var newLabel
      do {
        newLabel = '新建要素' + index
        index++
      } while (allLabels.indexOf(newLabel) >= 0)
      return newLabel
    },
    rowChecked(rows) {
      this.isColPropertyContentEnabled = true
      if (this.$isEmpty(rows) || rows.length > 1) {
        this.isColPropertyContentEnabled = false
      }
      if (this.$isNotEmpty(rows) && rows.length === 1) {
        this.colItem = rows[0]
      } else {
        this.emptyColItem()
      }
    },
    save() {
      var dataType = this.formId
      if (this.$isNotEmpty(this.tabName)) {
        dataType += `_${this.tabName}`
      }

      var columnListVo = { cols: [] }
      for (var i = 0; i < this.baseListObj.rowsData.length; i++) {
        var column = this.baseListObj.rowsData[i]
        column.id = ''
        column.bizid = ''
        column.order = i
        column.dataType = dataType
        column.labelOrigin = column.label
        if (this.$isEmpty(column.prop)) {
          column.prop = column.label
        }
        columnListVo.cols.push(column)
      }
      this.$callApi('saveTableColumn', columnListVo,
        result => {
          this.formTabRowEditDlgVisible = false
          if (this.refreshAfterSaved) {
            this.refreshAfterSaved()
          }
        }, undefined, {
          getExParamsCallApiSave: () => {
            return `&先清空相同dataType的列定义=${dataType}&不检验列的数据类型=是`
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
  .formTabRowEditDlg { }
  .formTabRowEditDlg .colPropertyBlock {
    height: calc(100% - 51px);flex: 1;margin: 52px 0px 0px 10px;
    border: 1px solid #DDDDDD !important; padding: 10px 10px 10px 5px;
  }
  .formTabRowEditDlg .colPropertyBlockEmpty { }
  .formTabRowEditDlg .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }
</style>
