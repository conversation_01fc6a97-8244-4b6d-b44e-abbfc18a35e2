<template>
  <el-dialog ref="rowFormTemplateDlg"
       :title='title'
       append-to-body
       class="common-dlg-form"
       :close-on-click-modal="false"
       :visible.sync="dlgVisible">
    <page>
      <template #pageContent>
        <div  style="height: calc(100% - 50px); width: 100%;">
          <el-form label-width="70px">
            <el-form-item label="模板名称">
              <el-input ref="templateNameInput" v-model="template.name"/>
            </el-form-item>
            <el-form-item label="模板类别">
              <el-select v-model="template.templateType">
                <el-option
                  v-for="(item, index) in templateTypes"
                  :key="index"
                  :label="item"
                  :value="item"/>
              </el-select>
            </el-form-item>
            <el-form-item label="格式类型">
              <el-select v-model="template.formatType">
                <el-option value="固定行数" label="固定行数"/>
                <el-option value="自由行数" label="自由行数"/>
              </el-select>
            </el-form-item>
            <el-form-item label="应用页面">
              <el-select v-model="applyPages" multiple placeholder="请选择">
                <el-option value="审核" label="审核"/>
                <el-option value="查询" label="查询"/>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div style="text-align: right;padding-top: 20px;">
          <el-button class="btn-normal" @click="dlgVisible = false">关闭</el-button>
          <el-button class="btn-normal" ref="btSave" type="primary" @click="save">保存</el-button>
        </div>
      </template>
    </page>
  </el-dialog>
</template>

<script>
export default{
  name: 'row-form-template-dlg',
  data() {
    return {
      templateName: '',
      template: undefined,
      templateTypes: undefined,
      dlgVisible: false,
      refreshTreeData: undefined,
      applyPages: []
    }
  },
  computed: {
    title() {
      return (this.$isEmpty(this.template) || this.$isEmpty(this.template.id))
        ? '新增模板' : `修改模板 (${this.templateName})`
    }
  },
  methods: {
    show(params) {
      params = params || {}
      this.template = params.template
      this.templateTypes = params.templateTypes
      this.applyPages = []
      this.templateName = ''
      if (this.template) {
        this.templateName = this.template.name
        if (this.template.auditKey === '是') {
          this.applyPages.push('审核')
        }
        if (this.$isNotEmpty(this.template.assembleKey)) {
          this.applyPages.push('查询')
        }
      }

      // 新增模板时默认选择第一个模板类型
      if (this.template &&
        this.$isEmpty(this.template.id) &&
        this.$isNotEmpty(this.templateTypes)) {
        this.template.templateType = this.templateTypes[0]
      }

      this.refreshTreeData = params.refreshTreeData
      this.dlgVisible = true
      this.$setDlgSize(this, 'rowFormTemplateDlg', 400, 360)
      this.$nextTick(() => {
        this.$refs.templateNameInput.focus()
      })
    },
    save() {
      this.template.auditKey = (this.applyPages.indexOf('审核') > -1) ? '是' : '否'
      this.template.assembleKey = (this.applyPages.indexOf('查询') > -1)
        ? this.template.templateKey : ''

      this.$refs.btSave.loading = true
      this.$callApi('saveRowFormTemplate', this.template,
        result => {
          if (this.refreshTreeData) {
            var templateId = result.data
            this.refreshTreeData(templateId)
          }
          this.dlgVisible = false
          this.$refs.btSave.loading = false
        }, result => { this.$refs.btSave.loading = false })
    }
  }
}
</script>

<style lang="scss">
.rowFormError {
  color: #F56C6C;position: absolute;top: 2px;right: 3px;
  font-size: 12px;height: 12px;line-height: 12px;}
.tableEditInputNumber .rowFormError { left: 3px; right: unset; }
</style>
