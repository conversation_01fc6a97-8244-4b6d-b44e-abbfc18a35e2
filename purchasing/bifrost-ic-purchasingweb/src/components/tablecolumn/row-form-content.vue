<template>
  <div :id="formTabRowFormRootId" class="rowFormContent" style="height: 100%;">
    <b-curd ref="baseCurdRowForm" :deleteNoRefreshBtn="isBasicExtend" :isVxe="false"/>
  </div>
</template>

<script>
import $ from 'jquery'
// tab常量
const { GOVEMMENT, BUDGET, SUBPROJECT } = {
  GOVEMMENT: '政府采购',
  BUDGET: '预算信息',
  SUBPROJECT: '子项目测算'
}
// 政府采购的prop常量
const { ITEMS, ENTERPRISE, GOVEMMENT_PROP, RESV_STYLE, RATIO } = {
  ITEMS: '采购品目',
  ENTERPRISE: '是否适宜由中小企业提供',
  GOVEMMENT_PROP: '政府采购',
  RESV_STYLE: '预留方式',
  RATIO: '预留中小企业比例'
}
// 预算信息的prop常量
const { DECLARE_NUM } = {
  DECLARE_NUM: '申报数'
}
const MASTER_BUDGET = '整体预留'

export default {
  name: 'row-form-content',
  props: {
    isBasicExtend: {
      default: false,
      type: Boolean
    }
  },
  data() {
    return {
      initParams: {},
      fnBgSys: false,
      params: {},
      propsLabelMap: {},
      isAutoTotal: false,
      formTabRowFormRootId: 'formTabRowFormRootId' + new Date().getTime(),
      isEdit: false,
      bizzId: '',
      treeNodeLabel: '',
      dataApiKey: '占位符',
      headers: [],
      headerIndexes: {},
      level3DetailObj: undefined,
      isCurrentTheLevel3Detail: undefined // 表明当前行表单就是3级明细列的明细数据
    }
  },
  methods: {
    init(params) {
      this.params = params || {}
      this.fnBgSys = this.params.dataVo?.extData?.['财政预算同步']
      this.isAutoTotal = params.dataVo.extData.isAutoTotal
      this.isCurrentTheLevel3Detail = params.isCurrentTheLevel3Detail
      if (params.pageData) {
        for (var i = 0; i < params.pageData.columns.length; i++) {
          if (params.pageData.columns[i].columnEntity &&
            params.pageData.columns[i].columnEntity.colType === '多选') {
            var prop = params.pageData.columns[i].prop
            for (var j = 0; j < params.pageData.rows.length; j++) {
              if (params.pageData.rows[j][prop] && !params.isNoEditButtons) {
                var value = params.pageData.rows[j][prop]
                params.pageData.rows[j][prop] = value.split(',')
              }
            }
          }
        }

        this.headers = params.pageData.columns
        this.propsLabelMap = {}
        this.resetPropsColMap(this.headers)
        this.initParams = {
          hideCurdButton: params.hideCurdButton,
          hiddenButtons: params.hiddenButtons,
          buttons: params.buttons,
          dataVo: params.dataVo,
          isEditRowForm: true,
          loadTable: false,
          isShowOrderNumber: true,
          editableAny: params.editableAny,
          isNoEditButtons: params.isNoEditButtons,
          rowCellChanged: this.rowCellChanged,
          rowCellClickCallback: this.rowCellClickCallback,
          rowCellClickBefore: params.rowCellClickBefore,
          aotuSummaryPmAmount: params.aotuSummaryPmAmount,
          rowRadioCallback: params.rowRadioCallback,
          level3DetailCellClicked: this.level3DetailCellClicked,
          afterRowCalculation: this.afterRowCalculation,
          customSelectOptionsSupplier: this.customSelectOptionsSupplier,
          addRowEx: (newRow) => { // 一个传递的是curd
            if (params.addRowEx) {
              params.addRowEx(newRow)
            }
            this.setBudgetDisabled(newRow)
            this.callbackAddRow(newRow)
            this.setGovemmentDisabled(newRow)
          },
          rowCopyEX: (newRow, baseList, copyRow) => { // 一个传递的是baseList
            if (params.rowCopyEX) {
              params.rowCopyEX(newRow)
            }
            if (params.tabName && params.tabName === '预算信息') { // 预算信息复制时 把子项目id置空
              newRow['子项目关联ID'] = ''
            }
            if (this.level3DetailObj) {
              this.level3DetailObj.parentAddRow(newRow, copyRow)
            }
          },
          deleteRowEx: (baseCurdObj, rowIdsDeleted, deleteData) => {
            if (params.deleteRowEx) {
              params.deleteRowEx(baseCurdObj, rowIdsDeleted, deleteData)
            }
            this.callbackDeleteRow(baseCurdObj, rowIdsDeleted, deleteData)
          },
          tableExStyleClass: params.tableExStyleClass,
          params: {
            dataApiKey: this.dataApiKey,
            bizzId: this.bizzId,
            isEdit: this.isEdit,
            treeNodeLabel: this.treeNodeLabel },

          reloadTableCallbackBeforeFillTable: (result, table, baseListObj) => {
            if (params.reloadTableCallbackBeforeFillTable) {
              params.reloadTableCallbackBeforeFillTable(result, table, baseListObj)
            }
          },
          reloadTableCallback: (result, table, baseListObj) => {
            if (params.reloadTableCallback) {
              params.reloadTableCallback(result, table, baseListObj)
            }
            this.initLevel3EditDetailIf(result, table, baseListObj, params)
            this.initGovemment(result.data?.rows)
            this.initBudget(result.data?.rows)
            this.initPlan(result.data?.rows)
          },
          rowCheckedCallback: rows => {
            if (this.level3DetailObj) {
              var rowId
              if (this.$isNotEmpty(rows) && rows.length === 1) {
                rowId = this.$getRowId(rows[0])
              }
              this.level3DetailObj.parentRowId = rowId
            }
          }
        }
        this.$refs.baseCurdRowForm.init(this.initParams)
        this.$refs.baseCurdRowForm.setButtonNormalNoPaddingTop(true)
        this.$nextTick(() => { this.fillTable(params.pageData) })
      }
    },
    resetPropsColMap(cols = []) {
      cols.forEach(item => {
        this.propsLabelMap[item.prop] = item
        if (item.children) {
          this.resetPropsColMap(item.children)
        }
      })
    },
    fillTable(pageData) { // 填充数据
      if (pageData) {
        this.headers = pageData.columns
        this.$refs.baseCurdRowForm.fillTable({
          attributes: {},
          data: pageData
        })
      }
    },
    disableAllBts() {
      this.$refs.baseCurdRowForm.disableAllBts()
    },
    disableAllBtsResume() {
      this.$refs.baseCurdRowForm.disableAllBtsResume()
    },
    level3DetailCellClicked(rowId, colProp) {
      if (this.level3DetailObj) {
        this.level3DetailObj.activedTab = colProp
        this.$nextTick(() => {
          // 先设置为undefined保证触发watch机制
          this.level3DetailObj.parentRowId = undefined
          this.$nextTick(() => {
            this.level3DetailObj.parentRowId = rowId
          })
        })
      }
    },
    initLevel3EditDetailIf(result, table, baseListObj, params) {
      // 如果当前已经是3级明细数据本身，不要进行下一层级的3级数据处理
      // 否则可能会出现无限循环
      if (this.isCurrentTheLevel3Detail === true) {
        return
      }

      var level3EditDetailCols = []
      var listContentSubHeight
      baseListObj.columnsData.forEach(col => {
        if (col.exData && col.exData['三级明细列'] === true) {
          level3EditDetailCols.push(col)
          listContentSubHeight = col.exData['三级明细数据列表高度']
        }
      })

      // 当前是模板时，模板自身肯定没有3级明细数据
      if (this.$isNotEmpty(level3EditDetailCols) &&
        params.isDesigningTemplate !== true) {
        baseListObj.listContentSubId = 'row-form-level3-detail'
        baseListObj.listContentSubStyle = `height:${listContentSubHeight}px !important;`
        this.$nextTick(() => {
          var subParams = {}
          subParams.originParams = params
          subParams.isEdit = this.isEdit
          subParams.rowsData = baseListObj.rowsData
          subParams.level3EditDetailCols = level3EditDetailCols

          this.$nextTick(() => {
            baseListObj.$refs.listContentSub.parentBaseListObj = baseListObj
            baseListObj.$refs.listContentSub.init(subParams)
            this.level3DetailObj = baseListObj.$refs.listContentSub
            this.$nextTick(() => {
              // 如果2级列表不为空，并且当前没有勾选的行，则自动勾选第一行
              var selections = this.$getTableSelection(baseListObj.getTable())
              if (this.$isNotEmpty(baseListObj.rowsData) &&
                  this.$isEmpty(selections)) {
                baseListObj.toggleRowSelection(baseListObj.rowsData[0], true)
              }
            })
          })
        })
      } else {
        baseListObj.listContentSubId = ''
        baseListObj.listContentSubStyle = 'display:none;'
      }
    },
    fillDataVoBeforeSave(dataVo) {
      this.clearError()
      var pageData = {
        columns: this.headers,
        rows: this.$refs.baseCurdRowForm.rowsData
      }
      this.fillDataVoBeforeSaveByPageData(dataVo, pageData, this.treeNodeLabel)

      // 填充3级明细数据
      if (this.level3DetailObj) {
        this.level3DetailObj.fillDataVoBeforeSave(
          dataVo, this.fillDataVoBeforeSaveByPageData)
      }
    },
    fillDataVoBeforeSaveByPageData(dataVo, pageData, treeNodeLabel) {
      var rowFormVo = this.makeRowFormVo(
        pageData.columns, pageData.rows, treeNodeLabel)

      // 按照规则将rowFormVo填充到dataVo，这些数据将被RowFormHandler执行保存
      var tabRowFormVoMap = dataVo.extData.tabRowFormVoMap
      if (!tabRowFormVoMap) {
        // 这里传递的“不要重新构造tabRowFormVoMap”，主要是告诉后端
        // 的WfUtil.wfDraftOrSave()方法，前端已经构造了tabRowFormVoMap，
        // 不要在后端逻辑里尝试再重新构造，因为传递到后端的tabRowFormPageDataMap
        // 是不完整的数据，重新构造反倒会产生错误数据的tabRowFormVoMap
        tabRowFormVoMap = {}
        dataVo.extData.tabRowFormVoMap = tabRowFormVoMap
        dataVo.extData.不要重新构造tabRowFormVoMap = '占位符'
      }
      tabRowFormVoMap[treeNodeLabel] = rowFormVo
    },
    makeRowFormVo(columnData, rowsData, treeNodeLabel) {
      var rowFormVo = {
        bizzId: this.bizzId,
        treeNodeLabel: treeNodeLabel,
        headers: [],
        rows: [],
        canEmptyRows: true
      }

      // 收集填写的数据
      rowsData.forEach(row => {
        var oneRow = {}
        this.$nextTick(() => {
          oneRow = this.collectColumnData(columnData, oneRow, row)
        })
        rowFormVo.rows.push(oneRow)
      })
      return rowFormVo
    },
    collectColumnData(columnData, oneRow, row) {
      columnData.forEach(header => {
        if (this.$isEmpty(header.children)) {
          var value = row[header.prop]
          if (value instanceof Array) {
            value = value.join(',')
          }
          if (this.$isEmpty(value)) {
            value = header.isNumber ? 0 : ''
          }
          oneRow[header.prop] = value

          var tagKey = header.prop + '_tagData'
          var tagData = row[tagKey]
          if (this.$isNotEmpty(tagData)) {
            oneRow[tagKey] = tagData
          }
          return oneRow
        } else {
          return this.collectColumnData(header.children, oneRow, row)
        }
      })
    },
    setButtonBarVisible(visible, isNoPaddingTop) { // 动态设置按钮栏是否显示
      this.$refs.baseCurdRowForm.setButtonBarVisible(visible, isNoPaddingTop)
    },
    clearError() {
      this.$refs.baseCurdRowForm.clearError()
    },
    showError(result) {
      // this.addErrorMessageSpans()
      this.cloneRow = {}
      this.headerIndexes = {}
      var headersNew = []
      var errorCount = 0

      for (let i = 0; i < this.headers.length; i++) {
        if (this.headers[i].columnEntity &&
          (this.headers[i].columnEntity.isEnabled === '是' ||
            this.headers[i].columnEntity.isEnabled === '')) {
          headersNew.push(this.headers[i])
        }
      }

      for (let j = 0; j < headersNew.length; j++) {
        var hd = headersNew[j]
        this.cloneRow[hd.label] = ''
        this.headerIndexes[hd.label] = j + 1 // 第一列是checkbox，需+1
      }

      var keys = Object.keys(result.attributes)
      // $('#' + this.formTabRowFormRootId + ' .rowFormError').text('')
      if (this.$isNotEmpty(keys)) {
        // var errorMap = {}
        var level2DetailObjName = []
        var level3DetailObjName = []
        var level3Item
        for (let i = 0; i < keys.length; i++) {
          var tokens = keys[i].split(':')
          // var rowIndex = parseInt(tokens[1])
          // var columnIndex = this.headerIndexes[tokens[2]]
          // errorMap['row' + rowIndex + '_col' + columnIndex] = result.attributes[keys[i]]

          if (tokens[0].indexOf('_') === tokens[0].lastIndexOf('_')) {
            var detailObjName2 = tokens[0].split('_')
            level2DetailObjName.push(detailObjName2[1])
          } else {
            var detailObjName3 = tokens[0].split('_')
            level3DetailObjName.push(detailObjName3[2])
            level3DetailObjName.push(detailObjName3[1])
            level3Item = detailObjName3[3]
          }

          if (tokens[0] === this.treeNodeLabel || level3DetailObjName.includes(this.treeNodeLabel)) {
            // var this_ = this
            // $('#' + this.formTabRowFormRootId + ' .rowFormError').text('')
            // $('#' + this.formTabRowFormRootId).find('input').attr('style', 'border: 1px solid #BBBBBB !important')
            // var $rows = $('#' + this.formTabRowFormRootId).find('tr.el-table__row')

            // $.each($rows, function(rIndex) {
            //   var $tds = $(this).find('td')
            //   $.each($tds, function(cIndex) {
            //     var errorKey = 'row' + rIndex + '_col' + cIndex
            //     if (this_.$isNotEmpty(errorMap[errorKey])) {
            //       $(this).find('.rowFormError').text(errorMap[errorKey])
            //       $(this).find('.el-table').find('.el-table__row').attr('style', 'border: 1px solid #F56C6C !important')
            //     }
            //   })
            // })
            this.$refs.baseCurdRowForm.showBlistError(tokens[1], tokens[2], result.attributes[keys[i]], i === 1)
          }
        }
        var $elTabPane = $('#cformTabSave').find('.el-tabs').find('.el-tabs__item')
        $.each($elTabPane, function(index) {
          var name = $elTabPane[index].id.split('-')
          const textContent = $elTabPane[index].textContent
          let newName = name[1]
          if (textContent.includes('.')) {
            newName = (index + 1 + '.') + newName
          }
          name.push(newName)
          $elTabPane[index].setAttribute('style', 'color: #303133 !important')
          if (level3DetailObjName.includes(name[1])) {
            $elTabPane[index].innerHTML = name[1]
            $elTabPane[index].setAttribute('style', 'color: #F56C6C !important')
            const count3 = level3DetailObjName.reduce((acc, curr) => {
              if (curr === name[1]) {
                return acc + 1
              }
              return acc
            }, 0)
            if (count3 > 0) {
              errorCount = errorCount + count3
              var msg3 = name[2] + '(检查发现' + count3 + '个错误)'
              $elTabPane[index].innerHTML = msg3
            }
          } else {
            $elTabPane[index].innerHTML = newName
          }
          if (level2DetailObjName.includes(name[1])) {
            $elTabPane[index].innerHTML = name[2]
            $elTabPane[index].setAttribute('style', 'color: #F56C6C !important')
            const count2 = level2DetailObjName.reduce((acc, curr) => {
              if (curr === name[1]) {
                return acc + 1
              }
              return acc
            }, 0)
            if (count2 > 0) {
              errorCount = errorCount + count2
              var msg2 = name[2] + '(检查发现' + count2 + '个错误)'
              $elTabPane[index].innerHTML = msg2
            }
          }
        })
        if (this.level3DetailObj) {
          var rowsData = this.$refs.baseCurdRowForm.rowsData
          this.$nextTick(() => {
            if (level3Item) {
              this.$refs.baseCurdRowForm.$refs.baseList.$refs.table.clearSelection()
              this.$refs.baseCurdRowForm.$refs.baseList.$refs.table.toggleRowSelection(rowsData[level3Item], true)
            }
            setTimeout(() => { this.level3DetailObj.showError(result) }, 100)
          })
        }
      }
      return errorCount
    },

    addErrorMessageSpans() {
      var $spanContainers = $('.rowFormContent').find('.vxe-table')
        .find('.vxe-body--row').find('.c--tooltip')
      $.each($spanContainers, function() {
        var $span = $(this).find('span.rowFormError')
        if ($span.length === 0) {
          $(this).append('<span class="rowFormError"/>')
        }
      })
    },
    updateRowsData(updatayear) {
      this.$refs.baseCurdRowForm.updateRowsData(updatayear)
    },
    addRow(bt) {
      this.$refs.baseCurdRowForm.addRow(bt)
    },
    // 可以添加行数据
    addRowsData(row) {
      this.setDeclareDisabled(row)
      this.$refs.baseCurdRowForm.addRowsData(row)
    },
    // 获取表格数据
    getRowsData() {
      return this.$refs.baseCurdRowForm.getRowsData()
    },
    reRenderBtns(hiddenButtons) {
      this.$refs.baseCurdRowForm.reRenderBtns(hiddenButtons)
    },
    clearTabPageData() {
      if (this.level3DetailObj) {
        this.level3DetailObj.clearTabPageData()
      }
    },
    // 子项目测算是否有挑选功能
    submeaGetEstMode() {
      if (this.level3DetailObj) {
        this.level3DetailObj.submeaGetEstMode?.()
      }
    },
    // 根据id删除行
    deleteTableDataByField(id) {
      this.$refs.baseCurdRowForm.deleteTableDataByField('id', id)
    },
    getHistoryOptions() {
      return this.$refs.baseCurdRowForm.getHistoryOptions()
    },
    clearRow() {
      this.$refs.baseCurdRowForm.clearRow()
    },
    callbackAddRow(newRow) {
      if (this.level3DetailObj) {
        this.level3DetailObj.parentAddRow(newRow)
      }
    },
    callbackDeleteRow(baseCurdObj, rowIdsDeleted) {
      if (this.level3DetailObj) {
        this.level3DetailObj.parentDeleteRow(baseCurdObj, rowIdsDeleted)
      }
    },
    initGovemment(rows = []) {
      if (this.params.metaIdTab !== GOVEMMENT || !this.isEdit || this.fnBgSys) {
        return
      }
      rows.forEach(row => {
        this.setGovemmentDisabled(row)
        this.itemGovemmentChange(row)
      })
    },
    // 选了采购品目明细，才能选是否适应由中小型企业提供
    setGovemmentDisabled(row = {}) {
      // 采购品目 是否适宜由中小企业提供
      // 有三级明细证明是外层新增行
      if (this.params.metaIdTab !== GOVEMMENT || !this.isEdit || this.fnBgSys) {
        return
      }
      let isDisabled = false
      if (!row[ITEMS]) {
        isDisabled = true
      }
      this.setRowDisabled(row, ENTERPRISE, isDisabled)
      if (isDisabled) {
        row[ENTERPRISE] = ''
      }
    },
    itemGovemmentChange(row = {}) {
      if (this.params.metaIdTab !== GOVEMMENT || !this.isEdit || this.fnBgSys) {
        return
      }
      const tagData = row[ITEMS + '_tagData'] || ''
      const govemmentMeney = this.$moneyToNumber(row[GOVEMMENT_PROP])
      const isMasterDudget = row[RESV_STYLE] === MASTER_BUDGET
      const setValAndDisabled = (num) => {
        // 是否适宜由中小企业提供等于否时，['预留方式', '预留中小企业比例', '是否仅面向小微企业']禁用，不需要操作
        if (row[ENTERPRISE] === '否') {
          return
        }
        let isDisabled = false
        const fulfillMoney = govemmentMeney <= num
        if (fulfillMoney) {
          isDisabled = true
          this.$set(row, RESV_STYLE, MASTER_BUDGET)
          this.$set(row, RATIO, 100)
          this.setRowDisabled(row, RESV_STYLE, isDisabled)
        } else if (isMasterDudget) {
          isDisabled = true
          // 采购品目选择货物或服务，且采购金额200万以上，且预留方式为整体预留时，预留中小企业比例默认100%，不可编辑
          this.$set(row, RATIO, 100)
          this.setRowDisabled(row, RESV_STYLE, false)
        }
        this.setRowDisabled(row, RATIO, isDisabled)
      }
      switch (tagData.charAt(0)) {
        case 'A': case 'C': {
          // 采购品目选择货物或服务，且采购金额200万（含）以下，预留方式只能为整体预算，预留中小企业比例默认100%，不可编辑
          setValAndDisabled(2000000)
          break
        }
        case 'B': {
          // 采购品目选择工程，且采购金额400万（含）以下，预留方式只能为整体预算，预留中小企业比例默认100%，不可编辑
          setValAndDisabled(4000000)
          break
        }
        default:
          if (row[ENTERPRISE] === '是') {
            this.setRowDisabled(row, RESV_STYLE, false)
            this.setRowDisabled(row, RATIO, false)
          }
          break
      }
    },
    rowCellChanged(scope, colItem, baseList) {
      this.params.rowCellChanged?.(scope, colItem, baseList)
      this.setGovemmentDisabled(scope.row)
      this.itemGovemmentChange(scope.row)
    },
    rowCellClickCallback(scope, colItem, selectedData, isRowCellMap) {
      this.params.rowCellClickCallback?.(scope, colItem, selectedData, isRowCellMap)
      this.setGovemmentDisabled(scope.row)
      this.itemGovemmentChange(scope.row)
    },
    afterRowCalculation(scope) {
      this.params.afterRowCalculation?.()
      this.itemGovemmentChange(scope.row)
    },
    setRowDisabled(row, prop, isDisabled) {
      const col = this.propsLabelMap[prop] || {}
      if (col.label) {
        this.$set(row, (col.label + '_禁用'), isDisabled)
      }
    },
    setBudgetDisabled(row = {}) {
      if (this.params.tabName !== BUDGET) {
        return
      }
      const col = this.propsLabelMap[SUBPROJECT]
      if (!col.exData?.['三级明细类别']) {
        return
      }
      this.setRowDisabled(row, DECLARE_NUM, true)
    },
    initBudget(rows = []) {
      if (this.params.tabName !== BUDGET || !this.isEdit || this.fnBgSys) {
        return
      }
      rows.forEach(row => {
        this.setBudgetDisabled(row)
      })
    },
    initPlan(rows) {
      if (!this.isBasicExtend || this.params.template?.name !== '分年支出计划' || !this.isAutoTotal || !this.isEdit || this.fnBgSys) {
        return
      }
      rows.forEach(row => {
        this.setDeclareDisabled(row)
      })
    },
    setDeclareDisabled(row = {}) {
      if (this.isBasicExtend && this.params.template?.name === '分年支出计划' && this.isAutoTotal) {
        this.setRowDisabled(row, DECLARE_NUM, true)
      }
    },
    customSelectOptionsSupplier(scope, colItem) {
      const GB = '管理级别'
      const FJ = '分级标准'
      if (this.params.tabName !== '预算信息') {
        return
      }
      const fljbMap = this.params.dataVo.extData.管理级别对分级标准 || {}
      const fljbKeys = Object.keys(fljbMap) || []
      const row = scope.row || {}
      if (colItem.prop === GB) {
        return fljbKeys.map(item => ({
          label: item,
          value: item
        }))
      }
      if (colItem.prop === FJ) {
        const gbVal = row[GB]
        const fjOptions = fljbMap[gbVal]
        if (!gbVal) {
          this.$message.warning('请先选择管理级别')
        }
        if (!gbVal || !fjOptions) {
          return []
        }
        return fjOptions.map(item => ({
          label: item.nextElementName,
          value: item.nextElementName
        }))
      }
    }
  }
}
</script>

<style lang="scss">
  .rowFormError {
    color: #F56C6C;position: absolute;top: 2px;right: 3px;
    font-size: 12px;height: 12px;line-height: 12px;  }
  .tableEditInputNumber .rowFormError { left: 3px; right: unset;}
</style>
