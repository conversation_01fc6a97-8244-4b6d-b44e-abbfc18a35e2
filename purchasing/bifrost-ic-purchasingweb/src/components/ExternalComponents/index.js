/*
 * @LastEditors  : Yx
 * @LastEditTime : 2025-03-13 11:44:34
 * @Description  : 未备注
 * <AUTHOR> Yx
 * @Date         : 2025-03-12 10:07:33
 * @FilePath     : \ic\bifrost-ic-frontend\src\components\ExternalComponents\index.js
 */
import Vue from 'vue'
import { event } from '@/utils/sunmei-data'
// 获取外部组件
export function getExternalComponents({ receive = 'bifrost-ic', sysName = 'bifrost-finance', componentName }) {
  // 页面引入使用
  // getExternalComponents({
  //   componentName: 'DialogVoucherDetail',
  // })
  event.emit('getCmp', {
    sysName: sysName, // 需要调用哪个子应用的组件
    path: '/', // 固定语法
  })
  // 发送获取组件信息
  const cmpData = {
    receive: receive, // 接收组件的应用名称（当前系统）
    send: sysName, // 发送组件的应用名称（要调用的系统）
    data: {
      cmp_name: componentName,
      cmp_names: [componentName],
    },
  }
  Vue.prototype.parentProps.setGlobalState({ loadcmp: cmpData })
}

// 全局调用
export function ExternalComponentsInfo(componentName, options = {}) {
  console.log("🚀 ~ ExternalComponentsInfo ~ Vue.options.components:", Vue.options.components)
  if (Vue.options.components[componentName]) {
    let externalDom = document.getElementById('externalComponent')
    if (externalDom !== null) {
      externalDom.remove()
    }
    //创建实例并且挂载
    let VueConstructor = Vue.extend(Vue.options.components[componentName]) // 使用Vue.extend创建组件构造器
    externalDom = document.createElement('div')
    const instance = new VueConstructor(options).$mount(externalDom)
    if (instance && instance.$children && instance.$children.length) {
      // 弹窗外部组件引入，会多一层 div
      instance.$children[0].$el.setAttribute('id', 'externalComponent')
      document.body.appendChild(instance.$children[0].$el) // 挂载到指定dom上
    } else {
      // 可能引入不一定会执行，还需测试
      instance.$el.setAttribute('id', 'externalComponent')
      document.body.appendChild(instance.$el)
    }
    Vue.prototype.$nextTick(() => {
      instance.init()
    })
  } else {
    Vue.prototype.$message.info('正在加载，请稍等几秒再尝试')
  }
  // 使用
  // this.$ExternalComponentsInfo('DialogVoucherDetail', {
  //   propsData: {
  //     voucherId: '1',
  //     booksetId: '1',
  //   },
  // })
}
