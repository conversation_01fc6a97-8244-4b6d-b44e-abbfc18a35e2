<template>
  <el-dialog
    title="查看投标文件重复率详情"
    :visible.sync="dialogVisible"
    width="90%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    modal-append-to-body
    append-to-body
  >
    <p class="text-center mb-2 " style="color:#d32f2f; font-size:15px">请点击下方下拉框切换不同的供应商对比查看重复的内容</p>
    <div class="detail-container">
      <!-- 左侧树形列表 -->
      <div class="tree-section">
        <el-input
          placeholder="输入关键字进行过滤"
          v-model="filterText"
          style="margin-bottom: 10px"
        ></el-input>
        <el-tree
          :data="treeData"
          node-key="id"
          :expand-on-click-node="false"
          highlight-current
          @node-click="handleNodeClick"
          :default-expand-all="true"
          :props="{ label: 'name', children: 'children' }"
          :filter-node-method="filterNode"
          ref="tree"
          v-loading="treeLoading"
        >
          <template #default="{ node, data }">
            <span class="custom-node">
              {{ node.label }}
            </span>
          </template>
        </el-tree>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-section">
        <!-- 供应商1内容 -->
        <div class="content-panel">
          <div class="panel-header">
            <el-select
              v-model="selectedSupplier1"
              placeholder="请选择供应商"
              @change="handleSupplier1Change"
              style="width: 100%"
            >
              <el-option
                v-for="supplier in availableSuppliers1"
                :key="supplier.supplierId"
                :label="supplier.supplierName"
                :value="supplier.supplierId"
              >
              </el-option>
            </el-select>
          </div>
          <div class="panel-content" v-loading="content1Loading">
            <div v-if="content1" v-html="processedContent1" class="html-content"></div>
            <div v-else class="empty-content">暂无内容</div>
          </div>
        </div>

        <!-- 供应商2内容 -->
        <div class="content-panel">
          <div class="panel-header">
            <el-select
              v-model="selectedSupplier2"
              placeholder="请选择供应商"
              @change="handleSupplier2Change"
              style="width: 100%"
            >
              <el-option
                v-for="supplier in availableSuppliers2"
                :key="supplier.supplierId"
                :label="supplier.supplierName"
                :value="supplier.supplierId"
              >
              </el-option>
            </el-select>
          </div>
          <div class="panel-content" v-loading="content2Loading">
            <div v-if="content2" v-html="processedContent2" class="html-content"></div>
            <div v-else class="empty-content">暂无内容</div>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import * as Diff from 'diff'

export default {
  name: 'SupplierSimilarityDetailDialog',
  data() {
    return {
      dialogVisible: false,
      filterText: '',
      treeData: [],
      treeLoading: false,
      content1Loading: false,
      content2Loading: false,
      content1: '',
      content2: '',
      processedContent1: '', // 处理后的内容1（带差异高亮）
      processedContent2: '', // 处理后的内容2（带差异高亮）
      supplierInfo: {},
      projectId: null,
      currentTreeId: null,
      // 供应商选择相关数据
      supplierList: [], // 所有供应商列表
      selectedSupplier1: null, // 选中的供应商1 ID
      selectedSupplier2: null, // 选中的供应商2 ID
      supplier1Name: '', // 供应商1名称
      supplier2Name: '', // 供应商2名称
      purFileConfigCatalogBizid: ''
    }
  },
  computed: {
    // 供应商1可选列表（排除已选择的供应商2）
    availableSuppliers1() {
      return this.supplierList.filter(supplier =>
        supplier.supplierId !== this.selectedSupplier2
      )
    },
    // 供应商2可选列表（排除已选择的供应商1）
    availableSuppliers2() {
      return this.supplierList.filter(supplier =>
        supplier.supplierId !== this.selectedSupplier1
      )
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree && this.$refs.tree.filter(val)
    }
  },
  methods: {
    // 打开弹窗
    openDialog(supplierInfo, projectId, supplierList) {
      this.supplierInfo = supplierInfo || {}
      this.projectId = projectId
      this.supplierList = supplierList || []

      // 设置默认选中的供应商
      this.initDefaultSuppliers()

      this.dialogVisible = true
      this.loadTreeData()
    },

    // 初始化默认选中的供应商
    initDefaultSuppliers() {
      // 根据原始数据设置默认选中
      if (this.supplierInfo.supplierName1) {
        // 先尝试通过 supplierId1 匹配
        let supplier1 = this.supplierList.find(s => s.supplierId === this.supplierInfo.supplierId1)
        // 如果没找到，通过名称匹配
        if (!supplier1) {
          supplier1 = this.supplierList.find(s => s.supplierName === this.supplierInfo.supplierName1)
        }
        if (supplier1) {
          this.selectedSupplier1 = supplier1.supplierId
          this.supplier1Name = supplier1.supplierName
        }
      }

      if (this.supplierInfo.supplierName2) {
        // 先尝试通过 supplierId2 匹配
        let supplier2 = this.supplierList.find(s => s.supplierId === this.supplierInfo.supplierId2)
        // 如果没找到，通过名称匹配
        if (!supplier2) {
          supplier2 = this.supplierList.find(s => s.supplierName === this.supplierInfo.supplierName2)
        }
        if (supplier2) {
          this.selectedSupplier2 = supplier2.supplierId
          this.supplier2Name = supplier2.supplierName
        }
      }

      // 如果还没有选中任何供应商，使用列表中的前两个
      if (!this.selectedSupplier1 && this.supplierList.length > 0) {
        this.selectedSupplier1 = this.supplierList[0].supplierId
        this.supplier1Name = this.supplierList[0].supplierName
      }

      if (!this.selectedSupplier2 && this.supplierList.length > 1) {
        this.selectedSupplier2 = this.supplierList[1].supplierId
        this.supplier2Name = this.supplierList[1].supplierName
      }
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.resetData()
      this.$emit('close')
    },

    // 重置数据
    resetData() {
      this.filterText = ''
      this.treeData = []
      this.content1 = ''
      this.content2 = ''
      this.processedContent1 = ''
      this.processedContent2 = ''
      this.supplierInfo = {}
      this.projectId = null
      this.currentTreeId = null
      this.supplierList = []
      this.selectedSupplier1 = null
      this.selectedSupplier2 = null
      this.supplier1Name = ''
      this.supplier2Name = ''
    },

    // 供应商1选择变化
    handleSupplier1Change(supplierId) {
      this.selectedSupplier1 = supplierId
      const supplier = this.supplierList.find(s => s.supplierId === supplierId)
      this.supplier1Name = supplier ? supplier.supplierName : ''

      // 重新加载供应商1的内容
      if (this.currentTreeId) {
        this.loadSupplierContent()
      }
    },

    // 供应商2选择变化
    handleSupplier2Change(supplierId) {
      this.selectedSupplier2 = supplierId
      const supplier = this.supplierList.find(s => s.supplierId === supplierId)
      this.supplier2Name = supplier ? supplier.supplierName : ''

      // 重新加载供应商2的内容
      if (this.currentTreeId) {
        this.loadSupplierContent()
      }
    },

    // 树形过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },

    // 加载树形数据
    loadTreeData() {
      if (!this.projectId) {
        this.$message.warning('项目ID不能为空')
        return
      }

      this.treeLoading = true
      this.$callApiParams('treeBidFileConfigCatalog',
        {
          projectInfoBizid: this.projectId,
          supplierInfoBizid: this.supplierInfo.supplierId1 || this.supplierInfo.supplierId2
        },
        (result) => {
          this.treeLoading = false
          if (result && result.data) {
            this.treeData = result.data
            // 默认选中第一个节点
            if (this.treeData.length > 0) {
              this.$nextTick(() => {
                this.handleNodeClick(this.treeData[0])
              })
            }
          } else {
            this.treeData = []
          }
          return true
        },
        () => {
          this.treeLoading = false
          this.$message.error('加载树形数据失败')
        }
      )
    },

    // 树节点点击事件
    handleNodeClick(node) {
      if (!node || !node.id) return

      this.currentTreeId = node.id
      this.purFileConfigCatalogBizid = node.purFileConfigCatalogBizid
      this.loadContent()
    },

    // 加载内容
    loadContent() {
      if (!this.currentTreeId) return

      // 加载供应商的内容
      this.loadSupplierContent()
    },

    // 加载所有供应商的内容
    loadSupplierContent() {
      this.content1Loading = true
      this.content2Loading = true

      // 根据供应商索引获取对应的供应商标识
      const supplierParam = {
        supplierId1: this.selectedSupplier1,
        supplierId2: this.selectedSupplier2,
        purFileConfigCatalogBizid: this.purFileConfigCatalogBizid
      }

      this.$callApiParams('listSuppliersBidContent',
        {
          bizid: this.currentTreeId,
          projectInfoBizid: this.projectId,
          ...supplierParam
        },
        ({ data }) => {
          this.content1Loading = false
          this.content2Loading = false
          if (data && data.data1 && data.data1.length > 0) {
            this.content1 = data.data1[0].content || ''
            this.content2 = data.data2[0].content || ''
          } else {
            this.content1 = ''
            this.content2 = ''
          }

          // 内容加载完成后进行差异对比
          this.$nextTick(() => {
            this.performContentDiff()
          })

          return true
        },
        () => {
          this.content1Loading = false
          this.content2Loading = false
          this.content1 = ''
          this.content2 = ''
          this.$message.error(`加载供应商内容失败`)
        }
      )
    },

    // 执行内容差异对比
    performContentDiff() {
      if (!this.content1 && !this.content2) {
        this.processedContent1 = ''
        this.processedContent2 = ''
        return
      }
      try {
        // 提取纯文本进行对比（去除HTML标签）
        const { label: label1, content: text1 } = this.extractTextFromHtml(this.content1)
        const { label: label2, content: text2 } = this.extractTextFromHtml(this.content2)

        // 使用 diff 库进行文本对比
        const diffs = Diff.diffChars(text1, text2)
        label1.querySelectorAll('*').forEach((item) => this.markRepeat(item, diffs))
        label2.querySelectorAll('*').forEach((item) => this.markRepeat(item, diffs))
        this.processedContent1 = label1.innerHTML || ''
        this.processedContent2 = label2.innerHTML || ''
      } catch (error) {
        console.error('差异对比失败:', error)
        // 如果对比失败，使用原始内容
        this.processedContent1 = this.content1
        this.processedContent2 = this.content2
      }
    },

    // 从HTML中提取纯文本
    extractTextFromHtml(html) {
      if (!html) return ''

      // 创建临时DOM元素来提取文本
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = html
      return {
        label: tempDiv,
        content: tempDiv.textContent || tempDiv.innerText || ''
      }
    },

    markRepeat(span, diff) {
      const flag = diff.find(part =>
        part.value.includes(span.innerText) &&
        !part.removed &&
        !part.added
      )
      if (flag) {
        span.classList.add('diff-same')
      }
    }

    // 加载指定供应商的内容
    // loadSupplierContent(supplierIndex) {
    //   const loadingKey = `content${supplierIndex}Loading`
    //   const contentKey = `content${supplierIndex}`
    //
    //   this[loadingKey] = true
    //
    //   // 根据供应商索引获取对应的供应商标识
    //   // 这里假设后端接口支持通过供应商名称或ID来区分不同供应商的内容
    //   const supplierParam = supplierIndex === 1
    //     ? { supplierName: this.supplierInfo.supplierName1 }
    //     : { supplierName: this.supplierInfo.supplierName2 }
    //
    //   this.$callApiParams('listContentBidFileConfigCatalog',
    //     {
    //       bizid: this.currentTreeId,
    //       projectInfoBizid: this.projectId,
    //       ...supplierParam
    //     },
    //     (result) => {
    //       this[loadingKey] = false
    //       if (result && result.data && result.data.length > 0) {
    //         this[contentKey] = result.data[0].content || ''
    //       } else {
    //         this[contentKey] = ''
    //       }
    //       return true
    //     },
    //     () => {
    //       this[loadingKey] = false
    //       this[contentKey] = ''
    //       this.$message.error(`加载供应商${supplierIndex}内容失败`)
    //     }
    //   )
    // },
  }
}
</script>

<style scoped lang="scss">
.detail-container {
  display: flex;
  height: 600px;
  border: 1px solid #ddd;
}

.tree-section {
  width: 300px;
  padding: 10px;
  border-right: 1px solid #ddd;
  overflow-y: auto;
}

.content-section {
  flex: 1;
  display: flex;
}

.content-panel {
  flex: 1;
  display: flex;
  flex-direction: column;

  &:first-child {
    border-right: 1px solid #ddd;
  }
}

.panel-header {
  padding: 10px;
  background: #f5f7fa;
  border-bottom: 1px solid #ddd;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
  }

  .el-select {
    .el-input__inner {
      font-weight: bold;
      background: transparent;
      border: 1px solid #dcdfe6;

      &:hover {
        border-color: #409eff;
      }

      &:focus {
        border-color: #409eff;
      }
    }
  }
}

.panel-content {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}

.html-content {
  line-height: 1.6;

  ::v-deep table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ddd;

    td, th {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }

    th {
      background-color: #f5f7fa;
      font-weight: bold;
    }
  }

  // 差异高亮样式
  ::v-deep .diff-same {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
  }

  ::v-deep .diff-different {
    background-color: transparent;
    color: inherit;
  }
}

.empty-content {
  text-align: center;
  color: #999;
  padding: 50px 0;
}

.dialog-footer {
  text-align: center;
}

.custom-node {
  display: flex;
  align-items: center;
}
</style>
