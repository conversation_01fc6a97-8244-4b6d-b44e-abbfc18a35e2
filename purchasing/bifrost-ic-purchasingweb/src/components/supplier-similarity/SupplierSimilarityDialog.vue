<template>
  <el-dialog
    title="查看投标文件重复率"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    modal-append-to-body
    append-to-body
  >
    <div class="supplier-similarity-container">
      <div v-if="tableData.length === 0 && !loading" class="empty-data">
        <p>暂无供应商重复率数据</p>
      </div>
      <el-table
        :data="tableData"
        border
        height="400"
        :header-cell-style="{ background: '#f0f5ff', fontWeight: '700', color: 'black' }"
        v-loading="loading"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="供应商名称1" prop="supplierName1" align="center" min-width="150"></el-table-column>
        <el-table-column label="供应商名称2" prop="supplierName2" align="center" min-width="150"></el-table-column>
        <el-table-column label="重复率" prop="similarity" align="center" width="160">
          <template slot-scope="scope">
            {{ scope.row.similarity }}%
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="80">
          <template slot-scope="scope">
            <el-button type="text" @click="handleViewDetail(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>

    <!-- 详情弹窗 -->
    <supplier-similarity-detail-dialog
      ref="detailDialog"
      @close="handleDetailClose"
    />
  </el-dialog>
</template>

<script>
import SupplierSimilarityDetailDialog from './SupplierSimilarityDetailDialog.vue'

export default {
  name: 'SupplierSimilarityDialog',
  components: {
    SupplierSimilarityDetailDialog
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      projectId: null
    }
  },
  methods: {
    // 打开弹窗
    openDialog(projectId) {
      this.projectId = projectId
      this.dialogVisible = true
      this.loadData()
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.tableData = []
      this.projectId = null
    },

    // 加载供应商重复率数据
    loadData() {
      if (!this.projectId) {
        this.$message.warning('项目ID不能为空')
        return
      }

      this.loading = true
      this.$callApiParams('supplierTextCompareList',
        { projectInfoBizid: this.projectId },
        (result) => {
          this.loading = false
          if (result && result.data) {
            this.tableData = result.data
          } else {
            this.tableData = []
          }
          return true
        },
        () => {
          this.loading = false
          this.$message.error('加载数据失败')
        }
      )
    },

    // 查看详情
    handleViewDetail(row) {
      // 生成供应商列表数据
      const supplierList = this.generateSupplierList()
      this.$refs.detailDialog.openDialog(row, this.projectId, supplierList)
    },

    // 生成供应商列表（去重）
    generateSupplierList() {
      const supplierMap = new Map()

      this.tableData.forEach((item, index) => {
        // 添加供应商1
        if (item.supplierName1) {
          const supplierId1 = item.supplierId1 || `supplier1_${index}_${item.supplierName1}`
          supplierMap.set(supplierId1, {
            supplierId: supplierId1,
            supplierName: item.supplierName1
          })
        }
        // 添加供应商2
        if (item.supplierName2) {
          const supplierId2 = item.supplierId2 || `supplier2_${index}_${item.supplierName2}`
          supplierMap.set(supplierId2, {
            supplierId: supplierId2,
            supplierName: item.supplierName2
          })
        }
      })

      return Array.from(supplierMap.values())
    },

    // 详情弹窗关闭回调
    handleDetailClose() {
      // 可以在这里处理详情弹窗关闭后的逻辑
    }
  }
}
</script>

<style scoped lang="scss">
.supplier-similarity-container {
  padding: 10px 0;
}

.dialog-footer {
  text-align: center;
}

.empty-data {
  height: 400px;
  text-align: center;
  padding: 50px 0;
  color: #999;
  font-size: 14px;
}
</style>
