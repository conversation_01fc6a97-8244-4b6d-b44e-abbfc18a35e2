<template>
  <!-- <div :class="isDbCol ? 'container-dbcol' : 'receptacle-border'"> -->
  <div
    ref="layoutTemRef"
    :class="
      (isDbCol ? 'receptacle-border flex-column' : 'receptacle-border flex-column') +
        (isNotPadding ? ' no-padding' : '')
    "
    :style="{ border: !isOutsideBorder ? 0 : '' }"
  >
    <slot name='blockName'></slot>
    <!-- <div class="receptacle-border flex-column"> -->
    <div class="column-top" ref="columnTopRef">
      <div class="filters-normal" v-if="isFilterShow">
        <!-- 搜索框区域 -->
        <!-- <slot name="filter" style="display:inline-block"></slot> -->
        <!-- 是否有下拉 -->
        <el-button v-if="isFilterDown" type="text" @click="handleDownChange">
          <template v-if="!downblock">
            展开
            <i class="el-icon-arrow-down el-icon--right"></i>
          </template>
          <template v-if="downblock">
            收起
            <i class="el-icon-arrow-up el-icon--right"></i>
          </template>
        </el-button>
        <el-button
          v-if="isFilterBtShow"
          plain
          type="primary"
          icon="el-icon-search"
          :loading="tableLoading"
          @click="handleSearch"
          >查询</el-button
        >
        <el-button
          v-if="isFilterBtShow"
          plain
          type="info"
          icon="el-icon-refresh"
          @click="handleSearchReset"
          >重置</el-button
        >
        <!-- 当有下拉条件时，自定义按钮位置是否并列第一行 add by yangpeng 02/26-->
        <template v-if="buttonTopFlag">
          <slot name="buttonTop"></slot>
        </template>
        <!-- 搜索下拉区域 -->
        <div v-if="isFilterDown" class="downblock">
          <transition name="down">
            <div class="down" v-if="downblock">
              <slot name="filter-down" style="display:inline-block"></slot>
            </div>
          </transition>
        </div>
      </div>
      <div
        :class="{'buttons-normal': true,'right': buttonFloatRight}"
        v-if="isButtonShow"
      >
        <!-- 按钮群区域 -->
        <slot name="filter" style="display:inline-block"></slot>
        <slot name="button"></slot>
      </div>
    </div>
    <div class="column-bottom" v-show="isFold">
      <div
        class="dbcol-left padding"
        :class="leftCollapsed ? 'collapsed' : ''"
        v-if="isDbCol"
        id="dbclo-left"
      >
        <!-- 左侧 -->
        <div class="dbColLeft" v-show="!leftCollapsed">
          <slot name="dbColLeft"></slot>
        </div>
      </div>
      <!-- 左侧拖动大小 -->
      <div id="dbclo-resize" class="dbclo-resize" v-if="isDbCol && isDragResize"></div>
      <!-- 缩进按钮 -->
      <!--  :style="leftCollapsed ? 'left: -4px' : ''" -->
      <div class="retract-block" style="cursor:pointer;" :class="{ 'mright-10': !leftCollapsed }" @click="leftCollapsed = !leftCollapsed" v-if="isDbCol && isRetract">
        <i class="el-icon-arrow-left" v-if="!leftCollapsed" style="font-size:13px;cursor:pointer;"></i>
        <i class="el-icon-arrow-right" v-if="leftCollapsed" style="font-size:13px;cursor:pointer;"></i>
      </div>
      <!-- isDbCol && isRetract -->
      <!-- isDbCol ? 'dbcol-right' : '' -->
      <div
        :class="{ 'dbcol-right': isDbCol, isRetract: isDbCol && isRetract, 'single-main': !isDbCol }"
        style="flex:1"
      >
        <div class="main-border flex-column" :class="{ isPageShow: isPageShow }">
          <!-- 主要区域 -->
          <slot name="main"></slot>
        </div>
        <!-- 分页组件 -->
        <div class="bottom-normal" v-if="isPageShow">
          <el-pagination
            :page-size="pageInfo.size"
            :current-page="pageInfo.page"
            :total="pageInfo.total"
            :page-sizes="pageSizes"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @keyup.enter.native="handleKey"
            :layout="pageLayout"
          ></el-pagination>
        </div>
        <!-- 附表区域 -->
        <div class="main-border" v-if="isSubTable">
          <!-- 二级区域 -->
          <slot name="subTable"></slot>
        </div>
      </div>
      <!-- 右侧折叠区块 -->
       <div class="padding dbcol-left collapsed-right" :class="rightCollapsed ? 'collapsed' : ''" id="collapsed-right"  :style="{width: `${sideRightWidth}px`}" v-show="isShowSideRight">
          <div class="retract-block retract-right" style="cursor:pointer;" @click="rightCollapsed = !rightCollapsed">
              <i class="el-icon-arrow-left" v-if="rightCollapsed"  style="font-size:13px;cursor:pointer;"></i>
              <i class="el-icon-arrow-right" v-else  style="font-size:13px;cursor:pointer;"></i>
          </div>
          <div class="dbColLeft"  v-show="rightCollapsed">
              <slot name="dbColRight" ></slot>
          </div>
       </div>
    </div>

  </div>
</template>

<script>
export default {
  name: 'LayoutTem',
  props: {
    isDbCol: {
      // 是否双栏布局
      type: Boolean,
      default: false
    },
    isDragResize: {
      // 是否可以拖动左侧
      type: Boolean,
      default: false
    },
    isRetract: {
      // 是否带缩进按钮
      type: Boolean,
      default: true
    },
    isFilterShow: {
      // 是否显示搜索区域
      type: Boolean,
      default: true
    },
    isFilterBtShow: {
      // 是否显示搜索区域的按钮
      type: Boolean,
      default: true
    },
    isFilterDown: {
      // 是否显示搜索下拉区域
      type: Boolean,
      default: false
    },
    buttonTopFlag: {
      // 自定义按钮是否并列第一行,默认true
      type: Boolean,
      default: true
    },

    isButtonShow: {
      // 是否显示按钮区域
      type: Boolean,
      default: true
    },
    buttonFloatRight: {
      // 按钮是否靠右
      type: Boolean,
      default: false
    },
    isPageShow: {
      // 是否显示页数组件区域
      type: Boolean,
      default: true
    },
    isSubTable: {
      // 是否显示附表区域
      type: Boolean,
      default: false
    },
    isOutsideBorder: {
      // 是否显示外边框
      type: Boolean,
      default: false
    },
    isNotPadding: {
      // 是否不需要内边距
      type: Boolean,
      default: false
    },
    tableLoading: {
      // 搜索按钮loading
      type: Boolean,
      default: false
    },
    pageInfo: {
      // 每页显示数量 当前页数 总数
      type: Object,
      default: () => {
        return {
          size: 20,
          page: 1,
          total: 0
        }
      }
    },
    pageSizes: {
      // 每页显示数量选择
      type: Array,
      default: () => {
        return [20, 30, 50, 100]
      }
    },
    pageLayout: {
      // 分页组件显示内容
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    sideRightWidth: { // 右侧折叠区宽度
      type: String,
      default: '300'
    },
    isShowSideRight: { // 是否展示右侧折叠区域
      type: Boolean,
      default: false
    },
    buttons: {
      // 按钮
      type: Array,
      default: () => {
        return []
      }
    },
    visible: {
      // 是否显示更多按钮浮层
      type: Boolean,
      default: false
    },
    recalculate: {
      // 是否重新计算
      type: Boolean,
      default: false
    },
    isFold: {
      // 是否展开
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      downblock: false,
      isFilterHeight: 0,
      leftCollapsed: false,
      rightCollapsed: true,
      debounceResize: null, // 存放返回的防抖函数
      initWidthSum: 0, // 保存初始按钮宽度总和
      initWidthArr: [], // 保存初始各个按钮的宽度
      initButtons: [], // 保存初始buttons
      showMore: false,
      buttonsArr: [],
      moreButtons: [],
      pageRoute: ''
    }
  },
  methods: {
    handleKey(val) {
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // ie下输入分页第二次回车没用
        this.pageInfo.page = val.target.value
        this.$emit('search')
      }
    },
    // 搜索事件
    handleSearch() {
      this.$emit('search')
    },
    // 搜索重置事件
    handleSearchReset() {
      this.$emit('search-reset')
    },
    // 显示页数改变事件
    handleSizeChange(val) {
      this.$emit('size-change', val)
    },
    // 页数变化事件
    handleCurrentChange(val) {
      this.$emit('current-change', val)
    },
    // 搜索下拉状态变化事件
    handleDownChange() {
      this.downblock = !this.downblock
      this.$emit('down-change', this.downblock)
    },
    dragControllerDiv() {
      const dbcloLeft = document.getElementById('dbclo-left')
      const resize = document.getElementById('dbclo-resize')
      resize.onmousedown = (e) => {
        const leftWidth = dbcloLeft.offsetWidth
        const startX = e.clientX
        document.onmousemove = (e) => {
          const endX = e.clientX
          const moveLen = endX - startX // 拖动的距离
          console.log(leftWidth, startX, moveLen)
          let leftEndWidth = leftWidth + moveLen
          if (leftEndWidth < 40) leftEndWidth = 40
          if (leftEndWidth > 450) leftEndWidth = 450
          console.log(leftEndWidth)
          dbcloLeft.style.width = leftEndWidth + 'px'
          document.onmouseup = () => {
            document.onmousemove = null
            document.onmouseup = null
            resize.releaseCapture && resize.releaseCapture()
          }
          resize.setCapture && resize.setCapture()
          return false
        }
      }
    },
    setMutationObserver() {
      this.$nextTick(() => {
        const columnTopTar = this.$refs.columnTopRef
        if (!columnTopTar) {
          return
        }
        const search = columnTopTar.querySelector('.button-list')
        this.$resizeObserver(search, this.debounceResize)
      })
    },
    resizeFunc() {
      // 屏幕宽度
      const COLUMN_TOP_REF = this.$refs.columnTopRef
      // 如果没有按钮栏 则return
      if(!COLUMN_TOP_REF || !this.$store) return

      const allBtn = COLUMN_TOP_REF.querySelectorAll('.buttonContainer .notMoreButton span.toolbar-button')
      const allBtnInfo = COLUMN_TOP_REF.querySelector('.button-list')

      // 每次屏幕宽度变化 如果浮层是打开的 都要把更多按钮的浮层关闭
      this.visible && this.$emit('changePopover')

      /**
       * 判断dom是否为null或者空伪数组
       * @param {Array} doms 传入的doms 如果有querySelectorAll或者其他dom的结果是伪数组需要使用Array.from转数组
       */
      // const hasOneEmpty = (doms) => {
      //   return doms.map(dom => {
      //     if (dom instanceof Array) {
      //       return !!dom.length
      //     } else {
      //       return !!dom
      //     }
      //   }).some(item => item === false)
      // }

      // 需要判断的dom数组
      // const needDetermine = [Array.from(allBtn), allBtnInfo]
      // 如果有一个dom为空则return
      // if (hasOneEmpty(needDetermine)) {
      //   return
      // }
      const searchFrom = COLUMN_TOP_REF.querySelector('#searchFrom')
      const searchFormGroup = COLUMN_TOP_REF.querySelector('#searchFromMenu')

      // 计算后的按钮组成的实际宽度
      const computedWidthSum = this.initWidthSum
      // 搜索表单按钮组实际宽度
      const searchFormGroupWdith = !!searchFormGroup && searchFormGroup.hasChildNodes() ? Array.from(searchFormGroup.childNodes).map(item => {
        // 是否有右边距
        const hasMr = +getComputedStyle(item)['margin-right'].replace('px', '')
        return (hasMr) ? hasMr + item.getBoundingClientRect().width : item.getBoundingClientRect().width
      }).reduce((a, b) => a + b, 0) : 0

      if (computedWidthSum + searchFormGroupWdith > COLUMN_TOP_REF.getBoundingClientRect().width) {
        allBtnInfo.style.setProperty('flex-wrap', 'wrap')
        // allBtnInfo.style.setProperty('row-gap', '20px')
        !!searchFrom && searchFrom.style.setProperty('padding', '0 0')
        !!searchFrom && searchFrom.style.setProperty('margin-top', '20px')
      } else {
        allBtnInfo.style.removeProperty('flex-wrap')
        // allBtnInfo.style.removeProperty('row-gap')
        !!searchFrom && searchFrom.style.removeProperty('padding')
        !!searchFrom && searchFrom.style.removeProperty('margin-top')
      }

      // 判断当前按钮是否溢出 溢出显示更多按钮
      const showMore = COLUMN_TOP_REF.getBoundingClientRect().width ? computedWidthSum > COLUMN_TOP_REF.getBoundingClientRect().width : false

      // 当前按钮溢出时 才显示差值
      const differenceValue = showMore ? +(computedWidthSum - COLUMN_TOP_REF.getBoundingClientRect().width).toFixed(2) : 0
      // 更多按钮的宽度
      const moreBtnWidth = 60
      const addMoreDifferenceValue = differenceValue + moreBtnWidth
      /**
       * 组装数据
       * 用于 显示/隐藏按钮 将按钮放到更多按钮的弹出框
       * slice().reverse() slice方法创建了一个数组副本 使reverse不会影响原数组
       */
      const arr = []
      let count = 0
      let curIndex = 0
      this.initWidthArr.forEach((item, index) => {
        count = +(count + item).toFixed(2)
        if(addMoreDifferenceValue - count > 0) {
          curIndex = index + 1
        }
      })
      Array.from(allBtn).slice().reverse().forEach((item, index) => {
        const width = Math.ceil(item.getBoundingClientRect().width +  Number(getComputedStyle(item)['margin-left'].replace('px','')))
        /**
         * 屏幕小于实际计算的行宽
         */
        const more = showMore ? (index <= curIndex) : false
        arr.push({
          id: new Date().getTime()+index,
          el: item,
          width,
          // 如果缩放的大于n个按钮的宽度 则把对应的visible设置false
          more
        })
      })
      const buttons = this.buttons.map((button, key) => {
        return Object.assign(button, { more: arr.slice().reverse()[key].more })
      })
      this.showMore = showMore
      this.buttonsArr = buttons
      const payload = {
        route: this.pageRoute,
        value: {
          el: this.$el,
          showMore: this.showMore,
          buttons: this.buttonsArr,
          initWidthSum: +(this.initWidthSum).toFixed(2),
          initWidthArr: this.initWidthArr,
          group: this.findParentsHasElTabs(this),
          moreBtnWidth
        }
      }
      if (COLUMN_TOP_REF.getBoundingClientRect().width) {
        this.$store.commit('SET_INITEDOBJ', payload)
        const payload1 = {
          el: this.$el,
          id: this.findParentsHasElTabs(this),
          width: +(COLUMN_TOP_REF.getBoundingClientRect().width).toFixed(2),
          route: this.pageRoute
        }
        this.$store.commit('SET_COLUMNTOP_WIDTH', payload1)
      }

      // 更多按钮中是否有按钮
      const hasButtonInMore = buttons?.filter(bt => bt?.more && bt.visible)?.length
      let hasBtInMore = false
      this.$forceUpdate()
      this.$nextTick(() => {
        // COLUMN_TOP_REF.offsetWidth
        const buttonContainer = COLUMN_TOP_REF.querySelector('.buttonContainer').getBoundingClientRect().width // 按钮栏宽度
        const bodyWidth = COLUMN_TOP_REF.getBoundingClientRect().width // 头部总宽
        const searchWidth = COLUMN_TOP_REF.querySelector('#buttonArea') ? COLUMN_TOP_REF.querySelector('#buttonArea').getBoundingClientRect().width : 0 // 搜索栏按钮宽度
        let searchFormNum = 0
        if (hasButtonInMore > 0) { // 存在更多按钮
          hasBtInMore = true
          // 存在更多按钮时 说明空间不够 另起一行放搜索条件 187是搜索框的宽度
          searchFormNum = parseInt((bodyWidth - searchWidth) / 187)
        } else {
          hasBtInMore = false
          if (bodyWidth - buttonContainer > searchWidth) { // 能放下搜索按钮组在计算能否放下搜索条件
            searchFormNum = parseInt((bodyWidth - buttonContainer - searchWidth) / 187)
          } else { // 否则另起一行放搜索
            searchFormNum = parseInt((bodyWidth - searchWidth) / 187)
          }
        }
        searchFormNum = parseInt((bodyWidth - searchWidth) / 260)
        this.$emit('showPopover', showMore, buttons, hasBtInMore, searchFormNum)
        this.$emit('changeRecalculate', false)
      })
    },
    init() {
      const COLUMN_TOP_REF = this.$refs.columnTopRef
      // 判断是否初始化过 ((初始化过不再执行 || 是否动态添加按钮) && 当tab切换时按钮如果增加也不更新)
      if (!this.$store || !this.$store.getters.initedObj[this.pageRoute]) {
        return
      }
      const isInited = (!this.$store.getters.initedObj[this.pageRoute].filter(item => item.el === this.$el).length || this.recalculate)
      if (isInited) {
        const notMoreButton = COLUMN_TOP_REF.querySelector('.buttonContainer .notMoreButton')
        const allBtn = COLUMN_TOP_REF.querySelectorAll('.buttonContainer .notMoreButton span.toolbar-button')

        const widthSum = notMoreButton ? notMoreButton.getBoundingClientRect().width : 0
        const widthArr = []
        Array.from(allBtn).slice().reverse().map(item => {
          const width = Math.ceil(item.getBoundingClientRect().width + Number(getComputedStyle(item)['margin-left'].replace('px','')))
          widthArr.push(width)
          return width
        }).reduce((a,b) => a + b, 0)
        this.initWidthSum = widthSum
        this.initWidthArr = widthArr
        this.initButtons = this.buttons
        this.recursionFindParent(this)
        this.debounceResize()
      } else {
        // 每次屏幕宽度变化 如果浮层是打开的 都要把更多按钮的浮层关闭
        this.visible && this.$emit('changePopover')
        this.recalculateFunc(COLUMN_TOP_REF)
      }
    },
    // 重新计算
    recalculateFunc(ref) {
      this.$store.getters.initedObj[this.pageRoute].forEach(item => {
        if(this.$el === item.el && !!(ref.getBoundingClientRect().width)) {
          this.$emit('showPopover', item.showMore, item.buttons)
        }
      })
    },
    // 递归查找当前实例的上级 知道找到当前展示的el-tab-pane 将当前实例的$el存起来 用于判断是否需要初始化
    recursionFindParent(ins) {
      const payload = {
        el: this.$el,
        route: this.pageRoute
      }
      if(ins.$el.className.indexOf('el-tab-pane') !== -1) {
        if(ins.$el.style.display !== 'none') {
          this.$store.commit('SET_INITEDORDER', payload)
        }
      } else {
        // 如果找到最后$parent是undefined 就把$el存起来 否则继续递归
        if (!ins.$parent) {
          this.$store.commit('SET_INITEDORDER', payload)
        } else {
          this.recursionFindParent(ins.$parent)
        }
      }
    },
    // 找出实例的父级是否存在el-tabs 存在把实例的_uid返回 用于解决tab切换后 隐藏tab-pane的按钮容器宽度无法自动更新
    findParentsHasElTabs(ins) {
      if (ins.$el.className.indexOf('el-tabs') !== -1) {
        return ins._uid
      } else {
        if (ins.$parent) {
          return this.findParentsHasElTabs(ins.$parent)
        } else {
          return -1
        }
      }
    }
  },
  activated() {
    this.setMutationObserver()
  },
  mounted() {
    this.pageRoute = this.$route ? this.$route.path.split('/').filter((item, index) => index > 2).join('_') : ''
    this.debounceResize = this.$debounce(this.resizeFunc, 300)
    window.$event.$on('componentInit', this.init)
    this.init()
    if (this.isDbCol && this.isDragResize) {
      this.dragControllerDiv()
    }
    this.setMutationObserver()
    window.$event.$on('windowResize', this.debounceResize)
  },
  beforeDestroy() {
    window.$event.$off('componentInit', this.init)
    window.$event.$off('windowResize', this.debounceResize)
  }
}
</script>

<style scoped lang="scss">
.mright-10{
  margin-right: 10px;
}
.right {
  text-align: right;
}
$border-color: #dcdfe6;
.downblock {
  // position: absolute;
  // top: 68px;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  z-index: 500;

  .down {
    background: #fff;
    padding: 0 0 5px 0;
  }

  .down {
    &-enter-active {
      transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1),
        opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
      transform-origin: top left;
    }

    &-leave-active {
      display: none;
    }

    &-enter,
    &-leave-to {
      opacity: 0;
      transform: scaleY(0);
    }
  }
}
.dbclo-resize {
  // height: 100%;
  // background: #000;
  width: 3px;
  position: relative;
  left: -2px;
  cursor: col-resize;
}

.flex-column {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  .column-top {
  }
  .column-bottom {
    overflow: hidden;
    width: 100%;
    display: flex;
    height: 100%;
    flex-grow: 1;
    .dbcol-left {
      margin: 0px 0 5px 10px;
      width: 360px;
      // min-width: 250px;
      // height: 100%;
      box-sizing: border-box;
      background: #fff;
      border: 1px solid $border-color;
      overflow: auto;
      transition: width 0.1s ease;
      .el-input {
        margin-bottom: 5px;
      }
      &.padding {
        padding: 10px 5px;
      }
      &.collapsed {
        transition: width 0.5s ease;
        width: 20px !important;
      }
    }
    .dbcol-right {
      flex-grow: 1;
      height: 100%;
      box-sizing: border-box;
      // margin-left: 10px;
      background: #fff;
      overflow: auto;
      // transition: width 0.5s ease;
      // .bottom-normal {
      //   width: 56%; // 兼容
      //   width: calc(72% - 50px);
      // }
      &.isRetract {
        .main-border {
          padding-left: 0;
        }
      }
    }
    .collapsed-right{
      margin-left: 10px !important;
      transition: width 0.5s ease;
      overflow: hidden;
      border: 1px solid $border-color;
      .retract-right{
        left: 0;
        margin-left: 0;
        margin-right: -5px;
      }
    }
  }
}

.single-main {
  height: 100%;
  width: 100%;
}
.main-border {
  height: 100%;
  position: relative;
  &.isPageShow {
    height: calc(100% - 40px);
  }
}
.receptacle-border.no-padding {
  .filters-normal .single-filter:first-child {
    margin: 0;
  }
  .dbcol-left {
    margin-left: 0;
  }
  .buttons-normal {
    padding: 0;
  }
  .main-border {
    padding-left: 0;
    padding-right: 0;
  }
}
.retract-block {
  display: inline-block;
  height: 30px;
  position: relative;
  border: 1px solid #8ebeff;
  top: 40%;
  left: 0px;
  padding: 4px 0;
  box-sizing: border-box;
}
</style>
<style lang="scss">
.receptacle-border {
  box-sizing: border-box;
  .dbcol-left {
    height: calc(100% - 10px);
    .dbColLeft {
      // height: calc(100% - 26px);
      height: 100%;
    }
    .el-tree {
      height: calc(100% - 40px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .main-border {
    .el-tabs {
      height: 100%;
      .el-tabs__content {
        height: calc(100% - 49px);
      }
      .el-tab-pane {
        height: 100%;
      }
    }
  }

}
</style>
