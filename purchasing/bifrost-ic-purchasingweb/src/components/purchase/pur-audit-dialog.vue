<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-04 08:47:39
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-12 14:00:41
-->
import init from '@/mixins/';
<template>
  <el-dialog ref="auditDialog" :title='titleText' :width="dialogWidth" append-to-body class="dialog-style1" :close-on-click-modal="false" :visible.sync="auditVisible">
    <div class="dialog-style-border" style="height:300px; padding:20px 30px 0 0">
      <el-form ref="auditForm" :model="auditForm" :rules="rules" label-width="90px" >
        <el-form-item label="审批意见"  prop="auditOpinion">
            <el-input
             type="textarea"
             v-model="auditForm.auditOpinion" 
            :rows="6" 
             maxlength="200"
             show-word-limit
             clearable
             placeholder="请输入审批意见"
            ></el-input>
        </el-form-item>
        <el-form-item label="审核结果" prop="auditResult">
            <el-radio-group v-model="auditForm.auditResult">
                <el-radio label="审核通过">通过</el-radio>
                <el-radio label="审核不通过">不通过</el-radio>
            </el-radio-group>
        </el-form-item>
    </el-form>
    </div>
    <div style="margin-top:20px;text-align:center">
        <el-button class="btn-normal" type="primary" icon="el-icon-edit" @click="saveAudit">确定</el-button>
        <!-- <el-button class="btn-normal" @click="close">返回</el-button> -->
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'WorkspaceJsonPurAuditDialog',
  props:{
    titleText:{type:String,default:''}
  },
  data() {
    return {
      auditVisible:false,
      dialogWidth:"450px",
      params:{},
      auditForm:{
        auditOpinion:"",
        auditResult:""
      },
      rules: {
        auditOpinion: [ { required: true, message: '请输入审批意见', trigger: 'blur' }],
        auditResult: [ { required: true, message: '请选择审核结果', trigger: 'change' }]
      }
    };
  },

  mounted() {

  },

  methods: {
    show(params){
     // this.rowId = row.getRowData().ID
      this.params = params
      this.auditVisible = true
    },
    saveAudit(){
        this.$refs['auditForm'].validate((valid) => {
          if (valid) {
            let infoParams = Object.assign(this.params.params, this.auditForm);
            this.$callApiParams(this.params.dataApiKey, infoParams, (result) => {
              if(result.success){
                this.$parent.$parent.init()
                this.auditVisible = false
              }
            })
          }
        })
    }
  },
};
</script>

<style lang="scss" scoped>

</style>
