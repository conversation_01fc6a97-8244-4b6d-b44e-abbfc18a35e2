<template>
    <el-dialog
        :close-on-click-modal="false"
        v-bind="$attrs"
        v-on="$listeners"
        @open="onOpen"
        @close="onClose"
        title="资格性评审"
        width="1600px"
        append-to-body
    >
    <div class="examineBox">

        <div v-if="isShowTop" class="examineTop">
            <div class="examineTopLeft">
                提示
            </div>
            <div class="examineTopRight">
                <el-select v-model="supplier" placeholder="供应商"  @change="changeSupplierData" style="width:100%">
                    <el-option
                        v-for="item in supplierOptions"
                        :key="item.id"
                        :label="item.label"
                        :value="item.id"
                        >
                    </el-option>
                </el-select>
                <el-select v-model="file" placeholder="投标文件" style="width:100%; margin-left: 5px;">
                    <el-option
                        v-for="item in fileOptions"
                        :key="item.url"
                        :label="item.label"
                        :value="item.url"
                        >
                    </el-option>
                </el-select>
            </div>
        </div>
        <div class="examineMain">
            <div class="examineMainLeft"
            v-loading="htmlLoading"
            element-loading-text="正在解析文件，请稍等。"
            element-loading-spinner="el-icon-loading"
            >
                <div v-if="fileView.showType==='html'">
                </div>
                <iframe :src="caigouUrl" width="100%" v-if="fileView.showType==='pdf'" height="99%" scrolling="yes">
                </iframe>
            </div>
            <div class="examineMainMid">
                <div v-if="fileView.showType==='html'">
                </div>
                <iframe :src="getViewUrl" width="100%" v-if="fileView.showType==='pdf'" height="99%" scrolling="yes">
                </iframe>

            </div>
            <div class="examineMainRight" style="margin-left: 5px;">
                <slot name="examineMainRight"  />
            </div>
        </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
    //评审，双pdf
    name: 'pur-examine-page',
    props:{
        isShowTop:{ type:Boolean, default: true}
    },
    data() {
        return {
            htmlLoading:false,
            supplierOptions:[],
            fileOptions:[],
            fileView:{
                showType:"pdf"
            },
            supplier:"",
            file:"",
            caigouUrl:"",
            getViewUrl:"",


        };
    },
    created(){

    },

    mounted() {

    },

    methods: {
        changeSupplierData(value){
            console.log(value)
        },
        getSupplierOpt(opt){
            console.log(opt)
        },

        onOpen(){
           // this.$emit('onOpen')
        },

        onClose(){

        },
        close(){
            this.$emit('update:visible', false)
        },

    },
};
</script>

<style lang="scss" scoped>
::v-deep .examineMainRight .footerBtn{
    .el-button{
        margin:0 5px 10px;
    }
}
::v-deep .examineMainRight .el-table  .highlight-row {
        background: #ECF5FF;
    }

.examineBox{
    height: 850px;
    .examineTop{
        display: flex;
        margin:5px 0;
        .examineTopLeft{
            width: 37%;
        }
        .examineTopRight{
            width: 63%;
            display: flex;
        }
    }
    .examineMain{
        display:flex;
        margin: 5px 0;
        height: 800px;
        .examineMainLeft{
            width: 37%;
        }
        .examineMainMid{
            width: 37%;
        }
        .examineMainRight{
            width: 26%;
        }
    }
}

::v-deep .finishColor{
    color:#67C23A
}

::v-deep.errorColor{
    color:#F56C6C
}

::v-deep .el-dialog .el-dialog__body{
      padding:0 10px;
}

::v-deep .el-dialog .el-dialog__header {
    padding: 10px 20px 10px;
}

::v-deep .el-dialog .el-dialog__title{
    font-size:16px;
}

::v-deep  .el-dialog__headerbtn{
    top: 16px;
}
</style>
