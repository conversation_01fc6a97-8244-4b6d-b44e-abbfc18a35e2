<template>
  <div class="project-card">
    <!-- 经费卡项目信息 -->
    <div v-if="data.expenseCardList && data.expenseCardList.length > 0" class="card-bottom">
      <div class="cus-title">经费卡信息</div>
      <el-table
              height="150"
              :data="data.expenseCardList"
              border
              ref="table"
              style="width: 100%"
            >
              <el-table-column
                v-for="(item, index) in expenseCardTableColumn"
                :key="index"
                :prop="item.prop"
                :label="item.label"
                :width="item.width"
                :align="item.align"
                :formatter="item.formatter"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span v-if="['expenseCardLimit','availableAmount','usedAmount'].includes(item.prop)">
                    {{ $formatMoney(scope.row[item.prop]) }}</span
                  >
                  <span v-else>{{ scope.row[item.prop] }}</span>
                </template>
              </el-table-column>
      </el-table>
    </div>
    <!-- 采购计划信息 -->
    <div v-if="data.plan && [data.plan].length > 0" class="card-bottom">
      <div class="cus-title">采购计划信息</div>
      <el-descriptions class="card-bottom" :column="2" border         
      :labelStyle="labelStyle"
      :contentStyle="{ width: '270px' }">
        <el-descriptions-item
            v-for="(item, key) in planEntityMap"
            :label="planEntityMap[key]"
            :key="key"
            :span="isSingleLine(key)"
        >
          {{ ["budgetAmount",'usedAmount', "availableAmount"].includes(key) ?$formatMoney(data.plan[key]):data.plan[key] }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 采购基本信息 -->
    <div v-if="data.projectInfo" class="card-bottom">
      <div class="cus-title">采购项目基本信息</div>
      <el-descriptions class="card-bottom" :column="2" border            
      :labelStyle="labelStyle"
      :contentStyle="{ width: '270px' }">
        <el-descriptions-item
            v-for="(item, key) in procurementProjectMap"
            :label="procurementProjectMap[key]"
            :key="key"
            :span="isSingleLine(key)"
        >
          {{ key=='unitPrice'||key=='budgetAmount'?$formatMoney(data.projectInfo[key]):data.projectInfo[key]}}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 商城采购信息 -->
    <div v-if="data.shoppingMallResult" class="card-bottom">
      <div class="cus-title">商城采购信息</div>
      <el-descriptions class="card-bottom" :column="2" border
        :labelStyle="labelStyle"
        :contentStyle="{ width: '270px' }"
      >
        <el-descriptions-item
            v-for="(item, key) in mallExtendEntityMap"
            :label="mallExtendEntityMap[key]"
            :key="key"
            :span="isSingleLine(key)"
        >
          {{ key == 'purchaseAmount' ? $formatMoney(data.shoppingMallResult[key]) : data.shoppingMallResult[key] }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 竞价采购信息 -->
    <div v-if="data.biddingResultEntity" class="card-bottom">
      <div class="cus-title">竞价采购信息</div>
      <el-descriptions class="card-bottom" :column="2" border
        :labelStyle="labelStyle"
        :contentStyle="{ width: '270px' }"
      >
        <el-descriptions-item
            v-for="(item, key) in biddingResultEntityMap"
            :label="biddingResultEntityMap[key]"
            :key="key"
            :span="isSingleLine(key)"
        >
          {{ data.biddingResultEntity[key] }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 零星采购信息 -->
    <div v-if="data.purchasingResultsRecordEntity">
      <div class="cus-title">零星采购信息</div>
      <!-- 货物类 -->
      <el-descriptions
          class="margin-top"
          :column="2"
          :labelStyle="labelStyle"
          :contentStyle="{ width: '270px' }"
          border
          v-if="data.purchasingResultsRecordEntity.recordType === '货物类'"
      >
        <el-descriptions-item
            v-for="(item, key) in recordEntityGoodsMap"
            :label="recordEntityGoodsMap[key]"
            :key="key"
            :span="isSingleLine(key)"
        >
          {{ key == 'transactionMoney' ? $formatMoney(data.purchasingResultsRecordEntity[key]) : data.purchasingResultsRecordEntity[key] }}
        </el-descriptions-item>
      </el-descriptions>
      <!-- 工程类 -->
      <el-descriptions
          class="margin-top"
          :column="2"
          :labelStyle="labelStyle"
          :contentStyle="{ width: '270px' }"
          border
          v-if="data.purchasingResultsRecordEntity.recordType === '工程类'"
      >
        <el-descriptions-item
            v-for="(item, key) in recordEntityengineerMap"
            :label="recordEntityengineerMap[key]"
            :key="key"
            :span="isSingleLine(key)"
        >
          {{key=='transactionMoney' ? $formatMoney(data.purchasingResultsRecordEntity[key]) : data.purchasingResultsRecordEntity[key] }}
        </el-descriptions-item>
      </el-descriptions>
      <!-- 服务类 -->
      <el-descriptions
          v-if="data.purchasingResultsRecordEntity.recordType === '服务类'"
          class="margin-top"
          :column="2"
          :labelStyle="labelStyle"
          :contentStyle="{ width: '270px' }"
          border
      >
        <el-descriptions-item
            v-for="(item, key) in serviceToMap"
            :label="serviceToMap[key]"
            :key="key"
            :span="isSingleLine(key)"
        >
          {{key=='transactionMoney' ? $formatMoney(data.purchasingResultsRecordEntity[key]) : data.purchasingResultsRecordEntity[key] }}
        </el-descriptions-item>
      </el-descriptions>
      <!-- 科研仪器设备类 -->
      <el-descriptions
          v-if="
          data.purchasingResultsRecordEntity.recordType === '科研仪器设备类'
        "
          class="margin-top"
          :column="2"
          :labelStyle="labelStyle"
          :contentStyle="{ width: '270px' }"
          border
      >
        <el-descriptions-item
            v-for="(item, key) in researchMap"
            :label="researchMap[key]"
            :key="key"
            :span="isSingleLine(key)"
        >
          {{ key== 'transactionMoney' ? $formatMoney(data.purchasingResultsRecordEntity[key]) : data.purchasingResultsRecordEntity[key] }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div>
      <template v-if="data.bidInfo || data.bidResult">
        <div class="cus-title">投标信息</div>
        <el-descriptions
            class="margin-top"
            :column="2"
            :labelStyle="labelStyle"
            :contentStyle="{ width: '270px' }"
            border
        >
          <!--有bidResult 才渲染 中标金额（元） 中标供应商 定标理由-->
<!--          <template v-if="data.bidResult">-->
          <template>
            <el-descriptions-item
                v-if="key=='bidMoney'?data.bidResult?true:false:true"
                v-for="(item, key) in bidInformationMap"
                :label="bidInformationMap[key]"
                :key="key"
                :span="isSingleLine(key)"
            >
              {{ key=='bidMoney' || key == 'bidUnitPrice' ? $formatMoney(data.bidResult[key]) : data.bidInfo[key] }}
            </el-descriptions-item>
          </template>
<!--          <template v-else>
            <el-descriptions-item
                v-for="(item, key) in bidNoInfoMap"
                :label="bidNoInfoMap[key]"
                :key="key"
                :span="isSingleLine(key)"
            >
              {{ data.bidInfo[key] }}
            </el-descriptions-item>
          </template>-->
        </el-descriptions>
      </template>
    </div>

    <el-table
      v-if="data.extractionResultList && data.extractionResultList.length > 0"
      :data="data.extractionResultList"
      border
      style="width: 100%; margin-top: -1px"
      height="calc(100% - 10rem)"
    >
      <el-table-column
        v-for="(item, index) in reviewColumn"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        :align="item.align"
      ></el-table-column>
    </el-table>
    <!-- 中标结果 -->
    <el-descriptions
      v-if="data.bidResult"
      class="margin-top"
      :column="2"
      :labelStyle="labelStyle"
      :contentStyle="{ width: '270px' }"
      border
    >
      <el-descriptions-item
        v-for="(item, key) in bidResultMap"
        :label="bidResultMap[key]"
        :key="key"
        :span="isSingleLine(key)"
      >
        {{ data.bidResult[key] }}
      </el-descriptions-item>
    </el-descriptions>
    <el-table
      v-if="data.attachmentList && data.attachmentList.length > 0"
      :data="data.attachmentList"
      @row-dblclick="onRowDblclick"
      border
      style="width: 100%; margin-top: -2px"
      height="calc(100% - 10rem)"
    >
      <el-table-column
        v-for="(item, index) in elTableColumn"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        :align="item.align"
      ></el-table-column>
    </el-table>
  </div>
</template>

<script>
import {downloadFile} from "@/api/file/file";

export default {
  // 采购项目卡片
  name: "pur-project-card",
  props: {
    id: {
      type: String,
      default: "",
    },
    // 卡片id 沟通传入的卡片id 查询卡片渲染数据
  },
  computed: {
    labelStyle() {
      return {
        textAlign: 'center', width: '180px'
      }
    }
  },
  data() {
    return {
      data: {
        attachmentList: [],
        extractionResultList: [],
      },
      elTableColumn: [
        {label: "采购文件附件", prop: "appendixType", width: "140", align: "center"},
        {label: "文件名称（双击下载）", prop: "attName", align: "center" },
        {label: "上传人", prop: "createUserName", width: "140", align: "center" },
        {label: "上传日期", prop: "uploadTime", align: "center" },
      ],
      // 评审专家姓名   专家类型、评标专业  所属部门/工作单位
      reviewColumn: [
        {label: "评审专家姓名", prop: "name", width: "180", align: "center" },
        {label: "专家类型、评标专业", prop: "evaluationMajor", width: "180", align: "center" },
        {label: "所属部门/工作单位", prop: "unit", align: "center" },
        {label: "手机号码", prop: "phone", align: "center" },
      ],
      // 经费卡项目信息
      // expenseCardEntityMap: {
      //   expenseCardNo: "经费卡项目编号",
      //   expenseCardPrincipal: "经费卡项目负责人",
      //   expenseCardName: "经费卡项目名称",
      //   expenseCardLimit: "经费额度（元）",
      //   expenseCardDeptName: "经费卡部门",
      //   expenseCardIndate: "经费有效期",
      // },
      // 采购项目基本信息
      procurementProjectMap: {
        purProjectInfoName: "采购项目名称",
        purProjectInfoNo: "采购项目编号",
        intendedCompletionTime: "拟完成时间",
        purchaseDepartmentName: "采购部门",
        oaApprovalNumber: "OA批准文号",
        projectManagerName: "采购项目经办人",
        projectLeaderName: "采购项目负责人",
        projectManagerPhone: "经办人电话",
        projectLeaderPhone: "负责人电话",
        purchaseClassification: "采购标的分类",
        isResearchEquipment: "是否科研设备",
        namesOfThingsName: "采购品目名称",
        isImportedEquipment: "是否进口设备",
        purchaseClassificationName: "采购标的名称",
        isSpecialEquipment: "是否特种设备",
        procurementMethodName: "采购方式",
        isInternationalBidding: "是否国际招标",
        unit: "计算单位",
        isRenewProject: "是否续签（追加）项目",
        quantity: "拟采购数量",
        isPrePurchaseDemonstration: "是否购前论证",
        unitPrice: "预计单价（元）",
        procurementCategory: "采购组织形式",
        budgetAmount: "预算金额（元）",
        purchaseActuator: "采购执行机构",
        itemOfExpenditure: "支出项目", // 单行
        bidEvaluation: "是否参派评标代表", // 单行
      },
      // 商城信息
      mallExtendEntityMap: {
        shoppingMallName: "商城名称",
        commercialTenantName: "商户名称",
        productAddress: "商城地址",
        productBrand: "商城品牌",
        productModel: "商城规格型号",
        productPrice: "商城报价",
        deliveryDate: "送货日期",
        productAmount: "购买数量",
        shippingContact: "送货联系人",
        purchaseAmount: "购买金额（元）",
        shippingContactPhone: "联系人电话",
        deliveryAddress: "送货地址", // 单行
      },
      // 竞价采购信息
      biddingResultEntityMap: {
        productBrand: "品牌",
        supplierInfoName: "成交供应商名称",
        bidUnitPrice: "成交单价",
        bidTime: "成交日期",
        productAmount: "购买数量",
        shippingContact: "送货联系人",
        bidMoney: "成交金额（元）",
        shippingContactPhone: "联系人电话",
        deliveryAddress: "送货地址", // 单行
      },
      // 货物类
      recordEntityGoodsMap: {
        goodsNameModel: "货物名称及型号",
        quantity: "购买数量",
        pivotalTechnologyTarget: "关键技术指标", //  单行
        purpose: "用途", //  单行
        transactionSupplier: "成交供应商名称",
        purchaseDate: "采购时间",
        transactionMoney: "成交金额（元）",
        currencyType: "币种",
      },
      // 工程类
      recordEntityengineerMap: {
        projectScope: "工程范围", //  单行
        quantities: "工程量", //单行
        purpose: "用途", //  单行
        transactionSupplier: "成交供应商名称",
        purchaseDate: "采购时间",
        transactionMoney: "成交金额（元）",
        currencyType: "币种",
      },
      // 服务类
      serviceToMap: {
        serviceContent: "服务内容", //  单行
        serviceDeadline: "服务期限", //  单行
        purpose: "用途", //  单行
        transactionSupplier: "成交供应商名称",
        purchaseDate: "采购时间",
        transactionMoney: "成交金额（元）",
        currencyType: "币种",
      },
      // 科研仪器类
      researchMap: {
        goodsNameModel: "货物名称及型号",
        quantity: "购买数量",
        pivotalTechnologyTarget: "关键技术指标", //  单行
        purpose: "用途",
        transactionSupplier: "成交供应商名称",
        purchaseDate: "采购时间",
        transactionMoney: "成交金额（元）",
        currencyType: "币种",
        affirmReason: "科研仪器设备的认定理由",
        affirmPerson: "认定人",
        approvePerson: "审核人",
      },
      // 投标信息
      bidInformationMap: {
        purchaseActuator: "招标执行机构",
        bidEvaluationRepresentativeNames: "评标代表姓名",
        agencyName: "招标代理机构",
        isEvaluationSeparation: "是否评定分离",
        reviewMethod: "评审方法",
        reconnaissanceTime: "现场踏勘时间",
        openTime: "开标时间",
        bidMoney: "中标金额（元）"
      },
      bidResultMap: {
        supplierInfoName: "中标供应商",
        bidReason: "定标理由",
      },
      // 经费卡列配置
      expenseCardTableColumn: [
        {
          prop: "expenseCardNo",
          label: "经费卡项目编号",
          align: "center",
          width: 200,
        },
        {
          prop: "expenseCardName",
          label: "经费卡项目名称",
          align: "center",
          width: 180,
        },
        {
          prop: "expenseCardLimit",
          label: "经费额度（元）",
          align: "center",
          width: 160,
        },
        {
          prop: "availableAmount",
          label: "剩余可用金额(元)",
          align: "center",
          width: 160,
        },
        {
          prop: "usedAmount",
          label: "本次使用金额(元)",
          align: "center",
          editable: true,
          width: "auto",
          minWidth: 180,
        },
      ],
      // 采购计划列配置
      // govCardTableColumn: [
      //   {
      //     prop: "procurementPlanNumber",
      //     label: "采购计划编号",
      //     align: "center",
      //     width: 200,
      //   },
      //   {
      //     prop: "procurementPlanName",
      //     label: "采购计划名称",
      //     align: "center",
      //     width: 180,
      //   },
      //   {
      //     prop: "budgetAmount",
      //     label: "计划金额",
      //     align: "center",
      //     width: 160,
      //   },
      //   {
      //     prop: "availableAmount",
      //     label: "剩余可用金额(元)",
      //     align: "center",
      //     width: 160,
      //   },
      //   {
      //     prop: "usedAmount",
      //     label: "本次使用金额(元)",
      //     align: "center",
      //     editable: true,
      //     width: "auto",
      //     minWidth: 180,
      //   },
      // ],
      planEntityMap: {
        procurementPlanNumber: "采购计划编号",
        procurementPlanName: "采购计划名称",
        budgetAmount: "计划金额",
        availableAmount: "剩余可用金额",
        usedAmount: "本次使用金额",
      },
      // 投标信息 不包含 中标金额（元） 中标供应商 定标理由 bidNoInfo
    };
  },
  mounted() {
    // this.init();
  },
  methods: {
    init() {
      // 初始化清空值 保证无旧数据污染
      this.data = {};
      this.$callApiParams("cardPurProjectInfo",
          {
            bizid: this.id,
          },
          (result) => {
            if (result.data) {
              this.data = result.data;
            }
            return true;
          }
      );
    },
    // 判断是否单行
    isSingleLine(key) {
      // 需要设置成单行的字段 都在此处添加
      const isIcludes = [
        "purProjectInfoName",
        "itemOfExpenditure",
        "purpose",
        "bidEvaluation",
        "deliveryAddress",
        "serviceContent",
        "serviceDeadline",
        "pivotalTechnologyTarget",
        "projectScope",
        "quantities",
        "supplierInfoName",
        "bidReason",
      ];
      return isIcludes.includes(key) ? 2 : 0;
    },
    // 双击下载
    onRowDblclick(row, column, event) {
      if (column.property === "attName") {
        this.fileDownload(row);
      }
    },
    // 下载功能
    fileDownload({ attId }) {
      if (attId) {
        downloadFile(attId).then((res) => {
          const str = res.headers["content-disposition"];
          if (str) {
            const index = str.lastIndexOf("=");
            const str1 = window.decodeURI(str.substring(index + 1, str.length));
            this.handleFileDownloadRes(res, str1);
          } else {
            this.$message.error("文件信息不存在");
          }
        });
      }
    },
    handleFileDownloadRes(res, str1) {
      if (!res.data) {
        this.$message.error("文件信息不存在");
        return;
      }
      var filename = str1 || undefined;
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // 检测是否在IE浏览器打开
        window.navigator.msSaveOrOpenBlob(new Blob([res.data]), filename);
      } else {
        // 谷歌、火狐浏览器
        let url = "";
        if (
            window.navigator.userAgent.indexOf("Chrome") >= 1 ||
            window.navigator.userAgent.indexOf("Safari") >= 1
        ) {
          url = window.webkitURL.createObjectURL(new Blob([res.data]));
        } else {
          url = window.URL.createObjectURL(new Blob([res.data]));
        }
        const link = document.createElement("a");
        const iconv = require("iconv-lite");
        iconv.skipDecodeWarning = true; // 忽略警告
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.project-card {
  width: 910px;
  padding: 0 10px 0 0;
}

.cus-title {
  background-color: #dbe6fb;
  border: 1px solid #dddddd;
  color: #000;
  padding: 13px 10px;
  text-align: center;
  font-size: 15px;
  font-weight: 700;
  line-height: 13px;
}
.card-bottom {
  margin-bottom: 20px;
}

::v-deep {
  .el-descriptions-item__label.is-bordered-label {
    background-color: transparent !important;
  }
  .el-descriptions-item__label {
    font-size: 14px;
    color: #000!important;
  }
  .el-descriptions-item__content {
    font-size: 13px;
  }
  .el-descriptions-item__cell {
    color: #000;
  }
  .el-table__header-wrapper{
    background: #dbe6fb !important;
  }
}
</style>
