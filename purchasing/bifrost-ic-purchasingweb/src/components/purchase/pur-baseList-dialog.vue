<template>
    <el-dialog ref="auditDialog" :title="`${title}`" :width="`${dlgWidth}px`" append-to-body class="dialog-style1" :close-on-click-modal="false" :visible.sync="baseListVisible">
        <b-curd ref="curdList" :style="{height:dlgHeight+'px'}"/>
    </el-dialog>
</template>

<script>
export default {
    name: 'pur-baseList-dialog',
    data() {
        return {
            baseListVisible:false,
            title:"",
            dlgWidth: 0,
            dlgHeight: 0,
            params:{}
        };
    },

    mounted() {

    },

    methods: {
        show(params){
            console.log(params)
            this.baseListVisible = true
            this.params = params
            if (this.$isEmpty(this.params.params.dataApiKey)) {
                this.$message.error('dataApiKey不能为空')
                return
            }
            if (this.params.title) {
                this.title = this.params.title
            }

            this.dlgWidth = 960
            if (this.params.dlgWidth) {
                this.dlgWidth = this.params.dlgWidth
            }

            this.dlgHeight = 600
            if (this.params.dlgHeight) {
                this.dlgHeight = this.params.dlgHeight
            }
            console.log(this.params)
            let initParams = {}
            initParams.params = params.params || {}
            initParams.hiddenButtons = ['新增','修改','删除', '详情']
            initParams.callbackRowDblclick= () => {}
            console.log(initParams)
            this.$nextTick(()=>{
                this.$refs.curdList.init(initParams)
            })
        }
    },
};
</script>

<style lang="scss" scoped>

</style>
