<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> 915540019qq.com
 * @Date: 2023-12-25 18:05:24
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-07-31 10:28:01
 * @FilePath: \bifrost-ic-purchasingweb\src\components\purchase\pur-project-cardDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog ref="noticeDialog" :title='titleText' append-to-body width="970px" class="dialog-tab-style"
             :close-on-click-modal="false" :visible.sync="dlgVisible" @close="handClose">
    <div style="height:750px; overflow-y:auto;">
      <pur-project-card :id="id" ref="cardRef"/>
    </div>
  </el-dialog>
</template>

<script>
import PurProjectCard from './pur-project-card'

export default {
  // 采购项目卡片
  name: 'pur-project-card-dialog',
  components: {
    PurProjectCard
  },
  data() {
    return {
      titleText: '采购项目卡片',
      dlgVisible: false,
      id: null
    }
  },
  mounted() {

  },
  methods: {
    show(id) {
      this.id = null
      this.id = id
      this.dlgVisible = true
      this.$nextTick(() => {
        this.$refs.cardRef.init()
      })
    },
    handClose() {
      this.dlgVisible = false
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
