<template>
    <div class="public-table">
      <el-table
        ref="elTable"
        :data="data"
        :height="initHeight"
        :style="{ 'min-height': minHeight }"
        v-loading="loading"
        :border="border"
        :stripe="stripe"
        :highlight-current-row="highlightCurrentRow"
        :default-sort="defaultSort"
        :show-summary="showSummary"
        :summary-method="getSummaries"
        :row-class-name="tableRowClassName"
        :row-style="rowStyle"
        @row-click="rowClick"
        @row-dblclick="rowDblclick"
        @select="select"
        @selection-change="selectionChange"
        @select-all="selectAll"
        @cell-dblclick="cellClick"
        @header-dragend="doLayout()"
      >
        <el-table-column
          type="index"
          v-if="columnIndex"
          width="50"
          align="center"
          label="序号"
          :fixed="fixed"
        ></el-table-column>
        <el-table-column
          type="selection"
          v-if="columnSelection"
          :selectable="selectInit"
          align="center"
          :fixed="fixed"
        ></el-table-column>

        <el-table-column
          type="column.type"
          v-for="(column, index) in columns"
          v-if="getShow(column)"
          :key="index"
          show-overflow-tooltip
          :width="columns.width"
          :min-width="column.minWidth"
          :align="column.align"
          header-align="center"
          :label="column.label"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :prop="column.prop"
        >
        <template slot-scope="scope">
          <template v-if="column.slot&&column.slot=='open'" >
            <div v-if="column.prop=='content'||column.prop=='type'||column.prop=='scoringItems'||column.prop=='weight'">{{scope.row[column.prop]}}</div>
            <div v-else>
               <i v-if="scope.row[column.prop].examineResult=='通过'" class="el-icon-check cellSuccess"></i>
               <i v-if="scope.row[column.prop].examineResult=='不通过'" class="el-icon-close cellError"></i>
               <p v-if="scope.row[column.prop].examineResult!=''">{{scope.row[column.prop].cause}}</p>
               <p v-if="scope.row[column.prop].examineResult==''" style="margin-top:10px;">尚未审查<br/>(双击审查)</p>
            </div>
          </template>
          <template v-else-if="column.slot&&column.slot=='score'" >
            <div v-if="column.prop=='type'||column.prop=='scoringItems'||column.prop=='weight'">{{scope.row[column.prop]}}</div>
            <div v-else>
               <p v-if="scope.row[column.prop].itemScore!=''" style="margin:0;">{{scope.row[column.prop].itemScore}}</p>
               <p v-if="scope.row[column.prop].itemScore!=''&&scope.row[column.prop].cause!=''" style="margin:0;">{{scope.row[column.prop].cause}}</p>
               <p v-if="scope.row[column.prop].itemScore==''" style="margin-top:10px;">尚未审查<br/>(双击审查)</p>
            </div>
          </template>
          <template v-else-if="column.slot&&column.slot=='calendar'" >
            <div v-if="column.prop=='room'">{{scope.row[column.prop]}}</div>
            <div v-else>
              <div v-if="scope.row[column.prop].length>0">
                <el-popover placement="right" width="350" trigger="hover">
                  <div>
                    <div>提示：点击单元格设置开标时间</div>
                    <ul v-for="(item, index) in scope.row[column.prop]" :key="index" style="margin:12px 0">
                      <li style="color:#409EFF">开标时间：{{item.openTime.substr(11, 5)}}</li>
                      <li>招标编号：{{item.biddingNo}}</li>
                      <li>项目名称：{{item.projectInfoName}}</li>
                    </ul>
                  </div>
                  <div slot="reference">
                    <div v-for="(item, index) in scope.row[column.prop]" :key="index">{{item.openTime.substr(11, 5)}}</div>
                  </div>
                </el-popover>
              </div>
            </div>
          </template>
          <template v-else-if="column.formatter">
            <span v-html="column.formatter(scope.row, column)"></span>
          </template>
          <template v-else>
            <span>{{scope.row[column.prop]}}</span>
          </template>
        </template>
        </el-table-column>
        <slot></slot>
      </el-table>
    </div>
  </template>

  <script>
  export default {
    name: "public-table",
    props: {
      fixed: {
        default: false
      },
      stripe: {
        type: Boolean,
        default: true
      },
      border: {
        type: Boolean,
        default: true
      },
      highlightCurrentRow: {
        type: Boolean,
        default: true
      },
      columnSelection: {
        type: Boolean,
        default: false
      },
      columnIndex: {
        type: Boolean,
        default: true
      },
      isDownload: {
        type: Boolean,
        default: false
      },
      loading: {
        type: Boolean,
        default: false
      },
      isCustom: {
        type: Boolean,
        default: false
      },
      sumParams: {
        type: Object,
        default: () => {}
      },
      sumsAmount: {
        type: Object,
        default: () => {}
      },
      data: {
        type: Array,
        default: () => []
      },
      defaultSort: {
        type: Object,
        default: function() {
          return {};
        }
      },
      rowStyle:{
        type: Object,
        default: function() {
          return {};
        }
      },
      columns: {
        type: Array,
        default: () => []
      },
      initHeight: {
        type: Number,
      },
      minHeight: {
        type: String,
        default: "80px"
      },
      isSelfAdaption: {
        // 是否使用自适应高度
        type: Boolean,
        default: false
      },
      maxHeight: {
        default: 600
      },
      showSummary: {
        type: Boolean,
        default: false
      },
      onRowSelect: {
        type: Boolean,
        default: false
      },
      onRowOneSelect: {
        type: Boolean,
        default: false
      },
      hasHeight: {
        // 是否定高
        type: Boolean,
        default: true
      }
    },
    computed: {
    //   documentClientHeight: {
    //     get() {
    //       return this.$store.state.common.documentClientHeight;
    //     }
    //   }
    },
    data() {
      return {
        selectList: [],
        addColumns: [],
        addData: [],
        stopData: []
      };
    },
    watch: {},
    methods: {
      getShow(column) {
        // 显示隐藏列
        if (column.isShow !== undefined && column.isShow != null) {
          return column.isShow;
        } else {
          return true;
        }
      },

      getSummaries(param, data) {
        if (this.isCustom) {
          const { columns, data } = param;
          const sums = [];
          columns.forEach((column, index) => {
            if (index === 0) {
              sums[index] = "合计";
              return;
            }
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
              // 前端合计，取数值累加
              // 后端做合计，取数值直接渲染
            }
          });
          if (sums.length > 0) {
            // 如果合计行有数据，操作dom修改合计行由于数值过大后自动换行的问题
            var footer = document.querySelector(".el-table__footer-wrapper");
            var cell = footer.querySelectorAll(".cell");
            cell.forEach(v => {
              v.style.wordBreak = "normal";
              v.style.textAlign = "right";
            });
            if (this.fixed) {
              // 解决处理了列换行后，fix 固定列盒子由组件内部控制导致的自动换行
              var fixFooter = document.querySelector(
                ".el-table__fixed-footer-wrapper"
              );
              if (fixFooter) {
                var fixCell = fixFooter.querySelectorAll(".cell");
                fixCell.forEach(v => {
                  v.style.wordBreak = "normal";
                  v.style.textAlign = "right";
                });
              }
            }
            this.doLayout();
          }
          return sums;
        } else {
          this.$emit("getSummaries", param, data);
        }
      },
      /* 重新调整表格高度，解决表格错位 */
      doLayout() {
        this.$nextTick(() => {
          this.$refs.elTable.doLayout();
        });
      },
      rowClick(row, event, column) {
        if (this.onRowSelect) {
          this.$refs.elTable.toggleRowSelection(row);
        }
        //双击是否选中
        // if (this.onRowSelect) {
        //   this.$refs.elTable.toggleRowSelection(row);
        // }else if(this.onRowOneSelect){
        //   if (this.selectList[0] == row) {
        //   // 取消
        //   this.selectList = [];
        //   this.$refs.elTable.clearSelection();
        // } else {
        //   // 选择
        //   this.selectList = row;
        //   this.$refs.elTable.clearSelection();
        //   this.$refs.elTable.toggleRowSelection(row, true);

        // }
        // }
        this.$emit("rowClick", row, this.selectList, event, column);
      },
      rowDblclick(row, event) {
        this.$emit("rowDblclick", row, event);
      },
      selectionChange(selection) {
        this.selectList = selection;
        this.$emit("selectionChange", selection);
      },
      select(selection, row) {
        this.$emit("tbSelect", selection, row);
        return selection;
      },
      selectAll(selection) {
        this.$emit("tbSelectAll", selection);
      },
      clearSelection() {
        this.$refs.elTable.clearSelection();
      },
      downloadClick(row) {
        this.$emit("downloadClick", row);
      },
      selectInit(selection, row) {
        // 如果是合计行则不能勾选的方法
        if (
          selection.deprMonth === "合计" ||
          this.stopData.some(el => {
            return el === selection.bizid;
          })
        ) {
          return false;
        } else {
          return true;
        }
      },
      stopDataFun(data) {
        const arr = [];
        data.map(v => {
          arr.push(v.bizid);
        });
        // console.log(arr)
        this.stopData = arr;
      },
      hanldeResize() {
        this.doLayout();
      },
      fileDown(){

      },
      tableRowClassName({ row, rowIndex }){
         row.index = rowIndex;
      },
      cellClick(row, column, cell, event){
        console.log(row[column.property])
        console.log(row)
        console.log(column)
        console.log(cell)
        console.log(event)
        let date = new Date()
        const yy = date.getFullYear()
        const mm = date.getMonth() + 1
        const dd = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
        const mz = mm < 10 ? '0' + mm : mm
        let dateDay = yy + '-' + mz + '-' + dd
        let beforeDay = this.$calculateDateDifference(dateDay,column.property)
        if(beforeDay<0){
          this.$message.warning('日期选择错误，只能选择当日及当日之后的日期!')
        }else{
          this.$emit("handlOpenClick", row, column)
        }
        //if(column.property=='content'){
        //  return
        //}else{
        //  const cellData = row[column.property]
        //  console.log(cellData)
        //}
      }
    },
    mounted() {
      window.addEventListener("resize", this.hanldeResize);
    },
    activated() {
      // 防止页面切换时，列表错位
      this.doLayout();
    },
    beforeDestroy() {
      window.removeEventListener("resize", this.hanldeResize);
    }
  }
</script>

<style lang="scss" scoped>
.public-table {
  .el-table__empty-block {
    min-height: 136px;
    // background-position: 785px 50%;
  }
}

.public-table ::v-deep .el-table .cell.el-tooltip{
  white-space:pre-wrap;
  text-align: center;
}


.cellSuccess{
  color: #67C23A;
  font-size: 20px;
}

.cellError{
  color: #F56C6C;
  font-size: 20px;
}
</style>

