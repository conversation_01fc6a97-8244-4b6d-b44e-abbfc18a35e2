<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> 915540019qq.com
 * @Date: 2023-12-25 10:09:54
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-05-29 10:04:33
 * @FilePath: \bifrost-ic-purchasingweb\src\components\purchase\pur-names-things.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    ref="namesThingsDialog"
    :title="titleText"
    id="dynamicDlg-namesThings"
    append-to-body
    class="shareDialog"
    :close-on-click-modal="false"
    :visible.sync="namesThingsVisible"
  >
    <b-curd ref="curdList" style="height: 600px; margin-top: -20px"></b-curd>
    <div style="margin-top: 30px; text-align: center">
      <el-button
        class="btn-normal"
        type="primary"
        icon="el-icon-edit"
        @click="affirm"
        >确定</el-button
      >
      <el-button class="btn-normal" @click="close">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  //采购品目
  name: "pur-names-things",

  data() {
    return {
      titleText: "采购品目",
      namesThingsVisible: false,
    };
  },

  mounted() {},

  methods: {
    show() {
      this.namesThingsVisible = true;
      this.$nextTick(() => {
        this.init();
      });
      this.$setDlgSize(this, "namesThingsDialog", 1380, 750);
    },
    init() {
      var initParams = {};
      initParams.params = {
        dataApiKey: "selectNamesOfThingsPageData",
        deleteApiKey: "deleteNamesOfThings",
      };
      (initParams.searchForm = [
        '年度:ANNUAL_eq:下拉:#全部:"",2023,2022,2021',
        "品目编号:SERIAL_NUMBER_like:文本",
        "品目名称:NAME_like:文本",
      ]),
        (initParams.hiddenButtons = ["新增", "修改", "删除", "详情", "导出"]);
      initParams.leftTreeApiKey = "selectNamesOfThingsTree";
      initParams.addParamsLeftTreeNodeClick = (exParams, treeNode) => {
        exParams["SERIAL_NUMBER_like"] = treeNode.exData.serialNumber;
        exParams["onlyEndNode"] = "是";
      };
      initParams.exportExcelName = "采购品目";
      initParams.isShowOrderNumber = true;
      initParams.callbackRowDblclick = () => {};
      this.$refs.curdList.init(initParams);
    },

    affirm() {
      const rows = this.$getTableSelection(
        this.$refs.curdList.$refs.baseList.$refs.table
      );
      if (rows.length === 1) {
        // 暴露的方法名不能随便修改,防止使用它的组件取不到值
        this.$emit("getThings", rows);
        this.namesThingsVisible = false;
      } else if (rows.length > 1) {
        this.$message.warning("请勾选一条数据进行操作!");
      } else {
        this.$message.error("请选择品目名称");
      }
    },

    close() {
      this.namesThingsVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
