<template>
    <div class="evaluation-page">
    <div v-show="isShowHeader" class="flowHeader">
        <ul class="cssNav">
            <li  :class="item.state" v-for="(item,index) in navName" :key="index">{{item.name}}</li>
        </ul>
        <div v-if="typeRight" class="ident">
            <div class="identLi"><div class="gray"></div><div>未开始</div></div>
            <div class="identLi"><div class="blue"></div><div>进行中</div></div>
            <div class="identLi"><div class="green"></div><div>已完成</div></div>
        </div>
    </div>
    <!-- 不是步骤，只有一个按钮 -->
    <div v-if="!hasSteps" >
        <div class="noSteps"><div class="navButton finishBtn">{{noStepsText}}</div></div>
        <slot name="noStepContent"/>
    </div>
    <!-- 带步骤 -->
    <div v-if="hasSteps" class="stepsBox">
        <div class="stepsBoxHeader">
            <el-steps class="stepsType"  :style="{width:stepsObj.width}" :active="active" finish-status="success" process-status="finish"  simple  >
                <el-step :title="item.name" style="width:200px" @click.native="on_click(index)" v-for="(item,index) in stepsObj.stepData" :key="index" ></el-step>
            </el-steps>
            <div v-if="!typeRight" class="ident">
                <div class="identLi"><div class="gray"></div><div>未开始</div></div>
                <div class="identLi"><div class="blue"></div><div>进行中</div></div>
                <div class="identLi"><div class="green"></div><div>已完成</div></div>
            </div>
        </div>
        <keep-alive>
            <component ref="stepsComponent" :is="iscomponentName" @sendValue="sendValue" :jieObj="jieObj" />
        </keep-alive>
    </div>
    <div class="stepsBoxFooter">
        <slot name="footerButton"/>
    </div>
    </div>
</template>

<script>
export default {
    name: 'pur-steps-nav',
    props: {
        typeRight:{
            type: Boolean,
            default: true
        },
        projectStatus:{
            type: String,
            default: ""
        }
        // navName: {
        //     type: Array,
        //     default() {
        //     return [];
        //     }
        // },

        // stepsObj:{
        //     type: Object,
        //     default() {
        //     return {};
        //     }
        // }
    },
    data() {
        return {
            active:0,
            iscomponentName:"",
            navName:[],    //顶部数据
            stepsObj:{},   //步骤数据
            noStepsText:undefined,
            hasSteps:true,
            jieObj:{},
            isShowHeader:true,
            isStateData:{}
            // stepsObj:{
            //     stepData:[{
            //         name:"采购计划",
            //         pageName:"caigoujihua"
            //     },
            //     {
            //         name:"采购申报",
            //         pageName:"caigoujihua1"
            //     },
            //     {
            //         name:"确认采购文件",
            //         pageName:"caigoujihua2"
            //     },
            //     {
            //         name:"采购xx",
            //         pageName:"caigoujihua3"
            //     },{
            //         name:"确认采购文件",
            //         pageName:"caigoujihua2"
            //     },{
            //         name:"供应商签到、推选组长",
            //         pageName:"caigoujihua2"
            //     },],
            //     width:'1300px'
            // },


        };
    },

    mounted() {
        this.projectInit()
    },

    methods: {
        projectInit(){

            this.expertState()
        },

        expertState(){
            var userInfo = JSON.parse(window.sessionStorage.getItem('userInfo'))
            this.ws = this.websocket('infoBidOpeningRecordsTimer-' + userInfo.id + '-1722558489101971458', (msg) => {
            var data = JSON.parse(msg.data)
            this.isStateData = JSON.parse(msg.data)
            console.log(this.isStateData)
            console.log('收到消息', data)
            })
        },

        show(params){
            if(!params.navName||params.navName.length==0){
                this.isShowHeader = false
            }
            this.navName = params.navName || []
            this.stepsObj = params.stepsObj || {}
            this.noStepsText = params.noStepsText || ''
            this.hasSteps = params.hasSteps? params.hasSteps: false
            if(this.hasSteps){
                this.iscomponentName = this.stepsObj.stepData[0].pageName
            }
        },

        on_click(index){
            this.active = index
            this.iscomponentName = this.stepsObj.stepData[index].pageName
        },

        getComponent() {
            console.log(this.active)
            console.log(this.$refs.stepsComponent)
            console.log(this.$refs.stepsComponent[this.active])
            this.$refs.stepsComponent.save()
        },

        sendValue(val){
            console.log(val)
            this.jieObj = val
        }
        // prev(){
        //     if(this.active>0){
        //         this.active--;
        //     }
        //     this.iscomponentName =  this.stepsObj.stepData[this.active].pageName
        // },
        // next() {
        //     if (this.active++ > ( this.stepsObj.stepData.length-2)) this.active = this.stepsObj.stepData.length-1;
        //     this.iscomponentName =  this.stepsObj.stepData[this.active].pageName
        // },

    },
};
</script>

<style lang="scss" scoped>
.noSteps{
    padding:10px 20px;
    background-color: #E4F0FC;
}

.navButton{
    width:170px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    border-radius: 15px;
}

.finishBtn{
    color: #fff;
    font-size:14px;
    background: #1890FF;
    box-shadow: 0px 3px 5px#a3a3a3 ;
    -moz-box-shadow: 0px 3px 5px #a3a3a3 ;
    -webkit-box-shadow: 0px 3px 5px #a3a3a3 ;
}

.evaluation-page{
    margin:0 20px;
    /* border: 1px solid #EBEEF5; */
}
.stepsBoxHeader{
    background-color: #E4F0FC;
    position: relative;
    ::v-deep .el-steps--simple{
        padding: 10px;
        background-color: #E4F0FC;
    }
    .ident{
        position: absolute;
        top: 13px;
        right: 10px;
    }
}
.stepsType {

    ::v-deep  .el-step__icon{
        display: none;
    }
    ::v-deep .el-step.is-simple .el-step__title{
        border-radius: 15px;
        width: 340px;
        padding:4px 0;
        text-align: center;
        cursor: pointer;
    }
    ::v-deep .el-step__title.is-success{
        color: #fff;
        font-size:14px;
        background: #009966;
    }
    ::v-deep .el-step__title.is-success + .el-step__arrow::after, ::v-deep .el-step__title.is-success + .el-step__arrow::before
    {
        background: #009966;
    }
    ::v-deep .el-step.is-simple .el-step__arrow::after, ::v-deep.el-step.is-simple .el-step__arrow::before{
        left:190px;
    }
    ::v-deep .el-step.is-simple .el-step__main{
        flex-grow:0;
        -webkit-box-flex:0
    }
    ::v-deep .el-step.is-simple .el-step__arrow{
        flex-grow:0;
        -webkit-box-flex:0
    }

    ::v-deep .el-step:last-of-type.is-flex{
        .el-step__title.is-wait{
            width: 180px;
        }
        .el-step__title.is-finish{
            width: 180px;
        }
    }


    ::v-deep .el-step__title.is-finish{
        color: #fff;
        font-size:14px;
        padding:6px 0 !important;
        background: #1890FF;
        box-shadow: 0px 3px 4px#a3a3a3 ;
        -moz-box-shadow: 0px 3px 4px #a3a3a3 ;
        -webkit-box-shadow: 0px 3px 4px #a3a3a3 ;
    }

    ::v-deep .el-step__title.is-wait{
        color: #fff;
        font-size:14px;
        background: #D7D7D7;
    }
}
.flowHeader{
    display: flex;
    height: 30px;
    justify-content:space-between;

}
.ident{
    width:260px;
    display: flex;
    justify-content:space-between;
    .identLi{
        display: flex;
        align-items:center;
    }
    .gray{
        width: 15px;
        height: 15px;
        background-color: #D7D7D7;
    }
    .blue{
        width: 15px;
        height: 15px;
        background-color: #409EFF;
    }
    .green{
        width: 15px;
        height: 15px;
        background-color: #67C23A;
    }
}
.cssNav li{
    padding: 0px 40px 0 55px;
    line-height: 30px;
    background: #D7D7D7;
    display: inline-block;
    color: #fff;
    position: relative;
    margin-right: 20px;
}
.cssNav li:after{
    content: '';
    display: block;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 15px solid #D7D7D7;
    position: absolute;
    right: -15px;
    top: 0;
    z-index: 10;
}
.cssNav li:before{
    content: '';
    display: block;
    border-top: 15px solid #D7D7D7;
    border-bottom: 15px solid #D7D7D7;
    border-left: 15px solid #fff;
    position: absolute;
    left: 0px;
    top: 0;
}

.cssNav li:first-child{

    padding-left: 25px;
}
.cssNav li:last-child,.cssNavEnd{


}
.cssNav li:first-child:before{
    display: none;
}
.cssNav li:last-child:after,.cssNavEnd:after{
    display: none;
}

.cssNav li.active {
    background-color: #E4F0FC;
    color: #1890FF;
    font-weight:700;
}
.cssNav li.active:after {
    border-left-color: #E4F0FC;
}

.cssNav li.active:before {
    border-top-color: #E4F0FC;
    border-bottom-color: #E4F0FC;
}

.cssNav li.finish {
    background-color: #E6F6CE;
    color: #009966;
    font-weight:700;
}

.cssNav li.finish:after {
    border-left-color: #E6F6CE;
}

.cssNav li.finish:before {
    border-top-color: #E4F0FC;
    border-bottom-color: #E4F0FC;
}


</style>
