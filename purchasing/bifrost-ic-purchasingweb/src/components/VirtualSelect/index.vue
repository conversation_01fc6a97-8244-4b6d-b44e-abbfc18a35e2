<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-23 13:26:48
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-11-19 17:04:59
-->
<template>
  <div>
    <el-select
      ref="vSelect"
      v-model="valueTitle"
      :clearable="clearable"
      :disabled="disabled"
      :placeholder="placeholder"
      :multiple="multiple && showCheckbox"
      :collapse-tags="multiple && showCheckbox"
      @clear="handleClear"
      :filter-method="selectFilter"
      filterable
      popper-class="virtual-select"
      @visible-change="handleVisible"
      @change="handleChange"
      @remove-tag="removeTag"
      style="width: 100%;"
      @focus="focus"
    >
      <el-option
        ref="elOptionItem"
        :value="valueTitle"
        class="virtualOption"
        style="height: auto;"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, .6)"
      >
        <VirtualTree
          ref="virtualTree"
          :tree="optionsDuplicate"
          :showCheckbox="showCheckbox"
          v-bind="$attrs"
          v-on="$listeners"
          @check="handleCheck"
          @node-click="handleNodeClick"
          @nodeClickBlur="nodeClickBlur"
        >
        </VirtualTree>
      </el-option>
    </el-select>
  </div>
</template>

<script>
  import VirtualTree from '../VirtualTree'

  export default {
    name: 'VirtualSelect',
    props: {
      tree: {
        required: true,
        type: Array,
        default() {
          return []
        },
      },
      value: {
        required: true,
        type: [String, Number, Array],
        default() {
          const val = this.showCheckbox ? [] : ''
          return val
        },
      },
      clearable: {
        type: Boolean,
        default: true,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      multiple: {
        type: Boolean,
        default: true,
      },
      /**
       * 2023/2/15添加multiple。
       * 添加showCheckbox属性防止使用单选时父组件未添加check事件而引发的问题
       */
      showCheckbox: {
        type: Boolean,
        default: false,
      },
      placeholder: {
        type: String,
        default: '请选择',
      },
    },
    components: {
      VirtualTree,
    },
    data() {
      return {
        valueTitle: '',
        valueId: this.value,
      }
    },
    watch: {
      tree: {
        handler: function(val) {
          this.optionsDuplicate = JSON.parse(JSON.stringify(val))
        },
        deep: true,
        immediate: true,
      },
      value: {
        handler: function(val) {
          this.valueId = val
        },
        deep: true,
        immediate: true,
      },
    },
    methods: {
      /**
       * @description: 下拉框点击后失去焦点
       */
      nodeClickBlur() {
        this.$refs.vSelect.blur()
      },
      focus(str) {
        if(this.valueTitle.length === 0 || this.valueTitle === '') {
          this.$refs.virtualTree.filter()
          return
        }
        !str && this.$refs.virtualTree.filter(str)
      },
      /**
       * @description: 下拉框自定义搜索方法，调用子组件filter方法
       * @param {string} str 输入框输入值
       */
      selectFilter(str) {
        this.$refs.virtualTree.filter(str)
      },
      removeTag(val) {
        this.$refs.virtualTree.handleCheckChange(false, val, false)
      },
      /**
       * @description: 可清空的单选模式下用户点击清空按钮时触发，当前用于显示子组件所有节点
       */
      handleClear() {
        // this.$refs.virtualTree.showAllNode();
        this.$refs.virtualTree.clearSelected()
        this.valueTitle = ''
        this.$emit('cleatSelect')
      },
      /**
       * @description: 下拉框出现/隐藏时触发, 当前用于处理子组件打开白屏问题
       * @param {Boolean} isVisible 出现为 true，隐藏则为 false
       */
      handleVisible(isVisible) {
        if (!isVisible) return
        this.$refs.virtualTree.updateView()
      },
      /**
       * @description: 下拉框选中值发生变化时触发，主要来清空子组件选中状态
       * @param {string | array} val 目前的选中值
       */
      handleChange(val) {
        if ((Array.isArray(val) && val.every(e => !e)) || !val) {
          this.valueTitle = ''
          this.$refs.virtualTree.clearSelected()
        }
      },
      /**
       * @description: 监听子组件check事件设置选中值
       * @param { Object {} } check：选中状态，currentData：当前节点，checked：所有选中数组，halfChecked：获取半选中状态下的节点
       */
      handleCheck(data) {
        const label = data.checked.map(e => e.label)
        this.valueTitle = this.showCheckbox ? label : label.join(',')
      },
      /**
       * @description: 监听子组件node-click事件设置选中值
       * @param {Object []} node 选中节点的数组
       */
      handleNodeClick(node) {
        const label = node.map(e => e.label)
        this.valueTitle = this.showCheckbox ? label : label.join(',')
      },
      /**
       * @description: 设置默认值透传，当前用于调用子组件setCurrent方法
       * @param {Object | String} current 当前选中id
       */
      setCurrent(current, noListens = false) {
        this.$refs.virtualTree.setCurrent(current)
      },
      //设置select显示值
      setTitle(title) {
        this.valueTitle = title
      },
      // 获取扁平化的数据
      getFlattenTree() {
        return this.$refs.virtualTree.flattenTree
      },
    },
  }
</script>
<style scoped lang="scss">
  .virtual-select {
    .el-select-dropdown__item {
      height: 250px !important;
      padding: 0!important;
      &.virtualOption {
        &:hover {
          background-color: #fff !important;
        }
      }
    }
    .el-select-dropdown__item.selected {
       background-color: #fff!important;
     }
  }
  ::v-deep {
  .el-scrollbar {
    overflow-y: auto;
    height: 100%;
   }
  }
</style>
<style>
  /* .virtual-select {
    width: 15%;
  } */
  .virtual-select .el-scrollbar__bar.is-vertical {
    display: none;
  }
</style>
