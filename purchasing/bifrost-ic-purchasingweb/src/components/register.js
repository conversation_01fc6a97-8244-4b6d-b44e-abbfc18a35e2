import Vue from 'vue'
import { Message } from 'element-ui'
Vue.prototype.$Message = Message // 消息提示组件

// 自动注册components和views目录下的vue组件
// 并记录组件的name，提供给类似即插即用功能使用
window.$viewNames = {}
const commonComponents = require.context('@/components', true, /\.vue$/)
const viewComponents = require.context('@/views', true, /\.vue$/)
function registerComponent(componentMap) {
  componentMap.keys().forEach((fileName) => {
    // 获取组件配置
    const componentConfig = componentMap(fileName)
    const filterName = fileName.replace(/\.\//, '')
    const componentName = componentConfig['default'].name
    if (componentName) {
      // 全局注册组件
      Vue.component(
        componentName,
        // 如果这个组件选项是通过 `export default` 导出的，
        // 那么就会优先使用 `.default`，
        // 否则回退到使用模块的根。
        componentConfig.default || componentConfig
      )
      window.$viewNames[componentName] = '占位符'
    } else {
      console.warn(`【${filterName}】组件的'export default'缺少name，无法注册！`)
    }
  })
}

registerComponent(commonComponents)
registerComponent(viewComponents)
