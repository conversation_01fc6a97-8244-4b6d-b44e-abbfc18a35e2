<template>
  <el-dialog
        ref="modifyWfConUserListDataDlg"
        :title="title"
        :visible.sync="isConUserListVisible"
        :close-on-click-modal='false'
        append-to-body width="610px">
    <ransfer-table ref="ransferTable" :staffList ="userData" style="height: 500px"/>
    <div slot="footer" class="dialog-footer">
      <el-button @click="isConUserListVisible = false">取消</el-button>
      <el-button type="primary" @click="conUserListChange">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'wf-countersign-userlist',
  data() {
    return {
      title: '选择用户',
      isConUserListVisible: false,
      userData: [],
      selectUserData: [],
      rightSelectVal: [],
      isMainUser: false // 是否需要主要审核人
    }
  },
  methods: {
    init(nextAuditUsers, isCountersign) {
      this.isConUserListVisible = true

      this.$callApiParams('getIsMainAudit', {}, result => {
        this.isMainUser = result.data
        if (isCountersign && isCountersign === true) { // 是加签是 不需要主审核人
          this.isMainUser = false
        }
        if (this.userData.length === 0) {
          const data = []
          nextAuditUsers.forEach((item, index) => {
            data.push({
              label: item.name,
              key: item.id,
              isMainUser: false
            })
          })

          this.userData = data
          this.$nextTick(() => {
            this.$refs.ransferTable.staffList = (this.$clone(this.userData))
            this.$refs.ransferTable.isMainUser = this.isMainUser
          })
        }
        return true
      })
    },
    conUserListChange() {
      if (this.$refs.ransferTable.selectedStaffList.length>0) {
        this.selectUserData = []
        var userMain = []
        this.selectUserData = this.$refs.ransferTable.selectedStaffList
        for (let i = 0; i < this.$refs.ransferTable.selectedStaffList.length; i++) {
          if (this.$refs.ransferTable.selectedStaffList[i].isMainUser) {
            userMain.push(this.$refs.ransferTable.selectedStaffList[i])
          }
        }
        if (userMain.length>1) {
          this.$message.error('主审核人只能选择一条')
          return
        }
        if (this.isMainUser && userMain.length <= 0) {
          this.$message.error('请选择主审核人')
          return
        }
      }
      this.$emit('conUserListChange', this.selectUserData, this.userData)
      this.isConUserListVisible = false
    },
    upUser() {
      if (this.rightSelectVal.length === 0) {
        this.$message.error('请选择已选中用户')
        return false
      }
      if (this.rightSelectVal.length > 1) {
        this.$message.error('只能选中一个用户')
        return false
      }
      if (this.selectUserData.length === 1) {
        this.$message.error('只有一个选中用户，无法移动')
        return false
      }
      for (var i = 0; i < this.selectUserData.length; i++) {
        if (this.selectUserData[i] === this.rightSelectVal[0]) {
          const lasti = i - 1
          if (i === 0) {
            this.$message.error('第一个用户，不能上移')
            return false
          }
          const selectUser = this.selectUserData[lasti]
          this.selectUserData.splice(lasti, 1)
          this.selectUserData.splice(i, 0, selectUser)
          return true
        }
      }
    },
    downUser() {
      if (this.rightSelectVal.length === 0) {
        this.$message.error('请选择已选中用户')
        return false
      }
      if (this.rightSelectVal.length > 1) {
        this.$message.error('只能选中一个用户')
        return false
      }
      if (this.selectUserData.length === 1) {
        this.$message.error('只有一个选中用户，无法移动')
        return false
      }
      for (var i = 0; i < this.selectUserData.length; i++) {
        if (this.selectUserData[i] === this.rightSelectVal[0]) {
          const nexti = i + 1
          if (this.selectUserData.length === nexti) {
            this.$message.error('已经是最后一个，不能下移')
            return false
          }
          const selectUser = this.selectUserData[nexti]
          this.selectUserData.splice(nexti, 1)
          this.selectUserData.splice(i, 0, selectUser)
          return true
        }
      }
    },
    rightSelect(rightSelectVal) {
      this.rightSelectVal = rightSelectVal
    },
    filterMethod(query, item) {
      return item.label.indexOf(query) > -1
    },
    resetData() {
      this.$refs.ransferTable?.removeStaff?.('all')
    }
  }
}
</script>

<style lang="scss">
</style>
