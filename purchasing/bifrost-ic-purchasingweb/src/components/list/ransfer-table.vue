<template>
  <div class="common-page">
  <el-row :gutter="20" style="height: 500px">
    <el-col :span="10" style="height: 100%">
      <div class="tableTitle">
        <span>待选列表</span>
        <span>{{ staffCheckNum }}/{{ staffNum== staffList.length ? staffNum: staffList.length}}</span>
      </div>
      <el-table
        ref="staffTable"
        :data="staffList"
        max-height="500"
        :row-key="getRowKey"
        style="height: 97%"
        border
        fit
        highlight-current-row
        @selection-change="handleStaffChange"
      >
        <el-table-column type="selection" :reserve-selection="true" width="55" align="center"></el-table-column>
        <el-table-column label="用户名称" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.label }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col :span="4" align="center" class="buttonAll" style="height: 100%;display: flex;justify-content: center;align-items: center;">
      <div >
      <el-button @click="addStaff('single')" type="primary" :disabled="!staffData.length" >
        移入
        <i class="el-icon-arrow-right"></i>
      </el-button>
      <el-button style="margin-top: 10px"
        @click="removeStaff('single')"
        type="primary"
        :disabled="!selectedStaffData.length"
        class="staffButton"> 移出
        <i class="el-icon-arrow-left"></i>
      </el-button>
<!--      <el-button style="margin-top: 10px" @click="addStaff('all')" type="primary" :disabled="!staffList.length" class="staffButton">-->
<!--        全部移入-->
<!--      </el-button>-->
<!--      <el-button style="margin-top: 10px;margin-left: inherit"-->
<!--        @click="removeStaff('all')"-->
<!--        type="primary"-->
<!--        :disabled="!selectedStaffList.length"-->
<!--        class="staffButton"-->
<!--      >-->
<!--        全部移出-->
<!--      </el-button>-->
      </div>
    </el-col>
    <el-col :span="10" style="height: 100%">
      <div class="tableTitle">
        <span>已选列表</span>
        <span>{{ selectedStaffCheckNum }}/{{ selectedStaffNum }}</span>
      </div>
      <el-table
        ref="selectedStaffTable"
        :data="selectedStaffList"
        max-height="500"
        :row-key="getRowKey"
        @select ="rightSelect"
        border
        fit
        style="height: 90%"
        highlight-current-row
        @selection-change="handleSelectedStaffChange"
      >
        <el-table-column type="selection" :reserve-selection="true" width="55" align="center"></el-table-column>
        <el-table-column label="用户名称" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.label }}</span>
          </template>
        </el-table-column>

        <el-table-column label="主审核人" align="center" v-if="isMainUser">
          <template slot-scope="{ row }">
            <el-checkbox v-model="row.isMainUser"></el-checkbox>
          </template>
        </el-table-column>
      </el-table>
      <el-button  size="small" style="margin-top: 5px" @click="upUser">上移</el-button>
      <el-button  size="small" @click="downUser" >下移</el-button>
    </el-col>
  </el-row>
  </div>
</template>

<script>
export default {
  name: 'ransfer-table',
  data() {
    return {
      // 待选
      staffList: [],
      // 待选选中
      staffData: [],
      // 总数
      staffNum: 0,
      // 选中数量
      staffCheckNum: 0,
      // 已选选中
      selectedStaffData: [],
      // 已选总数
      selectedStaffNum: 0,
      // 已选选中数量
      selectedStaffCheckNum: 0,
      // 已选列表
      selectedStaffList: [],
      isMainUser: false // 是否需要主要审核人
    }
  },
  methods: {
    rightSelect(selection, row) {

    },
    getRowKey(row) {
      return row.key
    },
    upUser() {
      if (this.selectedStaffData.length === 0) {
        this.$message.error('请选择已选中用户')
        return false
      }
      if (this.selectedStaffData.length > 1) {
        this.$message.error('只能选中一个用户')
        return false
      }
      if (this.selectedStaffList.length === 1) {
        this.$message.error('只有一个选中用户，无法移动')
        return false
      }
      for (var i = 0; i < this.selectedStaffList.length; i++) {
        if (this.selectedStaffList[i] === this.selectedStaffData[0]) {
          const lasti = i - 1
          if (i === 0) {
            this.$message.error('第一个用户，不能上移')
            return false
          }
          const selectUser = this.selectedStaffList[lasti]
          this.selectedStaffList.splice(lasti, 1)
          this.selectedStaffList.splice(i, 0, selectUser)
          return true
        }
      }
    },
    downUser() {
      if (this.selectedStaffData.length === 0) {
        this.$message.error('请选择已选中用户')
        return false
      }
      if (this.selectedStaffData.length > 1) {
        this.$message.error('只能选中一个用户')
        return false
      }
      if (this.selectedStaffList.length === 1) {
        this.$message.error('只有一个选中用户，无法移动')
        return false
      }
      for (var i = 0; i < this.selectedStaffList.length; i++) {
        if (this.selectedStaffList[i] === this.selectedStaffData[0]) {
          const nexti = i + 1
          if (this.selectedStaffList.length === nexti) {
            this.$message.error('已经是最后一个，不能下移')
            return false
          }
          const selectUser = this.selectedStaffList[nexti]
          this.selectedStaffList.splice(nexti, 1)
          this.selectedStaffList.splice(i, 0, selectUser)
          return true
        }
      }
    },
    // 左边表格选中存入staffData
    handleStaffChange(rows) {
      this.staffCheckNum = rows.length
      this.staffData = []
      if (rows) {
        rows.forEach((row) => {
          if (row) {
            this.staffData.push(row)
          }
        })
      }
    },
    // 左->右(单选-全选)
    addStaff(type) {
      setTimeout(() => {
        // 清空选择
        this.$refs['staffTable'].clearSelection()
        this.$refs['selectedStaffTable'].clearSelection()
      }, 0)
      if (type === 'single') {
        this.staffData.forEach((item) => {
          this.selectedStaffList.push(item)
        })
        this.selectedStaffNum = this.selectedStaffList.length
        for (let i = 0; i < this.staffList.length; i++) {
          for (let j = 0; j < this.staffData.length; j++) {
            if (this.staffList[i].key === this.staffData[j].key) {
              this.staffList.splice(i, 1)
              this.staffNum = this.staffList.length
            }
          }
        }
      }
      if (type === 'all') {
        this.staffList.forEach((item) => {
          this.selectedStaffList.push(item)
        })
        this.staffList = []
        this.staffNum = this.staffList.length
        this.selectedStaffNum = this.selectedStaffList.length
      }
    },
    // 右边表格选中存入selectedStaffData
    handleSelectedStaffChange(rows) {
      this.selectedStaffCheckNum = rows.length
      this.selectedStaffData = []
      if (rows) {
        rows.forEach((row) => {
          if (row) {
            this.selectedStaffData.push(row)
          }
        })
      }
    },
    // 右->左(单选-全选)
    removeStaff(type) {
      setTimeout(() => {
        this.$refs['staffTable'].clearSelection()
        this.$refs['selectedStaffTable'].clearSelection()
      }, 0)
      if (type === 'single') {
        this.selectedStaffData.forEach((item) => {
          this.staffList.push(item)
          this.staffNum = this.staffList.length
        })
        for (let i = 0; i < this.selectedStaffList.length; i++) {
          for (let j = 0; j < this.selectedStaffData.length; j++) {
            if (
              this.selectedStaffList[i] &&
              this.selectedStaffData[j] &&
              this.selectedStaffList[i].key === this.selectedStaffData[j].key
            ) {
              this.selectedStaffList.splice(i, 1)
              this.selectedStaffNum = this.selectedStaffList.length
            }
          }
        }
      }
      if (type === 'all') {
        this.selectedStaffList.forEach((item) => {
          this.staffList.push(item)
          this.staffNum = this.staffList.length
        })
        this.selectedStaffList = []
        this.selectedStaffNum = 0
      }
    }
  }
}
</script>

<style >

 .staffButton{
  margin-left:inherit !important;
}
</style>
