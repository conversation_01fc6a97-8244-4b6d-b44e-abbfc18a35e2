<template>
  <el-container style="height:100%;flex-direction: column;">
    <el-main>
      <slot name="formMainContent"/>
    </el-main>
    <el-footer id="form-edit-detail-el-footer" v-if="showButtons&&!isLoading" style="text-align: right;line-height:60px">
      <el-button size="small" icon="el-icon-d-arrow-left"
                 @click="btEditBack"> {{btEditBackText}}</el-button>
      <el-button ref="btDraft" size="small" icon="el-icon-tickets"
                 @click="btEditDraft" v-show="!hideBtDraft">存草稿</el-button>
      <el-button size="small" type="primary" icon="el-icon-edit"
                 @click="btEditSave" v-show="!hideBtSave"> 保存</el-button>
      <el-button size="small" type="success" icon="el-icon-position"
        @click="btEditSaveAndSubmit" v-if="needProcess"> 保存并送审</el-button>
    </el-footer>
  </el-container>
</template>

<script>
import { mixins } from '@/mixin'
export default {
  name: 'form-edit-detail',
  mixins: [mixins],
  props: {
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      btEditBackText: '返回',
      hideBtDraft: true,
      hideBtSave: false,
      needProcess: false,
      showButtons: true,
      eventSuffix: '',
      isFreedomForm: true
    }
  },
  methods: {
    setBtDraftLoading(isLoading) {
      if (this.$refs.btDraft) {
        this.$refs.btDraft.loading = isLoading
      }
    },
    exitEditFocus() {
      window.luckysheet && window.luckysheet.exitEditMode()
    },
    btEditBack() {
      this.exitEditFocus()
      this.fireEvent('btEditBack')
    },
    btEditDraft() {
      this.draftOrSave('btEditDraft')
    },
    btEditSave() {
      this.draftOrSave('btEditSave')
    },
    draftOrSave(eventKey) {
      const dataVo = this.getFormCanvas().getDataToSave()
      if (dataVo.meta) {
        this.isFreedomForm = dataVo.meta.isFreedomForm
      }
      this.exitEditFocus()
      this.fireEvent(eventKey, dataVo)
      window.luckysheet.exitEditMode()
      this.$setProperty(
        this.$parent.$parent,
        'baPayeelist',
        'isClickSaveBtn',
        true)
    },
    btEditSaveAndSubmit() {
      this.fireEvent('btEditSaveAndSubmit')
    },
    getFormCanvas() {
      return this.$parent.$parent
    },
    setPreviewText() { // 设置“退出预览”按钮文本
      this.btEditBackText = '退出预览'
      this.eventSuffix = 'Preview'
      this.hideBtSave = true
      this.exitEditFocus()
    },
    fireEvent(event, dataVo) {
      // 发出的事件带有后缀，是为了区分不同的场景使用这个组件时，
      // 映射到不同的时间处理方法，避免事件同名导致的按钮响应失效
      // this.$event(this, event + this.eventSuffix, dataVo)
      this.$event(this, event, dataVo)
    }
  }
}
</script>
