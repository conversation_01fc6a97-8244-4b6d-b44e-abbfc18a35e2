<!--
 * @Description: 页面名称
 * @version:
 * @Author: zhangshaowu <EMAIL>
 * @Date: 2024-06-04 16:54:25
 * @LastEditors: zhangshaowu <EMAIL>
 * @LastEditTime: 2024-06-07 09:23:20
-->
<!--
 * 横向拖拽
-->
<template>
  <div class="draggable-box">
    <div
      class="draggable-element"
      @mousedown="handleMouseDown"
      @mouseup="handleMouseUp"
      @mousemove="handleMouseMove"
    >
      <div class="lineMove"></div>
    </div>

    <div
      v-if="isDragging"
      class="drag-line"
      :style="{ left: mouseX + 'px' }"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'dragX',
  data() {
    return {
      isDragging: false,
      mouseX: 0,
      startPx: 0
    }
  },
  mounted() {
    document.addEventListener('mouseup', this.handleMouseUp)
  },
  beforeDestroy() {
  },
  methods: {
    handleMouseDown(event) {
      this.isDragging = true
      this.startPx = event.clientX
      this.mouseY = event.clientX
      this.$emit('handleMouseDown', this.isDragging)

      document.addEventListener('mousemove', this.handleMouseMove)
    },
    handleMouseMove(event) {
      event.preventDefault()
      if (this.isDragging) {
        this.mouseX = event.clientX
      }
    },
    handleMouseUp(event) {
      if (this.isDragging) {
        this.isDragging = false
        this.mouseX = 0
        this.$emit('handleMouseUp', event.clientX - this.startPx)
        document.removeEventListener('mousemove', this.handleMouseMove)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.draggable-box {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  z-index: 100;
}
.lineMove{
  height: 100%;
  width: 5px;
  /* margin: 5px 0; */

}
.draggable-element {
height: 100%;
  cursor: e-resize;
  /* position: absolute; */
  /* 其他样式 */
}
.drag-line {
  position: fixed;
  z-index: 999;
  top: 0;
  left: 437px;
  width: 1px;
  height: 10000px;
  background-color: #75B2F2;
  /* 其他样式 */
}
</style>
