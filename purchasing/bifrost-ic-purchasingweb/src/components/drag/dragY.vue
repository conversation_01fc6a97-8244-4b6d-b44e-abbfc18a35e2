<!--
 * 纵向拖拽
-->
<template>
  <div class="draggable-box">
    <div
      class="draggable-element"
      @mousedown="handleMouseDown"
      @mouseup="handleMouseUp"
      @mousemove="handleMouseMove"
    >
      <div class="lineMove"></div>
    </div>

    <div
      v-if="isDragging"
      class="drag-line"
      :style="{ top: mouseY + 'px' }"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'dragY',
  data() {
    return {
      isDragging: false,
      mouseY: 0,
      startPx: 0
    }
  },
  mounted() {
    document.addEventListener('mouseup', this.handleMouseUp)
  },
  beforeDestroy() {
  },
  methods: {
    handleMouseDown(event) {
      this.isDragging = true
      this.startPx = event.clientY
      this.mouseY = event.clientY
      this.$emit('handleMouseDown', this.isDragging)

      document.addEventListener('mousemove', this.handleMouseMove)
    },
    handleMouseMove(event) {
      event.preventDefault()
      if (this.isDragging) {
        this.mouseY = event.clientY
      }
    },
    handleMouseUp(event) {
      if (this.isDragging) {
        this.isDragging = false
        this.mouseY = 0
        this.$emit('handleMouseUp', event.clientY - this.startPx)
        document.removeEventListener('mousemove', this.handleMouseMove)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.draggable-box {
  position: absolute;
  top: 0px;
  bottom: 0;
  height: 10px;
  width: 100%;
  z-index: 999;
}
.lineMove{
   width: 100%;
  height: 10px;
  /* margin: 5px 0; */

}
.draggable-element {
  cursor: n-resize;
  /* position: absolute; */
  /* 其他样式 */
}
.drag-line {
  position: fixed;
  z-index: 999;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #75B2F2;
  left: 0;
  /* 其他样式 */
}
</style>
