<template>
    <el-dialog
            append-to-body
            ref="dlgExcelImp"
            title='导入Excel'
            @close="uploadClose"
            :close-on-click-modal="false"
            :visible.sync="dlgVisible">
        <div class="flex-column">
            <div class="flex-1" style="overflow: auto">
              <div v-if="importTypeShow">
                <el-radio v-model="importType" label="increment">增量导入</el-radio>
                <el-radio v-model="importType" label="cover">覆盖导入</el-radio>
              </div>
                <div>
                  <div v-show="isShow">
                    表单格式：
                    <el-select v-model="formNameCheckedVal" placeholder="请选择">
                      <el-option
                        v-for="item in formNameOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>

                    </el-select>
                  </div>
                    <p>进行数据导入操作，请按照以下步骤进行：<br/>
                        1. 请下载导入模板：<a @click="downloadTemplate">模板文件</a><br/>
                        2. 请按照模板格式，填充好数据并保存<br/>
                        3. 选取文件上传<br/>
                    </p>
                </div>
                <el-upload
                        ref="upload"
                        :action='`/api/ic/bifrost/cloud/api.do?apiKey=${apiKey}`'
                        :limit="1"
                        accept=".xls,.xlsx"
                        style="text-align: left;"
                        :on-remove="onRemove"
                        :on-change="onChange"
                        :file-list="fileList"
                        align="center"
                        :auto-upload="false">
                    <el-button @click="clearFiles">选取文件</el-button>
                </el-upload>
            </div>
            <div slot="footer" class="dialog-footer"
                 style="padding: 25px 5px 0px 0px;text-align: right;">
                <el-button @click="dlgVisible = false"
                    style="padding: 9px 32px;">取消</el-button>
                <el-button ref="btUpload" type="primary" @click="fileUpload">确认上传</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script>

export default {
  name: 'excel-imp',
  components: {},
  data() {
    return {
      file: undefined,
      fileList: [],
      apiKey: 'template/年初模板.xls', // 初始值为样例
      tableColumn: '',
      dlgVisible: false,
      queryParams: {},
      fileName: '',
      onSuccess: undefined,
      tableFormat: '',
      formNameCheckedVal: '',
      formNameOptions: [], value: '',
      isShow: true,
      importType: 'increment',
      importTypeShow: false
    }
  },
  mounted() {
    this.$setDlgSize(this, 'dlgExcelImp', 500, 360)
  },
  methods: {
    showDlg(apiKey, fileName, tableColumn, exParams) {
      this.dlgVisible = true
      this.apiKey = apiKey
      this.tableColumn = tableColumn
      this.fileName = fileName
      const excelImpExpParams = exParams.excelImpExpParams || {}
      this.$callApiParams('selectCformFormat',
        { 'formType': exParams.FORM_TYPE_eq,
          ...excelImpExpParams },
        result => {
          if (result.data !== '') {
            this.isShow = true
            this.formNameOptions = result.data
            this.formNameCheckedVal = result.data[0].value
          } else {
            this.formNameCheckedVal = null
            this.isShow = false
          }
          return true
        })
      exParams = exParams || {}
      this.importTypeShow = exParams.importTypeShow || false
      this.onSuccess = exParams.onSuccess
      if (exParams.onSuccess) {
        delete exParams.onSuccess
      }
      this.queryParams = { ...this.queryParams, ...exParams }
    },
    uploadClose() {
      this.dlgVisible = false
      this.clearFiles()
    },
    downloadTemplate() {
      var params = {
        importTemplate: true,
        tableColumn: this.formNameCheckedVal === '' ? this.tableColumn : this.formNameCheckedVal,
        tableFormat: this.formNameCheckedVal,
        fileName: this.fileName }
      const paramsAll = { ...params, ...this.queryParams, ...this.tableFormat }
      this.$fileDownloadBykey(this.apiKey, paramsAll)
    },
    fileUpload() {
      if (this.$isEmpty(this.file)) {
        this.$message.info('请先选取文件')
        return
      }

      const formData = new FormData()
      formData.append('file', this.file.raw)
      var params = {
        tableColumn: this.tableColumn,
        tableFormat: this.formNameCheckedVal,
        importType: this.importType
      }

      this.$refs.btUpload.loading = true
      const paramsAll = { ...params, ...this.queryParams, ...this.tableFormat }
      this.$fileUpload(this.apiKey, formData, paramsAll)
        .then(({ data }) => {
          this.$refs.btUpload.loading = false
          if (data.success) {
            this.$message.success('导入成功')
            this.fileData = data.data
            this.uploadClose()
            if (typeof this.onSuccess === 'function') {
              this.onSuccess()
            }
          } else {
            this.clearFiles()
            this.$message.error(
              { dangerouslyUseHTMLString: true,
                message: '导入失败' + data.resultMessage })
          }
        })
        .catch((_error) => {
          this.$refs.btUpload.loading = false
          this.clearFiles()
        })
    },
    clearFiles() {
      this.$refs.upload.clearFiles()
      this.fileList = []
      this.file = undefined
    },
    onRemove() {
      this.clearFiles()
    },
    onChange(file, fileList) {
      this.file = file
      this.fileList = fileList
    }
  }
}
</script>
