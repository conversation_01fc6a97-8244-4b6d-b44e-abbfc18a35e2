<template>
  <div class="select-tree-container">
    <el-select
      popper-class="select-tree-dropdown"
      v-model="valueTitle"
      ref="selectInput"
      @focus="focus"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      @clear="handleClear"
    >
      <el-option :value="valueTitle" :label="valueTitle" @click.stop.prevent>
        <div style="padding: 2px 10px" @click.stop.prevent v-if="isShowFilter">
          <el-input
            v-model="filterValue"
            @keyup.enter.native="handleSearch"
            :placeholder="searchPlaceholder"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="handleSearch"
            ></el-button>
          </el-input>
        </div>
        <div class="tree-container">
          <el-tree
            ref="selecttree"
            :data="treeData"
            :props="props"
            :load="load"
            :lazy="lazy"
            :node-key="props.value"
            @node-click="handleNodeClick"
            :show-checkbox="isCheckbox"
            @check-change="checkChange"
            :filter-node-method="filterNode"
            :check-strictly="isCheckStrictly"
            :default-expand-all="isExpand"
          >
          <!-- style 模拟禁用样式，node 节点数据中，disabledNodeValue === 0 为禁用 并配置 disabledNode:true,作为开关-->
            <span class="custom-tree-node" slot-scope="{ node, data }" :style="{color: (data.disabledNodeValue === 0 && disabledNode === true) ? '#c0c4cc':'',cursor:(data.disabledNodeValue === 0 && disabledNode === true) ? 'not-allowed' : '' }">
              <!-- {{valueTitle}} -->
              <span :title="node.label">
                <slot name="icon" :node="node" :data="data">
                  <svg-icon
                    v-if="lazy"
                    :icon-class="
                      data.hasChildren
                        ? node.expanded
                          ? 'folder-open'
                          : 'folder'
                        : 'file'
                    "
                  ></svg-icon>
                  <svg-icon
                    v-if="!lazy"
                    :icon-class="
                      data.children && data.children.length > 0
                        ? node.expanded
                          ? 'folder-open'
                          : 'folder'
                        : 'file'
                    "
                  ></svg-icon>
                </slot>
                {{ node.label }}
              </span>
            </span>
          </el-tree>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'SelectLoadTreeFilter',
  props: {
    props: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: '',
          children: 'children',
          isLeaf: 'leaf'
        }
      }
    },
    disabled: {
      // 是否禁用
      type: Boolean,
      default: false
    },
    disabledNode: {
      // 是否需要禁用配置
      type: Boolean,
      default: false
    },
    lazy: {
      // 是否懒加载
      type: Boolean,
      default: true
    },
    clearable: {
      // 是否可清空
      type: Boolean,
      default: true
    },
    expandOnClickNode: {
      // 是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。
      type: Boolean,
      default: false
    },
    placeholder: {
      // 未选择时的提示语
      type: String,
      default: '请选择数据'
    },
    searchPlaceholder: {
      // 未选择时的提示语
      type: String,
      default: '请输入搜索数据'
    },
    value: {
      type: [String, Number, Array],
      default: ''
    },
    load: {
      // 懒加载函数 load(node, resolve)
      type: Function
      // 参数和处理逻辑和下面方法要一样 只需修改if里的数据
      // loadNode1(node, resolve) {
      //   if (!node) { //搜索为空的处理
      //     this.treeData = [{ name: 'region' }]
      //     return
      //   }
      //   if (node.level === 0) {
      //     return resolve([{ name: 'region' }]);
      //   }
      //   if (node.level >= 1) return resolve([{
      //       name: 'leaf',
      //       leaf: true
      //     }, {
      //       name: 'zone'
      //     }])
      // }
    },
    treeData: {
      // 树形数据
      type: Array,
      default: () => {
        return []
      }
    },
    isShowFilter: {
      // 暂时只考虑到非懒加载的情况
      // 是否可以搜索true
      type: Boolean,
      default: false
    },
    isExpand: {
      type: Boolean,
      default: true
    },
    isCheckbox: {
      // 暂时只考虑到非懒加载的情况
      // 是否多选
      type: Boolean,
      default: false
    },
    isCheckParent: {
      // 暂时只考虑到非懒加载的情况
      // 是否多选时，数据需要父级
      type: Boolean,
      default: true
    },
    isCheckStrictly: {
      // 暂时只考虑到非懒加载的情况
      // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法
      type: Boolean,
      default: false // false -关联  true - 不关联
    },
    isOnlySelectLeaf: {
      // 暂时只考虑到非懒加载的情况
      // 是否只能选叶子节点
      type: Boolean,
      default: false // false -关联  true - 不关联
    }
  },
  data() {
    return {
      valueId: this.value,
      valueTitle: '',
      filterValue: ''
    }
  },
  watch: {
    value: {
      handler(val, oldVal) {
        // 对于value的watch仅仅为了实现直接赋值能正确勾选数据，仅在非懒加载下起效
        if (!this.lazy) {
          if (!val) {
            if (this.isCheckbox) {
              this.valueTitle = ''
              // 如果树还没渲染出来就等待下次视图变化
              if (this.$refs.selecttree) {
                this.$refs.selecttree.setCheckedKeys([])
              } else {
                this.$nextTick(() => {
                  this.$refs.selecttree.setCheckedKeys([])
                })
              }
              return
            }
            this.valueTitle = ''
            // 如果树还没渲染出来就等待下次视图变化
            if (this.$refs.selecttree) {
              this.$refs.selecttree.setCurrentKey()
            } else {
              this.$nextTick(() => {
                this.$refs.selecttree.setCurrentKey()
              })
            }
            return
          }
          if (this.isCheckbox) {
            // 多选
            if (!this.treeData.length) {
              return
            }
            const titleArr = []
            const valueArr = []
            if (this.isCheckStrictly) {
              // 如果严格遵循父子不关联就给所有数据
              val.forEach((item) => {
                titleArr.push(this.tranTitleById(item))
                valueArr.push(item)
              })
            } else {
              val.forEach((item) => {
                if (this.isCheckParent) {
                  // 如果需要父级
                  if (item.children && item.children.length > 0) {
                    titleArr.push(this.tranTitleById(item))
                  } else {
                    // 此时的valueArr是用于setCheckedKeys的，因此在父子关联的情况下还是要去除掉父级
                    titleArr.push(this.tranTitleById(item))
                    valueArr.push(item)
                  }
                } else {
                  // 如果不需要父级
                  if (item.children && item.children.length > 0) return
                  titleArr.push(this.tranTitleById(item))
                  valueArr.push(item)
                }
              })
            }
            // 如果树还没渲染出来就等待下次视图变化
            if (this.$refs.selecttree) {
              this.valueTitle = titleArr.join(',')
              this.$refs.selecttree.setCheckedKeys(valueArr)
            } else {
              this.$nextTick(() => {
                this.valueTitle = titleArr.join(',')
                this.$refs.selecttree.setCheckedKeys(valueArr)
              })
            }
            return
          }
          // 如果树还没渲染出来就等待下次视图变化
          if (this.$refs.selecttree) {
            this.valueTitle = this.$refs.selecttree.getNode(val) ? this.tranTitle(
              this.$refs.selecttree.getNode(val).data
            ) : ''
            this.$refs.selecttree.setCurrentKey(val)
          } else {
            this.$nextTick(() => {
              this.valueTitle = this.$refs.selecttree.getNode(val) ? this.tranTitle(
                this.$refs.selecttree.getNode(val).data
              ) : ''
              this.$refs.selecttree.setCurrentKey(val)
            })
          }
        }
      },
      immediate: true
    },
    treeData: {
      handler(val, oldVal) {
        this.$nextTick(() => {
          if (this.isCheckbox) {
            // 多选
            if (!this.treeData.length) {
              return
            }
            const titleArr = []
            const valueArr = []
            if (this.isCheckStrictly) {
              // 如果严格遵循父子不关联就给所有数据
              this.value.forEach((item) => {
                titleArr.push(this.tranTitleById(item))
                valueArr.push(item)
              })
            } else {
              this.value.forEach((item) => {
                if (this.isCheckParent) {
                  // 如果需要父级
                  if (item.children && item.children.length > 0) {
                    titleArr.push(this.tranTitleById(item))
                  } else {
                    // 此时的valueArr是用于setCheckedKeys的，因此在父子关联的情况下还是要去除掉父级
                    titleArr.push(this.tranTitleById(item))
                    valueArr.push(item)
                  }
                } else {
                  // 如果不需要父级
                  if (item.children && item.children.length > 0) return
                  titleArr.push(this.tranTitleById(item))
                  valueArr.push(item)
                }
              })
            }
            // 如果树还没渲染出来就等待下次视图变化
            if (this.$refs.selecttree) {
              this.valueTitle = titleArr.join(',')
              this.$refs.selecttree.setCheckedKeys(valueArr)
            } else {
              this.$nextTick(() => {
                this.valueTitle = titleArr.join(',')
                this.$refs.selecttree.setCheckedKeys(valueArr)
              })
            }
            return
          }
          this.valueTitle = this.$refs.selecttree.getNode(this.value) ? this.tranTitle(
            this.$refs.selecttree.getNode(this.value).data
          ) : ''
          this.$refs.selecttree.setCurrentKey(this.value)
        })
      },
      deep: true
    }
  },
  methods: {
    tranTitle(node) {
      if (typeof this.props.label === 'function') {
        // 有可能是自定义函数
        return this.props.label(node)
      } else {
        return node[this.props.label]
      }
    },
    tranTitleById(id) {
      if (typeof this.props.label === 'function') {
        // 有可能是自定义函数
        return this.props.label(this.$refs.selecttree.getNode(id).data)
      } else {
        return this.$refs.selecttree.getNode(id).data[this.props.label]
      }
    },
    handleClear() {
      if (this.isCheckbox) {
        this.$emit('input', [])
        this.$emit('change', [])
        this.$refs.selecttree.setCheckedKeys([])
        return
      }
      this.$emit('input', '')
      this.$emit('change', '')
      this.$emit('changeNode', {})
    },
    handleSearch() {
      this.$refs.selecttree.filter(this.filterValue)
    },
    filterNode(value, data) {
      if (!value) return true
      return this.tranTitle(data).indexOf(value) !== -1
    },
    handleNodeClick(node) {
      // 多选就不操作,disabledNodeValue === 0 为禁用状态
      if (this.isCheckbox || (node.disabledNodeValue === 0 && this.disabledNode)) {
        return
      }
      // 是否只能选子节点
      if (this.isOnlySelectLeaf) {
        let myLeaf = ''
        if (typeof this.props.isLeaf === 'function') {
          // 有可能是自定义函数
          myLeaf = this.props.isLeaf(node)
        } else {
          myLeaf = node[this.props.isLeaf]
        }
        if (!myLeaf) {
          return
        }
      }
      // this.valueTitle = node[this.props.label]
      this.valueTitle = this.tranTitle(node)
      this.valueId = node[this.props.value]
      this.$emit('input', node[this.props.value]) // 双向绑定
      this.$emit('change', node[this.props.value])
      this.$emit('changeNode', node)
      this.$refs.selectInput.blur()
    },
    checkChange() {
      const titleArr = []
      const valueArr = []
      if (this.isCheckStrictly) {
        // 如果严格遵循父子不关联就给所有数据
        this.$refs.selecttree.getCheckedNodes().forEach((item) => {
          titleArr.push(this.tranTitle(item))
          valueArr.push(item[this.props.value])
        })
      } else {
        this.$refs.selecttree.getCheckedNodes(false, true).forEach((item) => {
          if (this.isCheckParent) {
            // 如果需要父级
            titleArr.push(this.tranTitle(item))
            valueArr.push(item[this.props.value])
          } else {
            // 如果不需要父级
            if (item.children && item.children.length > 0) return
            titleArr.push(this.tranTitle(item))
            valueArr.push(item[this.props.value])
          }
        })
      }
      this.valueTitle = titleArr.join(',')
      this.$emit('input', valueArr) // 双向绑定
      this.$emit('change', valueArr)
    },
    focus() {
      this.$emit('focus')
    },
    handleFocus() {
      this.$refs.selectInput.toggleMenu()
    },
    setTitle(val) {
      this.valueTitle = val
    },
    setfilterValue() {
      this.filterValue = ''
      this.$refs.selecttree.filter(this.filterValue)
    }
  }
}
</script>
<style lang="scss">
.select-tree-container {
  width: 100%;
  .el-select {
    width: 100%;
  }
}
.el-select-dropdown .is-current {
    background-color: #F5F7FA;
}
</style>
<style scoped>
/* .select-tree-container {
  width: 100%;
} */
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  /*height: auto;*/
  height: 100% !important;
  padding: 0;
  max-height: 220px;
}
.el-select-dropdown__item.selected {
  font-weight: normal;
}
ul li >>> .el-tree .el-tree-node__content {
  height: 26px;
}
.el-tree-node__label {
  font-weight: normal;
}
.el-tree >>> .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}
.el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.tree-container {
  max-height: 220px;
  overflow: auto;
}
/* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸 */
.tree-container::-webkit-scrollbar {
  width: 3px;
  height: 6px;
}

/* 定义滚动条轨道 内阴影+圆角 */
.tree-container::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

/* 定义滑块 内阴影+圆角 */
.tree-container::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
}
</style>
<style lang="scss">
.select-tree-dropdown {
  max-width: 500px;
}
.select-tree-dropdown .el-scrollbar__wrap {
  overflow: hidden;
  margin-bottom: 0 !important;
  margin-right: 0 !important;
}
.select-tree-dropdown .el-scrollbar__bar.is-vertical,
.select-tree-dropdown .el-scrollbar__bar.is-horizontal {
  display: none;
}

</style>
