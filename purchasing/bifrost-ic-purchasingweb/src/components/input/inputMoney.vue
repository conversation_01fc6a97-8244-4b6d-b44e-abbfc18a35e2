<template>
<el-input
  v-bind="$attrs"
  v-on="$listeners"
  :value="value"
  @blur="inputBlur"
  @input="inputMethod"
  >
  <template v-for="(index, name) in $slots" :slot="name">
    <slot :name="name"></slot>
  </template>
</el-input>
</template>

<script>
export default {
  name: 'input-money',
  props: {
    value: String, // 默认接收一个名为 value 的 prop
    default: ''
  },
  data() {
    return {

    }
  },
  mounted() {
    if (this.value === '') {
      this.$emit('input', this.value)
    } else {
      this.$emit('input', this.$formatMoney(this.value))
    }
  },
  methods: {
    inputMethod(val) {
      // 需要nextTick后触发input
      this.$nextTick(() => {
        const newVal = val.replace(/[^0-9.]/g, '')
        this.$emit('input', newVal)
      })
    },
    inputBlur() {
      let value = ''
      if (this.$isEmpty(this.value)) {
        value = '0.00'
      } else {
        value = this.$formatMoney(this.value)
      }
      this.$emit('input', value)
    }
  }
}
</script>

<style>

</style>
