<template>
  <div>
    <div class="classifytree-filter-container">
      <el-input placeholder="查找" prefix-icon="el-icon-search" v-model="filterText" class="classifytree-filter-input"/>
      <el-row class="classifytree-filter-button-container">
        <el-button icon="el-icon-circle-plus-outline" :title="btAddTip" @click="btAddClick" circle/>
        <el-button icon="el-icon-edit" :title="btEditTip" @click="btEditClick" circle/>
        <el-button icon="el-icon-delete" :title="btDeleteTip" @click="btDeleteClick" circle :loading="isDeleting"/>
      </el-row>
    </div>

    <el-tree
            ref="classifytree"
            node-key="id"
            :data="treeData"
            :default-expand-all="true"
            :highlight-current="true"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            @node-click="nodeClick">
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <i :class="data.isLeaf ? 'el-icon-document' : data.children.length==0 ? 'el-icon-folder paddingLeft5' : 'el-icon-folder'"/> {{ node.label }}
      </span>
    </el-tree>

    <el-dialog
            append-to-body
            width="450px"
            :title='`${btEditType}分类`'
            :close-on-click-modal="false"
            :visible.sync="addNodeDlgVisible">
      <el-form label-width="80px" size="mini" :model="tempNode">
        <el-form-item label="业务年度">
          <el-select v-model="tempNode.year">
            <el-option label="2021" value="2021"></el-option>
            <el-option label="2022" value="2022"></el-option>
            <el-option label="2023" value="2023"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="节点类型" v-if="this.showNodeType">
          <el-select v-model="tempNode.isLeaf">
            <el-option label="文件夹" :value="false"></el-option>
            <el-option label="节点" :value="true"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="父级分类" :allowClear="true">
           <!--<el-select v-model="tempNode.parentId" placeholder="请选择父级分类">
            <el-option
              v-for="treeParentNode in treeParentNodes"
              :key="treeParentNode.id"
              :label="treeParentNode.label"
              :value="treeParentNode.id">
            </el-option>
          </el-select>-->
           <SelectLoadTreeFilter
                  placeholder="请选择父级分类"
                  v-model="tempNode.parentId"
                  :treeData="treeParentNodes"
                  :isExpand="true"
                  :lazy="false"
                  :disabledNode="false"
                  :props="{
                    label: 'label',
                    value: 'id',
                    children: 'children',
                    isLeaf: (data, node) => { return false }
                  }"
          />
        </el-form-item>

        <el-form-item label="分类名称">
          <el-input v-model="tempNode.label" :disabled="this.showNodeType === true ? false : tempNode.isLeaf && !this.leafCanRename"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addNodeDlgVisible = false">取消</el-button>
        <el-button type="primary" @click="saveNode">{{btEditType}}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
export default {
  name: 'classifytree',
  props: {
    leafCanBeTopLevel: { type: Boolean, default: false }, // 叶子节点是否可以在顶级分类，相当于不属于任何文件夹
    leafCanRename: { type: Boolean, default: false }, // 叶子节点是否可以通过分组树修改名称
    dataType: { type: String, default: '' },
    parentSaveButton: { type: String, default: '保存' }, // 父组件保存按钮名称
    saveExtraParams: { type: Object, default: () => ({}) }, // 叶子节点保存时额外的参数，格式如A=1&B=2
    deleteApiKey: { type: String, default: '' }, // 删除叶子节点的apiKey
    deleteExtraParams: { type: Object, default: () => {} }, // 叶子节点删除时额外的参数
    showNodeType: { type: Boolean, default: false } // 是否显节点类型选项
  },
  created() {
    if (!this.dataType || this.dataType === '') {
      this.$message.error('分类组件的数据类型不能为空')
    } else {
      this.$callApiParams('selectClassifyTree',
        { dataType: this.dataType }, result => {
          this.treeData = result.data
          return true
        })
    }
  },
  watch: {
    filterText(val) {
      this.$refs.classifytree.filter(val)
    }
  },
  data() {
    return {
      isDeleting: false,
      filterText: '',
      btEditType: '',
      btAddTip: '',
      btEditTip: '',
      btDeleteTip: '删除分类或节点',
      treeData: [],
      treeParentNodes: [],
      currentItemKey: '', // 当前打开的叶子节点的key
      currentItem: null, // 当前打开的叶子节点的dom
      addNodeDlgVisible: false,
      tempNode: this.makeNewNode()
    }
  },
  methods: {
    handleParentNodes(isUpdate) {
      if (!isUpdate) { // 新增时，初始的文件夹的设置
        var currentNode = this.$refs.classifytree.getCurrentNode()
        if (currentNode) { // 有当前节点
          if (currentNode.isLeaf) { // 当前节点是叶子节点
            this.tempNode.parentId = currentNode.parentId
          } else { // 当前节点就是文件夹
            this.tempNode.parentId = currentNode.id
          }
        }
      }

      var treeDataClone = JSON.parse(JSON.stringify(this.treeData))
      this.removeLeaf(treeDataClone)
      this.treeParentNodes = treeDataClone
    },
    removeLeaf(nodeChildren) { // 移除叶子节点
      for (let i = 0; i < nodeChildren.length; i++) {
        if (nodeChildren[i].isLeaf) {
          nodeChildren.splice(i, 1)
          i--
        } else {
          this.removeLeaf(nodeChildren[i].children)
        }
      }
    },
    checkBeforeCallApiSave(savingData) { // 父组件业务保存时，需要额外传递的参数，以自动添加分类树节点
      var currentNode = this.$refs.classifytree.getCurrentNode()
      if (!currentNode && !this.leafCanBeTopLevel) {
        this.$message.error('请指定保存到哪个文件夹')
        return false
      }
      return true
    },
    getExParamsCallApiSave(savingData) { // 父组件业务保存时，需要额外传递的参数，以自动添加分类树节点
      // 取树的当前节点为父节点，如果当前节点是文件夹，则取该节点直接作为父节点
      var parentId = ''
      var currentNode = this.$refs.classifytree.getCurrentNode()
      var saveExtraParamsStr = ''
      if (this.$isNotEmpty(this.saveExtraParams)) {
        saveExtraParamsStr = '&' + Object.keys(this.saveExtraParams)
          .map(key => key + '=' + this.saveExtraParams[key])
          .join('&')
      }
      if (currentNode) {
        parentId = (currentNode.isLeaf) ? currentNode.parentId : currentNode.id
      }
      return `&apiAutoClassifyTree=${this.dataType}&parentId=${parentId}${saveExtraParamsStr}`
    },
    postHandleCallApiSave(result) { // 父组件业务保存后，执行自动添加或更新分类树节点
      this.syncTreeNode(result.data)
    },
    saveNode() {
      this.$set(this.tempNode, 'dataType', this.dataType)
      this.$callApi('saveClassifyTreeNode', this.tempNode, result => {
        this.syncTreeNode(result.data)
        this.addNodeDlgVisible = false
      })
    },
    syncTreeNode(nodeData) { // 保存之后同步节点
      var container = this.treeData
      if (nodeData.parentId !== '') {
        var parent = this.$refs.classifytree.getNode(nodeData.parentId)
        container = parent.data.children
      }

      var nodeBefore = this.$refs.classifytree.getNode(nodeData.id)
      var isUpdate = (nodeBefore !== null)
      if (isUpdate) {
        var isDiffParent = (nodeBefore.data.parentIdBefore !== nodeData.parentId)
        if (isDiffParent) {
          // 修改时移动到了新的文件夹，则直接删除旧节点，然后追加新节点
          this.$refs.classifytree.remove(nodeBefore)
          container.push(nodeData)
          if (this.currentItemKey === nodeData.itemKey) {
            this.$nextTick(() => { // 如果移动了当前节点，则需要移动后该节点还是高亮状态
              this.setCurrentItem(nodeData.id, nodeData.itemKey)
            })
          }
        } else { // 还是原来的文件夹，则直接删除旧节点，然后新节点插入到原来的位置上
          var index = container.indexOf(nodeBefore.data)
          container.splice(index, 0, nodeData)
          this.$refs.classifytree.remove(nodeBefore)
        }
      } else {
        container.push(nodeData)
        if (nodeData.isLeaf) {
          this.$nextTick(() => {
            this.$emit('leafNodeAdded', nodeData.id, nodeData.itemKey) // 新增叶子节点事件
          })
        }
      }

      container.sort(function(x, y) { // 重新排序：按照节点产生的时间排序
        var xId = parseInt(x.id)
        var yId = parseInt(y.id)
        if (xId < yId) {
          return -1
        } else if (xId > yId) {
          return 1
        } else {
          return 0
        }
      })
    },
    btAddClick() {
      this.tempNode = this.makeNewNode()
      this.handleParentNodes(false)
      this.btEditType = '新增'
      this.addNodeDlgVisible = true
    },
    btEditClick() {
      var currentNode = this.$refs.classifytree.getCurrentNode()
      if (currentNode) {
        currentNode.parentIdBefore = currentNode.parentId
        this.tempNode = currentNode
        this.handleParentNodes(true)
        this.btEditType = '修改'
        this.addNodeDlgVisible = true
      }
    },
    btDeleteClick() {
      var node = this.$refs.classifytree.getCurrentNode()
      if (node == null) {
        this.$message.error('当前没有选中的节点')
      } else if (node.children && node.children.length > 0) {
        this.$message.error('请先删除子节点')
      } else {
        var nodeType = node.isLeaf ? '节点' : '文件夹'
        this.$confirm(`确定要删除当前${nodeType} [${node.label}] 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var doActionAfterDeleteSuccess = result => {
            this.$emit('nodeDeleted', node)
            this.$refs.classifytree.remove(node)
            this.isDeleting = false
            return true
          }
          var doActionAfterDeleteFailed = result => {
            this.isDeleting = false
          }

          if (node.isLeaf && !this.showNodeType) {
            if (this.deleteApiKey === '') {
              this.$message.error('没有设置节点删除的apiKey')
            } else {
              var ps = {
                ids: node.itemKey,
                apiAutoClassifyTree: `DELETE_${this.dataType}` }
              var deleteParams = Object.assign(ps, this.deleteExtraParams)
              this.isDeleting = true
              this.$callApiParams(this.deleteApiKey, deleteParams,
                doActionAfterDeleteSuccess, doActionAfterDeleteFailed)
            }
          } else {
            this.isDeleting = true
            this.$callApiParams(
              'deleteClassifyTreeNode', { ids: node.id },
              doActionAfterDeleteSuccess, doActionAfterDeleteFailed)
          }
        })
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    nodeClick(data) {
      // 点击当前节点，不需要再次从远端加载数据
      if (data.isLeaf && this.showNodeType === true ? true : (data.itemKey !== this.currentItemKey)) {
        var exData = {}
        this.$emit('nodeClick', data, exData)
        this.setCurrentItem(data.id, data.itemKey)
      }
    },
    getLeafNodeDom(nodeId, children) { // 获取叶子节点的dom
      var target = null
      for (let i = 0; i < children.length; i++) {
        if (children[i].node.data.isLeaf && children[i].node.data.id === nodeId) {
          target = children[i].$el
        } else if (children[i].$children && children[i].$children.length > 0) {
          target = this.getLeafNodeDom(nodeId, children[i].$children)
        }

        if (target != null) {
          break
        }
      }
      return target
    },
    setCurrentItem(nodeId, itemKey) { // 设置当前节点，null则取消选中
      this.$refs.classifytree.setCurrentKey(nodeId)
      this.currentItemKey = itemKey

      if (this.currentItem != null) {
        this.currentItem.firstChild.classList.remove('current-leaf')
      }
      const currentItemDom = this.getLeafNodeDom(nodeId, this.$refs.classifytree.$children)
      if (currentItemDom != null) {
        currentItemDom.firstChild.classList.add('current-leaf')
        this.currentItem = currentItemDom
      }
    },
    makeNewNode() {
      return {
        id: '',
        label: '新建分类',
        children: [],
        parentId: '',
        isLeaf: false,
        year: '2022'
      }
    },
    getCurrentNode() {
      var currentNode = this.$refs.classifytree.getCurrentNode()
      return currentNode
    },
    setCurrentParentNode(currentNode) {
      var parentNode = this.$refs.classifytree.getNode(currentNode.parentId)
      this.$refs.classifytree.setCurrentNode(parentNode.data)
    }
  }
}
</script>

<style lang="scss">
  .vue-bifrostIcApp .common-page .el-tree-node__expand-icon.is-leaf {
    display: none;
  }
  .vue-bifrostIcApp .common-page
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #dfe0e2;
  }
  .vue-bifrostIcApp .common-page
  .el-tree--highlight-current .el-tree-node.is-focusable > div.current-leaf {
    background-color: #8cc4ff;
    color: #fff;
  }
  .vue-bifrostIcApp .common-page .el-tree--highlight-current .el-tree-node.is-current
  > .el-tree-node__content .el-tree-node__expand-icon{
    color: #fff;
  }
  .vue-bifrostIcApp .common-page .el-tree--highlight-current .el-tree-node.is-current
  > .el-tree-node__content span.is-leaf {
    color: #8cc4ff;
  }

  .vue-bifrostIcApp .common-page .classifytree-filter-container {
    display: flex;
  }
  .vue-bifrostIcApp .common-page .classifytree-filter-container button {
    padding: 5px;
    border: none;
    margin-left: 2px;
  }
  .vue-bifrostIcApp .common-page .classifytree-filter-button-container {
    width: 128px;
    padding-top: 3px;
  }
  .paddingLeft5 {
    padding-left: 5px;
  }
</style>

