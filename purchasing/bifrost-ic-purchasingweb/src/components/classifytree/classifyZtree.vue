<template>
  <div id="classifyZtree">
    <el-dialog
            width="450px"
            :title='`${btEditType}分类`'
            append-to-body
            :close-on-click-modal="false"
            :visible.sync="addNodeDlgVisible">
      <el-form label-width="80px" size="mini" :model="tempNode">
        <!--<el-form-item label="业务年度">
          <el-select v-model="tempNode.fiscalYear">
            <el-option label="2021" value="2021"></el-option>
            <el-option label="2022" value="2022"></el-option>
            <el-option label="2023" value="2023"></el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item label="节点类型" v-if="this.showNodeType">
          <el-select v-model="tempNode.isLeaf">
            <el-option label="文件夹" :value="false"></el-option>
            <el-option label="节点" :value="true"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="父级分类" :allowClear="true">
          <sup-tree :setting="{
                      check: {
                        enable: false
                      },
                      data: setting.data,
                    }"
                    :btnSwitch="{
                      showEdit: false,
                      showRefresh: false,
                      showExpandAll: false
                    }"
                    @getCheckObjs="checkClassParent"
                    ref="classParentTree"
                    :is-get-child="false"
                    :nodes="treeParentNodes"
                    :checked-values="tempNode.parentId?[tempNode.parentId]:[]"></sup-tree>
        </el-form-item>
        <el-form-item label="指标类型" v-if="showSourceType">
          <el-select v-model="tempNode.sourceType">
            <el-option label="指标"  value="指标"></el-option>
            <el-option label="事前申请"  value="事前申请"></el-option>
            <el-option label="指标+事前申请" value="指标+事前申请" ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item required :label="tempNode.isParent?'分类名称':'节点名称'" style="margin-bottom:0px;">
          <el-input v-model="tempNode.label" :disabled="this.showNodeType === true ? false : !tempNode.isClass&&!leafCanRename"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addNodeDlgVisible = false">取消</el-button>
        <el-button type="primary" v-lockButton @click="saveNode">{{btEditType}}</el-button>
      </div>
    </el-dialog>
    <sup-tree :setting="setting"
              ref="supTree"
              :btnSwitch="{
                ...btnSwitch,
                showCopy: ( dataType == 'TableColumn' ? true : false ) //列定义那边的复制功能
              }"
              :nodes="treeData"
              :is-popover="false"
              :edit-enable="true"
              :tree-title="title"
              :drag="true"
              @onCreated="handleCreated"
              @onClickAdd="btAddClick"
              @onClickEdit="btEditClick"
              @onClickCopy="btCopyClick"
              @onClickDel="btDeleteClick"
              @dragSuccess="dragSuccess" />
  </div>

</template>

<script>
import SupTree from '../gianttree/supTree'

export default {
  name: 'classifyZtree',
  components: { SupTree },
  props: {
    leafCanBeTopLevel: { type: Boolean, default: false }, // 叶子节点是否可以在顶级分类，相当于不属于任何文件夹
    leafCanRename: { type: Boolean, default: false }, // 叶子节点是否可以通过分组树修改名称
    dataType: { type: String, default: '' },
    parentSaveButton: { type: String, default: '保存' }, // 父组件保存按钮名称
    saveExtraParams: { type: Object, default: () => ({}) }, // 叶子节点保存时额外的参数，格式如A=1&B=2
    deleteApiKey: { type: String, default: '' }, // 删除叶子节点的apiKey
    deleteExtraParams: { type: Object, default: () => {} }, // 叶子节点删除时额外的参数
    customDeletedFn: { type: Function, default: undefined }, // 是否自定义删除节点方法
    showNodeType: { type: Boolean, default: false }, // 是否显节点类型选项
    showSourceType: { type: Boolean, default: false }, // 是否显示指标参照默认类型
    drag: { type: Boolean, default: false }, // 是否开启拖拽
    title: { type: String, default: '分类' },
    isBlockForm: { type: Boolean, default: false }, // 是否是区块表单
    btnSwitch: { type: Object, default: () => ({
      showEdit: true,
      showRefresh: false,
      showExpandAll: false
    })
    }
  },
  created() {
    if (!this.dataType || this.dataType === '') {
      this.$message.error('分类组件的数据类型不能为空')
    } else {
      this.init()
    }
  },
  data() {
    return {
      isOpenNew: false,
      isDeleting: false,
      isCopying: false,
      filterText: '',
      btEditType: '',
      btAddTip: '',
      btEditTip: '',
      btDeleteTip: '删除分类或节点',
      treeData: [],
      dialogTreeData: [],
      treeParentNodes: [],
      currentItemKey: '', // 当前打开的叶子节点的key
      currentItem: null, // 当前打开的叶子节点的dom
      addNodeDlgVisible: false,
      tempNode: this.makeNewNode(),
      zTreeObj: null,
      setting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'label'
          }
        },
        view: {
          selectedMulti: false, // 按住ctrl是否可以多选
          showIcon: true,
          showLine: true,
          fontCss: function(treeId, treeNode) {
            return (treeNode.searchNode) ? { 'color': '#A60000', 'font-weight': 'bold' } : ''
          }
        },
        callback: {
          onClick: this.nodeClick,
          beforeClick: this.beforeClick
        }
      },
      nodeAll: [],
      zTreeParentNode: ''
    }
  },
  methods: {
    dragSuccess() {
      this.init()
    },
    beforeClick(treeId, treeNode) {
      this.zTreeParentNode = treeNode.level === 0 ? treeNode : ''
      // 追加所有点击过的父级ID
      this.nodeAll.push(treeNode.tId)
      // 去重 防止重复添加样式
      const nodeAll = this.repetition(this.nodeAll)
      for (let i = 0; i < nodeAll.length; i++) {
        if (treeNode.level === 0) {
          if (nodeAll[i] !== treeNode.tId) {
            // document.getElementById(nodeAll[i]) ? (document.getElementById(nodeAll[i]).childNodes[1]
            //   ? this.addParentClassName(nodeAll[i], 'remove', 'parentLevelDiy') : '') : ''
            if (document.getElementById(nodeAll[i]) &&
              document.getElementById(nodeAll[i]).childNodes[1]) {
              this.addParentClassName(nodeAll[i], 'remove', 'parentLevelDiy')
            }
          } else {
            this.addParentClassName(nodeAll[i], 'add', 'parentLevelDiy')
          }
        } else {
          // document.getElementById(nodeAll[i]) ? (document.getElementById(nodeAll[i]).childNodes[1]
          //   ? this.addParentClassName(nodeAll[i], 'remove', 'parentLevelDiy') : '') : ''
          if (document.getElementById(nodeAll[i]) &&
            document.getElementById(nodeAll[i]).childNodes[1]) {
            this.addParentClassName(nodeAll[i], 'remove', 'parentLevelDiy')
          }
        }
      }
      return !!(this.isOpenNew || treeNode.parentId)
    },
    // 公用添加移除父级方法
    addParentClassName(el, type, className) {
      if (type === 'add') {
        document.getElementById(el).childNodes[1].classList.add(className)
      } else {
        document.getElementById(el).childNodes[1].classList.remove(className)
      }
    },
    repetition(arr) {
      const newArr = [... new Set(arr)]
      return newArr
    },
    checkClassParent(nodes) {
      if (this.$isNotEmpty(nodes)) {
        this.tempNode.parentId = nodes[0].id
        this.$refs.classParentTree.visible = false
      } else {
        this.tempNode.parentId = ''
      }
    },
    handleCreated(zTreeObj) {
      this.zTreeObj = zTreeObj
    },
    init(callBack) {
      this.$callApiParams('selectClassifyList',
        { dataType: this.dataType }, result => {
          this.treeData = result.data
          this.dialogTreeData = result.data
          callBack && callBack()
          return true
        })
    },
    getZTreeCurrentNode() {
      let currentNode = this.zTreeObj.getSelectedNodes(true)
      if (currentNode) { // 有当前节点
        currentNode = currentNode[0]
      } else {
        currentNode = ''
      }
      return currentNode
    },
    handleParentNodes(isUpdate) {
      const isParent = this.$isNotEmpty(this.zTreeParentNode) && this.zTreeParentNode.level === 0
      const currentNode = isParent ? this.zTreeParentNode : this.getZTreeCurrentNode()
      if (currentNode) { // 有当前节点
        this.tempNode.parentId = currentNode.parentId
        this.initTreeData()
      }
      this.treeParentNodes = this.dialogTreeData.filter(node => { return !node.itemKey })
    },
    initTreeData() {
      this.$callApiParams('selectClassifyList',
        { dataType: this.dataType }, result => {
          this.dialogTreeData = result.data
          return true
        })
    },
    getDialogTreeData() {
      return this.dialogTreeData
    },
    checkBeforeCallApiSave(savingData) { // 父组件业务保存时，需要额外传递的参数，以自动添加分类树节点
      var currentNode = this.getZTreeCurrentNode()
      if (!currentNode && !this.leafCanBeTopLevel) {
        this.$message.error('请指定保存到哪个文件夹')
        return false
      }
      return true
    },
    getExParamsCallApiSave(savingData) { // 父组件业务保存时，需要额外传递的参数，以自动添加分类树节点
      // 取树的当前节点为父节点，如果当前节点是文件夹，则取该节点直接作为父节点
      var parentId = ''
      var currentNode = this.getZTreeCurrentNode()
      var saveExtraParamsStr = ''
      if (this.$isNotEmpty(this.saveExtraParams)) {
        saveExtraParamsStr = '&' + Object.keys(this.saveExtraParams)
          .map(key => key + '=' + this.saveExtraParams[key])
          .join('&')
      }
      if (currentNode) {
        parentId = (!currentNode.isClass) ? currentNode.parentId : currentNode.id
      }
      return `&apiAutoClassifyTree=${this.dataType}&parentId=${parentId}${saveExtraParamsStr}`
    },
    postHandleCallApiSave(result) { // 父组件业务保存后，执行自动添加或更新分类树节点
      this.syncTreeNode(result.data)
    },
    saveNode() {
      this.$set(this.tempNode, 'dataType', this.dataType)
      this.$callApi('saveClassifyTreeNode', this.tempNode, result => {
        this.syncTreeNode(result.data)
        this.initTreeData()
        this.addNodeDlgVisible = false
      })
    },
    syncTreeNode(nodeData) { // 保存之后同步节点
      // oldSystemId返回时设置了当前账套，如果不同于数据的账套，此时直接返回
      if (nodeData?.oldSystemId && nodeData.oldSystemId !== nodeData.accountSetId) {
        return
      }
      const nodeBefore = this.zTreeObj.getNodeByParam('id', nodeData.id, null)
      const isUpdate = (nodeBefore !== null)
      if (isUpdate) {
        let isDiffParent = (nodeBefore.parentIdBefore && nodeBefore.parentIdBefore !== nodeData.parentId)
        // 2024.3.20 解决新增修改复制 树没有同步更新bug
        const TYPE = ['新增', '修改', '复制']
        if (TYPE.includes(this.btEditType)) {
          isDiffParent = (nodeBefore.parentIdBefore !== nodeData.parentId)
        }
        if (isDiffParent) {
          this.init()
          // 修改时移动到了新的文件夹，则直接删除旧节点，然后追加新节点
          this.$nextTick(() => {
            this.setCurrentItem(nodeBefore, nodeData.itemKey)
          })
        }
        const updateNode = this.zTreeObj.getNodeByParam('id', nodeData.id, null)
        const isLeafDif = updateNode.isLeaf !== nodeData.isLeaf
        if (updateNode.label !== nodeData.label ||
          updateNode.fiscalYear !== nodeData.fiscalYear ||
          isLeafDif) {
          updateNode.isLeaf = nodeData.isLeaf
          updateNode.isParent = !nodeData.isLeaf
          updateNode.label = nodeData.label
          updateNode.fiscalYear = nodeData.fiscalYear
          updateNode.sourceType = nodeData.sourceType
          updateNode.lockVersion = nodeData.lockVersion
          this.zTreeObj.updateNode(updateNode)
        }
        this.$emit('nodeUpdated', updateNode)
      } else {
        const parentNode = this.zTreeObj.getNodeByParam('id', nodeData.parentId, null)
        this.zTreeObj.addNodes(parentNode, -1, nodeData)
        if (!nodeData.isClass) {
          this.$nextTick(() => {
            this.$emit('leafNodeAdded', nodeData.id, nodeData.itemKey) // 新增叶子节点事件
          })
        }
      }
    },
    btAddClick() {
      this.tempNode = this.makeNewNode()
      this.handleParentNodes(false)
      this.btEditType = '新增'
      this.addNodeDlgVisible = true
    },
    btCopyClick() {
      this.btEditType = '复制'
      const isParent = this.$isNotEmpty(this.zTreeParentNode) && this.zTreeParentNode.level === 0
      const node = isParent ? this.zTreeParentNode : this.getZTreeCurrentNode()
      if (node == null) {
        this.$message.error('当前没有选中的节点')
        return
      }
      const nodeType = node.hasOwnProperty('children') ? '文件夹' : '节点'
      this.$confirm(`确定要复制当前${nodeType} [${node.label}] 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.isCopying = true
        const copySuccess = result => {
          this.isCopying = false
          this.init()
        }
        const copyFailed = result => {
          this.isCopying = false
          this.init()
        }
        this.$callApiParams(
          'copyClassifyList', { copyNodeId: node.id }, copySuccess, copyFailed)
      })
    },
    btEditClick() {
      const isParent = this.$isNotEmpty(this.zTreeParentNode) && this.zTreeParentNode.level === 0
      const currentNode = isParent ? this.zTreeParentNode : this.getZTreeCurrentNode()
      if (currentNode) {
        currentNode.parentIdBefore = currentNode.parentId
        this.tempNode = JSON.parse(JSON.stringify(currentNode))
        this.handleParentNodes(true)
        this.btEditType = '修改'
        this.addNodeDlgVisible = true
      } else {
        this.$message.error('当前没有选中的节点')
      }
    },
    btDeleteClick() {
      const isParent = this.$isNotEmpty(this.zTreeParentNode) && this.zTreeParentNode.level === 0
      const node = isParent ? this.zTreeParentNode : this.getZTreeCurrentNode()
      if (node == null) {
        this.$message.error('当前没有选中的节点')
      } else if (node.children && node.children.length > 0) {
        this.$message.error('请先删除子节点')
      } else {
        var nodeType = node.isParent ? '文件夹' : '节点'
        this.$confirm(`确定要删除当前${nodeType} [${node.label}] 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var doActionAfterDeleteSuccess = result => {
            this.$emit('nodeDeleted', node)
            this.zTreeObj.removeNode(node)
            var parentNode = node.getParentNode()
            // 如果父节点下的子节点不存在
            if (this.$isNotEmpty(parentNode.children) || this.$isEmpty(parentNode.children)) {
              // 修改父节点图标为文件夹样式
              parentNode.isParent = true
              parentNode.open = true
              this.zTreeObj.updateNode(parentNode)
            }
            this.isDeleting = false
            this.initTreeData()
            return true
          }
          var doActionAfterDeleteFailed = result => {
            this.isDeleting = false
          }

          // 自定义删除节点方法
          if (this.customDeletedFn) {
            this.customDeletedFn(doActionAfterDeleteSuccess, doActionAfterDeleteFailed)
            return
          }

          if (!node.isClass && !this.showNodeType) {
            if (this.deleteApiKey === '') {
              this.$message.error('没有设置节点删除的apiKey')
            } else {
              const apiKey = this.isBlockForm ? 'deleteBlockMeta' : this.deleteApiKey
              const ps = {
                ids: node.itemKey,
                viewId: this.isBlockForm ? node.actualKey : '',
                apiAutoClassifyTree: `DELETE_${this.dataType}` }
              const deleteParams = Object.assign(ps, this.deleteExtraParams)
              this.isDeleting = true
              this.$callApiParams(apiKey, deleteParams,
                doActionAfterDeleteSuccess, doActionAfterDeleteFailed)
            }
          } else {
            this.isDeleting = true
            this.$callApiParams(
              'deleteClassifyTreeNode', { ids: node.id },
              doActionAfterDeleteSuccess, doActionAfterDeleteFailed)
          }
        })
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    nodeClick(event, treeId, treeNode) {
      // 点击当前节点，不需要再次从远端加载数据
      // if (treeNode.check_Child_State === 0 || treeNode.children != null) return
      this.$emit('customNodeClick', treeNode)
      if (treeNode.isLeaf && (this.showNodeType === true ? true : (treeNode.itemKey !== this.currentItemKey))) {
        var exData = {}
        this.$emit('nodeClick', treeNode, exData)
        this.setCurrentItem(treeNode, treeNode.itemKey)
      }
    },
    setCurrentItem(node, itemKey) { // 设置当前节点，null则取消选中
      if (this.zTreeObj) { // 解决 this.zTreeObj 控制台报错
        var thisNode = this.zTreeObj.getNodeByParam('id', node, null)
        this.zTreeObj.selectNode(thisNode, false, true)
        this.currentItemKey = itemKey
      }
    },
    changeCurrentItemKey(itemKey) {
      this.currentItemKey = itemKey
    },
    makeNewNode() {
      return {
        id: '',
        label: '新建分类',
        children: [],
        parentId: '',
        isLeaf: false,
        fiscalYear: '2022',
        sourceType: '指标+事前申请',
        isClass: true
      }
    },
    getCurrentNode() {
      return this.getZTreeCurrentNode()
    },
    setCurrentParentNode(currentNode) {
      this.zTreeObj.selectNode(this.zTreeObj.getNodeByParam('id', currentNode.parentId, null))
    }
  }
}
</script>

<style lang="scss">
#classifyZtree{height: 100%;}
  .vue-bifrostIcApp .common-page .el-tree-node__expand-icon.is-leaf {
    display: none;
  }
  .vue-bifrostIcApp .common-page
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #dfe0e2;
  }
  .vue-bifrostIcApp .common-page
  .el-tree--highlight-current .el-tree-node.is-focusable > div.current-leaf {
    background-color: #8cc4ff;
    color: #fff;
  }
  .vue-bifrostIcApp .common-page .el-tree--highlight-current .el-tree-node.is-current
  > .el-tree-node__content .el-tree-node__expand-icon{
    color: #fff;
  }
  .vue-bifrostIcApp .common-page .el-tree--highlight-current .el-tree-node.is-current
  > .el-tree-node__content span.is-leaf {
    color: #8cc4ff;
  }

  .vue-bifrostIcApp .common-page .classifytree-filter-container {
    display: flex;
  }
  .vue-bifrostIcApp .common-page .classifytree-filter-container button {
    padding: 5px;
    border: none;
    margin-left: 2px;
  }
  .vue-bifrostIcApp .common-page .classifytree-filter-button-container {
    width: 128px;
    padding-top: 3px;
  }
  .paddingLeft5 {
    padding-left: 5px;
  }
  .parentLevelDiy {
    background: #BBBBBB !important;
  }
</style>

