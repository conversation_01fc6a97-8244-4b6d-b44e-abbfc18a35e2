<template>
  <div class="flex-row container">
    <div class="flex-1 container-box container-left">
      <span class="container-box-txt">{{componentName}}</span>
      <sup-tree :setting="composeComTreeSetting"
        class="container-box-tree"
        :isPopover="false"
        ref='classParentTree'
        :is-get-child="true"
        :nodes="composeComNodes"
        :checked-values="value.component"
        placeholder="请选择"
        @getCheckObjs="getComponentCheckObjs"
        @onCreated="componentTreeCreated"
      />
    </div>
    <div class="flex-1 container-box container-right">
      <span class="container-box-txt">账套列表</span>
      <sup-tree :setting="treeSetting"
        class="container-box-tree"
        :isPopover="false"
        ref='classParentTree'
        :is-get-child="true"
        :nodes="bookSetNodes"
        :checked-values="value.bookSet"
        placeholder="请选择"
        @getCheckObjs="getBookSetCheckObjs"
        @onCreated="BookSetTreeCreated"
      />
    </div>
  </div>
</template>

<script>
/**
 * 当前组件的v-model需要是一个对象
 * {
 *   bookSet: [], // 数组id
 *   component: [] // 数组id
 * }
 */
export default {
  name: 'bookSetZtree',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    // 请求组件数据的params,包含apiKey
    componentApiParams: {
      type: Object,
      default: () => ({})
    },
    // 组件数据nodes
    componentNodes: {
      type: Array,
      default: () => []
    },
    // 组件名称
    componentName: {
      type: String,
      default: '组件列表'
    },
    comTreeSetting: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 默认配置
      treeSetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'label',
            id: 'id'
          }
        }
      },
      // 组件不请求的nodes
      comNodes: [],
      // 账套不请求的nodes
      bookSetNodes: [],
      // 树实例bookSet，component
      zTreeObjs: {},
      // 树节点的map（id为key）bookSet，component
      nodesMap: {}
    }
  },
  watch: {
    value: {
      handler() {
        this.asyncStatus()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getBookSets()
    this.getComponentNodes()
  },
  computed: {
    composeComTreeSetting() {
      return Object.assign(this.$clone(this.treeSetting), this.comTreeSetting)
    },
    composeComNodes() {
      const nodes = this.componentApiParams.apiKey ? this.comNodes : this.componentNodes
      return nodes
    }
  },
  methods: {
    // 设置树节点的map（id为key）bookSet，component
    setMap(key, nodes) {
      this.nodesMap[key] = {}
      const recursionSet = (nodes) => {
        nodes.forEach(node => {
          this.nodesMap[key][node.id] = node
          if (this.$isNotEmpty(node.children)) {
            recursionSet(node.children)
          }
        })
      }
      recursionSet(nodes)
    },
    BookSetTreeCreated(zTreeObj) {
      const nodes = zTreeObj.getNodes()
      this.setMap('bookSet', nodes)
      this.zTreeObjs.bookSet = zTreeObj
      if (this.$isNotEmpty(nodes)) {
        this.asyncStatus()
      }
    },
    componentTreeCreated(zTreeObj) {
      const nodes = zTreeObj.getNodes()
      this.setMap('component', nodes)
      this.zTreeObjs.component = zTreeObj
      if (this.$isNotEmpty(nodes)) {
        this.asyncStatus()
      }
    },
    // v-model值改变时同步树的状态
    asyncStatus() {
      const keys = Object.keys(this.zTreeObjs)
      // 选中父节点
      const checkParentNode = (node, { nodeMap, treeObj }) => {
        if (!node.parentId) return
        const parentNode = nodeMap[node.parentId]
        if (parentNode) {
          treeObj.checkNode(parentNode, true)
          checkParentNode(parentNode, { nodeMap, treeObj })
        }
      }
      keys.map(item => {
        const nodeMap = this.nodesMap[item] || {}
        const checks = this.value[item] || []
        const treeObj = this.zTreeObjs[item]
        treeObj.checkAllNodes(false)
        checks.map(id => {
          const node = nodeMap[id]
          if (node) {
            treeObj.checkNode(node, true)
            checkParentNode(node, { nodeMap, treeObj })
          }
        })
      })
    },
    getComponentNodes() {
      if (!this.componentApiParams.apiKey) {
        return
      }
      this.$callApiParams(this.componentApiParams.apiKey, this.componentApiParams, (result) => {
        if (result.success) {
          this.comNodes = result.data || []
        }
        return true
      }, () => true)
    },
    // 请求账套nodes信息
    getBookSets() {
      this.$callApiParams('选择用户账套', {}, (result) => {
        if (result.success) {
          this.bookSetNodes = result.data || []
        }
        return true
      }, () => true)
    },
    // 更改账套的value
    getBookSetCheckObjs(nodes) {
      const data = {
        ...this.value,
        bookSet: nodes.map(item => item.id)
      }
      this.$emit('input', data)
    },
    // 更改组件的value
    getComponentCheckObjs(nodes) {
      const data = {
        ...this.value,
        component: nodes.map(item => item.id)
      }
      this.$emit('input', data)
    }
  }
}
</script>

<style lang='scss' scoped>
.container {
  height: 100%;
  &-box {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    &-tree {
      overflow: hidden;
      flex: 1;
      padding: 10px 5px;
      border: #DDDDDD solid 1px;
    }
    &-txt {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;
    }
  }
  &-left {
    margin-right: 20px;
  }
}
</style>
