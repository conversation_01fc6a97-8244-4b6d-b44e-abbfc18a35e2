<template>
  <el-table
    class="eTable"
    ref="eTable"
    :header-cell-style="{ background: '#F0F5FF' }"
    border
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-table-column
      type="selection"
      width="55"
      align="center"
      v-if="selection"
    >
    </el-table-column>
    <el-table-column
      v-for="(item, index) in tableColumn"
      :show-overflow-tooltip="$isEmpty(item.tooltip) ? true : item.tooltip"
      :key="index"
      :prop="item.prop"
      :fixed="item.fixed"
      :label="item.aliasLabel"
      :width="item.aliasWidth"
      :min-width="item.minWidth"
      :sortable="item.sortable"
      :align="item.align"
    >
      <template #default="{ row, $index }">
        <slot
          name="slotColumn"
          v-if="item.slotColumn"
          :row="row"
          :item="tableColumn[index]"
          :index="$index"
        ></slot>
        <span v-else-if="item.type === 'money'">
          {{ $formatMoney(row[item.prop]) }}
        </span>
        <div
          v-else
          class="rowTooltip"
          :class="item.align"
        >
          {{ row[item.prop] }}
        </div>
      </template>
      <template #header>
        <slot v-if="item.slotHead" name="header" :item="item"></slot>
        <span v-else><span v-if="item.isRequired" style="color: red">*</span>{{item.aliasLabel}}</span>
      </template>
    </el-table-column>
    <template slot="empty">
      <div
        style="text-align: center; color: gray"
        :style="`position:${loading ? 'relative' : 'static'}`"
      >
        <div v-if="loading" class="el-loading-spinner">
          <svg viewBox="25 25 50 50" class="circular">
            <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
          </svg>
        </div>
        <p v-else>暂无数据</p>
      </div>
    </template>
  </el-table>
</template>

<script>
export default {
  name: 'e-table',
  props: {
    tableColumn: {
      type: Array,
      default() {
        return []
      }
    },
    selection: {
      // 是否开启多选模式
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean, // 加载状态
      default: true
    }
  },
  data() {
    return {}
  },
  methods: {
    getTable() {
      return this.$refs.eTable
    }
  }
}
</script>

<style lang="scss" scoped>
.eTable {
  overflow: hidden;
  height: 100%;
  .left {
    text-align: left;
  }
  .center {
    text-align: center;
  }
  .right {
    text-align: right;
  }
  .el-loading-spinner {
    display: flex;
    top: 50%;
    left: 50%;
    margin-top: 0;
    width: auto;
    text-align: center;
    position: absolute;
    transform: translate(-50%, -50%);
  }
  .rowTooltip {
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
    // display: inline-block;
  }
  .el-tooltip {
    display: flex;
    justify-content: center;
  }
  .el-table__empty-block {
    width: 100% !important;
    position: sticky;
    left: 0;
  }
}
</style>