<template>
    <div>
        <el-input
                placeholder="查找"
                prefix-icon="el-icon-search"
                v-model="filterText"
                class="checkboxtree-filter-input">
        </el-input>
        <el-tree
            ref="checkboxtree"
            :data="data"
            show-checkbox
            :node-key="nodeKeyValue"
            :props="defaultProps"
            :default-expand-all="true"
            :filter-node-method="filterNode"
            @check-change="checkChange">
        </el-tree>

    </div>
</template>

<script>

export default {
  name: 'checkboxtree',
  props: {
    propsVaule: {
      type: Object, default: () => {
        return {
          children: 'children',
          label: 'label'
        }
      }
    },
    selectApiKey: { type: String, default: '' },
    isNewData: { type: Boolean, default: false },
    nodeKey: { type: String, default: '' },
    checkedKeys: { type: Array, default: () => [] },
    params: { type: Object, default: () => ({}) }
  },
  watch: {
    filterText(val) {
      this.$refs.checkboxtree.filter(val)
    }
  },
  mounted() {
    this.init()
  },
  data() {
    return {
      data: [],
      defaultProps: this.propsVaule,
      nodeKeyValue: this.nodeKey,
      filterText: ''
    }
  },
  methods: {
    init() {
      if (!this.selectApiKey || this.selectApiKey === '') {
        this.$message.error('查询数据接口不能为空')
      } else {
        this.$callApiParams(this.selectApiKey,
          this.params, result => {
            this.data = result.data
            this.$emit('getResult', result)
            return true
          })
      }
    },
    checkChange() {
      this.$refs.checkboxtree.getCheckedNodes()
      const checkList = this.$refs.checkboxtree.getCheckedNodes()
      this.$emit('checkChange', checkList)
    },
    getCheckedNodesData() {
      var result = this.$refs.checkboxtree.getCheckedKeys(true)// 这里的true表示只获取叶子节点的keys
      return result
    },
    getCheckedNodes() {
      var result = this.$refs.checkboxtree.getCheckedNodes(true)// 这里的true表示只获取叶子节点
      return result
    },
    setCheckedKeys(array) {
      if (this.isNewData) {
        if (!this.selectApiKey || this.selectApiKey === '') {
          this.$message.error('查询数据接口不能为空')
        } else {
          this.$callApiParams(this.selectApiKey,
            {}, result => {
              this.data = result.data
              this.$refs.checkboxtree.setCheckedKeys(array)
              return true
            })
        }
      } else {
        this.$refs.checkboxtree.setCheckedKeys(array)
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    }
  }
}
</script>

<style>
    .vue-bifrostIcApp .common-page .checkboxtree-filter-input .el-input__inner {
        line-height: 28px;
        height: 28px;
        padding: 5px 5px 5px 21px;
    }
</style>
