<template>
  <div id="not-register-dialog">
    <el-dialog
      append-to-body
      :title="'无需登记'"
      :visible.sync="isdialog"
      width="30%"
      :close-on-click-modal='false'
      @close="handleClose">
      <page>
        <template #pageContent>
          <el-form
            ref="amountForm"
            :model="amountForm"
            label-width="150px"
            :disabled="isDisabled"
            style="border: 1px solid #ccc;padding-top: 20px;">
            <el-row>
            <el-col :span="11">
              <el-form-item label="预算金额" prop="totalPrice"  :required="isRequired" style="width:420px;">
                <InputMoney v-model="amountForm.totalPrice" :disabled="true" />
              </el-form-item>
            </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item label="采购金额" prop="purAmount" style="width:420px;">
                  <InputMoney v-model="amountForm.purAmount" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>

      </page>
      <template #footer>
        <el-button @click="handleClose('amountForm')" v-if="!contracdetails"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('amountForm')"
          v-if="!contracdetails"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'notRegisterDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.contracdetails = bool
    },
    isDisabled(bool) {
      this.isDisabled = bool
    }
  },
  mounted() {

  },
  data() {
    return {
      isdialog: this.dialog,
      contracdetails: this.isDetails,
      // eslint-disable-next-line vue/no-dupe-keys
      isDisabled: this.isdialog,
      amountForm: {
        totalPrice: '',
        purAmount: '',
        registrationMoney: ''
      },
      length: 0,
      orderIds: ''
    }
  },
  methods: {
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(formName) {
      const params = {}
      params.totalPrice = this.amountForm
      params.purAmount = this.purAmount
      params.orderIds = this.amountForm.orderIds
      this.$callApiParams('notRegister ', this.amountForm,
        result => {
          if (result.success) {
            this.handleClose()
            this.$parent.$refs.curdList.$children[0].reloadTable()
          }
        })
    },
    resetForm() {
      if (this.$refs['amountForm']) { // 清空form数据
        this.$nextTick(() => {
          this.$refs['amountForm'].clearValidate()
        })
      }
    },
    reload() {
      this.$reInit(this)
    }
  }
}

</script>

<style scoped>

</style>
