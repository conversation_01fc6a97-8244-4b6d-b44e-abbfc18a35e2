<template>
  <div id="place-an-order-dialog">
    <el-dialog
      append-to-body
      :title="'供应商登记'"
      :visible.sync="isdialog"
      width="50%"
      ref="resetFields"
      :close-on-click-modal='false'
      @close="handleClose">
      <page>
        <template #pageContent>
          <el-form
            ref="supplierForm"
            :model="supplierForm"
            label-width="150px"
            :disabled="orderDetails"
            :rules="rules"
            style="border: 1px solid #ccc;padding-top: 20px;">
            <el-row class="row-marginBottom">
              <el-col :span="11">
                <el-form-item label="采购计划号">
                  <el-input v-model="supplierForm.purPlanNum" placeholder="请输入采购计划号"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="发票号码">
                  <el-input v-model="supplierForm.invoiceNum" placeholder="请输入发票号码"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="供应商名称" prop="supplierName">
                  <el-input v-model="supplierForm.supplierName" :readonly="true" placeholder="请输入供应商名称">
                    <el-button slot="append" icon="el-icon-search" @click="selectSupplier"></el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="中标日期" prop="bidDate">
                  <el-date-picker v-model="supplierForm.bidDate"  value-format="yyyy-MM-dd" type="date" placeholder="选择日期"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="供应商联系电话" prop="supplierTel">
                  <el-input v-model="supplierForm.supplierTel" placeholder="请输入供应商联系电话"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="供应商类别">
                  <el-input v-model="supplierForm.supplierCategory" placeholder="请输入供应商类别"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="供应商联系人" prop="supplierContacts">
                  <el-input v-model="supplierForm.supplierContacts" placeholder="请输入供应商联系人"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="tableQuota" v-if="isShow">
            <el-table style="width: 100%;height: 100%" border :data="tableData" ref="purForm">
              <el-table-column
                prop="purCode"
                label="采购编码"
                width="150"
                show-overflow-tooltip
                align="center">
              </el-table-column>
              <el-table-column
                prop="projectName"
                label="采购项目"
                width="178"
                show-overflow-tooltip
                align="center">
              </el-table-column>
              <el-table-column
                prop="purArticlesName"
                label="采购名称"
                width="170"
                show-overflow-tooltip
                align="center">
              </el-table-column>
              <el-table-column
                prop="purQuantity"
                label="数量"
                width="80"
                show-overflow-tooltip
                align="center">
              </el-table-column>
              <el-table-column
                prop="confirmNum"
                label="确认数量"
                width="80"
                show-overflow-tooltip
                align="center">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.confirmNum" oninput="value=value.replace(/[^\d]/g,'')" :readonly="false"/>
                </template>
              </el-table-column>
              <el-table-column
                prop="totalPrice"
                label="预算金额"
                width="130"
                show-overflow-tooltip
                align="center">
              </el-table-column>
              <el-table-column
                prop="confirmAmount"
                label="采购金额"
                width="130"
                show-overflow-tooltip
                align="center">
                <template slot-scope="scope">
                  <InputMoney v-model="scope.row.purAmount" :is-unit-show="false" :readonly="false"/>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </page>
      <template #footer>
        <el-button @click="handleClose()" v-if="!orderDetails"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('supplierForm')"
          v-if="!orderDetails"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'placeAnOrderDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.orderDetails = bool
    }
  },
  mounted() {
    this.getNowDateTime()
  },
  data() {
    return {
      isdialog: this.dialog,
      orderDetails: this.isDetails,
      supplierForm: {
        purPlanNum: '',
        invoiceNum: '',
        supplierName: '',
        bidDate: '',
        supplierTel: '',
        supplierCategory: '',
        supplierContacts: '',
        contractingPartyId: '',
        purOrderId: ''
      },
      isShow: false,
      orderIds: '',
      purCategory: '',
      tableData: [],
      rules: {
        supplierName: [
          { required: true, message: '请输入供应商名称', trigger: 'change' }
        ],
        bidDate: [
          { required: true, message: '请输入中标日期', trigger: 'change' }
        ],
        supplierTel: [
          { required: true, message: '请输入供应商联系电话', trigger: 'change' }
        ],
        supplierContacts: [
          { required: true, message: '请输入供应商联系人', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init(rows) {
      this.isdialog = true
      this.tableData = rows
    },
    selectSupplier() {
      this.$refDataCommon('选择合约方',
        selectedData => {
          this.$nextTick(() => {
            this.supplierForm.contractingPartyId = selectedData.list[0].id
            this.supplierForm.supplierName = selectedData.list[0].合约方名称
            this.supplierForm.supplierTel = selectedData.list[0].联系方式
            this.supplierForm.supplierCategory = selectedData.list[0].category
            this.supplierForm.supplierContacts = selectedData.list[0].法人代表
          })
        })
    },
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.supplierForm.purPlanNum = null
      this.supplierForm.invoiceNum = null
      this.supplierForm.supplierCategory = null
      this.$emit('update:dialog', false)
      this.$refs['supplierForm'].resetFields()
    },
    handleSumbit(supplierForm) {
      this.$refs[supplierForm].validate((valid) => {
        if (valid) {
          var PurOrderVo = {}
          PurOrderVo.ids = []
          PurOrderVo.ids = this.orderIds.split(',')
          PurOrderVo.supplierEntity = this.supplierForm
          if (this.isShow === true) {
            PurOrderVo.purOrderEntities = this.$refs.purForm.tableData
          }
          this.$callApi('saveSupplier ', PurOrderVo,
            result => {
              if (result.success) {
                this.handleClose()
                this.$parent.$refs.curdList.$children[0].reloadTable()
              }
            })
        } else {
          return false
        }
      })
    },

    resetForm() {
      if (this.$refs['supplierForm']) { // 清空form数据
        this.$nextTick(() => {
          this.$refs['supplierForm'].clearValidate()
        })
      }
    },
    getNowDateTime() {
      this.supplierForm.bidDate = this.$nowFormatDate()
    }
  }
}

</script>

<style scoped>

</style>
