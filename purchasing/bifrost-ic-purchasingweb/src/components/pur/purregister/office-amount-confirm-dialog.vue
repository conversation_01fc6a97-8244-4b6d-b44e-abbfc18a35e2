<template>
  <div id="amount-confirm-dialog">
    <el-dialog
      append-to-body
      :title="'办公消耗用品金额确认'"
      :visible.sync="isdialog"
      width="30%"
      :close-on-click-modal='false'
      @close="handleClose">
      <page>
        <template #pageContent>
          <el-form
            ref="confirmForm"
            :model="confirmForm"
            label-width="150px"
            :rules="rules"
            style="border: 1px solid #ccc;padding-top: 20px;">
            <el-row>
              <el-col :span="11">
                <el-form-item label="预算金额" prop="totalPrice"  :required="isRequired" style="width:420px;">
                  <InputMoney v-model="confirmForm.totalPrice" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item label="确认金额" prop="confirmAmount" style="width:420px;">
                  <InputMoney v-model="confirmForm.confirmAmount" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>

      </page>
      <template #footer>
        <el-button @click="handleClose('confirmForm')"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('confirmForm')"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'amountConfirmDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.contracdetails = bool
    }
  },
  data() {
    return {
      confirmForm: {
        totalPrice: '',
        confirmAmount: ''
      },
      ids: '',
      isdialog: this.dialog,
      rules: {
        confirmAmount: [
          { required: true, message: '请输入确认金额', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(confirmForm) {
      this.$refs[confirmForm].validate((valid) => {
        if (valid) {
          const params = {}
          params.confirmAmount = this.confirmForm
          params.ids = this.ids
          this.$callApiParams('amountChangeConfirm ', this.confirmForm,
            result => {
              if (result.success) {
                this.handleClose()
                this.$parent.$refs.curdList.$children[0].reloadTable()
              }
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      if (this.$refs['confirmForm']) { // 清空form数据
        this.$nextTick(() => {
          this.$refs['confirmForm'].clearValidate()
        })
      }
    },
    reload() {
      this.$reInit(this)
    }
  }
}

</script>

<style scoped>

</style>
