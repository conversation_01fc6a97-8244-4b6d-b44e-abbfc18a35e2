<template>
  <div id="num-confirm-dialog">
    <el-dialog
      append-to-body
      :title="'办公消耗用品数量确认'"
      :visible.sync="isdialog"
      width="30%"
      :close-on-click-modal='false'
      @close="handleClose">
      <page>
        <template #pageContent>
          <el-form
            ref="confirmForm"
            :model="confirmForm"
            label-width="150px"
            :rules="rules"
            style="border: 1px solid #ccc;padding-top: 20px;">
            <el-row>
              <el-col :span="11">
                <el-form-item label="原数量">
                  <el-input v-model="confirmForm.purQuantity" :disabled="true" style="width:270px;"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item label="新数量" prop="confirmNum">
                  <el-input type="number" min="0" v-model="confirmForm.confirmNum"
                            :disabled="false"
                            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                            style="width:270px;"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>

      </page>
      <template #footer>
        <el-button @click="handleClose('confirmForm')"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('confirmForm')"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'numConfirmDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.contracdetails = bool
    }
  },
  data() {
    return {
      confirmForm: {
        purQuantity: '',
        confirmNum: ''
      },
      ids: '',
      isdialog: this.dialog,
      rules: {
        confirmNum: [
          { required: true, message: '请输入新数量', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(confirmForm) {
      this.$refs[confirmForm].validate((valid) => {
        if (valid) {
          if (this.confirmForm.confirmNum === 0) {
            this.$message.error('新数量不能为0')
          } else {
            const params = {}
            params.purQuantity = this.confirmForm
            params.ids = this.ids
            this.$callApiParams('numChangeConfirm ', this.confirmForm,
              result => {
                if (result.success) {
                  this.handleClose()
                  this.$parent.$refs.curdList.$children[0].reloadTable()
                }
              })
          }
        } else {
          return false
        }
      })
    },
    resetForm() {
      if (this.$refs['confirmForm']) { // 清空form数据
        this.$nextTick(() => {
          this.$refs['confirmForm'].clearValidate()
        })
      }
    },
    reload() {
      this.$reInit(this)
    }
  }
}

</script>

<style scoped>

</style>
