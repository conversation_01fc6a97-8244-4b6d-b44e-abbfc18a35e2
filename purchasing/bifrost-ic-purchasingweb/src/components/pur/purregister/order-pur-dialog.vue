<template>
  <div id="order-pur-dialog">
    <el-dialog
      append-to-body
      :title="'供应商登记'"
      :visible.sync="isdialog"
      width="60%"
      :close-on-click-modal='false'
      @close="handleClose">
      <page>
        <template #pageContent>
          <el-form
            ref="purForm"
            :model="purForm"
            label-width="150px"
            :disabled="contracdetails"
            style="border: 1px solid #ccc;padding-top: 20px;">
            <el-row class="row-marginBottom">
              <el-col :span="11">
                <el-form-item label="采购编码">
                  <el-input v-model="purForm.purCode" :disabled="true"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="采购项目">
                  <el-input v-model="purForm.projectName" :disabled="true"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="采购名称">
                  <el-input v-model="purForm.productsName" :disabled="true"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="数量">
                  <el-input v-model="purForm.num" :disabled="true"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="确认数量">
                  <el-input v-model="purForm.confirmNum"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="预算金额">
                  <InputMoney v-model="purForm.budgetAmount" :is-unit-show="false" :readonly="false"
                                :disabled="true"/>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="采购金额">
                  <InputMoney v-model="purForm.purAmount" :is-unit-show="false"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>

      </page>
      <template #footer>
        <el-button @click="handleClose('purForm')" v-if="!contracdetails"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('purForm')"
          v-if="!contracdetails"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'orderPurDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.contracdetails = bool
    }
  },
  mounted() {

  },
  data() {
    return {
      isdialog: this.dialog,
      contracdetails: this.isDetails,
      purForm: {
        purCode: '',
        projectName: '',
        productsName: '',
        num: 0,
        confirmNum: 0,
        budgetAmount: 0,
        purAmount: 0
      }
    }
  },
  methods: {
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(purForm) {
      this.$refs[purForm].validate((valid) => {
        if (valid) {
          var PurOrderVo = {}
          PurOrderVo.supplierEntity = this.purForm
          this.$callApi(' ', PurOrderVo,
            result => {
              if (result.success) {
                this.handleClose()
                this.$parent.$refs.curdList.$children[0].reloadTable()
              }
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      if (this.$refs['purForm']) { // 清空form数据
        this.$nextTick(() => {
          this.$refs['purForm'].clearValidate()
        })
      }
    },
    reload() {
      this.$reInit(this)
    }
  }
}

</script>

<style scoped>

</style>
