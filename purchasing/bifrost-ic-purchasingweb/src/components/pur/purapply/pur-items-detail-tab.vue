<template>
  <div class="common-page multipleTabsSupplier">
    <pur-items-detail-list ref="purItemDetail"></pur-items-detail-list>
  </div>
</template>

<script>
export default {
  name: 'pur-items-detail-tab',
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      purType: ''
    }
  },
  methods: {
    init(dlg, params) {
      this.initAssembly(params.dataVo, {}, {})
    },
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.initAssembly(dataVo, this.baseListFormObj, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      if (this.purType === '脱贫地区农副产品采购' ||
          this.purType === '电子商场直购、竞价采购' ||
          this.purType === '1万以下服务类或货物类采购') {
        const purDirectItemDetail = this.$refs.purItemDetail.purDirectItemDetail
        const purBiddingItemDetail = this.$refs.purItemDetail.purBiddingItemDetail
        // 保存时应对 显示千分位的数值进行处理 否则后端JSON.parseObject 处理带千分位的数值会报错
        this.amountFormat(purDirectItemDetail)
        this.amountFormat(purBiddingItemDetail)
        dataVo.extData.purDirectItemDetail = purDirectItemDetail
        dataVo.extData.purBiddingItemDetail = purBiddingItemDetail
        return dataVo
      }
      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
      this.$refs.purItemDetail.showAfter(dataVo)
    },
    initAssembly(meta, mode, formFormat) {
      var col = meta.colItems.filter(item => item.labelOrigin === '采购单类型')[0]
      this.purType = col.defaultValue
      this.$nextTick(() => {
        if (meta.extData) {
          if (this.purType === '脱贫地区农副产品采购' ||
              this.purType === '电子商场直购、竞价采购' ||
              this.purType === '1万以下服务类或货物类采购') {
            const purDirectItemDetail = meta.extData.purDirectItemDetail
            const purBiddingItemDetail = meta.extData.purBiddingItemDetail
            this.amountFormat(purDirectItemDetail)
            this.amountFormat(purBiddingItemDetail)
            this.$refs.purItemDetail.purDirectItemDetail = purDirectItemDetail
            this.$refs.purItemDetail.purBiddingItemDetail = purBiddingItemDetail
          }
        }
      })
      this.$refs.purItemDetail.init(meta, mode, formFormat)
    },
    amountFormat(amounts) {
      amounts && amounts.map(item => {
        if (item.purPrice) {
          item.purPrice = this.$fixMoney(item.purPrice)
        }
        if (item.purAmount) {
          item.purAmount = this.$fixMoney(item.purAmount)
        }
      })
    }
  }
}
</script>
<style>
  .multipleTabsSupplier .supplier-list {
    height: 100%;
    padding-left: 0px;
  }

  .multipleTabsSupplier .supplier-list .supplier-container {
    height: calc(100% - 48px);
    padding-left: 0px;
  }
</style>
