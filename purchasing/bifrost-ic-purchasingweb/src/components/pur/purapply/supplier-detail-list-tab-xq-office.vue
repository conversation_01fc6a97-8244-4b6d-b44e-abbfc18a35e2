<template>
    <div class="common-page multipleTabsSupplier">
        <supplier-detail-list-xq-office ref="supplierDetail"></supplier-detail-list-xq-office>
    </div>
  </template>

<script>
import SupplierDetailListXqOffice from './supplier-detail-list-xq-office'
export default {
  name: 'supplier-detail-list-tab-xq-office',
  components: { SupplierDetailListXqOffice },
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      needInitPlanData: true,
      purType: ''
    }
  },
  methods: {
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.initAssembly(dataVo, {}, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      if (this.purType === '办公消耗品自行采购' || this.purType === '办公消耗品集中采购') {
        var purDetailed = this.$refs.supplierDetail.purDetailed
        this.amountFormat(purDetailed)
        dataVo.extData.purOffice = purDetailed
      }
      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
      dataVo.colItems.forEach(colItems => {
        if (colItems.labelOrigin === '采购单类型') {
          if (colItems.dataValue === '办公消耗品集中采购') {
            const forPurType = '集中采购'
            this.$refs.supplierDetail.formPurType = forPurType
          }
          if (colItems.dataValue === '办公消耗品自行采购') {
            const forPurTypes = '自行采购'
            this.$refs.supplierDetail.formPurType = forPurTypes
          }
        }
      })
    },
    initAssembly(meta, mode, formFormat) {
      var col = meta.colItems.filter(item => item.labelOrigin === '采购单类型')[0]
      this.purType = col.defaultValue
      if (this.purType === '办公消耗品自行采购' || this.purType === '办公消耗品集中采购') {
        if (this.$isEmpty(meta.data.id) && this.$isEmpty(meta.extData.initRefDataVoFromRemoteData)) {
          const purDetailed = []
          if (this.isMonthlyPayment) {
            this.dataId = meta.data.id
          }
          this.$refs.supplierDetail.purDetailed = purDetailed
        } else {
          this.needInitPlanData = false // 当是修改时，不触发初始化履行计划数据
          this.$refs.supplierDetail.purDetailed = meta.extData.purOffice
        }
        this.$refs.supplierDetail.init(meta, mode, formFormat)
      }
    },
    amountFormat(purDetailed) {
      purDetailed && purDetailed.map(item => {
        if (item.purAmount) {
          item.purAmount = this.$fixMoney(item.purAmount)
        }
        if (item.purPrice) {
          item.purPrice = this.$fixMoney(item.purPrice)
        }
        if (item.totalPrice) {
          item.totalPrice = this.$fixMoney(item.totalPrice)
        }
      })
    }
  }
}
</script>
  <style>
  .multipleTabsSupplier .supplier-list { height: 100%; padding-left: 0px; }
  .multipleTabsSupplier .supplier-list .supplier-container { height: calc(100% - 48px); padding-left: 0px; }
  </style>
