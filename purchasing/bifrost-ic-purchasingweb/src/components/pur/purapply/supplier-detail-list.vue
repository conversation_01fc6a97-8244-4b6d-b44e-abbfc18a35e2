<template>
  <div class="supplier-list" id="supplier">
    <div class="top-btns" v-show="!isAudit">
      <el-button class="btn-normal" @click="chooseBtn" :disabled='!showButton' icon="el-icon-circle-plus-outline">选择供应商
      </el-button>
      <el-button class="btn-normal" @click="addBtn" plain icon="el-icon-circle-plus-outline">添加供应商</el-button>
      <el-button class="btn-normal" @click="deleteBtn" :disabled='checkedRow.length === 0 || !showButton'
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table supplier-container">
      <el-table border :data="supplierData" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" align="center" width="30" v-if="!isAudit"></el-table-column>
        <el-table-column align="left" prop="contracpartyName" label="合约方名称"></el-table-column>
        <el-table-column align="center" prop="corporation" label="合约方法人代表">
        </el-table-column>
        <el-table-column align="left" prop="corporationTel" label="联系方式">
        </el-table-column>
        <el-table-column align="right" label="报价" v-if="showPrice">
          <template slot-scope="{row}">
            <InputMoney v-model="row.quotedPrice" :isUnitShow='false' :toFixed="2" class="quotedPrice"/>
          </template>
        </el-table-column>
        <el-table-column align="center" label="报价比例" v-if="showPrice" class="quotedPriceRatio">
          <template slot-scope="{row}">
            <el-input-number v-model="row.quotedPriceRatio" data-unit="%" :precision="2" :step="0.1" :min="0" :max="100">
            </el-input-number>
          </template>
        </el-table-column>
        <el-table-column align="center" label="是否中标" v-if="showPrice">
          <template slot-scope="{row}">
            <el-select clearable v-model="row.isCheck" class="isCheck"
                       placeholder="是否中标">
              <el-option
                v-for="item in isCheck"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <contracpartydialog ref="contracpartydialog" :dialog.sync="isDialog" @reload='reload'></contracpartydialog>
  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'supplier-detail-list',
  data() {
    return {
      checkedRow: [],
      supplierData: [],
      isDialog: false,
      isChange: false,
      showButton: true,
      showPrice: false,
      isCheck: [
        { eleName: '是' },
        { eleName: '否' }
      ],
      isAudit: false
    }
  },
  methods: {
    init(meta, mode, formFormat) {
      if (this.$isNotEmpty(formFormat)) {
        formFormat.addShowErrorCallbacks({
          '供应商错误提示': errorItems => {
            if (this.$isNotEmpty(errorItems)) {
              var planIndexes = []
              var keys = Object.keys(errorItems)
              keys.forEach(key => {
                planIndexes.push(key.replace('supplierInfo', ''))
              })
              this.showPlanError(planIndexes)
            }
          }
        })
      }
      this.hideError()
    },
    showPlanError(planIndexes) {
      this.hideError()
      if (this.$isNotEmpty(planIndexes)) {
        var $planTableTr = $('.supplier-container table tr')
        $.each($planTableTr, (index, item) => {
          const planIndex = index - 1 // 目的是为了排除掉第一个tr(列头)
          if (planIndexes.indexOf(planIndex + '-contracpartyName') > -1) {
            $(item).find('.contracpartyName').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-corporation') > -1) {
            $(item).find('.corporation').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-corporationTel') > -1) {
            $(item).find('.corporationTel').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-quotedPrice') > -1) {
            $(item).find('.quotedPrice').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-isCheck') > -1) {
            $(item).find('.isCheck').find('input').addClass('planError')
          }
        })
      }
    },
    hideError() {
      var $rows = $('.supplier-container .el-table__row')
      $.each($rows, (index, item) => {
        $(item).find('.planError').removeClass('planError')
      })
    },
    chooseBtn() {
      this.$refDataCommon('选择合约方', selectedData => {
        for (var item of selectedData.list) {
          var purSupplierDetail = {}
          purSupplierDetail.contracpartyName = item.合约方名称
          purSupplierDetail.corporationTel = item.corporationTel
          purSupplierDetail.corporation = item.corporation
          purSupplierDetail.quotedPrice = '0.00'
          purSupplierDetail.quotedPriceRatio = ''
          purSupplierDetail.contractPartyId = item.ID
          for (var i = 0; i < this.supplierData.length; i++) {
            if (this.supplierData[i].contractPartyId === item.ID) {
              return
            }
          }
          this.supplierData.push(purSupplierDetail)
        }
      }, { multiple: true, STATUS_eq: '启用' })
    },
    deleteBtn() {
      for (var i = 0; i < this.checkedRow.length; i++) {
        var index = this.supplierData.indexOf(this.checkedRow[i])
        this.supplierData.splice(index, 1)
      }
    },
    handleSelectionChange(rows) {
      this.checkedRow = rows
    },
    reload(data) {
      data.contractPartyId = data.id
      if (!this.isChange) {
        if (this.$isEmpty(this.supplierData)) {
          this.supplierData = []
        }
        this.supplierData = this.supplierData.concat([], data)
      } else {
        this.supplierData.forEach(item => {
          if (item.id === this.changeItemId) {
            item = Object.assign(item, data)
          }
        })
      }
      if (this.$parent.reloadContracparty) {
        this.$parent.reloadContracparty(this.supplierData)
      }
      this.isChange = false
    },
    addBtn() {
      this.$refs.contracpartydialog.contracForm = {
        code: '',
        legalPersonType: '法人',
        status: '启用',
        type: '',
        level: '',
        isPreselection: '否',
        contracpartyName: '',
        unifiedSocialCreditCode: '',
        corporation: '',
        corporationTel: '',
        address: '',
        isMinorEnterprises: '是',
        contacts: '',
        contactsTel: '',
        identityCard: '',
        remark: '',
        payeeList: []
      }
      this.$refs.contracpartydialog.resetForm()
      this.isDialog = true
      this.$nextTick(() => {
        this.$refs.contracpartydialog.setValue()
      })
    }
  }
}
</script>

<style lang="scss">
.bottom-table {
  flex: 1;
  margin-top: 10px;
}

.supplier-list {
  /deep/ .planError {
    border-color: #ff5c00 !important;
  }
}

#supplier .top-btns {
  padding-bottom: 0px !important;
}
</style>

<style lang="scss" scoped>
.quotedPriceRatio[data-unit] {
  --el-input-number-unit-offset-x: 35px;
  position: relative;
}
.quotedPriceRatio[data-unit]::after {
  content: attr(data-unit);
  height: 100%;
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  right: var(--el-input-number-unit-offset-x);
  color: #999999;
}
.quotedPriceRatio[data-unit] .el-input__inner {
  padding-left: 30px;
  padding-right: calc(var(--el-input-number-unit-offset-x) + 12px);
}
</style>
