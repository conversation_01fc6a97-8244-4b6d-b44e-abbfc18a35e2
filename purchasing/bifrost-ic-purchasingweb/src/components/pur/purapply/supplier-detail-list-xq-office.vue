<template>
  <div class="plan-list">
    <div class="top-btns">
      <el-button size="mini" @click="addBtn()"  icon="el-icon-circle-plus-outline">添加明细</el-button>
      <el-button size="mini" @click="deleteBtn" :disabled='checkedRow.length === 0 || !havePlan || !showButton' plain icon="el-icon-delete">删除</el-button>
    </div>
    <div class="bottom-table plan-container">
      <el-table border :data="purDetailed" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" prop="purCodeDetail" label="采购编码" width="240">
          <template #header><span style="color:#f56c6c;">*</span>采购编码</template>
          <template slot-scope='{row}'>
            <el-input disabled placeholder="保存后自动添加" v-model="row.purCodeDetail" />
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purArticlesName" label="用品名称" width="240">
          <template slot-scope='{row}'>
            <el-input  v-if="havePlan" class="purArticlesName"
                       v-model="row.purArticlesName"/>
            <span v-else>{{row.purArticlesName}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="unit" label="计量单位" width="95">
          <template #header><span style="color:#f56c6c;">*</span>计量单位</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.unit" class="purMode"
                       placeholder="计量单位" v-if="havePlan" @change="hideError($index)">
              <el-option
                v-for="item in unit"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{row.unit}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purQuantity" label="数量">
          <template #header><span style="color:#f56c6c;">*</span>数量</template>
          <template slot-scope='{row}'>
            <el-input  v-if="havePlan"  type="number" min="1" @blur="calculateAmount(row)"
                       v-model="row.purQuantity"
                       onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
            <span v-else>{{row.purQuantity}}</span>
          </template>
        </el-table-column>
        <el-table-column label="预算单价"  width="150">
          <template #header><span style="color:#f56c6c;">*</span>预算单价</template>
          <template slot-scope='{row, $index}'>
            <el-input  v-if="havePlan"
                       class="money"
                       v-model="row.purPrice"
                       oninput="value=value.replace(/[^0-9.]/g,'')"
                       @blur="formFormatItem(row)"
                       @input="hideError($index)"
                       placeholder="输入预算单价"/>
            <span v-else>{{row.purPrice}}</span>
          </template>
        </el-table-column>
        <el-table-column label="预算总价" width="150">
          <template #header><span style="color:#f56c6c;">*</span>预算总价</template>
          <template slot-scope='{row, $index}'>
            <el-input  v-if="havePlan"
                       class="money"
                       v-model="row.totalPrice"
                       oninput="value=value.replace(/[^0-9.]/g,'')"
                       @blur="formFormatItem(row)"
                       @input="hideError($index)"
                       placeholder="输入预算总价"/>
            <span v-else>{{row.totalPrice}}</span>
          </template>
        </el-table-column>
          <el-table-column label="采购金额"  width="150">
            <template slot-scope='{row, $index}'>
              <el-input  v-if="havePlan"
                         class="money"
                         v-model="row.purAmount"
                         oninput="value=value.replace(/[^0-9.]/g,'')"
                         @blur="formFormatItem(row)"
                         @input="hideError($index)"
                         placeholder="输入申请金额"/>
              <span v-else>{{row.purAmount}}</span>
            </template>
        </el-table-column>
        <el-table-column align="left" prop="remark" label="备注">
          <template slot-scope='{row}'>
            <el-input  v-if="havePlan"
                       v-model="row.remark"/>
            <span v-else>{{row.remark}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purType" label="采购类型">
          <template #header><span style="color:#f56c6c;">*</span>采购类型</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.purType" class="purMode"
                       placeholder="采购类型" v-if="havePlan" disabled @change="hideError($index)">
              <el-option
                v-for="item in purType"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{row.purType}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purMode" label="采购方式">
          <template #header><span style="color:#f56c6c;">*</span>采购方式</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.purMode" class="purMode"
                       placeholder="采购方式" v-if="havePlan" @change="hideError($index)">
              <el-option
                v-for="item in purMode"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{row.purMode}}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'supplier-detail-list-xq-office',
  data() {
    return {
      checkedRow: [],
      purDetailed: [],
      isDialog: false,
      isChange: false,
      havePlan: true,
      showButton: true,
      contractList: [], // 合约方信息
      purModeList: [], // 履行阶段
      purMode: [], // 采购方式
      unit: [], // 计量单位
      purType: [], // 采购类型
      formPurType: ''
    }
  },
  methods: {
    formFormatItem(item) {
      item.purQuantity = item.purQuantity.replace(/-/g, '')
      item.totalPrice = item.purQuantity * this.$unFormatMoney(item.purPrice)
      item.purAmount = this.$formatMoney(item.purAmount)
      item.totalPrice = this.$formatMoney(item.totalPrice)
      item.purPrice = this.$formatMoney(item.purPrice)
    },
    selectpurModeList() {
      this.$callApiParams('selectPurType', {}, result => {
        if (result.success) {
          this.unit = result.data.meterType
          this.purMode = result.data.performStage
          this.purType = result.data.purType
        }
        return true
      })
    },
    init(meta, mode, formFormat) {
      this.selectpurModeList()
      formFormat.addShowErrorCallbacks({
        '履行计划错误提示': errorItems => {
          if (this.$isNotEmpty(errorItems)) {
            var planIndexes = []
            var keys = Object.keys(errorItems)
            keys.forEach(key => {
              planIndexes.push(key.replace('planInfo', ''))
            })
            this.showPlanError(planIndexes)
          }
        }
      })
    },
    showPlanError(planIndexes) {
      if (this.$isNotEmpty(planIndexes)) {
        var $planTableTr = $('.plan-container table tr')
        $.each($planTableTr, (index, item) => {
          const planIndex = index - 1 // 目的是为了排除掉第一个tr(列头)

          if (planIndexes.indexOf(planIndex + '-contracpartyName') > -1) {
            $(item).find('.contracpartyName').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-purMode') > -1) {
            $(item).find('.purMode').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planMoney') > -1) {
            $(item).find('.planMoney').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planPaymentDate') > -1) {
            $(item).find('.planPaymentDate').find('input').addClass('planError')
          }
        })
      }
    },
    hideError(rowIndex) {
      var $rows = $('.plan-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.planError').removeClass('planError')
        }
      })
    },
    addBtn() {
      const data = {}
      data.purCode = ''
      data.purCodeDetail = ''
      data.purArticlesName = ''
      data.unit = ''
      data.purQuantity = ''
      data.purPrice = ''
      data.totalPrice = ''
      data.purAmount = ''
      data.remark = ''
      data.purType = this.formPurType
      data.purMode = ''
      this.purDetailed = this.purDetailed.concat([], data)
    },
    deleteBtn() {
      for (var i = 0; i < this.checkedRow.length; i++) {
        var index = this.purDetailed.indexOf(this.checkedRow[i])
        this.purDetailed.splice(index, 1)
      }
    },
    handleSelectionChange(rows) { this.checkedRow = rows },
    reload(data) {
      if (!this.isChange) {
        this.purDetailed = this.purDetailed.concat([], data)
      } else {
        this.checkedRow[0] = Object.assign(this.checkedRow[0], data)
      }
      this.isChange = false
    },
    calculateAmount(row) {
      // row.purQuantity = row.purQuantity.replace(/-/g, '')
      row.purPrice = this.$fixMoney(row.purPrice)
      row.totalPrice = row.purQuantity * row.purPrice
      row.totalPrice = this.$formatMoney(row.totalPrice)
    }
  }
}
</script>

<style scoped lang="scss">
  .plan-list{
    padding: 3px 0px 3px 0px;
    width: 100%;
    height: 100% !important;
    display: flex;
    flex-direction: column;
    .total-amt{margin-left: 10px;font-size: 14px;}
    .top-btns{
      padding-bottom: 10px;
      margin-left: 0px;
      width: 27%;
      display: flex;
      .el-input{
        margin-left: 5px;
      }
    }
    .bottom-table{ flex: 1;}
    /deep/ .el-table .cell{
      padding: 0px 5px !important;
    }
    /deep/ .el-table .warning-row {
      background-color: rgb(255, 43, 43) !important;
    }
    /deep/ .el-table--border .el-table__cell:first-child .cell{
      padding: 0px 5px !important
    }
    /deep/ .planError {border-color: #ff5c00 !important;}
  }
</style>
<style lang="scss">
  .mini-table .plan-list .el-table .el-table-column--selection .cell {padding: 0px 0px !important;}
  .mini-table .plan-list .el-table .cell {padding: 0px 3px;}
  .mini-table .plan-list .el-table input {padding: 0px 3px; height: 28px;}
  .mini-table .plan-list .el-table .el-input__suffix {right: -4px;}
  .mini-table .plan-list .el-table .el-select .el-input .el-select__caret {font-size: 12px;}
  .mini-table .plan-list .el-table .cell .el-date-editor .el-input__prefix { display: none; }
  .plan-container input.el-input__inner {font-size: 12px;}
  .plan-container .money input.el-input__inner {text-align: right;padding-right: 5px;}
</style>
