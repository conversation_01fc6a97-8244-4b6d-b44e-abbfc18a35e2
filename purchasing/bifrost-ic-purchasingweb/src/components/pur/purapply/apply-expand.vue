<template>
  <div id="apply-expand">
    <supplier-detail-list ref="supplierDetail" v-show="purType!=='公开招标'" style="height: 50%"></supplier-detail-list>
    <base-attachment :class="purType==='公开招标'?'base-attachment bb':'base-attachment aa'" ref="baseAttachment"
                     :style="{height:purType==='公开招标'?'100%':'50%'}"></base-attachment>
  </div>
</template>

<script>
export default {
  name: '采购申请单',
  data() {
    return {
      purType: '',
      attList: [], // 第一次加载的附件数据
      isError: false // 是否发生过错误
    }
  },
  methods: {
    initAssembly(meta, mode, formFormat) {
      this.$refs.baseAttachment.$children[0].btAddText = '上传附件'
      this.$refs.baseAttachment.$children[0].attTypeTableName = 'ELE_CG_ATT_TYPE'
      this.$refs.baseAttachment.$children[0].initMeta(meta)
      const col = meta.colItems.filter(item => item.labelOrigin === '采购单类型')[0]
      this.$nextTick(() => {
        this.purType = col.defaultValue
        if (meta.extData && this.purType !== '公开招标' && this.purType !== '公开招标-报主任会') {
          this.$refs.supplierDetail.showPrice = this.purType === '1万-5万采购' ||
            this.purType === '5万-10万采购' ||
            this.purType === '1万以下服务类或货物类采购' ||
            this.purType === '脱贫地区农副产品采购' ||
            this.purType === '电子商场直购、竞价采购' ||
            this.purType === '非公开招标采购'
        }
      }
      )
      this.$refs.supplierDetail.init(meta, mode, formFormat)
      // 附件区块按钮栏顶部隐藏默认的padding
      this.$nextTick(() => {
        this.$refs.baseAttachment.setButtonNormalNoPaddingTop(true)
      })
      // 初始化数据
      this.attList = []
      this.isError = false
    },

    getExtData() {
      const extData = {}
      if (this.purType !== '公开招标' && this.purType !== '公开招标-报采购会') {
        extData.supplierInfo = this.$refs.supplierDetail.supplierData
      }
      return extData
    },
    showError(result) {
      this.isError = true
      return 0
    },
    fillAtt(dataVo) {
      this.$fillAtt(this, dataVo)
    }
  }
}
</script>

<style lang="scss">
  #apply-expand {
    padding: 0px 0px 0px 10px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .bottom-table {
      height: calc(100% - 50px);
    }

    .aa {
      .column-bottom {
        height: calc(100% - 13px) !important;
      }
    }

    .bb {
      .column-bottom {
        height: calc(100%) !important;
      }
    }
  }
</style>

