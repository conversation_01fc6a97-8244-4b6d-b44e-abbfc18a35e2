<template>
  <div class="plan-list">
    <div class="top-btns">
      <el-button size="mini" @click="addItemBtn" icon="el-icon-circle-plus-outline">添加品目</el-button>
      <el-button size="mini" @click="deleteItemBtn" :disabled='checkedDirectItemRow.length === 0'
                 plain
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table plan-container">
      <el-table border :data="purDirectItemDetail" @selection-change="handleSelectionDirectItemChange"
                style="width: 100%"
                show-summary
                :summary-method="getSummaries" height=activeHeight>
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" prop="purItem" label="采购品目" :width="purItemDetailWidth.purItem">
          <template #header><span style="color:#f56c6c;">*</span>采购品目</template>
          <template slot-scope='{row}'>
            <el-input v-model="row.purItem"/>
          </template>
        </el-table-column>
        <el-table-column label="单价" prop="purPrice" :width="purItemDetailWidth.purPrice" align="right">
          <template #header><span style="color:#f56c6c;">*</span>单价</template>
          <template slot-scope='{row, $index}'>
            <el-input class="money"
                      @blur="calculateAmount(row, '直购品目')"
                      v-model="row.purPrice"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @input="hideError($index)"/>
          </template>
        </el-table-column>
        <el-table-column label="数量" prop="purQuantity" align="center" :width="purItemDetailWidth.purQuantity">
          <template #header><span style="color:#f56c6c;">*</span>数量</template>
          <template slot-scope='{row}'>
            <el-input type="number" min="1" @blur="calculateAmount(row, '直购品目')"
                      v-model="row.purQuantity"
                      onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
          </template>
        </el-table-column>

        <el-table-column label="采购金额" prop="purAmount" :width="purItemDetailWidth.purAmount"
                         align="right">
          <template #header><span style="color:#f56c6c;">*</span>采购金额</template>
          <template slot-scope='{row, $index}'>
            <el-input class="money"
                      :disabled="true"
                      v-model="row.purAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @input="hideError($index)"/>
          </template>
        </el-table-column>

        <el-table-column label="备注" prop="remark" :width="purItemDetailWidth.remark"
                         align="right" v-if="isNeedRemark">
          <template #header><span style="color:#f56c6c;">*</span>备注</template>
          <template slot-scope='{row, $index}'>
            <el-input v-model="row.remark"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @input="hideError($index)"/>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="top-btns" v-show="isShowBa" style="margin-top: 20px;">
      <el-button size="mini" @click="addBiddingItem()" icon="el-icon-circle-plus-outline">添加品目</el-button>
      <el-button size="mini" @click="deleteBiddingItem"
                 :disabled='checkedBiddingRow.length === 0'
                 plain
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table plan-container" v-show="isShowBa">
      <el-table border :data="purBiddingItemDetail" @selection-change="handleSelectionBiddingChange" style="width: 100%"
                show-summary
                :summary-method="getSummariesItem" height=activeHeight>
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" prop="purItem" label="竞价品目" :width="625">
          <template #header><span style="color:#f56c6c;">*</span>竞价品目</template>
          <template slot-scope='{row}'>
            <el-input v-model="row.purItem"/>
          </template>
        </el-table-column>
        <el-table-column label="单价" prop="purPrice" :width="400" align="right">
          <template #header><span style="color:#f56c6c;">*</span>单价</template>
          <template slot-scope='{row, $index}'>
            <el-input
              class="money"
              v-model="row.purPrice"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              @blur="calculateAmount(row, '竞价品目')"
              @input="hideError($index)"/>
          </template>
        </el-table-column>

        <el-table-column label="数量" prop="purQuantity" align="center" :width="200">
          <template #header><span style="color:#f56c6c;">*</span>数量</template>
          <template slot-scope='{row}'>
            <el-input type="number" min="1" @blur="calculateAmount(row, '竞价品目')"
                      v-model="row.purQuantity"
                      onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
          </template>
        </el-table-column>

        <el-table-column label="预算金额" prop="purAmount" :width="400"
                         align="right">
          <template #header><span style="color:#f56c6c;">*</span>预算金额</template>
          <template slot-scope='{row, $index}'>
            <el-input
              class="money"
              v-model="row.purAmount"
              :disabled="true"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              @input="hideError($index)"/>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'pur-items-detail-list',
  data() {
    return {
      checkedDirectItemRow: [],
      checkedBiddingRow: [],
      purDirectItemDetail: [],
      purBiddingItemDetail: [],
      havePlan: true,
      showButton: true,
      isShowBa: true,
      activeHeight: '300',
      deptCode: '',
      beDepartment: '', // 归口管理部门
      formFormat: {},
      purDept: '', // 采购单部门
      isNeedRemark: false,
      purItemDetailWidth: { // 关联指标时采购列表宽
        purItem: 625,
        purPrice: 400,
        purQuantity: 200,
        purAmount: 400,
        remark: 0
      }
    }
  },
  mounted() {
  },
  methods: {
    showAfter(dataVo) { // 更新数据
      this.beDepartment = ''
      dataVo.colItems.forEach(colItem => {
        if (colItem.labelOrigin === '归口管理部门' &&
            this.$isNotEmpty(colItem.dataValue)) {
          this.beDepartment = colItem.dataValue.split(' ')[1]
          return
        }
      })
    },
    init(dataVo, mode, formFormat) {
      this.formFormat = formFormat
      dataVo.colItems.forEach(colItem => {
        if (colItem.labelOrigin === '采购单类型') {
          if (colItem.defaultValue === '脱贫地区农副产品采购') {
            this.isShowBa = false
            this.activeHeight = 630
            mode.repeatTabLabel('pur-items-detail-tab', '采购详情清单')
          } else if (colItem.defaultValue === '电子商场直购、竞价采购') {
            this.isShowBa = true
            this.activeHeight = 300
            mode.repeatTabLabel('pur-items-detail-tab', '直购品目与竞价品目')
          } else if (colItem.defaultValue === '1万以下服务类或货物类采购') {
            this.isShowBa = false
            this.isNeedRemark = true
            this.purItemDetailWidth = { // 关联指标时采购列表宽
              purItem: 400,
              purPrice: 300,
              purQuantity: 200,
              purAmount: 300,
              remark: 425
            }
            mode.repeatTabLabel('pur-items-detail-tab', '采购明细')
          }
          return
        }
      })
    },
    showPlanError(planIndexes) {
      if (this.$isNotEmpty(planIndexes)) {
        var $planTableTr = $('.plan-container table tr')
        $.each($planTableTr, (index, item) => {
          const planIndex = index - 1 // 目的是为了排除掉第一个tr(列头)

          if (planIndexes.indexOf(planIndex + '-contracpartyName') > -1) {
            $(item).find('.contracpartyName').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-purMode') > -1) {
            $(item).find('.purMode').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planMoney') > -1) {
            $(item).find('.planMoney').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planPaymentDate') > -1) {
            $(item).find('.planPaymentDate').find('input').addClass('planError')
          }
        })
      }
    },
    hideError(rowIndex) {
      var $rows = $('.plan-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.planError').removeClass('planError')
        }
      })
    },
    addItemBtn() {
      const data = {}
      data.purItem = ''
      data.purQuantity = ''
      data.purPrice = ''
      data.purAmount = ''
      data.itemId = ''
      data.remark = ''
      !Array.isArray(this.purDirectItemDetail) && (this.purDirectItemDetail = [])
      this.purDirectItemDetail = this.purDirectItemDetail.concat([], data)
    },
    deleteItemBtn() {
      for (var i = 0; i < this.checkedDirectItemRow.length; i++) {
        const index = this.purDirectItemDetail.indexOf(this.checkedDirectItemRow[i])
        this.purDirectItemDetail.splice(index, 1)
      }
    },
    addBiddingItem() {
      const data = {}
      data.purItem = ''
      data.purQuantity = ''
      data.purPrice = ''
      data.purAmount = ''
      data.itemId = ''
      !Array.isArray(this.purBiddingItemDetail) && (this.purBiddingItemDetail = [])
      this.purBiddingItemDetail = this.purBiddingItemDetail.concat([], data)
    },
    deleteBiddingItem() {
      for (var i = 0; i < this.checkedBiddingRow.length; i++) {
        var index = this.purBiddingItemDetail.indexOf(this.checkedBiddingRow[i])
        this.purBiddingItemDetail.splice(index, 1)
      }
    },
    handleSelectionDirectItemChange(rows) {
      this.checkedDirectItemRow = rows
    },

    handleSelectionBiddingChange(rows) {
      this.checkedBiddingRow = rows
    },
    // reload(data) {
    //   if (!this.isChange) {
    //     this.purDirectItemDetail = this.purDirectItemDetail.concat([], data)
    //     this.purBiddingItemDetail = this.purBiddingItemDetail.concat([], data)
    //   } else {
    //     this.checkedItemRow[0] = Object.assign(this.checkedItemRow[0], data)
    //     this.checkedBiddingRow[0] = Object.assign(this.checkedBiddingRow[0], data)
    //   }
    //   this.isChange = false
    // },
    calculateAmount(row, directOrBidding) {
      if (directOrBidding === '直购品目') {
        row.purPrice = this.$fixMoney(row.purPrice)
        row.purAmount = row.purQuantity * row.purPrice
        row.purPrice = this.$formatMoney(row.purPrice)
        row.purAmount = this.$formatMoney(row.purAmount)
        let purAmount = 0
        this.purDirectItemDetail.forEach(directItem => {
          purAmount += parseFloat(this.$unFormatMoney(directItem.purAmount))
          this.formFormat.setValue('采购金额', this.$fixMoney(purAmount))
        })
      } else if (directOrBidding === '竞价品目') {
        row.purPrice = this.$fixMoney(row.purPrice)
        row.purAmount = row.purQuantity * row.purPrice
        row.purPrice = this.$formatMoney(row.purPrice)
        row.purAmount = this.$formatMoney(row.purAmount)
        let purAmount = 0
        this.purBiddingItemDetail.forEach(biddingItem => {
          purAmount += parseFloat(this.$unFormatMoney(biddingItem.purAmount))
          this.formFormat.setValue('预算金额', this.$fixMoney(purAmount))
        })
      }
    },
    getSummaries(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(this.unFmtMoney(item[column.property])))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                prev = Number(_this.$fixMoney(prev))
              }
              if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                curr = Number(_this.$fixMoney(curr))
              }
              if (column.label !== '数量') {
                return _this.$formatMoney(Number(prev) + Number(curr))
              } else {
                return Number(prev) + Number(curr)
              }
            } else {
              return prev
            }
          }, 0)
          sums[index] += ''
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    getSummariesItem(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(this.unFmtMoney(item[column.property])))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                prev = Number(_this.$fixMoney(prev))
              }
              if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                curr = Number(_this.$fixMoney(curr))
              }
              if (column.label !== '数量') {
                return _this.$formatMoney(Number(prev) + Number(curr))
              } else {
                return Number(prev) + Number(curr)
              }
            } else {
              return prev
            }
          }, 0)
          sums[index] += ''
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    unFmtMoney(money) {
      if (this.$isNotEmpty(money)) {
        return money.toString().replace(/￥|$|,/g, '')
      }
      return undefined
    }
  }
}
</script>

<style scoped lang="scss">
  .plan-list {
    padding: 3px 0px 3px 0px;
    width: 100%;
    height: 100% !important;
    display: flex;
    flex-direction: column;

    .total-amt {
      margin-left: 10px;
      font-size: 14px;
    }

    .top-btns {
      padding-bottom: 10px;
      margin-left: 0px;
      width: 27%;
      display: flex;

      .el-input {
        margin-left: 5px;
      }
    }

    .bottom-table {
      flex: 1;
    }

    /deep/ .el-table .cell {
      padding: 0px 5px !important;
    }

    /deep/ .el-table .warning-row {
      background-color: rgb(255, 43, 43) !important;
    }

    /deep/ .el-table--border .el-table__cell:first-child .cell {
      padding: 0px 5px !important
    }

    /deep/ .planError {
      border-color: #ff5c00 !important;
    }
  }
</style>
<style lang="scss">
  .mini-table .plan-list .el-table .el-table-column--selection .cell {
    padding: 0px 0px !important;
  }

  .mini-table .plan-list .el-table .cell {
    padding: 0px 3px;
  }

  .mini-table .plan-list .el-table input {
    padding: 0px 3px;
    height: 28px;
  }

  .mini-table .plan-list .el-table .el-input__suffix {
    right: -4px;
  }

  .mini-table .plan-list .el-table .el-select .el-input .el-select__caret {
    font-size: 12px;
  }

  .mini-table .plan-list .el-table .cell .el-date-editor .el-input__prefix {
    display: none;
  }

  .plan-container input.el-input__inner {
    font-size: 12px;
  }

  .plan-container .money input.el-input__inner {
    text-align: right;
    padding-right: 5px;
  }
</style>
