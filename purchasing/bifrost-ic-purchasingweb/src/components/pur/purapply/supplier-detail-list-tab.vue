<template>
  <div class="common-page multipleTabsSupplier" id="supplier-detail-list-tab">
    <supplier-detail-list ref="supplierDetail"></supplier-detail-list>
  </div>
</template>

<script>
export default {
  name: 'supplier-detail-list-tab',
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      purType: ''
    }
  },
  methods: {
    init(dlg, params) {
      this.$refs.supplierDetail.isAudit = true
      this.initAssembly(params.dataVo, {}, {})
    },
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.initAssembly(dataVo, {}, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      if (this.purType !== '公开招标') {
        dataVo.extData.supplierInfo = this.$refs.supplierDetail.supplierData
      }
      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
    },
    initAssembly(meta, mode, formFormat) {
      var col = meta.colItems.filter(item => item.labelOrigin === '采购单类型')[0]
      this.$nextTick(() => {
        this.purType = col.defaultValue
        if (meta.extData) {
          this.$refs.supplierDetail.showPrice = this.purType === '1万-5万采购' ||
            this.purType === '5万-10万采购' ||
            this.purType === '1万以下服务类或货物类采购' ||
            this.purType === '脱贫地区农副产品采购' ||
            this.purType === '电子商场直购、竞价采购' ||
            this.purType === '非公开招标采购'
          this.$refs.supplierDetail.supplierData = meta.extData.supplierInfo || []
        }
      }
      )
      this.$refs.supplierDetail.init(meta, mode, formFormat)
    }
  }
}
</script>
<style>
.multipleTabsSupplier .supplier-list {
  height: 100%;
  padding-left: 0px;
}

.multipleTabsSupplier .supplier-list .supplier-container {
  height: calc(100% - 48px);
  padding-left: 0px;
}
#supplier-detail-list-tab .el-tab-pane {
  height: 100%;
}
</style>
