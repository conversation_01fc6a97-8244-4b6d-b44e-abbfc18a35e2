<template>
  <div id="gooddialog">
    <el-dialog
      append-to-body
      :title="'货物类采购'"
      :visible.sync="isdialog"
      width="50%"
      :close-on-click-modal='false'
      @close="handleClose">
      <el-form
        ref="goodForm"
        :model="goodForm"
        label-width="200px"
        :disabled="gooddetails"
        :rules="rules">
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="预算项目" prop="baName">
                <el-input v-model="goodForm.baName" readonly>
                  <el-button slot="append" icon="el-icon-search" @click="selectBa"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="是否政府采购" prop="isGovPur">
                <el-input v-model="goodForm.isGovPur" readonly></el-input>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="采购品目" prop="purItem">
                <el-input v-model="goodForm.purItem" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="采购名称" prop="purArticlesName">
                <el-input v-model="goodForm.purArticlesName"  placeholder="请输入采购名称">
                </el-input>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="型号规格" prop="model">
                <el-input v-model="goodForm.model"  placeholder="请输入型号规格">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="计量单位" prop="unit">
                <el-input v-model="goodForm.unit" >
                </el-input>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="数量" prop="purQuantity">
                <el-input v-model="goodForm.purQuantity" type="number" >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="预算单价" prop="purPrice">
                <InputMoney v-model="goodForm.purPrice" />
              </el-form-item>
            </el-col>
          </el-form-item>

        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="预算总价" prop="totalPrice">
                <InputMoney v-model="goodForm.totalPrice" />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="采购金额" prop="applyAmount">
                <InputMoney v-model="goodForm.applyAmount" />
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
        <el-form-item>
          <el-col :span="11">
            <el-form-item label="采购方式" prop="purMode">
              <el-select v-model="goodForm.purMode" >
                <el-option
                  v-for="item in goodTypeList"
                  :key="item.eleName"
                  :label="item.eleName"
                  :value="item.eleName">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-row>

        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="22">
              <el-form-item label="备注">
                <el-input type="textarea" v-model="goodForm.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="handleClose('goodForm')" v-if="!gooddetails"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('goodForm')"
          v-if="!gooddetails">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'gooddialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.gooddetails = bool
    }
  },
  mounted() {
    this.selectPerformStageList() // 查询履行阶段基础数据
  },
  data() {
    return {
      isdialog: this.dialog,
      gooddetails: this.isDetails,
      goodForm: {
        baName: '',
        isGovPur: '',
        purItem: ''

      },
      goodTypeList: [],
      contractList: [],
      rules: {
        purArticlesName: [
          { required: true, message: '请输入用品名称', trigger: 'blur' }
        ],
        unit: [{ required: true, message: '请输入计量单位', trigger: 'blur' }],
        purQuantity: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        purPrice: [
          { required: true, message: '请输入单价', trigger: 'blur' }
        ],
        totalPrice: [
          { required: true, message: '请输入预算总价', trigger: 'blur' }
        ],
        applyAmount: [
          { required: true, message: '请输入申请金额', trigger: 'blur' }
        ],
        purMode: [
          { required: true, message: '请选择采购方式', trigger: 'blur' }
        ],
        model: [
          { required: true, message: '请输入型号规格', trigger: 'blur' }
        ],
        baName: [
          { required: true, message: '请选择预算项目', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    selectBa() {
      this.$refDataBa(
        { '关闭部门控制': '', '关闭可用金额控制': '' },
        selectedData => {
          this.$nextTick(() => {
            this.goodForm.baId = selectedData[0].ID
            this.goodForm.baName = selectedData[0].预算项目
            this.goodForm.isGovPur = selectedData[0].是否政府采购
            this.goodForm.purItem = selectedData[0].采购品目
          })
        })
    },
    selectPerformStageList() {
      this.$callApi('selectPurType', {}, result => {
        if (result.success) {
          this.goodTypeList = result.data
        }
        return true
      })
    },
    contracChange(data) {
      this.goodForm.contracpartyId = data.id
      this.goodForm.contracpartyName = data.contracpartyName
    },
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.handleClose()
          this.$parent.reload(this.goodForm)
          return true
        } else {
          return false
        }
      })
    },
    resetForm() {
      if (this.$refs['goodForm']) { // 清空form数据
        this.$refs['goodForm'].resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  /deep/ .el-dialog__body {
    margin-left: -245px;
  }

  /deep/ .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }
</style>
<style lang="scss">
</style>
