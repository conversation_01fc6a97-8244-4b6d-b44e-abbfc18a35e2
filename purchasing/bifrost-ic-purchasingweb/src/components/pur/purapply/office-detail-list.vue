<template>
  <div slass="plan-list">
    <div class="top-btns">
      <el-button size="mini" @click="addBtn" :disabled='!showButton' icon="el-icon-circle-plus-outline">添加用品
      </el-button>
      <el-button size="mini" @click="updateBtn" :disabled='checkedRow.length !== 1 || !showButton'
                 icon="el-icon-edit">修改用品
      </el-button>
      <el-button size="mini" @click="deleteBtn" :disabled='checkedRow.length === 0 || !showButton'
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table">
      <el-table border :data="officeData" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" align="center" width="30"></el-table-column>
        <el-table-column align="left" prop="purArticlesName" label="用品名称"></el-table-column>
        <el-table-column align="center" prop="unit" label="计量单位"></el-table-column>
        <el-table-column align="right" prop="purQuantity" label="数量"></el-table-column>
        <el-table-column align="right" prop="purPrice" label="预算单价" ></el-table-column>
        <el-table-column align="right" prop="totalPrice" label="预算总价"></el-table-column>
        <el-table-column align="right" prop="applyAmount" label="申请金额"></el-table-column>
        <el-table-column align="left" prop="purMode" label="采购方式"></el-table-column>
        <el-table-column align="left" prop="remark" label="备注"></el-table-column>
      </el-table>
    </div>
    <officedialog ref="officeDialog" :dialog.sync="isDialog" @reload='reload'></officedialog>
  </div>
</template>

<script>
export default {
  name: 'office-detail-list',
  data() {
    return {
      checkedRow: [],
      officeData: [],
      isDialog: false,
      isChange: false,
      showButton: true
    }
  },
  methods: {
    addBtn() {
      this.$refs.officeDialog.officeForm = {}
      this.isDialog = true
    },
    updateBtn() {
      this.isDialog = true
      this.isChange = true
      const cloneData = JSON.parse(JSON.stringify(this.checkedRow[0]))
      this.$refs.officeDialog.officeForm = cloneData
    },
    deleteBtn() {
      for (var i = 0; i < this.checkedRow.length; i++) {
        var index = this.officeData.indexOf(this.checkedRow[i])
        this.officeData.splice(index, 1)
      }
    },
    handleSelectionChange(rows) { this.checkedRow = rows },
    reload(data) {
      if (!this.isChange) {
        if (this.$isEmpty(this.officeData)) {
          this.officeData = []
        }
        this.officeData = this.officeData.concat([], data)
      } else {
        this.checkedRow[0] = Object.assign(this.checkedRow[0], data)
      }
      this.isChange = false
    },
    getOfficeData() {
      return this.officeData
    }
  }
}
</script>

<style scoped>
  .bottom-table{ flex: 1;
  margin-top: 10px;
  }
</style>
