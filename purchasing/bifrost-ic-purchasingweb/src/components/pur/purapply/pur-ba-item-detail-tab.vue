<template>
  <div class="common-page multipleTabsSupplier">
    <pur-ba-item-detail-list ref="purBaItemDetail"></pur-ba-item-detail-list>
  </div>
</template>

<script>
export default {
  name: 'pur-ba-item-detail-tab',
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      purType: ''
    }
  },
  methods: {
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.initAssembly(dataVo, this.baseListFormObj, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      var baDetail = this.$refs.purBaItemDetail.baDetail
      var purItemDetail = this.$refs.purBaItemDetail.purItemDetail
      purItemDetail.forEach(purItem => { // 填充关联指标id
        if (this.$isNotEmpty(purItem.corrBaStr)) {
          purItem.baId = baDetail[purItem.corrBaStr - 1].baId
        }
      })
      this.amountFormat(baDetail)
      this.amountFormat(purItemDetail)
      dataVo.extData.baDetail = baDetail
      dataVo.extData.purItemDetail = purItemDetail

      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
      this.$refs.purBaItemDetail.showAfter(dataVo)
    },
    initAssembly(meta, mode, formFormat) {
      this.$nextTick(() => {
        if (meta.extData) {
          const departmentColItem = meta.colItems.find(colItem => colItem.labelOrigin === '部门')
          this.$refs.purBaItemDetail.purDept = departmentColItem.dataValue
          this.$refs.purBaItemDetail.baDetail = meta.extData.baDetail
          this.$refs.purBaItemDetail.purItemDetail = meta.extData.purItemDetail
        }
      })
      this.$refs.purBaItemDetail.init(meta, mode, formFormat)
    },
    amountFormat(amounts) {
      amounts && amounts.map(item => {
        if (item.itemAmount) {
          item.itemAmount = this.$fixMoney(item.itemAmount)
        }
        if (item.itemUsableAmount) {
          item.itemUsableAmount = this.$fixMoney(item.itemUsableAmount)
        }
        if (item.baAmount) {
          item.baAmount = this.$fixMoney(item.baAmount)
        }
        if (item.useAmount) {
          item.useAmount = this.$fixMoney(item.useAmount)
        }
        if (item.usableAmount) {
          item.usableAmount = this.$fixMoney(item.usableAmount)
        }
        if (item.applyAmount) {
          item.applyAmount = this.$fixMoney(item.applyAmount)
        }
        if (item.purPrice) {
          item.purPrice = this.$fixMoney(item.purPrice)
        }
        if (item.thoseYearsAmount) {
          item.thoseYearsAmount = this.$fixMoney(item.thoseYearsAmount)
        }
        if (item.comingYearsAmount) {
          item.comingYearsAmount = this.$fixMoney(item.comingYearsAmount)
        }
      })
    }
  }
}
</script>
<style>
.multipleTabsSupplier .supplier-list {
  height: 100%;
  padding-left: 0px;
}

.multipleTabsSupplier .supplier-list .supplier-container {
  height: calc(100% - 48px);
  padding-left: 0px;
}
</style>
