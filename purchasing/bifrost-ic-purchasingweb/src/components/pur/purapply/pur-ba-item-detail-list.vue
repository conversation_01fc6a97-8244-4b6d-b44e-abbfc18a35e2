<template>
  <div class="plan-list">
    <div class="top-btns" v-show="isShowBa">
      <el-button size="small" @click="addBaBtn" icon="el-icon-circle-plus-outline">添加指标</el-button>
      <el-button size="small" @click="deleteBaBtn" :disabled='checkedBaRow.length === 0 || !havePlan || !showButton'
                 plain
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table plan-container" v-show="isShowBa">
      <el-table border :data="baDetail" @selection-change="handleSelectionChange" style="width: 100%" show-summary
                :summary-method="getSummaries">
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" label="指标编码" width="170">
          <template #header><span style="color:#f56c6c;">*</span>指标编码</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      v-model="row.baCode"
                      readonly
                      placeholder="请选择指标"
                      @click.native="chooseFuncBtn($index)"/>
          </template>
        </el-table-column>

        <el-table-column align="center" label="指标名称" width="228">
          <template #header><span style="color:#f56c6c;">*</span>指标名称</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.baName"/>
          </template>
        </el-table-column>

        <el-table-column align="left" label="部门经济分类科目" width="250">
          <template #header><span style="color:#f56c6c;">*</span>部门经济分类科目</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.deptEcoSubject"/>
          </template>
        </el-table-column>

        <el-table-column label="项目金额(元)" width="170" prop="itemAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>项目金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.itemAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.itemAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目可用金额(元)" width="170" prop="itemUsableAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>项目可用金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.itemUsableAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.itemUsableAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="指标金额(元)" width="170" prop="baAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>指标金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      disabled
                      v-model="row.baAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.baAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已用金额(元)" width="170" prop="useAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>已用金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      disabled
                      v-model="row.useAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.useAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用金额(元)" width="170" prop="usableAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>可用金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      disabled
                      v-model="row.usableAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.usableAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="申请金额(元)" prop="applyAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>申请金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.applyAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.applyAmount }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="top-btns" :style="{
      marginTop: isShowBa ? '20px' : '0px'
    }">
      <el-button size="small" @click="addItemBtn()" icon="el-icon-circle-plus-outline">添加品目</el-button>
      <el-button size="small" @click="deleteItemBtn" :disabled='checkedItemRow.length === 0 || !havePlan || !showButton'
                 plain
                 icon="el-icon-delete">删除
      </el-button>
    </div>

    <div class="bottom-table plan-container">
      <el-table border :data="purItemDetail" @selection-change="handleSelectionItemChange" style="width: 100%"
                show-summary
                :summary-method="getSummariesItem" height=activeHeight>
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" prop="purCodeDetail" label="采购品目" :width="purItemDetailWidth.purCodeDetail">
          <template #header><span style="color:#f56c6c;">*</span>采购品目</template>
          <template slot-scope='{row, $index}'>
            <el-input v-model="row.purItem" readonly placeholder="请选择品目" @click.native="chooseItemBtn($index)"/>
          </template>
        </el-table-column>
        <el-table-column align="center" label="计量单位" :width="purItemDetailWidth.purMode">
          <template #header>计量单位</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.unit" class="purMode"
                       placeholder="计量单位" v-if="havePlan" @change="hideError($index)">
              <el-option
                v-for="item in unit"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{ row.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column label="数量" prop="purQuantity" align="center" :width="purItemDetailWidth.number">
          <template #header><span style="color:#f56c6c;">*</span>数量</template>
          <template slot-scope='{row}'>
            <el-input v-if="havePlan" type="number" min="1" @blur="calculateAmount(row)"
                      v-model="row.purQuantity"
                      onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
            <span v-else>{{ row.purQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column label="单价" prop="purPrice" :width="purItemDetailWidth.purPrice" align="right">
          <template #header><span style="color:#f56c6c;">*</span>单价</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.purPrice"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.purPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="是否跨年采购" :width="purItemDetailWidth.isYearPur">
          <template #header><span style="color:#f56c6c;">*</span>是否跨年采购</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.isYearPur"
                       v-if="havePlan" @change="setThoseAmount(row),hideError($index)">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="当年采购金额" prop="thoseYearsAmount" :width="purItemDetailWidth.thoseYearsAmount"
                         align="right">
          <template #header><span style="color:#f56c6c;">*</span>当年采购金额</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.thoseYearsAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row,'thoseYearsAmount')"
                      @input="hideError($index)"/>
            <span v-else>{{ row.thoseYearsAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="来年采购金额" prop="comingYearsAmount" :width="purItemDetailWidth.comingYearsAmount"
                         align="right">
          <template #header><span style="color:#f56c6c;">*</span>来年采购金额</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.comingYearsAmount"
                      :disabled="row.isYearPur === '否'"
                      oninput="value=value.replace(/[^0-9.  ]/g,'')"
                      @blur="formFormatItem(row, 'comingYearsAmount')"
                      @input="hideError($index)"/>
            <span v-else>{{ row.comingYearsAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="关联指标" v-if="isShowBa">
          <template #header>关联指标</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.corrBaStr" class="purMode" @click.native="selectCorrBa"
                       placeholder="关联指标" v-if="havePlan" @change="hideError($index)" style="width: 300px">
              <el-option
                v-for="(item, index) in corrBaStr"
                :key="index"
                :label="item"
                :value="item"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{ row.corrBaStr }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>

  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'pur-ba-item-detail-list',
  data() {
    return {
      checkedBaRow: [],
      checkedItemRow: [],
      baDetail: [],
      purItemDetail: [],
      havePlan: true,
      showButton: true,
      purMode: [], // 采购方式
      unit: [], // 计量单位
      isYearPur: false,
      isShowBa: true,
      activeHeight: '300',
      corrBaStr: '',
      deptCode: '',
      purItemDetailWidth: { // 关联指标时采购列表宽
        purCodeDetail: 350,
        purMode: 95,
        number: 95,
        purPrice: 240,
        isYearPur: 95,
        thoseYearsAmount: 240,
        comingYearsAmount: 240
      },
      beDepartment: '', // 归口管理部门
      formFormat: {},
      purDept: ''// 采购单部门
    }
  },
  mounted() {
    this.selectpurModeList()
  },
  methods: {
    showAfter(dataVo) { // 更新数据
      this.beDepartment = ''
      dataVo.colItems.forEach(colItem => {
        if (colItem.labelOrigin === '归口管理部门' &&
          this.$isNotEmpty(colItem.dataValue)) {
          this.beDepartment = colItem.dataValue.split(' ')[1]
          return
        }
        if (colItem.labelOrigin === '部门') {
          if (this.purDept !== colItem.dataValue) {
            this.baDetail = []
            this.purDept = colItem.dataValue
            return
          }
        }
      })
    },
    formFormatItem(item, thoseOrComing) {
      item.itemAmount = this.$formatMoney(item.itemAmount)
      item.itemUsableAmount = this.$formatMoney(item.itemUsableAmount)
      item.baAmount = this.$formatMoney(item.baAmount)
      item.useAmount = this.$formatMoney(item.useAmount)
      item.usableAmount = this.$formatMoney(item.usableAmount)
      item.applyAmount = this.$formatMoney(item.applyAmount)
      item.purPrice = this.$formatMoney(item.purPrice)
      if (item.isYearPur === '是') {
        if (thoseOrComing === 'thoseYearsAmount') {
          item.comingYearsAmount = this.$unFormatMoney(item.purPrice) * item.purQuantity - this.$unFormatMoney(item.thoseYearsAmount)
        } else if (thoseOrComing === 'comingYearsAmount') {
          item.thoseYearsAmount = this.$unFormatMoney(item.purPrice) * item.purQuantity - this.$unFormatMoney(item.comingYearsAmount)
        }
      } else {
        item.thoseYearsAmount = this.$unFormatMoney(item.purPrice) * item.purQuantity
      }
      item.comingYearsAmount = this.$formatMoney(item.comingYearsAmount)
      item.thoseYearsAmount = this.$formatMoney(item.thoseYearsAmount)
      // 计算采购品目金额
      let totalPurItemMoney = 0
      this.purItemDetail.forEach(purItem => {
        totalPurItemMoney += parseFloat(this.$unFormatMoney(purItem.thoseYearsAmount))
        totalPurItemMoney += parseFloat(this.$unFormatMoney(purItem.comingYearsAmount))
      })
      // 采购金额合计 跟随采购品目总额改变而改变
      this.formFormat.setValue('采购金额合计', this.$fixMoney(totalPurItemMoney))
    },
    setThoseAmount(item) {
      if (item.isYearPur === '是') {
        item.thoseYearsAmount = 0
        item.comingYearsAmount = 0
        item.thoseYearsAmount = this.$formatMoney(item.thoseYearsAmount)
        item.comingYearsAmount = this.$formatMoney(item.comingYearsAmount)
      }
      if (item.isYearPur === '否') {
        item.thoseYearsAmount = item.purQuantity * this.$unFormatMoney(item.purPrice)
        item.thoseYearsAmount = this.$formatMoney(item.thoseYearsAmount)
        item.comingYearsAmount = this.$formatMoney(0)
      }
    },
    selectpurModeList() {
      this.$callApiParams('selectPurUnit', {}, result => {
        if (result.success) {
          this.unit = result.data
        }
        return true
      })
    },
    init(dataVo, mode, formFormat) {
      this.formFormat = formFormat
      dataVo.colItems.forEach(colItem => {
        if (colItem.labelOrigin === '采购单类型') {
          if (colItem.dataValue === '采购需求-不关联指标') {
            this.isShowBa = false
            this.activeHeight = 630
            this.purItemDetailWidth = { // 不关联指标时采购列表宽
              purCodeDetail: 395,
              purMode: 140,
              number: 140,
              purPrice: 285,
              isYearPur: 140,
              thoseYearsAmount: 285,
              comingYearsAmount: ''
            }
            mode.repeatTabLabel('pur-ba-item-detail-tab', '采购品目')
          } else {
            this.isShowBa = true
            this.activeHeight = 300
            this.purItemDetailWidth = { // 关联指标时采购列表宽
              purCodeDetail: 350,
              purMode: 95,
              number: 95,
              purPrice: 240,
              isYearPur: 95,
              thoseYearsAmount: 240,
              comingYearsAmount: 240
            }
            mode.repeatTabLabel('pur-ba-item-detail-tab', '指标与采购品目')
          }
          return
        }
      })

      this.selectpurModeList()
      formFormat.addShowErrorCallbacks({
        '履行计划错误提示': errorItems => {
          if (this.$isNotEmpty(errorItems)) {
            var planIndexes = []
            var keys = Object.keys(errorItems)
            keys.forEach(key => {
              planIndexes.push(key.replace('planInfo', ''))
            })
            this.showPlanError(planIndexes)
          }
        }
      })
    },
    showPlanError(planIndexes) {
      if (this.$isNotEmpty(planIndexes)) {
        var $planTableTr = $('.plan-container table tr')
        $.each($planTableTr, (index, item) => {
          const planIndex = index - 1 // 目的是为了排除掉第一个tr(列头)

          if (planIndexes.indexOf(planIndex + '-contracpartyName') > -1) {
            $(item).find('.contracpartyName').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-purMode') > -1) {
            $(item).find('.purMode').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planMoney') > -1) {
            $(item).find('.planMoney').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planPaymentDate') > -1) {
            $(item).find('.planPaymentDate').find('input').addClass('planError')
          }
        })
      }
    },
    hideError(rowIndex) {
      var $rows = $('.plan-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.planError').removeClass('planError')
        }
      })
    },
    addBaBtn() {
      const data = {}
      data.baCode = ''
      data.baName = ''
      data.deptEcoSubject = ''
      data.itemAmount = ''
      data.itemUsableAmount = ''
      data.baAmount = ''
      data.useAmount = ''
      data.applyAmount = ''
      !Array.isArray(this.baDetail) && (this.baDetail = [])
      this.baDetail = this.baDetail.concat([], data)
    },
    deleteBaBtn() {
      var flag = true
      for (var i = 0; i < this.checkedBaRow.length; i++) {
        var index = this.baDetail.indexOf(this.checkedBaRow[i]) // 被删除的索引
        this.purItemDetail.forEach(purItem => {
          var corrBaStr = purItem.corrBaStr
          if (corrBaStr === index + 1) {
            flag = false
            return this.$message.error('选择数据中已有关联指标，不能删除')
          }
        })
      }
      if (flag) {
        // eslint-disable-next-line no-redeclare
        for (var i = 0; i < this.checkedBaRow.length; i++) {
          // eslint-disable-next-line no-redeclare
          var index = this.baDetail.indexOf(this.checkedBaRow[i])
          this.baDetail.splice(index, 1)
        }
      }
    },
    dept() {
      this.$parent.formCanvasMeta.colItems.forEach(colItem => {
        if (colItem.labelOrigin === '部门ID') {
          this.deptCode = colItem.dataValue
        }
        if (colItem.labelOrigin === '部门') {
          this.purDept = colItem.dataValue
        }
      })
    },
    addItemBtn() {
      const data = {}
      data.purItem = ''
      data.unit = ''
      data.purQuantity = ''
      data.purPrice = ''
      data.isYearPur = '否'
      data.thoseYearsAmount = ''
      data.comingYearsAmount = ''
      data.corrBaStr = ''
      if (this.baDetail && this.baDetail.length === 1) {
        data.corrBaStr = 1
      }
      data.itemId = ''
      !Array.isArray(this.purItemDetail) && (this.purItemDetail = [])
      this.purItemDetail = this.purItemDetail.concat([], data)
    },
    deleteItemBtn() {
      for (var i = 0; i < this.checkedItemRow.length; i++) {
        var index = this.purItemDetail.indexOf(this.checkedItemRow[i])
        this.purItemDetail.splice(index, 1)
      }
    },
    handleSelectionItemChange(rows) {
      this.checkedItemRow = rows
    },

    handleSelectionChange(rows) {
      this.checkedBaRow = rows
    },
    reload(data) {
      if (!this.isChange) {
        this.baDetail = this.baDetail.concat([], data)
      } else {
        this.checkedBaRow[0] = Object.assign(this.checkedBaRow[0], data)
      }
      this.isChange = false
    },
    calculateAmount(row) {
      row.purQuantity = row.purQuantity.replace(/-/g, '')
      row.purPrice = this.$fixMoney(row.purPrice)
      row.purPrice = this.$formatMoney(row.purPrice)
      if (row.isYearPur !== '是') {
        row.thoseYearsAmount = row.purQuantity * this.$unFormatMoney(row.purPrice)
        row.thoseYearsAmount = this.$formatMoney(row.thoseYearsAmount)
        row.comingYearsAmount = this.$formatMoney(0)
      }
      // else{
      //    row.comingYearsAmount = row.purQuantity * this.$unFormatMoney(row.purPrice)
      //    row.comingYearsAmount = this.$formatMoney(row.comingYearsAmount)
      //    row.thoseYearsAmount = this.$formatMoney(0)
      // }
    },
    chooseFuncBtn(index) {
      const _this = this
      this.dept()
      this.$refDataBa(
        { '部门ID': this.deptCode, '关闭可用金额控制': '', '是否自定义指标参照': '是' },
        selectedData => {
          var flag = true
          _this.baDetail.forEach(purItem => {
            if (purItem.baId === selectedData[0].ID) {
              this.$message.error('该指标已经选择')
              flag = false
            }
          })
          if (selectedData[0].可用金额 === 0) {
            return this.$message({
              message: '可用金额为0,请重新选预算项目',
              type: 'warning'
            })
          } else {
            if (flag) {
              this.$nextTick(() => {
                this.baDetail[index].baId = selectedData[0].ID
                this.baDetail[index].baCode = selectedData[0].业务编码
                this.baDetail[index].baName = selectedData[0].业务名称
                this.baDetail[index].deptEcoSubject = selectedData[0].经济分类
                this.baDetail[index].baAmount = this.$formatMoney(selectedData[0].指标总金额)
                if (this.$isNotEmpty(selectedData[0].下达金额)) {
                  this.baDetail[index].baAmount = this.$formatMoney(selectedData[0].下达金额)
                }
                this.baDetail[index].useAmount = this.$formatMoney(selectedData[0].指标已使用.toFixed(2))
                this.baDetail[index].usableAmount = this.$formatMoney(selectedData[0].可用金额.toFixed(2))
                this.baDetail[index].deptEcoSubjectId = selectedData[0].经济分类ID
                // this.baDetail[index].baId = selectedData[0].预算项目ID
              })
            }
          }
        })
    },
    chooseItemBtn(index) {
      const params = { multiple: true }
      if (this.beDepartment) {
        params.BE_DEPARTMENT_eq = this.beDepartment
      }
      this.$refDataCommon('选择采购品目', selectedData => {
        if (selectedData.list.length === 1) {
          this.purItemDetail[index].purItem = selectedData.list[0].purItemCode + selectedData.list[0].purItemName
          this.purItemDetail[index].itemId = selectedData.list[0].ID
        } else {
          for (let i = 0; i < selectedData.list.length; i++) {
            this.purItemDetail[this.purItemDetail.length - 1].purItem = selectedData.list[i].purItemCode + selectedData.list[i].purItemName
            this.purItemDetail[this.purItemDetail.length - 1].itemId = selectedData.list[i].ID
            if (!(i === selectedData.list.length - 1)) {
              this.addItemBtn()
            }
          }
        }
      }, params)
    },
    selectCorrBa() {
      var baDetailLength = this.baDetail.length
      var count = []
      for (let i = 0; i < baDetailLength; i++) {
        count.push(i + 1)
      }
      this.corrBaStr = count
    },
    getSummaries(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 3) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(this.unFmtMoney(item[column.property])))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                prev = Number(_this.$fixMoney(prev))
              }
              if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                curr = Number(_this.$fixMoney(curr))
              }
              return _this.$formatMoney(Number(prev) + Number(curr))
            } else {
              return prev
            }
          }, 0)
          sums[index] += ''
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    getSummariesItem(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(this.unFmtMoney(item[column.property])))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                prev = Number(_this.$fixMoney(prev))
              }
              if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                curr = Number(_this.$fixMoney(curr))
              }
              if (column.label !== '数量') {
                return _this.$formatMoney(Number(prev) + Number(curr))
              } else {
                return Number(prev) + Number(curr)
              }
            } else {
              return prev
            }
          }, 0)
          sums[index] += ''
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    unFmtMoney(money) {
      if (this.$isNotEmpty(money)) {
        return money.toString().replace(/￥|$|,/g, '')
      }
      return undefined
    }
  }
}
</script>

<style scoped lang="scss">
.plan-list {
  // padding: 3px 0px 3px 0px;
  width: 100%;
  height: 100% !important;
  display: flex;
  flex-direction: column;

  .total-amt {
    margin-left: 10px;
    font-size: 14px;
  }

  .top-btns {
    padding-bottom: 10px;
    margin-left: 0px;
    width: 27%;
    display: flex;

    .el-input {
      margin-left: 5px;
    }
  }

  .bottom-table {
    flex: 1;
  }

  /deep/ .el-table .cell {
    padding: 0px 5px !important;
  }

  /deep/ .el-table .warning-row {
    background-color: rgb(255, 43, 43) !important;
  }

  /deep/ .el-table--border .el-table__cell:first-child .cell {
    padding: 0px 5px !important
  }

  /deep/ .planError {
    border-color: #ff5c00 !important;
  }
}
</style>
<style lang="scss">
.mini-table .plan-list .el-table .el-table-column--selection .cell {
  padding: 0px 0px !important;
}

.mini-table .plan-list .el-table .cell {
  padding: 0px 3px;
}

.mini-table .plan-list .el-table input {
  padding: 0px 3px;
  height: 28px;
}

.mini-table .plan-list .el-table .el-input__suffix {
  right: -4px;
}

.mini-table .plan-list .el-table .el-select .el-input .el-select__caret {
  font-size: 12px;
}

.mini-table .plan-list .el-table .cell .el-date-editor .el-input__prefix {
  display: none;
}

.plan-container input.el-input__inner {
  font-size: 12px;
}

.plan-container .money input.el-input__inner {
  text-align: right;
  padding-right: 5px;
}
</style>
