<template>
  <div slass="plan-list">
    <div class="top-btns">
      <el-button size="mini" @click="addBtn" :disabled='!showButton' icon="el-icon-circle-plus-outline">添加用品
      </el-button>
      <el-button size="mini" @click="updateBtn" :disabled='checkedRow.length !== 1 || !showButton'
                 icon="el-icon-edit">修改用品
      </el-button>
      <el-button size="mini" @click="deleteBtn" :disabled='checkedRow.length === 0 || !showButton'
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table">
      <el-table border :data="goodData" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" align="center" width="30"></el-table-column>
        <el-table-column align="left" prop="baName" label="预算项目"></el-table-column>
        <el-table-column align="center" prop="isGovPur" label="是否政府采购"></el-table-column>
        <el-table-column align="left" prop="purArticlesName" label="采购品目"></el-table-column>
        <el-table-column align="right" prop="purPrice" label="采购名称" ></el-table-column>
        <el-table-column align="right" prop="model" label="型号规格"></el-table-column>
        <el-table-column align="center" prop="unit" label="计量单位"></el-table-column>
        <el-table-column align="center" prop="purQuantity" label="数量"></el-table-column>
        <el-table-column align="left" prop="purPrice" label="预算单价"></el-table-column>
        <el-table-column align="left" prop="totalPrice" label="预算总价"></el-table-column>
        <el-table-column align="left" prop="applyAmount" label="采购金额"></el-table-column>
        <el-table-column align="left" prop="purMode" label="采购方式"></el-table-column>
        <el-table-column align="left" prop="remark" label="备注"></el-table-column>
      </el-table>
    </div>
    <gooddialog ref="goodDialog" :dialog.sync="isDialog" @reload='reload'></gooddialog>
  </div>
</template>

<script>
export default {
  name: 'good-detail-list',
  data() {
    return {
      checkedRow: [],
      goodData: [],
      isDialog: false,
      isChange: false,
      showButton: true
    }
  },
  methods: {
    addBtn() {
      this.$refs.goodDialog.goodForm = {
        baName: '',
        isGovPur: '',
        purItem: ''

      }
      this.isDialog = true
    },
    updateBtn() {
      this.isDialog = true
      this.isChange = true
      const cloneData = JSON.parse(JSON.stringify(this.checkedRow[0]))
      this.$refs.goodDialog.goodForm = cloneData
    },
    deleteBtn() {
      for (var i = 0; i < this.checkedRow.length; i++) {
        var index = this.goodData.indexOf(this.checkedRow[i])
        this.goodData.splice(index, 1)
      }
    },
    handleSelectionChange(rows) { this.checkedRow = rows },
    reload(data) {
      if (!this.isChange) {
        if (this.$isEmpty(this.goodData)) {
          this.goodData = []
        }
        this.goodData = this.goodData.concat([], data)
      } else {
        this.checkedRow[0] = Object.assign(this.checkedRow[0], data)
      }
      this.isChange = false
    },
    getGoodData() {
      return this.goodData
    }
  }
}
</script>

<style scoped>
  .bottom-table{ flex: 1;
    margin-top: 10px;
  }
</style>
