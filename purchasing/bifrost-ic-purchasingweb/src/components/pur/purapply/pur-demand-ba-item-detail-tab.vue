<template>
  <div class="common-page multipleTabsSupplier">
    <pur-ba-demand-item-detail-list ref="supplierDetail"></pur-ba-demand-item-detail-list>
  </div>
</template>

<script>
export default {
  name: 'pur-demand-ba-item-detail-tab',
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      purType: ''
    }
  },
  methods: {
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.initAssembly(dataVo, {}, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      var baDetail = this.$refs.supplierDetail.baDetail
      var purItemDetail = this.$refs.supplierDetail.purItemDetail
      dataVo.extData.baDetail = baDetail
      purItemDetail.forEach(purItem => { // 填充关联指标id
        if (this.$isNotEmpty(purItem.corrBaStr)) {
          purItem.baId = baDetail[purItem.corrBaStr - 1].baId
        }
      })
      var purDemandDetail = this.$refs.supplierDetail.purDemandDetail
      this.amountFormat(baDetail)
      this.amountFormat(purItemDetail)
      this.amountFormat(purDemandDetail)
      dataVo.extData.purItemDetail = purItemDetail
      dataVo.extData.purDemandDetail = purDemandDetail

      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
    },
    initAssembly(dataVo, mode, formFormat) {
      this.$nextTick(() => {
        if (dataVo.extData) {
          this.$refs.supplierDetail.baDetail = dataVo.extData.baDetail || []
          this.$refs.supplierDetail.purItemDetail = dataVo.extData.purItemDetail || []
          this.$refs.supplierDetail.purDemandDetail = dataVo.extData.purDemandDetail || []
        }
      }
      )
      this.$refs.supplierDetail.init(dataVo, mode, formFormat)
    },
    amountFormat(amounts) {
      if (this.$isNotEmpty(amounts)) {
        amounts.map(item => {
          if (item.itemAmount) {
            item.itemAmount = this.$fixMoney(item.itemAmount)
          }
          if (item.itemUsableAmount) {
            item.itemUsableAmount = this.$fixMoney(item.itemUsableAmount)
          }
          if (item.baAmount) {
            item.baAmount = this.$fixMoney(item.baAmount)
          }
          if (item.useAmount) {
            item.useAmount = this.$fixMoney(item.useAmount)
          }
          if (item.usableAmount) {
            item.usableAmount = this.$fixMoney(item.usableAmount)
          }
          if (item.applyAmount) {
            item.applyAmount = this.$fixMoney(item.applyAmount)
          }
          if (item.purPrice) {
            item.purPrice = this.$fixMoney(item.purPrice)
          }
          if (item.thoseYearsAmount) {
            item.thoseYearsAmount = this.$fixMoney(item.thoseYearsAmount)
          }
          if (item.comingYearsAmount) {
            item.comingYearsAmount = this.$fixMoney(item.comingYearsAmount)
          }
          if (item.thisPurAmount) {
            item.thisPurAmount = this.$fixMoney(item.thisPurAmount)
          }
        })
      }
    }
  }
}
</script>
<style>
.multipleTabsSupplier .supplier-list {
  height: 100%;
  padding-left: 0px;
}

.multipleTabsSupplier .supplier-list .supplier-container {
  height: calc(100% - 48px);
  padding-left: 0px;
}
</style>
