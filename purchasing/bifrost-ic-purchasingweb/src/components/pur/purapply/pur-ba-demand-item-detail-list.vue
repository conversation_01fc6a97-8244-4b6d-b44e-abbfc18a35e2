<template>
  <div class="plan-list">

    <div class="top-btns">
      <el-button size="mini" @click="addDemandBtn" icon="el-icon-circle-plus-outline">添加需求单</el-button>
      <el-button size="mini" @click="deleteDemandBtn"
                 :disabled='checkedDemandRow.length === 0 || !havePlan || !showButton' plain
                 icon="el-icon-delete">删除
      </el-button>
    </div>

    <div class="bottom-table plan-container">
      <el-table ref="purDemandTable" border :data="purDemandDetail" @selection-change="handleSelectionDemandChange"
                style="width: 100%" show-summary
                :summary-method="getPurDemandSummaries" height='180'>
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" prop="purCodeDetail" label="采购编码" width="200">
          <template #header><span style="color:#f56c6c;">*</span>采购编码</template>
          <template slot-scope='{row, $index}'>
            <el-input v-model="row.purCode" readonly placeholder="请选择需求单" @click.native="chooseItemBtn($index)"/>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="purCodeDetail" label="采购需求项目名称" width="300">
          <template #header><span style="color:#f56c6c;">*</span>采购需求项目名称</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.purDemandName"/>
          </template>
        </el-table-column>
        <el-table-column label="归口管理部门" width="200">
          <template #header><span style="color:#f56c6c;">*</span>归口管理部门</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.mangeDept"/>
          </template>
        </el-table-column>
        <el-table-column label="申请部门" width="200">
          <template #header><span style="color:#f56c6c;">*</span>申请部门</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.applyDept"/>
          </template>
        </el-table-column>
        <el-table-column align="center" label="申请人" width="260">
          <template #header><span style="color:#f56c6c;">*</span>申请人</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.applicant"/>
          </template>
        </el-table-column>
        <el-table-column prop="applyAmount" label="申请金额" width="260" align="right">
          <template #header><span style="color:#f56c6c;">*</span>申请金额</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.applyAmount"
                      :disabled="true"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.applyAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="thisPurAmount" label="本次采购金额" align="right">
          <template #header><span style="color:#f56c6c;">*</span>本次采购金额</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.thisPurAmount"
                      :disabled="true"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.thisPurAmount }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="top-btns" style="margin-top: 10px;">
            <el-button size="mini" @click="deleteItemBtn" :disabled='checkedItemRow.length === 0 || !havePlan || !showButton'
                       plain
                       icon="el-icon-delete">删除
            </el-button>
    </div>

    <div class="bottom-table plan-container">
      <el-table ref="purItemTable" border :data="purItemDetail" @selection-change="handleSelectionItemChange"
                style="width: 100%" show-summary
                :summary-method="getSummariesItem" height="180">
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" prop="purCodeDetail" label="采购品目" width="350">
          <template #header><span style="color:#f56c6c;">*</span>采购品目</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.purItem"/>
          </template>
        </el-table-column>
        <el-table-column align="center" label="计量单位" width="95">
          <template #header>计量单位</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.unit" class="purMode" disabled
                       placeholder="计量单位" v-if="havePlan" @change="hideError($index)">
              <el-option
                v-for="item in unit"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{ row.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="purQuantity" label="数量" width="95" align="right">
          <template #header><span style="color:#f56c6c;">*</span>数量</template>
          <template slot-scope='{row}'>
            <el-input v-if="havePlan" type="number" min="1" @blur="calculateAmount(row)"
                      v-model="row.purQuantity" disabled
                      onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
            <span v-else>{{ row.purQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="purPrice" label="单价" width="240" align="right">
          <template #header><span style="color:#f56c6c;">*</span>单价</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      disabled
                      v-model="row.purPrice"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.purPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="是否跨年采购" width="95">
          <template #header><span style="color:#f56c6c;">*</span>是否跨年采购</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.isYearPur" disabled
                       v-if="havePlan" @change="hideError($index)">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="thoseYearsAmount" label="当年采购金额" width="240" align="right">
          <template #header><span style="color:#f56c6c;">*</span>当年采购金额</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.thoseYearsAmount"
                      disabled
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.thoseYearsAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="comingYearsAmount" label="来年采购金额" width="240" align="right">
          <template #header><span style="color:#f56c6c;">*</span>来年采购金额</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.comingYearsAmount"
                      disabled
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.comingYearsAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="关联指标">
          <template #header>关联指标</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.corrBaStr" class="purMode" :disabled="showRefBa"
                       @click.native="selectCorrBa"
                       placeholder="关联指标" v-if="havePlan" @change="hideError($index)" style="width: 300px">
              <el-option
                v-for="(item, index) in corrBaStr"
                :key="index"
                :label="item"
                :value="item"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{ row.corrBaStr }}</span>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="top-btns" v-show="isShowBa" style="margin-top: 10px;">
      <el-button size="small" @click="addBaBtn" :disabled='editBa' icon="el-icon-circle-plus-outline" v-if="!editBa">
        添加指标
      </el-button>
      <el-button size="small" @click="deleteBaBtn"
                 :disabled='checkedRow.length === 0 || !havePlan || !showButton || editBa' plain
                 v-if="!editBa"
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table plan-container" v-show="isShowBa">
      <el-table ref="baDetailTable" border :data="baDetail" @selection-change="handleSelectionBaChange"
                style="width: 100%" show-summary
                :summary-method="getSummaries" height='180'>
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" label="指标编码" width="170">
          <template #header><span style="color:#f56c6c;">*</span>指标编码</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      v-model="row.baCode"
                      readonly
                      :disabled='showRefBa'
                      placeholder="请选择指标"
                      @click.native="chooseFuncBtn($index)"/>
          </template>
        </el-table-column>

        <el-table-column align="center" label="指标名称" width="228">
          <template #header><span style="color:#f56c6c;">*</span>指标名称</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.baName"/>
          </template>
        </el-table-column>

        <el-table-column align="center" label="部门经济分类科目" width="250">
          <template #header><span style="color:#f56c6c;">*</span>部门经济分类科目</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.deptEcoSubject"/>
          </template>
        </el-table-column>

        <el-table-column label="项目金额(元)" width="170" prop="itemAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>项目金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      :disabled="editBa"
                      v-model="row.itemAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.itemAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目可用金额(元)" width="170" prop="itemUsableAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>项目可用金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      :disabled="editBa"
                      v-model="row.itemUsableAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.itemUsableAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="指标金额(元)" width="170" prop="baAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>指标金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      disabled
                      v-model="row.baAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.baAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已用金额(元)" width="170" prop="useAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>已用金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      disabled
                      v-model="row.useAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.useAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用金额(元)" width="170" prop="usableAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>可用金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      disabled
                      v-model="row.usableAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.usableAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="申请金额(元)" prop="applyAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>申请金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan"
                      class="money"
                      v-model="row.applyAmount"
                      :disabled="showRefBa"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.applyAmount }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <ref-extend-item ref="refExtendContract" :showUI="false"/>
  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'pur-ba-demand-item-detail-list',
  data() {
    return {
      checkedRow: [],
      checkedItemRow: [],
      checkedDemandRow: [],
      baDetail: [],
      purItemDetail: [],
      purDemandDetail: [],
      havePlan: true,
      showButton: true,
      purMode: [], // 采购方式
      unit: [], // 计量单位
      isYearPur: false,
      isShowBa: true,
      corrBaStr: '',
      deptCode: '',
      isCentralizedPur: '',
      demandSumPurMode: '',
      editBa: false,
      showRefBa: false,
      purType: '',
      itemIds: [],
      baIds: [],
      purDemandIds: '',
      formFormat: {},
      isRefresh: false,
      delItemIds: []
    }
  },
  mounted() {
    this.selectpurModeList()
  },
  methods: {
    formFormatItem(item) {
      item.itemAmount = this.$formatMoney(item.itemAmount)
      item.itemUsableAmount = this.$formatMoney(item.itemUsableAmount)
      item.thoseYearsAmount = item.purQuantity * this.$unFormatMoney(item.purPrice)
      item.applyAmount = this.$formatMoney(item.applyAmount)
      item.purPrice = this.$formatMoney(item.purPrice)
      item.thoseYearsAmount = this.$formatMoney(item.thoseYearsAmount)
      item.comingYearsAmount = this.$formatMoney(item.comingYearsAmount)
      // 计算采购品目金额
      let totalPurItemMoney = 0
      this.purItemDetail.forEach(purItem => {
        totalPurItemMoney += parseFloat(this.$unFormatMoney(purItem.thoseYearsAmount))
        totalPurItemMoney += parseFloat(this.$unFormatMoney(purItem.comingYearsAmount))
      })
      // 采购金额合计 跟随采购品目总额改变而改变
      this.formFormat.setValue('采购金额合计', this.$fixMoney(totalPurItemMoney))
    },
    selectpurModeList() {
      this.$callApiParams('selectPurUnit', {}, result => {
        if (result.success) {
          this.unit = result.data
        }
        return true
      })
    },
    init(meta, mode, formFormat) {
      this.formFormat = formFormat
      this.isRelBaPurDemand(meta)
      this.selectpurModeList()
      formFormat.addShowErrorCallbacks({
        '履行计划错误提示': errorItems => {
          if (this.$isNotEmpty(errorItems)) {
            var planIndexes = []
            var keys = Object.keys(errorItems)
            keys.forEach(key => {
              planIndexes.push(key.replace('planInfo', ''))
            })
            this.showPlanError(planIndexes)
          }
        }
      })
    },
    showPlanError(planIndexes) {
      if (this.$isNotEmpty(planIndexes)) {
        var $planTableTr = $('.plan-container table tr')
        $.each($planTableTr, (index, item) => {
          const planIndex = index - 1 // 目的是为了排除掉第一个tr(列头)

          if (planIndexes.indexOf(planIndex + '-contracpartyName') > -1) {
            $(item).find('.contracpartyName').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-purMode') > -1) {
            $(item).find('.purMode').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planMoney') > -1) {
            $(item).find('.planMoney').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planPaymentDate') > -1) {
            $(item).find('.planPaymentDate').find('input').addClass('planError')
          }
        })
      }
    },
    hideError(rowIndex) {
      var $rows = $('.plan-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.planError').removeClass('planError')
        }
      })
    },
    addBaBtn() {
      const data = {}
      data.baCode = ''
      data.baName = ''
      data.deptEcoSubject = ''
      data.itemAmount = ''
      data.itemUsableAmount = ''
      data.baAmount = ''
      data.useAmount = ''
      data.applyAmount = ''
      !Array.isArray(this.baDetail) && (this.baDetail = [])
      this.baDetail = this.baDetail.concat([], data)
    },
    deleteBaBtn() {
      for (var i = 0; i < this.checkedRow.length; i++) {
        var index = this.baDetail.indexOf(this.checkedRow[i])
        this.baDetail.splice(index, 1)
      }
    },
    queryCfromData() {
      this.$parent.formCanvasMeta.colItems.forEach(colItems => {
        if (colItems.labelOrigin === '部门') {
          this.deptCode = colItems.dataValue.substring(0, colItems.dataValue.lastIndexOf(' '))
        }
        if (colItems.labelOrigin === '是否政府集中采购') {
          this.isCentralizedPur = colItems.dataValue
        }
        if (colItems.labelOrigin === '采购方式') {
          this.demandSumPurMode = colItems.dataValue
        }
      })
    },
    addItemBtn() {
      const data = {}
      data.purItem = ''
      data.unit = ''
      data.purQuantity = ''
      data.purPrice = ''
      data.isYearPur = ''
      data.thoseYearsAmount = ''
      data.comingYearsAmount = ''
      data.corrBaStr = ''
      data.itemId = ''
      data.purDemandId = ''
      this.purItemDetail = this.purItemDetail.concat([], data)
    },
    deleteItemBtn() {
      // 存储相同corrbaStr的数量,和对应
      const itemMap = {}
      this.checkedItemRow.forEach(item => {
        if (this.$isNotEmpty(item)) {
          const { corrBaStr } = item
          itemMap[corrBaStr] = itemMap[corrBaStr] || { itemCount: 0, items: [] }
          itemMap[corrBaStr].items.push(item)
          this.delItemIds.push(item.bizid)
        }
      })
      // 要两个东西 corrBaStr和corrBaStr对应的item   要删除的都在checkedItemRow里面  里面有每个item
      for (const corrBaStr in itemMap) {
        const { items } = itemMap[corrBaStr]
        // 删除逻辑要兼容不关联指标
        const purBaIndex = parseInt(corrBaStr || 0) - 1
        // 修改金额和删除品目
        items.forEach(item => {
          const purItemIndex = this.purItemDetail.indexOf(item)
          if (purItemIndex !== -1) {
            this.changeAmountInfo(purItemIndex, purBaIndex)
            this.purItemDetail.splice(purItemIndex, 1)
          }
        })
      }
      if (!this.isRefresh) {
        this.purDemandDetail = this.purDemandDetail.filter(pur => pur.purCode !== '' && parseFloat(pur.thisPurAmount) !== 0)
      }
      this.baDetail = this.baDetail.filter(ba => parseFloat(ba.applyAmount) !== 0)
      // 根据品目对应指标索引，修改品目关联指标的值
      this.updatePurItemCorrBaStr()
    },
    addDemandBtn() {
      const data = {}
      data.purCode = ''
      data.purDemandName = ''
      data.mangeDept = ''
      data.applyDept = ''
      data.applicant = ''
      data.applyAmount = ''
      data.thisPurAmount = ''
      data.comingYearsAmount = ''
      data.purId = ''
      this.purDemandDetail = this.purDemandDetail.concat([], data)
    },
    deleteDemandBtn() {
      this.deleteItemBtn()
    },
    refreshData(index, purIds, thoseYearAmounts, list) {
      let newPur = null
      let deleteIndex = index

      const purDemand = this.purDemandDetail[index]
      // 如果新增单据和现在单据的purCode不相同就删除现在单据的品目和关联指标(不是新增也不是替换相同的需求单)（更新需求单时应删除对应品目和指标）
      if (this.$isNotEmpty(purDemand) && this.$isNotEmpty(purDemand.purId) && purDemand.purId !== list.id) {
        this.isRefresh = true
        this.checkedItemRow = this.purItemDetail.filter(item =>
          item.purId === purDemand.purId || item.purDemandId === purDemand.purId)
        this.deleteItemBtn()
      }

      const purIndex = purIds.indexOf(list.id)
      if (purIndex !== -1) { // 合并
        thoseYearAmounts += parseFloat(this.purDemandDetail[purIndex].thisPurAmount)
        this.purDemandDetail[purIndex].thisPurAmount = this.$formatMoney(thoseYearAmounts)
        newPur = this.purDemandDetail[purIndex]
        deleteIndex = purIndex
      } else { // 新增 (更新）
        this.purDemandDetail[index].purCode = list.业务编码
        this.purDemandDetail[index].purDemandName = list.业务名称
        this.purDemandDetail[index].mangeDept = list.归口管理部门
        this.purDemandDetail[index].applyDept = list.申请部门
        this.purDemandDetail[index].applicant = list.申请人
        this.purDemandDetail[index].purId = list.id
        this.purDemandDetail[index].applyAmount = this.$formatMoney(list.申请金额.toFixed(2))
        this.purDemandDetail[index].thisPurAmount = this.$formatMoney(thoseYearAmounts)
        newPur = this.purDemandDetail[index]
      }
      this.purDemandDetail.push(newPur)
      this.purDemandDetail.splice(deleteIndex, 1)

      this.purDemandDetail = this.purDemandDetail.filter(pur => pur.purCode !== '' && parseFloat(pur.thisPurAmount) !== 0)
      this.isRefresh = false
    },
    handleSelectionDemandChange(rows) {
      this.checkedDemandRow = rows
      this.$refs.purItemTable.clearSelection()
      this.$refs.baDetailTable.clearSelection()
      const demandIds = []
      const demandItemIds = []
      const selectedItem = []
      this.checkedDemandRow.forEach(it => demandIds.push(it.purId))
      this.$callApiParams('selectItemIds', { 'purIds': demandIds.join(',') }, result => {
        if (result.success) {
          result.data.forEach(demandItemId => demandItemIds.push(demandItemId))
        }
        this.purItemDetail.forEach(purItem => {
          if (demandIds.includes(purItem.purId) || demandItemIds.includes(purItem.parentId)) {
            this.$refs.purItemTable.toggleRowSelection(purItem, true)
            selectedItem.push(purItem)
          }
        })
        this.handleSelectionItemChange(selectedItem)
        return true
      })
    },
    handleSelectionItemChange(rows) {
      this.checkedItemRow = rows
      this.$refs.baDetailTable.clearSelection()
      this.checkedItemRow.forEach(purItem => {
        const baIndex = purItem.corrBaStr - 1
        const relBa = this.baDetail[baIndex]
        this.$refs.baDetailTable.toggleRowSelection(relBa, true)
      })
    },
    handleSelectionBaChange(rows) {
      this.checkedRow = rows
    },
    reload(data) {
      if (!this.isChange) {
        this.baDetail = this.baDetail.concat([], data)
      } else {
        this.checkedRow[0] = Object.assign(this.checkedRow[0], data)
      }
      this.isChange = false
    },
    calculateAmount(row) {
      row.purQuantity = row.purQuantity.replace(/-/g, '')
      row.purPrice = this.$fixMoney(row.purPrice)
      row.thoseYearsAmount = row.purQuantity * row.purPrice
      row.thoseYearsAmount = this.$formatMoney(row.thoseYearsAmount)
    },
    chooseFuncBtn(index) {
      this.queryCfromData()
      this.$refDataBa(
        { '部门ID': this.deptCode, '关闭可用金额控制': '', '是否自定义指标参照': '是' },
        selectedData => {
          if (selectedData[0].可用金额 === 0) {
            return this.$message({
              message: '可用金额为0,请重新选预算项目',
              type: 'warning'
            })
          } else {
            this.$nextTick(() => {
              this.baDetail[index].baId = selectedData[0].ID
              this.baDetail[index].baCode = selectedData[0].业务编码
              this.baDetail[index].baName = selectedData[0].业务名称
              this.baDetail[index].deptEcoSubject = selectedData[0].经济分类
              this.baDetail[index].baAmount = this.$formatMoney(selectedData[0].指标总金额)
              this.baDetail[index].useAmount = this.$formatMoney(selectedData[0].指标已使用.toFixed(2))
              this.baDetail[index].usableAmount = this.$formatMoney(selectedData[0].可用金额.toFixed(2))
              this.baDetail[index].deptEcoSubjectId = selectedData[0].经济分类ID
              this.baDetail[index].baId = selectedData[0].预算项目ID
            })
          }
        })
    },
    chooseItemBtn(index) {
      this.queryCfromData()
      let thoseYearAmounts = 0
      // 界面上现有的 需求单Id和品目Id
      const purIds = this.purDemandDetail.map(purDemand => purDemand.purId)
      const purItemIds = this.purItemDetail.map(purItem => purItem.bizid)
      // delItemIds可能存储了保存为汇总单的品目Id 和 未保存为需求单品目的Id(这个两个品目数据一致)
      const delItemIds = this.delItemIds.filter(item =>
        !(purItemIds.includes(item.bizid) || purItemIds.includes(item.parentId)))
      // 将数组转换为逗号分隔的字符串
      const purDemandIdsStr = purIds.join(',')
      const purItemIdsStr = purItemIds.join(',')
      const delItemIdsStr = delItemIds.join(',')
      var getBaDetail = (data) => {
        if (this.$isNotEmpty(data)) {
          data.forEach(newBa => this.updateBaDetail(newBa))
        }
        this.updatePurItemCorrBaStr()
      }
      var getPurItemDetail = (data) => {
        data.forEach(newItem => {
          thoseYearAmounts += parseFloat(newItem.thoseYearsAmount) // 新增品目当年采购金额之和
          this.purItemDetail.push(newItem)
        })
      }
      var params = {
        multiple: false,
        formType: '采购需求汇总单',
        getBaDetail: getBaDetail,
        getPurItemDetail: getPurItemDetail,
        isCentralizedPur: this.isCentralizedPur,
        demandSumPurMode: this.demandSumPurMode,
        purDemandIdsStr: purDemandIdsStr,
        purItemIdsStr: purItemIdsStr,
        delItemIdsStr: delItemIdsStr
      }
      this.exhandleRefDataParams(params)
      this.$refDataCommon('选择采购需求单', selectedData => {
        selectedData.list.forEach(list => {
          if (list.metaName.includes('不关联指标')) {
            this.editBa = false
            this.showRefBa = false
            this.purType = '不关联指标'
          } else {
            this.editBa = true
            this.showRefBa = true
            this.purType = '关联指标'
          }
          this.refreshData(index, purIds, thoseYearAmounts, list)
        })
      }, params)
    },
    selectCorrBa() {
      var baDetailLength = this.baDetail.length
      var count = []
      for (let i = 0; i < baDetailLength; i++) {
        count.push(i + 1)
      }
      this.corrBaStr = count
    },
    exhandleRefDataParams(params, colItem, formFormat) {
      params.getRefTableExtend = () => {
        return this.$refs.refExtendContract
      }
    },
    getPurDemandSummaries(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(this.unFmtMoney(item[column.property])))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                prev = Number(_this.$fixMoney(prev))
              }
              if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                curr = Number(_this.$fixMoney(curr))
              }
              return _this.$formatMoney(Number(prev) + Number(curr))
            } else {
              return prev
            }
          }, 0)
          sums[index] += ''
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    getSummariesItem(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(this.unFmtMoney(item[column.property])))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                prev = Number(_this.$fixMoney(prev))
              }
              if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                curr = Number(_this.$fixMoney(curr))
              }
              return _this.$formatMoney(Number(prev) + Number(curr))
            } else {
              return prev
            }
          }, 0)
          sums[index] += ''
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    getSummaries(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(this.unFmtMoney(item[column.property])))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                prev = Number(_this.$fixMoney(prev))
              }
              if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                curr = Number(_this.$fixMoney(curr))
              }
              return _this.$formatMoney(Number(prev) + Number(curr))
            } else {
              return prev
            }
          }, 0)
          sums[index] += ''
        } else {
          sums[index] = ''
        }
      })
      return sums
    },

    unFmtMoney(money) {
      if (this.$isNotEmpty(money)) {
        return money.toString().replace(/￥|$|,/g, '')
      }
      return undefined
    },
    // 汇总单中选中需求单是否为关联指标采购需求单
    isRelBaPurDemand(meta) {
      if (this.$isNotEmpty(meta.extData.purDemandDetail)) {
        var purId = meta.extData.purDemandDetail[0].purId
        this.$callApiParams('isRelBa', { purId }, result => {
          if (result.success) {
            this.showRefBa = result.data
            this.editBa = result.data
            if (this.showRefBa) {
              this.purType = '关联指标'
            }
          }
          return true
        })
      }
    },
    // 更新品目删除后对应的关联需求单和指标的金额
    changeAmountInfo(purItemIndex, baIndex) {
      const purItem = this.purItemDetail[purItemIndex]
      const thoseYearsAmount = parseFloat(purItem.thoseYearsAmount) // 使用 parseFloat 将字符串转换为浮点数
      const changeAmount = thoseYearsAmount
      // 更新 本次采购金额
      this.purDemandDetail.forEach(purDemand => {
        // 未保存时需求单的purId等于品目的purId,保存后的需求单的purId等于purItem.purDemandId
        if (purDemand.purId === purItem.purId || (purDemand.purId === purItem.purDemandId)) {
          let thisPurAmount = parseFloat(this.$unFormatMoney(purDemand.thisPurAmount)) // 使用 parseFloat 将字符串转换为浮点数
          thisPurAmount -= thoseYearsAmount
          purDemand.thisPurAmount = this.$formatMoney(thisPurAmount) // 转换回字符串并更新
        }
      })
      // 更新 申请金额(元）
      if (baIndex !== -1) {
        let baApplyAmount = this.baDetail[baIndex].applyAmount
        baApplyAmount = baApplyAmount - changeAmount
        this.baDetail[baIndex].applyAmount = this.$formatMoney(baApplyAmount)
      }
    },
    // 更新品目关联指标
    updatePurItemCorrBaStr() {
      this.purItemDetail = this.purItemDetail.map(purItem => {
        // 相同baId的指标都合并了
        const foundBa = this.baDetail
          .find(ba => purItem.baId === ba.baId)
        if (foundBa) {
          return { ...purItem, corrBaStr: this.baDetail.indexOf(foundBa) + 1 }
        }
        return purItem
      })
    },
    // 更新指标金额,合并相同指标
    updateBaDetail(newBa) {
      const foundBa = this.baDetail
        .find(ba => ba.baId === newBa.baId)
      const existBaIndex = this.baDetail.indexOf(foundBa)
      if (existBaIndex !== -1) {
        const existBa = this.baDetail[existBaIndex]
        const applyAmount = this.$formatMoney(parseFloat(newBa.applyAmount) + parseFloat(existBa.applyAmount))
        this.baDetail[existBaIndex].applyAmount = applyAmount
      } else {
        this.baDetail.push(newBa)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.plan-list {
  padding: 3px 0px 3px 0px;
  width: 100%;
  height: 100% !important;
  display: flex;
  flex-direction: column;

  .total-amt {
    margin-left: 10px;
    font-size: 14px;
  }

  .top-btns {
    padding-bottom: 10px;
    margin-left: 0px;
    width: 27%;
    display: flex;

    .el-input {
      margin-left: 5px;
    }
  }

  .bottom-table {
    flex: 1;
  }

  /deep/ .el-table .cell {
    padding: 0px 5px !important;
  }

  /deep/ .el-table .warning-row {
    background-color: rgb(255, 43, 43) !important;
  }

  /deep/ .el-table--border .el-table__cell:first-child .cell {
    padding: 0px 5px !important
  }

  /deep/ .planError {
    border-color: #ff5c00 !important;
  }
}
</style>
<style lang="scss">
.mini-table .plan-list .el-table .el-table-column--selection .cell {
  padding: 0px 0px !important;
}

.mini-table .plan-list .el-table .cell {
  padding: 0px 3px;
}

.mini-table .plan-list .el-table input {
  padding: 0px 3px;
  height: 28px;
}

.mini-table .plan-list .el-table .el-input__suffix {
  right: -4px;
}

.mini-table .plan-list .el-table .el-select .el-input .el-select__caret {
  font-size: 12px;
}

.mini-table .plan-list .el-table .cell .el-date-editor .el-input__prefix {
  display: none;
}

.plan-container input.el-input__inner {
  font-size: 12px;
}

.plan-container .money input.el-input__inner {
  text-align: right;
  padding-right: 5px;
}
</style>
