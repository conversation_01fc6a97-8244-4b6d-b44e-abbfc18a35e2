<template>
  <div id="officedialog">
    <el-dialog
      append-to-body
      :title="'办公消耗品'"
      :visible.sync="isdialog"
      width="50%"
      :close-on-click-modal='false'
      @close="handleClose">
      <el-form
        ref="officeForm"
        :model="officeForm"
        label-width="200px"
        :disabled="officedetails"
        :rules="rules">
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="用品名称" prop="purArticlesName">
                <el-input v-model="officeForm.purArticlesName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="计量单位" prop="unit">
                <el-input v-model="officeForm.unit"></el-input>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="数量" prop="purQuantity">
                <el-input v-model="officeForm.purQuantity" type="number"
                          oninput="value=value.replace(/[^0-9]/g,'')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="预算单价" prop="purPrice">
                <InputMoney v-model="officeForm.purPrice" />
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="预算总价" prop="totalPrice">
                <InputMoney v-model="officeForm.totalPrice" />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="申请金额" prop="applyAmount">
                <InputMoney v-model="officeForm.applyAmount" />
              </el-form-item>
            </el-col>
          </el-form-item>

        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="11">
              <el-form-item label="采购方式" prop="purMode">
                <el-select v-model="officeForm.purMode" >
                  <el-option
                    v-for="item in purTypeList"
                    :key="item.eleName"
                    :label="item.eleName"
                    :value="item.eleName">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item>
            <el-col :span="22">
              <el-form-item label="备注">
                <el-input type="textarea" v-model="officeForm.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="handleClose('officeForm')" v-if="!officedetails"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('officeForm')"
          v-if="!officedetails">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'officedialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.officedetails = bool
    }
  },
  mounted() {
    this.selectPerformStageList() // 查询履行阶段基础数据
  },
  data() {
    return {
      isdialog: this.dialog,
      officedetails: this.isDetails,
      officeForm: {},
      purTypeList: [],
      contractList: [],
      rules: {
        purArticlesName: [
          { required: true, message: '请输入用品名称', trigger: 'blur' }
        ],
        unit: [{ required: true, message: '请输入计量单位', trigger: 'blur' }],
        purQuantity: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        purPrice: [
          { required: true, message: '请输入单价', trigger: 'blur' }
        ],
        totalPrice: [
          { required: true, message: '请输入预算总价', trigger: 'blur' }
        ],
        applyAmount: [
          { required: true, message: '请输入申请金额', trigger: 'blur' }
        ],
        purMode: [
          { required: true, message: '请选择采购方式', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    selectPerformStageList() {
      this.$callApi('selectPurType', {}, result => {
        if (result.success) {
          this.purTypeList = result.data
        }
        return true
      })
    },
    contracChange(data) {
      this.officeForm.contracpartyId = data.id
      this.officeForm.contracpartyName = data.contracpartyName
    },
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.handleClose()
          this.$parent.reload(this.officeForm)
          return true
        } else {
          return false
        }
      })
    },
    resetForm() {
      if (this.$refs['officeForm']) { // 清空form数据
        this.$refs['officeForm'].resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  /deep/ .el-dialog__body {
    margin-left: -245px;
  }

  /deep/ .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }
</style>
<style lang="scss">
</style>
