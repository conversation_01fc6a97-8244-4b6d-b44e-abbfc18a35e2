<template>
  <div class="plan-list" :id="extendContractId">
    <div class="top-btns">
      <el-button size="mini" @click="addBtn()"  icon="el-icon-circle-plus-outline">添加明细</el-button>
      <el-button size="mini" @click="deleteBtn" :disabled='checkedRow.length === 0 || !havePlan || !showButton' plain icon="el-icon-delete">删除</el-button>
    </div>
    <div class="bottom-table plan-container">
      <el-table border :data="purDetailed" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="center" prop="purCodeDetail" label="采购编码" width="130">
          <template #header><span style="color:#f56c6c;">*</span>采购编码</template>
          <template slot-scope='{row}'>
            <el-input disabled placeholder="保存后自动添加" v-model="row.purCodeDetail"/>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="budgetItemsName" width="215">
          <template #header><span style="color:#f56c6c;">*</span>预算项目</template>bu
          <template slot-scope='{row, $index}'>
            <el-input  v-if="havePlan"
                       v-model="row.budgetItemsName"
                       readonly
                       @click.native="chooseFuncBtn($index)"/>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="isGovPur" label="是否政府采购" width="85">
          <template #header><span style="color:#f56c6c;">*</span>是否政府采购</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.isGovPur" disabled class="purMode"
                       v-if="havePlan" @change="hideError($index)">
              <el-option label="是" value="isGovPur"> </el-option>
              <el-option label="否" value="isGovPur"> </el-option>
            </el-select>
            <span v-else>{{row.isGovPur}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purItem" label="采购品目" width="120">
          <template #header><span style="color:#f56c6c;">*</span>采购品目</template>
          <template slot-scope='{row, $index}'>
            <el-input  v-if="havePlan" class="purItem"
                       v-model="row.purItem"
                       readonly
                       @click.native="getPurItems($index)"/>
            <span v-else>{{row.purItem}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purDetailName" label="采购名称" width="120">
          <template #header><span style="color:#f56c6c;">*</span>采购名称</template>
          <template slot-scope='{row}'>
            <el-input  v-if="havePlan" class="purDetailName"
                       v-model="row.purDetailName"/>
            <span v-else>{{row.purDetailName}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="model" label="规格型号" width="120">
          <template slot-scope='{row}'>
            <el-input  v-if="havePlan"
                       v-model="row.model"/>
            <span v-else>{{row.model}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="unit" label="计量单位" width="85">
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.unit" class="purMode"
                       v-if="havePlan" @change="hideError($index)">
              <el-option
                v-for="item in unit"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{row.unit}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purQuantity" label="数量" width="80">
          <template #header><span style="color:#f56c6c;">*</span>数量</template>
          <template slot-scope='{row}'>
            <el-input  v-if="havePlan"  type="number" min="1"
                       v-model="row.purQuantity"
                       @blur="calculateAmount(row)"
                       onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"/>
            <span v-else>{{row.purQuantity}}</span>
          </template>
        </el-table-column>
        <el-table-column label="预算单价" width="80">
          <template #header><span style="color:#f56c6c;">*</span>预算单价</template>
          <template slot-scope='{row, $index}'>
            <el-input  v-if="havePlan"
                       class="money"
                       v-model="row.purPrice"
                       oninput="value=value.replace(/[^0-9.]/g,'')"
                       @blur="formFormatItem(row)"
                       @input="hideError($index)"/>
            <span v-else>{{row.purPrice}}</span>
          </template>
        </el-table-column>
        <el-table-column label="预算总价"  width="120">
          <template #header><span style="color:#f56c6c;">*</span>预算总价</template>
          <template slot-scope='{row, $index}'>
            <el-input  v-if="havePlan"
                       class="money"
                       v-model="row.totalPrice"
                       oninput="value=value.replace(/[^0-9.]/g,'')"
                       @blur="formFormatItem(row)"
                       @input="hideError($index)"/>
            <span v-else>{{row.totalPrice}}</span>
          </template>
        </el-table-column>
          <el-table-column label="采购金额"  width="100">
            <template slot-scope='{row, $index}'>
              <el-input  v-if="havePlan"
                         class="money"
                         v-model="row.purAmount"
                         oninput="value=value.replace(/[^0-9.]/g,'')"
                         @blur="formFormatItem(row)"
                         @input="hideError($index)"/>
              <span v-else>{{row.purAmount}}</span>
            </template>
        </el-table-column>
        <el-table-column align="left" prop="remark" width="170" label="备注">
          <template slot-scope='{row}'>
            <el-input  v-if="havePlan"
                       v-model="row.remark"/>
            <span v-else>{{row.remark}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purType" width="110" label="采购类型">
          <template #header><span style="color:#f56c6c;">*</span>采购类型</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.purType" class="purMode"
                       placeholder="采购类型" v-if="havePlan" disabled @change="hideError($index)">
              <el-option
                v-for="item in purType"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{row.purType}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="purMode"  label="采购方式">
          <template #header><span style="color:#f56c6c;">*</span>采购方式</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.purMode" class="purMode"
                       placeholder="采购方式" v-if="havePlan" @change="hideError($index)">
              <el-option
                v-for="item in purMode"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{row.purMode}}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'supplier-detail-list-xq-good',
  data() {
    return {
      extendContractId: 'extendContractId' + new Date().getTime(),
      checkedRow: [],
      purDetailed: [],
      isDialog: false,
      isChange: false,
      havePlan: true,
      showButton: true,
      contractList: [], // 合约方信息
      purModeList: [], // 履行阶段
      purMode: [], // 采购方式
      unit: [], // 计量单位
      purType: [], // 采购类型
      formPurType: '',
      baId: '', // 指标Id
      budgetItemsName: '', // 项目名称
      isGovPur: '', // 是否政府采购
      purItem: '', // 采购品目
      baAbstract: '', // 指标摘要
      deptCode: '',
      billType: '' // 采购单类型
    }
  },
  methods: {
    formFormatItem(item) {
      item.purQuantity = item.purQuantity.replace(/-/g, '')
      item.totalPrice = item.purQuantity * this.$unFormatMoney(item.purPrice)
      item.purAmount = this.$formatMoney(item.purAmount)
      item.totalPrice = this.$formatMoney(item.totalPrice)
      item.purPrice = this.$formatMoney(item.purPrice)
    },
    selectpurModeList() {
      this.$callApiParams('selectPurType', {}, result => {
        if (result.success) {
          this.unit = result.data.meterType
          this.purMode = result.data.performStage
          this.purType = result.data.purType
        }
        return true
      })
    },
    init(meta, mode, formFormat) {
      this.selectpurModeList()
      formFormat.addShowErrorCallbacks({
        '履行计划错误提示': errorItems => {
          if (this.$isNotEmpty(errorItems)) {
            var planIndexes = []
            var keys = Object.keys(errorItems)
            keys.forEach(key => {
              planIndexes.push(key.replace('planInfo', ''))
            })
            this.showPlanError(planIndexes)
          }
        }
      })
    },
    showPlanError(planIndexes) {
      if (this.$isNotEmpty(planIndexes)) {
        var $planTableTr = $('.plan-container table tr')
        $.each($planTableTr, (index, item) => {
          const planIndex = index - 1 // 目的是为了排除掉第一个tr(列头)

          if (planIndexes.indexOf(planIndex + '-contracpartyName') > -1) {
            $(item).find('.contracpartyName').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-purMode') > -1) {
            $(item).find('.purMode').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planMoney') > -1) {
            $(item).find('.planMoney').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planPaymentDate') > -1) {
            $(item).find('.planPaymentDate').find('input').addClass('planError')
          }
        })
      }
    },
    hideError(rowIndex) {
      var $rows = $('.plan-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.planError').removeClass('planError')
        }
      })
    },
    dept() {
      this.$parent.formCanvasMeta.colItems.forEach(colItems => {
        if (colItems.labelAlias === '申购部门') {
          this.deptCode = colItems.dataValue.substring(0, colItems.dataValue.lastIndexOf(' '))
        }
      })
    },
    addBtn() {
      const data = {}
      data.purCode = ''
      data.purCodeDetail = ''
      data.baId = ''
      data.budgetItemsName = ''
      data.isGovPur = ''
      data.baAbstract = ''
      data.purItem = ''
      data.purDetailName = ''
      data.model = ''
      data.unit = ''
      data.purQuantity = ''
      data.purPrice = ''
      data.totalPrice = ''
      data.purAmount = ''
      data.remark = ''
      data.purType = this.formPurType
      data.billType = this.billType
      data.purMode = ''
      this.purDetailed = this.purDetailed.concat([], data)
    },
    deleteBtn() {
      for (var i = 0; i < this.checkedRow.length; i++) {
        var index = this.purDetailed.indexOf(this.checkedRow[i])
        this.purDetailed.splice(index, 1)
      }
    },
    handleSelectionChange(rows) { this.checkedRow = rows },
    reload(data) {
      if (!this.isChange) {
        this.purDetailed = this.purDetailed.concat([], data)
      } else {
        this.checkedRow[0] = Object.assign(this.checkedRow[0], data)
      }
      this.isChange = false
    },
    chooseFuncBtn(index) {
      this.dept()
      this.$refDataBa(
        { '部门ID': this.deptCode, '关闭可用金额控制': '' },
        selectedData => {
          if (selectedData[0].可用金额 === 0) {
            return this.$message({
              message: '可用金额为0,请重新选预算项目',
              type: 'warning'
            })
          } else {
            this.$nextTick(() => {
              this.purDetailed[index].baId = selectedData[0].ID
              var name = selectedData[0].预算项目
              this.purDetailed[index].budgetItemsName = selectedData[0].业务编码 + name.substring(name.lastIndexOf(' '))
              this.purDetailed[index].isGovPur = selectedData[0].是否政府采购
              this.purDetailed[index].purItem = selectedData[0].采购品目
              this.purDetailed[index].baAbstract = selectedData[0].指标摘要
              this.purDetailed[index].baDeptId = selectedData[0].部门ID
            })
          }
        })
    },
    getPurItems(index) {
      const refData = { colType: '弹框', dataRef: '基础数据#采购品目' }
      this.$refData(undefined, refData,
        () => {
          // // 反选中弹出框中树的节点内容，return code。treeLable=code+" "+name
          if (this.purDetailed[index].purItem) {
            return this.purDetailed[index].purItem.split(' ')[0]
          }
        },
        () => {},
        selectedData => {
          this.purDetailed[index].purItem = selectedData.list[0].id
          this.purDetailed[index].purItem = selectedData.list[0].treeLable
        },
        { IS_ENABLED_eq: '是', multiple: false })
    },
    calculateAmount(row) {
      // row.purQuantity = row.purQuantity.replace(/-/g, '')
      row.purPrice = this.$fixMoney(row.purPrice)
      row.totalPrice = row.purQuantity * row.purPrice
      row.totalPrice = this.$formatMoney(row.totalPrice)
    }
  }
}
</script>

<style scoped lang="scss">
  .plan-list{
    padding: 3px 0px 3px 0px;
    width: 100%;
    height: 100% !important;
    display: flex;
    flex-direction: column;
    .total-amt{margin-left: 10px;font-size: 14px;}
    .top-btns{
      padding-bottom: 10px;
      margin-left: 0px;
      width: 27%;
      display: flex;
      .el-input{
        margin-left: 5px;
      }
    }
    .bottom-table{ flex: 1;}
    /deep/ .el-table .cell{
      padding: 0px 5px !important;
    }
    /deep/ .el-table .warning-row {
      background-color: rgb(255, 43, 43) !important;
    }
    /deep/ .el-table--border .el-table__cell:first-child .cell{
      padding: 0px 5px !important
    }
    /deep/ .planError {border-color: #ff5c00 !important;}
  }
</style>
<style lang="scss">
  .mini-table .plan-list .el-table .el-table-column--selection .cell {padding: 0px 0px !important;}
  .mini-table .plan-list .el-table .cell {padding: 0px 3px;}
  .mini-table .plan-list .el-table input {padding: 0px 3px; height: 28px;}
  .mini-table .plan-list .el-table .el-input__suffix {right: -4px;}
  .mini-table .plan-list .el-table .el-select .el-input .el-select__caret {font-size: 12px;}
  .mini-table .plan-list .el-table .cell .el-date-editor .el-input__prefix { display: none; }
  .plan-container input.el-input__inner {font-size: 12px;}
  .plan-container .money input.el-input__inner {text-align: right;padding-right: 5px;}
</style>
