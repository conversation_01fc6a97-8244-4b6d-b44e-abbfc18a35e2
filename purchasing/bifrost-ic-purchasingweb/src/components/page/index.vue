<template>
  <div :class="`${commonPageClass} ${commonPageClassEx}`" class="pageWarp">
    <slot name="pageContent"></slot>
  </div>
</template>

<script>
export default {
  name: 'page',
  data() {
    return {
      commonPageClass: 'common-page',
      commonPageClassEx: ''
    }
  },
  methods: {
    pageHideLeftRight() { // 隐藏主界面的左右内容，主要用于表单的制单预览样式
      this.commonPageClass = 'common-page common-page-hide-left-right'
    },
    pageHideLeftRightResume() { // 恢复显示主界面的左右内容，主要用于结束表单的制单预览
      this.commonPageClass = 'common-page'
    }
  },
  mounted() {
    // 浏览器兼容
    this.$nextTick(() => {
      this.$browserCompatibility('pageIndex')
    })
  }

}
</script>

<style lang="scss">
  // .vue-bifrostIcApp>div,
  // .vue-bifrostIcApp>div>div,
  // .vue-bifrostIcApp>div>div>div {
  //   overflow-x: auto;
  //   overflow-y: hidden;
  // }
  // 注意不能写死容器宽度 避免小屏无法正常渲染 重要！！勿加！！！
  // .vue-bifrostIcApp>div.pageWarp,
  // .vue-bifrostIcApp>div>div.pageWarp,
  // .vue-bifrostIcApp>div>div>div.pageWarp { // 增加页面最小宽度
  //    min-width: 1700px;
  // }
  $pageSpace: 0px;
  $columnTop: 53px;
  @mixin common-font {
    font-family: "iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .el-select-dropdown .el-select-dropdown__item{height: 36px !important; line-height: 36px !important; margin: 0 !important;}
  .el-select-dropdown .el-select-dropdown__item:hover{background-color: #E7F1FD;}
  .el-select-dropdown ul{margin: 0;}
  .el-select-dropdown .el-select-dropdown__item.selected{font-weight: 400; background-color: #f0f4f8; color: #333333;}
  .el-input.el-input--mini.el-input--suffix.is-focus input:focus{color: #333333 !important;}
  .el-picker-panel .el-date-table td span{border-radius: 2px;font-family: PingFangSC-Regular;font-size: 14px;}
  .el-date-editor .el-range-input::placeholder{ color: #999999 !important;}
  .el-date-range-picker__header div{font-weight: 700 !important; font-size: 14px !important;}
  .el-message .el-message__content{width: calc(100% - 24px);}
  .el-date-table th{width: 14px;
height: 20px;
font-family: PingFangSC-Medium;
font-size: 14px;
color: #666666;
text-align: left;
font-weight: 500; }
  .el-date-editor.el-range-editor.el-input__inner.el-date-editor--daterange.el-range-editor--mini.is-active{border: 1px solid #1F7FFF !important; }
  .el-range-editor--mini .el-range-input{font-size: 14px !important;}
  .el-picker-panel .el-date-table td.end-date span, .el-date-table td.start-date span {background: #1177EE !important;}
  .vue-bifrostIcApp .el-dropdown,
  // .vue-bifrostIcApp .el-dropdown .el-dropdown-selfdefine:active
  // {color: #ffffff !important;}
  // .el-table__empty-text { display: none; }
  // 表格空状态
  .table-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    .table-empty-img {
      width: 64px;
      height: 64px;
    }
    .table-empty-p {
      font-size: 14px;
      color: #999999;
      line-height: normal;
      margin: 5px 0 0;
    }
  }
  .luckysheet-input-box { // 自由表单编辑输入框的字体设置
    font-size: 14px !important;
    font-family: '宋体', Serif !important;
    color: #000 !important;
  }
  .luckysheet {border: 1px solid #DDDDDD !important;}
  .hideLuckysheetBorder{border:none !important;}
  #formPrintContainerGlobal{
    width: 100%;
  }
  .regularformPrintContainerGlobal{
    width: 970px !important;
  }
  .hideformPrintContainerGlobal{
    display: none !important;
  }
  .hidePrintSheetBorder{
    border: none !important;
  }
  .showformPrintContainerGlobal{
    display: flex !important;
  }
  .tableEditInput {
    .el-input__inner{
      padding: 3px;
    }
  }
  .tableEditInputNumber {
    .el-input__inner {
      text-align: right;
    }
  }
  .tableEditInputAlign-left {
    .el-input__inner {
      text-align: left;
    }
  }
  .tableEditInputAlign-center {
    .el-input__inner {
      text-align: center;
    }
  }
  .tableEditInputAlign-right {
    .el-input__inner {
      text-align: right;
    }
  }

  .el-select-dropdown__item {padding: 0px 10px !important;}
  .layout-container .content .showMain { top: 52px !important; }
  .ant-spin-container>div:first-child { height: calc(100% - 42px) !important; }
  #bifrostic {top:42px !important;}

  .common-page {height: 100%;position: relative;}
  .common-page .el-form-item__label { padding: 0px 5px !important; }
  .common-page .search-from .el-form-item__label,
  .common-page .search-style-1 .el-form-item__label { font-size: 13px !important;}
  .common-page .search-from .el-input__inner,
  .common-page .search-style-1 .el-input__inner { padding: 0px 12px !important; }
  .search-from .search-style-1 .el-form-item--mini.el-form-item,
  .search-from .search-style-1 .el-form-item--small.el-form-item { height: 32px !important; }
  // #root button.is-disabled{
  //     border: #DDDDDD solid 1px !important;
  //     color: #999999 !important ;
  //     background: #EEEEEE !important;
  //     &:hover{
  //       color: #999999 !important;
  //     }
  //     &:focus{
  //       color: #999999 !important;
  //     }
  //   }
    .el-message {
      width: 384px;
      min-height: 56px;
      background: #FFFFFF !important;
      box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.15);
      border-radius: 2px;
      border: none !important;
      font-family: PingFangSC-Medium;
      font-size: 16px !important;
      line-height: 24px !important;
      font-weight: 500 !important;
      padding: 16px 24px !important;
      i{
        margin-right: 12px ;
      }
      .el-message__content{
        word-break:break-all;
        color: #333333 !important;
      }
}
.el-icon-aliiconchenggongzhuangtai{color: #11BB77;}
.el-icon-error:before{background-color: #FA5555; color: #fff;border-radius: 50%;font-weight: 900;}
.el-switch{width: 44px;
height: 22px;
border-radius: 11px;}
.el-switch.is-checked{background: #1F7FFF;}
.el-message--error .el-message__content{color: #333333 !important;}
  .common-page .search-from .el-date-editor--date .el-input__prefix,
  .common-page .search-style-1 .el-date-editor--date .el-input__prefix{ display: none; }
  .root-common-page {
    height: calc(100% + #{$pageSpace})!important;
    position: absolute;
    top: -$pageSpace !important;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0px 20px 20px!important;
  }
  .vue-bifrostIcApp>div.common-page,
  .vue-bifrostIcApp>div>div.common-page,
  .vue-bifrostIcApp>div>div>div.common-page {
    height: calc(100% + #{$pageSpace});position: relative; top: -$pageSpace; padding: 0px 20px 20px; }
  .vue-bifrostIcApp>div.common-page.hasWfTabs,
  .vue-bifrostIcApp>div>div.common-page.hasWfTabs,
  .vue-bifrostIcApp>div>div>div.common-page.hasWfTabs {
    height: calc(100% + #{$pageSpace});position: relative; padding: 20px; }
  .el-table__header-wrapper{
    background: #f0f5ff !important;
  }
  .common-page .el-radio__inner,
  .common-page .el-textarea__inner,
  .common-page .el-checkbox__inner,
  .common-page .el-input__inner,
  .common-page .el-input-group__append {
    border: 1px solid #DDDDDD !important;
  }
  .common-page .el-range-editor--mini .el-range-separator{width: 15px; font-size: 14px;}
  .common-page .customDom .el-textarea__inner,
  .common-page .customDom .el-input__inner,
  .common-page .customDom .el-input-group__append {
    border:none !important;
  }
  .common-page .el-input-group__append { border-left: none !important; }
  .common-page .formCommonCols .el-textarea__inner,
  .common-page .formCommonCols .el-input__inner,
  .common-page .formCommonCols .el-input-group__append {
    border:none !important;
  }
  .common-page .dbcol-right .wf-audit-content-empty{ margin-top: 0px;}

  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    border-color: #1F7FFF !important;
    background-color:#1F7FFF !important;
  }

  .common-page .el-textarea__inner {padding: 5px !important;}
  .el-textarea .el-input__count { right: 5px !important; }
  .common-page .button-upload { display: inline-block; }
  .common-page .buttons-normal {padding: 5px 0 0 0}
  .common-page .notCformBiz .buttons-normal {padding: 20px 0 0 0 !important;}
  .common-page.hasWfTabs .buttons-normal {padding: 20px 0 0}
  .common-page .buttons-normal .el-button,
  .common-page .buttons-normal .el-button--mini {margin-top: 0px;}
  .common-page .column-bottom div.dbcol-left,.collapsed-right {
    margin: 10px 0px 0px 0px;
    border: 1px solid #bbb;
  }
  // .common-page .column-bottom {height: calc(100% - #{$columnTop}) !important;}
  // .common-page.buttons-normal-no-padding-top .column-bottom {height: calc(100% - 53px) !important;}
  .common-page.no-pager .column-bottom {height: 100% !important;}
  .common-page .el-table-column--selection .cell { text-align: center; }
  .rowFormContent .common-page.column-top-hide-no-padding-top .column-bottom {height: calc(100% - 0px) !important;}

  .common-dlg-form .common-page.buttons-normal-no-padding-top .column-bottom {height: 100% !important;}
  .common-dlg-form .listContentPagination.el-pagination {
    padding-left: 0px !important;padding-top: 10px !important; margin-left: -5px;}

  .vue-bifrostIcApp .flex-column .column-bottom .dbcol-left.padding,.collapsed-right.padding {
    padding:12px;
    height: calc(100% - 20px);
    margin-top: 20px;
    margin-left: 0px;
    margin-bottom: 0px;
    border: 1px solid #ddd;
  }
  .vue-bifrostIcApp .flex-column .column-bottom .dbcol-left.padding {
    margin-top: 0;
  }
  .common-page .el-icon-search {font-size: 13px;}
  .vue-bifrostIcApp .common-page .el-tree-node__content>.el-tree-node__expand-icon {
    padding: 2px;
  }
  .vue-bifrostIcApp .el-table{color: #666666;}
  .vue-bifrostIcApp .common-page .buttons-normal .el-input__suffix {right: 0px;}
  .vue-bifrostIcApp .common-page .custom-tree-node .el-icon-document {
    padding-left: 3px;
  }
  /*.flex-column .column-bottom .dbcol-left {
    width: 195px !important;
  }*/
  .vue-bifrostIcApp .common-page .el-tree-node__children .el-tree-node .el-tree-node__content .el-checkbox {
    padding-left: 11px;
  }
  .vue-bifrostIcApp .common-page .buttons-normal .el-input-group__append,
  .vue-bifrostIcApp .common-page .buttons-normal .el-input-group__prepend {
    padding: 0px 5px;
  }
  .vue-bifrostIcApp .common-page .el-form-item__content {text-align: left;}
  .common-page .el-radio__label {padding-left: 2px;}
  .common-page .el-radio {margin-right: 5px;}
  .common-page .colSettingSelectedSettingEdit .el-radio {margin-right: 3px;}
  .el-dropdown-menu {
    margin-top: 5px !important;
    // border: 1px solid #ccc !important;
    box-shadow: 0px 1px 6px 0px rgba(100,117,162,0.2) !important;
    padding: 12px 0!important;
    .el-dropdown-menu__item {
      font-family: PingFangSC-Regular;
      padding: 0 12px!important;
      font-size: 14px!important;
      color: #333333!important;
      line-height: 32px!important;
      &:not(.is-disabled):hover {
        background-color: rgba(17,119,238,.1);
      }
    }
  }
  .el-popper[x-placement^=bottom] .popper__arrow {
    border-bottom-color: #ccc !important;
  }

  .common-page .buttons-normal .el-button--small{
    // min-width: 80px;
    flex: 0 0 auto;
    font-size: 14px;
    padding: 0px 12px;
    line-height: 30px;
  }
  .common-page .top-btns .el-button--small{
    font-size: 14px;
    padding: 0px 12px;
    line-height: 30px;
  }
  .common-page .top-btns {
    display: flex;
    column-gap: 6px;
  }
  .common-page .top-btns .el-button {
    margin-left: 0px;
  }
  .common-page .el-button--mini {
    padding: 7px 7px 6px 7px !important;
    border-radius: 2px;
  }
  // .common-page .el-button+.el-button,
  // .common-page span.toolbar-button:not([style="display: none;"]) + span.toolbar-button,
  // .common-page span.toolbar-button:not(:first-child) + span.toolbar-button, {
  //   margin-left: 6px;
  // }
  .common-page .el-button [class*=el-icon-]+span {
    margin-left: 0px;
  }
  .common-page .notMoreButton { display: flex; column-gap: 6px; }

  .vue-bifrostIcApp .base-tab { height: 100%; }
  .vue-bifrostIcApp .base-tab .el-tabs__header { margin: -5px 0px 0px 0px; }
  // .vue-bifrostIcApp .base-tab .column-bottom {height: calc(100% - #{$columnTop}) !important;}
  // .vue-bifrostIcApp .base-tab #wf-audit-block .column-bottom {height: calc(100% - #{$columnTop}) !important;}
  .vue-bifrostIcApp .base-tab #wf-audit-content .wf-audit-detail .column-bottom {height: calc(100% - #{$columnTop}) !important;}

  #wf-audit-content .wf-audit-detail .common-page .form-create .el-radio__inner,
  #wf-audit-content .wf-audit-detail .common-page .form-create .el-textarea__inner,
  #wf-audit-content .wf-audit-detail .common-page .form-create .el-checkbox__inner,
  #wf-audit-content .wf-audit-detail .common-page .form-create .el-input__inner,
  #wf-audit-content .wf-audit-detail .common-page .form-create .el-input-group__append {
    border: none !important;
  }
  #wf-audit-content .wf-audit-detail .common-page .form-create .el-radio__inner.auditCanEdit,
  #wf-audit-content .wf-audit-detail .common-page .form-create .el-checkbox__inner.auditCanEdit{
    border: 1px solid #DDDDDD !important;

  }
  #wf-audit-content .wf-audit-detail .common-page .form-create .el-radio__input.is-checked .el-radio__inner.auditCanEdit,
  #wf-audit-content .wf-audit-detail .common-page .form-create .el-checkbox__input.is-checked .el-checkbox__inner.auditCanEdit {
    border-color:#1F7FFF !important;
    background-color:#1F7FFF !important;
  }

  .wf-audit-content.wf-audit-content-multiple-tabs .single-main .main-border { padding-top: 0px; }
  .vue-bifrostIcApp .base-tab #wf-audit-content.wf-audit-content-multiple-tabs .wf-audit-detail .column-bottom {
    height: calc(100% - 3px) !important;}
  .wf-audit-content.wf-audit-content-multiple-tabs .common-page .container-border .column-top {
    position: absolute; right: 0px; z-index: 10000;}
  .wf-audit-content.wf-audit-content-multiple-tabs .audit-file-tab .common-page .container-border .column-top {
    position: relative; padding-bottom: 2px;}
  .wf-audit-content.wf-audit-content-multiple-tabs
  .wf-audit-right-action .common-page .container-border .column-top { position: relative; }
  .wf-audit-content.wf-audit-content-multiple-tabs
  .wf-audit-right-action .single-main .main-border { padding-top: 10px; }
  .wf-audit-detail-final { height: 100%; }
  .wf-audit-content.wf-audit-content-multiple-tabs .el-tabs__item { padding: 0px 15px;}
  // .wf-audit-content.wf-audit-content-multiple-tabs .wf-audit-detail-final { height: calc(100% + 33px); }
  .wf-audit-detail-final .el-tabs.el-tabs--top .formRegularContainer { padding: 0px; border: none; }
  .wf-audit-detail-final .el-tabs.el-tabs--top .el-tabs__header.is-top {margin-bottom: 10px; }
  .wf-audit-detail-final .el-tabs.el-tabs--top .el-tabs__item {height: 34px !important;line-height: 34px !important; }
  .wf-audit-detail-final .el-tabs.el-tabs--top .el-tabs__nav-wrap::after {background-color: #fff;}
  // .el-tabs__header { border-bottom: #eeeeee 1px solid;}
  .no-tabitem .el-tabs__header {
    margin: 0;
    border: none;
  }
  .wf-audit-detail-final .el-main { padding: 0px!important; }
  .wf-audit-content.wf-audit-content-multiple-tabs .wf-audit-detail-final .el-tabs.el-tabs--top .formCommonHeader { display: none; }
  .wf-audit-content.wf-audit-content-multiple-tabs.showRegularFormHeader .wf-audit-detail-final .el-tabs.el-tabs--top .formCommonHeader { display: unset !important; }
  .wf-audit-content.wf-audit-content-multiple-tabs .single-main { width: 100% !important; }
  .wf-audit-content.wf-audit-content-multiple-tabs .el-tab-pane { overflow: auto; }
  .wf-audit-content.wf-audit-content-multiple-tabs .el-tabs__content { height: calc(100% - 60px) !important; }
  .wf-audit-content.wf-audit-content-multiple-tabs .formRegularContainer .el-tabs__content { height: 100% !important; }
  .vue-bifrostIcApp .base-tab .el-tabs__content {height: calc(100% - 28px);}
  .vue-bifrostIcApp .base-tab .el-tab-pane { height: 100%; }
  .vue-bifrostIcApp .base-tab .baDetailTabs .el-tab-pane { height: calc(100% + 5px); }
  // .vue-bifrostIcApp .base-tab.base-tab-hide .column-bottom {height: calc(100% - 51px) !important;}
  .vue-bifrostIcApp .base-tab-hide .el-tabs__content { height: 100% !important; }
  .vue-bifrostIcApp .base-tab-hide .el-tabs__header { display: none; }
  .common-page .main-border {padding: 0px 0 0 0}
  .common-page.buttons-normal-no-padding-top .main-border {padding:0}
  .common-page.hasWfTabs .main-border {padding: 20px 0px 0px 0px;}
  // .common-page.column-top-hide .main-border {padding: 20px 0px 0px 0px;}
  .common-page .show-apply-supplier-main .main-border { padding-top: 20px; }
  .common-page .wf-audit-detail .main-border {padding-top: 0px;}
  .common-page .wf-audit-detail .main-border .main-border {padding-top: 20px;}
  .common-page.column-top-hide .cformTabMainContent .main-border { padding-top: 0px; }
  .common-page.column-top-hide .cformTabMainContent .common-page .common-page.column-top-show .main-border { padding-top: 20px; }
  .common-page.column-top-hide .cformTabMainContent .common-page.buttons-normal-no-padding-top .main-border { padding-top: 12px; }

  .common-page.column-top-hide-no-padding-top .main-border { padding: 0px 0px 0px 0px; }
  .common-page.column-top-hide-no-padding-top .buttons-normal { padding: 0px 0px 0px 0px; }
  .common-page.buttons-normal-no-padding-top .buttons-normal { padding: 5px 0px 0px 0px; }
  .common-page.column-top-show .button-list { margin-bottom: 20px;; }
  .vue-bifrostIcApp .common-page .retract-block {
    // 影响审核显示审核区域按钮样式
    // left: -15px;
    top: 42.3%;
    width: 10px;
    height: 69px;
    line-height: 59px;
    color: white;
    background: rgba(31,127,255,.5);
  }
  .vue-bifrostIcApp .common-page .retract-block i {
    margin-left: -2px;
  }
  .flex-column .column-bottom .dbcol-left.collapsed { border: none !important; }
  .vue-bifrostIcApp .common-page .retract-block:hover {
    cursor: pointer;
    background: rgba(31,127,255,1);
    border: 1px solid rgba(31,127,255,1);
  }
  .el-input--mini .el-input__inner, textarea.el-input__inner {
    border: 1px solid #bbb;
    padding: 0px 5px;
  }
  .el-radio__input .el-radio__inner, .el-radio__input input { cursor: pointer !important; }
  .vue-bifrostIcApp .common-page .el-form-item--mini .el-date-editor--date .el-input__inner,
  .vue-bifrostIcApp .common-page .el-form-item--mini .el-date-editor--time .el-input__inner{
    padding-left: 25px;
  }
  .common-page .el-input__prefix {
    left: 0px;
    top: -1px;
  }
  .vue-bifrostIcApp .common-page .item-status-sign {
    border-radius: 30px;
    height: 10px;
    width: 10px;
    margin: 10px 0px;
  }
  .common-page .details-el-tab{ .el-tabs__nav-wrap{height: 0;}}
  .vue-bifrostIcApp .common-page .item-status-enabled { background: #00dc00; }
  .vue-bifrostIcApp .common-page .item-status-disabled { background: #cacaca; }
  .vue-bifrostIcApp .common-page .item-status-disablednew { background: #ffc966; }
  .vue-bifrostIcApp .common-page .main-border .el-pagination {
    padding-left: 0px;
    margin-top: 0px
  }
  .vue-bifrostIcApp .el-pagination { font-weight: 400; }
  .vue-bifrostIcApp .common-page .main-border .el-pagination .btn-prev {
    margin-left: 0px;
  }
  .vue-bifrostIcApp .el-button, .common-page .el-button,
  .vue-bifrostIcApp .el-button--danger, .common-page .el-button--danger,
  .vue-bifrostIcApp .common-page .el-button--danger,
  .vue-bifrostIcApp .common-page .el-button--danger.is-plain {border: 1px solid #DDDDDD;}

  .vue-bifrostIcApp .el-button--primary, .common-page .el-button--primary {border: 1px solid #3a8ee6;}
  .vue-bifrostIcApp .el-button--success, .common-page .el-button--success {border: 1px solid #5daf34;}

  .common-page .el-table th.el-table__cell>.cell {
    font-family: PingFangSC-Medium;
    padding-left: 1px;
    padding-right: 1px;
  }
  .el-table__header tr, .el-table__header th {
    padding: 0;
    height: 40px;
  }
  .el-table__header th.el-table__cell {padding: 0px;}
  .common-page .el-table--border{
    display: flex;
    flex-direction: column;
  }
  .el-table .caret-wrapper { height: 28px !important; }
  .common-page .el-table--small th.el-table__cell { padding: 0px; }
  .el-table .sort-caret.ascending { top: 3px !important; }
  .el-table .sort-caret.descending { bottom: 4px !important; }

  .common-page .el-table--border, .el-table--group {
    height: 100%
  }
  .common-page .el-table th.el-table__cell.is-leaf,
  .common-page .el-table td.el-table__cell {border-right: 1px solid #ddd!important; }
  .common-page {
    .el-table--group,.el-table--border {
      border: 1px solid #DDDDDD !important;
      &::after {
        background-color: #DDDDDD;
        width: 0px;
      }
    }
    .el-table {
      .el-table__header tr th:nth-last-child(2),th:last-child {
        border-right: none !important;
      }
      .el-table__row td:last-child {
        border-right: none !important;
        padding: 0;
      }
      &::before{
        height: 0px;
      }
    }
  }
  .common-page  .el-descriptions .is-bordered .el-descriptions-item__cell { border: 1px solid #DDDDDD }
  .common-page .el-table--border .el-table__cell,
  .common-page .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed,
  .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid #DDDDDD !important;
  }
  // .common-page .el-table__footer-wrapper .el-table__cell {//去除合计行的border
  //   border-bottom: none !important;
  // }
  .common-page .el-table__body-wrapper {
    flex: 1;
    overflow: overlay;
  }
  .common-page .el-table th.el-table__cell>.cell {font-weight: 700;color: #333333;}
  .common-page .el-table th.el-table__cell {background-color: #f0f5ff;}
  .common-page .el-table--small .el-table__cell { padding: 10px 0px; }
  .common-page .el-table .cell { line-height: 20px; padding: 0px 3px; }
  .common-page .el-table .el-table-column--selection .cell{padding: 0;}
  .common-page .el-table tr.el-table__row.checkedTR td.el-table__cell { background: #F4FCFF !important; }
  .common-page .el-table tr.el-table__row.currentTR td.el-table__cell { background: #f9ab6a !important; }

  .el-table thead.is-group th.el-table__cell { background-color: #f0f5ff !important; }
  .common-page .blue-table .el-table__cell {
    padding: 4px 0;
  }
  // .common-page .el-table.blue-table.el-table--border,
  // .common-page .el-table.blue-table.el-table--group {border: 1px solid #98bfff !important;}
  // .common-page .el-table.blue-table th.el-table__cell.is-leaf,
  // .common-page .el-table.blue-table td.el-table__cell {border-right: 1px solid #98bfff !important;}
  // .common-page .el-table.blue-table th.el-table__cell {background-color: #dbeeff !important; }
  // .common-page .blue-table.el-table--border .el-table__cell,
  // .common-page .el-table__body-wrapper .blue-table.el-table--border.is-scrolling-left ~ .el-table__fixed,
  // .el-table.blue-table th.el-table__cell.is-leaf {border-bottom: 1px solid #98bfff !important;}
  // .el-table.blue-table thead {color: #666666;}
  // .el-table--striped.blue-table .el-table__body tr.el-table__row--striped td.el-table__cell {background: #ededed;}
  .el-table--striped.editing-table .el-table__body tr.el-table__row--striped td.el-table__cell {background: #fff;}

  // .el-table.el-table--border .el-table__cell { border-right: 1px solid #ddd !important; }
  // .el-table.blue-table.el-table--border .el-table__cell { border-right: 1px solid #98bfff !important; }

  .el-select-dropdown__item {
    height: 26px !important;
    line-height: 26px !important;
  }
  // .vue-bifrostIcApp .el-textarea textarea {
  //   padding: 5px;
  //   border: 1px solid #bbb;
  // }
  .vue-bifrostIcApp .el-radio__inner {
    border: 1px solid #aaa;
  }

  .vue-bifrostIcApp .common-page .previewButtons {display: none; }
  .vue-bifrostIcApp .common-page-hide-left-right .dbcol-left { display: none; }
  .vue-bifrostIcApp .common-page-hide-left-right .formRegularSetting { display: none; }
  .vue-bifrostIcApp .common-page-hide-left-right .retract-block { margin-left: -15px; }
  .vue-bifrostIcApp .common-page-hide-left-right .previewButtons {display: block; }
  .common-page .baseListTable{ font-size: 14px; height: calc(100% - 0px); }

  .el-table--border .el-table__cell:first-child .cell {
    text-overflow: clip;
    padding: 0px !important;
  }
  .listContentNoSelection .el-table--border .el-table__cell:first-child .cell { padding-left: 10px !important;}

  .mini-table .el-table .el-table--border .el-table__cell:first-child .cell { padding-left: 0px !important; }
  /*.mini-table .el-table--border .el-table__cell:first-child .cell {*/
  /*  padding: 0px 0px 0px 5px !important;*/
  /*}*/
  /*.el-table--border.el-table--small .el-table__cell:first-child .cell{*/
  /*  padding: 0px 0px 0px 0px !important;*/
  /*}*/
  /*.mini-table .el-table--border.el-table--small .el-table__cell:first-child .cell {*/
  /*  padding: 0px 0px 0px 5px !important;*/
  /*}*/
  .common-page .el-table th.el-table__cell.is-leaf .cell{
    padding: 0px 0px 0px 0px !important;
    text-align: center;
  }
  .mini-table .el-table__header tr, .el-table__header th {
    padding: 0;
    height: 30px;
  }
  .mini-table .el-table__body tr, .el-table--small,.mini-table .el-table__body .el-table__cell {
    padding: 0;
    height: 30px;
  }
  .mini-table .el-table--small .el-table__cell {
    padding: 2px 0;
  }
  .vue-bifrostIcApp .mini-table th.el-table__cell>.cell {
    padding: 0px;
    text-align: center;
  }

  /*弹框内容自适应弹框高度*/
  .el-dialog__body {
    height: calc(100% - 60px) !important;
  }

  .common-page .classifytree-filter-input .el-input__inner {
    line-height: 28px;
    height: 28px;
    padding: 5px 5px 5px 21px;
  }

  .ztree {padding-top: 10px; border: none !important;}
  .ztree .button.checkbox_true_part:after {width: 8px !important;}
  .ztree .button.noline_open:before {
    position: absolute  !important;
    top: 7px !important;
    left: 9px !important;
    content: ''  !important;
    -webkit-transition: -webkit-transform ease 0.3s  !important;
    transition: transform ease 0.3s, -webkit-transform ease 0.3s  !important;
    -webkit-transform: rotateZ(0deg)  !important;
    transform: rotateZ(0deg)  !important;
    -webkit-transform-origin: 25% 50%  !important;
    transform-origin: 25% 50%  !important;
    border: 4px solid  !important;
    border-color: transparent transparent transparent #666  !important;
  }

  .ztree .button.noline_close:before,
  .ztree .button.noline_open:before,
  .ztree .button.root_open:before,
  .ztree .button.root_close:before,
  .ztree .button.roots_open:before,
  .ztree .button.roots_close:before,
  .ztree .button.bottom_open:before,
  .ztree .button.bottom_close:before,
  .ztree .button.center_open:before,
  .ztree .button.center_close:before {
    // position: absolute;
    // top: 7px !important;
    // left: 9px !important;
    // content: '' !important;
    // transition: -webkit-transform ease 0.3s !important;
    // -webkit-transition: -webkit-transform ease 0.3s !important;
    // transition: transform ease 0.3s !important;
    // transition: transform ease 0.3s, -webkit-transform ease 0.3s !important;
    // -webkit-transform: rotateZ(0deg) !important;
    // transform: rotateZ(0deg) !important;
    // -webkit-transform-origin: 25% 50% !important;
    // transform-origin: 25% 50% !important;
    // border: 4px solid !important;
    // border-color: transparent transparent transparent #666 !important;
    @include common-font;
    display: inline-block;
    position: relative;
    right: -2px;
    font-size: 14px;
    color: #c3c3c3;
    line-height: normal;
    content: '\e69b';
  }

  .ztree .button.noline_open:before,
  .ztree .button.root_open:before,
  .ztree .button.roots_open:before,
  .ztree .button.bottom_open:before,
  .ztree .button.center_open:before {
    // -webkit-transform: rotateZ(90deg) !important;
    // transform: rotateZ(90deg) !important;
    content: '\e69c';
  }

  .ztree .button.ico_open,
  .ztree .button.ico_close{
    display: none;
  }
  // .ztree .button.ico_docu {background-position-y:9px !important;background-position-x:4px !important;}
  .ztree .node_name {border-radius: 2px !important;}
  .ztree .curSelectedNode {background-color: #e5f1fe!important;}
  // .ztree .curSelectedNode::after{
  //   content: '\e6da';
  //   position: absolute;
  //   right: 8px;
  //   color: #2985fe;
  //   font-family: element-icons!important;
  // }
  // 隐藏子部门线条
  .ztree .center_docu,.bottom_docu,.roots_docu {
    display: none!important;
  }

  .common-page.hasWfTabs .el-tabs__item{font-weight: 400;height: 32px !important;line-height: 34px !important;}
  .control-layout .el-tabs__header .el-tabs__item {font-weight: 400 !important;}
  .layout-container .header .logo {display: none; }
  .layout-container .header .sysname { padding-left: 20px; }
  .layout-container .header .h-right .userinfo:first-child { display: none; }
  .layout-container .header .h-right .userinfo { font-size: 14px; }
  .layout-container .header i {font-size: 18px;}
  .layout-container .aside { width: 180px; }
  .layout-container .content { left: 180px; }
  .layout-container .el-menu-vertical-demo:not(.el-menu--collapse) {width: 180px;}
  .el-submenu .el-menu-item {width: 180px;}
  .control-layout .el-submenu .el-menu-item,
  .control-layout .el-menu--vertical .el-menu-item,
  .control-layout .el-menu--horizontal .el-menu-item {height: 30px;line-height: 30px;}
  .control-layout .el-menu-item, .control-layout .el-submenu__title {height: 40px;line-height: 40px;font-size: 14px;}
  .layout-container .aside .user-block {height: 70px;}
  .layout-container .aside .user-block .user-info {padding: 20px 10% 10% 10px;height: 70px;}
  .control-layout .el-submenu .el-menu-item,
  .control-layout .el-menu--vertical .el-menu-item,
  .control-layout .el-menu--horizontal .el-menu-item {padding-left: 30px !important;}
  .control-layout .el-submenu__title.is-active,
  .control-layout .el-menu-item.is-active,
  .control-layout .el-submenu__title i.is-active,
  .control-layout .el-menu-item i.is-active {background: #000 !important;}
  .layout-container .aside {padding-bottom:0px !important;}
  .layout-container .aside > div:last-child {height: 30px !important;line-height: 30px !important;font-size: 14px;}

  .common-dlg-form .dialog-footer { text-align: right; }
  .common-dlg-form .el-input__inner { padding-left: 5px; }
  .common-dlg-form .el-descriptions { margin-left: 0px !important; }
  .common-dlg-form .ba-mini th { padding: 5px !important; width: 95px !important; }

  .ba-mini .el-descriptions__header { margin-bottom: 7px; }
  .ba-mini .el-descriptions__title {font-size: 14px;font-weight: 400;}
  .ba-mini .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {padding-bottom: 5px;}
  .ba-mini .el-descriptions .is-bordered .el-descriptions-item__cell {border: 1px solid #d2d2d2;padding: 5px 10px;}
  .ba-mini .el-descriptions-item__label.is-bordered-label { background: #efefef; }

  .system-header-logo { margin-left: 20px; }
  // !!新UI样式展示界面logo
  // .system-header-logo img {display: none;}
  .system-header-logo a span {left: 0px;}
  .system-header-search-wrap {display: none;}
  .basicLayout-siderMenu .siderTitle .menuCard .card-pic { margin-left: 20px; }
  .ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title { font-size: 14px; }
  // !! 导致融合平台菜单二级菜单以及二级菜单子级没有缩进 无法正确区分二级菜单
  // .basicLayout-siderMenu .baseMenu .ant-menu-submenu-title { padding-left: 20px !important; }
  .basicLayout-siderMenu .baseMenu .ant-menu-inline .ant-menu-item,
  .basicLayout-siderMenu .baseMenu .ant-menu-vertical .ant-menu-item {
    font-size: 14px;
    // !! 导致融合平台菜单二级菜单以及二级菜单子级没有缩进 无法正确区分二级菜单
    // padding-left: 35px !important;
  }
  .ant-menu-sub.ant-menu-inline > .ant-menu-item,
  .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title { line-height: 32px;height: 32px; }
  .basicLayout-siderMenu .baseMenu .ant-menu-inline .ant-menu-item-selected::after { top: 11px; border-width: 5px;right: 0px;}
  // !! 导致融合平台菜单二级菜单以及二级菜单子级没有缩进 无法正确区分二级菜单
  // .CqLayout .ant-layout-sider.special .basicLayout-siderMenu .baseMenu .ant-menu-inline .ant-menu-submenu-open > div { padding-left: 20px !important; }
  .CqLayout .ant-layout-sider.special .basicLayout-siderMenu .baseMenu .ant-menu-inline .ant-menu-submenu-open > ul { padding: 5px 0px; }
  .ant-tabs.page-tab-global > .ant-tabs-card-bar .ant-tabs-nav-container{ font-size: 14px; }
  .ant-tabs.page-tab-global > .ant-tabs-content,
  .ant-tabs.page-tab-global > .ant-tabs-bar { height: 36px; line-height: 36px; }
  .ant-tabs.page-tab-global > .ant-tabs-card-bar .ant-tabs-nav-wrap .ant-tabs-tab {
    border: none;height: 36px; line-height: 36px; background: #F1F3F7; color: #bbb; padding: 0px 10px 0px 20px;border-right: 1px solid #d9d9d9;}
  // .ant-tabs.page-tab-global > .ant-tabs-card-bar .ant-tabs-nav-wrap .ant-tabs-tab:nth-last-child(1) { border: none; }
  .ant-tabs.page-tab-global > .ant-tabs-card-bar .ant-tabs-nav-container  { height: 36px; line-height: 36px; max-height: 36px; }
  .ant-tabs.page-tab-global > .ant-tabs-card-bar .ant-tabs-nav-wrap .ant-tabs-tab.ant-tabs-tab-active { font-weight: normal; }
  .ant-tabs.page-tab-global > .ant-tabs-card-bar .ant-tabs-nav-wrap .ant-tabs-tab i.ant-tabs-close-x svg { height: 14px; width: 14px; }
  .ant-tabs.page-tab-global > .ant-tabs-card-bar .ant-tabs-nav-wrap .ant-tabs-tab i.ant-tabs-close-x { margin-top: 2.5px; color: #bbb; }
  .ant-tabs.page-tab-global > .ant-tabs-card-bar .ant-tabs-nav-wrap .ant-tabs-tab i.ant-tabs-close-x:hover { color: #000; }
  .CqLayout .ant-layout-sider.special .ant-layout-sider-trigger { width: 10px !important;}
  .CqLayout .ant-layout-sider.special .ant-layout-sider-trigger > i { font-size: 13px; color: #a5a5a5;margin-left: -1px; }
  ::-webkit-scrollbar { width: 8px !important; height: 8px !important; }
  ::-webkit-scrollbar-thumb { border-radius: 0px !important; background-color: #cfcfcf; }
  .el-pagination.is-background .el-pager li:not(.disabled).active {  border-radius: 2px; font-size: 14px;background: #1F7FFF !important; }
 .toolbar-buttin-list .el-button{color: #666666;}
  // .el-button.is-plain:hover {
  //   background: #478BFE !important;
  //   border-color: #478BFE !important;
  //   color: #fff !important;
  // }
  // .el-button.is-plain:focus {
  //     color: #fff !important;
  //     background: #478BFE !important;
  //   }
  //   .el-button.is-plain:active{
  //     background: #1070E1 !important;
  //   border-color: #1070E1 !important;
  //   color: #fff !important;
  //   }

  .vue-bifrostIcApp .common-page .el-button--danger:hover,
  .vue-bifrostIcApp .common-page .el-button--danger:focus,
  .vue-bifrostIcApp .common-page .el-button--danger.is-plain:focus,
  .vue-bifrostIcApp .common-page .el-button--danger.is-plain:hover{
    color: #fff !important;
    background: #F56C6C !important;
    border-color: #F56C6C !important;
  }

  .common-page .el-button.is-disabled.is-plain,
  .common-page .el-button.is-disabled.is-plain:focus,
  .common-page .el-button.is-disabled.is-plain:hover {
    // background-color: #409eff !important;
    // border-color: #DDDDDD !important;
    // color: #fff !important; 

        // background: #478BFE;
        // border-color: #478BFE;
        // color: #fff;
    }
  .el-popover.el-popper .moreButtonContainer{
    display: grid;
    row-gap: 6px;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    text-align: center;
  }
  .vue-bifrostIcApp .common-page .el-button.is-disabled.is-plain,
  .vue-bifrostIcApp .common-page .el-button.is-disabled.is-plain:focus,
  .vue-bifrostIcApp .common-page .el-button.is-disabled.is-plain:hover,
  .el-popover.el-popper .moreButtonContainer .el-button.is-disabled.is-plain,
  .el-popover.el-popper .moreButtonContainer .el-button.is-disabled.is-plain:focus,
  .el-popover.el-popper .moreButtonContainer .el-button.is-disabled.is-plain:hover {
    // background-color: #409eff !important;
    // border-color: #DDDDDD !important;
    // color: #fff !important; 
    background-color: #409eff;
    border-color: #EBEEF5;
    color: #fff;
  }

  .bxExtendRefBaDlg .bxExtendRefBaDlgEditInput .el-input__inner {
    text-align: right !important;
    font-size: 12px !important;
    padding: 0px 5px !important;
  }
  // 审核附件tab
  .common-page  #audit-file-tab{height: 100%;}
  .common-page  #audit-file-tab .buttons-normal{padding: 0px 0px 3px 0px;}
  .common-page  #audit-file-tab .main-border{padding: 0px 0px 0px 0px;}
  .common-page  #audit-file-tab #attach-audit-extent .el-table-fix {
    position: absolute;
    width: 100%;
    height: calc(100% - 32px);
  }
  // 审核附件发票tab
  .common-page  #audit-file-invoice-tab{height: 100% !important; padding-left: 0px;}
  .common-page  #audit-file-invoice-tab .buttons-normal{padding: 0px 0px 3px 0px;}
  .common-page  #audit-file-invoice-tab .main-border{padding: 0px 0px 0px 0px;}
  .common-page  #audit-file-invoice-tab #attach-audit-extent { height: calc((100% - 20px) / 2 + 10px); padding-left: 0px;}
  .common-page  #audit-file-invoice-tab #e-invoice { height: calc((100% - 20px) / 2 + 10px); padding-left: 0px; }
  .common-page  #audit-file-invoice-tab #attach-audit-extent .el-table-fix {
    position: absolute;
    width: 100%;
    height: calc(100% - 28px);
  }
  .common-page  #audit-file-invoice-tab #attach-audit-extent .column-bottom{height: calc((100% - 5px)) !important;}
  .common-page  #audit-file-invoice-tab #e-invoice .column-bottom{height: calc((100% - 32px)) !important;}
  .common-page  #audit-file-invoice-tab .column-top{position: relative; }
  // 报销单附件详情设置
  .common-page  #form-detail-tab-attach-bx .buttons-normal{padding: 0px 0px 8px 0px;}
  .common-page  #form-detail-tab-attach-bx .main-border{padding: 0px 0px 0px 0px;}
  .common-page  #form-detail-tab-attach-bx #attach-audit-extent { height: calc((100% - 20px) / 2 + 10px); padding-left: 0px; }
  .common-page  #form-detail-tab-attach-full #attach-audit-extent { height: 100%; padding-left: 0px; }
  .common-page  #form-detail-tab-attach-bx .e-invoice { height: calc((100% - 20px) / 2 + 10px); padding-left: 0px; }
  /*.common-page #form-detail-tab-attach-bx .column-bottom {
    height: calc(100% - 38px) !important;
  }*/

  // 事前申请单附件详情设置
  .common-page  #form-detail-tab-attach-sq{height: 100%;}
  .common-page  #form-detail-tab-attach-sq .buttons-normal{padding: 0px 0px 2px 0px;}
  .common-page  #form-detail-tab-attach-sq .main-border{padding: 0px 0px 0px 0px;}

  // 报销单收款人详情设置
  .common-page  #form-detail-tab-payee-bx .el-table{font-size: 14px;}
  .common-page  #form-detail-tab-payee-bx .el-table .cell{line-height: 20px;}

  .inc-canvas-top-buttons { position: absolute; top: 15px; left: 15px; z-index: 3000; }
  .inc-canvas-top-buttons .el-button--small,
  .inc-canvas-top-buttons .el-button--small.is-round {
    padding: 3px 8px !important;
    border-radius: 15px !important;
    margin-right: -8px !important;
  }

  .formCanvasDetail .inc-canvas-top-buttons { top: 5px; left: 5px; }
  .formCanvasDetail .inc-canvas-top-buttons .el-button--small,
  .formCanvasDetail .inc-canvas-top-buttons .el-button--small.is-round {
    margin-right: -3px !important;
  }

  .formCanvasTab {position: absolute;} // 需单独处理
  .formCanvasTab .el-tabs__content {overflow: inherit}
  // 应急处理
  #dudraftapply .formCanvasTab { position: static !important;}
  // 处理预算申报高度被绝对定位影响
  #oneupapply .formCanvasTab { position: static !important;}
  .formCanvasTab .el-tabs__active-bar { background-color: unset; }
  .formCanvasTab .el-tabs__nav-wrap::after { background-color: unset; }
  .formCanvasTab .el-tabs__item { height: 20px; line-height: 20px; padding: 0px 8px; }
  .formCanvasTab.hideFormCanvasTab .el-tabs__header { display: none; }

  .el-message--error .el-message__content { line-height: 18px; }

  .common-page .el-textarea.is-disabled .el-textarea__inner {background-color: #efefef;}
  .common-page .el-input.is-disabled .el-input__inner {background-color: #efefef;}
  .common-page .formRegularDetailOrAuditMode .el-textarea.is-disabled .el-textarea__inner {background-color: #fff;}
  .common-page .formRegularDetailOrAuditMode .el-input.is-disabled .el-input__inner {background-color: #fff;}

  .ba-biz-form {
    height:100%;width: 100%;border: 1px solid #ddd;padding: 35px 30px 35px 0px;}
  .ba-biz-form .ba-biz-form-biz-type .el-radio--medium.is-bordered {padding: 4px 20px 0 15px;}
  .ba-biz-adjust .ba-biz-form-buttons .el-form-item__content { text-align: right; }
  .ba-biz-form .el-input__inner { padding-left: 5px; }

  .ba-biz-form .el-radio__input.is-checked .el-radio__inner,
  .el-radio-ex-style .el-radio__input.is-checked .el-radio__inner {
    border-color: #409EFF;
    background: #fff;
  }
  .ba-biz-form .el-radio__input.is-checked .el-radio__inner,
  .el-radio-ex-style .el-radio__input.is-checked .el-radio__inner {
    border-color: #fff !important;
  }
  .ba-biz-form .el-radio__input.is-checked+.el-radio__label,
  .el-radio-ex-style .el-radio__input.is-checked+.el-radio__label {
    color: #fff;
  }
  .baShiftContent .ba-biz-form .el-radio__input.is-checked.is-disabled + span.el-radio__label {
    color: #fff;
  }
  .ba-biz-form .el-radio__input.is-checked .el-radio__inner::after,
  .el-radio-ex-style .el-radio__input.is-checked .el-radio__inner::after {
    background: #409EFF;
  }
  .ba-biz-form .el-radio.is-bordered.is-checked,
  .el-radio-ex-style .el-radio.is-bordered.is-checked {
    border-color: #fff;
    background: #409EFF;
  }

  .lawfile-save-dlg-extend { margin-left: 5px; }
  .common-page .lawfile-save-dlg-extend .el-radio { margin-right: 0px; }
  .lawfile-save-dlg-extend .el-radio.is-bordered+.el-radio.is-bordered { margin-left: 5px; }
  .lawfile-save-dlg-extend .el-radio--small.is-bordered { padding-top: 6px; height: 28px; }
  .lawfile-save-dlg-extend.el-radio-ex-style .el-radio.is-bordered.is-checked {
    border: 1px solid #409EFF;
    padding-top: 5px;
    height: 27px;
  }
  .baShiftContent .shift-amount-total input{font-weight: 800;font-size: 14px;}
  @import '../../styles/dialog.scss'; // 处理乾坤react版导致el-dialog弹框样式设置不上

  .common-page .listReport .el-table th.el-table__cell .cell {
    font-family: PingFangSC-Medium!important;
    font-size: 14px;
    font-weight: 800;
    color: #323232;
  }
  .common-page .listReport .el-table th.el-table__cell { padding: 5px 2px; }
  // .common-page .listReport .el-table th.el-table__cell.is-leaf,
  // .common-page .listReport .el-table td.el-table__cell {
  //   border-right: 1px solid #ddd !important;
  // }
  .common-page .listReport .el-table--border .el-table__cell,
  .common-page .listReport .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed,
  .listReport .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid #ddd !important;
  }
  // .common-page .listReport .el-table .cell { padding: 0px 5px; }
  .common-page .listReport .el-table--border, .common-page .listReport .el-table--group {
    border: 1px solid #DDDDDD !important;
    // border-right: none !important;
  }
  .listReport .el-table .cell { color: #323232; font-family: PingFangSC-Regular; font-size:14px; }
  .listReport .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {background: #fff;}
  .contentHeader {
    border: 1px solid #DDDDDD !important;
    border-bottom: none !important;
    text-align: center;
    padding: 10px 0px 0px 0px;
  }
  .reportTitle{ font-family: PingFangSC-Medium !important; font-size: 26px;font-weight: 800;color: #000; padding-bottom: 10px; }
  .enterpriseName {
    text-align: left;
    border-top: 1px solid #ddd !important;
    color: #323232;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    padding: 1px 0px 1px 3px;
  }
  // .common-page .listReport .el-checkbox__inner { border: 1px solid #5a5a5a !important; }
  .contentFooter {
    text-align: left;
    padding: 0px;
  }
  .contentFooterLevel {
    display: flex;
    border-bottom: 1px solid #DDDDDD !important;
    border-right: 1px solid #DDDDDD !important;
  }
  .contentFooterLevel .reportSumCell {
    color: #323232;
    font-family: 宋体, Serif;
    font-size: 16px;
    padding: 5px;
    border-left: 1px solid #DDDDDD !important;
    font-weight: 800;
  }

  .refTablePager { position: absolute;left: -10px;bottom: 5px; }
  .el-select .el-input .el-select__caret { color: #8c8c8c !important; }

  .custom-card { width: 100%;height: 100%; }
  .custom-card-header:before,
  .custom-card-header:after { display: table; content: ""; }
  .custom-card-header:after { clear: both; }
  .custom-card .el-card__body { border: none; height: calc(100% - 32px); }
  .custom-card .el-card__header, .custom-card .el-card__body { padding: 5px; }
  .custom-card .el-card__header { border-bottom: 1px solid #ccc !important; background: #f8f8f8; }
  .el-input__inner{border-radius: 2px !important;}
  .custom-card.el-card {
    border-radius: 2px;
    border: 1px solid #DDDDDD !important;
    cursor: default !important; }
  // .custom-card.el-card.is-always-shadow { box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.2) !important; }
  .custom-card .custom-card-header span { font-weight: 400; color: #666; }
  .custom-card .custom-card-header button { float: right;padding: 3px 0px;font-size: 14px; border: none; }
  .custom-card.custom-card-just-table .common-page .el-table--border { margin-top: 12px; }
  .custom-card.custom-card-just-table .el-card__body { padding: 0px; }

  .cform-edit-tab-textereas-container { height: 100%;display: flex;flex-direction: column; }
  .vue-bifrostIcApp .common-page .cform-edit-tab-textereas-container .el-form-item__content { margin-left: 105px !important; }
  .vue-bifrostIcApp .common-page .cform-edit-tab-textereas-container .el-form-item--small .el-form-item__label {
    width: 105px !important; white-space: pre-line;line-height: 16px; }
  .cform-edit-tab-textereas-container .custom-card .el-card__body > div { display: flex; flex-direction: column;height: 100%; }
  .cform-edit-tab-textereas-container .row-marginBottom {flex: 1;}
  .cform-edit-tab-textereas-container .row-marginBottom:not(:last-child) {margin-bottom: 5px !important;}
  .cform-edit-tab-textereas-container .row-marginBottom:last-child {margin-bottom: 0px !important;}
  .cform-edit-tab-textereas-container .row-marginBottom div,
  .cform-edit-tab-textereas-container .row-marginBottom .el-textarea__inner { height: 100%; }
  .cform-edit-tab-textereas-container .row-marginBottom .el-form-item--small.el-form-item { margin-bottom: 0px !important; }

  .dynamic-tab .strip-blocks > div:not(:last-child),
  .wf-audit-content.wf-audit-content-multiple-tabs .strip-blocks > div:not(:last-child) {margin-bottom: 5px !important;}
  .wf-audit-content.wf-audit-content-multiple-tabs .strip-blocks-flex { display: flex; flex-direction: column; height: 100%; }
  .wf-audit-content.wf-audit-content-multiple-tabs .strip-blocks-flex > div { flex: 1;}

  .el-popover { box-shadow: 0 2px 12px 0 rgba(0,0,0, 0.5) !important; }
  /*#form-detail-tab-attach-bx{*/
  /*  .el-table .cell, .el-table--border .el-table__cell:first-child .cell {*/
  /*    padding-left: 5px;*/
  /*  }*/
  /*}*/
  /*#form-detail-tab-attach-sq{*/
  /*  .el-table .cell, .el-table--border .el-table__cell:first-child .cell {*/
  /*    padding-left: 5px;*/
  /*  }*/
  /*}*/
  #form-datail-info #attach-audit-extent {
    .main-border {
      padding: 2px 0px 0px 0px;
    }
    .buttons-normal .el-button--mini {
      margin-top: 2px;
    }
    /*.el-table .cell, .el-table--border .el-table__cell:first-child .cell {*/
    /*  padding-left: 5px;*/
    /*}*/
  }

  .el-table--medium .el-table__cell { padding: 4px 0 !important; }
  .hide-checkbox-if-disabled .el-checkbox.is-disabled { display: none !important; }

  .common-page a { cursor: pointer; }
  .common-page a:hover { text-decoration: underline; }

  .extend-file-pur-empty {
    height: 100%;
    width: 100%;
    background: #e9e9e9;
    border: 1px solid #bbb;
  }

  .formTabSaveFile .common-page.buttons-normal-no-padding-top .column-bottom,
  #dynamicDlg-global .formTabSaveFile .common-page.buttons-normal-no-padding-top .column-bottom {
    height: calc(100% - 40px) !important;
  }
  .cformTabSave .formRegularDetailOrAuditMode .el-input__prefix,
  .cformTabSave .formRegularDetailOrAuditMode .el-input__suffix-inner {
    display: none !important;
  }
  .cformTabSave .formRegularDetailOrAuditMode .formCommonCols
  .el-input--prefix .el-input__inner { padding-left: 5px !important; }

  .cformTabSave .formRegularDetailOrAuditMode .formCommonCols .el-checkbox-group label:not(.is-checked),
  .cformTabSave .formRegularDetailOrAuditMode .formCommonCols .el-radio-group label:not(.is-checked) {
    display: none !important;
  }
  .cformTabSave .formRegularDetailOrAuditMode .formCommonCols .el-radio-group { padding-left: 3px; }
  /*.cformTabSave .el-table--border .el-table__cell:first-child .cell { padding-left: 5px; }*/
  .wf-audit-content.wf-audit-content-multiple-tabs .cformTabSave .common-page .container-border .column-top { position: relative; }
  // .wf-audit-detail .cformTabSave { height: calc(100% - 23px); }
  .wf-audit-content.wf-audit-content-multiple-tabs .cformTabSave .el-tabs__content { height: calc(100% - 40px) !important; }
  .auditComp .el-form-item__label { font-size: 12px; }
  .common-page .el-loading-mask {
    background-color: rgba(255,255,255, 1);
  }
  #b-list-wf {
    .el-tabs__content > div {
      &[id="pane-储备库"],
      &[id="pane-立项库"] {
        .el-tabs {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }
      }
    }
  }

  .formCanvasRuntime-nopadding .formCanvasRuntime {
    padding: 0px!important;
  }
  .table-selection-pleft10.el-table-left .el-table__cell:first-child .cell {
    padding-left: 8px!important;
  }
  .makeMissingDlg .el-dialog__header { height: 50px;}
  .makeMissingDlg .el-dialog__body {height: 415px !important;overflow: hidden;}
  .makeMissingDlg .el-form .el-input,
  .makeMissingDlg .el-form .el-select,
  .makeMissingDlg .el-color-picker__icon,
  .makeMissingDlg .el-input,
  .makeMissingDlg .el-textarea {
    width: 99.5% !important;
  }
  .tabs-column { display: flex; flex-direction: column;}
  .tabs-column .el-tabs__content {
    flex: 1;
  }
  /** 穿透样式 */
  .frozenDetail .baFrozenDlg { display: flex; flex-direction: column; height: 100% }
  .frozenDetail .baFrozenDlg .el-tabs__content { flex: 1; }
  .frozenDetail .baFrozenDlg .el-tabs__content .el-tab-pane,
  .frozenDetail .baFrozenDlg .el-tabs__content .el-tab-pane .baFrozenTabPane,
  .frozenDetail .baFrozenTabPane,
  .frozenDetail .baFrozenDlg .el-tabs__content .el-tab-pane .baFrozenTabPane > div {
    height: 100%
  }
  .auditComp .el-form-item__label { font-size: 12px; }
  .common-page .el-loading-mask {
    background-color: rgba(255,255,255, 1);
  }

  // .contentBlock {border: 1px solid #DDDDDD;border-radius: 3px;transition: .2s;}
  // .contentBlock.hover{box-shadow:0 0 8px 0 rgba(232,237,250,.6),0 2px 4px 0 rgba(232,237,250,.5)}
  .contentBlock .contentBlockContent {padding: 10px;}

  .el-date-editor .el-range-input { width: 74px !important; }
  .el-range-editor--small .el-range-separator { width: 18px !important;padding: 0px; }
  .el-date-editor.el-input .el-input--prefix .el-input__inner { padding-left: 10px; }
  .el-date-editor.el-input .el-input--suffix .el-input__inner { padding-right: 10px; }
  .el-date-editor--daterange.el-input__inner { width: 202px !important; }
  .common-page .el-table--small.editing-table .el-table__cell { padding: 5px 0px !important; }
  .common-page .el-table--small.editing-table.editing-table-not-editing .el-table__cell { padding: 10px 0px !important; }
  .common-page .el-button.el-button--text { border: none !important;padding: 6px 6px !important; }
  .common-page .el-button.el-button--text:hover { color: #ff9800;border-color: transparent;background-color: transparent !important; }

  .common-page .el-button.el-button--text.is-disabled,
  .common-page .el-button.el-button--text.is-disabled:focus,
  .common-page .el-button.el-button--text.is-disabled:hover {
    // color: #C0C4CC;
    // cursor: not-allowed;
    // background-image: none;
    // background-color: transparent;
    // border-color: transparent;
  }
</style>
