<template>
  <div
    class="formCanvasRuntime common-page"
    :style="formCanvasRuntimeExStyle"
    v-if="openCanvas"
  >
    <div class="cformTabSave flex-1" v-if="isUsingCformTab">
      <form-tab-save ref="cformTabSave">
        <template #headExtendContent>
          <slot name="headExtendContent" />
        </template>
      </form-tab-save>
    </div>
    <div class="flex-1" style="height: 100%" v-else-if="isUsingCformBlock">
      <block-view ref="blockView" :saveBefore="saveBefore" />
    </div>
    <form-canvas
      class="flex-1"
      ref="formCanvas"
      v-else-if="!isUsingCformTab && !isMultipleEditTabs && !isUsingCformBlock"
    >
      <template #content>
        <slot name="content" />
      </template>
    </form-canvas>
    <div
      class="flex-1"
      style="display: flex; flex-direction: column; height: 100%"
      v-else-if="!isUsingCformTab && !isUsingCformBlock && isMultipleEditTabs"
    >
      <el-tabs
        v-model="formEditTabsActiveName"
        style="flex: 1; overflow: hidden"
        @tab-click="formEditTabClick"
        :class="formEditTabsClass"
        ref="formEditTabs"
      >
        <el-tab-pane
          v-for="tab in filterTabs"
          :key="tab.name"
          :ref="tab.ref"
          :class="tab.class"
          style="height: 100%; overflow: auto"
          :label="tab.label"
          :name="tab.name"
          :disabled="tab.disabled">
          <component
            :ref="tab.compRef"
            :is="loadDynamicComp(tab.comp)"
            v-on="tab.eventNameObj"
          />
        </el-tab-pane>
      </el-tabs>
      <el-footer>
        <el-button
          class="btn-normal"
          icon="el-icon-d-arrow-left"
          @click="doBtEditBack"
          >返回</el-button
        >
        <el-button
          ref="btDraft"
          class="btn-normal"
          icon="el-icon-tickets"
          @click="doBtEditDraft"
          v-show="isShowBtDraft"
          :disabled="draftBtnDisabled"
          >存草稿</el-button
        >
        <el-button
          class="btn-normal"
          type="primary"
          icon="el-icon-edit"
          :disabled="saveBtnLoading"
          @click="doBtEditSave"
          >保存</el-button
        >
      </el-footer>
    </div>
    <div v-if="showMainPanel || blockTabs.length" class="flex-row-center">
      <div class="retract-block" @click="handlerCollapse">
        <i
          class="el-icon-arrow-left"
          v-if="blockTabCollapsed"
          style="font-size: 13px; cursor: pointer"
        ></i>
        <i
          class="el-icon-arrow-right"
          v-else
          style="font-size: 13px; cursor: pointer"
        ></i>
      </div>
    </div>
    <div v-show="!blockTabCollapsed" class="right-block-tab">
      <blockTab
        ref="blockTabRef"
        style="width: 390px"
        :tabs.sync="blockTabs"
        :currentRow="currentRow"
        :showMainPanel.sync="showMainPanel"
        :formType="formType"
        :metaName="metaName"
        :metaId="metaId"
        :dataType="dataType"
        :nodeId="currentNodeId"
        :billId="billId"
        @initCallBack="initCallBack"
      >
      </blockTab>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'base-list-common-form',
  props: {
    metaId: { type: String, default: '' },
    showMainPanel: { type: Boolean, default: true },
    openCanvas: { type: Boolean, default: false },
    refFormParams: { type: Object, default: undefined },
    dataVo: { type: Object, default: () => ({}) },
    formEditTabsClass: { type: String, default: 'formEditTabs' },
    queryParams: { type: Object, default: () => ({}) },

    currentRow: { type: Object, default: () => ({}) },
    dataType: { type: String, default: '' },
    needProcess: { type: Boolean, default: false },
    isShowBtDraftOutside: { type: Boolean, default: false },
    formType: { type: String, default: '' },
    getFormCanvasExTabs: { type: Function, default: undefined },
    handleDataVoBeforeSave: { type: Function, default: undefined },
    jumpToSaveFormData: { type: Object, default: undefined },
    firstBtNewDropDown: { type: Object, default: undefined },
    customSaveAction: { type: Function, default: undefined },
    showHeaderFillMode: { type: Boolean, default: true },
    showRegularFormHeader: { type: Boolean, default: false },
    noTabWhenSaveForm: { type: Boolean, default: false },
    exHandleExParamsFormTabSave: { type: Function, default: undefined },
    stateItem: { type: String, default: '' },
    isRefFormNameSelect: { type: Boolean, default: false },
    isShowTabs: { type: Boolean, default: true },

    needPadding: { type: Boolean, default: true },
    displayTopMultitab: { type: Function, default: function() {} },
    goBackToBackPathIf: { type: Function, default: function() {} }
  },
  inject: {
    baseListCformIns: { default: undefined }
  },
  data() {
    return {
      blockTabs: [],
      billId: '',
      currentNodeId: '',
      metaName: '',
      isShowBtDraft: false,

      refFormSelectedData: [], // 制单初始参照选中的数据
      formCanvasMeta: undefined,
      isUsingCformTab: false, // 制单使用CformTab机制
      isUsingCformBlock: false, // 制单使用区块制单机制

      formEditTabs: [], // 如果是制单多tab模式，则formEditTabs[0]=主表单tab标题，formEditTabs[x]=其他tab组件名称
      formEditTabsIndexes: {}, // tabKey对应索引映射表
      formEditTabsMap: {},
      formEditTabsComponents: {}, // tabKey对应组件映射表
      formEditTabsActiveName: '', // 当前tab
      formRegularContainerPaddingTop: 0, // 制单多tab时规则表单顶部间距

      saveBtnLoading: true,
      draftBtnDisabled: true,
      blockTabCollapsed: true,
      tabs: []
    }
  },
  computed: {
    filterTabs() {
      return this.tabs.filter(tab => tab.show)
    },
    formEditTabsExcludeMain() {
      // 除去第一个表单后的其他的tab
      return this.tabs.slice(1) || []
    },
    isMultipleEditTabs() {
      if (this.noTabWhenSaveForm === true) {
        return false
      }
      return this.formEditTabs !== undefined && this.formEditTabs.length > 1
    },
    formCanvasRuntimeExStyle() {
      let exStyle = 'height: 100%;'
      if (this.needPadding) {
        exStyle = exStyle + 'padding: 12px 20px 20px 20px;'
      }
      return exStyle
    }
  },
  mounted() {
    window.$event.$on('listenerTimeout', this.listenerTimeout)
  },
  beforeDestroy() {
    window.$event.$off('listenerTimeout', this.listenerTimeout)
  },
  methods: {
    listenerTimeout() {
      console.log('触发超时事件listenerTimeout')
      this.autoSaveDraft()
    },
    removeFormFormatEventListeners() {
      if (this.$refs.formCanvas) {
        this.$tabsGetRef(this.$refs.formCanvas)?.$refs?.formFormat?.removeEventListeners?.()
      }
    },
    changeBtnStatus(boolean) {
      if (this.openCanvas) {
        this.saveBtnLoading = boolean
        this.draftBtnDisabled = boolean
      }
    },
    initCallBack(data) {
      if (this.$isNotEmpty(data.auditFiles)) {
        this.blockTabs = [{
          label: '规章制度',
          name: '规章制度',
          component: 'ruleBlock'
        }]
      } else {
        this.blockTabs = []
      }
    },
    changeTabDisabled(disabled = true) {
      this.filterTabs?.forEach(tab => {
        tab.disabled = disabled
      })
    },
    initTabsMeta(initParams) { // 初始tab数据
      this.tabs = []
      this.formEditTabs = initParams.formEditTabs || []
      this.formEditTabsIndexes = {}
      this.formEditTabsMap = {}

      // 是否为多重选项编辑卡
      if (this.isMultipleEditTabs) {
        for (let i = 0; i < this.formEditTabs.length; i++) {
          let name = this.formEditTabs[i]
          let label = name
          if (name.indexOf(':') > -1) {
            const tabTokens = name.split(':')
            name = tabTokens[0]
            label = tabTokens[1]
          }
          const formEditTabFlag = 'formEditTab' + i
          const compRef = i ? 'formEditTabComponent' + i : 'formCanvas'
          const comp = i ? name : 'form-canvas'
          const tab = {
            key: name,
            ref: formEditTabFlag,
            class: formEditTabFlag,
            label,
            name,
            compRef,
            comp,
            disabled: true,
            show: false
          }
          if (!i) {
            tab.eventNameObj = { 'changeBtnStatus': this.changeBtnStatus }
          }
          this.tabs.push(tab)
          this.formEditTabsIndexes[name] = i
          this.formEditTabsMap[name] = { ...tab }
        }
      }
    },
    formEditTabClick(tab) {
      // 防止多tab制单切换页签时，自由表单界面位置大小出现错乱
      if (this.tabs?.[0]?.name === tab.name &&
          this.$refs.formCanvas &&
          this.$tabsGetRef(this.$refs.formCanvas).isFreedomForm) {
        this.$tabsGetRef(this.$refs.formCanvas).$refs.formFormat.resizeClient()
      }

      const getDataToSave = () => {
        return new Promise((resolve, reject) => {
          try {
            const data = this.$tabsGetRef(this.$refs.formCanvas)?.getDataToSave()
            resolve(data)
          } catch (e) {
            reject(e)
          }
        })
      }

      getDataToSave().then(data => {
        var comp = this.formEditTabsComponents[tab.name]
        comp?.showAfter?.(data)

        var tabsClass = 'formEditTabs formEditTabsCard'
        var index = this.formEditTabsIndexes[tab.name]
        if (index === 0 || (comp && comp.isFormEditTabsCard === false)) {
          tabsClass = 'formEditTabs'
        }
        if (this.showRegularFormHeader === true) {
          tabsClass += ' showRegularFormHeader'
        }

        this.$emit('setBaseListCformData', 'formEditTabsClass', tabsClass)
      }).catch(e => {
        console.log(e)
      })
    },
    doBtEditBack(needConfirm = true, callback) {
      const back = () => {
        callback?.()
        this.$emit('setBaseListCformData', 'openCanvas', false)
        this.saveBtnLoading = true
        this.draftBtnDisabled = true
        if (this.$refs.formCanvas && this.$tabsGetRef(this.$refs.formCanvas).isFreedomForm && window.icLuckysheet) {
          window.icLuckysheet.destroy()
        }
        this.tabs.forEach((tab, index) => {
          this.setEditTabNameLabelIndex(index, tab.name, this.formEditTabsMap[tab.name].label)
        })
        const formCanvasComp = this.$findRefInner(this, 'formCanvas')
        formCanvasComp && this.$tabsGetRef(formCanvasComp).stopCalculateFormulaValues?.()
        window.icLuckysheet && window.icLuckysheet.exitEditMode()
        this.displayTopMultitab(true)
        this.goBackToBackPathIf?.()
      }
      if (needConfirm) {
        this.$confirm('确认返回吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          back()
        }).catch(() => {

        })
      } else {
        back()
      }
    },
    setBtDraftLoading(isLoading) {
      if (this.$refs.btDraft) {
        this.$refs.btDraft.loading = isLoading
      }

      if (this.$refs.formCanvas) {
        this.$tabsGetRef(this.$refs.formCanvas).setBtDraftLoading(isLoading)
      }

      if (this.$refs.cformTabSave) {
        this.$refs.cformTabSave.setBtDraftLoading(isLoading)
      }
    },
    async doBtEditDraft(e, dataVo, exData = {}) {
      const isAutoSaveDraft = exData.isAutoSaveDraft
      if (isAutoSaveDraft) {
        this.$message.warning('由于太长时间未操作，自动执行存草稿动作！')
      }
      var callbackSuccess = (result) => {
        this.$refreshCount(this)
        this.setBtDraftLoading(false)
        this.displayTopMultitab(true)
        this.saveBtnLoading = true
        this.draftBtnDisabled = true
        if (!this.needProcess) {
          this.$emit('setBaseListCformData', 'openCanvas', false)
        }

        // 保存返回列表后高亮显示对应的行
        var extData = {}
        if (this.$isNotEmpty(result.attributes)) {
          extData.highlightRowId = result.attributes.id
        }
        this.$reInit(this.baseListCformIns?.$parent, extData)
      }

      if (!this.isUsingCformTab) {
        // 避免dataVo为undefined导致fillDataVoBeforeSave中报错
        if (!dataVo) {
          await new Promise(resolve => {
            dataVo = this.$tabsGetRef(this.$refs.formCanvas)?.getDataToSave(resolve)
          })
        }
        dataVo.extData = dataVo.extData || {}
        var ret = this.doActionFromEditComponents((comp) => {
          // 多tab制单的情形
          if (comp.fillDataVoBeforeSave) {
            return comp.fillDataVoBeforeSave(dataVo)
          }
        })
        if (ret === true) {
          // 终止保存动作
          return
        }

        // TODO:临时处理数值计算公式 保存时 在校验一次公式
        this.$tabsGetRef(this.$refs.formCanvas)?.thCalculateFormulaValues()
      }

      var callbackFailed = (result) => {
        this.setBtDraftLoading(false)
      }
      this.setBtDraftLoading(true)
      var apiKey = 'WFDRAFT&dataType=CformDataEntity'
      dataVo = await this.wrapDataVoBeforeDraftOrSave(dataVo)
      this.beforeSaveCallback(dataVo, {}, {
        isDraft: true
      })
      this.$callApi(apiKey, dataVo, callbackSuccess, callbackFailed)
    },
    doSave(params, callback, callbackFailed,
      message, callbackBeforeCallApi, extra, customConfirmSetting) {
      // 执行流程保存动作
      message = message || '确定执行保存吗?'
      var apiKey = 'WFSAVE&dataType=' + this.dataType
      // 补录附件单独一个接口
      if (params.extData?.isBackAttach) {
        apiKey = 'supplementAttachments'
      }
      if (customConfirmSetting) {
        customConfirmSetting.message =
          customConfirmSetting.message || message
        this.$callApiConfirmCustom(
          customConfirmSetting, apiKey, params, callback, callbackFailed, extra)
      } else {
        this.$callApiConfirm(
          message, callbackBeforeCallApi, apiKey,
          params, callback, callbackFailed, extra)
      }
    },
    autoSaveDraft() {
      if (!this.openCanvas) return
      // this.isShowBtDraftOutside
      if (this.isUsingCformTab) {
        this.$refs.cformTabSave.doBtEditDraft(void 0, { isAutoSaveDraft: true })
      } else if (this.isUsingCformBlock) {
        this.$refs.blockView.doBtEditDraft({ isAutoSaveDraft: true })
      } else if (!this.isMultipleEditTabs) {
        this.$call(this.$tabsGetRef(this.$refs.formCanvas), 'formEditDetail', 'btEditDraft', { isAutoSaveDraft: true })
      } else {
        this.isShowBtDraft && !this.draftBtnDisabled && this.doBtEditDraft(void 0, void 0, { isAutoSaveDraft: true })
      }
    },
    beforeSaveCallback(data, confirmInstance = {}, exData = {}) {
      const setBtLoading = exData.isDraft ? this.setBtDraftLoading : (loading = false) => {
        this.saveBtnLoading = loading
        this.draftBtnDisabled = loading
        if (confirmInstance?.instance && !loading) {
          confirmInstance.instance.confirmButtonLoading = false
          confirmInstance.instance.confirmButtonText = '确定'
        }
      }
      if (this.isUsingCformTab) {
        this.$refs.cformTabSave.beforeSaveCallback(data, { confirmInstance, setBtLoading })
      } else if (!this.isUsingCformTab && !this.isUsingCformBlock) {
        this.$tabsGetRef(this.$refs.formCanvas)?.beforeSaveCallback?.(
          data,
          { confirmInstance, setBtLoading }
        )
      }
    },
    async doBtEditSave(e, dataVo, exData) {
      dataVo = await this.wrapDataVoBeforeDraftOrSave(dataVo)
      this.doSaveAction(dataVo, this.doSave, exData)
    },
    saveBefore(dataVo = {}, blockView = {}) {
      if (this.$isNotEmpty(this.jumpToSaveFormData)) {
        dataVo.extData['指引配置'] = this.jumpToSaveFormData.sourceType
      }
      // 补录附件时要传，否则更改后不能撤回
      if (dataVo.extData?.isBackAttach) {
        dataVo.extData.cleanWithdrawNode = false
        const attList = dataVo.attList || []
        const moneyProps = ['invoiceMoney', 'expenseMoney', 'invoiceTax', 'excludingTaxAmount']
        attList.forEach(row => {
          if (row.attType === '1') {
            moneyProps.forEach(prop => {
              row[prop] = this.$fixMoney(row[prop])
            })
          }
        })
      }
      dataVo.colItems.forEach(item => {
        if (item.labelOrigin === '部门') {
          let dataValue = item.dataValue.split(' ')[0]
          const suffix = item.dataValue.split('-')[1]
          if (suffix) {
            dataValue += ` ${suffix}`
          } else {
            dataValue = item.dataValue
          }
          item.dataValue = dataValue
        }
      })
      const thisInitParams = this.$getInitParams(this.baseListCformIns)
      thisInitParams.saveBefore?.(dataVo, blockView)
    },
    async wrapDataVoBeforeDraftOrSave(dataVo) {
      window.icLuckysheet?.exitEditMode()
      if (!dataVo) {
        await new Promise(resolve => {
          dataVo = this.$tabsGetRef(this.$refs.formCanvas)?.getDataToSave(resolve)
        })
      }
      dataVo.extData = dataVo.extData || {}
      // 多tab制单的情形
      var ret = this.doActionFromEditComponents((comp) => {
        if (comp.fillDataVoBeforeSave) {
          return comp.fillDataVoBeforeSave(dataVo)
        }
      })
      if (ret === true) { // 终止保存动作
        return
      }

      this.handleDataVoBeforeSave?.(dataVo, this.dataVo, this.getFormCanvasTabComponent, this)
      this.saveBefore(dataVo)
      return dataVo
    },
    setEditTabVisible(tabKey, isVisible) {
      // 多tab制单时设置页签的可见性
      // 注意：第一个页签的tabKey与标题相同，除第一个之外，tabKey=页签的组件name
      const index = this.formEditTabsIndexes[tabKey]
      const ret = this.setEditTabVisibleIndex(index, isVisible)

      // 表明隐藏后再次显示，整个页签会重新挂接，此时其保护的组件需要重新执行初始化
      if (ret === true && isVisible === true) {
        this.$nextTick(() => {
          const comp = this.$tabsGetRef(this.$refs[this.tabs[index].compRef])

          if (comp) {
            this.formEditTabsComponents[tabKey] = comp
            comp.initDataVo?.(this.baseListCformIns, this.dataVo, this.$tabsGetRef(this.$refs.formCanvas))
          }
        })
      }
    },
    setEditTabVisibleIndex(index, isVisible) {
      // 多tab制单时设置页签的可见性
      if (this.$isNotEmpty(index)) {
        if (index === 0) {
          this.$message.error('不能隐藏主表单页签')
          return
        }

        if (this.tabs[index].show !== isVisible) {
          this.tabs[index].show = isVisible
          return true
        }
      }
    },
    setEditTabNameLabelIndex(index, name, label) {
      // 多tab制单时设置页签的name和label
      if (this.$isNotEmpty(index)) {
        this.tabs[index].name = name
        this.tabs[index].label = label
      }
    },
    setEditTabLabel(name, label) {
      for (let i = 0; i < this.tabs.length; i++) {
        const tab = this.tabs[i]
        if (tab.name === name) {
          tab.label = label
          break
        }
      }
    },
    switchEditTab(tabKey) {
      // 多tab制单时切换不同页签
      // 注意：第一个页签的tabKey与标题相同，除第一个之外，tabKey=页签的组件name
      this.formEditTabsActiveName = tabKey
    },
    addFormRef() {
      // 进入新增界面时有表单参照，即使this.btAddNoRef = true
      this.openFormCanvas(
        this.firstBtNewDropDown.metaVersionId,
        '',
        undefined,
        false
      )
    },
    initCformTabParamsBy(row) {
      var exInitData = {}
      exInitData.versionId = row.metaVersionId
      exInitData.dataId = row.ID
      exInitData.metaIdTab = row.metaIdTab
      exInitData.viewId = row.viewId
      exInitData.currentNodeId = row.currentNodeId
      exInitData.stateItem = this.stateItem
      exInitData.metaName = row.metaName
      exInitData.isTemplate = row.isTemplate
      exInitData.status = row.status
      exInitData.showFormTabs = row?.showFormTabs ? row?.showFormTabs?.split(',') : []
      this.$emit('setBaseListCformData', 'metaId', row.metaId)
      return exInitData
    },
    initBlockView(exInitData) {
      const callbackSaveSuccess = (result) => {
        if (!this.needProcess) {
          this.$emit('setBaseListCformData', 'openCanvas', false)
        }

        // 保存返回列表后高亮显示对应的行
        const extData = {}
        if (this.$isNotEmpty(result.attributes)) {
          extData.highlightRowId = result.attributes.id
        }

        if (this.goBackToBackPathIf?.(result)) {
          return true
        }
        this.$reInit(this.baseListCformIns?.$parent, extData)
      }

      exInitData = exInitData || {}

      if (this.customSaveAction) {
        // 自定义调用保存动作
        exInitData.customSaveAction = this.customSaveAction
      }

      var params = {
        needProcess: this.needProcess,
        isEdit: true,
        mode: '制单',
        callbackSaveSuccess: callbackSaveSuccess,
        doBtEditBack: this.doBtEditBack,
        jumpToSaveFormData: this.jumpToSaveFormData,
        refFormParams: this.refFormParams, // 设置表单配置
        isShowTabs: this.isShowTabs // 是否展示tabs
      }
      Object.assign(params, exInitData)
      const thisInitParams = this.$getInitParams(this.baseListCformIns) || {}
      const blockParams = thisInitParams.blockParams || {}
      Object.assign(params, blockParams)
      this.$refs.blockView.init(undefined, params)
    },
    initCformTabToEdit(exInitData) { // 使用CformTab机制进行制单
      const metaIdTab = exInitData.metaIdTab
      const exParamsFormTabSave = {
        btEditBack: this.doBtEditBack,
        btEditDraft: this.doBtEditDraft,
        btEditSave: this.doBtEditSave,
        callAfterFormLoaded: (dataVo, formCanvasMeta, cformObj) => {
          exInitData?.callAfterFormLoaded?.(dataVo, formCanvasMeta, cformObj)
          this.addCallbacks(dataVo)
        }
      }
      Object.assign(exParamsFormTabSave, exInitData)
      this.exHandleExParamsFormTabSave?.(exParamsFormTabSave)
      const cbBeforeApi = () => {}
      const cbFailed = () => {}
      const cbSuccess = (result, tabVo, formTabSave) => {}

      this.$refs.cformTabSave.isEdit = true
      this.$refs.cformTabSave.refreshTabs(
        metaIdTab,
        cbBeforeApi,
        cbSuccess,
        cbFailed,
        exParamsFormTabSave
      )
    },
    openFormCanvas(versionId, dataId, callback, noRefForm, exInitData = {}) {
      // 默认收起
      this.blockTabCollapsed = true
      this.blockTabs = []
      this.billId = dataId
      this.metaName = exInitData.metaName
      this.currentNodeId = exInitData.currentNodeId
      this.$emit('setBaseListCformData', 'showMainPanel', !!(exInitData.hasOwnProperty('status') && exInitData.status !== '草稿' && this.currentNodeId))

      this.$emit('setBaseListCformData', 'openCanvas', true)

      this.$nextTick(() => {
        // 多tab制单处理
        this.formEditTabsComponents = {}
        exInitData = exInitData || {}
        this.isUsingCformTab = this.$isNotEmpty(exInitData.metaIdTab)
        this.isUsingCformBlock = this.$isNotEmpty(exInitData.viewId)
        if (this.noTabWhenSaveForm === true) {
          this.isUsingCformTab = false
        }

        if (this.isUsingCformBlock) {
          this.$nextTick(() => {
            this.initBlockView(exInitData)
          })
        } else if (this.isUsingCformTab) {
          this.$nextTick(() => {
            this.initCformTabToEdit(exInitData)
          })
        } else if (this.isMultipleEditTabs) {
          this.formEditTabsActiveName = this.formEditTabs[0]
          this.tabs[0].show = true

          for (var i = 0; i < this.formEditTabsExcludeMain.length; i++) {
            const index = i + 1
            this.setEditTabVisibleIndex(index, true)
            const tabName = this.formEditTabsExcludeMain[i].name
            this.tabs[index].comp = tabName
          }

          this.$nextTick(() => {
            this.formEditTabsComponents[this.formEditTabs[0]] = this.$tabsGetRef(this.$refs.formCanvas)
            if (this.tabs[0]) {
              this.setEditTabNameLabelIndex(0, this.tabs[0].name, this.tabs[0].label)
            }
            for (let i = 0; i < this.formEditTabsExcludeMain.length; i++) {
              const index = i + 1
              const tabName = this.formEditTabsExcludeMain[i].name
              const tabLabel = this.formEditTabsExcludeMain[i].label

              const component = this.$refs['formEditTabComponent' + index]
              if (component) {
                this.formEditTabsComponents[tabName] = this.$tabsGetRef(component)
                this.formEditTabsComponents[tabLabel] = this.$tabsGetRef(component)
              }
              this.setEditTabNameLabelIndex(index, tabName, tabLabel)
            }
            this.initCanvasAfterTabInit(
              versionId,
              dataId,
              callback,
              noRefForm,
              exInitData
            )
          })
        } else {
          this.initCanvasAfterTabInit(
            versionId,
            dataId,
            callback,
            noRefForm,
            exInitData
          )
        }
      })
    },
    showOrHideBtDraft(dataVo) {
      // 已正式保存过的表单，没有存草稿功能
      // 跳转到制单页面时，没有存草稿功能

      // 过渡阶段，使用页面指定开放存草稿功能的模块

      var shouldShow = this.isShowBtDraftOutside
      if ((this.$isNotEmpty(dataVo.data.id) && dataVo.data.status !== '草稿') ||
        this.$isNotEmpty(this.jumpToSaveFormData) && this.jumpToSaveFormData.isShowBtDraft !== true) {
        shouldShow = false
      }
      this.isShowBtDraft = shouldShow
      if (this.$refs.formCanvas) {
        this.$tabsGetRef(this.$refs.formCanvas).showBtDraft(shouldShow)
      }
    },
    initCanvasAfterTabInit(versionId, dataId, callback, noRefForm, exInitData) {
      this.$nextTick(() => {
        if (!this.isMultipleEditTabs) {
          this.$tabsGetRef(this.$refs.formCanvas)?.setExtDataAssembly(this.formType)
        }

        // 设置表单画布额外TAB组件
        var callbackInitCanvas = (meta, dataVo) => {
          this.formCanvasMeta = meta
          this.$emit('setBaseListCformData', 'dataVo', JSON.parse(JSON.stringify(dataVo)))
          this.getFormCanvasExTabs?.(dataVo)
        }

        exInitData = exInitData || {}
        var thisInitParams = this.$getInitParams(this.baseListCformIns)
        var initFormExData = thisInitParams.initFormExData || {}
        initFormExData = Object.assign(initFormExData, exInitData)
        initFormExData.baseListFormObj = this.baseListCformIns
        initFormExData.showHeader = this.showHeaderFillMode
        initFormExData.isMultipleEditTabs = this.isMultipleEditTabs
        initFormExData.stateItem = exInitData.stateItem
        // 多tab制单时，不显示formCanvas的编辑按钮，规则表单不显示标题
        if (this.isMultipleEditTabs) {
          initFormExData.showEditButtons = false
        }

        initFormExData.callbackBeforeFormLoaded = (dataVo) => {
          const jumpToSaveFormData = this.jumpToSaveFormData || {}
          const extData = jumpToSaveFormData.extData || {}
          jumpToSaveFormData.extData = {
            ...extData,
            isBackAttach: initFormExData.isBackAttach
          }
          this.$fillDataVoFromJumpToSaveFormData(dataVo, jumpToSaveFormData)
        }

        // 第二次调用initByVersionDataId返回的dataVo中包含数据
        // dataVo.extData.initRefDataVoFromRemoteData，这样能避免无限调用的情况
        initFormExData.callbackAfterFormLoaded = (dataVo) => {
          this.tabPaneDisabled = false
          this.changeTabDisabled(false)
          this.showOrHideBtDraft(dataVo)
          this.doActionFromEditComponents((comp) => {
            // 多tab制单的情形
            if (comp.initDataVo) {
              comp.initDataVo(this.baseListCformIns, this.dataVo, this.$tabsGetRef(this.$refs.formCanvas), initFormExData)
            }
          })

          // 这里给业务提供表单填充之后设置表单的机会
          // 应用场景如，采购跳转到合同制单生成合同，需要设置采购的值
          if (this.$isEmpty(dataVo.extData.initRefDataVoFromRemoteData)) {
            dataVo.extData = dataVo.extData || {}
            const callbackAfterFormLoadedInExtData = dataVo.extData.callbackAfterFormLoaded
            callbackAfterFormLoadedInExtData?.(dataVo, this.$tabsGetRef(this.$refs.formCanvas).$refs.formFormat)
          }

          if (noRefForm === false) {
            // 制单表单参照的处理
            if (this.$isNotEmpty(this.refFormParams) &&
                dataVo &&
                this.$isEmpty(dataVo.extData.initRefDataVoFromRemoteData) &&
                this.$tabsGetRef(this.$refs.formCanvas)?.$refs.formFormat.refAfterInitWhenFillForm) {
              // 表单参照是否使用后端DataVo重新初始化制单界面
              var initRefDataVoFromRemoteData = this.$isEmpty(
                this.refFormParams.initRefDataVoFromRemoteData
              )
                ? false
                : this.refFormParams.initRefDataVoFromRemoteData

              var paramsRef = {
                isRelatedRefIncludeDept: true,
                noRefAction: initRefDataVoFromRemoteData, // 表单参照不执行参照填充
                onDlgClose: (params) => {
                  // 如果表单参照不进行参照，则直接退出制单界面返回到列表
                  if ((params.closeWhetherToReturn || false) && params.isOK !== true) {
                    this.doBtEditBack(false)
                  }
                },
                exHandleSelectedData: (selectedData) => {
                  this.refFormSelectedData = selectedData

                  // 使用参照的表单重新初始化制单界面，以获得联动和右侧扩展
                  if (
                    initRefDataVoFromRemoteData &&
                    this.$isNotEmpty(selectedData?.list)
                  ) {
                    var refFormId = selectedData.list[0].ID

                    // 这个方法会导致再次调用callbackAfterFormLoaded，但此时
                    // dataVo.data.id不为空，所以不会引发无限循环
                    // 参数initRefDataVoFromRemoteData触发后端对dataVo清除id编码等要素
                    // refSourceType表单参照来源类型 后端可通过该参数过滤不需要的数据
                    var selectCformVoQueryParams = {
                      initRefDataVoFromRemoteData: true,
                      refSourceType: this.refFormParams.refSourceType
                    }
                    this.$tabsGetRef(this.$refs.formCanvas)?.initByVersionDataId(
                      '',
                      refFormId,
                      '制单',
                      callbackInitCanvas,
                      undefined,
                      true,
                      this.getFormCanvasExTabs,
                      initFormExData,
                      selectCformVoQueryParams
                    )
                  }
                }
              }

              if (this.isRefFormNameSelect) {
                this.$emit('setBaseListCformData', 'refFormParams', Object.assign(this.refFormParams, { NAME_eq: dataVo.meta.name }))
              }
              paramsRef = Object.assign(paramsRef, this.refFormParams)
              this.$tabsGetRef(this.$refs.formCanvas)?.$refs.formFormat.refAfterInitWhenFillForm(
                paramsRef
              )
            }
          }

          // 自由表单多tab制单时，调整制单区域边界的间距
          var $formEditTabs = $('.formEditTabs')
          $formEditTabs.removeClass('isMultipleEditTabs')
          if (this.isMultipleEditTabs) {
            var $tabsContent = $formEditTabs.find('.el-tabs__content')
            $formEditTabs.addClass('isMultipleEditTabs')
            $tabsContent.find('.formEditTab0.formCanvasRuntimeHasAssemblyFree')
              .find('#formFreeView')
              .css('padding', '0px 1px 0px 0px')
            $formEditTabs.find('.formFreeViewRightContent')
              .css('cssText', 'margin-left: -9px;padding: 0px 0px 0px 0px;')
          }
          this.addCallbacks(dataVo)
        }

        var formIdFromJump = this.getDataIdFromJumpToSaveFormData()
        if (this.$isNotEmpty(formIdFromJump)) {
          dataId = formIdFromJump
        }
        var extData = this.getExtDataFromJumpToSaveFormData()
        if (this.$isNotEmpty(extData)) {
          const queryParams = Object.assign(this.queryParams, { extData })
          this.$emit('setBaseListCformData', 'queryParams', queryParams)
        }

        if (this.$isNotEmpty(this.jumpToSaveFormData?.selectCformVoParams)) {
          Object.assign(this.queryParams, this.jumpToSaveFormData.selectCformVoParams)
        }
        const mode = initFormExData.isBackAttach ? '详情' : '制单'
        this.$tabsGetRef(this.$refs.formCanvas)?.initByVersionDataId(
          versionId,
          dataId,
          mode,
          callbackInitCanvas,
          undefined,
          true,
          this.getFormCanvasExTabs,
          initFormExData,
          this.queryParams
        )

        if (this.isMultipleEditTabs) {
          var $tabsContent = $('.formEditTabs').find('.el-tabs__content')
          $tabsContent.css('height', 'calc(100% - 51px)')
          $tabsContent.find('.formEditTab0').css('height', '100%')
          $tabsContent
            .find('main.el-main')
            .css('cssText', 'padding: 0px !important')
          $tabsContent.find('.formRegularContainer').css('padding', '0px')
          $tabsContent
            .find('.formRegularContainer')
            .css('padding-top', this.formRegularContainerPaddingTop + 'px')

          $('.formEditTabs')
            .parent()
            .find('.el-footer')
            .css('height', '44px')
            .css('padding', '12px 0px 0px 0px')
            .css('text-align', 'right')
        }
      })
    },
    doActionFromEditComponents(action) {
      for (var i = 0; i < this.formEditTabsExcludeMain.length; i++) {
        var tabName = this.formEditTabsExcludeMain[i].name
        var component = this.formEditTabsComponents[tabName]
        if (component) {
          var ret = action(component)
          if (ret === true) {
            // 可以用于表明终止保存操作
            return true
          }
        }
      }
    },
    getFormCanvasTabComponent(tabName) {
      return this.$call(
        this,
        'formFormat',
        'getFormCanvasTabComponent',
        tabName
      )
    },
    doSaveAction(dataVo, doSaveHandler, exData = {}) {
      // 保存和保存并送审的统一方法
      var callbackSuccess = (result) => {
        this.$refreshCount(this.baseListCformIns)
        this.saveBtnLoading = false
        this.draftBtnDisabled = false
        this.$emit('setBaseListCformData', 'openCanvas', false)

        // 保存返回列表后高亮显示对应的行
        var extData = {}
        if (this.$isNotEmpty(result.attributes)) {
          extData.highlightRowId = result.attributes.id
        }
        this.saveBtnLoading = true
        this.draftBtnDisabled = true

        if (this.goBackToBackPathIf(result)) {
          return true
        }
        this.$reInit(this.baseListCformIns?.$parent, extData)

        // 单位项目管理新增的时候,取消显示顶部的多标签内容
        this.displayTopMultitab(true)

        // 保存成功后上传pdf
        this.$showPrint(
          {
            printId: result.attributes.id,
            isSavePdf: true
          }, '', { type: 'PDF' })
        const noTip = exData.callbackSuccess?.(result)
        if (noTip) {
          return true
        }
      }
      var showTabErrorTip = (tabName, errorCount) => {
        if (this.isMultipleEditTabs) {
          if (this.$isNumber(errorCount)) {
            var tabIndex = this.formEditTabsIndexes[tabName]
            var labelNoError = this.formEditTabsMap[tabName].label
            var label =
              parseInt(errorCount) === 0
                ? labelNoError
                : `${labelNoError}(有${errorCount}个错误)`
            this.setEditTabNameLabelIndex(tabIndex, tabName, label)
            if (errorCount > 0) {
              $('#tab-' + tabName).addClass('formEditTabsError')
              this.switchEditTab(tabName)
            }
          }
        }
      }

      var callbackFailed = (result) => {
        this.saveBtnLoading = false
        this.draftBtnDisabled = false
        if (this.isUsingCformTab) {
          return this.$refs.cformTabSave.showError(result)
        }
        // 校验错误个数显示在tab上的顺序，按主表单 -> 第2个tab ...依次处理，
        // 当前有一个tab有错误，则不再处理后面有错误的tab的错误显示
        var errorHitContainer = {}
        this.$tabsGetRef(this.$refs.formCanvas)?.showError(result, errorCount, errorHitContainer)
        var errorCount = errorHitContainer['hitCountInt']
        if (this.$isNumber(errorCount) && parseInt(errorCount) > 0) {
          // 主表单有校验错误
          showTabErrorTip(this.formEditTabs[0], errorCount)
        } else if (result?.attributes) {
          var formEditTabErrors = result.attributes.formEditTabErrors
          if (this.$isNotEmpty(formEditTabErrors)) {
            // 表明错误发生在非主表单的其他tab
            var firstErrorTabName
            for (let i = 0; i < this.formEditTabsExcludeMain.length; i++) {
              const tabName = this.formEditTabsExcludeMain[i].name
              if (this.$isNotEmpty(formEditTabErrors[tabName])) {
                if (!firstErrorTabName) {
                  firstErrorTabName = tabName
                }

                errorCount = parseInt(formEditTabErrors[tabName])
                showTabErrorTip(tabName, errorCount)
                const comp = this.formEditTabsComponents[tabName]
                comp?.showError?.(result)
              }
            }

            if (firstErrorTabName) {
              this.switchEditTab(firstErrorTabName, true)
            }
          }
        }
        return parseInt(errorCount) > 0
      }

      dataVo.extData = dataVo.extData || {}
      var saveAction = (noConfirm, extra) => {
        var message = noConfirm === true ? '#不需要确认#' : undefined
        doSaveHandler(
          dataVo,
          callbackSuccess,
          callbackFailed,
          message,
          (data, confirmInstance) => {
            this.saveBtnLoading = true
            this.draftBtnDisabled = true
            this.beforeSaveCallback(data, confirmInstance)
          },
          extra,
          dataVo.extData['customConfirmSetting']
        )
      }

      // 先清空之前可能存在的tab错误
      if (this.isMultipleEditTabs) {
        const replaceFormEditTabsClass = this.formEditTabsClass.replace(' formEditTabsError', '')
        this.$emit('setBaseListCformData', 'formEditTabsClass', replaceFormEditTabsClass)
        showTabErrorTip(this.formEditTabs[0], 0)
        for (let i = 0; i < this.formEditTabsExcludeMain.length; i++) {
          showTabErrorTip(this.formEditTabsExcludeMain[i].name, 0)
        }
      }
      if (this.customSaveAction) {
        // 自定义调用保存动作
        this.customSaveAction(saveAction, dataVo, this.refFormSelectedData)
      } else {
        saveAction()
      }
    },
    getDataIdFromJumpToSaveFormData() { // 跳转过来是可传递formId来表明直接修改的表单
      if (this.$isNotEmpty(this.jumpToSaveFormData)) {
        return this.jumpToSaveFormData.formId
      }
    },
    getExtDataFromJumpToSaveFormData() { // 跳转过来是可传递formId来表明直接修改的表单
      if (this.$isNotEmpty(this.jumpToSaveFormData)) {
        return this.jumpToSaveFormData.extData
      }
    },
    addCallbacks(dataVo) { // formColItemChange回调单独针对于blockTabRef
      const callbacks = {
        'formColItemChange': (theColItem, selects) => {
          this.$refs.blockTabRef?.formColItemChange(theColItem, selects, dataVo)
        }
      }
      if (this.isUsingCformTab) {
        this.$nextTick(() => {
          this.$call(this, 'formCanvas', 'addColItemModifiedCallbacks', callbacks)
        })
        return
      }
      this.$tabsGetRef(this.$refs.formCanvas)?.addColItemModifiedCallbacks(callbacks)
    },
    handlerCollapse() {
      this.blockTabCollapsed = !this.blockTabCollapsed
      if (!this.blockTabCollapsed) {
        this.$refs.blockTabRef?.setBarWidth()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.formCanvasRuntime {
  display: flex;
  overflow: hidden;
  .right-block-tab {
    overflow: hidden;
    height: 100%;
    ::v-deep .el-tabs__header.is-top {
      margin-bottom: 20px;
    }
  }
}
</style>
