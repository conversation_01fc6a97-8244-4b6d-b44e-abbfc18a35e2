<template>
  <div id="billsInput">
    <el-form ref="form" :model="form" :label-width="labelWidth">
      <!-- <el-form-item :label="formLabel"> -->
        <el-input v-model="form.name" :size="formInputSize" :style="formInputStyle" clearable :placeholder="'请输入'+ formLabel"></el-input>
      <!-- </el-form-item> -->
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'billsinputaccept',
  props: {
    // label名
    formLabel: {
      type: String,
      default: '单据号'
    },
    // input样式
    formInputStyle: {
      type: String,
      default: 'width: 25%;'
    },
    // 表单域标签的宽度
    labelWidth: {
      type: String,
      default: '60px'
    },
    // 需要处理的值
    editValue: {
      type: String,
      default: ''
    },
    // 设置input输入框尺寸
    formInputSize: {
      type: String,
      default: 'mini'
    }
  },
  watch: {
    form: {
      deep: true,
      immediate: true,
      name: 'billsinputaccept',
      handler: function(Nval) {
        // 防止重复触发监听
        if (Nval.name === null || Nval.name === undefined || Nval.name === '') return
        if (this.$isNotEmpty(Nval.name) && Nval.name.length>10) {
          Nval.name = Nval.name.trim()
          const code = this.$clone(Nval.name)
          if (this.codeList.indexOf(code.split('#')[0]) === -1) {
            this.codeList.push(code.split('#')[0])
            this.codeList = [...new Set(this.codeList)]
            // 如果code.split('#')[1]为空时无需重新赋值  防止重复触发监听
            if (code.split('#')[1] !== undefined) {
              this.form.name = code.split('#')[0]
              this.billsId = code.split('#')[1]
            }
            window.$event.$emit('isShowBillsInputFun', this.form.name, this.billsId)
          } else {
            // 重置
            this.codeList = []
          }
        }
      }
    }
  },
  data() {
    return {
      form: {
        name: ''
      },
      billsId: '',
      codeList: []
    }
  },
  created() {
    window.$event.$off('cleanNvalName', null, 'componentB')
    window.$event.$on('cleanNvalName', () => {
      this.form.name = ''
      this.codeList = []
    }, 'componentB')
  },
  methods: {
    init() {
      if (this.$isNotEmpty(this.editValue)) {
        this.form.name = this.editValue.split('#')[0]
        this.billsId = this.editValue.split('#')[1]
      }
      // list体系表格样式处理
      const mainTable = document.querySelector('.column-bottom') // 获取主表dom元素
      mainTable.style = 'height: calc(100% - 95px) !important'
    }
  }
}
</script>
<style lang="scss" scoped>
#billsInput{
  padding: 20px 0  0px 0px;
  .el-form-item--small.el-form-item{
    margin-bottom: 0px !important;
  }
}
</style>
<style lang="scss">
.common-page .column-bottom {
    // height: calc(100% - 77px) !important;
}
#billsInput  .el-input__inner {
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: #999999;
    border-color: #fff;
    padding: 0 15px !important;
    cursor: text !important;
}
#billsInput .el-input__inner:focus {
    border-color: #1f7fff !important;
    outline: 0 !important;
}
</style>

