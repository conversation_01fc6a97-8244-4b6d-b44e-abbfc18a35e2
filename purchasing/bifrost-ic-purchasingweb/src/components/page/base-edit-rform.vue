<template>
  <div :style="`height: calc(100% - 60px)`">
    <div class="baseEditExtendClass"
         :style="`height: ${editRformExtendHeight}px`" v-if="editRformExtendName !==''">
      <component ref="editRformExtend" :is="editRformExtendName"/>
    </div>
    <div :style="`height: calc(100% - ${editRformExtendHeight}px - 20px)`">
      <b-curd ref="curdList" :isVxe="false"/>
    </div>
  </div>
</template>

<script>
import BCurd from './base-curd'
export default {
  name: 'base-edit-rform',
  components: { BCurd },
  data() {
    return {
      isEdit: true,
      editRformExtendHeight: -20,
      editRformExtendName: ''
    }
  },
  methods: {
    init(params) {
      this.isEdit = params.isEdit
      if (this.$isEmpty(params.title)) {
        params.title = '行表单编辑'
      }

      if (this.$isNotEmpty(params.editRformExtendName)) { // 顶部的扩展组件
        this.editRformExtendName = params.editRformExtendName
        this.$nextTick(() => {
          if (this.$refs.editRformExtend && params.editRformExtendInit) {
            params.editRformExtendInit(this.$refs.editRformExtend, this)
          }
        })
      }

      if (this.$isNotEmpty(params.editRformExtendHeight)) {
        this.editRformExtendHeight = params.editRformExtendHeight
      }

      params.apiKeySelectVo = 'selectRformData'
      params.apiKeySaveVo = 'saveRformDataVo'
      params.selectVoWhenInsert = true
      params.saveFailedCallback = (data) => {
        if (this.$isNotEmpty(data.attributes)) {
          this.$refs.curdList.showError(data.attributes)
        }
      }
      params.getSaveVo = (baseObj) => {
        return {
          pageData: JSON.stringify(baseObj.dataVo),
          exParams: this.$cloneParams(params)
        }
      }

      params.isShowOrderNumber = true
      params.initVoCallback = (baseObj) => {
        this.$refs.curdList.initEditRowForm(
          baseObj.dataVo, params.isEdit, undefined, params)
      }
    }
  }
}
</script>

<style lang="scss">
  .baseEditExtendClass {
    padding: 25px;
    margin-bottom: 20px;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    transition: .2s;
  }
</style>
