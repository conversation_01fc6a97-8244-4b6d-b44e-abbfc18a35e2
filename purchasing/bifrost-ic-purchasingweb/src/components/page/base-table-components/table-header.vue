<template>
  <span>
    <span v-if="isShowRequired" style="color: red">*</span>
    <span>{{column.label}}</span>
    <el-tooltip v-if="column.columnEntity && remindInfo[column.columnEntity.labelOrigin]" class="item" effect="light" :content="remindInfo[column.columnEntity.labelOrigin]" placement="top">
      <i style="color: rgba(255, 0, 0, .5);font-size: 14px;margin-left: 3px" class="el-icon-warning"></i>
    </el-tooltip>
  </span>
</template>

<script>
export default {
  name: 'base-table-header',
  props: {
    column: {
      type: Object,
      default: () => ({})
    },
    remindInfo: {
      type: Object,
      default: () => ({})
    },
    editColMap: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    isShowRequired() {
      return this.column.columnEntity?.isRequired &&
      (this.editColMap[this.column.prop] || this.column.editable) && this.column.editableType !== 'a'
    }
  },
  data() {
    return {

    }
  }
}
</script>

<style>

</style>
