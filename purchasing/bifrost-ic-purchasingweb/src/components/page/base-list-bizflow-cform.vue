<template>
  <div style="height: 100%">
    <b-curd ref="baseCurdList4BizFlow"/>
    <base-list-wf-submitform ref="bListWfSubmitForm" v-if="needProcess"/>
  </div>
</template>

<script>
import BCurd from './base-curd'
import BaseListWfSubmitForm from './base-list-wf-submitform'

export default {
  name: 'base-list-bizflow-cform',
  components: { BaseListWfSubmitForm, BCurd },
  data() {
    return {
      needProcess: false,
      pageData: undefined, // 列表的pageData
      subBizVersionId: undefined,
      subBizViewId: undefined // 子业务是表单时，这两个属性是对应的区块表单viewId
    }
  },
  mounted() {
    if (this.$parent && this.$parent.initParams) {
      const initParams = this.$parent.initParams()
      if (this.$isEmpty(initParams)) {
        this.$message.error('业务组件initParams方法返回空数据')
        return
      }
      this.init(initParams)
    }
  },
  methods: {
    init(initParams) {
      initParams = initParams || {}
      initParams.params = initParams.params || {}

      if (this.$isEmpty(initParams['业务流转节点'])) {
        this.$message.error('initParams[\'业务流转节点\']不能为空')
        return
      }

      // 如果没有设置dataApiKey，则表明当前是基于表单的业务进行流转
      const isCformBiz = this.$isEmpty(initParams.dataApiKey)
      const dataApiKey = isCformBiz ? 'wfActionCformDataEntity' : initParams.dataApiKey
      if (isCformBiz && this.$isEmpty(initParams.FORM_TYPE_eq)) {
        this.$message.error('表单类别initParams.FORM_TYPE_eq不能为空')
        return
      }

      // 总是有状态查询条件
      const subBizStatuses = initParams.业务流转状态列表 || []
      const subBizStatusesStr = subBizStatuses.join(',')
      const bizFlowStatusName = initParams.bizFlowStatusName || '状态'
      const bizFlowStatusWidth = initParams.bizFlowStatusWidth || '150px'
      const searchFormStyle = initParams.searchFormStyle || ''
      const searchFormStyleArray = searchFormStyle.split(',')
      searchFormStyleArray.push(bizFlowStatusName + '#width:' + bizFlowStatusWidth)
      initParams.searchFormStyle = searchFormStyleArray.join(',')

      const searchFormFinal = []
      searchFormFinal.push(bizFlowStatusName + ':业务流转状态:下拉:#' + subBizStatusesStr)
      initParams.searchForm = initParams.searchForm || []
      initParams.searchForm.forEach(item => searchFormFinal.push(item))
      initParams.searchForm = searchFormFinal
      // initParams.isShowDefaultVal = '状态:全部'

      initParams.params.actionKey = 'selectWfList'
      initParams.params.dataApiKey = dataApiKey
      initParams.params.dataType = 'CformDataEntity'
      initParams.params.FORM_TYPE_eq = initParams.FORM_TYPE_eq
      initParams.params.STATUS = '审核结束'
      initParams.params['查询所有用户的单据'] = initParams['查询所有用户的单据']
      initParams.params['业务流转节点'] = initParams['业务流转节点']

      if (this.$isNotEmpty(initParams['子业务表单类型'])) {
        initParams.params['子业务表单类型'] = initParams['子业务表单类型']
        initParams['子业务类型'] = 'CformDataEntity' // 子业务表单类型不为空，表明子业务是表单
      }
      if (this.$isNotEmpty(initParams['子业务类型'])) {
        initParams.params['子业务类型'] = initParams['子业务类型']
      }

      // 设置了这属性，撤销按钮在没有流转到下个节点是也可用
      if (initParams.撤销不要检查是否处理过业务) {
        initParams.params.撤销不要检查是否处理过业务 = initParams.撤销不要检查是否处理过业务
      }

      const buttons = initParams.buttons || []

      let btMakeSubBizCformText = initParams.btMakeSubBizCformText
      if (this.$isNotEmpty(initParams['子业务表单类型'])) {
        btMakeSubBizCformText = '生成' + initParams['子业务表单类型']
      }
      if (this.$isNotEmpty(btMakeSubBizCformText)) {
        // 跳转动作单独抽离出一个方法，是为了实现在子列表里点击修改按钮
        // 时跳转去表单制单页面进行修改
        const jumpToEditForm = (formId, bt) => {
          // 以下处理点击跳转到表单制单业务时，带给表单初始化的数据
          const isUpdate = this.$isNotEmpty(formId)
          const jumpToSaveData = {
            pathJumpTo: initParams.pathJumpTo,
            pathBackTo: initParams.pathBackTo,
            viewId: this.subBizViewId,
            formVersionId: this.subBizVersionId,
            isUpdate: isUpdate,
            formId: formId
          }

          let jumpToSaveExtData
          if (initParams.getJumpToSaveExtData) {
            jumpToSaveExtData = initParams.getJumpToSaveExtData(bt, isUpdate)
          }
          let jumpToSaveColItemValues
          if (initParams.getJumpToSaveColItemValues) {
            jumpToSaveColItemValues = initParams.getJumpToSaveColItemValues(bt, isUpdate)
          }
          jumpToSaveData.extData = jumpToSaveExtData
          jumpToSaveData.colItemValues = jumpToSaveColItemValues
          if (initParams.exHandleJumpToSaveData) { // 额外处理跳转制单参数
            initParams.exHandleJumpToSaveData(bt, jumpToSaveData)
          }
          this.$jumpToSave(jumpToSaveData)
        }
        initParams.jumpToEditForm = jumpToEditForm
        initParams.addBtnMulti = initParams.addBtnMulti || false
        buttons.push({ text: btMakeSubBizCformText, icon: 'el-icon-edit-outline',
          enabledType: initParams.addBtnMulti ? '1+' : '1',
          click: bt => {
            const subBizId = bt.getRowValues('子业务ID')
            jumpToEditForm(subBizId, bt)
          } })
      }

      initParams.btModifyClick = initParams.btModifyClick || {}
      initParams.btModifyClick.text = '编辑业务'
      if (this.$isNotEmpty(initParams.btEditText)) {
        initParams.btModifyClick.text = initParams.btEditText
      }

      const btDeleteText = this.$isEmpty(initParams.btDeleteText)
        ? '删除' : initParams.btDeleteText
      initParams.btDeleteClick = initParams.btDeleteClick || {}
      initParams.btDeleteClick.text = btDeleteText

      // 当前是业务流转主子业务场景，统一删除操作
      if (this.$isNotEmpty(initParams['子业务类型'])) {
        const subBizType = initParams['子业务类型']
        const bizFlowNode = initParams['业务流转节点']
        initParams.params.deleteApiKey = 'WFDELETE'
        initParams.params.deleteApiExParams = initParams.params.deleteApiExParams || {}
        initParams.params.deleteApiExParams.dataType = subBizType
        initParams.params.deleteApiExParams.业务流转节点 = bizFlowNode

        initParams.getIdsStr4doActionByIds = (baseListObj, rows) => {
          var ids = []
          rows.forEach(obj => {
            var id = obj['子业务ID']
            if (this.$isNotEmpty(id)) {
              ids.push(id)
            }
          })
          return ids.join(',')
        }
      }

      // 点击单据详情等按钮时，这个方法负责获取到单据ID
      const getBillId = (bt) => {
        if (initParams.getBillId) {
          return initParams.getBillId(bt)
        } else {
          return bt.getRowId()
        }
      }

      const btDetailText = this.$isEmpty(initParams.btDetailText)
        ? '详情' : initParams.btDetailText
      initParams.btDetailClick = initParams.btDetailClick || {}
      initParams.btDetailClick.text = btDetailText

      const btDetailTextBill = this.$isEmpty(initParams.btDetailTextBill)
        ? '单据详情' : initParams.btDetailTextBill
      const btExportText = this.$isEmpty(initParams.btExportText)
        ? '导出' : initParams.btExportText

      // 这里的撤销是指整个业务流转节点动作的撤销
      // btWithdrawBizFlowText可能包含冒号，冒号前面的是按钮文本，
      // 冒号之后的是点击按钮的确认提示框
      const btWithdrawBizFlowText = this.$isEmpty(initParams.btWithdrawBizFlowText)
        ? '撤销' : initParams.btWithdrawBizFlowText
      let btWithdrawBizFlowTextFinal = btWithdrawBizFlowText
      const btWithdrawBizFlowTextTokens = btWithdrawBizFlowText.split(':')
      if (btWithdrawBizFlowTextTokens.length > 0) {
        btWithdrawBizFlowTextFinal = btWithdrawBizFlowTextTokens[0]
      }
      buttons.push({ text: btWithdrawBizFlowTextFinal, icon: 'el-icon-back', enabledType: '1+',
        click: bt => {
          const bizFlowType = this.$isNotEmpty(initParams.子业务类型) ? initParams.子业务类型 : ''
          const params = {
            dataType: 'CformDataEntity',
            子业务类型: bizFlowType,
            业务流转节点: initParams.业务流转节点
          }
          if (initParams.撤销不要检查是否处理过业务) {
            params.撤销不要检查是否处理过业务 = initParams.撤销不要检查是否处理过业务
          }
          this.$refs.baseCurdList4BizFlow.doActionByIds(
            btWithdrawBizFlowText, 'withdrawBizFlow', params)
        } })

      buttons.push({ text: '送审', icon: 'el-icon-position', enabledType: '1+', click: bt => {
        const getRowIds = () => { return bt.getRowValues('子业务ID') }
        this.$refs.bListWfSubmitForm.show(
          this, bt.getRowValue('子业务类型'), { getRowIds: getRowIds })
      } })

      // 这里的撤回是子业务的撤回，实际是撤回送审
      buttons.push({ text: '撤回', icon: 'el-icon-back', enabledType: '1+', click: bt => {
        this.$refs.baseCurdList4BizFlow.doActionByIds(
          '撤回', 'WFWITHDRAW', { dataType: bt.getRowValue('子业务类型') })
      } })

      if (initParams.subDeleteName) {
        const delIcon = (initParams.subDeleteIcon === undefined) ? 'el-icon-delete' : initParams.subDeleteIcon
        buttons.push({
          text: initParams.subDeleteName, icon: delIcon, enabledType: '1+', click: bt => {
            initParams.deleteSubData?.(bt.getRowValue('子业务类型'))
          }
        })
      }
      buttons.push({ text: '审核历史', icon: 'el-icon-time', enabledType: '1', click: bt => {
        // dataApiKey4WfHistory是审核历史针对的业务类型
        // 如果没有传递就会查询表单类型的审核历史
        let bizId = getBillId(bt)
        let dataApiKey4WfHistory = initParams.dataApiKey4WfHistory

        // 如果子业务是表单，主列表的审核历史按钮显示的是主业务的审核历史
        if (this.$isEmpty(initParams['子业务表单类型']) && this.needProcess) {
          bizId = bt.getRowValue('子业务ID')
          dataApiKey4WfHistory = 'wfAction' + bt.getRowValue('子业务类型')
        }
        if (this.$isEmpty(bizId)) {
          this.$message.error('bizId不能为空')
          return
        }

        this.$showWfHistory(bizId, null, null, dataApiKey4WfHistory)
      } })

      buttons.push({ text: btDetailTextBill, icon: 'el-icon-document', enabledType: '1',
        click: bt => {
          this.$showDetail(
            getBillId(bt),
            null,
            undefined,
            bt.getRowValue('metaName'),
            { 'isApply': false })
        } })

      initParams.buttons = buttons
      initParams.hiddenButtons = initParams.hiddenButtons || []
      initParams.hiddenButtons.push('新增')
      if (initParams.当前节点没有配置流程 === '是') {
        initParams.hiddenButtons.push('送审')
        initParams.hiddenButtons.push('撤回')
        initParams.hiddenButtons.push(initParams.btModifyClick.text)
        initParams.hiddenButtons.push(btDeleteText)
        initParams.hiddenButtons.push(btDetailText)
      } else if (this.$isEmpty(btMakeSubBizCformText)) {
        initParams.hiddenButtons.push(btWithdrawBizFlowText)
      }

      initParams.btAfters = initParams.btAfters || {}
      initParams.btAfters[btDetailText] = '撤回'
      initParams.btAfters[btDeleteText] = btDetailText

      // 子业务是表单时，需要额外设置隐藏按钮的数据
      const extraHiddenBts = []
      if (this.$isNotEmpty(initParams['子业务表单类型'])) {
        // 子业务是表单时有子列表
        initParams.listContentSubId = 'base-list-bizflow-cform-sublist'
        initParams.listContentSubHeight = 260

        extraHiddenBts.push(initParams.btModifyClick.text)
        extraHiddenBts.push('送审')
        extraHiddenBts.push('撤回')
        extraHiddenBts.push('撤销')
        extraHiddenBts.push(btDetailText)
        extraHiddenBts.push(btDeleteText)

        // 按钮排序，比如：生成合同，撤销
        initParams.btAfters[btWithdrawBizFlowText] = btMakeSubBizCformText
      }
      extraHiddenBts.forEach(bt => initParams.hiddenButtons.push(bt))

      if (initParams['隐藏导出按钮'] === true) {
        initParams.exportExcelName = undefined
      } else {
        if (this.$isEmpty(initParams.exportExcelName)) {
          initParams.exportExcelName = initParams['业务流转节点']
        }
        initParams.btAfters[btExportText] = btDetailTextBill
      }

      var reloadTableCallbackOutside = initParams.reloadTableCallback
      initParams.reloadTableCallback = (result, table, baseListObj) => {
        this.subBizViewId = result.attributes.subBizViewId
        this.subBizVersionId = result.attributes.subBizVersionId
        this.pageData = result.data

        // 使用后端返回的业务流转状态列表
        if (initParams.updateTree &&
            result.attributes.业务流转状态列表) {
          const statusList = result.attributes.业务流转状态列表.join(',')
          initParams.updateTree([bizFlowStatusName + ':业务流转状态:下拉:#' + statusList])
        }

        this.needProcess = (result.attributes['子业务开启流程'] === true)
        let hiddenBts = ['新增', '送审', '撤回']
        if (!this.needProcess) {
          if (initParams.当前节点没有配置流程 === '是') {
            hiddenBts.push(initParams.btModifyClick.text)
            hiddenBts.push(btDeleteText)
            hiddenBts.push(btDetailText)
          }
        } else {
          hiddenBts = ['新增']
          if (this.$isEmpty(btMakeSubBizCformText)) {
            hiddenBts.push(btWithdrawBizFlowText)
          }
        }
        // 刷新列表后保持子业务是表单时按钮的一致性
        extraHiddenBts.forEach(bt => {
          if (hiddenBts.indexOf(bt) < 0) {
            hiddenBts.push(bt)
          }
        })

        // 初始化时设置隐藏的按钮也需要隐藏
        initParams.hiddenButtons.forEach(bt => {
          if (hiddenBts.indexOf(bt) < 0) {
            hiddenBts.push(bt)
          }
        })
        baseListObj.reRenderBtns(hiddenBts)

        // 根据实时数据设置按钮的可用性
        baseListObj.rowsData.forEach(row => {
          if (this.needProcess) {
            const subBizId = row['子业务ID']
            const subBizStatus = row['子业务审核状态']
            const isSubmitted = row['子业务已送审'] || false
            const canWithdrawSubmitted = row['子业务可撤回送审'] || false
            // const isCompleted = row['子业务审核结束'] || false
            const canSubmit = (!isSubmitted && subBizStatus !== '草稿')
            const canDelete = (this.$isNotEmpty(subBizId) && !isSubmitted)
            let canShowWfHistory = this.$isNotEmpty(subBizId)

            // 子业务是表单类型时，审核历史按钮按照通用的设置执行可用性处理
            // 此时显示的审核历史是主业务的审核历史，不是子业务的
            if (this.$isNotEmpty(initParams['子业务表单类型'])) {
              canShowWfHistory = true
            }

            const setBtEnabledForRowChecked = (btText, value) => {
              if (hiddenBts.indexOf(btText) < 0) { // 按钮显示时，才设置可用性
                baseListObj.setBtEnabledForRowChecked(row, btText, value)
              }
            }
            setBtEnabledForRowChecked(initParams.btModifyClick.text, !isSubmitted)
            setBtEnabledForRowChecked('送审', canSubmit)
            setBtEnabledForRowChecked('撤回', canWithdrawSubmitted)
            setBtEnabledForRowChecked(btDeleteText, canDelete)
            setBtEnabledForRowChecked('审核历史', canShowWfHistory)
          }
        })

        if (reloadTableCallbackOutside) {
          reloadTableCallbackOutside(result, table, baseListObj)
        }
      }

      this.$saveInitParams(this, initParams)
      this.$saveInitParams(this.$parent, initParams)
      this.$refs.baseCurdList4BizFlow.init(initParams)
    },
    getBaseCurdList() {
      return this.$refs.baseCurdList4BizFlow
    },
    getBaseListTable() {
      return this.$refs.baseCurdList4BizFlow.getTable()
    },
    reloadTable() {
      const initParams = this.$getInitParams(this)
      this.$refs.baseCurdList4BizFlow.reloadTable(undefined, initParams)
    },
    showEditDialog(editCompName, bt, isEdit, exParams) { // 显示编辑框
      this.$refs.baseCurdList4BizFlow.showEditDialog(
        editCompName, bt, isEdit, exParams)
    },
    // 常规的弹框行表单编辑场景显示弹框
    showEditRform(exParams) {
      this.$refs.baseCurdList4BizFlow.showEditRform(exParams)
    }
  }
}

</script>

<style lang="scss">
</style>
