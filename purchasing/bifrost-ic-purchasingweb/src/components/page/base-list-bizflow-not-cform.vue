<template>
  <base-list-bizflow-cform ref="baseListBizflowCform"/>
</template>

<script>
import BaseListBizflowCform from './base-list-bizflow-cform'
export default {
  name: 'base-list-bizflow-not-cform',
  components: { BaseListBizflowCform },
  data() {
    return {
    }
  },
  methods: {
    initParams() {
      let params = {}
      if (this.$parent && this.$parent.initParams) {
        params = this.$parent.initParams()
        params.params = params.params || {}
        params.dataApiKey = 'selectPageDataNotCform'
        params.params['业务流转类型'] = params['业务流转类型']
        params.params['业务流转明细实体关联子业务外键字段'] = params['业务流转明细实体关联子业务外键字段']
      }
      return params
    },
    getBizflowCformObj() {
      return this.$refs.baseListBizflowCform
    }
  }
}
</script>

