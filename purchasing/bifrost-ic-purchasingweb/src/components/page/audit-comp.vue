<template>
         <!-- v-loading="loading" -->
  <div class="auditComp loading-size common-page flex-column"
       element-loading-spinner="el-icon-loading">
    <el-form
      label-width="85px"
      :model="auditData"
      class="auditForm flex-1"
      style="overflow: auto;overflow-x: hidden;"
      :disabled="auditFormDisabled"
      :rules="rules"
      :show-message="false"
      ref="audit"
    >
      <el-form-item v-show="showAuditRes" label="审核结果" class="auditForm-result">
        <el-radio-group v-model="auditData.auditResult" @input="changeAuditResult">
          <el-radio label="审核通过"></el-radio>
          <el-radio label="退回修改"></el-radio>
          <el-radio label="加签" v-show="showAuditAddCountersign"></el-radio>
          <el-radio label="转办" v-show="showTransfer"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="showCountersignRes" label="会签结果" >
        <el-radio-group v-model="countersignRes" @input="changeAuditResult">
          <el-radio label="同意"></el-radio>
          <el-radio label="不同意"></el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="指定类型" v-show="isShowAuditNext">
        <el-radio-group v-model="typeRadio" @change="typeChange"
        :disabled='!isShowAuditNext' >
          <el-radio label="1">指定用户</el-radio>
          <el-radio label="2">指定角色</el-radio>
        </el-radio-group>
      </el-form-item>
      <div style="display: flex">
        <el-form-item label="下环节人" style="flex: 1" v-if="isShowAuditNext && typeRadio == 1" prop="nextUserId">
          <el-select
            label="下环节人"
            v-model="auditData.nextUserId"
            placeholder="请选择下环节人"
            clearable
            filterable
            :disabled="selectLoading"
          >
            <el-option
              v-for="item in nextAuditUsers"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="下环节人" style="flex: 1" v-if="isShowAuditNext && typeRadio == 2" prop="nextRoleId">
          <el-select
            label="下环节人"
            v-model="auditData.nextRoleId"
            placeholder="请选择下环节人"
            clearable
            filterable
            :disabled="selectLoading"
          >
            <el-option
              v-for="item in nextAuditRoles"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div style="display: flex" v-if="showBackNode">
        <el-form-item label="退回节点:" style="flex: 1;" prop="returnNodeId">
          <el-select  v-model="auditData.returnNodeId" clearable>
            <el-option
              v-for="item in backHisNodes"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div style="display: flex" v-if="showBackMode">
        <el-form-item label="退送模式" style="flex: 1;" prop="backType">
          <el-select label="退送模式:" filterable v-model="auditData.backType" clearable>
            <el-option
              v-for="(item, index) in backTypes"
              :key="index"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div style="display: flex" v-if="isShowAuditAddCounterSign" class="auditCountersign">
        <el-form-item
          label="加签人员"
          style="flex: 1" prop="auditCountersignVal"
        >
          <el-input
            v-model="auditData.auditCountersignVal"
            placeholder="请点击指定加签"
            readonly
            v-on:click.native="addCountersignClick"
          >
            <el-button style="border:none!important" slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </el-form-item>
      </div>
      <div style="display: flex" v-if="isShowAuditAddCounterSign&&!isSpecialNodes">
        <el-form-item label="加签顺序:" style="flex: 1;" prop="addCountersignOrder">
          <el-select label="加签顺序:" filterable v-model="auditData.addCountersignOrder" clearable  :disabled="isAddCountersignType">
            <el-option
              v-for="item in addCountersignOrders"
              :key="item.id"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div style="display: flex" v-if="isShowAuditAddCounterSign&&!isSpecialNodes">
        <el-form-item label="加签后:" style="flex: 1;" prop="addCountersignAfter">
          <el-select label="加签后:" filterable v-model="auditData.addCountersignAfter" clearable  :disabled="isAddCountersignType">
            <el-option
              v-for="item in addCountersigNodes"
              :key="item.id"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
      </div>

      <div style="display: flex" v-if="isShowTransfer">
        <el-form-item
          label="转办人员"
          style="flex: 1" prop="auditTransfer"
        >
          <el-input
            v-model="auditData.auditTransferVal"
            placeholder="请点击指定转办人员"
            readonly
            v-on:click.native="btTransfer"
          >
            <el-button style="border:none!important" slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </el-form-item>
      </div>
<!--      <div style="display: flex" v-if="showAuditCountersign">-->
<!--        <el-form-item label="会签人员" style="flex: 1">-->
<!--          <el-input-->
<!--            v-model="auditData.auditCountersignVal"-->
<!--            placeholder="请点击指定会签人员"-->
<!--            readonly-->
<!--            v-on:click.native="countersignClick"-->
<!--          >-->
<!--            <el-button slot="append" icon="el-icon-search"></el-button>-->
<!--          </el-input>-->
<!--        </el-form-item>-->
<!--      </div>-->

      <!-- 审核补充条件要素动态设置：单选框 -->
      <div v-show ="showAuditElements">
        <div
          style="margin-bottom: 6px"
          v-for="(item) in Object.keys(auditConsCheckedData)"
          :key="item">
          <el-form-item :label="item">
            <el-radio v-model="auditCheckData[item]" :label="auditConsCheckedData[item][0]" >{{auditConsCheckedData[item][0]}}</el-radio>
            <el-radio v-model="auditCheckData[item]" :label="auditConsCheckedData[item][1]">{{auditConsCheckedData[item][1]}}</el-radio>
          </el-form-item>
        </div>
        <!-- 审核补充条件要素动态设置：下拉框 -->
        <div
          style="margin-bottom: 6px"
          v-for="(item) in Object.keys(auditConsSelectedData)"
          :key="item">
          <el-form-item :label="item" :prop="item">
            <el-select v-model="auditDropData[item]" clearable>
              <el-option
                v-for="item in auditConsSelectedData[item]"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
      <el-form-item  style="margin-bottom: 0px" v-show="isShowAudit&& passAudit" class="blod-checkbox">
        <el-checkbox v-model="auditData.checkAudit">后续流程节点出现我审核时,自动跳过审核</el-checkbox>
      </el-form-item>
      <el-form-item label="常用语" class="audit-common-words useFulTexts" v-show = "isOpinion">
        <el-button
          round
          v-for="item in useFulTexts"
          :title="item.title"
          :key="item.title"
          @click="setUseful(item.title)"
          >{{ item.text }}</el-button
        >
        <el-button type="primary" icon="el-icon-d-arrow-right" style="padding:6px;" @click="setUsefulData">更多</el-button>
      </el-form-item>
      <el-form-item label="审批意见" prop="opinion" style="margin-bottom: 4px" v-show = "isOpinion">
        <textarea
          style="font-size:14px"
          class="el-textarea el-input--small el-textarea__inner"
          type="textarea"
          rows="4"
          v-model="auditData.opinion"
          maxlength="150"
          show-word-limit
        />
      </el-form-item>
      <el-form-item  style="margin-bottom: 18px" v-show = "!isOpinion">
        <el-checkbox v-model="transferChecked">流程重新流经本节点时，直接由转办人员处理</el-checkbox>
      </el-form-item>
      <el-form-item label="备注"  style="margin-bottom: 18px" v-show = "!isOpinion">
        <textarea
          style="font-size:14px"
          class="el-textarea el-input--small el-textarea__inner"
          type="textarea"
          rows="4"
          v-model="auditData.opinion"
          maxlength="150"
          show-word-limit
        />
      </el-form-item>
      <div style="margin-top: 16px;" v-show="isShowUploadFile">
        <component v-if="extendFormItem" ref="extendFormItem" :is="loadDynamicComp(extendFormItem)" />
        <uploadFile
          ref="uploadFile"
          v-show="isShowUploadFile"
          :files="files"
          :autoUpload="false"
          :showTip="false"
          layout="Horizontal"
          :tableData="tableData"
          :tableLoading="tableLoading"
          :formDisabled="auditFormDisabled"
          :bizDataId="bizDataId"
          :hasAttType="true"
          :source="auditData.nodeName"
          :bizTblName="bizTblName"
          :attTypeTableName="attTypeTableName"
          :showFileList="false"
          :needUploadMessage="false"
          :sameType="false"
          :isDialog="isDialog"
          @initFileList="initFileList" />
      </div>
    </el-form>
    <div
      v-if="!isDialog"
    >
      <i
        v-show="showElectronicSeal && ShowElectronicSealSucceed"
        class="el-alert__icon el-icon-success"
        style="color: #67c23a; padding-left: 20px"
      ></i>
      <span
        v-show="showElectronicSeal && ShowElectronicSealSucceed"
        class="el-alert__title"
        style="color: #67c23a; padding-left: 20px"
        >{{ electronicSealMsg }}</span
      >
      <i
        v-show="showElectronicSeal && !ShowElectronicSealSucceed"
        class="el-alert__icon el-icon-error"
        style="color: #f56c6c; padding-left: 20px"
      ></i>
      <span
        v-show="showElectronicSeal && !ShowElectronicSealSucceed"
        class="el-alert__title"
        style="color: #f56c6c; padding-left: 20px"
        >{{ electronicSealMsg }}</span
      >
      <a
        v-show="showElectronicSeal && !ShowElectronicSealSucceed"
        @click="certCaCommon"
        style="text-decoration: underline"
        >刷新</a
      >
    </div>
    <!-- <div v-if="!isDialog" style="text-align: right;">
      <el-button type="primary" @click="submitAudit">确定</el-button>
    </div> -->
    <wf-useful-list-detail
      ref="WfUsefulListDetail"
      @usefulChange="usefulChange"
      @setUseful="setUseful"
    />
    <wf-countersign-userlist
      ref="WfCountersignUserlist"
      @conUserListChange="conUserListChange"
    />
  </div>
</template>

<script>
import { store } from '@/utils/sunmei-data'
import moment from 'moment'
import vueFiles from '@/constant/vueFiles.json'
import { transferCamelCaseName, containsChineseOrDigit } from '@/utils'

export default {
  name: 'audit-comp',
  props: {
    isDialog: {
      type: Boolean,
      default: false
    },
    isBatch: {
      type: Boolean,
      default: false
    },
    isUsingBlockView: {
      type: Boolean,
      default: false
    },
    auditFiles: {
      type: Array,
      default: () => []
    }
  },
  inject: {
    currentInstance: { default: undefined },
    tableAuditing: { default: undefined },
    getVo4AuditEditSave: { default: undefined },
    syncAuditData: { default: undefined },
    dataApiKey: { default: '' },
    dataType: { default: '' },
    getSaveApiExtraObj: { default: undefined },
    showErrorIfForm: { default: undefined },
    reLoadAnyway: { default: undefined },
    handleDialogClose: { default: undefined },
    getBlockTabRef: { default: undefined },
    outCheckPatchAudit: { default: undefined },
    changeIsShowDetail: { default: undefined },
    getIsBackList: { default: undefined },
    pageRoute: { default: '' }
  },
  data() {
    return {
      extendFormItem: '',
      selectLoading: true,
      nextAuditUsers: [],
      currAuditUsers: [], // 当前节点审核人加签时使用
      usefuls: [],
      useFulTexts: [],
      isAddCountersignType: false, // 加签类型是否可选
      // auditData: this.makeDefaultAuditData(),
      auditData: {},
      countersignRes: '同意',
      auditFormDisabled: false,
      showAuditNext: false, // 显示下一环节人
      showAuditNextRequired: false, // 选人必填
      showAuditCountersign: false, // 是否是会签
      showCountersignRes: false, // 是否显示会签结果
      showAuditRes: true, // 是否显示审核结果
      showAuditAddCountersign: false, // 显示加签人员
      showTransfer: false,
      isTransfer: false, // 显示转办
      isEnd: false, // 是否结束流程
      showAuditConsInput: false, // 显示审核条件单选要素
      auditConsSelectedData: {}, // 审核条件要素下拉数据
      auditConsCheckedData: {}, // 审核条件要素单选数据
      auditCountersignVal: '',
      isUsefuled: false, // 是否显示常用语
      electronicSealMsg: '电子签章认证失败！',
      showElectronicSeal: false, // 是否显示电子签章认证
      ShowElectronicSealSucceed: false, // 是否显示电子签章认证成功
      isWebJsCert: false,
      webCertType: '',
      rules: this.makeDefaultRules(),
      files: [],
      tableData: {},
      tableLoading: true,
      bizDataId: '', // 业务记录编码,业务表主键ID
      bizTblName: 'ATTACHMENT', // 业务表名称
      attTypeTableName: '', // 附件类型表名
      backTypes: [], // 流程退回提交设置配置种取值
      addCountersignOrders: [{ id: 1, name: '串行加签：按选择顺序审核' }, { id: 2, name: '并行加签：不需按选择顺序审核' }],
      addCountersigNodes: [{ id: 1, name: '流向下一节点' }, { id: 2, name: '回到本节点' }],
      showBackNode: false,
      showBackMode: false,
      passAudit: true,
      backUpdate: false,
      addCountersign: false,
      backHisNodes: [],
      isCustomFile: false,
      hasAuditRule: false,
      typeRadio: '1',
      nextAuditRoles: [], // 指定角色的数据
      loading: false,
      auditDropData: {},
      auditCheckData: {},
      isOpinion: true, // 是否显示审核意见转办时为显示备注
      transferChecked: false, // 转办是使用
      isDefault: true,
      isShowAudit: false,
      isUploadFile: false, // 是否允许上传附件
      isSpecialNodes: false, // 是否是特殊节点 程序内置的会签节点可以进行加签
      checkedRows: []
    }
  },
  watch: {
    auditFormDisabled: function(newValue) {
      this.$emit('changeAuditFormDisabled', newValue)
    },
    isUploadFile() {
      this.$emit('changeShowUploadFile', this.isUploadFile)
    }
  },
  computed: {
    isShowAuditNext() {
      return this.passAudit && this.showAuditNext
    },
    isShowAuditAddCounterSign() {
      return this.addCountersign && this.showAuditAddCountersign
    },
    isShowTransfer() {
      return this.isTransfer && this.showTransfer
    },
    showAuditElements() {
      // 加签
      const isOpenElements = this.isShowAuditAddCounterSign
      const isopenAuditCons = this.showAuditConsInput
      const auditConsCheckedData = Object.keys((this.auditConsCheckedData || {}))
      const auditConsSelectedData = Object.keys((this.auditConsSelectedData || {}))

      return (isOpenElements || isopenAuditCons || this.passAudit) && (auditConsCheckedData.length || auditConsSelectedData.length)
    },
    isShowUploadFile() {
      // && !this.isBatch
      return this.isDialog && this.isUploadFile
    }
  },
  methods: {
    // 设置审核扩展表单组件
    setExtendFormItem(compName) {
      if (this.$isNotEmpty(compName) && typeof compName === 'string') {
        const hasComp = containsChineseOrDigit(compName) ? vueFiles[compName] : vueFiles(transferCamelCaseName(compName))
        if (!hasComp) {
          this.$nextTick(() => {
            this.$message.error(`审核扩展表单组件${compName}不存在`)
          })
          return
        }
        this.extendFormItem = compName
      }
    },
    /**
     * 初始化审核扩展表单组件
     * @param {Object} result wfActionCformDataEntity接口返回的数据
     */
    initExtendFormItem(result) {
      if (this.extendFormItem) {
        const extendFormItemRules = this.$refs.extendFormItem.rules
        if (this.$isNotEmpty(extendFormItemRules)) {
          Object.assign(this.rules, this.$refs.extendFormItem.rules)
        }
        const params = {
          auditFormDisabled: this.auditFormDisabled,
          result
        }
        this.$refs.extendFormItem.init(params)
      }
    },
    /**
     * 合并扩展表单组件数据
     * @param {Object} data
     */
    handleExtendFormItemData(data) {
      if (this.$refs.extendFormItem) {
        const extendFormItemData = this.$refs.extendFormItem.getExtendData()
        Object.assign(data, extendFormItemData)
      }
    },
    changeAuditResult(e) {
      // 切换审核结果
      this.rules.opinion[0].required = true
      switch (e) {
        case '审核通过':
          this.passAudit = true
          this.backUpdate = false
          this.addCountersign = false
          this.showBackNode = false
          this.showBackMode = false
          this.typeRadio = '1'
          // if (this.auditData.opinion === '不同意') {
          //   this.auditData.opinion = '同意'
          // }
          this.isTransfer = false
          this.isOpinion = true
          break
        case '退回修改':
          this.passAudit = false
          this.backUpdate = true
          this.addCountersign = false
          this.showBackNode = this.showAuditSpecify
          this.showBackMode = true
          // if (this.auditData.opinion === '同意' ||
          //   this.auditData.opinion === '拟同意' || this.auditData.opinion === '') {
          //   this.auditData.opinion = '不同意'
          // }
          this.isTransfer = false
          this.isOpinion = true
          break
        case '加签':
          this.passAudit = false
          this.backUpdate = true
          this.addCountersign = true
          this.showBackNode = false
          this.showBackMode = false
          this.isTransfer = false
          this.isOpinion = true
          break
        case '转办':
          this.passAudit = false
          this.backUpdate = true
          this.addCountersign = false
          this.showBackNode = false
          this.showBackMode = false
          this.isTransfer = true
          this.isOpinion = false
          this.auditData.opinion = ''
          this.rules.opinion[0].required = false
          break
        default:
          break
      }
      this.$nextTick(this.$refs['audit']?.clearValidate)
    },
    getAppendix() {
      const params = {
        dataApiKey: 'selectAttachForAuditPageData',
        deleteApiKey: 'deleteAttachment',
        FK_GUID_in: this.bizDataId,
        isAudit: 1,
        APPENDIX_TYPE_eq: '审核依据',
        size: 100000, // 不显示分页默认 100000
        current: 1,
        isReloadByInit: true,
        menuId: store.get('menuData').getMenuId()
      }
      this.$callApiParams('selectAttachForAuditPageData', params, (result) => {
        this.tableLoading = false
        const tableHeader = ['操作', '附件名称', '上传时间', '附件类别']
        // 组装表头
        const header = tableHeader.map(header => {
          return {
            prop: header === '操作' ? 'option' : header,
            label: header === '操作' ? '' : header,
            width: header === '操作' ? '50px' : 'auto'
          }
        })
        // 组装内容
        const content = result.data.rows.map(content => {
          return {
            id: content.id,
            attId: content.attId,
            attName: content['附件名称'],
            option: '删除',
            附件名称: content['附件名称'],
            上传时间: content['上传时间'],
            附件类别: content['类别']
          }
        })
        this.files = content
        this.tableData = {
          header,
          content
        }
        return true
      })
    },
    makeDefaultRules() {
      const defaultRules = {
        opinion: [
          { required: true, message: '请填写审批意见', trigger: 'blur' }
        ]
      }
      defaultRules.returnNodeId = [
        { required: true, message: '请选择退回节点', trigger: 'change' }
      ]
      defaultRules.backType = [
        { required: true, message: '请选择退送模式', trigger: 'change' }
      ]
      defaultRules.auditCountersignVal = [
        { required: true, message: '请选择加签人员', trigger: 'change' }
      ]
      defaultRules.auditTransferVal = [
        { required: true, message: '请选择转办人员', trigger: 'change' }
      ]
      defaultRules.addCountersignOrder = [
        { required: true, message: '请选择加签顺序', trigger: 'change' }
      ]
      defaultRules.addCountersignAfter = [
        { required: true, message: '请选择加签后', trigger: 'change' }
      ]
      return defaultRules
    },
    makeDefaultAuditData() {
      const defaultData = {
        nodeName: '',
        auditUser: '',
        opinion: '',
        commonWords: '',
        nextUserId: '',
        nextRoleId: '',
        returnNodeId: '',
        nodeId: '',
        auditCountersignVal: '',
        backType: '',
        auditDropData: {},
        auditCheckData: {},
        checkAudit: false
      }
      defaultData.auditResult = this.auditFormDisabled ? '' : '审核通过'
      return defaultData
    },
    initFileList(list, typeOption) {
      this.files = list.map(ls => {
        if (ls.附件类别) {
          return { ...ls }
        } else {
          return {
            ...ls,
            option: '删除',
            附件名称: ls.name,
            上传时间: moment(Date.now()).format('YYYY-MM-DD'),
            附件类别: ls.appendixClass || typeOption[0].eleName,
            tcode: ls.appendixTypeCode || typeOption[0].eleCode,
            tname: ls.appendixClass || typeOption[0].eleName
          }
        }
      })
    },
    backSubmit(auditData, backType) {
      this.auditData = auditData
      this.doAudit('0', backType)
    },
    conUserListChange(obj, data) {
      let nextUserIdStr = ''
      let nextUserIdVal = ''
      let mainAuditId = '' // 主审核人
      for (let i = 0; i < obj.length; i++) {
        nextUserIdStr += obj[i].key + ','
        nextUserIdVal += obj[i].label + ','
        if (obj[i].isMainUser) {
          mainAuditId = obj[i].key
        }
      }
      this.auditData.mainAuditId = mainAuditId
      this.auditData.countersignNextUser = nextUserIdStr
      const auditCountersignVal = nextUserIdVal.substring(
        0,
        nextUserIdVal.length - 1
      )
      this.$set(this.auditData, 'auditCountersignVal', auditCountersignVal)
    },
    countersignClick() {
      this.$refs.WfCountersignUserlist.init(this.nextAuditUsers, false)
    },
    addCountersignClick() {
      if (this.currAuditUsers.length > 0) {
        this.$refs.WfCountersignUserlist.init(this.currAuditUsers, true)
      }
    },
    btTransfer() {
      const params = {
        multiple: false,
        updateParamsCallback: (params) => {
          // 处理参照数据反向选择
          if (this.auditData.auditCountersignKey !== undefined) {
            params.checkedData = [this.auditData.auditCountersignKey]
          }
        }
      }
      const refData = { colType: '弹框', dataRef: '选择系统人员' }
      this.$refData(undefined,
        refData,
        () => {},
        () => {},
        () => {},
        selectedData => {
          if (selectedData) {
            this.$set(this.auditData, 'countersignNextUser', selectedData.list[0].id)
            this.$set(this.auditData, 'auditTransferVal', selectedData.list[0].label)
            this.$set(this.auditData, 'auditCountersignKey', selectedData.list[0].itemKey)
          }
        }, params)
    },
    usefulChange() {
      this.initUsefulData()
    },
    setUseful(title) {
      this.auditData.opinion = title
    },
    setUsefulData() {
      // var check = this.$getTableSelection(this.tableAuditing?.())[0]
      var check = this.checkedRows[0]
      var node = {}
      node.usefuls = this.useFuls
      node.name = check ? check['当前节点'] : '1'
      node.bizid = check ? check.currentNodeId : ''
      node.metaId = check ? check.wfMetaId : ''
      // 设置页面为用户常用语页面
      this.$refs.WfUsefulListDetail.isUsefulUser = true
      this.$refs.WfUsefulListDetail.isUsefulPage = true
      if (this.auditData.opinion && this.auditData.opinion !== '同意') {
        var istext = 0
        for (let i = 0; i < this.useFuls.length; i++) {
          if (this.useFuls[i].usefulText === this.auditData.opinion) {
            istext = 1
            break
          }
        }
        if (istext === 0) {
          this.$confirm('是否需要将审核意见框的数据添加到常用语', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          })
            .then(() => {
              this.$refs.WfUsefulListDetail.initMeta(
                node.name,
                node,
                true,
                this.auditData.opinion
              )
            })
            .catch(() => {
              this.$refs.WfUsefulListDetail.initMeta(node.name, node, true)
            })
          return true
        }
      }
      this.$refs.WfUsefulListDetail.initMeta(node.name, node, true)
    },
    selectAuditData(rows) {
      // todo
      this.isUploadFile = false
      if (rows.length === 1) {
        // 单条审核才去请求审核单据的数据
        this.bizDataId = rows[0].id
        this.getAppendix()
      }
      this.auditFormDisabled = true
      this.auditData = this.makeDefaultAuditData()
      this.rules.nextUserId = []
      this.rules.nextRoleId = []
      const params = {
        id: rows.map(row => row.id)?.join?.(',') || '',
        actionKey: 'getCurrentNodeAuditOfInfo',
        isAudit: 1,
        pageRoute: this.pageRoute
      }
      // 加载推送模式数据
      this.getSelectBackTypeSetting(rows)
      if (rows.length === 0) {
        this.initUsefulData()
        return
      } else if (rows.length === 1) {
        if (this.isDialog) {
          this.attTypeTableName = rows[0].attTypeTableName
          const data = {
            wfMetaId: rows[0].wfMetaId,
            nodeName: this.auditData.nodeName,
            attTypeTableName: this.attTypeTableName,
            isSelAttForAudit: rows[0].isSelAttForAudit
          }
          this.showWhat(data)
        }
        this.initUsefulData()
        this.auditData.auditResult = '审核通过'
        this.auditFormDisabled = false
        // 批量支持上传附件
        const metaIdList = []
        const nodeNameList = []
        const metaVersionList = []
        const bizidList = []
        rows.forEach(row => {
          bizidList.push(row.id)
          metaIdList.push(row.wfMetaId)
          nodeNameList.push(row.nodeName)
          metaVersionList.push(row.wfMetaVersionId)
        })
        this.$callApiParams('getCurrentNodeAuditBathUploadFile', {
          metaIdStr: metaIdList.join(','),
          nodeNameStr: nodeNameList.join(','),
          metaVersionStr: metaVersionList.join(',')
        }, (result) => {

          this.isUploadFile = result.data.isUploadFile
          if (this.isUploadFile) {
            this.bizDataId = bizidList.join(',')
            this.getAppendix()
            this.loading = false
          }
          return true
        })
        // return
      }
      this.loading = true
      this.auditData.auditCountersignVal = ''
      this.auditData.nextUserId = ''
      this.$callApiParams(`wfAction${this.dataType}`, params, (result) => {
        console.log(result,'--------------')
        this.auditFormDisabled = false
        result.data.remark.auditResult = '审核通过'
        // 重置为审核通过
        this.changeAuditResult('审核通过')
        this.auditData = result.data.remark
        this.initExtendFormItem(result)
        // 流程设置控制评论开启功能
        this.$emit('setBlockTabs', undefined, true)
        if (this.$isNotEmpty(result.data.currtNode)) {
          this.auditData.nodeId = result.data.currtNode.id
          const wfMetaData = result.data.wfMeta
          if (result.data.currtNode.auditRuleFilesId || this.$isNotEmpty(this.auditFiles)) {
            this.$emit('setBlockTabs', {
              label: '规章制度',
              name: '规章制度',
              component: 'ruleBlock'
            })
          }
          this.hasAuditRule = !!result.data.currtNode.auxiliaryAuditRuleId
          if (result.data.currtNode.auxiliaryAuditRuleId) {
            this.$emit('setBlockTabs', {
              label: '辅助审核',
              name: '辅助审核',
              component: 'assistBlock'
            })
          }
          if (this.$isNotEmpty(wfMetaData) && wfMetaData.selWfSettingStr.indexOf('开启评论功能') > -1) {
            this.$emit('setBlockTabs', {
              label: '评论信息',
              name: '评论信息',
              component: 'wf-evaluate-tab'
            })
          }
        }
        if (this.isDialog) {
          this.attTypeTableName = rows[0].attTypeTableName
          const data = {
            wfMetaId: rows[0].wfMetaId,
            nodeName: this.auditData.nodeName,
            attTypeTableName: this.attTypeTableName,
            isSelAttForAudit: rows[0].isSelAttForAudit
          }
          this.$refs['audit'].resetFields()
          this.showWhat(data)
          this.loading = false
        } else {
          this.loading = false
        }
        this.showAuditNext = result.data.currtNode.isNextAuthUser === '启用'
        this.isShowAudit = result.data.isShowAudit
        this.isUploadFile = result.data.isUploadFile
        this.showAuditNextRequired = result.data.currtNode.isNextAuthUserRequired === '启用'
        this.showAuditSpecify = result.data.currtNode.isBackNode === '启用'
        this.showAuditCountersign = result.data.isCountersign === '启用'
        this.showAuditRes = result.data.isAuditRes
        this.showCountersignRes = result.data.showCountersignRes
        if (this.showAuditCountersign) {
          this.countersignRes = '同意'
        }
        this.showAuditAddCountersign = result.data.isAddCountersign === '启用'
        this.isSpecialNodes = result.data.isSpecialNodes
        if (this.isSpecialNodes) {
          this.auditData.addCountersignAfter = '流向下一节点'
          this.auditData.addCountersignOrder = '并行加签：不需按选择顺序审核'
        }
        if (this.showAuditAddCountersign &&
          result.data.countersignType === '单组长模式的会签') { // 启用加签时判断是否是 单组长模式会签加签
          this.auditData.addCountersignAfter = '回到本节点'
          this.auditData.addCountersignOrder = '并行加签：不需按选择顺序审核'
          this.isAddCountersignType = true
        }
        this.showTransfer = result.data.showTransfer
        this.isEnd = result.data.isEnd
        this.showAuditConsInput = result.data.isOpenAuditCondition
        this.auditConsSelectedData = result.data.auditConsSelectedData || {}
        this.auditConsCheckedData = result.data.auditConsCheckedData || {}

        if (this.$isNotEmpty(this.auditConsSelectedData)) {
          // this.isShowAuditConsSelected = true
          Object.keys(this.auditConsSelectedData).map(item => {
            this.$set(this.auditDropData, item, '')
          })
        }
        if (this.$isNotEmpty(this.auditConsCheckedData)) {
          this.showAuditConsInput = true
          Object.keys(this.auditConsCheckedData).map(item => {
            this.$set(this.auditCheckData, item, '')
          })
        }
        this.showElectronicSeal =
          result.data.currtNode.isElectronicSeal === '启用'
        if (result.data.isNotExistNextNode === '1') {
          this.showAuditNext = false
        }
        if (this.showAuditNext && result.data.currtNode.isNextAuthUserRequired === '启用') {
          this.rules.nextUserId = [
            { required: true, message: ' ', trigger: 'change' }
          ]
          this.rules.nextRoleId = [
            { required: true, message: ' ', trigger: 'change' }
          ]
          this.showAuditNextRequired = true
        }
        if (this.showAuditNext) {
          this.typeRadio = '1'
          this.getNextNodeAssign(rows[0].id)
        }
        if (this.showAuditAddCountersign) {
          // 存在加签时加载当前节点的审核用户
          this.getCurrNodeAssign(rows[0].id)
        }
        if (this.showAuditSpecify) {
          this.getBackHisNodes(rows[0].id)
        }
        if (this.showElectronicSeal) {
          this.certCaCommon()
        } else {
          this.isWebJsCert = false
          this.webCertType = ''
        }
        this.initUsefulData(rows[0].id)

        this.syncAuditData?.(this)
        this.loading = false
        return true
      }, () => {
        this.loading = false
      })
    },
    initUsefulData(dataId) {
      this.isUsefuled = false
      // var nodeName = this.$getTableSelection(this.tableAuditing?.())[0]
      //   ? this.$getTableSelection(this.tableAuditing?.())[0]['当前节点']
      //   : '1'
      var nodeName = this.checkedRows[0]?.nodeName ? this.checkedRows[0]?.nodeName : '1'
      if (!nodeName) return
      this.$callApiParams(
        'getCurrentNodeWfNodeUsefuls',
        { nodeName: nodeName, isUsefulPage: true, size: 20, current: 1 },
        (result) => {
          var wfNodeUsefuls = result.data.rows
          this.useFuls = wfNodeUsefuls
          const arr = []
          this.isDefault = result.attributes.isDefault
          if (!result.attributes.isDefault) {
            this.auditData.opinion = ''
          } else {
            this.auditData.opinion = '同意'
          }
          const maxCount = this.isDialog ? 15 : 3
          for (var i = 0; i < wfNodeUsefuls.length; i++) {
            if (i === 0) {
              if (result.attributes.isDefault) {
                this.auditData.opinion = wfNodeUsefuls[i].usefulText
              }
            }
            const bt = {}

            if (i < maxCount) {
              bt.title = wfNodeUsefuls[i].usefulText
              bt.text = wfNodeUsefuls[i].usefulText
              if (wfNodeUsefuls[i].usefulText.length > 16) {
                bt.text = wfNodeUsefuls[i].usefulText.slice(0, 15) + '...'
              }
              arr.push(bt)
            } else {
              this.isUsefuled = true
              break
            }
          }
          this.useFulTexts = arr
          this.isUseful = this.useFuls.length > 0
          if (dataId) {
            this.getCurrtUseful(dataId)
          }
          return true
        }
      )
    },
    getNextNodeAssign(dataId) {
      // ====== TODO: 获取下个节点审核人
      var nextParams = {}
      nextParams.actionKey = 'getNextNodeAssign'
      nextParams.id = dataId
      this.selectLoading = true
      this.$callApiParams(`wfAction${this.dataType}`, nextParams, (result) => {
        this.selectLoading = false
        this.nextAuditUsers = result.data.nextNodeAssigns
        this.nextAuditRoles = result.data.nextNodeRoles
        this.typeChange()
        if (this.nextAuditUsers.length === 1 && this.typeRadio === '1') {
          // 当下一环节人只有一个时默认选中
          this.auditData.nextUserId = this.nextAuditUsers[0].id
        } else if (this.nextAuditRoles.length === 1 && this.typeRadio === '2') {
          this.auditData.nextRoleId = this.nextAuditRoles[0].id
        }
        this.$nextTick(this.$refs['audit'].clearValidate)
        return true
      })
    },
    getCurrNodeAssign(dataId) {
      var nextParams = {}
      nextParams.actionKey = 'getUser'
      nextParams.id = dataId
      this.$callApiParams(`wfAction${this.dataType}`, nextParams, (result) => {
        this.currAuditUsers = result.data
        return true
      })
    },
    getCurrtUseful(dataId) {
      // ====== TODO: 获取当前节点的节点常用语
      var backParams = {}
      backParams.actionKey = 'getCurrtUseful'
      backParams.id = dataId
      this.$callApiParams(`wfAction${this.dataType}`, backParams, (result) => {
        if (result.data.currtUsefulText && this.isDefault) {
          this.auditData.opinion = result.data.currtUsefulText
        }
        return true
      })
    },
    auditOrSaveWithEditVo(doActionHandler) {
      if (doActionHandler === undefined) {
        this.$message.error('doActionHandler不能为空')
        return
      }
      // 非区块表单时，dataVo和vo是同一对象
      // 区块表单时，dataVo是表单对象，vo是blockView
      var doActionHandlerFinal = (dataVo, vo) => {
        dataVo.extData['auditData'] = this.auditData
        doActionHandler(vo)
        doActionHandlerFinal = null
      }
      if (this.isUsingBlockView) {
        this.$emit('wrapDataAndAction', false, doActionHandlerFinal)
      } else {
        if (typeof this.getVo4AuditEditSave !== 'function') {
          this.$message.error('this.getVo4AuditEditSave 必须是函数')
          return
        }
        var vo = this.getVo4AuditEditSave()
        if (vo === undefined) {
          this.$message.error('vo对象不能为空')
        } else {
          doActionHandlerFinal(vo, vo)
        }
      }
    },
    getBackHisNodes(dataId) {
      // ====== TODO: 获取退回环节的数据
      var backParams = {}
      backParams.actionKey = 'getBackHisNodes'
      backParams.id = dataId
      this.$callApiParams(`wfAction${this.dataType}`, backParams, (result) => {
        this.backHisNodes = result.data.backHisNodes
        if (this.backHisNodes.length > 0) {
          this.setBackNodes(result.data.backHisNodes)
          this.showAuditSpecify = true
        } else {
          this.showAuditSpecify = false
        }
        return true
      })
    },
    backAudit() {
      // 退回修改
      this.$refs['audit'].validate((valid) => {
        if (valid) {
          this.backSubmit(this.auditData, this.auditData.backType)
        } else {
          return
        }
      })
    },
    doAudit(isApprove, backType) {
      if (this.auditCheckData) {
        this.auditData.auditCheckData = this.auditCheckData
      }
      if (this.auditDropData) {
        this.auditData.auditDropData = this.auditDropData
      }
      // isApprove =1 审核通过 0退回 2加签 3会签 4转办
      if (this.auditData.opinion === '' && this.rules.opinion?.[0]?.required) {
        return
      }
      if (
        !this.showAuditNext &&
        !this.showAuditCountersign &&
        isApprove === '1'
      ) {
        this.auditData.nextUserId = ''
      }
      const h = this.$createElement
      // var checkedRows = this.$getTableSelection(this.isDialog ? this.currentInstance?.().$refs.table : this.tableAuditing?.())
      // if (this.checkPatchAudit(checkedRows, true)) {
      const checkedRows = this.checkedRows
      var backHisNodeText = '制单环节'
      this.backHisNodes.forEach((row) => {
        if (row.id === this.auditData.returnNodeId) {
          backHisNodeText = row.name
          this.auditData.returnNodeName = row.name
        }
      })
      var pass = isApprove
      var message = ''
      if (isApprove === '1') {
        this.auditData.countersignNextUser = ''
        if (this.isEnd) {
          message = h('div', [
            h('p', { style: 'color: red' }, '当前节点为最后一个审核节点！'),
            h('p', '确定要 [审核通过] 吗?')
          ])
        } else {
          // 存在下一环节人时必须选
          if (this.showAuditNext && this.showAuditNextRequired && this.typeRadio === '1') {
            if (this.$isEmpty(this.auditData.nextUserId)) {
              this.$message.error('下一环节人不能为空')
              return
            }
          }
          message = '确定要 [审核通过] 吗?'
        }
      }
      if (isApprove === '0') {
        message = '确定要 [退回修改]，退回到' + backHisNodeText + '吗?'
      }
      if (isApprove === '2') {
        message = '确定要 [加签] 吗?'
      }
      if (isApprove === '3') {
        message = '确定要 [' + this.countersignRes + '] 吗?'
      }
      if (isApprove === '4') {
        message = '确定要 [转办] 吗?'
      }
      var ids = this.$getTableCheckedIdsStrBy(checkedRows)
      var params = this.auditData
      params._PASS = pass
      params.ids = ids
      if (checkedRows?.length > 1) { // 批量审核传参nodeId
        params.nodeId = checkedRows[0]?.currentNodeId
      }else {
        params.nodeId = checkedRows[0]?.currentNodeId
      }
      params.countersignRes = this.countersignRes
      params.showAuditNext = this.showAuditNext
      params.dataType = this.dataType
      params.transferChecked = this.transferChecked
      if (backType) {
        params.backType = backType
      }

      // 如果当前是可编辑的审核，则使用json+参数的方式传递
      var extra = this.getSaveApiExtraObj?.(ids, pass)
      var doActionHandler = (vo) => {
        if (vo.containers?.[0]?.blocks?.[0]?.data?.extData) {
          vo.containers[0].blocks[0].data.extData['审核编辑保存'] = '是'
        }
        this.handleExtendFormItemData(vo)
        this.$callApiParamsConfirm(
          message,
          this.isDialog ? async() => {
            // 审核通过前判断有无删除服务器中的文件 有则需要请求服务器进行删除
            this.$refs.uploadFile.handleRemoveFromServer()
            // 审核通过前上传文件 满足 (显示上传文件 && fileList不为空)
            if (this.$isNotEmpty(this.$refs.uploadFile.fileList) && this.isShowUploadFile) {
              return await this.$refs.uploadFile.handlefiledSumbit()
            }
          } : (isApprove === '1' ? this.verifyAudit : undefined),
          'WFAUDIT',
          vo,
          async(result) => {
            if (this.isDialog) {
              const table = this.currentInstance?.().$refs.table
                this.handleDialogClose?.()
                table.clearSelection()
                this.$removeTableRowHeightlight(table)
                this.currentInstance?.().reloadTable()
            } else {
              if (isApprove === '1') {
                const blockTabRef = this.getBlockTabRef?.()
                if (this.$isNotEmpty(blockTabRef)) {
                  blockTabRef.submit(params)
                }
              }
              if (this.getIsBackList?.()) {
                this.changeIsShowDetail?.(false)
              } else {
                this.reLoadAnyway?.()
              }
            }
            if (this.isEnd) {
              for (let i = 0; i < checkedRows.length; i++) {
                if (this.$isNotEmpty(checkedRows[i].formType)) {
                  await new Promise((resolve) => {
                    var newParams = {}
                    newParams.printId = checkedRows[i].id
                    newParams.isSavePdf = true
                    newParams.onPrintDlgClose = () => {
                      resolve()
                    }
                    this.$showExportWps(newParams, checkedRows[i].formType, '', '电子签章')
                  })
                }
              }
            }
            const data = result.attributes
            if (this.$isNotEmpty(data)) {
              if (this.$isNotEmpty(data['重新签章单据ID']) && this.$isNotEmpty(data['重新签章单据的表单类别'])) {
                await new Promise((resolve) => {
                  const params = {}
                  params.printId = data['重新签章单据ID']
                  params.isSavePdf = true
                  params.onPrintDlgClose = () => {
                    resolve()
                  }
                  const formType = data['重新签章单据的表单类别']
                  this.$showExportWps(params, formType, '', '重新电子签章')
                })
              }
            }
            this.$refreshCount(this)
          },
          (result) => {
            return this.isDialog ? false : this.showErrorIfForm?.(result)
          },
          extra
        )
      }
      if (extra) {
        this.auditOrSaveWithEditVo(doActionHandler)
      } else {
        doActionHandler(params)
      }
      // }
    },
    verifyAudit() {
      const blockTabRef = this.getBlockTabRef?.()
      if (this.hasAuditRule && blockTabRef) {
        return blockTabRef.verifyAudit()
      }
    },
    certCaCommon() {
      this.$callApiParams(
        'certCaCommon',
        { a: Math.round(Math.random() * 3) },
        (result) => {
          this.isWebJsCert = result.data.isWebJsCert
          this.webCertType = result.data.webCertType
          if (this.isWebJsCert) this.webWfCaCert()
          this.ShowElectronicSealSucceed = result.success
          this.electronicSealMsg = result.msg
          return true
        }
      )
    },
    webWfCaCert() {
      this.ShowElectronicSealSucceed = false
      this.electronicSealMsg = '未查询到签章认证'
      if (this.webCertType === 'HCCA') this.wfCaHCCACert()
    },
    wfCaHCCACert() {
      const a = Math.round(Math.random() * 3)
      if (a === 0) {
        this.ShowElectronicSealSucceed = true
        this.electronicSealMsg = '电子签章认证成功！'
      } else if (a === 1) {
        this.ShowElectronicSealSucceed = false
        this.electronicSealMsg = '获取设备序列号失败！'
      } else if (a === 2) {
        this.ShowElectronicSealSucceed = false
        this.electronicSealMsg = '远程调用认证服务失败！'
      } else {
        this.ShowElectronicSealSucceed = false
        this.electronicSealMsg = '电子签章认证失败！'
      }
    },
    checkPatchAudit(checkedRows, showErrorMessage) {
      return this.outCheckPatchAudit?.(checkedRows, showErrorMessage)
    },
    showWhat(data) {
      // 判断是显示审核依据弹窗还是正常弹窗内容
      this.$callApiParams('getAllWfNodesByWfMetaId',
        { 'wfMetaIds': data.wfMetaId, 'nodeName': data.nodeName },
        result => {
          const wfNodes = result.data
          if (result.success && wfNodes && wfNodes[0] && wfNodes[0].selWfNodeSettingStr.indexOf('必须上传附件') !== -1 &&
            this.$isNotEmpty(wfNodes[0].selectRecAcctTypeOptions)) {
            this.$refs.uploadFile.selectAttTypeList(wfNodes[0].selectRecAcctTypeOptions)
            this.isCustomFile = true
            this.fileUploadData()
          } else if (data.isSelAttForAudit === '是') {
            this.fileUploadData()
          } else {
            // 如果是直接选择文件 则是审核依据 下拉框不可下拉
            this.fileUploadData('审核依据', true)
          }
          this.loading = false
          return true
        }, () => {
          this.loading = false
        })
    },
    fileUploadData(selectRecAcctTypeOptions = '', selectDisabled = false) {
      if (!this.isCustomFile) {
        this.$refs.uploadFile.selectAttTypeList(selectRecAcctTypeOptions, selectDisabled)
      }
      this.$refs.uploadFile.bizTblName = this.bizTblName
      this.$refs.uploadFile.bizDataId = this.bizDataId
    },
    getSelectBackTypeSetting(rows = []) {
      const currentSelect = rows[0]
      this.checkedRows = rows
      if (!currentSelect || currentSelect?.status === '草稿') return
      const params = {
        metaId: currentSelect?.wfMetaId,
        nodeName: currentSelect?.currentNode,
        metaVersionId: currentSelect?.wfMetaVersionId
      }
      this.$callApiParams('selectBackTypeSetting', params, (result) => {
        this.setBackTypes(result.data)
        // this.setUsefulData()
        return true
      })
    },
    /**
     * 动态设置推送模式数据
     * @param {Array} data 推送模式数据
     */
    setBackTypes(data) {
      var type = data.filter(item => item === '退送后再次送审需依次从头审批')
      if (type[0]) {
        this.$set(this.auditData, 'backType', type[0])
      } else {
        if (data.length === 1) {
          this.$set(this.auditData, 'backType', data[0])
        }
      }
      console.log('动态设置推送模式数据', data)
      // this.backTypes = data
      this.$set(this, 'backTypes', data)
      // this.loading = false
    },

    /*
    设置默认值
     */
    setBackNodes(data) {
      var node = data.filter(item => item.isFirstNode === true)
      if (node[0]) {
        this.auditData.returnNodeId = node[0].id
      }
    },
    typeChange(val) {
      if (this.showAuditNext) {
        this.auditData.nextUserId = ''
        this.auditData.nextRoleId = ''
      }
    },
    submitAudit() {
      const auditCompExtendFormItemRef = this.$refs.extendFormItem
      let auditStatus = this.auditData.auditResult
      const showAuditCountersign = this.showAuditCountersign
      if (!this.showAuditRes) {
        auditStatus = this.countersignRes
      }
      // 审核状态映射表
      const auditStatusMap = {
        '退回修改': '0',
        '审核通过': showAuditCountersign ? '3' : '1',
        '加签': '2',
        '同意': '3',
        '不同意': '3', // 会签
        '转办': '4'
      }
      let mainFormVaild = false
      let extendFormVaild = this.$isEmpty(auditCompExtendFormItemRef)
      const successFunc = () => {
        if (!mainFormVaild || !extendFormVaild) return
        if (auditStatusMap[auditStatus] === '0') {
          this.backAudit()
        } else {
          this.doAudit(auditStatusMap[auditStatus])
        }
      }
      this.$refs['audit'].validate((valid) => {
        if (valid) {
          mainFormVaild = true
          successFunc()
        } else {
          mainFormVaild = false
        }
      })
      if (auditCompExtendFormItemRef) {
        const ref = auditCompExtendFormItemRef.ref
        auditCompExtendFormItemRef.$refs?.[ref]?.validate(
          async(extendValid) => {
            if (extendValid) {
              const validForm = auditCompExtendFormItemRef.validForm ? await auditCompExtendFormItemRef.validForm() : true
              extendFormVaild = validForm
              successFunc()
            } else {
              extendFormVaild = false
            }
          }
        )
      }
    }
  }
}
</script>

<style>
  .auditComp .el-input--small,.auditComp .el-radio__label {
    font-size: 14px;
  }
  .auditComp .auditCountersign .el-button{
    margin-top: -5px;
  }
  .auditComp .el-form .el-form-item .el-form-item__label, .auditComp .auditForm-result .el-radio__label {
    color: #303133;
    font-weight: 600;
  }
</style>
