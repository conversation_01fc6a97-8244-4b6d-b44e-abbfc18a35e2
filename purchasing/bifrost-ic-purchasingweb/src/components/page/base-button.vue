<template>
  <span class="toolbar-button">
    <el-button
      :type="bt.type"
      :icon="bt.icon"
      :plain="bt.plain"
      :size="bt.size"
      :disabled="bt.disabled"
      :loading="bt.loading"
      v-if="!bt.hasDropDown && !bt.isUpload"
      style="position: relative"
      @click="btClick(bt)"
      :style="isCustomButtonStyle(bt)"
    >
      {{ bt.text }}
      <span
        v-if="typeof bt.num === 'number'"
        class="number-text"
        style="
          position: absolute; top: -5px; right: -5px;
          display: flex; justify-content: center; align-items: center;
          border: 1px solid red; color: red; border-radius: 7px; font-size: 12px;
          width: auto; min-width: 14px; height: 14px; padding: 2px;">
        {{ bt.num }}
      </span>
    </el-button>

    <el-upload
      action
      class="button-upload"
      ref="uploadRef"
      :auto-upload="false"
      :show-file-list="false"
      :limit="bt.fileLimit"
      :multiple="bt.filemultiple"
      :disabled="bt.disabled"
      :on-error="fileUploadError"
      :on-change="(file, fileList)=>bt.fileOnChange(file, fileList, $refs.uploadRef)"
      :accept="bt.accept"
      v-if="!bt.hasDropDown && bt.isUpload"
    >
      <el-button
        :style="isCustomButtonStyle(bt)"
        :type="bt.type"
        :plain="bt.plain"
        :icon="bt.icon"
        :size="bt.size"
        :loading="bt.loading"
        :disabled="bt.disabled"
        >{{ bt.text }}</el-button
      >
    </el-upload>

    <el-dropdown
      :disabled="bt.disabled"
      v-if="bt.hasDropDown"
      @command="dropdownClick"
    >
      <el-button
        :icon="bt.icon"
        :plain="bt.plain"
        :size="bt.size"
        :loading="bt.loading"
        @click="btClick(bt)"
        :style="isCustomButtonStyle(bt)"
      >
        {{ bt.text }}<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="(item, index) in bt.dropDowns"
          :key="index"
          :command="item"
        >
         <i :class="item.icon"></i> {{ item.text }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </span>
</template>

<script>

export default {
  name: 'b-button',
  props: {
    bt: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttonsDisableData: {} // 按钮可用禁用数据，这个数据来自后端
    }
  },
  methods: {
    btClick(button, exHandleButtonBeforeClick) {
      this.$emit('btClick', button, exHandleButtonBeforeClick)
    },
    dropdownClick(dropdownItem) {
      this.$emit('dropdownClick', dropdownItem)
    },
    fileUploadError(res) {
      this.$message.error('文件上传错误，请重试')
    },
    isCustomButtonStyle({ mainButton }) {
      if (mainButton) {
        return {
          background: '#006CFF!important',
          border: '1.5px solid #006CFF !important',
          color: '#fff!important',
          borderRadius: '7px !important'
        };
      } else {
        return {
          background: '#fff!important',
          color: '#006CFF!important',
          border: '1.5px solid #006CFF !important',
          borderRadius: '7px !important'
        };
      }
    }
  },
}
</script>
<style lang="scss" scoped>
::v-deep {
  .el-button.is-plain:hover, .el-button.is-plain:focus,
  .el-button.is-plain:active {
    background: #fff !important;
    color: #006CFF !important;
    border: 1px solid #006CFF !important
  }
}
</style>
