<template>
  <el-dialog ref="baseEditDlgObj"
      v-if="dialogVisible"
      append-to-body
     :title="`${titlePrefix}${title}${titlePostfix}`"
     :visible.sync="dialogVisible"
     :width="`${dlgWidth}px`"
     :height="`${dlgHeight}px`"
     :close-on-click-modal="false"
     @close="onDialogClose">
    <div style="height: 100%">
      <slot name="bizContent"></slot>
      <div v-if="params.isEdit" style="text-align: center;margin-top: 20px" >
        <el-button class="btn-normal" icon="el-icon-d-arrow-left" @click="onDialogClose">返回</el-button>
        <el-button  ref="btSave" :loading="loading" class="btn-normal" type="primary" icon="el-icon-edit" @click="btEditSave">保存</el-button>
        <el-button v-if="params.isCusBtn" @click="clickIsCusBtn">查看已调用项目</el-button>
      </div>
      <div v-else style="text-align: center;margin-top: 20px" >
        <el-button class="btn-normal" icon="el-icon-d-arrow-left" @click="onDialogClose">返回</el-button>
        <slot name="aliasButton"></slot>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'base-edit-dlg',
  props: {
    newVo: { type: Function, default: () => {} }
  },
  data() {
    return {
      dialogVisible: false,
      title: '业务',
      titlePrefix: '',
      titlePostfix: '',
      dlgWidth: 0,
      dlgHeight: 0,
      params: { isEdit: true },
      dataVo: this.newVo(),
      loading: false
    }
  },
  methods: {
    show(params) {
      this.params = params
      // 审核加载业务详情时，需要指定初始化时不使用弹窗
      if (this.params.isNoDlg !== true) {
        this.dialogVisible = true
      }

      this.$nextTick(() => {
        const formComponent = this.getFormComponent()
        if (formComponent && formComponent.init) {
          formComponent.init(params)
        }
        // 防止修改或详情时，初始化后有校验的红框
        if (this.$parent.$refs['editForm'] && this.$parent.$refs['editForm'].rules) { // 默认编辑表单的ref=editForm
          this.$parent.$refs['editForm'].clearValidate()
        }
        if (this.$isNotEmpty(this.params.row) &&
          this.$isNotEmpty(this.params.row['子业务类型'])) {
          const subBizType = this.params.row['子业务类型']
          const bizFlowNode = this.params.row['业务流转节点']
          if (this.$isEmpty(subBizType)) {
            this.$message.error('当前是业务流转场景，但是不能获取到子业务类型')
            return
          }
          if (this.$isEmpty(bizFlowNode)) {
            this.$message.error('当前是业务流转场景，但是不能获取到业务流转节点')
            return
          }

          this.params.apiKeySaveVo = 'WFSAVE' +
            '&dataType=' + subBizType +
            '&业务流转节点=' + bizFlowNode
        }

        if (this.params.isEdit === undefined) {
          this.params.isEdit = true
        }

        // 没有指定不需要弹框，表明当前是编辑场景，需要检查apiKeySaveVo
        if (this.$isEmpty(this.params.apiKeySaveVo) && this.params.isEdit) {
          this.$message.error('apiKeySaveVo不能为空')
          return
        }

        if (this.$isEmpty(this.params.apiKeySelectVo)) {
          this.$message.error('apiKeySelectVo不能为空')
          return
        }

        if (this.params.title) {
          this.title = this.params.title
        }

        this.dlgWidth = 960
        if (this.params.dlgWidth) {
          this.dlgWidth = this.params.dlgWidth
        }

        this.dlgHeight = 700
        if (this.params.dlgHeight) {
          this.dlgHeight = this.params.dlgHeight
        }

        this.titlePostfix = ''
        this.titlePrefix = this.params.titlePrefix
        if (this.$isEmpty(this.titlePrefix)) {
          if (this.$isEmpty(this.params.row)) {
            this.titlePrefix = '新增'
          } else {
            this.titlePrefix = this.params.isEdit ? '修改' : ''
          }
        }

        if (!this.params.isEdit) {
          this.titlePrefix = ''
          this.titlePostfix = '详情'
        }

        if (this.params.isNoDlg !== true) {
          this.$setDlgSize(this, 'baseEditDlgObj', this.dlgWidth, this.dlgHeight)
        }

        this.initVo(() => {
          this.$nextTick(() => {
            if (this.params.initVoCallback) {
              this.params.initVoCallback(this)
            }
          })
        })
      })
    },
    initVo(callback) {
      this.setDataVo(this.newVo())

      let isBizFlowParentSub = false
      var rowId = this.params.rowId4SelectVo
      if (this.$isEmpty(rowId) && this.$isNotEmpty(this.params.row)) {
        // 业务流转主子业务场景
        if (this.$isNotEmpty(this.params.row['子业务类型'])) {
          rowId = this.params.row['子业务ID']
          isBizFlowParentSub = true
        } else {
          rowId = this.$getRowId(this.params.row)
        }
      }

      if (this.$isNotEmpty(rowId) ||
        this.params.selectVoWhenInsert) {
        const paramsEx = { ids: rowId, isEdit: this.params.isEdit }
        const params4SelectVo = this.params.params4SelectVo
        if (this.$isNotEmpty(params4SelectVo)) {
          Object.assign(paramsEx, params4SelectVo)
        }
        if (isBizFlowParentSub) {
          paramsEx['主业务ID'] = this.$getRowId(this.params.row)
        }

        // 这个情况是后端的审核列表里已经填充了主业务ID
        if (this.$isNotEmpty(this.params.row)) {
          const mainBizId = this.params.row['主业务ID']
          if (this.$isNotEmpty(mainBizId)) {
            paramsEx['主业务ID'] = mainBizId
          }
        }
        this.$callApiParams(
          this.params.apiKeySelectVo,
          paramsEx,
          result => {
            this.setDataVo(result.data)
            callback()
            // 防止修改或详情时，初始化后有校验的红框
            if (this.$parent.$refs['editForm']) { // 默认编辑表单的ref=editForm
              this.$parent.$refs['editForm'].clearValidate()
            }

            return true
          })
      } else {
        callback()
      }
    },
    setDataVo(vo) {
      if (vo) {
        this.dataVo = vo

        // 外部业务组件绑定vo
        const formComponent = this.getFormComponent()
        if (formComponent) {
          if (formComponent.dataVo) {
            formComponent.dataVo = vo
          }
        } else if (this.$parent.dataVo) {
          this.$parent.dataVo = this.dataVo
        } else if (this.$parent.setDataVo) {
          this.$parent.setDataVo(vo)
        }
      }
    },
    // 这个方法主要针对业务编辑弹框编辑表单本身抽象出一个组件时，这个组件的ref属性
    // 必须baseEditDlgFormComponent。这个抽象出来的组件，可以作为审核时的内容组件。
    // 获取单独设置getFormComponent()来返回组件对象
    clickIsCusBtn() {
      this.$emit('clickIsCusBtn')
    },
    getFormComponent() {
      if (this.params.getFormComponent) {
        // 这个里提供给base-crud中自行指定填单组件
        return this.params.getFormComponent()
      } else if (this.$parent) {
        return this.$parent.$refs['baseEditDlgFormComponent']
      }
    },
    async btEditSave() {
      let isValueFlag = false
      if (this.$parent.$refs['editForm'] && this.$parent.$refs['editForm'].rules) {
        this.$parent.$refs['editForm'].validate((valid) => {
          if (!valid) {
            isValueFlag = true
          }
        })
      }
      if (isValueFlag) {
        this.$message.warning('请填写必填项!')
        return
      }
      if (this.params.beforeSave) {
        this.params.beforeSave(this)
      }

      const callbackSuccess = () => {
        // this.$refs.btSave.loading = false
        this.loading = false
        this.dialogVisible = false
        if (this.params.reloadTable) {
          this.params.reloadTable()
        } else {
          this.$reInit(this.$parent.$parent)
        }
      }

      const callbackFailed = (result) => {
        // this.$refs.btSave.loading = false
        this.loading = false
        if (this.$parent.$refs['editForm']) { // 默认编辑表单的ref=editForm
          this.$parent.$refs['editForm'].validate()
        }

        if (this.params.saveFailedCallback) {
          return this.params.saveFailedCallback(result)
        }
      }

      let saveParams
      if (this.params.getSaveParams) {
        saveParams = this.params.getSaveParams(this)
      }
      // this.$refs.btSave.loading = true
      this.loading = true
      if (this.$isEmpty(saveParams)) {
        let saveVo = this.dataVo
        if (this.params.getSaveVo) {
          saveVo = this.params.getSaveVo(this)
        }
        // // 针对多选处理
        // if(this.params.apiKeySaveVo === 'savePurchaseActuator' && Array.isArray(saveVo?.procurementMethodBizid)){
        //  saveVo.procurementMethodBizid = saveVo.procurementMethodBizid.join(',') || ''
        // }
        this.$callApi(
          this.params.apiKeySaveVo, saveVo, callbackSuccess, callbackFailed, { isSave: false })
        // 2024327GleenLey
        // 这里为什么要加上 {isSave:false}
        // 是因为二次请求回调回出现阻塞 导致无法获取到loading状态 阻塞确定保存的请求
      } else {
        this.$callApiParams(
          this.params.apiKeySaveVo, saveParams, callbackSuccess, callbackFailed, { isSave: false })
      }
    },
    onDialogClose() {
      // 弹窗关闭时的回调
      if (this.params.onDialogClose) {
        this.params.onDialogClose()
      }
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss">
</style>
