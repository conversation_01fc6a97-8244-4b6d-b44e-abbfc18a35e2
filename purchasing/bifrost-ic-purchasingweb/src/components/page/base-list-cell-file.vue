<template>
  <div class="base-list-cell-file-block">
    <el-upload
      ref="btUploadEl"
      v-if="isEdit"
      action="#"
      :show-file-list="false"
      :limit="1"
      :http-request="btUploadClicked"
      :before-upload="beforeUpload"
      accept=".doc,.docx,.docm,.xls,.xlsx,.ppt,.pptx,.pdf,.jpg,.png,.ofd">
      <el-button ref="btUpload" type="text">上传</el-button>
    </el-upload>
    <el-button type="text" ref="btPreview" @click="btPreviewClicked"
               :disabled="btPreviewDisabled">预览</el-button>
    <el-button type="text" ref="btRemove" @click="btRemoveClicked"
               :disabled="btRemoveDisabled" v-if="isEdit">删除</el-button>
    <file-view ref="fileView"/>
  </div>
</template>

<script>
import FileView from '../fileview/file-view'
export default {
  name: 'base-list-cell-file',
  components: { FileView },
  props: {
    value: {
      type: String,
      default: '',
      require: true
    },
    bizId: {
      type: String,
      default: ''
    },
    fileSource: {
      type: String,
      default: '普通附件'
    },
    isEdit: {
      type: Boolean,
      default: true,
      require: true
    }
  },
  data() {
    return {
      isUploading: false,
      currentValue: this.value
    }
  },
  watch: {
    value(newVal) {
      this.currentValue = newVal
    }
  },
  computed: {
    btPreviewDisabled() {
      return this.$isEmpty(this.currentValue) || this.isUploading
    },
    btRemoveDisabled() {
      return this.$isEmpty(this.currentValue) || this.isUploading
    }
  },
  methods: {
    changeValue(value) {
      this.currentValue = value
      this.$emit('input', this.currentValue)
    },
    setIsUploading(uploading) {
      this.$refs.btUpload.loading = uploading
      this.isUploading = uploading
    },
    beforeUpload(v) {
      this.setIsUploading(true)
      if ((v.size / 1024 / 1024) > 20) {
        this.setIsUploading(false)
        this.$message.error('文件不能大于20MB')
        return false
      }
      return true
    },
    btUploadClicked(v) {
      this.$refs.btUploadEl.clearFiles()

      const formData = new FormData()
      formData.append('files', v.file, v.file.name)
      formData.append('path', '')// 文件存储路径
      formData.append('subId', '')// 业务系统编码
      formData.append('typeCode', this.fileSource)// 类型编码
      formData.append('bizCode', 'ZZZS')// 模块编码
      formData.append('bizCodeName', this.fileSource)// 模块名称
      formData.append('bizTblName', this.fileSource)// 业务表名称
      formData.append('bizId', this.bizId)// 业务记录编码,业务表主键ID
      formData.append('isEsSearch', 'true')// 是否全文检索
      formData.append('source', this.fileSource)// 来源
      this.$callApi('uploadAttachment', formData,
        result => {
          this.setIsUploading(false)
          this.$nextTick(() => {
            this.changeValue(result.data.attList[0].attId)
          })
        }, result => {
          this.setIsUploading(false)
        })
    },
    btPreviewClicked() {
      this.$refs.fileView.open({ fileIds: [this.currentValue] })
    },
    btRemoveClicked() {
      this.$refs.btUploadEl.clearFiles()
      this.changeValue('')
    }
  }
}
</script>
<style>
  .base-list-cell-file-block div { display: inline-block; }
</style>
