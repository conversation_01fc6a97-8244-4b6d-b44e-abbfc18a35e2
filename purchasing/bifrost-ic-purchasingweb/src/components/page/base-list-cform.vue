<template>
  <div style="height: 100%">
    <b-list-wf-apply ref="baseListWfApply" v-if="isApply" v-show="!openCanvas" @handleSearch="handleSearch">
      <template #dbColLeft>
        <slot name="dbColLeft"/>
      </template>
      <template #contentHeader>
        <slot name="contentHeader"/>
      </template>
    </b-list-wf-apply>
    <b-list-wf-audit ref="baseListWfAudit" v-if="!isApply" v-show="!openCanvas" :hideTicketButton="hideTicketButton"/>
    <div
      class="formCanvasRuntime common-page"
      :style="formCanvasRuntimeExStyle"
      v-if="openCanvas">
      <div class="cformTabSave flex-1" v-if="isUsingCformTab">
        <form-tab-save ref="cformTabSave">
          <template #headExtendContent>
            <slot name="headExtendContent"/>
          </template>
        </form-tab-save>
      </div>
      <div class="flex-1" style="height: 100%;" v-else-if="isUsingCformBlock">
        <block-view ref="blockView" :saveBefore="saveBefore"/>
      </div>
      <form-canvas class="flex-1" ref="formCanvas"
          v-else-if="!isUsingCformTab && !isMultipleEditTabs && !isUsingCformBlock">
        <template #content>
          <slot name="content"/>
        </template>
      </form-canvas>
      <div class="flex-1" style="display: flex;flex-direction: column;height: 100%;"
           v-else-if="!isUsingCformTab && !isUsingCformBlock && isMultipleEditTabs" >
        <el-tabs
          v-model="formEditTabsActiveName"
          style="flex:1;overflow: hidden;"
          @tab-click="formEditTabClick"
          :class="formEditTabsClass"
          ref="formEditTabs"
        >
          <el-tab-pane
            class="formEditTab1st" ref="formEditTab1st"
            style="height: 100%;" :label="formEditTabLabel0"
            :name="formEditTabName0" v-if="formEditTabsVisible0">
            <form-canvas ref="formCanvas" @changeBtnStatus="changeBtnStatus"/>
          </el-tab-pane>

          <el-tab-pane
            ref="formEditTab1"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel1"
            :name="formEditTabName1"
            v-if="formEditTabsVisible1"
            key="formEditTabLabel1"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent1"
                :is="formEditTabComponentId1"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
            ref="formEditTab2"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel2"
            :name="formEditTabName2"
            v-if="formEditTabsVisible2"
            key="formEditTabLabel2"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent2"
                :is="formEditTabComponentId2"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
            ref="formEditTab3"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel3"
            :name="formEditTabName3"
            v-if="formEditTabsVisible3"
            key="formEditTabLabel3"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent3"
                :is="formEditTabComponentId3"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
            ref="formEditTab4"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel4"
            :name="formEditTabName4"
            v-if="formEditTabsVisible4"
            key="formEditTabLabel4"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent4"
                :is="formEditTabComponentId4"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
            ref="formEditTab5"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel5"
            :name="formEditTabName5"
            v-if="formEditTabsVisible5"
            key="formEditTabLabel5"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent5"
                :is="formEditTabComponentId5"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
            ref="formEditTab6"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel6"
            :name="formEditTabName6"
            v-if="formEditTabsVisible6"
            key="formEditTabLabel6"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent6"
                :is="formEditTabComponentId6"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
            ref="formEditTab7"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel7"
            :name="formEditTabName7"
            v-if="formEditTabsVisible7"
            key="formEditTabLabel7"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent7"
                :is="formEditTabComponentId7"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
            ref="formEditTab8"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel8"
            :name="formEditTabName8"
            v-if="formEditTabsVisible8"
            key="formEditTabLabel8"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent8"
                :is="formEditTabComponentId8"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
            ref="formEditTab9"
            style="height: 100%;overflow: auto;"
            :label="formEditTabLabel9"
            :name="formEditTabName9"
            v-if="formEditTabsVisible9"
            key="formEditTabLabel9"
          >
            <keep-alive>
              <component
                ref="formEditTabComponent9"
                :is="formEditTabComponentId9"
              />
            </keep-alive>
          </el-tab-pane>
        </el-tabs>
        <el-footer>
          <el-button class="btn-normal" icon="el-icon-d-arrow-left" @click="doBtEditBack">返回</el-button>
          <el-button ref="btDraft" class="btn-normal" icon="el-icon-tickets"
                     @click="doBtEditDraft" v-show="isShowBtDraft">存草稿</el-button>
          <el-button class="btn-normal" type="primary" icon="el-icon-edit" :disabled="saveBtnLoading" @click="doBtEditSave">保存</el-button>
        </el-footer>
      </div>
      <div v-if="showMainPanel|| blockTabs.length" class="flex-row-center">
        <div class="retract-block" @click="handlerCollapse">
          <i class="el-icon-arrow-left" v-if="blockTabCollapsed" style="font-size:13px;cursor:pointer;"></i>
          <i class="el-icon-arrow-right" v-else style="font-size:13px;cursor:pointer;"></i>
        </div>
      </div>
      <div v-show="!blockTabCollapsed" class="right-block-tab">
        <blockTab
          ref="blockTabRef"
          style="width: 390px"
          :tabs.sync="blockTabs"
          :currentRow="currentRow"
          :selectedData='selectedData'
          :showMainPanel.sync="showMainPanel"
          :formType="formType"
          :metaName="metaName"
          :metaId="metaId"
          :dataType="dataType"
          :nodeId="currentNodeId"
          :billId="billId"
          @initCallBack="initCallBack"
          >
        </blockTab>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import BListWfApply from './base-list-wf-apply'
import BListWfAudit from './base-list-wf-audit'
import FormCanvas from '../cform/form-canvas'
import { store } from '@/utils/sunmei-data'
import BlockView from '../block/block-view'

export default {
  name: 'base-list-cform',
  components: { BlockView, FormCanvas, BListWfAudit, BListWfApply },
  props: {
    needPadding: { type: Boolean, default: true } // 是否需要边距
  },
  provide() {
    return {
      initBlockView: (exInitData) => this.initBlockView(exInitData),
      getConference: (data) => this.getConference(data)
    }
  },
  watch: {
    openCanvas: function(newValue) {
      if (!newValue) {
        this.$refs.formCanvas && this.$refs.formCanvas.$refs.formFormat.removeEventListeners && this.$refs.formCanvas.$refs.formFormat.removeEventListeners()
      }
    }
  },
  data() {
    return {
      selectedData: [],
      currentRow: {},
      blockTabs: [],
      dataType: '',
      billId: '',
      currentNodeId: '',
      metaName: '',
      metaId: '',
      showMainPanel: true,
      isApply: true,
      openCanvas: false,
      needProcess: false,
      isShowBtDraft: false,
      isShowBtDraftOutside: false,
      formType: '',
      formTypeVoExtraData: {},
      getFormCanvasExTabs: undefined,
      hasMultipleFormCanvasTabs: false,
      handleDataVoBeforeSave: undefined, // 保存之前额外处理DataVo对象
      jumpToSaveFormData: undefined,
      firstBtNewDropDown: undefined,
      customSaveAction: undefined, // 自定义保存动作
      btAddNoRef: false, // 设置了refFormParams，btAddNoRef=true表明新增按钮不使用表单参照
      refFormParams: undefined, // 制单初始参照机制
      refFormSelectedData: [], // 制单初始参照选中的数据
      dataVo: {},
      formCanvasMeta: undefined,
      showHeaderFillMode: true, // 制单时是否显示规则表单的标题
      showHeaderAuditMode: true, // 审核模式时是否显示规则表单的标题
      showRegularFormHeader: false, // 多tab制单时可特别指定不隐藏规则表单标题
      isUsingCformTab: false, // 制单使用CformTab机制
      isUsingCformBlock: false, // 制单使用区块制单机制
      noTabWhenSaveForm: false, // 页面强制不使用CformTab和多tab机制，只是纯表单
      exHandleExParamsFormTabSave: undefined, // 额外处理CfromTab参数初始化
      syncAuditDataOld: undefined,
      stateItem: '',
      // 如果是制单多tab模式，则formEditTabs[0]=主表单tab标题，formEditTabs[x]=其他tab组件名称
      formEditTabs: [],
      formEditTabsExcludeMain: [], // 除去第一个表单后的其他的tab
      formEditTabsIndexes: {}, // tabKey对应索引映射表
      formEditTabsComponents: {}, // tabKey对应组件映射表
      formEditTabsObjects: {}, // tabKey对应el-tab-pane对象映射表
      formEditTabsActiveName: '',
      formRegularContainerPaddingTop: 0, // 制单多tab时规则表单顶部间距
      isRefFormNameSelect: false,
      formEditTabsClass: 'formEditTabs',

      // 制单多tab时，涉及动态设置tab可见性时，vue的双向绑定不能使
      // 用数组实现可见性动态设置，所以使用预置支持10个固定tab的方案
      formEditTabsVisible0: false,
      formEditTabsVisible1: false,
      formEditTabsVisible2: false,
      formEditTabsVisible3: false,
      formEditTabsVisible4: false,
      formEditTabsVisible5: false,
      formEditTabsVisible6: false,
      formEditTabsVisible7: false,
      formEditTabsVisible8: false,
      formEditTabsVisible9: false,

      formEditTabComponentId1: '',
      formEditTabComponentId2: '',
      formEditTabComponentId3: '',
      formEditTabComponentId4: '',
      formEditTabComponentId5: '',
      formEditTabComponentId6: '',
      formEditTabComponentId7: '',
      formEditTabComponentId8: '',
      formEditTabComponentId9: '',

      formEditTabName0: '',
      formEditTabName1: '',
      formEditTabName2: '',
      formEditTabName3: '',
      formEditTabName4: '',
      formEditTabName5: '',
      formEditTabName6: '',
      formEditTabName7: '',
      formEditTabName8: '',
      formEditTabName9: '',

      formEditTabLabel0: '',
      formEditTabLabel1: '',
      formEditTabLabel2: '',
      formEditTabLabel3: '',
      formEditTabLabel4: '',
      formEditTabLabel5: '',
      formEditTabLabel6: '',
      formEditTabLabel7: '',
      formEditTabLabel8: '',
      formEditTabLabel9: '',
      queryParams: {},
      extData: {},
      hideDetailTabs: undefined, // 隐藏详情tabs
      saveBtnLoading: true,
      radioList: { type: Array, default: () => [] },
      hideTicketButton: false,
      blockTabCollapsed: true,
      isShowTabs: false // 是否展示区块tabs 默认不展示
    }
  },
  mounted() {
    // 如果父组件实现了initParams方法，自动初始化申请列表
    if (this.$parent.initParams) {
      const initParams = this.$parent.initParams?.()
      if (this.$isEmpty(initParams)) {
        this.$message.error('initParams方法返回空数据')
        return
      }
      this.init(initParams)
    }
  },
  computed: {
    isMultipleEditTabs() {
      if (this.noTabWhenSaveForm === true) {
        return false
      }
      return this.formEditTabs !== undefined && this.formEditTabs.length > 1
    },
    isNotMultipleEditTabsAndNotIsUsingCformTab() { // 不是静态多tab，也不是动态多tab
      return !this.isMultipleEditTabs && !this.isUsingCformTab
    },
    isNotMultipleEditTabsAndIsUsingCformTab() { // 是动态多tab
      return !this.isMultipleEditTabs && this.isUsingCformTab
    },
    formCanvasRuntimeExStyle() {
      var exStyle = 'height: 100%;'
      if (this.needPadding) {
        exStyle = exStyle + 'padding: 12px 20px 20px 20px;'
        exStyle = this.isUsingCformTab ? exStyle + ';padding: 12px 20px 20px 20px;' : exStyle
      }
      return exStyle
    }
  },
  methods: {
    getConference(data) {
      this.selectedData = data
    },
    changeBtnStatus(boolean) {
      if (this.openCanvas) {
        this.saveBtnLoading = boolean
      }
    },
    initCallBack(data) {
      if (this.$isNotEmpty(data.auditFiles)) {
        this.blockTabs = [{
          label: '规章制度',
          name: '规章制度',
          component: 'ruleBlock'
        }]
      } else {
        this.blockTabs = []
      }
    },
    handleSearch(param) {
      this.$emit('handleSearch', param)
    },
    init(initParams) {
      this.$parent.isListObj = true
      this.isShowTabs = initParams.isShowTabs || false
      initParams.buttons = initParams.buttons || []
      if (!this.$isNotEmpty(initParams.params.FORM_TYPE_eq)) {
        this.$message.error('FORM_TYPE_eq不能为空')
        return
      }
      this.extData = initParams.extData || {}
      this.stateItem = initParams.params.stateItem
      // 其他业务跳转到表单列表来制单，此时不加载列表
      this.jumpToSaveFormData = this.$getJumpToSaveFormData()
      if (this.$isNotEmpty(this.jumpToSaveFormData)) {
        initParams.loadTable = false
        initParams.isShowBtDraft = this.jumpToSaveFormData.isShowBtDraft
        this.firstBtNewDropDown = this.jumpToSaveFormData.dropDown
      }

      if (initParams.isShowBtDraft) {
        this.isShowBtDraftOutside = initParams.isShowBtDraft
      }

      this.radioList = initParams.radioList
      this.noTabWhenSaveForm = initParams.noTabWhenSaveForm || false
      this.formType = initParams.params.FORM_TYPE_eq // 表单类型
      this.exHandleExParamsFormTabSave = initParams.exHandleExParamsFormTabSave
      this.getFormCanvasExTabs = initParams.getFormCanvasExTabs
      this.handleDataVoBeforeSave = initParams.handleDataVoBeforeSave
      this.refFormParams = initParams.refFormParams || {}
      this.isRefFormNameSelect = this.refFormParams.isRefFormNameSelect || false
      this.customSaveAction = initParams.customSaveAction
      this.btAddNoRef = this.refFormParams.btAddNoRef || false
      this.queryParams = initParams.queryParams || {}
      this.formRegularContainerPaddingTop =
        initParams.formRegularContainerPaddingTop || 0

      this.showRegularFormHeader = initParams.showRegularFormHeader
      if (this.showRegularFormHeader === true) {
        this.formEditTabsClass += ' showRegularFormHeader'
      }

      // 标题填充模式
      if (initParams.showHeaderFillMode !== undefined) {
        this.showHeaderFillMode = initParams.showHeaderFillMode
      }

      // 标题审核模式
      if (initParams.showHeaderAuditMode !== undefined) {
        this.showHeaderAuditMode = initParams.showHeaderAuditMode
      }

      if (this.$isEmpty(initParams.noExcelExport)) {
        // 表单默认有导出按钮
        initParams.exportExcelName = this.formType
      }
      if (
        this.$isEmpty(initParams.noExcelImp) ||
        initParams.noExcelImp === false
      ) {
        // 表单有导入按钮
        var impExcelKey = initParams.params.impExcelKey || '表单导入'
        initParams.fileName = this.formType
        initParams.params.impExcelKey = impExcelKey
        initParams.params.tableColumn = this.formType
        initParams.params.formType = this.formType
      }

      initParams.isApply = this.$isNotEmpty(initParams.isApply)
        ? initParams.isApply
        : true // 默认是申请列表
      this.isApply = initParams.isApply
      if (!this.isApply) {
        // 审核待办时，审核详情的组件是表单画布，或是CformTab组件
        var getMetaIdTab = (baseListWf) => {
          // 审核待办时 隐藏底部返回确定按钮
          $('#form-edit-detail-el-footer').hide()
          if (baseListWf && baseListWf.currentRow) {
            return baseListWf.currentRow.metaIdTab
          }
        }

        // 以下auditComponentName和callbackInitAuditDetailComponent
        // 处理审核主界面表单详细内容的填充方式，兼容常规表单和使用CformTab的表单
        initParams.auditComponentName =
          (baseListWf, callbackAfterComponentName) => {
            baseListWf.isUsingCformTab = false
            baseListWf.isNoAuditFileOnTabAnyway = false
            baseListWf.useFormDetailsAsAuditTabs = true
            var metaIdTab = getMetaIdTab(baseListWf)
            var compName = 'form-canvas'
            if (this.$isNotEmpty(metaIdTab)) {
              compName = 'form-tab-save'
              baseListWf.isUsingCformTab = true
              baseListWf.isNoAuditFileOnTabAnyway = true
              baseListWf.useFormDetailsAsAuditTabs = false
            }
            callbackAfterComponentName(compName)
          }

        initParams.callbackInitAuditDetailComponent = (baseListWf, callback) => {
          var billid = baseListWf.billId
          if (baseListWf.row.cformId) {
            billid = baseListWf.row.cformId
          }
          if (this.syncAuditDataOld) {
            baseListWf.syncAuditData = this.syncAuditDataOld
          }

          var metaIdTab = getMetaIdTab(baseListWf)
          if (this.$isNotEmpty(metaIdTab)) { // 处理审核时使用CformTab
            var exInitDataCformTab = this.initCformTabParamsBy(baseListWf.currentRow)
            exInitDataCformTab.isShowAuditFileTabWhenInit =
              baseListWf.isShowAuditFileTabWhenInit
            exInitDataCformTab.nodeName = ''
            exInitDataCformTab.isAudit = true
            if (this.exHandleExParamsFormTabSave) {
              this.exHandleExParamsFormTabSave(exInitDataCformTab)
            }
            var exParams = { exInitDataCformTab: exInitDataCformTab }
            baseListWf.$refs.auditDetailComponent.init(undefined, exParams)

            // 需要等待审核数据从服务器取回后，设置附件组件的nodeName
            this.syncAuditDataOld = baseListWf.syncAuditData
            baseListWf.syncAuditData = (baseListWf) => {
              if (baseListWf.$refs.auditDetailComponent.setNodeNameWhenAudit) {
                var auditData = baseListWf.auditData || {}
                var nodeName = auditData.nodeName || ''
                baseListWf.$refs.auditDetailComponent.setNodeNameWhenAudit(nodeName)
              }
            }
            return
          }

          var initFormExData = {}
          initFormExData.baseListFormObj = baseListWf
          initFormExData.showHeader = this.showHeaderAuditMode
          initFormExData.isMultipleEditTabs = baseListWf.isMultipleAuditTabs

          baseListWf.$refs.auditDetailComponent.initByVersionDataId(
            '',
            billid,
            '审核',
            (meta, dataVo) => {
              if (typeof callback === 'function') {
                // 构建auditTabParams给多tab审核时，初始化详情tab使用
                var exData = {
                  auditTabParams: { dataVo: dataVo, billId: billid }
                }
                callback('audit-extend-' + this.formType, exData, initFormExData?.jumpToSaveFormData)
              }
            },
            undefined,
            undefined,
            this.getFormCanvasExTabs,
            initFormExData
          )
        }

        // 审核编辑获取表单VO
        initParams.getVo4AuditEditSave = (baseListWf) => {
          return baseListWf.$refs.auditDetailComponent?.getDataToSave()
        }
      }

      // 先保存参数以便后续重新初始化加载
      initParams.beforeReload = () => {
        this.openCanvas = false
      }
      this.$saveInitParams(this, initParams)

      // 这样的处理可以使得父页面不用调用$saveInitParams方法，
      // 以及不需要一定写有init方法
      if (this.$isEmpty(this.$parent.init)) {
        this.$parent.init = (ps) => {
          this.init(ps)
        }
      }
      this.$saveInitParams(this.$parent, initParams)

      initParams.params = initParams.params || {}
      initParams.params.actionKey = 'selectWfList'
      if (!initParams.params.dataApiKey) {
        initParams.params.dataApiKey = 'wfActionCformDataEntity'
      }
      if (!initParams.params.dataType) {
        initParams.params.dataType = 'CformDataEntity'
      }
      this.dataType = initParams.params.dataType
      if (!initParams.params.needProcessApi) {
        initParams.params.needProcessApi = 'selectFormTypeVo'
      }
      initParams.params.needProcessParams = {
        ...initParams.params.needProcessParams,
        FORM_TYPE_eq: initParams.params.FORM_TYPE_eq,
        bizName: this.$isNotEmpty(initParams.bizName) ? initParams.bizName : initParams.params.FORM_TYPE_eq,
        menuId: store.get('menuData').getMenuId() // 菜单id
      }

      // 业务可能已经指定了的修改按钮响应
      if (initParams.btModifyClick === undefined) {
        initParams.btModifyClick = {
          click: (row) => {
            if (row.formType) {
              this.btModifyClick(row)
            }
          }
        }
      }

      if (!initParams.btDetailClick) {
        initParams.btDetailClick = {
          click: (row) => {
            this.btDetailClick(row)
          }
        }
      }
      initParams.btPrint = {
        click: (row) => {
          this.$showPrint(row.id, row.formType)
        }
      }
      initParams.btExportWps = {
        click: (row) => {
          const onPrintDlgClose = () => {
            const baseListWfAudit = this.$refs.baseListWfAudit
            if (!baseListWfAudit) {
              return
            }
            let isFreedomForm = false
            if (baseListWfAudit.isShowDetail && !baseListWfAudit.isUsingBlockView) {
              isFreedomForm = baseListWfAudit.$refs.auditDetailComponent.isFreedomForm
            }
            if (isFreedomForm && baseListWfAudit.callbackInitAuditDetailComponent) {
              baseListWfAudit.callbackInitAuditDetailComponent(baseListWfAudit)
            }
          }
          this.$showExportWps({
            printId: row.id,
            onPrintDlgClose
          }, row.formType, '', initParams.button)
        }
      }

      // 表单列表按钮禁用数据接口设置
      initParams.refreshButtonsDisableDataKey = 'refreshButtonsDisableDataForm'
      var tempParamsBefore = initParams.refreshButtonsDisableDataParams || {}
      initParams.refreshButtonsDisableDataParams =
        Object.assign(tempParamsBefore, {
          FORM_TYPE_eq: initParams.params.FORM_TYPE_eq,
          isSkipVer: this.$isNotEmpty(initParams.isSkipVer) ? initParams.isSkipVer : true
        })

      // 初始化远程获取数据后，表单环境进行额外初始化处理
      // 1、记录是否启用流程，对应tab的处理
      // 2、同时处理新增时是否使用下拉菜单
      var needProcessCallBackEx = initParams.needProcessCallBackEx
      initParams.needProcessCallBack = (result) => {
        if (needProcessCallBackEx) {
          needProcessCallBackEx(result, initParams)
        }
        // 是否显示缺票提醒
        if (result.data && (!result.data.isBillMissing || result.data.isBillMissing === '否')) {
          if (initParams.isApply) {
            if (initParams.hideButtons) {
              initParams.hideButtons()
            }
          } else {
            this.hideTicketButton = true
          }
        }
        // 后端可覆盖页面设置的noTabWhenSaveForm
        if (result.attributes.noTabWhenSaveForm !== undefined) {
          this.noTabWhenSaveForm = result.attributes.noTabWhenSaveForm
        }

        if (initParams.isMultipleTabsTab4FillForm === true) { // 初始化tab
          this.initTabsMeta(initParams)
          // 由后端控制详情隐藏按钮
          const hideDetailTabs = result.attributes['表单隐藏tab']
          if (this.$isNotEmpty(hideDetailTabs)) {
            this.hideDetailTabs = hideDetailTabs
          }
        }

        this.needProcess = result.data.needProcess
        this.formTypeVoExtraData = result.data.extraData

        // 这里click的处理，对应base-page中的bt.click = bt.dropDowns[0].click
        initParams.needProcess = result.data.needProcess
        initParams.btNewDropDowns = result.data.dropdownMenus
        if (this.$isEmpty(initParams.btNewDropDowns)) {
          initParams.btAddClick = {
            click: (bt) => {
              this.$message.error({ dangerouslyUseHTMLString: true,
                message: '表单数据为空，请确认：<br>1.表单是否启用<br>2.关联流程是否启用<br>3.是否拥有流程申请的权限' })
            }
          }
        } else {
          const _this = this
          initParams.btNewDropDowns.forEach((item) => {
            item.click = (bt) => {
              _this.btAddClick(item, bt)
            }
            if (_this.jumpToSaveFormData?.formVersionId === item?.metaVersionId) {
              _this.firstBtNewDropDown = item
            }
            if (_this.firstBtNewDropDown === undefined) {
              _this.firstBtNewDropDown = item
            }
          })
          this.goSaveBillDirectIf() // 需要的话直接跳转到制单界面
        }
      }

      this.$nextTick(() => {
        var baseListWf = initParams.isApply
          ? this.$refs.baseListWfApply
          : this.$refs.baseListWfAudit
        if (baseListWf) {
          baseListWf.init(initParams)
        }
        this.initBtEditEvents()
      })
    },
    initBtEditEvents() {
      var this_ = this
      this.$onEvent(this, {
        btEditBack(status) {
          this_.doBtEditBack(status)
        },
        btEditDraft(dataVo) {
          this_.doBtEditDraft(undefined, dataVo)
        },
        btEditSave(dataVo) {
          this_.doBtEditSave(undefined, dataVo)
        }
      })
    },
    initTabsMeta(initParams) { // 初始tab数据
      this.formEditTabs = initParams.formEditTabs || []
      this.formEditTabsExcludeMain = []
      this.formEditTabsIndexes = {}

      // 是否为多重选项编辑卡
      if (this.isMultipleEditTabs) {
        this.formEditTabName0 = this.formEditTabs[0]
        this.formEditTabLabel0 = this.formEditTabs[0]
        if (this.formEditTabName0.indexOf(':') > -1) {
          var tabTokens = this.formEditTabName0.split(':')
          this.formEditTabName0 = tabTokens[0]
          this.formEditTabLabel0 = tabTokens[1]
        }

        this.formEditTabsIndexes[this.formEditTabName0] = 0
        for (let i = 1; i < this.formEditTabs.length; i++) {
          var tabName = this.formEditTabs[i]
          var tabLabel = tabName
          if (tabName.indexOf(':') > -1) {
            tabTokens = tabName.split(':')
            tabName = tabTokens[0]
            tabLabel = tabTokens[1]
          }
          this.formEditTabsIndexes[tabName] = i
          this.formEditTabsExcludeMain.push({ name: tabName, label: tabLabel })
        }
      }
    },
    /* async formEditTabClick(tab) {
      // 防止多tab制单切换页签时，自由表单界面位置大小出现错乱
      if (this.formEditTabName0 === tab.name &&
        this.$refs.formCanvas &&
        this.$refs.formCanvas.isFreedomForm) {
        this.$refs.formCanvas.$refs.formFormat.resizeClient()
      }
      var comp = this.formEditTabsComponents[tab.name]
      if (comp && comp.showAfter) {
        var dataVvo = await this.$refs.formCanvas.getDataToSave()
        comp.showAfter(dataVvo)
      }

      var tabsClass = 'formEditTabs formEditTabsCard'
      var index = this.formEditTabsIndexes[tab.name]
      if (index === 0 || (comp && comp.isFormEditTabsCard === false)) {
        tabsClass = 'formEditTabs'
      }
      if (this.showRegularFormHeader === true) {
        tabsClass += ' showRegularFormHeader'
      }

      this.formEditTabsClass = tabsClass
    },*/
    formEditTabClick(tab) { // 比起上面的方法中的await更严谨
      // 防止多tab制单切换页签时，自由表单界面位置大小出现错乱
      if (this.formEditTabName0 === tab.name &&
        this.$refs.formCanvas &&
        this.$refs.formCanvas.isFreedomForm) {
        this.$refs.formCanvas.$refs.formFormat.resizeClient()
      }

      const getDataToSave = () => {
        return new Promise((resolve, reject) => {
          try {
            var data = this.$refs.formCanvas.getDataToSave()
            resolve(data)
          } catch (e) {
            reject(e)
          }
        })
      }

      getDataToSave().then(data => {
        var comp = this.formEditTabsComponents[tab.name]
        if (comp && comp.showAfter) {
          comp.showAfter(data)
        }

        var tabsClass = 'formEditTabs formEditTabsCard'
        var index = this.formEditTabsIndexes[tab.name]
        if (index === 0 || (comp && comp.isFormEditTabsCard === false)) {
          tabsClass = 'formEditTabs'
        }
        if (this.showRegularFormHeader === true) {
          tabsClass += ' showRegularFormHeader'
        }

        this.formEditTabsClass = tabsClass
      }).catch(e => {
        console.log(e)
      })
    },
    doBtEditBack(needConfirm = true) {
      const back = () => {
        this.openCanvas = false
        this.saveBtnLoading = true
        this.setEditTabNameLabelIndex(0, this.formEditTabs[0], this.formEditTabs[0])
        const formCanvasComp = this.$findRefInner(this, 'formCanvas')
        formCanvasComp && formCanvasComp.stopCalculateFormulaValues()
        window.luckysheet && window.luckysheet.exitEditMode()
        this.displayTopMultitab(true)
        this.goBackToBackPathIf()
      }
      if (needConfirm) {
        this.$confirm('确认返回吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          back()
        }).catch(() => {

        })
      } else {
        back()
      }
    },
    setBtDraftLoading(isLoading) {
      if (this.$refs.btDraft) {
        this.$refs.btDraft.loading = isLoading
      }

      if (this.$refs.formCanvas) {
        this.$refs.formCanvas.setBtDraftLoading(isLoading)
      }

      if (this.$refs.cformTabSave) {
        this.$refs.cformTabSave.setBtDraftLoading(isLoading)
      }
    },
    doBtEditDraft(e, dataVo) {
      var callbackSuccess = (result) => {
        this.$refreshCount(this)
        this.setBtDraftLoading(false)
        this.displayTopMultitab(true)
        this.saveBtnLoading = true
        if (!this.needProcess) {
          this.openCanvas = false
        }

        // 保存返回列表后高亮显示对应的行
        var extData = {}
        if (this.$isNotEmpty(result.attributes)) {
          extData.highlightRowId = result.attributes.id
        }
        this.$reInit(this.$parent, extData)
      }

      if (!this.isUsingCformTab) {
        // 避免dataVo为undefined导致fillDataVoBeforeSave中报错
        if (!dataVo) {
          dataVo = this.$refs.formCanvas.getDataToSave()
        }
        dataVo.extData = dataVo.extData || {}
        var ret = this.doActionFromEditComponents((comp) => {
          // 多tab制单的情形
          if (comp.fillDataVoBeforeSave) {
            return comp.fillDataVoBeforeSave(dataVo)
          }
        })
        if (ret === true) {
          // 终止保存动作
          return
        }

        // TODO:临时处理数值计算公式 保存时 在校验一次公式
        this.$refs.formCanvas.calculateFormulaValues()
      }

      var callbackFailed = (result) => {
        this.setBtDraftLoading(false)
      }

      this.setBtDraftLoading(true)
      var apiKey = 'WFDRAFT&dataType=CformDataEntity'
      dataVo = this.wrapDataVoBeforeDraftOrSave(dataVo)
      this.$callApi(apiKey, dataVo, callbackSuccess, callbackFailed)
    },
    doBtEditSave(e, dataVo) {
      dataVo = this.wrapDataVoBeforeDraftOrSave(dataVo)
      this.doSaveAction(dataVo, this.$refs.baseListWfApply.doSave)
    },
    saveBefore(dataVo) {
      if (this.$isNotEmpty(this.jumpToSaveFormData)) {
        dataVo.extData['指引配置'] = this.jumpToSaveFormData.sourceType
      }
    },
    wrapDataVoBeforeDraftOrSave(dataVo) {
      window.luckysheet && window.luckysheet.exitEditMode()
      if (!dataVo) {
        dataVo = this.$refs.formCanvas.getDataToSave()
      }
      dataVo.extData = dataVo.extData || {}
      // 多tab制单的情形
      var ret = this.doActionFromEditComponents((comp) => {
        if (comp.fillDataVoBeforeSave) {
          return comp.fillDataVoBeforeSave(dataVo)
        }
      })
      if (ret === true) { // 终止保存动作
        return
      }

      if (this.handleDataVoBeforeSave) {
        this.handleDataVoBeforeSave(
          dataVo, this.dataVo, this.getFormCanvasTabComponent, this)
      }
      this.saveBefore(dataVo)
      return dataVo
    },
    setEditTabVisible(tabKey, isVisible) {
      // 多tab制单时设置页签的可见性
      // 注意：第一个页签的tabKey与标题相同，除第一个之外，tabKey=页签的组件name
      var index = this.formEditTabsIndexes[tabKey]
      var ret = this.setEditTabVisibleIndex(index, isVisible)

      // 表明隐藏后再次显示，整个页签会重新挂接，此时其保护的组件需要重新执行初始化
      if (ret === true && isVisible === true) {
        this.$nextTick(() => {
          var comp
          if (index === 0) comp = this.$refs.formEditTab1st
          if (index === 1) comp = this.$refs.formEditTabComponent1
          if (index === 2) comp = this.$refs.formEditTabComponent2
          if (index === 3) comp = this.$refs.formEditTabComponent3
          if (index === 4) comp = this.$refs.formEditTabComponent4
          if (index === 5) comp = this.$refs.formEditTabComponent5
          if (index === 6) comp = this.$refs.formEditTabComponent6
          if (index === 7) comp = this.$refs.formEditTabComponent7
          if (index === 8) comp = this.$refs.formEditTabComponent8
          if (index === 9) comp = this.$refs.formEditTabComponent9

          if (comp) {
            this.formEditTabsComponents[tabKey] = comp
            if (comp.initDataVo) {
              comp.initDataVo(this, this.dataVo, this.$refs.formCanvas)
            }
          }
        })
      }
    },
    setEditTabVisibleIndex(index, isVisible) {
      // 多tab制单时设置页签的可见性
      if (index !== undefined) {
        if (index === 0) {
          this.$message.error('不能隐藏主表单页签')
          return
        }

        // vue双向绑定问题，只能这样穷举处理
        if (index === 1 && this.formEditTabsVisible1 !== isVisible) {
          this.formEditTabsVisible1 = isVisible
          return true
        }

        if (index === 2 && this.formEditTabsVisible2 !== isVisible) {
          this.formEditTabsVisible2 = isVisible
          return true
        }

        if (index === 3 && this.formEditTabsVisible3 !== isVisible) {
          this.formEditTabsVisible3 = isVisible
          return true
        }

        if (index === 4 && this.formEditTabsVisible4 !== isVisible) {
          this.formEditTabsVisible4 = isVisible
          return true
        }

        if (index === 5 && this.formEditTabsVisible5 !== isVisible) {
          this.formEditTabsVisible5 = isVisible
          return true
        }

        if (index === 6 && this.formEditTabsVisible6 !== isVisible) {
          this.formEditTabsVisible6 = isVisible
          return true
        }

        if (index === 7 && this.formEditTabsVisible7 !== isVisible) {
          this.formEditTabsVisible7 = isVisible
          return true
        }

        if (index === 8 && this.formEditTabsVisible8 !== isVisible) {
          this.formEditTabsVisible8 = isVisible
          return true
        }

        if (index === 9 && this.formEditTabsVisible9 !== isVisible) {
          this.formEditTabsVisible9 = isVisible
          return true
        }
      }
    },
    setEditTabNameLabelIndex(index, name, label) {
      // 多tab制单时设置页签的name和label
      if (index !== undefined) {
        // vue双向绑定问题，只能这样穷举处理
        if (index === 0) this.formEditTabName0 = name
        if (index === 1) this.formEditTabName1 = name
        if (index === 2) this.formEditTabName2 = name
        if (index === 3) this.formEditTabName3 = name
        if (index === 4) this.formEditTabName4 = name
        if (index === 5) this.formEditTabName5 = name
        if (index === 6) this.formEditTabName6 = name
        if (index === 7) this.formEditTabName7 = name
        if (index === 8) this.formEditTabName8 = name
        if (index === 9) this.formEditTabName9 = name

        if (index === 0) this.formEditTabLabel0 = label
        if (index === 1) this.formEditTabLabel1 = label
        if (index === 2) this.formEditTabLabel2 = label
        if (index === 3) this.formEditTabLabel3 = label
        if (index === 4) this.formEditTabLabel4 = label
        if (index === 5) this.formEditTabLabel5 = label
        if (index === 6) this.formEditTabLabel6 = label
        if (index === 7) this.formEditTabLabel7 = label
        if (index === 8) this.formEditTabLabel8 = label
        if (index === 9) this.formEditTabLabel9 = label
        /* if (index > 0) { // 改tabName和标签名时 还需 同步改 formEditTabsExcludeMain 的name和标签名
          this.formEditTabsExcludeMain[index - 1].name = name
          this.formEditTabsExcludeMain[index - 1].label = label
        }*/
      }
    },
    setEditTabLabel(name, label) {
      // 多tab制单时根据页签的name设置label
      // vue双向绑定问题，只能这样穷举处理
      if (name === this.formEditTabName0) this.formEditTabLabel0 = label
      if (name === this.formEditTabName1) this.formEditTabLabel1 = label
      if (name === this.formEditTabName2) this.formEditTabLabel2 = label
      if (name === this.formEditTabName3) this.formEditTabLabel3 = label
      if (name === this.formEditTabName4) this.formEditTabLabel4 = label
      if (name === this.formEditTabName5) this.formEditTabLabel5 = label
      if (name === this.formEditTabName6) this.formEditTabLabel6 = label
      if (name === this.formEditTabName7) this.formEditTabLabel7 = label
      if (name === this.formEditTabName8) this.formEditTabLabel8 = label
      if (name === this.formEditTabName9) this.formEditTabLabel9 = label
    },
    // 重写tab的名称
    repeatTabLabel(name, label) {
      this.setEditTabLabel(name, label)
      // 改标签名时 还需 同步改 formEditTabsExcludeMain 的标签
      for (let i = 0; i < this.formEditTabsExcludeMain.length; i++) {
        if (name === this.formEditTabsExcludeMain[i].name) {
          this.formEditTabsExcludeMain[i].label = label
          break
        }
      }
    },
    switchEditTab(tabKey) {
      // 多tab制单时切换不同页签
      // 注意：第一个页签的tabKey与标题相同，除第一个之外，tabKey=页签的组件name
      this.formEditTabsActiveName = tabKey
    },
    goSaveBillDirectIf() {
      if (this.$isNotEmpty(this.jumpToSaveFormData) &&
        this.$isNotEmpty(this.firstBtNewDropDown)) {
        this.btAddClick(this.firstBtNewDropDown)
      }
    },
    goBackToBackPathIf(result) {
      if (this.$isNotEmpty(this.jumpToSaveFormData) &&
        this.jumpToSaveFormData.doNotJumpBackAfterSaved !== true) {
        // 填充后端返回的result.attributes.extData到jumpToSaveFormData.extData
        if (result && result.attributes) {
          result.attributes.extData = result.attributes.extData || {}

          this.jumpToSaveFormData.saveResult = result
          this.jumpToSaveFormData.extData =
            this.jumpToSaveFormData.extData || {}
          Object.assign(
            this.jumpToSaveFormData.extData,
            result.attributes.extData
          )
        }
        this.$jumpBackAfterSave(this.jumpToSaveFormData, true)
        this.jumpToSaveFormData = undefined // 单次使用
        return true
      }
      this.jumpToSaveFormData = undefined // 单次使用
      return false
    },
    addFormRef() {
      // 进入新增界面时有表单参照，即使this.btAddNoRef = true
      this.openFormCanvas(
        this.firstBtNewDropDown.metaVersionId,
        '',
        undefined,
        false
      )
    },
    btAddClick(dropdownItem, bt) {
      this.showMainPanel = false
      this.currentRow = dropdownItem
      bt = bt || {}
      var btParams = bt.params || {}
      var metaVersionId = dropdownItem.metaVersionId
      var exInitData = btParams.doBtClickParams || {}
      exInitData.versionId = metaVersionId
      exInitData.metaIdTab = dropdownItem.metaIdTab
      exInitData.viewId = dropdownItem.viewId
      exInitData.stateItem = this.stateItem
      exInitData.metaName = dropdownItem.text
      this.metaId = dropdownItem.metaId

      // 可由业务场景传递额外的参数，提供给表单制单时的selectCformVo请求
      const initParams = this.$getInitParams(this)
      if (initParams && this.$isNotEmpty(initParams.表单制单初始化参数)) {
        Object.assign(exInitData, initParams.表单制单初始化参数)
      }

      // 从别的业务逻辑跳转到表单制单时，如果传递了formVersionId
      // 则由该版本号决定打开哪个表单的哪个版本进行新增表的编辑界面
      if (this.$isNotEmpty(this.jumpToSaveFormData)) {
        exInitData.jumpToSaveFormData = this.jumpToSaveFormData
        if (this.$isNotEmpty(this.jumpToSaveFormData.editingRow)) {
          this.btModifyClick(this.jumpToSaveFormData.editingRow)
          return
        } else if (this.$isNotEmpty(this.jumpToSaveFormData.formVersionId)) {
          metaVersionId = this.jumpToSaveFormData.formVersionId
        }

        if (this.$isNotEmpty(this.jumpToSaveFormData.viewId)) {
          exInitData.viewId = this.jumpToSaveFormData.viewId
          exInitData.versionId = metaVersionId
        }
      }

      this.openFormCanvas(
        metaVersionId,
        '',
        undefined,
        this.btAddNoRef,
        exInitData
      )
      this.displayTopMultitab(false)
    },
    // 单位项目管理新增的时候,取消显示顶部的多标签内容
    displayTopMultitab(flag) {
      this.$emit('displaytab', flag)
    },
    btModifyClick(row, exData) {
      var exInitData = this.initCformTabParamsBy(row)
      this.currentRow = row
      if (this.$isNotEmpty(exData)) {
        Object.assign(exInitData, exData)
      }
      this.openFormCanvas(
        row.metaVersionId, row.ID,
        undefined, undefined, exInitData)
      this.displayTopMultitab(false)
    },
    btDetailClick(row) {
      this.$showDetail(row.id, undefined, this.hideDetailTabs,
        row.metaName, { 'isApply': this.isApply, isShowTabs: this.isShowTabs })
    },
    initCformTabParamsBy(row) {
      var exInitData = {}
      exInitData.versionId = row.metaVersionId
      exInitData.dataId = row.ID
      exInitData.metaIdTab = row.metaIdTab
      exInitData.viewId = row.viewId
      exInitData.currentNodeId = row.currentNodeId
      exInitData.stateItem = this.stateItem
      exInitData.metaName = row.metaName
      exInitData.isTemplate = row.isTemplate
      exInitData.status = row.status
      this.metaId = row.metaId
      return exInitData
    },
    initBlockView(exInitData) {
      // // 设置表单画布额外TAB组件
      // var callbackInitCanvas = (meta, dataVo) => {
      //   this.formCanvasMeta = meta
      //   this.dataVo = JSON.parse(JSON.stringify(dataVo))
      //   this.hasMultipleFormCanvasTabs = false
      // }

      var callbackSaveSuccess = (result) => {
        if (!this.needProcess) {
          this.openCanvas = false
        }

        // 保存返回列表后高亮显示对应的行
        var extData = {}
        if (this.$isNotEmpty(result.attributes)) {
          extData.highlightRowId = result.attributes.id
        }

        if (this.goBackToBackPathIf(result)) {
          return true
        }
        this.$reInit(this.$parent, extData)
      }

      exInitData = exInitData || {}

      if (this.customSaveAction) {
        // 自定义调用保存动作
        exInitData.customSaveAction = this.customSaveAction
      }

      // var thisInitParams = this.$getInitParams(this)
      // var initFormExData = thisInitParams.initFormExData || {}
      // // initFormExData = Object.assign(initFormExData, exInitData)
      // initFormExData.baseListFormObj = this
      // initFormExData.showHeader = true
      // initFormExData.isMultipleEditTabs = false
      // initFormExData.showEditButtons = false
      // initFormExData.callbackBeforeFormLoaded = (dataVo) => {
      //   this.fillDataVoFromJumpToSaveFormData(dataVo)
      // }

      var params = {
        isEdit: true,
        mode: '制单',
        callbackSaveSuccess: callbackSaveSuccess,
        doBtEditBack: this.doBtEditBack,
        jumpToSaveFormData: this.jumpToSaveFormData,
        refFormParams: this.refFormParams, // 设置表单配置
        isShowTabs: this.isShowTabs // 是否展示tabs
        // callbackInitCanvas: callbackInitCanvas,
        // initFormExData: initFormExData
      }
      Object.assign(params, exInitData)
      this.$refs.blockView.init(undefined, params)
    },
    initCformTabToEdit(exInitData) { // 使用CformTab机制进行制单
      var metaIdTab = exInitData.metaIdTab
      var exParamsFormTabSave = {
        btEditBack: this.doBtEditBack,
        btEditDraft: this.doBtEditDraft,
        btEditSave: this.doBtEditSave,
        callAfterFormLoaded: (dataVo, formCanvasMeta, cformObj) => {
          exInitData?.callAfterFormLoaded?.(dataVo, formCanvasMeta, cformObj)
          this.addCallbacks(dataVo)
        }
      }
      Object.assign(exParamsFormTabSave, exInitData)
      if (this.exHandleExParamsFormTabSave) {
        this.exHandleExParamsFormTabSave(exParamsFormTabSave)
      }
      var cbBeforeApi = () => {
      }
      var cbFailed = () => {
      }
      var cbSuccess = (result, tabVo, formTabSave) => {

      }

      this.$refs.cformTabSave.isEdit = true
      this.$refs.cformTabSave.refreshTabs(
        metaIdTab, cbBeforeApi, cbSuccess, cbFailed,
        exParamsFormTabSave)
    },
    openFormCanvas(versionId, dataId, callback, noRefForm, exInitData = {}) {
      // 默认收起
      this.blockTabCollapsed = true
      this.blockTabs = []
      this.billId = dataId
      this.metaName = exInitData.metaName
      this.currentNodeId = exInitData.currentNodeId
      this.showMainPanel = !!(exInitData.status && exInitData.status !== '草稿' && this.currentNodeId)
      this.openCanvas = true
      this.$nextTick(() => {
        // 多tab制单处理
        this.formEditTabsComponents = {}
        this.formEditTabsObjects = {}
        exInitData = exInitData || {}
        this.isUsingCformTab = this.$isNotEmpty(exInitData.metaIdTab)
        this.isUsingCformBlock = this.$isNotEmpty(exInitData.viewId)
        if (this.noTabWhenSaveForm === true) {
          this.isUsingCformTab = false
        }

        // dataVo.extData = dataVo.extData || {}
        // const callbackAfterFormLoadedInExtData = dataVo.extData.callbackAfterFormLoaded
        // if (callbackAfterFormLoadedInExtData) {
        //   callbackAfterFormLoadedInExtData(dataVo, this.$refs.formCanvas.$refs.formFormat)
        // }
        if (this.isUsingCformBlock) {
          this.$nextTick(() => {
            this.initBlockView(exInitData)
          })
        } else if (this.isUsingCformTab) {
          this.$nextTick(() => {
            this.initCformTabToEdit(exInitData)
          })
        } else if (this.isMultipleEditTabs) {
          this.formEditTabsActiveName = this.formEditTabs[0]
          this.formEditTabsObjects[this.formEditTabs[0]] = this.$refs.formEditTab1st
          this.formEditTabsComponents[this.formEditTabs[0]] = this.$refs.formCanvas
          this.formEditTabsVisible0 = true

          for (var i = 0; i < this.formEditTabsExcludeMain.length; i++) {
            var index = i + 1
            this.setEditTabVisibleIndex(index, true)
            // vue双向绑定问题，只能这样穷举处理
            var tabName = this.formEditTabsExcludeMain[i].name
            if (index === 1) this.formEditTabComponentId1 = tabName
            if (index === 2) this.formEditTabComponentId2 = tabName
            if (index === 3) this.formEditTabComponentId3 = tabName
            if (index === 4) this.formEditTabComponentId4 = tabName
            if (index === 5) this.formEditTabComponentId5 = tabName
            if (index === 6) this.formEditTabComponentId6 = tabName
            if (index === 7) this.formEditTabComponentId7 = tabName
            if (index === 8) this.formEditTabComponentId8 = tabName
            if (index === 9) this.formEditTabComponentId9 = tabName
          }

          this.$nextTick(() => {
            this.setEditTabNameLabelIndex(
              0, this.formEditTabName0, this.formEditTabLabel0)
            for (var i = 0; i < this.formEditTabsExcludeMain.length; i++) {
              var index = i + 1
              var tabName = this.formEditTabsExcludeMain[i].name
              var tabLabel = this.formEditTabsExcludeMain[i].label

              var component = this.$refs['formEditTabComponent' + index]
              if (component) {
                this.formEditTabsComponents[tabName] = component
                this.formEditTabsComponents[tabLabel] = component
              }
              this.setEditTabNameLabelIndex(index, tabName, tabLabel)
            }
            this.initCanvasAfterTabInit(
              versionId,
              dataId,
              callback,
              noRefForm,
              exInitData
            )
          })
        } else {
          this.initCanvasAfterTabInit(
            versionId,
            dataId,
            callback,
            noRefForm,
            exInitData
          )
        }
      })
    },
    showOrHideBtDraft(dataVo) {
      // 已正式保存过的表单，没有存草稿功能
      // 跳转到制单页面时，没有存草稿功能
      // var shouldShow = true

      // 过渡阶段，使用页面指定开放存草稿功能的模块
      var shouldShow = this.isShowBtDraftOutside
      if ((this.$isNotEmpty(dataVo.data.id) && dataVo.data.status !== '草稿') ||
        (this.$isNotEmpty(this.jumpToSaveFormData)) && this.jumpToSaveFormData.isShowBtDraft !== true) {
        shouldShow = false
      }
      this.isShowBtDraft = shouldShow
      if (this.$refs.formCanvas) {
        this.$refs.formCanvas.showBtDraft(shouldShow)
      }
    },
    initCanvasAfterTabInit(versionId, dataId, callback, noRefForm, exInitData) {
      this.$nextTick(() => {
        if (!this.isMultipleEditTabs) {
          this.$refs.formCanvas.setExtDataAssembly(this.formType)
        }

        // 设置表单画布额外TAB组件
        var callbackInitCanvas = (meta, dataVo) => {
          var exTabs = []
          this.formCanvasMeta = meta
          this.dataVo = JSON.parse(JSON.stringify(dataVo))
          if (this.getFormCanvasExTabs) {
            exTabs = this.getFormCanvasExTabs(dataVo)
          }
          this.hasMultipleFormCanvasTabs = this.$isNotEmpty(exTabs)
        }

        exInitData = exInitData || {}
        var thisInitParams = this.$getInitParams(this)
        var initFormExData = thisInitParams.initFormExData || {}
        initFormExData = Object.assign(initFormExData, exInitData)
        initFormExData.baseListFormObj = this
        initFormExData.showHeader = this.showHeaderFillMode
        initFormExData.isMultipleEditTabs = this.isMultipleEditTabs
        initFormExData.stateItem = exInitData.stateItem
        // 多tab制单时，不显示formCanvas的编辑按钮，规则表单不显示标题
        if (this.isMultipleEditTabs) {
          initFormExData.showEditButtons = false
        }

        initFormExData.callbackBeforeFormLoaded = (dataVo) => {
          this.$fillDataVoFromJumpToSaveFormData(dataVo, this.jumpToSaveFormData)
        }

        // 第二次调用initByVersionDataId返回的dataVo中包含数据
        // dataVo.extData.initRefDataVoFromRemoteData，这样能避免无限调用的情况
        initFormExData.callbackAfterFormLoaded = (dataVo) => {
          this.showOrHideBtDraft(dataVo)
          this.doActionFromEditComponents((comp) => {
            // 多tab制单的情形
            if (comp.initDataVo) {
              comp.initDataVo(this, this.dataVo, this.$refs.formCanvas)
            }
          })

          // 这里给业务提供表单填充之后设置表单的机会
          // 应用场景如，采购跳转到合同制单生成合同，需要设置采购的值
          if (this.$isEmpty(dataVo.extData.initRefDataVoFromRemoteData)) {
            dataVo.extData = dataVo.extData || {}
            const callbackAfterFormLoadedInExtData = dataVo.extData.callbackAfterFormLoaded
            if (callbackAfterFormLoadedInExtData) {
              callbackAfterFormLoadedInExtData(dataVo, this.$refs.formCanvas.$refs.formFormat)
            }
          }

          if (noRefForm === false) {
            // 制单表单参照的处理
            if (this.$isNotEmpty(this.refFormParams) &&
              dataVo &&
              this.$isEmpty(dataVo.extData.initRefDataVoFromRemoteData) &&
              this.$refs.formCanvas.$refs.formFormat.refAfterInitWhenFillForm) {
              // 表单参照是否使用后端DataVo重新初始化制单界面
              var initRefDataVoFromRemoteData = this.$isEmpty(
                this.refFormParams.initRefDataVoFromRemoteData
              )
                ? false
                : this.refFormParams.initRefDataVoFromRemoteData

              var paramsRef = {
                isRelatedRefIncludeDept: true,
                noRefAction: initRefDataVoFromRemoteData, // 表单参照不执行参照填充
                // onDlgClose: (params) => {
                //   // 如果表单参照不进行参照，则直接退出制单界面返回到列表
                //   if (params.isOK !== true) {
                //     this.doBtEditBack(false)
                //   }
                // },
                exHandleSelectedData: (selectedData) => {
                  this.refFormSelectedData = selectedData

                  // 使用参照的表单重新初始化制单界面，以获得联动和右侧扩展
                  if (
                    initRefDataVoFromRemoteData &&
                    this.$isNotEmpty(selectedData) &&
                    this.$isNotEmpty(selectedData.list)
                  ) {
                    var refFormId = selectedData.list[0].ID

                    // 这个方法会导致再次调用callbackAfterFormLoaded，但此时
                    // dataVo.data.id不为空，所以不会引发无限循环
                    // 参数initRefDataVoFromRemoteData触发后端对dataVo清除id编码等要素
                    // refSourceType表单参照来源类型 后端可通过该参数过滤不需要的数据
                    var selectCformVoQueryParams = {
                      initRefDataVoFromRemoteData: true,
                      refSourceType: this.refFormParams.refSourceType
                    }
                    this.$refs.formCanvas.initByVersionDataId(
                      '',
                      refFormId,
                      '制单',
                      callbackInitCanvas,
                      undefined,
                      true,
                      this.getFormCanvasExTabs,
                      initFormExData,
                      selectCformVoQueryParams
                    )
                  }
                }
              }

              if (this.isRefFormNameSelect) {
                this.refFormParams.NAME_eq = dataVo.meta.name
              }
              paramsRef = Object.assign(paramsRef, this.refFormParams)
              this.$refs.formCanvas.$refs.formFormat.refAfterInitWhenFillForm(
                paramsRef
              )
            }
          }

          // 自由表单多tab制单时，调整制单区域边界的间距
          var $formEditTabs = $('.formEditTabs')
          $formEditTabs.removeClass('isMultipleEditTabs')
          if (this.isMultipleEditTabs) {
            var $tabsContent = $formEditTabs.find('.el-tabs__content')
            $formEditTabs.addClass('isMultipleEditTabs')
            $tabsContent.find('.formEditTab1st.formCanvasRuntimeHasAssemblyFree')
              .find('#formFreeView')
              .css('padding', '0px 1px 0px 0px')
            $formEditTabs.find('.formFreeViewRightContent')
              .css('cssText', 'margin-left: -9px;padding: 0px 0px 0px 0px;')
          }
          this.addCallbacks(dataVo)
        }

        var formIdFromJump = this.getDataIdFromJumpToSaveFormData()
        if (this.$isNotEmpty(formIdFromJump)) {
          dataId = formIdFromJump
        }
        var extData = this.getExtDataFromJumpToSaveFormData()
        if (this.$isNotEmpty(extData)) {
          this.queryParams.extData = extData
        }

        if (this.$isNotEmpty(this.jumpToSaveFormData) &&
          this.$isNotEmpty(this.jumpToSaveFormData.selectCformVoParams)) {
          Object.assign(this.queryParams, this.jumpToSaveFormData.selectCformVoParams)
        }

        this.$refs.formCanvas.initByVersionDataId(
          versionId,
          dataId,
          '制单',
          callbackInitCanvas,
          undefined,
          true,
          this.getFormCanvasExTabs,
          initFormExData,
          this.queryParams
        )

        if (this.isMultipleEditTabs) {
          var $tabsContent = $('.formEditTabs').find('.el-tabs__content')
          $tabsContent.css('height', 'calc(100% - 51px)')
          $tabsContent.find('.formEditTab1st').css('height', '100%')
          // $tabsContent.closest('.formCanvasRuntime').css('padding-top', '0px')
          $tabsContent
            .find('main.el-main')
            .css('cssText', 'padding: 0px !important')
          $tabsContent.find('.formRegularContainer').css('padding', '0px')
          $tabsContent
            .find('.formRegularContainer')
            .css('padding-top', this.formRegularContainerPaddingTop + 'px')

          $('.formEditTabs')
            .parent()
            .find('.el-footer')
            .css('height', '44px')
            .css('padding', '12px 0px 0px 0px')
            .css('text-align', 'right')
        }
      })
    },
    doActionFromEditComponents(action) {
      for (var i = 0; i < this.formEditTabsExcludeMain.length; i++) {
        var tabName = this.formEditTabsExcludeMain[i].name
        var component = this.formEditTabsComponents[tabName]
        if (component) {
          var ret = action(component)
          if (ret === true) {
            // 可以用于表明终止保存操作
            return true
          }
        }
      }
    },
    getFormCanvasTabComponent(tabName) {
      return this.$call(
        this,
        'formFormat',
        'getFormCanvasTabComponent',
        tabName
      )
    },
    doSaveAction(dataVo, doSaveHandler) {
      // 保存和保存并送审的统一方法
      var callbackSuccess = (result) => {
        this.$refreshCount(this)
        this.saveBtnLoading = false
        if (!this.needProcess) {
          this.openCanvas = false
        }
        this.$emit('handleDataVoBeforeSave')

        // 保存返回列表后高亮显示对应的行
        var extData = {}
        if (this.$isNotEmpty(result.attributes)) {
          extData.highlightRowId = result.attributes.id
        }
        this.saveBtnLoading = true

        if (this.goBackToBackPathIf(result)) {
          return true
        }
        this.$reInit(this.$parent, extData)

        // 单位项目管理新增的时候,取消显示顶部的多标签内容
        this.displayTopMultitab(true)

        // 保存成功后上传pdf
        this.$showPrint(
          {
            printId: result.attributes.id,
            isSavePdf: true
          }, '', { type: 'PDF' })
      }
      var showTabErrorTip = (tabName, errorCount) => {
        if (this.isMultipleEditTabs) {
          if (this.$isNumber(errorCount)) {
            var tabIndex = this.formEditTabsIndexes[tabName]
            var labelNoError =
              tabIndex === 0
                ? this.formEditTabs[0]
                : this.formEditTabsExcludeMain[tabIndex - 1].label
            var label =
              parseInt(errorCount) === 0
                ? labelNoError
                : `${labelNoError}(有${errorCount}个错误)`
            this.setEditTabNameLabelIndex(tabIndex, tabName, label)
            if (errorCount > 0) {
              $('#tab-' + tabName).addClass('formEditTabsError')
              this.switchEditTab(tabName)
            }
          }
        }
      }

      var callbackFailed = (result) => {
        this.saveBtnLoading = false
        if (this.isUsingCformTab) {
          return this.$refs.cformTabSave.showError(result)
        }
        // 校验错误个数显示在tab上的顺序，按主表单 -> 第2个tab ...依次处理，
        // 当前有一个tab有错误，则不再处理后面有错误的tab的错误显示
        var errorHitContainer = {}
        this.$refs.formCanvas.showError(result, errorCount, errorHitContainer)
        var errorCount = errorHitContainer['hitCountInt']
        if (this.$isNumber(errorCount) && parseInt(errorCount) > 0) {
          // 主表单有校验错误
          showTabErrorTip(this.formEditTabs[0], errorCount)
        } else if (result && result.attributes) {
          var formEditTabErrors = result.attributes.formEditTabErrors
          if (this.$isNotEmpty(formEditTabErrors)) {
            // 表明错误发生在非主表单的其他tab
            var firstErrorTabName
            for (let i = 0; i < this.formEditTabsExcludeMain.length; i++) {
              var tabName = this.formEditTabsExcludeMain[i].name
              if (this.$isNotEmpty(formEditTabErrors[tabName])) {
                if (!firstErrorTabName) {
                  firstErrorTabName = tabName
                }

                errorCount = parseInt(formEditTabErrors[tabName])
                showTabErrorTip(tabName, errorCount)
                var comp = this.formEditTabsComponents[tabName]
                if (comp && comp.showError) {
                  comp.showError(result)
                }
              }
            }

            if (firstErrorTabName) {
              this.switchEditTab(firstErrorTabName, true)
            }
          }
        }
        return parseInt(errorCount) > 0
      }

      dataVo.extData = dataVo.extData || {}
      var saveAction = (noConfirm, extra) => {
        var message = noConfirm === true ? '#不需要确认#' : undefined
        doSaveHandler(
          dataVo,
          callbackSuccess,
          callbackFailed,
          message,
          () => {
            this.saveBtnLoading = true
          },
          extra,
          dataVo.extData['customConfirmSetting']
        )
      }

      // 先清空之前可能存在的tab错误
      if (this.isMultipleEditTabs) {
        this.formEditTabsClass = this.formEditTabsClass.replace(
          ' formEditTabsError', '')

        showTabErrorTip(this.formEditTabs[0], 0)
        for (let i = 0; i < this.formEditTabsExcludeMain.length; i++) {
          showTabErrorTip(this.formEditTabsExcludeMain[i].name, 0)
        }
      }
      if (this.customSaveAction) {
        // 自定义调用保存动作
        this.customSaveAction(saveAction, dataVo, this.refFormSelectedData)
      } else {
        saveAction()
      }
    },
    getDataIdFromJumpToSaveFormData() { // 跳转过来是可传递formId来表明直接修改的表单
      if (this.$isNotEmpty(this.jumpToSaveFormData)) {
        return this.jumpToSaveFormData.formId
      }
    },
    getExtDataFromJumpToSaveFormData() { // 跳转过来是可传递formId来表明直接修改的表单
      if (this.$isNotEmpty(this.jumpToSaveFormData)) {
        return this.jumpToSaveFormData.extData
      }
    },

    // 此方法为使用base-list-cform的业务提供，成功后会刷新列表
    doActionByIds(actionName, apiKey, callbackSuccess, exParams) {
      this.$call(
        this,
        'baseListWf',
        'doActionByIds',
        actionName,
        apiKey,
        this.$getInitParams(this.$parent),
        callbackSuccess,
        exParams
      )
    },
    getBaseListWf() {
      return this.isApply
        ? this.$refs.baseListWfApply
        : this.$refs.baseListWfAudit
    },
    doBtClick(btText, params) {
      this.$call(
        this.getBaseListWf(),
        'baseListWf',
        'doBtClick',
        btText,
        params
      )
    },
    addCallbacks(dataVo) { // formColItemChange回调单独针对于blockTabRef
      const callbacks = {
        'formColItemChange': (theColItem, selects) => {
          this.$refs.blockTabRef?.formColItemChange(theColItem, selects, dataVo)
        }
      }
      if (this.isUsingCformTab) {
        this.$nextTick(() => {
          this.$call(this, 'formCanvas', 'addColItemModifiedCallbacks', callbacks)
        })
        return
      }
      this.$refs.formCanvas?.addColItemModifiedCallbacks(callbacks)
    },
    handlerCollapse() {
      this.blockTabCollapsed = !this.blockTabCollapsed
      if (!this.blockTabCollapsed) {
        this.$refs.blockTabRef?.setBarWidth()
      }
    }
  }
}
</script>

<style>
.formEditTabs.formEditTabsError .el-tabs__item.is-active,
.formEditTabs.formEditTabsError .el-tabs__item.is-active:hover {
  color: red;
}

.formEditTabs.formEditTabsError .el-tabs__active-bar {
  background-color: red;
}

.formEditTabs .formEditTabsError {
  color: red;
}

.formEditTabs .formCommonHeader {
  display: none;
}

.formEditTabs.showRegularFormHeader .formCommonHeader {
  display: block !important;
  /*display: unset important;*/
}

.formEditTabsCard .el-tab-pane > div {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.formEditTabsCard .el-tab-pane > div > div:not(:last-child),
.formEditTabsCard .el-tab-pane .formEditTabCardStrip:not(:last-child) {
  padding-bottom: 5px;
}

.formEditTabsCard
.el-tab-pane
> div
.el-table--border
.el-table__cell:first-child
.cell,
.formEditTabsCard
.el-tab-pane
.formEditTabCardStrip
.el-table--border
.el-table__cell:first-child
.cell {
  padding-left: 5px;
}

.cformTabSave {
  height: 100%;
}

.cformTabSave .el-tabs {
  height: 100%;
}

.cformTabSave .el-tabs .el-tab-pane {
  height: 100%;
}

.cformTabSave .el-tabs .el-tabs__content {
  height: calc(100% - 48px);
}
</style>
<style lang="scss" scoped>
.formCanvasRuntime {
  display: flex;
  overflow: hidden;
  .right-block-tab {
    overflow: hidden;
    height: 100%;
    /deep/.el-tabs__header.is-top {
      margin-bottom: 20px;
    }
  }
}
</style>
