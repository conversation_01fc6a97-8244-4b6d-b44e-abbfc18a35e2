<template>
  <b-list ref="baseList"
          :isVxe="isVxe"
          :isEditRowForm="isEditRowForm"
          @handleSearch="handleSearch"
          @baseListBtClick="baseListBtClick">
    <template #toolbarExtend>
      <slot name="toolbarExtend"/>
    </template>
    <template #dbColLeft>
      <slot name="dbColLeft"/>
    </template>
    <template #tableTopInfo>
      <slot name="tableTopInfo" />
    </template>
    <template #tableRowSlot="{row, column}">
      <slot name="tableRowSlot" :row="row" :column="column"/>
    </template>
    <template #contentHeader>
      <slot name="contentHeader"/>
    </template>

  </b-list>
</template>

<script>

import BList from './base-list'
import { getUUID } from '@/utils'

export default {
  name: 'b-curd',
  components: { BList },
  inject: {
    hiddenCustomizeButton: { default: undefined },
    customizeButton: { default: undefined },
    blockConfig: { default: undefined }
  },
  props: {
    isVxe: {
      type: Boolean,
      default: false
    },
    deleteNoRefreshBtn: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    // 如果业务页面实现了initParamsCrud方法，则自动使用initParamsCrud作为初始数据
    if (this.$parent.initParamsCrud) {
      const initParamsCrud = this.$parent.initParamsCrud()
      if (this.$isEmpty(initParamsCrud)) {
        this.$message.error('initParamsCrud方法返回空数据')
        return
      }
      this.init(initParamsCrud)
    }
  },
  data() {
    return {
      // 当前组件标识，解决$saveInitParams(this.$parent)覆盖问题
      baseCurd: true,
      deleteApiKey: '',
      isEditRowForm: false, // 是否是可编辑的列表
      isNoEditButtons: false, // 行编辑表单时，是否隐藏新增，删除，上移，下移按钮
      isEditing: false, // 是否正在编辑
      pageData: undefined,
      rowsData: [],
      headerIndexes: {},
      cloneRow: {},
      addRowEx: undefined,
      deleteRowBefore: undefined,
      deleteRowEx: undefined,
      rowCopyEX: undefined,
      showEditDlg: undefined,
      showPager: false,
      checkButton: false // 审核状态下的行表单操作按钮显示隐藏
    }
  },
  methods: {
    /**
     * 根据什么字段 删除rowsData的数据
     * @param {*} field 字段(行中的字段 每种场景可能需要的字段不同)
     * @param {*} id 传入的id
     */
    deleteTableDataByField(field, id) {
      if (typeof field === 'object') {
        let isMatch = true
        for (let i = this.rowsData.length - 1; i >= 0; i--) {
          const row = this.rowsData[i]
          for (const key in field) {
            if (field.hasOwnProperty(key) && row.hasOwnProperty(key) && row[key] === field[key]) {
              isMatch = true
              continue
            } else {
              isMatch = false
              break
            }
          }
          if (isMatch) {
            this.rowsData.splice(i, 1)
          }
        }
      } else {
        for (let i = this.rowsData.length - 1; i >= 0; i--) {
          if (this.rowsData[i][field] === id) {
            this.rowsData.splice(i, 1)
          }
        }
      }
    },
    clearTableData() {
      this.rowsData.splice(0, this.rowsData.length)
      this.$refs.baseList.clearTableData()
    },
    handleSearch(param) {
      this.$emit('handleSearch', param)
    },
    showBlistError(rowIndex, rowLable, tip, isReset) {
      this.$refs.baseList.showBlistError(rowIndex, rowLable, tip, isReset)
    },
    changeRowTar(attribute, row, isRest, tip) {
      this.$refs.baseList.changeRowTar(attribute, row, isRest, tip)
    },
    getBlistRowData() {
      return this.$refs.baseList.rowsData
    },
    init(initParams) {
      this.checkButton = initParams.checkButton
      initParams = initParams || {}
      initParams.params = initParams.params || {}
      this.deleteApiKey = initParams.params.deleteApiKey

      // 行编辑表单开关打开时，不显示修改和详情按钮
      this.isEditRowForm = initParams.isEditRowForm || false
      this.isEditing = initParams.isEditing || false
      if (this.$isNotEmpty(initParams.编辑针对的实体)) {
        this.isEditRowForm = true
      }

      this.isNoEditButtons = initParams.isNoEditButtons || false
      this.addRowEx = initParams.addRowEx
      this.addBaUniqueFlag = initParams.addBaUniqueFlag // 指标区块数据添加唯一标识
      this.rowCopyEX = initParams.rowCopyEX
      this.deleteRowBefore = initParams.deleteRowBefore
      this.deleteRowEx = initParams.deleteRowEx
      // 传入的是一个函数(隐藏按钮的数组)
      if (this.hiddenCustomizeButton) {
        initParams.hiddenButtons = initParams.hiddenButtons || []
        initParams.hiddenButtons.push(...this.hiddenCustomizeButton())
      }
      if (this.isEditRowForm) {
        initParams.hideCurdButton = initParams.hideCurdButton || []
        initParams.hideCurdButton.push('修改')
        initParams.hideCurdButton.push('详情')

        // 加入编辑列表标识
        initParams.tableExStyleClass = initParams.tableExStyleClass || ''
        initParams.tableExStyleClass += ' editing-table'
        if (!this.isEditing && initParams.hasRowFormButton === undefined) {
          initParams.tableExStyleClass += ' editing-table-not-editing'
        }
      }

      // 统一的编辑和详情弹框机制
      this.showEditDlg = (isEdit, row, bt, exParams) => {
        exParams = exParams || {}
        const params4SelectVo = exParams
        const ps = {
          ...exParams,
          isEdit: isEdit,
          row: row,
          bt: bt,
          params4SelectVo: params4SelectVo
        }

        if (initParams.editDlg) {
          if (initParams.editDlg.show) {
            initParams.editDlg.show(ps)
          } else {
            // 如果编辑弹框没有定义show方法，则查找其内部是否有名称为“baseEditDlg”的组件
            // 如果找到这个组件，然后调用这个组件的show方法
            const baseEditDlg = initParams.editDlg.$refs['baseEditDlg']
            if (baseEditDlg && baseEditDlg.show) {
              baseEditDlg.show(ps)
            }
          }
          return true
        } else {
          // 点击按钮时，可通过调用bt.setParam设置编辑框使用哪个编辑内容组件
          if (bt) {
            const dynamicEditContent = bt.getParam?.('编辑内容组件')
            if (this.$isNotEmpty(dynamicEditContent)) {
              bt.setParam('编辑内容组件', '') // 只使用一次，如果再使用需要重新设置
              this.$refs.baseList.editContent = dynamicEditContent
              // 如果是链接形式的,需要置空,不然会影响到详情
              ps.cancelCallBack = () => {
                this.$refs.baseList.editContent = ''
              }
            }
          }

          if (this.$isNotEmpty(this.$refs.baseList.editContent)) {
            this.$nextTick(() => {
              // 没有指定editDlg时，取b-list中通用的baseEditDlgCommon
              ps.getFormComponent =
                () => this.$refs.baseList.$refs.baseEditDlgCommonContent
              ps.reloadTable = () => this.reloadTable()
              this.$refs.baseList.$refs.baseEditDlgCommon.show(ps)
            })
            return true
          }
        }
      }

      // 浏览操作日志的附件时,禁用删除的按钮
      if (this.$isNotEmpty(initParams.isbrowse) && !initParams.isbrowse) {
        initParams.hideCurdButton.push('删除')
      }

      var buttons = []

      // 添加区块名字 用文字按钮实现
      if (this.$isNotEmpty(initParams.blockformName)) {
        buttons.unshift({
          type: 'text',
          text: initParams.blockformName,
          icon: `el-icon-arrow-${this.$refs.baseList.$refs.basePage.isFold ? 'down' : 'right'}`,
          enabledType: '0',
          click: bt => {
            // 点击后区块名称 修改按钮的可见性 + 更多按钮的可见性
            this.$refs.baseList?.$refs?.basePage?.changeIsFold()
            const isFold = this.$refs.baseList.$refs.basePage.isFold
            bt.icon = `el-icon-arrow-${isFold ? 'down' : 'right'}`
            // 调用按钮自适应
            this.$refs.baseList?.$refs?.basePage?.reInitLayout()
          }
        })
      }

      if (this.customizeButton && this.$isNotEmpty(this.customizeButton)) {
        buttons.push(...this.customizeButton())
      }
      if (this.$isEmpty(initParams.hideCurdButton) || initParams.hideCurdButton.indexOf('新增') === -1) {
        initParams.btAddClick = initParams.btAddClick || {}
        var haveBtAddText = this.$isNotEmpty(initParams.btAddClick.text)
        var btNew = {
          // type: 'primary',
          text: haveBtAddText ? initParams.btAddClick.text : '新增',
          icon: 'el-icon-zoom-in',
          enabledType: '0',
          
          click: bt => {
            const showEditDlgResult = this.showEditDlg(true, undefined, bt)
            if (!showEditDlgResult) {
              if (this.$isNotEmpty(initParams.btAddClick.click)) {
                initParams.btAddClick.click()
              } else {
                this.$message.info('未找到响应函数：<br/>initParams.btAddClick.click')
              }
            }
          } }
        buttons.push(btNew)
        if (this.$isNotEmpty(initParams.btNewDropDowns)) {
          btNew.dropDowns = initParams.btNewDropDowns

          // 如果存在多个下拉菜单，新增按钮本身的事件不响应，而是响应下拉菜单的事件
          // 而如果只有单个菜单，baseList中会处理下拉菜单的事件覆盖按钮的事件
          if (btNew.dropDowns.length > 1) {
            btNew.click = null
          }
        }
      }
      if (this.$isEmpty(initParams.hideCurdButton) || initParams.hideCurdButton.indexOf('修改') === -1) {
        initParams.btModifyClick = initParams.btModifyClick || {}
        var haveBtModifyText = this.$isNotEmpty(initParams.btModifyClick.text)
        buttons.push({
          text: haveBtModifyText ? initParams.btModifyClick.text : '修改',
          icon: 'el-icon-edit',
          enabledType: '1',
          click: bt => {
            const row = this.$getTableSelection(this.$refs.baseList.$refs.table)[0]
            const showEditDlgResult = this.showEditDlg(true, row, bt)
            if (!showEditDlgResult) {
              if (this.$isNotEmpty(initParams.btModifyClick.click)) {
                if (initParams.btModifyClickCustom) {
                  initParams.btModifyClickCustom(bt, initParams.btModifyClick.click(row, bt))
                } else {
                  initParams.btModifyClick.click(row, bt)
                }
              } else {
                this.$message.info('未找到响应函数：<br/>initParams.btModifyClick.click')
              }
            }
          } })
      }

      initParams.btDeleteClick = initParams.btDeleteClick || {}
      const btDeleteText = this.$isNotEmpty(initParams.btDeleteClick.text)
        ? initParams.btDeleteClick.text : '删除'
      const btDeleteEnabledType = this.$isNotEmpty(initParams.btDeleteClick.enabledType)
        ? initParams.btDeleteClick.enabledType : '1+'
      if (this.$isEmpty(initParams.hideCurdButton) ||
        initParams.hideCurdButton.indexOf(btDeleteText) === -1) {
        initParams.btDeleteClick = initParams.btDeleteClick || {}
        buttons.push({
          type: 'danger',
          text: btDeleteText,
          icon: 'el-icon-delete',
          enabledType: btDeleteEnabledType,
          click: bt => {
            if (this.$isNotEmpty(this.deleteApiKey)) {
              var deleteParams = {}
              deleteParams.apiKey = this.deleteApiKey
              deleteParams.actionName = btDeleteText
              deleteParams.apiExParams = initParams.params.deleteApiExParams || {} // { dataType: 'CformDataEntity' }
              deleteParams.isReloadTree = initParams.showTree
              deleteParams.callbackTableTempDataDelete = initParams.callbackTableTempDataDelete
              deleteParams.doActionBefore = initParams.doActionBefore
              deleteParams.getIdsStr4doActionByIds = initParams.getIdsStr4doActionByIds
              deleteParams.callbackSuccess = (result, delIdsArr) => this.initLeftTree()
              if (initParams.callbackSuccessDelete) {
                deleteParams.callbackSuccess = initParams.callbackSuccessDelete
              }

              deleteParams.isReloadTable = true
              if (initParams.donotReloadTableAfterDelete) {
                deleteParams.isReloadTable = false
              }

              if (initParams.btDeleteClickCustom) {
                initParams.btDeleteClickCustom(bt, this.$refs.baseList.doActionByIds(deleteParams, initParams))
              } else {
                this.$refs.baseList.doActionByIds(deleteParams, initParams)
              }
            } else if (this.$isNotEmpty(initParams.btDeleteClick.click)) {
              initParams.btDeleteClick.click(bt)
            } else {
              this.$message.info('未设置：initParams.params.deleteApiKey 或 initParams.btDeleteClick.click')
            }
          } })
      }

      initParams.btDetailClick = initParams.btDetailClick || {}
      const btDetailText = this.$isNotEmpty(initParams.btDetailClick.text)
        ? initParams.btDetailClick.text : '详情'
      let btDetailClick

      if (this.$isEmpty(initParams.hideCurdButton) || initParams.hideCurdButton.indexOf(btDetailText) === -1) {
        initParams.btDetailClick = initParams.btDetailClick || {}
        btDetailClick = (row, bt) => {
          const showEditDlgResult = this.showEditDlg(false, row, bt)
          if (!showEditDlgResult) {
            if (this.$isNotEmpty(initParams.btDetailClick.click)) {
              if (initParams.btDetailClickCustom) {
                initParams.btDetailClickCustom(row, initParams.btDetailClick.click)
              } else {
                initParams.btDetailClick.click(row, bt)
              }
            } else {
              this.$message.info('未找到响应函数：<br/>initParams.btDetailClick.click')
            }
          }
        }

        // 设置双击行打开详情，前提是没有单独设置双击行响应事件时
        if (this.$isEmpty(initParams.callbackRowDblclick)) {
          initParams.callbackRowDblclick = btDetailClick
        }

        buttons.push({
          text: btDetailText,
          icon: 'el-icon-document',
          enabledType: '1',
          click: bt => { btDetailClick(this.$getTableSelection(this.$refs.baseList.$refs.table)[0], bt) }
        })
      }

      // base-curd列表，没有设置超链接响应时，
      // 如果这时没有隐藏详情按钮，则点击超链接默认调用详情的响应方法
      // 这种情形调用详情的方法时，没有bt参数，需要手动构造相应参数
      if (initParams.clickA === undefined) {
        initParams.clickA = (scope, item, bt) => {
          if (btDetailClick) {
            btDetailClick(scope.row, bt)
          }
        }
      }

      if (this.$isNotEmpty(initParams.buttons)) {
        for (var i = buttons.length - 1; i >= 0; i--) {
          initParams.buttons.splice(0, 0, buttons[i])
        }
      } else {
        initParams.buttons = buttons
      }

      // 设置纯粹列表编辑模式
      if (this.$isNotEmpty(initParams.编辑针对的实体)) {
        initParams.params.dataApiKey = 'selectRformData'
        initParams.params.dataType = initParams.编辑针对的实体
        initParams.params.不需要处理实体外键 = ''
        initParams.params.isEdit = true
        initParams.isShowOrderNumber = true
        initParams.btAfters = {
          '导出': '复制', '保存': '导出' }

        // 新增删除上移下移复制等按钮会根据选择行的变化，其可用性会变化
        // 这里强制非编辑模式时这些按钮不可用
        const rowCheckedCallbackEx = initParams.rowCheckedCallback
        initParams.rowCheckedCallback = (rows) => {
          if (rowCheckedCallbackEx) {
            rowCheckedCallbackEx(rows)
          }
          if (initParams.params.isEdit === false) {
            this.setBtProperty('新增', 'disabled', true)
            this.setBtProperty('删除', 'disabled', true)
            this.setBtProperty('上移', 'disabled', true)
            this.setBtProperty('下移', 'disabled', true)
            this.setBtProperty('复制', 'disabled', true)
          }
        }
        // 设置单击行打开详情，前提是没有单独设置单击行响应事件时
        if (this.$isEmpty(initParams.clickRowCallback)) {
          initParams.clickRowCallback = (row) => {
            const editIdMap = {}
            editIdMap[this.$getRowId(row)] = true
            this.$refs.baseList.changeEditRow(editIdMap)
          }
        }

        initParams.buttons.push(
          { text: '保存', icon: 'el-icon-document-checked', disabled: false, click: (bt) => {
            // 默认列表可以为空
            const listCanBeEmpty =
                this.$isEmpty(initParams.列表可以为空) ? true : initParams.列表可以为空
            const vo = {
              dataType: initParams.编辑针对的实体,
              pageData: JSON.stringify(this.pageData),
              exParams: { 列表可以为空: listCanBeEmpty, 不需要处理实体外键: '' }}
            this.$callApiConfirm(
              '确定要保存吗？',
              initParams.beforeEditSave,
              'saveRformDataVo', vo,
              result => {
                this.reloadTable()
              }, result => {
                if (this.$isEmpty(result.attributes)) {
                  this.$message.error(result.msg)
                } else {
                  this.$refs.baseList.showError(result.attributes, false)
                }
              })
          } })
      }

      // 处理行编辑表单开关剩余的设置
      if (this.isEditRowForm) {
        initParams.showPager = false
        if (initParams.supportRowUpAndDown === undefined) {
          initParams.supportRowUpAndDown = true
        }

        if (initParams.btAddClick) {
          initParams.btAddClick.click = this.addRow
        }
        if (initParams.btDeleteClick) {
          initParams.btDeleteClick.click = this.deleteRow
        }

        var reloadTableCallbackEx = initParams.reloadTableCallback || (() => {})
        initParams.reloadTableCallback = (result, table, baseListObj) => {
          this.reloadTableCallback(result, table, baseListObj)
          reloadTableCallbackEx(result, table, baseListObj)
        }
      }

      this.$saveInitParams(this.$parent, initParams, undefined, this)
      this.$refs.baseList.init(initParams)
    },
    initEditRowForm(pageData, isEditing, editableAny, exParams) { // 初始化可编辑行表单
      isEditing = (isEditing === undefined) ? true : isEditing
      editableAny = (editableAny === undefined) ? false : editableAny

      exParams = exParams || {}
      let isNoSelection = !isEditing
      if (exParams.isNoSelection !== undefined) {
        isNoSelection = exParams.isNoSelection
      }
      var initParams = Object.assign({
        isEditing: isEditing,
        isEditRowForm: true,
        editableAny: editableAny,
        loadTable: false,
        noSelection: isNoSelection,
        isNoEditButtons: exParams.isAuditMode || !isEditing,
        showPager: false,
        params: {
          dataApiKey: '占位符',
          isEdit: isEditing }
      }, exParams)
      this.init(initParams)

      this.setButtonNormalNoPaddingTop(true)
      this.$nextTick(() => {
        if (pageData) {
          this.fillTable({
            attributes: {},
            data: pageData
          })
        }
      })
    },
    // 此方法成功后会刷新列表
    doActionByIds(actionName, apiKey, apiExParams, callbackSuccess) {
      const initParams = this.$getInitParams(this.$parent, this)
      var params = {
        apiKey: apiKey,
        actionName: actionName,
        apiExParams: apiExParams,
        callbackSuccess: callbackSuccess,
        isReloadTable: true,
        getIdsStr4doActionByIds: initParams.getIdsStr4doActionByIds
      }
      this.$refs.baseList.doActionByIds(params, initParams)
    },
    disableAllBts() {
      this.$refs.baseList.disableAllBts()
    },
    disableAllBtsResume() {
      this.$refs.baseList.disableAllBtsResume()
    },
    setButtonBarVisible(visible, isNoPaddingTop) {
      this.$refs.baseList.setButtonBarVisible(visible, isNoPaddingTop)
    },
    setButtonNormalNoPaddingTop(isNoPaddingTop) {
      this.$refs.baseList.setButtonNormalNoPaddingTop(isNoPaddingTop)
    },
    setNoPager() {
      this.$refs.baseList.setNoPager()
    },
    setSimpleList() { // 设置列表没有按钮和分页时，高度间隔等
      this.$refs.baseList.setSimpleList()
    },
    getTableCheckedIdsStr() { // 获取当前列表选择行的id逗号串
      return this.$refs.baseList.getTableCheckedIdsStr()
    },
    getTableCheckedIds() {
      return this.$refs.baseList.getTableCheckedIds()
    },
    getTableCheckedRows() {
      return this.$refs.baseList.getTableCheckedRows()
    },
    getTableSelection() {
      return this.$getTableSelection(this.getTable()) || []
    },
    getTable() { // 获取当前列表的表格对象
      return this.$refs.baseList.getTable()
    },
    setBtProperty(btText, property, value) {
      this.$refs.baseList.setBtProperty(btText, property, value)
    },
    handlePreView(row) {
      this.$refs.baseList.handlePreView(row)
    },
    getResultAttributes() {
      return this.resultAttributes
    },
    setBtEnabledForRowChecked(row, btText, disableValue) {
      if (this.$refs.baseList) {
        this.$refs.baseList.setBtEnabledForRowChecked(row, btText, disableValue)
      }
    },
    rowChecked(rows) {
      if (this.$refs.baseList) {
        this.$refs.baseList.rowChecked(rows)
      }
    },
    rowCellChangedFireManually(row, prop) { // 手动触发单元格值变化事件
      if (this.$refs.baseList) {
        this.$refs.baseList.rowCellChangedFireManually(row, prop)
      }
    },
    toggleRowSelection(row, selected) {
      if (this.$refs.baseList) {
        this.$refs.baseList.toggleRowSelection(row, selected)
      }
    },
    reloadTableCallback(result) {
      this.pageData = result.data
      if (this.isEditRowForm) {
        this.rowsData = result.data.rows
        this.cloneRow = {}
        this.headerIndexes = {}
        var headers = result.data.columns
        for (let i = 0; i < headers.length; i++) {
          var hd = headers[i]
          if (this.$isNotEmpty(hd.columnEntity)) {
            if (hd.columnEntity.colType !== '多选') {
              this.cloneRow[hd.prop] = hd.columnEntity.defaultValue
            } else {
              var value = hd.columnEntity.defaultValue.replace(/，/g, ',')
              hd.columnEntity.defaultValue = value.split(',')
              this.cloneRow[hd.prop] = hd.columnEntity.defaultValue
            }
          } else {
            this.cloneRow[hd.prop] = ''
          }
          // this.cloneRow[hd.label] = ''
          this.headerIndexes[hd.prop] = i + 1 // 第一列是checkbox，需+1
        }

        // 隐藏按钮栏与否
        if (!this.checkButton) {
          this.setButtonBarVisible(!this.isNoEditButtons, true)
        }
      }
    },
    updateRowsData(updatayear) {
      updatayear(this.cloneRow)
    },
    /**
     * 填充新行默认值
     * @param {Object} newRow 新增行
     */
    fillNewRowDefault(newRow) {
      const defaultMap = {
        '金额': '0.00',
        '小数': '0.00',
        '整数': '0',
        '百分比': '0%'
      }
      this.pageData?.columns?.forEach(col => {
        if (defaultMap[col.colType]) {
          newRow[col.prop] = defaultMap[col.colType]
        }
      })
    },
    addRow(bt) {
      const tableData = this.getBlistRowData()
      /**
       * upperLimit 上限数
       * upperLimitLine 上限显示行数
       */
      const upperLimit = this.blockConfig?.()?.upperLimit
      // 区块上限数限制
      if (tableData?.length > 0 && upperLimit > 0 && tableData?.length >= upperLimit) {
        this.$message.error(`上限数为${upperLimit}`)
        return
      }

      var newRow = this.$clone(this.cloneRow)
      var rowId = this.$getRowId(newRow)
      if (this.$isEmpty(rowId)) {
        newRow.id = 'newRow' + new Date().getTime()
      }
      this.fillNewRowDefault(newRow)
      if (this.addRowEx) {
        if (newRow.label === '') {
          newRow = this.addRowEx(newRow, this)
        } else {
          this.addRowEx(newRow, this)
        }
      }
      this.rowsData.push(newRow)
      this.$nextTick(() => {
        // 区块分页
        if (this.blockConfig) {
          this.blockPagination(this.blockConfig())
        }

        var scope = {}
        scope.$index = this.$refs.baseList.rowsData.length - 1
        this.$refs.baseList.formula(scope)
        this.$refs.baseList.uncheckedAllRows()
        this.$refs.baseList.clickRow(newRow)
        // this.$refs.baseList.bindLevel3CellClicked()

        // 自动弹出参照
        var autoRefCol
        this.$refs.baseList.columnsData.forEach(col => {
          if (this.$isNotEmpty(col.columnEntity) &&
            col.columnEntity.colType === '弹框' &&
            col.columnEntity.isPopupWhenAdding === '是') {
            autoRefCol = col
          }
        })
        if (autoRefCol) {
          this.$refs.baseList.rowCellClick({ row: newRow }, autoRefCol)
        }
      })
    },
    getHistoryOptions() {
      return this.$refs.baseList.getHistoryOptions()
    },
    clearRow() {
      if (!this.$refs.baseList ||
          !this.$refs.baseList.getTable()) {
        return
      }

      var allDatas = this.$refs.baseList.getRowsData()
      var allIds = []
      allDatas.forEach(item => {
        allIds.push(item.id)
      })
      var bt = {}
      bt.getRowIds = () => {
        return allIds
      }
      this.deleteRow(bt)
    },
    deleteRow(bt) {
      var rowIds = bt.getRowIds()
      // 被删除的数据
      var deleteRows = []
      for (let i = 0; i < this.rowsData.length; i++) {
        var row = this.rowsData[i]
        for (let j = 0; j < rowIds.length; j++) {
          var rowId = rowIds[j]
          if (this.$getRowId(row) === rowId) {
            deleteRows.push(row)
            break
          }
        }
      }
      // 区块 是否不允许删除 this.deleteRowBefore 函数返回true代表不能删除
      const isDeleteAllowed = this.deleteRowBefore?.(this, rowIds, deleteRows) !== true
      if (isDeleteAllowed) {
        this.$deleteRows(this.rowsData, rowIds)
      }

      // 区块分页
      if (this.blockConfig) {
        this.blockPagination(this.blockConfig())
      }

      this.$refs.baseList.uncheckedAllRows()
      if (!this.deleteNoRefreshBtn) {
        this.$nextTick(() => { this.$refs.baseList.rowChecked([]) })
      }
      if (isDeleteAllowed && this.deleteRowEx) {
        this.$refs.baseList.changeShowEmptyImg(false)
        this.deleteRowEx(this, rowIds, deleteRows)
      }
    },
    fillTable(
      result, lastHeightLightId, callback,
      initParams, exParams, isSearchBtn, afterCallBack) {
      if (this.$refs.baseList) {
        this.$refs.baseList.fillTable(
          result, lastHeightLightId, callback,
          initParams, exParams, isSearchBtn, afterCallBack)
      }
    },
    reloadTable(callback, initParams, exParams, isSearchBtn, afterCallBack) {
      if (initParams === undefined) {
        initParams = this.$getInitParams(this.$parent, this)
      }
      this.$refs.baseList.reloadTable(callback, initParams, exParams, isSearchBtn, afterCallBack)
    },
    setCellDisable(rowIdOrRowIndex, label, disabled) {
      this.$refs.baseList.setCellDisable(rowIdOrRowIndex, label, disabled)
    },
    setCellValue(rowIdOrRowIndex, propOrLabel, value) {
      this.$refs.baseList.setCellValue(rowIdOrRowIndex, propOrLabel, value)
    },
    getCellValue(rowIdOrRowIndex, propOrLabel) {
      return this.$refs.baseList.getCellValue(rowIdOrRowIndex, propOrLabel)
    },
    setEditable(isEditable) { // 动态改变行表单是否可编辑
      this.$refs.baseList.setEditable(isEditable)
    },
    reRenderBtns(hiddenButtons) {
      this.$refs.baseList.reRenderBtns(hiddenButtons)
    },
    doSearch(exParams) { // 手动触发搜索
      this.$refs.baseList.doSearch(exParams)
    },
    resetSearch() {
      this.$refs.baseList.resetSearch()
    },
    getRowsData() {
      return this.$refs.baseList.getRowsData()
    },
    initLeftTree() {
      this.$refs.baseList.initLeftTree()
    },
    getBaseListExData() {
      return this.$refs.baseList.getBaseListExData()
    },
    showError(errorData, isBlock) {
      return this.$refs.baseList.showError(errorData, isBlock)
    },
    clearError() {
      this.$refs.baseList.clearError()
    },
    clearCurrentRFormTable() {
      this.$refs.baseList.clearCurrentRFormTable()
    },
    // 显示编辑框，使用bt.setParam('编辑内容组件', 'xxx')机制
    showEditDialog(editCompName, bt, isEdit, exParams) {
      if (this.$isEmpty(editCompName)) {
        this.$message.success('editCompName不能为空')
        return
      }
      if (this.$isEmpty(bt)) {
        this.$message.success('bt不能为空')
        return
      }

      if (this.showEditDlg) {
        const row = bt.getRowData()
        if (this.$isEmpty(row)) {
          this.$message.success('没有选中行')
          return
        }

        if (isEdit === undefined) {
          isEdit = true
        }
        bt.setParam('编辑内容组件', editCompName)
        this.showEditDlg(isEdit, row, bt, exParams)
      }
    },
    // 常规的弹框行表单编辑场景显示弹框
    showEditRform(exParams) {
      const params = exParams || {}
      if (this.$isEmpty(params.bt)) {
        this.$message.success('按钮对象params.bt不能为空')
        return
      }
      if (this.$isEmpty(params.dataType)) {
        this.$message.success('行表单针对实体类型params.dataType不能为空')
        return
      }
      if (params.isEdit === undefined) {
        params.isEdit = true
      }

      // 如果bt保留在params中，会出现与vue机制冲突的情况
      const bt = params.bt
      delete params.bt
      params.实体外键 = bt.getRowId()
      this.showEditDialog(
        'base-edit-rform', bt, params.isEdit, params)
    },
    baseListBtClick(button) {
      // 点击任何按钮时，向按钮对象中填充数据，提供给业务场景使用
      button.params.baseCurdObj = this
      this.$emit('baseListBtClick', button)
    },
    addRowsData(newRow) {
      // 如果是数组(一次性推入多行)
      if (Array.isArray(newRow)) {
        newRow?.map((row) => {
          if (!row.hasOwnProperty('id')) {
            row.id = 'newRow' + new Date().getTime() + getUUID()
          }
          row = { ...this.cloneRow, ...row }
        })
        this.blockRformSetCode(newRow)
        this.addBaUniqueFlag?.(newRow)
        this.$refs.baseList.addRowsData(newRow)
        return
      }
      const row = { ...this.cloneRow, ...newRow }
      if (!row.hasOwnProperty('id')) {
        row.id = 'newRow' + new Date().getTime() + getUUID()
      }
      this.blockRformSetCode(row)
      this.addBaUniqueFlag?.(row)
      this.$refs.baseList.addRowsData(row)
    },
    // 区块行表单应用表单编码设置
    blockRformSetCode(rows) {
      const rformParams = this.$getInitParams(this.$parent, this)?.rformParams
      const hasOption = (labelOrigin, dataRef) => {
        return rformParams?.isRefNoCode?.(labelOrigin, dataRef)
      }
      const loop = (row) => {
        /*eslint no-labels: ["error", { "allowLoop": true }]*/
        colLoop: for (let j = 0; j < this.pageData.columns?.length; j++) {
          const col = this.pageData.columns[j]
          for (let k = 0; k < Object.keys(row)?.length; k++) {
            const key = Object.keys(row)[k]
            if (col.columnEntity.dataRef) {
              const noCode = hasOption(col.columnEntity.labelOrigin, col.columnEntity.dataRef)
              if (!noCode) {
                continue colLoop
              }
              if (row[key] && key === col.prop && noCode) {
                row[key] = row[key]?.includes(' ') ? row[key].split(' ')[1] : row[key]
              }
            }
          }
        }
      }
      if (Array.isArray(rows)) {
        for (let i = 0; i < rows?.length; i++) {
          const row = rows[i]
          loop(row)
        }
      } else {
        loop(rows)
      }
      return false
    },
    blockPagination(blockConfig) {
      // 区块分页
      const upperLimitLine = blockConfig?.upperLimitLine
      const dataTotal = this.getBlistRowData()?.length
      const showPager = upperLimitLine > 0 && dataTotal > upperLimitLine
      const pageSizes = upperLimitLine
      const pagerConfig = {
        showPager,
        pageSizes,
        dataTotal,
        blockConfig
      }
      this.$refs.baseList?.changePagerConfig?.(pagerConfig)
    },
    reInitLayout() {
      this.$refs.baseList?.reInitLayout()
    }
  }
}
</script>

<style scoped lang='scss'>
</style>
