<template>
  <page ref="pageObj">
    <template #pageContent>
      <el-tabs
        v-model="activeTab"
        @tab-click="tabClick"
        id="b-list-wf"
        :class="['base-tab', { 'base-tab-hide': hideTabHeader }]">
        <el-tab-pane :label="tabTitleTodo" :name="tabTitleTodo" v-if="!isTodoHidden" :disabled="disabled">
          <slot name="listTodo"/>
        </el-tab-pane>
        <el-tab-pane :label="tabTitleDone" :name="tabTitleDone" v-if="!isDoneHidden" :disabled="disabled">
          <b-list ref="listDone"/>
        </el-tab-pane>
        <el-tab-pane :label="tabTitleNoDone" :name="tabTitleNoDone" v-if="!isDoneHidden" :disabled="disabled">
          <b-list ref="listNoDone"/>
        </el-tab-pane>
        <el-tab-pane :label="tabTitleAll" :name="tabTitleAll" v-if="!isAllHidden" :disabled="disabled">
          <b-list ref="listAll"/>
        </el-tab-pane>

        <el-tab-pane
          :key="tab.tabName"
          :label="tab.tabLabel"
          :name="tab.tabName"
          :disabled="disabled"
          v-for="tab in exTabs">
          <component ref="exTabsContents" :is="tab.ref"/>
        </el-tab-pane>
      </el-tabs>

      <base-list-wf-submitform
        ref="bListWfSubmitform"
        :dialog.sync="isWfSubmitformDialog"/>
    </template>
  </page>
</template>

<script>
import BList from './base-list'
import { store } from '@/utils/sunmei-data'

export default {
  name: 'b-list-wf-style',
  inject: {
    provideTab: { default: undefined }
  },
  provide() {
    return {
      tabDisabled: this.tabDisabled
    }
  },
  components: { BList },
  created() {
    var this_ = this
    this_.$onEvent(this, {
      currencyBaseConfirm(operationName, apiKey, params, initParams, callBack) {
        this_.currencyBaseConfirm(
          operationName,
          apiKey,
          params,
          initParams,
          callBack
        )
      }
    })
  },
  data() {
    return {
      exTabs: [],
      isWfSubmitformDialog: false,
      tabTitleTodo: '待审核',
      tabTitleDone: '审核通过',
      tabTitleNoDone:'审核不通过',
      tabTitleAll: '全部',
      tabTitleTodoSize: 0,
      tabTitleDoneSize: 0,
      tabTitleNoDoneSize: 0,
      tabTitleAllSize: 0,
      activeTab: '待审核',
      isApply: true,
      tabMap: {},
      isTodo: true,
      isDone: false,
      isAll: false,
      isTodoHidden: false,
      isDoneHidden: false,
      isAllHidden: false,
      hideTabHeader: false,
      checkedRow: [],
      dataApiKey: '',
      historyDlgShow: false,
      noCommonShowDetail: false,
      showTreeOrigin: undefined,
      hasOfflineAudit: false, // 当前流程是否启用了线下审核
      isExportPrintFile: false,
      showButtons: [],
      disabled: true
    }
  },
  computed: {
    fromParentTab() {
      if (this.provideTab) {
        return this.provideTab()
      } else {
        return this.activeTab
      }
    }
  },
  methods: {
    init(initParams) {
      this.isListObj = true
      this.$parent.isListObj = true
      if (this.showTreeOrigin === undefined) {
        // 记录最初的值
        this.showTreeOrigin = initParams.showTree || false
      }

      initParams.buttons = initParams.buttons || []
      this.extButtons = this.extButtons || initParams.buttons
      initParams.tab = this.activeTab
      this.$saveInitParams(this, initParams)

      if (this.$isEmpty(initParams.params.dataType)) {
        this.$message.error('dataType不能为空')
        return
      } else if (this.$isEmpty(initParams.params.dataApiKey)) {
        initParams.params.actionKey = 'selectWfList'
        initParams.params.dataApiKey = 'wfAction' + initParams.params.dataType
        initParams.params.apiKey = initParams.params.dataApiKey
        initParams.params.deleteApiKey = 'WFDELETE'
      }

      if (this.$isEmpty(initParams.params.dataApiKey)) {
        this.$message.error('dataApiKey不能为空')
        return
      }

      this.dataApiKey = initParams.params.dataApiKey
      this.isApply = initParams.isApply
      this.tabMap = {}
      this.tabMap[this.tabTitleTodo] = this.$parent.$refs.listTodo
      this.tabMap[this.tabTitleDone] = this.$refs.listDone
      this.tabMap[this.tabTitleNoDone] = this.$refs.listNoDone
      this.tabMap[this.tabTitleAll] = this.$refs.listAll
      initParams.tab = initParams.tab || this.tabTitleTodo

      // 使用通用的弹框详情响应，同时审核列表的已办和待办的双击也是使用这个响应
      if (initParams.noCommonShowDetail) {
        this.noCommonShowDetail = initParams.noCommonShowDetail
      }

      // 流程是否启用线下审核功能
      if (initParams.hasOfflineAudit) {
        this.hasOfflineAudit = initParams.hasOfflineAudit
      }

      if (
        this.noCommonShowDetail !== true &&
        initParams.btDetailClick === undefined
      ) {
        initParams.btDetailClick = {
          click: (row) => {
            this.$showDetail(row.ID, undefined, undefined, undefined,
              { pageElementConfigFlag: initParams.pageElementConfigFlag,
                isAllWfAuditConfigFlag: initParams.isAllWfAuditConfigFlag })
          }
        }
      }

      // 如果没有设置needProcessApi，但是设置了supportProcess
      // 则统一使用selectWfListNeedProcess接口查询是否启用流程
      if (
        this.$isEmpty(initParams.params.needProcessApi) &&
        this.$isNotEmpty(initParams.supportProcess) &&
        initParams.supportProcess === true
      ) {
        initParams.params.needProcessApi = 'selectWfListNeedProcess'
        initParams.params.needProcessParams =
          initParams.params.needProcessParams || {}
        initParams.params.needProcessParams['dataType'] =
          initParams.params.dataType
        initParams.params.needProcessParams['bizName'] = initParams.bizName
        initParams.params.needProcessParams['menuId'] = store.get('menuData').getMenuId() // 菜单id
      }

      var initNeedProcess = () => {
        // 页面可通过传递showProcessTabAnyway值，来忽略后端返回的needProcess
        // 这样可以强制显示顶部的页签，这里不能在外界直接设置needProcess
        // 否则会导致新增按钮的行为有错
        if (this.$isNotEmpty(initParams.showProcessTabAnyway)) {
          initParams.needProcess = initParams.showProcessTabAnyway
        }

        var tabList = this.tabMap[initParams.tab]
        this.setNeedProcess(initParams)

        // 申请列表的待办时，设置“送审”按钮在新增的后面
        // 其他情形不处理“送审”按钮的位置
        initParams.btAfters = initParams.btAfters || {}
        if (this.isApply && initParams.tab === this.tabTitleTodo) {
          initParams.btAfters = Object.assign(initParams.btAfters, {
            送审: '新增'
          })

          // 有线下审核时，按钮放在送审后面
          if (this.hasOfflineAudit) {
            initParams.btAfters = Object.assign(initParams.btAfters, {
              线下审核: '送审'
            })
          }
        } else {
          delete initParams.btAfters['送审']
        }
        if (!(initParams.params.dataType === 'CformDataEntity')) {
          delete initParams.params.FORM_TYPE_eq
        }

        // 设置顶部3个页签之后，外界可再自定义处理
        // 在init列表之前执行，这样可以定制按钮可见性
        if (initParams.doAfterInitNeedProcess) {
          initParams.doAfterInitNeedProcess(this, initParams)
        }

        tabList.init(initParams)
      }

      initParams.hiddenButtons = initParams.hiddenButtons || []
      if (!this.$isNotEmpty(initParams.needProcess)) {
        if (this.$isNotEmpty(initParams.params.needProcessApi)) {
          var apiParams = initParams.params.needProcessParams || {}
          apiParams.isApply = initParams.isApply // 是否是申请列表

          this.$callApiParams(
            initParams.params.needProcessApi,
            apiParams,
            (result) => {
              console.log(result)
              // 默认找result.attributes中是否有needProcess
              if (this.$isNotEmpty(result.attributes) &&
                this.$isNotEmpty(result.attributes.needProcess)) {
                initParams.needProcess = result.attributes.needProcess
              }

              // 由后端返回是否显示导出打印文件按钮
              this.isExportPrintFile = this.$isNotEmpty(result.attributes)
                ? (result.attributes['hide_导出打印文件'] === true) : ''

              // 由后端控制查询条件显示
              const selects = result.attributes['查询条件']
              if (this.$isNotEmpty(selects)) {
                const addSelects = selects.新增查询
                const showSelects = selects.显示查询
                const searchForm = []
                if (this.$isNotEmpty(initParams.searchForm)) {
                  initParams.searchForm.forEach(item => {
                    if (showSelects.indexOf(item.split(':')[0]) > -1) {
                      searchForm.push(item)
                    }
                  })
                  initParams.searchForm = searchForm.concat(addSelects)
                }
              }
              // 由后端控制按钮显示
              const showButtons = result.attributes['显示按钮']
              if (this.$isNotEmpty(showButtons)) {
                this.showButtons = showButtons
              }

              if (initParams.needProcessCallBack) {
                initParams.needProcessCallBack(result) // 回调函数，让外部页面处理数据
              }

              // 填充接口返回的隐藏按钮的数据
              var reslutData = result.data || {}
              var extraData = reslutData.extraData || {}
              var hiddenButtons = extraData.hiddenButtons || []
              hiddenButtons.forEach((bt) => initParams.hiddenButtons.push(bt))

              initNeedProcess()
              return true
            }
          )
        } else {
          this.$message.error('查询是否需要流程，needProcessApi不能为空')
          return
        }
      } else {
        initNeedProcess()
      }
    },
    setTabName(initParams) {
      const copyParams = { ...initParams, actionKey: 'getAllListCounts' }
      this.$callApiParams(copyParams.dataApiKey, copyParams, (result) => {
        this.tabTitleTodoSize = result.data.todoSize
        this.tabTitleDoneSize = result.data.doneSize
        this.tabTitleNoDoneSize = result.data.doneNoSize
        this.tabTitleAllSize = result.data.allSize
        // this.setTabNameStr()
        return true
      })
    },
    setTabNameStr() {
      this.tabTitleTodo = '待审核(' + this.tabTitleTodoSize + ')'
      this.tabTitleDone = '审核通过(' + this.tabTitleDoneSize + ')'
      this.tabTitleNoDone = '审核不通过(' + this.tabTitleNoDoneSize + ')'
      this.tabTitleAll = '全部(' + this.tabTitleAllSize + ')'
      if (this.activeTab.indexOf('待审核') !== -1) {
        this.activeTab = '待审核(' + this.tabTitleTodoSize + ')'
      } else if (this.activeTab.indexOf('审核通过') !== -1) {
        this.activeTab = '审核通过(' + this.tabTitleDoneSize + ')'
      }else if (this.activeTab.indexOf('审核不通过') !== -1) {
        this.activeTab = '审核不通过(' + this.tabTitleNoDoneSize + ')'
      } else if (this.activeTab.indexOf('全部') !== -1) {
        this.activeTab = '全部(' + this.tabTitleAllSize + ')'
      }
    },
    setNeedProcess(initParams) {
      if (initParams.needProcess) {
        this.hideTabHeader = false
        initParams.params.needProcess = true
        this.$nextTick(() => {
          if (this.$refs.pageObj.commonPageClassEx.indexOf('hasWfTabs') < 0) {
            this.$refs.pageObj.commonPageClassEx =
              this.$refs.pageObj.commonPageClassEx + ' hasWfTabs'
          }
        })
      } else {
        this.hideTabHeader = true
        initParams.params.needProcess = false
        initParams.params.STATUS = null
      }

      initParams.showTree = this.showTreeOrigin
      if (initParams.loadTable === undefined) {
        initParams.loadTable = true
      }

      // 组织不同页签按钮
      var buttons = []
      var status = initParams.params.STATUS
      var buttonMap = this.initButtonMap(initParams)
      initParams.loadTable = true
      var isShowBtPrintPayee = initParams.isShowBtPrintPayee
      if (this.activeTab === this.tabTitleTodo) {
        if (initParams.needProcess) {
          if (initParams.isApply) {
            if (isShowBtPrintPayee) {
              buttons = [
                buttonMap['送审'],
                buttonMap['审核历史'],
                buttonMap['打印'],
                buttonMap['打印收款人'],
                buttonMap['导出打印文件']
              ]
            } else {
              buttons = [
                buttonMap['送审'],
                buttonMap['审核历史'],
                buttonMap['打印'],
                buttonMap['导出打印文件']
              ]
            }
            if (this.hasOfflineAudit) {
              buttons.push(buttonMap['线下审核'])
            }
          } else {
            // 审核的待办在base-list-wf-audit.vue中特别处理
            // 这里不设置按钮，也不显示其他通用按钮
            // buttons = [buttonMap['审核框'], buttonMap['作废'], buttonMap['审核历史']]
            buttons = []
            initParams.hiddenButtons.push('导出')
            initParams.showTree = true
            initParams.loadTable = false

            // 审核待办页面主内容不显示顶端按钮
            this.$parent.$refs.listTodo.setButtonBarVisible(false)
          }
        }
      } else if (this.activeTab === this.tabTitleDone) {
        if (isShowBtPrintPayee) {
          buttons = [
            buttonMap['撤回'],
            buttonMap['详情'],
            buttonMap['审核历史'],
            buttonMap['打印'],
            buttonMap['打印收款人'],
            buttonMap['导出打印文件']
          ]
        } else {
          buttons = [
            buttonMap['撤回'],
            buttonMap['详情'],
            buttonMap['审核历史'],
            buttonMap['打印'],
            buttonMap['导出打印文件']
          ]
        }
        this.extButtons.forEach(btn => {
          if (this.$isNotEmpty(btn.show) && btn.show.indexOf(this.activeTab) !== -1) {
            buttons.unshift(btn)
          }
        })
        status = initParams.isApply ? '已送审' : '已审核'
      }else if (this.activeTab === this.tabTitleNoDone) {
        status="审核不通过"
      } else if (this.activeTab === this.tabTitleAll) {
        if (isShowBtPrintPayee) {
          buttons = [buttonMap['详情'], buttonMap['审核历史'], buttonMap['打印'], buttonMap['打印收款人'], buttonMap['导出打印文件']]
        } else {
          buttons = [buttonMap['详情'], buttonMap['审核历史'], buttonMap['打印'], buttonMap['导出打印文件']]
        }
        this.extButtons.forEach(btn => {
          if (this.$isNotEmpty(btn.show) && btn.show.indexOf(this.activeTab) !== -1) {
            buttons.unshift(btn)
          }
        })
        console.log(initParams.isApply)
        status = initParams.isApply ? null : '全部'
      }
      if (this.isExportPrintFile) {
        // 是否显示导出打印文件按钮
        buttons = buttons.filter(button => button.text !== '导出打印文件')
      }

      if (this.$route.path.indexOf('ba-biz-list-audit-adjust') !== -1) {
        // 指标调整审核的界面,不要出现打印按钮
        buttons = buttons.filter(button => button.text !== '打印' && button.text !== '导出打印文件')
      }
      initParams.params.STATUS = status

      // 一些场景需要强制指定查询的状态
      if (initParams.params.queryThisStatusAnyway !== undefined) {
        // initParams.params.STATUS = initParams.params.queryThisStatusAnyway
        initParams.params.STATUS_in = initParams.params.queryThisStatusAnyway
      }

      initParams.buttons = initParams.buttons.concat(buttons)
      if (this.$isNotEmpty(this.showButtons)) {
        this.handleShowButtons(initParams)
      }
      // 最终对外提供处理tab按钮的事件
      if (this.$isNotEmpty(initParams.exHandleTabButtons)) {
        initParams.exHandleTabButtons(initParams.buttons, this.activeTab)
      }
    },
    handleShowButtons(initParams) {
      const showButtons = []
      initParams.buttons.forEach(bt => {
        if (this.showButtons.indexOf(bt.text) > -1) {
          showButtons.push(bt)
        }
      })
      initParams.buttons = showButtons
    },
    tabClick(tab) {
      this.activeTab = tab.name

      if (tab.name !== this.tabTitleTodo &&
        tab.name !== this.tabTitleDone &&
        tab.name !== this.tabTitleNoDone &&
        tab.name !== this.tabTitleAll) {
        // 是扩展的tab，则不作下面的处理
        return
      }

      this.$nextTick(() => {
        var isNotTabTodo = tab.name !== this.tabTitleTodo
        var exData = { isNotTabTodo: isNotTabTodo }
        var $root = this.$getObjRoot(this)
        this.$event(this, 'reLoadAnyway')
        if (!isNotTabTodo) {
          //走的第一个tab
          $root.$reInit($root, exData)
        } else {
          // 业务页面通过设置isTabsAllUseCustomButtons来实现
          // 3个tab的页面都带有自定义的按钮
          // 走的第二个tab
          var initParams = this.$getInitParams(this)
          // 审核列表的已办和全部双击也响应详情
          if (!this.isApply && this.noCommonShowDetail !== true) {
            exData.callbackRowDblclick = (row) => {
              this.btDetailClick(initParams, row)
            }
          }

          //  触发该方法重新刷新，更新实例对象
          var $obj = initParams.isTabsAllUseCustomButtons ? $root : this
          $obj.$reInit($obj, exData)
        }
      })
    },
    initButtonMap(initParams) {
      var buttons = [
        {
          text: '送审',
          icon: 'el-icon-position',
          enabledType: '1+',
          click: (bt) => this.btSubmitClick(initParams)
        },
        {
          text: '审核历史',
          icon: 'el-icon-time',
          enabledType: '1',
          click: (bt) => this.btAuditHistory(initParams)
        },
        {
          text: '打印',
          icon: 'el-icon-printer',
          enabledType: '1',
          click: (bt) => this.btPrint(initParams)
        },
        {
          text: '打印收款人',
          icon: 'el-icon-printer',
          enabledType: '1',
          click: (bt) => this.btPrintPayee(initParams)
        },
        {
          text: '审核框',
          icon: 'el-icon-reading',
          enabledType: '1+',
          click: (bt) => this.btAuditClick(initParams)
        },
        {
          text: '作废',
          icon: 'el-icon-document-delete',
          enabledType: '1+',
          click: (bt) => this.btCancelClick(initParams)
        },
        {
          text: '撤回',
          icon: 'el-icon-back',
          enabledType: '1+',
          click: (bt) => this.btWithdrawClick(initParams)
        },
        {
          text: '详情',
          icon: 'el-icon-document',
          enabledType: '1',
          click: (bt) => this.btDetailClick(initParams)
        },
        {
          text: '导出打印文件',
          icon: 'el-icon-back',
          enabledType: '1',
          click: (bt) => this.btExportWps(initParams)
        }
      ]
      if (this.hasOfflineAudit) {
        buttons.push({
          text: '线下审核',
          icon: 'el-icon-edit-outline',
          enabledType: '1+',
          click: (bt) => this.btOfflineAuditClick(initParams)
        })
      }

      var buttonMap = {}
      buttons.forEach((bt) => {
        buttonMap[bt.text] = bt
      })
      return buttonMap
    },
    btSubmitClick(initParams) {
      this.$refs.bListWfSubmitform.init(this, initParams.params.dataType)
      this.isWfSubmitformDialog = true
      // var params = {}
      // this.currencyBaseConfirm('送审', 'WFSUBMIT', params, initParams)
    },
    btDetailClick(initParams, row) {
      if (this.$isNotEmpty(initParams.btDetailClick.click)) {
        row = row || this.getBaseListTable().selection[0] //  call获取表格数据
        initParams.btDetailClick.click(row)
      }
    },
    btPrint(initParams) {
      // // 获取被选中行
      // var rows = this.$getTableSelection(this.getBaseListTable())
      // if (initParams.printTemplateFormType) {
      //   this.$showPrint(rows[0].ID, initParams.printTemplateFormType)
      // } else {
      //   initParams.btPrint.click(rows[0])
      // }
      initParams.button = '打印'
      var row = this.$getTableSelection(this.getBaseListTable())[0]
      if (initParams.printTemplateFormType) {
        row.formType = initParams.printTemplateFormType
      }
      initParams.btExportWps.click(row, this.getBaseListTable(), initParams)
    },
    btPrintPayee(initParams) {
      // 获取被选中行
      var rows = this.$getTableSelection(this.getBaseListTable())
      var params = {
        doExcelExport: 'doExcelExport',
        exportExcelName: '报销申请(收款人).xls',
        BILL_ID_eq: rows[0].ID
      }
      this.$fileDownloadBykey('printPayee', params)
    },
    btAuditHistory(initParams) {
      if (initParams.loadTable) {
        this.checkedRow = this.getBaseListTable().selection[0]
      } else {
        this.checkedRow = this.$parent.$refs.table.selection[0]
      }
      this.$showWfHistory(this.checkedRow.ID, null, null, this.dataApiKey)
    },
    btWithdrawClick(initParams) {
      var params = {}
      this.currencyBaseConfirm('撤回', 'WFWITHDRAW', params, initParams)
    },
    btAuditClick(initParams) {
      this.$parent.btAuditClick(initParams)
    },
    btCancelClick(initParams) {
      this.$parent.btCancelClick(initParams)
    },
    btOfflineAuditClick(initParams) {
      this.$parent.btOfflineAuditClick(initParams)
    },
    currencyBaseConfirm(operationName, apiKey, params, initParams, callBack) {
      var table = this.getBaseListTable()
      var rowIds = this.$getTableCheckedIdsStr(table)
      params = params || {}
      params.ids = rowIds
      params.dataType = initParams.params.dataType
      var message = `确认要${operationName}${table.selection.length} 条数据吗？`
      this.$callApiParamsConfirm(message, null, apiKey, params, (result) => {
        if (
          operationName === '撤回' &&
          this.$route.path.includes('audit-tab')
        ) {
          this.$event(this, 'refreshCount')
        }
        // this.reloadTable()
        if (callBack) {
          callBack()
        } else {
          if (this.activeTab === this.tabTitleTodo) {
            this.$parent.reLoad()
          } else {
            this.$reInit(this)
            this.setTabName(initParams.params)
          }
          // this.afterLoad()
        }
      })
    },

    doActionByIds(actionName, apiKey, initParams, callbackSuccess, exParams) {
      // 此方法成功后会刷新列表
      exParams = exParams || {}
      var params = {
        apiKey: apiKey,
        actionName: actionName,
        apiExParams: { dataType: initParams.params.dataType },
        callbackSuccess: callbackSuccess,
        isReloadTable: true
      }
      Object.assign(params, exParams)
      Object.assign(params.apiExParams, exParams)
      this.getBaseList().doActionByIds(params, initParams)
    },

    getBaseList() {
      var baseList = null
      if (this.activeTab === this.tabTitleTodo) {
        baseList = this.$parent.$refs.listTodo.$refs.baseList
      } else if (this.activeTab === this.tabTitleDone) {
        baseList = this.$refs.listDone
      } else if (this.activeTab === this.tabTitleAll) {
        baseList = this.$refs.listAll
      }
      return baseList
    },
    getBaseListTable() {
      // 返回表格对象
      return this.getBaseList().$refs.table
    },

    // 调用base-page的doBtClick方法
    doBtClick(btText, params) {
      this.$call(this.getBaseList(), 'basePage', 'doBtClick', btText, params)
    },

    afterLoad() {
      if (this.activeTab === this.tabTitleTodo) {
        this.isDone = false
        this.isAll = false
      } else if (this.activeTab === this.tabTitleDone) {
        this.isTodo = false
        this.isAll = false
      } else if (this.activeTab === this.tabTitleAll) {
        this.isTodo = false
        this.isDone = false
      }
    },
    changeActiveTab(tab) {
      this.activeTab = ''
      this.$nextTick(() => {
        this.tabClick(tab)
      })
    },
    // 导出打印文件
    btExportWps(initParams) {
      // const row = this.getBaseListTable().selection[0] //  call获取表格数据
      // this.$exportWps(row)
      initParams.button = '导出打印文件'
      var rows = this.$getTableSelection(this.getBaseListTable())[0]
      initParams.btExportWps.click(rows, this.getBaseListTable(), initParams)
    },
    tabDisabled(val) {
      this.disabled = val
    }
  },
  mounted() {
    this.activeTab = this.fromParentTab
    const pathQuery = this.$getPathQuery()
    if (this.$isNotEmpty(pathQuery) && this.$isNotEmpty(pathQuery.tab)) {
      this.changeActiveTab({ name: pathQuery.tab })
    }
  }
}
</script>

<style lang="scss" scoped>
  #b-list-wf {
    display: flex;
    flex-direction: column;
    ::v-deep.el-tabs__content {
      flex: 1;
    }
  }
</style>
