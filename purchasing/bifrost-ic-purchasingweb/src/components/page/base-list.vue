<template>
  <div style="height: 100%" id="baseList">
    <b-page
      ref="basePage"
      :loading="loading"
      @baseListBtClick="baseListBtClick"
      @handleSearch="handleSearch"
    >
      <template #dbColLeft>
        <slot name="dbColLeft"/>
      </template>
      <template #toolbarExtend>
        <slot name="toolbarExtend"/>
      </template>
      <template #mainContent>
        <!-- <loading ref="loading" :hasTop="!!radioList.length"/> -->
        <slot name="mainContent">
          <slot name="contentHeader"/>
          <div :class="`${listContentClass} ${listContentNoPager} ${listContentNoSelectionClass}`">
            <search-radio :list="radioList" @search="radioSearch"/>
            <slot name="tableTopInfo" />
            <div :id="tableId"
                 class="listContentMain"
                 v-loading="loading"
                 element-loading-spinner="el-icon-loading">
              <!-- 表格的height属性不能删除 兼容低版本浏览器进入区块页面卡死 -->
              <component
                v-if="loadTable && tableInitialized"
                :is="isVxe?'vxe-table':'el-table'"
                ref="table"
                :class="
                  `${baseListTableClass} theMainTable-${listContentSubId} ${tableExStyleClass} blist-table ${currentRFormTable} ${hasSummaryClass}`
                "
                :data="tableData"
                :row-style="rowStyle"
                border
                size="small"
                v-bind="attrObj[tableType]"

                :show-summary="showSummary"
                @select="handleRowSelect"
                @selection-change="rowChecked"
                @row-click="clickRow"
                @header-click="headerClick"

                :treeConfig="treeConfig"
                :show-footer="showSummary"
                :checkbox-config="{highlight: !noRowHighlight,checkMethod: checCheckboxkMethod}"
                @checkbox-change="rowChecked"
                @cell-click="cellClick"
                @cell-dblclick="rowDblclick"
                @checkbox-all="checkboxAll"
              >
                <template slot="empty">
                  <div class="table-empty" >
                    <img v-if="showEmptyImg" class="table-empty-img" src="@/assets/image/empty.png" alt="">
                    <p class="table-empty-p">暂无数据</p>
                  </div>
                </template>
                <component v-if="!noSelection"
                  :is="isVxe?'vxe-column':'el-table-column'"
                  :type="isVxe?'checkbox':'selection'"
                  :width="selectionColumnWidth"
                  :selectable="(row)=>checCheckboxkMethod({row})"
                />
                <component v-if="orderNumber.isShow"
                  :is="isVxe ? 'vxe-column':'el-table-column'"
                  :type="isVxe ? 'seq' : 'index'"
                  :align="isVxe?'left':'center'"
                  :header-align="isVxe ? 'center' : ''"
                  :width="isVxe ? '60' : ''"
                  :index="indexMethod"

                  :label="orderNumber.label"

                  tree-node
                  :title="orderNumber.label"
                />
                <component
                  v-for="(item, columnIndex) in getColumnsData(columnsData)"
                  :key="columnIndex"
                  :is="isVxe?'vxe-column':'el-table-column'"
                  header-align="center"
                  :align="item.align"
                  :width="item.width==='0px'||item.width===''||item.width==='0'?'':item.width"
                  :sortable="item.sortable"

                  :prop="item.prop"
                  :fixed="columnFixed(item, columnIndex)"
                  :label="item.label"
                  :show-overflow-tooltip="!(item.editable && item.editableType !== 'a' && item.editableType !== 'html')"

                  resizable
                  :field="item.prop"
                  :title="item.label"
                  >
                    <component
                      v-for="(itemLevel2, index) in getColumnsData(item.children)"
                      :key="index"
                      :is="isVxe?'vxe-column':'el-table-column'"
                      :width="itemLevel2.width==='0px'||itemLevel2.width===''||itemLevel2.width==='0'?null:itemLevel2.width"
                      :min-width="itemLevel2.width==='0px'||itemLevel2.width===''||itemLevel2.width==='0'?100:itemLevel2.width"
                      :align="itemLevel2.align"
                      :sortable="itemLevel2.sortable"
                      header-align="center"

                      show-overflow-tooltip
                      :label="itemLevel2.label"
                      :prop="itemLevel2.prop"

                      :title="itemLevel2.label"
                      >
                      <component
                        v-for="(itemLevel3, index) in getColumnsData(itemLevel2.children)"
                        :key="index"
                        :is="isVxe?'vxe-column':'el-table-column'"
                        :width="itemLevel3.width==='0px'||itemLevel3.width===''||itemLevel3.width==='0'?null:itemLevel3.width"
                        :min-width="itemLevel3.width==='0px'||itemLevel3.width===''||itemLevel3.width==='0'?100:itemLevel3.width"
                        :align="itemLevel3.align"
                        :sortable="itemLevel3.sortable"
                        header-align="center"

                        show-overflow-tooltip
                        :label="itemLevel3.label"
                        :prop="itemLevel3.prop"

                        :title="itemLevel3.label"
                        >
                        <template v-slot:header>
                          <base-table-header :remindInfo="remindInfo" :column="itemLevel3" :editColMap="editColMap"/>
                        </template>
                        <template slot-scope="scope">
                          <slot name="tableRowSlot" :slotScope="scope">
                        <span v-if="!isItemEditable(scope.row, itemLevel3)"
                              :style="`width:100%;text-align:${itemLevel3.align}`"
                              :class="isColType({ item: itemLevel3, row: scope.row }, '三级明细列') ? 'level3DetailColSpan': ''"
                              :rowId="$getRowId(scope.row)"
                              :colProp="itemLevel3.prop"
                              @click="level3CellClicked(itemLevel3,scope.row)">

                          <span class="rowFormButton" v-if="itemLevel3.columnEntity && itemLevel3.columnEntity.isRowFormButton">
                              <el-button @click.native="rowCellClick(scope, itemLevel3)">
                                {{itemLevel3.columnEntity.rowFormButtonText}}
                              </el-button>
                          </span>

                          <span v-if="itemLevel3.editableType !== '文件上传'">
                          {{formatColData(scope.row, itemLevel3.prop)}}
                          </span>
                          <span v-if="itemLevel3.editableType === '文件上传'">
                              <base-list-cell-file v-model="scope.row[itemLevel3.prop]" :is-edit="false"/>
                          </span>
                        </span>

                        <span v-if="!isItemEditable(scope.row, itemLevel3) && itemLevel3.editableType === 'a'"
                              @click="clickA(scope, itemLevel3)">
                          <a>{{scope.row[itemLevel3.prop]}}</a>
                        </span>

                        <span v-if="isItemEditable(scope.row, itemLevel3) && itemLevel3.editableType !== 'a'" class="edit-container">
                          <span v-if="itemLevel3.editableType == '起始日期'"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="itemLevel3.prop">
                              <el-date-picker
                                v-model="scope.row[itemLevel3.prop]"
                                :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder=""
                                end-placeholder=""/>
                          </span>
                          <span v-if="itemLevel3.editableType == '文件上传'"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="itemLevel3.prop">
                              <base-list-cell-file v-model="scope.row[itemLevel3.prop]" :biz-id="$getRowId(scope.row)"/>
                          </span>
                          <span v-if="isColType({ item: itemLevel3, row: scope.row }, '三级明细列')"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="itemLevel3.prop"
                                @click="level3CellClicked(itemLevel3,scope.row)"
                                @blur="verifyRequired(scope, itemLevel3)">
                              <el-input v-model="scope.row[itemLevel3.prop]"
                                        :readonly="true"
                                        :disabled="scope.row[itemLevel3.label + '_禁用'] === true">
                                <i slot="prefix" class="el-input__icon el-icon-edit"/>
                              </el-input>
                          </span>
                          <span v-if="isColType({ item: itemLevel3, row: scope.row }, '没有列定义的数值列')">
                            <el-input
                              @change="rowCellChangedFire(scope, itemLevel3)"
                              @input="scope.row[itemLevel3.prop] = scope.row[itemLevel3.prop].replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')"
                              :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${itemLevel3.align}`"
                              :style="`width:100%;text-align:${itemLevel3.align}`"
                              v-model="scope.row[itemLevel3.prop]"
                              @blur="verifyRequired(scope, itemLevel3)"/>
                          </span>
                          <span v-if="isColType({ item: itemLevel3, row: scope.row }, '文本金额数值')">
                            <el-input
                              v-if="itemLevel3.editableType == 'numInput' && (itemLevel3.columnEntity.colType == '整数' || itemLevel3.columnEntity.colType == '数值')"
                              @change="rowCellChangedFire(scope, itemLevel3)"
                              @input="scope.row[itemLevel3.prop] = scope.row[itemLevel3.prop].replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')"
                              :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${itemLevel3.align}`"
                              :style="`width:100%;text-align:${itemLevel3.align}`"
                              v-model="scope.row[itemLevel3.prop]"
                              @blur="verifyRequired(scope, itemLevel3)"/>
                            <input-money
                              v-if="itemLevel3.editableType == 'numInput' &&  itemLevel3.columnEntity.colType == '金额'"
                              @change="rowCellChangedFire(scope, itemLevel3)"
                              :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${itemLevel3.align}`"
                              :style="`width:100%;text-align:${itemLevel3.align}`"
                              v-model="scope.row[itemLevel3.prop]"
                            ></input-money>
                            <el-input
                              v-if="itemLevel3.editableType == 'input'"
                              @change="rowCellChangedFire(scope, itemLevel3)"
                              :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputAlign-${itemLevel3.align}`"
                              :style="`width:100%;text-align:${itemLevel3.align}`"
                              v-model="scope.row[itemLevel3.prop]"
                              @blur="verifyRequired(scope, itemLevel3)"/>
                          </span>
                          <span v-if="isColType({ item: itemLevel3, row: scope.row }, '日期')">
                            <el-date-picker type="date" value-format="yyyy-MM-dd"
                                            v-model="scope.row[itemLevel3.prop]"
                                            @change="rowCellChangedFire(scope, itemLevel3)"
                                            :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${itemLevel3.align}`"
                                            :style="`width:100%;text-align:${itemLevel3.align}`"
                                            placeholder="选择日期"/>
                          </span>
                          <span v-if="isColType({ item: itemLevel3, row: scope.row }, '单选')">
                            <el-radio-group v-model="scope.row[itemLevel3.prop]"
                                            @change="rowCellChangedFire(scope, itemLevel3)"
                                            @click.native="rowCellClick(scope, itemLevel3)"
                                            :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${itemLevel3.align}`"
                                            :style="`width:100%;text-align:${itemLevel3.align}`">
                              <el-radio v-for="(it, index) in itemLevel3.columnEntity.dataRef" :key="index" :label="it">{{it}}</el-radio>
                            </el-radio-group>
                          </span>
                          <span v-if="isColType({ item: itemLevel3, row: scope.row }, '多选')">
                            <el-checkbox-group  v-model="scope.row[itemLevel3.prop]"
                                                @change="rowCellChangedFire(scope, itemLevel3)"
                                                @click.native="rowCellClick(scope, itemLevel3)"
                                                :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                                                :class="`tableEditInputAlign-${itemLevel3.align}`"
                                                :style="`width:100%;text-align:${itemLevel3.align}`">
                              <el-checkbox v-for="(it, index) in itemLevel3.columnEntity.dataRef" :key="index" :label="it">{{it}}</el-checkbox>
                            </el-checkbox-group>
                          </span>
                          <span v-if="isColType({ item: itemLevel3, row: scope.row }, '下拉框')">
                            <el-select clearable v-model="scope.row[itemLevel3.prop]" placeholder="请选择"
                                       @change="rowCellChangedFire(scope, itemLevel3)"
                                       @click.native="rowCellClick(scope, itemLevel3)"
                                       :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                                       :class="`tableEditInputAlign-${itemLevel3.align}`"
                                       :style="`width:100%;text-align:${itemLevel3.align}`">
                              <div slot="empty" style="text-align: center"> 正在加载</div>
                                <el-option
                                  v-for="it in selectOptions"
                                  :key="it.value"
                                  :label="it.label"
                                  :value="it.value"
                                  :disabled="it.disabled">
                                </el-option>
                              </el-select>
                          </span>
                          <span v-if="isColType({ item: itemLevel3, row: scope.row }, '弹框')" class="BLDefInput">
                               <div v-if="!itemLevel3.columnEntity.isRowFormButton">
                                  <el-input v-model="scope.row[itemLevel3.prop]"
                                            readonly
                                            id="tkInput"
                                            @change="rowCellChangedFire(scope, itemLevel3)"
                                            :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${itemLevel3.align}`"
                                            :style="`width:100%; text-align:${itemLevel3.align}`"
                                            @click.native="rowCellClick(scope, itemLevel3)">
                                  </el-input>
                                  <span class="el-input__prefix">
                                            <i :class="getIconClass(scope.row[itemLevel3.prop])" :key="`icon-${getRowIndex(scope)}`"
                                              @click="rowCellClick(scope, itemLevel3, getIconClass(scope.row[itemLevel3.prop]))"/>
                                  </span>
                                </div>
                                <div class="rowFormButton" v-if="itemLevel3.columnEntity.isRowFormButton">
                                  <el-button @click.native="rowCellClick(scope, itemLevel3)">
                                    {{itemLevel3.columnEntity.rowFormButtonText}}
                                  </el-button>
                                  <input-money
                                    v-model="scope.row[itemLevel3.prop]"
                                    v-if="itemLevel3.columnEntity?.isSpecialAoumt === '是'"
                                    readonly
                                    id="tkInput"
                                    @change="rowCellChangedFire(scope, itemLevel3)"
                                    :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                                    class="tableEditInputAlign-right"
                                    @click.native="rowCellClick(scope, itemLevel3)"></input-money>
                                  <el-input v-else v-model="scope.row[itemLevel3.prop]"
                                            readonly
                                            id="tkInput"
                                            @change="rowCellChangedFire(scope, itemLevel3)"
                                            :disabled="scope.row[itemLevel3.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${itemLevel3.label.includes('金额') ? 'right' : itemLevel3.align}`"
                                            @click.native="rowCellClick(scope, itemLevel3)">
                                  </el-input>
                                </div>
                              </span>
                            </span>
                            <span v-if="isCanEditable(itemLevel3, scope.row) && itemLevel3.editableType == 'a'">
                              <a v-if="itemLevel3.aclick == 'handlePreView'" @click="handlePreView(scope.row,scope)">
                                {{ scope.row[itemLevel3.prop] }}
                              </a>
                            </span>
                            <span
                                v-if="isCanEditable(itemLevel3, scope.row) && itemLevel3.editableType == 'html'"
                                v-html="scope.row[itemLevel3.prop]"
                                @click="clickToShowDetail($event,scope.row)"
                                />
                          </slot>
                        </template>

                      </component>
                      <template v-slot:header>
                        <base-table-header :remindInfo="remindInfo" :column="itemLevel2" :editColMap="editColMap"/>
                      </template>
                    <template slot-scope="scope">
                      <slot name="tableRowSlot" :slotScope="scope">
                        <span v-if="!isItemEditable(scope.row, itemLevel2)"
                              :style="`width:100%;text-align:${itemLevel2.align}`"
                              :class="isColType({ item: itemLevel2, row: scope.row }, '三级明细列') ? 'level3DetailColSpan': ''"
                              :rowId="$getRowId(scope.row)"
                              :colProp="itemLevel2.prop"
                              @click="level3CellClicked(itemLevel2,scope.row)">
                          {{formatColData(scope.row, itemLevel2.prop)}}
                        </span>

                        <span v-if="!isItemEditable(scope.row, itemLevel2) && itemLevel2.editableType === 'a'"
                              @click="clickA(scope, itemLevel2)">
                          <a>{{scope.row[itemLevel2.prop]}}</a>
                        </span>

                        <span v-if="isItemEditable(scope.row, itemLevel2) && itemLevel2.editableType !== 'a'" class="edit-container">
                          <span v-if="itemLevel2.editableType == '起始日期'"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="itemLevel2.prop">
                              <el-date-picker
                                v-model="scope.row[itemLevel2.prop]"
                                :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder=""
                                end-placeholder=""/>
                          </span>
                          <span v-if="itemLevel2.editableType == '文件上传'"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="itemLevel2.prop">
                              <base-list-cell-file v-model="scope.row[itemLevel2.prop]" :biz-id="$getRowId(scope.row)"/>
                          </span>
                          <span v-if="isColType({ item: itemLevel2, row: scope.row }, '三级明细列')"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="itemLevel2.prop">
                              <el-input v-model="scope.row[itemLevel2.prop]"
                                        :readonly="true"
                                        :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                        @blur="verifyRequired(scope, itemLevel2)">
                                <i slot="prefix" class="el-input__icon el-icon-edit"/>
                              </el-input>
                          </span>
                          <span v-if="isColType({ item: itemLevel2, row: scope.row }, '没有列定义的数值列')">
                            <el-input
                              @change="rowCellChangedFire(scope, itemLevel2)"
                              @input="scope.row[itemLevel2.prop] = scope.row[itemLevel2.prop].replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')"
                              :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${itemLevel2.align}`"
                              :style="`width:100%;text-align:${itemLevel2.align}`"
                              v-model="scope.row[itemLevel2.prop]"
                              @blur="verifyRequired(scope, itemLevel2)"/>
                          </span>
                          <span v-if="isColType({ item: itemLevel2, row: scope.row }, '文本金额数值')">
                            <el-input
                              v-if="itemLevel2.editableType == 'numInput' && (itemLevel2.columnEntity.colType == '整数' || itemLevel2.columnEntity.colType == '数值')"
                              @change="rowCellChangedFire(scope, itemLevel2)"
                              @input="scope.row[itemLevel2.prop] = scope.row[itemLevel2.prop].replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')"
                              :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${itemLevel2.align}`"
                              :style="`width:100%;text-align:${itemLevel2.align}`"
                              v-model="scope.row[itemLevel2.prop]"
                              @blur="verifyRequired(scope, itemLevel2)"/>
                            <input-money
                              v-if="itemLevel2.editableType == 'numInput' &&  itemLevel2.columnEntity.colType == '金额'"
                              @change="rowCellChangedFire(scope, itemLevel2)"
                              :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${itemLevel2.align}`"
                              :style="`width:100%;text-align:${itemLevel2.align}`"
                              v-model="scope.row[itemLevel2.prop]"
                            ></input-money>
                            <el-input
                              v-if="itemLevel2.editableType == 'input'"
                              @change="rowCellChangedFire(scope, itemLevel2)"
                              :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputAlign-${itemLevel2.align}`"
                              :style="`width:100%;text-align:${itemLevel2.align}`"
                              v-model="scope.row[itemLevel2.prop]"
                              @blur="verifyRequired(scope, itemLevel2)"/>
                          </span>
                          <span v-if="isColType({ item: itemLevel2, row: scope.row }, '日期')">
                            <el-date-picker type="date" value-format="yyyy-MM-dd"
                                            v-model="scope.row[itemLevel2.prop]"
                                            @change="rowCellChangedFire(scope, itemLevel2)"
                                            :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${itemLevel2.align}`"
                                            :style="`width:100%;text-align:${itemLevel2.align}`"
                                            placeholder=""/>
                          </span>
                          <span v-if="isColType({ item: itemLevel2, row: scope.row }, '单选')">
                            <el-radio-group v-model="scope.row[itemLevel2.prop]"
                                            @change="rowCellChangedFire(scope, itemLevel2)"
                                            @click.native="rowCellClick(scope, itemLevel2)"
                                            :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${itemLevel2.align}`"
                                            :style="`width:100%;text-align:${itemLevel2.align}`">
                              <el-radio v-for="(it, index) in itemLevel2.columnEntity.dataRef" :key="index" :label="it">{{
                                  it
                                }}</el-radio>
                            </el-radio-group>
                          </span>
                          <span v-if="isColType({ item: itemLevel2, row: scope.row }, '多选')">
                            <el-checkbox-group v-model="scope.row[itemLevel2.prop]"
                                               @change="rowCellChangedFire(scope, itemLevel2)"
                                               @click.native="rowCellClick(scope, itemLevel2)"
                                               :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                               :class="`tableEditInputAlign-${itemLevel2.align}`"
                                               :style="`width:100%;text-align:${itemLevel2.align}`">
                              <el-checkbox v-for="(it, index) in itemLevel2.columnEntity.dataRef" :key="index"
                                           :label="it">{{ it }}</el-checkbox>
                            </el-checkbox-group>
                          </span>
                          <span v-if="isColType({ item: itemLevel2, row: scope.row }, '下拉框')">
                            <el-select clearable v-model="scope.row[itemLevel2.prop]" placeholder="请选择"
                                       @change="rowCellChangedFire(scope, itemLevel2)"
                                       @click.native="rowCellClick(scope, itemLevel2)"
                                       :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                       :class="`tableEditInputAlign-${itemLevel2.align}`"
                                       :style="`width:100%;text-align:${itemLevel2.align}`">
                              <div slot="empty" style="text-align: center"> 正在加载</div>
                                <el-option
                                  v-for="it in selectOptions"
                                  :key="it.value"
                                  :label="it.label"
                                  :value="it.value"
                                  :disabled="it.disabled">
                                </el-option>
                              </el-select>
                          </span>
                          <span v-if="isColType({ item: itemLevel2, row: scope.row }, '弹框')" class="BLDefInput">
                               <div v-if="itemLevel2.columnEntity.isRowFormButton">
                                  <el-input v-model="scope.row[itemLevel2.prop]"
                                            readonly
                                            id="tkInput"
                                            @change="rowCellChangedFire(scope, itemLevel2)"
                                            :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${itemLevel2.align}`"
                                            :style="`width:100%; text-align:${itemLevel2.align}`"
                                            @click.native="rowCellClick(scope, itemLevel2)">
                                  </el-input>
                                  <span class="el-input__prefix">
                                            <i :class="getIconClass(scope.row[itemLevel2.prop])" :key="`icon-${getRowIndex(scope)}`"
                                               @click="rowCellClick(scope, itemLevel2, getIconClass(scope.row[itemLevel2.prop]))"/>
                                  </span>
                                </div>
                                <div class="rowFormButton" v-if="itemLevel2.columnEntity.isRowFormButton">
                                  <el-button @click.native="rowCellClick(scope, itemLevel2)">
                                    {{itemLevel2.columnEntity.rowFormButtonText}}
                                  </el-button>
                                  <input-money
                                    v-model="scope.row[itemLevel2.prop]"
                                    v-if="itemLevel2.columnEntity?.isSpecialAoumt === '是'"
                                    readonly
                                    id="tkInput"
                                    @change="rowCellChangedFire(scope, itemLevel2)"
                                    :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                    class="tableEditInputAlign-right"
                                    @click.native="rowCellClick(scope, itemLevel2)"></input-money>
                                  <el-input v-else v-model="scope.row[itemLevel2.prop]"
                                            readonly
                                            id="tkInput"
                                            @change="rowCellChangedFire(scope, itemLevel2)"
                                            :disabled="scope.row[itemLevel2.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${itemLevel2.label.includes('金额') ? 'right' : itemLevel2.align}`"
                                            @click.native="rowCellClick(scope, itemLevel2)">
                                  </el-input>
                                </div>
                           </span>
                        </span>
                        <span v-if="isCanEditable(itemLevel2, scope.row) && itemLevel2.editableType == 'a'">
                          <a v-if="itemLevel2.aclick == 'handlePreView'" @click="handlePreView(scope.row,scope)">
                            {{ scope.row[itemLevel2.prop] }}
                          </a>
                        </span>
                        <span
                          v-if="isCanEditable(itemLevel2, scope.row) && itemLevel2.editableType == 'html'"
                          v-html="scope.row[itemLevel2.prop]"
                          @click="clickToShowDetail($event,scope.row)"
                        />
                        </slot>
                      </template>

                    </component>
                    <template v-slot:header>
                      <base-table-header :remindInfo="remindInfo" :column="item" :editColMap="editColMap"/>
                    </template>
                    <template slot-scope="scope" v-if="!item.hasChildren">
                      <slot name="tableRowSlot" :row="scope.row" :column="item">
                        <span v-if="!isItemEditable(scope.row, item)&&item.editableType !== 'a'"
                              :style="styleExtract(item)"
                              :class="isColType({ item, row: scope.row }, '三级明细列') ? 'level3DetailColSpan': ''"
                              :rowId="$getRowId(scope.row)"
                              :colProp="item.prop"
                              @click="level3CellClicked(item,scope.row)">

                          <span class="rowFormButton" v-if="item.columnEntity && item.columnEntity.isRowFormButton">
                              <el-button @click.native="rowCellClick(scope, item)">
                                {{item.columnEntity.rowFormButtonText}}
                              </el-button>
                          </span>
                          <span v-if="item.editableType !== '文件上传'" style="width: 100%">
                              {{formatColData(scope.row, item.prop)}}
                          </span>
                          <span v-if="item.editableType === '文件上传'">
                              <base-list-cell-file v-model="scope.row[item.prop]" :is-edit="false"/>
                          </span>
                        </span>
                        <!-- 2025218修改自定义双击编辑 -->
                        <span v-if="isColType({ item, row: scope.row }, 'textarea')">
                          <el-input
                            type="textarea"
                            :rows="5"
                            :readonly="initInjectParams.disabled"
                            :class="`tableEditInput tableEditInputAlign-${item.align}`"
                            :style="`width:100%;text-align:${item.align};cursor:pointer;`"
                            v-model="scope.row[item.prop]"
                            @blur="cellRowsOnBlur(scope.row[item.prop])"
                            />
                        </span>

                        <span v-if="!isItemEditable(scope.row, item) && item.editableType === 'a'" @click="clickA(scope, item)">
                          <a>{{scope.row[item.prop]}}</a>
                        </span>

                        <span v-if="isItemEditable(scope.row, item) && item.editableType !== 'a'" class="edit-container">
                          <span v-if="item.editableType == '起始日期'"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="item.prop">
                              <el-date-picker
                                v-model="scope.row[item.prop]"
                                :disabled="scope.row[item.label + '_禁用'] === true"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder=""
                                end-placeholder=""
                                @change="verifyRequired(scope, item)"/>
                          </span>
                          <span v-if="item.editableType == '文件上传'"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="item.prop">
                              <base-list-cell-file v-model="scope.row[item.prop]" :biz-id="$getRowId(scope.row)"/>
                          </span>
                          <span v-if="isColType({ item, row: scope.row }, '三级明细列')"
                                class="level3DetailColSpan"
                                :rowId="$getRowId(scope.row)"
                                :colProp="item.prop">
                              <el-input v-model="scope.row[item.prop]"
                                        :readonly="true"
                                        :disabled="scope.row[item.label + '_禁用'] === true"
                                        @blur="verifyRequired(scope, item)">
                                <i slot="prefix" class="el-input__icon el-icon-edit"/>
                              </el-input>
                          </span>
                          <span v-if="isColType({ item, row: scope.row }, '没有列定义的数值列')">
                            <el-input
                              v-if="item.editableType == 'numInput'"
                              @change="rowCellChangedFire(scope, item)"
                              @input="scope.row[item.prop] = scope.row[item.prop].replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')"
                              :disabled="scope.row[item.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${item.align}`"
                              :style="`width:100%;text-align:${item.align}`"
                              v-model="scope.row[item.prop]"
                              @blur="verifyRequired(scope, item)"/>
                            <el-input
                              v-if="item.editableType == 'input'"
                              @change="rowCellChangedFire(scope, item)"
                              :class="`tableEditInput tableEditInputAlign-${item.align}`"
                              :style="`width:100%;text-align:${item.align}`"
                              v-model="scope.row[item.prop]"
                              @blur="verifyRequired(scope, item)"/>
                            <el-select clearable v-model="scope.row[item.prop]" placeholder="请选择"
                                       v-if="item.editableType == '下拉框'"
                                       @change="((value)=>{rowCellSelectChangedFire(scope, item, value)})"
                                       @click.native="rowCellClick(scope, item)"
                                       :disabled="scope.row[item.label + '_禁用'] === true"
                                       :class="`tableEditInputAlign-${item.align}`"
                                       :style="`width:100%;text-align:${item.align}`">
                              <div slot="empty" style="text-align: center"> 正在加载</div>
                              <el-option
                                v-for="it in scope.row.options"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                                :disabled="it.disabled">
                              </el-option>
                            </el-select>
                          </span>
                          <span v-if="isColType({ item, row: scope.row }, '文本金额数值')">
                            <el-input
                              v-if="item.editableType == 'numInput' && (item.columnEntity.colType == '整数' || item.columnEntity.colType == '数值')"
                              @change="rowCellChangedFire(scope, item)"
                              @input="scope.row[item.prop] = scope.row[item.prop].replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')"
                              :disabled="scope.row[item.label + '_禁用'] === true"
                              :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${item.align}`"
                              :style="`width:100%;text-align:${item.align}`"
                              @blur="rowCellBlurFire(scope, item)"
                              v-model="scope.row[item.prop]"/>
                              <input-money
                                v-if="item.editableType == 'numInput' &&  item.columnEntity.colType == '金额'"
                                @change="rowCellChangedFire(scope, item)"
                                :disabled="scope.row[item.label + '_禁用'] === true"
                                :class="`tableEditInput tableEditInputNumber tableEditInputAlign-${item.align}`"
                                :style="`width:100%;text-align:${item.align}`"
                                v-model="scope.row[item.prop]"
                              ></input-money>
                              <el-input
                                v-if="item.editableType == 'input'"
                                @change="rowCellChangedFire(scope, item)"
                                :disabled="scope.row[item.label + '_禁用'] === true"
                                :class="`tableEditInput tableEditInputAlign-${item.align}`"
                                :style="`width:100%;text-align:${item.align}`"
                                v-model="scope.row[item.prop]"
                                @blur="verifyRequired(scope, item)"/>
                          </span>
                          <span v-if="isColType({ item, row: scope.row }, '日期')">
                            <el-date-picker type="date" value-format="yyyy-MM-dd"
                                            v-model="scope.row[item.prop]"
                                            @change="rowCellChangedFire(scope, item)"
                                            :disabled="scope.row[item.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${item.align}`"
                                            :style="`width:100%;text-align:${item.align}`"
                                            placeholder=""/>
                          </span>
                          <span v-if="isColType({ item, row: scope.row }, '单选')">
                            <el-radio-group v-model="scope.row[item.prop]"
                                            @change="rowCellChangedFire(scope, item)"
                                            @click.native="rowCellClick(scope, item)"
                                            :disabled="scope.row[item.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${item.align}`"
                                            :style="`width:100%;text-align:${item.align}`">
                              <el-radio v-for="(it, index) in item.columnEntity.dataRef" :key="index"
                                        :label="it">{{ it }}</el-radio>
                            </el-radio-group>
                          </span>
                          <span v-if="isColType({ item, row: scope.row }, '多选')">
                            <el-checkbox-group v-model="scope.row[item.prop]"
                                               @change="rowCellChangedFire(scope, item)"
                                               @click.native="rowCellClick(scope, item)"
                                               :disabled="scope.row[item.label + '_禁用'] === true"
                                               :class="`tableEditInputAlign-${item.align}`"
                                               :style="`width:100%;text-align:${item.align}`">
                              <el-checkbox v-for="(it, index) in item.columnEntity.dataRef" :key="index"
                                           :label="it">{{ it }}</el-checkbox>
                            </el-checkbox-group>
                          </span>
                          <span v-if="isColType({ item, row: scope.row }, '下拉框')">
                            <el-select clearable v-model="scope.row[item.prop]" placeholder="请选择"
                                       @change="rowCellChangedFire(scope, item)"
                                       @click.native="rowCellClick(scope, item)"
                                       :disabled="scope.row[item.label + '_禁用'] === true"
                                       :class="`tableEditInputAlign-${item.align}`"
                                       :style="`width:100%;text-align:${item.align}`">
                              <div slot="empty" style="text-align: center; color: #b2b2b2"> 正在加载</div>
                                <el-option
                                  v-for="it in selectOptions"
                                  :key="it.value"
                                  :label="it.label"
                                  :value="it.value"
                                  :disabled="it.disabled">
                                </el-option>
                              </el-select>
                          </span>
                          <span v-if="isColType({ item, row: scope.row }, '弹框')" class="BLDefInput">
                              <div v-if="!item.columnEntity.isRowFormButton">
                                <el-input v-model="scope.row[item.prop]"
                                          readonly
                                          id="tkInput"
                                          @change="rowCellChangedFire(scope, item)"
                                          :disabled="scope.row[item.label + '_禁用'] === true"
                                          :class="`tableEditInputAlign-${item.align}`"
                                          :style="`width:100%; text-align:${item.align}`"
                                          @click.native="rowCellClick(scope, item)">
                                </el-input>
                                <span class="el-input__prefix">
                                          <i :class="getIconClass(scope.row[item.prop])" :key="`icon-${getRowIndex(scope)}`"
                                             @click="rowCellClick(scope, item, getIconClass(scope.row[item.prop]))"/>
                                </span>
                              </div>
                              <div class="rowFormButton" v-if="item.columnEntity.isRowFormButton">
                                  <el-button @click.native="rowCellClick(scope, item)">
                                    {{item.columnEntity.rowFormButtonText}}
                                  </el-button>
                                  <input-money
                                    v-model="scope.row[item.prop]"
                                    v-if="item.columnEntity?.isSpecialAoumt === '是'"
                                    readonly
                                    id="tkInput"
                                    @change="rowCellChangedFire(scope, item)"
                                    :disabled="scope.row[item.label + '_禁用'] === true"
                                    class="tableEditInputAlign-right"
                                    @click.native="rowCellClick(scope, item)"></input-money>
                                  <el-input v-else v-model="scope.row[item.prop]"
                                            readonly
                                            id="tkInput"
                                            @change="rowCellChangedFire(scope, item)"
                                            :disabled="scope.row[item.label + '_禁用'] === true"
                                            :class="`tableEditInputAlign-${item.label.includes('金额') ? 'right' : item.align}`"
                                            @click.native="rowCellClick(scope, item)">
                                  </el-input>
                              </div>
                          </span>
                        </span>
                        <span v-if="isCanEditable(item, scope.row) && item.editableType == 'a'">
                          <span v-if="isAuditBasis(scope.row) || scope.row.deptName === '合计:'">
                            {{ formatColData(scope.row, item.prop) }}
                          </span>
                          <a v-else-if="item.aclick == 'handlePreView'"
                           @click="handlePreView(scope.row,scope)">
                          {{ scope.row[item.prop] }}
                          </a>
                          <a v-else-if="item.aclick !== 'handlePreView'"
                              @click="linkShow(item.aclick,scope.row,item)">
                          {{formatColData(scope.row, item.prop)}}
                          </a>
                          <div v-if="rowOperatTar.errTip[$getRowId(scope.row)]" style="color: #ff5c00;font-size: 8px;line-height: normal;">{{rowOperatTar.errTip[$getRowId(scope.row)]}}</div>
                        </span>
                        <span
                          v-if="isCanEditable(item, scope.row) && item.editableType == 'html'"
                          v-html="scope.row[item.prop]"
                          @click="clickToShowDetail($event,scope.row)"/>
                        <span v-if="errorTip[getRowIndex(scope)]&&errorTip[getRowIndex(scope)][item.columnEntity.labelAlias ? item.columnEntity.labelAlias : item.columnEntity.labelOrigin]" class="rowFormError">
                          {{errorTip[getRowIndex(scope)][item.columnEntity.labelAlias ? item.columnEntity.labelAlias : item.columnEntity.labelOrigin]}}
                        </span>
                      </slot>
                    </template>
                </component>
              </component>

              <file-view ref="fileView"/>
            </div>
            <slot name="contentFooter" />
            <div v-if="tableInitialized && loadTable && showPager" class="pagination">
              <span class="pagination-container" :class="{'pagination-absolute': !!listContentSubId}">
                <span class="pagination-info">共 {{ dataTotal }} 条记录 第{{ current }} / {{ Math.ceil((dataTotal / size)) ? Math.ceil((dataTotal / size)) :1}} 页</span>
                <el-pagination
                  class="listContentPagination"
                  background
                  layout="prev, pager, next, sizes, jumper"
                  :page-sizes="pageSizes"
                  :page-size="size"
                  :total="dataTotal"
                  :current-page="current"
                  @current-change="handleCurrentChange"
                  @size-change="handleSizeChange"
                >
                </el-pagination>
              </span>
            </div>
            <div :style="listContentSubStyle" :class="listContentSubId">
              <component ref="listContentSub" :is="listContentSubId"/>
            </div>
            <keep-alive>
                <component ref="showDialogDetailSub" :is="showDialogDetailSubId"></component>
            </keep-alive>
          </div>
        </slot>
      </template>
    </b-page>
    <div></div>
    <hyperlinkDialog ref="hyperlinkDialog"></hyperlinkDialog>
    <base-edit-dlg ref="baseEditDlgCommon">
      <template #bizContent>
        <component ref="baseEditDlgCommonContent" :is="editContent"/>
      </template>
    </base-edit-dlg>
  </div>
</template>

<script>
import { getUUID } from '@/utils'
import BasePage from './base-page.vue'
import BPage from './base-page'
import $ from 'jquery'
import { store } from '@/utils/sunmei-data'
import BaseListCellFile from './base-list-cell-file'
import BaseEditDlg from './base-edit-dlg'

export default {
  name: 'b-list',
  components: { BaseEditDlg, BaseListCellFile, BPage, BasePage },
  props: {
    baseListTableClass: { type: String, default: 'baseListTable' },
    isVxe: {
      type: Boolean,
      default: false
    },
    isEditRowForm: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      attrObj: {
        vxe: { // vxeTable属性
          footerMethod: this.getSummaries,
          autoResize: true,
          scrollY: { enabled: true, gt: 20, oSize: 10 },
          rowConfig: { isHover: true, useKey: true, keyField: 'id' },
          showOverflow: 'tooltip',
          showHeaderOverflow: 'tooltip',
          height: '100%'
        },
        el: { // elTable属性
          treeProps: { children: 'children', hasChildren: 'hasChildren' },
          rowKey: this.getRowKey,
          rowClassName: this.rowClassCame,
          summaryMethod: this.getSummaries,
          headerCellClassName: this.headerCellClassName,
          spanMethod: this.spanMethod,
          cellClassName: this.cellClass
        }
      },
      editContent: '',
      editableAny: undefined, // 外界觉得忽略列的读写设置，列总是可编辑
      selectOptions: [],
      tableInitialized: false,
      dataApiKey: '',
      impExcelKey: '',
      current: 1,
      pageSizes: [20, 50, 100],
      size: 20,
      dataTotal: 0,
      rowsData: [],
      columnsData: [],
      columnsDataPropMap: {}, // prop => column
      columnsPropMap: {}, // column.label => prop
      orderNumber: { label: '序号', isShow: false },
      resultAttributes: {},
      remindInfo: {},
      loadTable: true, // 加载列表
      showPager: true, // 显示分页
      noSelection: false, // 是否显示表格复选框
      noRowHighlight: false, // 是否有行高亮背景效果
      tableId: 'baseList' + new Date().getTime() + '',
      radioList: [],
      radioParam: {},
      listContentSubId: '',
      showDialogDetailSubId: '',
      listContentClass: 'listContent',
      listContentNoSelectionClass: '',
      listContentSubStyle: 'display:none;',
      listContentNoPager: '',
      showSummary: false, // 是否进行表格汇总
      rowCheckedCallback: (rows) => {
      },
      checkRow: [],
      callbackRowDblclick: () => {
      },
      rowCellChanged: undefined,
      level3DetailCellClicked: undefined,
      afterRowCalculation: undefined,
      rowCellSelectChanged: undefined, // select选择框事件
      rowCellClickCallback: undefined, // 弹框选择事件
      rowCellClickBefore: undefined, // 弹框选择之前
      rowRadioCallback: undefined, // 单选事件
      callbackHeaderClick: undefined, // 表头点击事件
      callbackHeaderCellClassName: undefined, // 表头单元格类名回调
      supportRowUpAndDown: false, // 列表支持上移和下移功能
      aotuSummaryPmAmount: undefined, // 三级明细汇总统计项目总额
      selectionColumnWidth: 40,
      refreshButtonsDisableDataKey: '', // 后端刷新按钮禁用数据接口的apiKey
      refreshButtonsDisableDataParams: {}, // 后端刷新按钮禁用数据接口的额外参数
      historySelectOptions: new Map(),
      customSelectOptionsSupplier: undefined, // 行表单下拉框自定义数据源
      renderDropOptionsBefore: undefined, // 行表单下拉数据获取后渲染之前（一般用于处理options的disable）
      dataVo: '',
      showEmptyImg: false,
      rowCopyEX: undefined,
      choiceProp: '',
      isMultiChoice: false,
      errorTip: {}, // 错误提示
      rowOperatTar: {
        cannotChecks: [], // 不能选择
        errList: [], // 行错误
        errTip: {}
      },
      isBlockViewEdit: false, // 是否是区块制单
      handleBeforeBlockRef: undefined, // 区块行制单的参照弹出之前，额外处理数据
      handlebeforeFilldataBlockRef: undefined, // 区块行制单的参照填充数据之前，额外处理数据
      handleAfterBlockRef: undefined, // 区块行制单的参照填充数据之后，额外处理数据
      exData: {}, // 额外的辅助数据
      currentRFormTable: '', // 区块表单设计时 目前选中的是哪一个表格的标识 整个页面最多只有一个
      fileBlAudit: false, // 是否附件补录页面审核
      addRformExparamsCallback: undefined, // 区块表单 是否需要添加区块行表单额外参数 的回调
      clearRowDataCallback: undefined, // 区块表单 清除行数据 的回调
      reqTimestamp: Date.now(),
      mergeKey: '',
      customMergeAll: {},
      mergeMap: {}, // 已经合并的单元格
      loading: false,
      treeConfig: { transform: true, rowField: 'id', parentField: 'parentId' },
      editIdMap: {}, // 可以编辑行
      editColMap: {}, // 可以编辑列
      isEditing: false, // 控制是否可编辑
      initInjectParams: {
        disabled: true
      },
      cellRowsOnBlur: () => {}
    }
  },
  computed: {
    tableType() {
      if (this.isVxe) {
        return 'vxe'
      }
      return 'el'
    },
    hasSummaryClass() {
      // 有合计行且表格有数据
      return this.showSummary && this.tableData?.length ? 'summaryClass' : 'noSummaryClass'
    },
    tableData() {
      const initParam = this.$getInitParams(this)
      // 区块表单分页操作
      if (initParam?.isRformBlock) {
        const head = (this.current - 1) * this.size
        const tail = head + this.size
        return this.rowsData.slice(head, tail)
      }
      return this.rowsData
    },
    columnFixed() {
      const fixedMap = {}
      return (column, columnIndex) => {
        if (column.fixed) {
          if (columnIndex === 0 || columnIndex === this.columnsData.length - 1) {
            fixedMap[columnIndex] = columnIndex === 0 ? 'left' : 'right'
          } else if (fixedMap[columnIndex - 1] || fixedMap[columnIndex + 1]) {
            fixedMap[columnIndex] = fixedMap[columnIndex - 1] ? 'left' : 'right'
          }
        }
        return fixedMap[columnIndex]
      }
    },
    cellClick() {
      if (this.isVxe) {
        return this.clickRow
      }
      return () => {}
    }
  },
  methods: {
    // 当前行更改为编辑操作
    changeEditRow(editIdMap = {}) {
      this.editIdMap = editIdMap
    },
    // 记住那一列可编辑(行编辑)
    remindEdit(col) {
      if (!this.isEditRowForm) {
        return
      }
      if (col.editable) {
        this.editColMap[col.prop] = true
      }
      col.editable = false
    },
    styleExtract(item) {
      let data = `width:100%;text-align:${item.align};`
      if (item.columnEntity?.isRowFormButton) {
        data += 'display:flex;flex-direction:row-reverse'
      }
      return data
    },
    headerCellClassName({ row, column, rowIndex, columnIndex }) {
      if (this.$isEmpty(this.currentRFormTable)) return ''
      if (this.callbackHeaderCellClassName) {
        return this.callbackHeaderCellClassName({ row, column, rowIndex, columnIndex })
      }
    },
    rowClassCame({ row }) {
      if (this.$getTableCheckedIds(this.$refs.table).includes(this.$getRowId(row))) {
        return 'checkedTR'
      }
      return ''
    },
    getRowIndex(scope) {
      if (this.isVxe) {
        return scope.$rowIndex
      }
      return scope.$index
    },
    rowStyle({ row }) {
      const initParam = this.$getInitParams(this) || {}
      const currentStyle = initParam.rowStyle?.(row) || {}
      if (this.rowOperatTar.errList.includes(this.$getRowId(row))) {
        currentStyle.color = 'rgb(255,43,43)'
      }
      return currentStyle
    },
    changeRowTar(attribute, row, isRest, tip) {
      if (isRest) {
        this.rowOperatTar[attribute] = []
      }
      if (!this.$isNotEmpty(row)) {
        return
      }
      if (Array.isArray(this.rowOperatTar[attribute])) {
        this.rowOperatTar[attribute].push(this.$getRowId(row))
      } else if (Object.prototype.toString.call(this.rowOperatTar[attribute]) === '[object Object]') {
        this.$set(this.rowOperatTar[attribute], this.$getRowId(row), tip)
      }
    },
    isAuditBasis(row) {
      if (row.appendixTypeCode === '999' && this.$getRowId(row) === '审核依据') {
        return true
      }
      return false
    },
    checCheckboxkMethod({ row }) {
      if (this.rowOperatTar.cannotChecks.includes(this.$getRowId(row))) {
        return false
      }
      return true
    },
    showBlistError(rowIndex, rowLable, tip, isReset) {
      if (isReset) {
        this.errorTip = {}
      }
      if (this.$isEmpty(rowIndex) || this.$isEmpty(rowLable)) {
        return
      }
      if (this.$isEmpty(this.errorTip[rowIndex])) {
        this.$set(this.errorTip, rowIndex, {})
      }
      this.$set(this.errorTip[rowIndex], rowLable, tip)
      this.$nextTick(() => {
        // 只有一个错误才让他滚动到可视区域
        const errorNum = document.querySelectorAll('.rowFormError')
        if (errorNum && Array.from(errorNum).length === 1) {
          errorNum[0].scrollIntoView({ behavior: 'auto', block: 'center', inline: 'center' })
        }
      })
    },
    clickToShowDetail($event, row) {
      if ($event.target.classList.contains('clickToShowDetail')) {
        this.$showDetail($($event.target).attr('bizId'), null, null, row?.metaName || row?.billName)
        $event.stopPropagation()
      }

      if ($event.target.classList.contains('clickToShowLogDetail')) {
        this.$showLogDetail($($event.target).attr('bizId'))
        $event.stopPropagation()
      }

      if ($event.target.classList.contains('clickToShowTransferDetail')) {
        const initParam = this.$getInitParams(this)
        initParam.showDlg($($event.target).attr('bizId'))
        $event.stopPropagation()
      }
    },
    clickA(scope, item) {
      const initParams = this.$getInitParams(this)
      if (initParams && initParams.clickA) {
        // 构建模拟bt对象
        const bt = {}
        bt.params = {}
        bt.scope = scope
        bt.item = item
        bt.getParam = (key) => {
          return bt.params[key]
        }
        bt.setParam = (key, value) => {
          bt.params[key] = value
        }
        bt.getRowData = () => {
          return scope.row
        }
        bt.getRowId = () => {
          return this.$getRowId(scope.row)
        }

        initParams.clickA(scope, item, bt)
      } else {
        this.$message.error('没有设置超链接的响应：<br/>initParams.clickA(scope, item, bt)')
      }
    },
    getColumnsData(list) {
      return this.$isNotEmpty(list) ? list.filter((item) => this.isColEnabled(item)) : []
    },
    addRedStar(item) {
      return (h) => {
        if (this.$isNotEmpty(item.columnEntity) &&
          item.columnEntity.isRequired !== undefined &&
          item.columnEntity.isRequired !== null &&
          item.columnEntity.isRequired !== '') {
          if (item.columnEntity.isRequired && item.editable && item.editableType !== 'a') {
            return [
              h('span', { style: 'color: red' }, '*'),
              h('span', ' ' + item.label)
            ]
          } else {
            return h('span', item.label)
          }
        } else {
          return h('span', item.label)
        }
      }
    },
    isColEnabled(item) {
      if (this.$isNotEmpty(item.columnEntity) &&
        item.columnEntity.isEnabled !== undefined &&
        item.columnEntity.isEnabled !== null &&
        item.columnEntity.isEnabled !== '') {
        // 区块特有属性 isShow 用于切换rform列的的显示隐藏
        if (item.columnEntity.isShow !== undefined &&
        item.columnEntity.isShow !== null &&
        item.columnEntity.isShow !== '') {
          return item.columnEntity.isShow === '是' && item.columnEntity.isEnabled === '是'
        }
        return item.columnEntity.isEnabled === '是'
      }
      return true
    },
    /**
     * 校验是否满足超链接
     **/
    isLinkshow(item) {
      const aclick = item.aclick
      const columEntity = item.columnEntity
      if (aclick === 'showDetail') { // 查看详情
        return true
      } else if (this.$isNotEmpty(columEntity) &&
        this.$isNotEmpty(item.columnEntity.reserveData)) { // 查看详情外的锡尼希
        return true
      }
      return false
    },
    isColType({ item, row }, colType) {
      if (colType === '三级明细列') {
        return (item.columnEntity && item.isLevel3DetailCol)
      } else if ((item.columnEntity && item.isLevel3DetailCol)) {
        return false
      }
      switch (colType) {
        case '没有列定义的数值列':
          return (item.editableType === 'numInput' || item.editableType === 'input' || item.editableType === '下拉框') &&
          this.isCanEditable(item, row) && !item.columnEntity
        case '文本金额数值':
          return (item.columnEntity && (
            item.columnEntity.colType.indexOf('文本') >= 0 ||
          item.columnEntity.colType === '金额' ||
          item.columnEntity.colType === '小数' ||
          item.columnEntity.colType === '整数' ||
            item.columnEntity.colType === '数值'))
        case '日期':
          return (item.columnEntity && item.columnEntity.colType === '日期')
        case '单选':
          return (item.columnEntity && item.columnEntity.colType === '单选')
        case '多选':
          return (item.columnEntity && item.columnEntity.colType === '多选')
        case '下拉框':
          return (item.columnEntity && item.columnEntity.colType === '下拉框')
        case '弹框':
          return (item.columnEntity && item.columnEntity.colType === '弹框')
        case 'textarea':
          return (item.columnEntity && item.columnEntity.colType === 'textarea')
        default:
          return false
      }
    },
    cellClass({ row, column }) {
      if (column.label === '状态') {
        // 状态默认的 签署备案状态（签署备案）
        if (row['状态'] === '被退回' || row['签署备案状态'] === '被退回' || row['子业务审核状态'] === '被退回') {
          return 'danger-color'
        }
      }
    },
    spanMethod(param) {
      const initParam = this.$getInitParams(this)
      if (typeof initParam.spanMethod === 'function') {
        // 报表合并行合并列
        const customMap = {}
        customMap['customMergeAll'] = this.customMergeAll
        customMap['columnsDataPropMap'] = this.columnsDataPropMap
        customMap['mergeMap'] = this.mergeMap
        return initParam.spanMethod(param, this.$refs.table,
          this.rowsData, this.radioParam, customMap)
      }
      const { column, columnIndex, row, rowIndex } = param
      const columnData = this.columnsDataPropMap[column.property]
      const excludeMerge = row.notMergeKeyList || [] // 后端返回的row数据里，排除这些行不合并
      if (this.$isEmpty(this.mergeKey) || this.$isEmpty(row[this.mergeKey]) || this.$isEmpty(columnData) || !columnData.isMerge || excludeMerge.includes(column.property)) {
        return () => {}
      }
      if (this.mergeMap[`${columnIndex}_${rowIndex}`]) {
        return {
          rowspan: 0,
          colspan: 0
        }
      }

      let rowspan = 1
      const columnProp = columnData.prop
      const that = this
      function getSpanNum(curIndex, prop, rowData) {
        const nextIndex = curIndex + 1
        const nextRow = that.rowsData[nextIndex]
        if (that.$isEmpty(nextRow)) {
          return
        }
        const nextExcludeMerge = row.notMergeKeyList || []
        if (that.$isEmpty(nextRow[that.mergeKey]) || nextExcludeMerge.includes(prop)) {
          return
        }
        const isTrue = nextRow[that.mergeKey] === rowData[that.mergeKey]
        if (nextRow[prop] === rowData[prop] && isTrue) {
          rowspan++
          that.mergeMap[`${columnIndex}_${nextIndex}`] = true
          getSpanNum(nextIndex, prop, rowData)
        }
      }
      getSpanNum(rowIndex, columnProp, row)
      return {
        rowspan,
        colspan: 1
      }
    },
    setFixedWrapper() { // 固定列滚动不跟随问题
      const baseListTable = this.$refs.table
      if (this.$isNotEmpty(baseListTable)) {
        const tarEle = baseListTable.$el
        const height = $(tarEle).find('.el-table__header-wrapper').height()
        $(tarEle).find('.el-table__fixed-body-wrapper').height(`calc(100% - ${height}px)`)
      }
    },
    isCanEditable(col, row) {
      const isRformAble = this.isEditRowForm && this.editIdMap[this.$getRowId(row)] && this.editColMap[col.prop]
      return isRformAble || col.editable
    },
    isItemEditable(row, item) {
      // 如果row中的id为空，自动添加临时id
      var tempIdPrefix = 'eidt_'
      var rowId = this.$getRowId(row) + ''
      if (this.$isEmpty(rowId)) {
        var tempId = tempIdPrefix + new Date().getTime()
        row.id = tempId
      }

      var editable = item.editable
      // 行编辑操作
      const isEditRowProp = this.isEditRowForm && this.editIdMap[this.$getRowId(row)] && this.editColMap[item.prop]
      if (editable || isEditRowProp) {
        var isUpdate = false
        if (this.$isNotEmpty(rowId) && rowId.indexOf(tempIdPrefix) < 0) {
          isUpdate = true
        }

        if (isUpdate &&
          this.$isNotEmpty(item.columnEntity) &&
          item.columnEntity.modifyType === '保存后只读') {
          editable = false
        }
        if (this.$isNotEmpty(item.columnEntity) &&
          (item.columnEntity.colType === '单选' ||
            item.columnEntity.colType === '多选')) {
          if (!(item.columnEntity.dataRef instanceof Array)) {
            var value = item.columnEntity.dataRef.replace(/，/g, ',')
            item.columnEntity.dataRef = value.split(',')
          }
        }
      }

      // 检查RowFormTemplateEntity的tagData机制
      if (this.$isNotEmpty(item.columnEntity)) {
        var tagData = row[`${item.columnEntity.labelOrigin}_tagData`]
        if (this.$isNotEmpty(tagData)) {
          if (tagData === '只读') {
            editable = false
          }
          if (tagData === '可编辑') {
            editable = true
          }
        }
      }

      // 比如：数据模板编辑状态时，即使只读的列也需要可编辑
      // ||isEditRowProp行编辑时双击后可编辑
      if (this.editableAny === true || isEditRowProp) {
        editable = true
      }
      return editable
    },
    setButtonBarVisible(visible, isNoPaddingTop) {
      this.$refs.basePage.setButtonBarVisible(visible, isNoPaddingTop)
    },
    setButtonNormalNoPaddingTop(isNoPaddingTop) {
      this.$refs.basePage.setButtonNormalNoPaddingTop(isNoPaddingTop)
    },
    setNoPager() {
      this.$refs.basePage.setNoPager()
    },
    setSimpleList() {
      // 设置列表没有按钮和分页时，高度间隔等
      this.setButtonBarVisible(false, true)
      this.setButtonNormalNoPaddingTop(true)
      this.setNoPager()
    },
    handlePreView(row, item) {
      if (['指标编码', '预算项目', '关联指标', '采购需求编码', '借款单', '指标名称'].includes(item?.column?.label)) {
        return this.$refs.hyperlinkDialog.init(row)
      }
      if (row.fileIds) {
        this.$refs.fileView.open({ fileIds: row.fileIds.split(','), bizDataId: null })
      } else {
        const attId = row.attId
        if (row.attType === '1') { // 发票预览时需组装发票额外显示的数据
          const fileExtInfos = {}
          const ext = row.attName.substring(row.attName.lastIndexOf('.') + 1)
          // 签章信息不为空时才组装数据 暂时只支持pdf
          const signatureInfo = row.signatureInfo
          if (this.$isNotEmpty(signatureInfo) && (ext === 'pdf' || ext === 'PDF')) {
            fileExtInfos[attId] = [signatureInfo]
          }
          this.resultAttributes['fileExtInfos'] = fileExtInfos
        }
        this.$fileDownloadByFileId(
          this.$refs.fileView, row.attName, row.attId, this.resultAttributes)
      }
    },
    /**
     * refName  表单名称
     * row 当前行信息
     *  @item 列配置信息
     **/
    linkShow(refName, row, item) {
      const column = item.columnEntity
      if (this.$isNotEmpty(column)) {
        var dataRef = column.dataRef// 可以设置多个，用冒号隔开，用于参数拓展
        var attId = row[dataRef]
        if (this.$isEmpty(dataRef)) {
          this.$message.error('找不到定义的key值')
          return
        }
        if (this.$isEmpty(attId)) {
          this.$message.error('无法获取请求参数信息')
          return
        }
      }
      var exportPram = {
        row: row,
        item: item,
        fileView: this.$refs.fileView
      }
      this.showDialogDetailSubId = refName
      this.$nextTick(() => {
        this.$refs.showDialogDetailSub.showDetailDialog(exportPram)
      })
    },
    // 生成表格合计
    getSummaries(param) {
      const initParam = this.$getInitParams(this)
      if (typeof initParam.getSummaries === 'function') {
        if (this.isVxe) {
          const { columns } = param
          columns.forEach((column) => {
            column.label = column.title
          })
          return [initParam.getSummaries(param, this.$refs.table)]
        }
        return initParam.getSummaries(param, this.$refs.table)
      } else if (initParam.showSummary) {
        const sumDataMap = {}
        let index = this.orderNumber.isShow ? 1 : 0
        this.columnsData.forEach(col => {
          if (col.isEnabled) {
            if (col.colType === '数值' || col.colType === '金额') {
              this.rowsData.forEach(row => {
                let cellValue = row[col.prop]
                if (this.$isNotEmpty(cellValue)) {
                  cellValue = (cellValue + '').replace(',', '')
                  const sumValue = sumDataMap[index + '']
                  let sum = this.$isEmpty(sumValue) ? 0 : parseFloat(sumValue)
                  sum += parseFloat(cellValue)
                  sumDataMap[index + ''] = sum
                }
              })

              if (col.colType === '金额') {
                sumDataMap[index + ''] = this.$formatMoney(sumDataMap[index + ''])
              } else {
                sumDataMap[index + ''] = parseInt(sumDataMap[index + ''])
              }
            }
            index++
          }
        })

        const sumData = []
        const keys = Object.keys(sumDataMap)
        keys.forEach(key => {
          sumData[parseInt(key)] = sumDataMap[key]
        })
        return sumData
      }
      return null
    },
    getResultAttributes() {
      return this.resultAttributes
    },
    getRowKey(row) {
      // 保存选中的数据id,row-key就是要指定一个key标识这一行的数据
      return row.id
    },
    formatColData(row, prop) {
      var colValue = row[prop] // 列显示值

      // 找到对应列类型colType
      var colType = ''
      var colItem = this.columnsDataPropMap[prop] || {}
      if (this.$isNotEmpty(colItem)) {
        colType = colItem.colType
      }
      // colItem.columnEntity?.isSpecialAoumt === '是' 判断弹框按钮时（文本可能是金额）
      if (colType === '金额' || colItem.columnEntity?.isSpecialAoumt === '是') {
        // 金额千分位(2位小数)处理
        if (this.$isNotEmpty(colValue)) {
          return this.$formatMoney(colValue)
        } else {
          return colValue
        }
      } else {
        return colValue
      }
    },
    init(initParams) {
      initParams = initParams || {}
      // 处理低版本浏览器行表单无限拉伸问题
      if (!initParams?.isRformBlock) {
        this.$set(this.attrObj.el, 'height', '100%')
      }
      // 区块表单相关 start
      if (this.$isNotEmpty(initParams.remindInfo)) {
        this.remindInfo = initParams.remindInfo || {}
      }
      if (this.$isNotEmpty(initParams.blockExtendObjMap) && this.$isNotEmpty(initParams.targetClassName)) {
        const addExparams = initParams.blockExtendObjMap[initParams.targetClassName].addExparams
        if (addExparams) {
          this.addRformExparamsCallback = addExparams
        }
        const clearRowData = initParams.blockExtendObjMap[initParams.targetClassName].clearRowData
        if (clearRowData) {
          this.clearRowDataCallback = clearRowData
        }
      }
      // 区块表单相关 end
      if (this.$isNotEmpty(initParams.radioList)) {
        this.radioList = initParams.radioList
      }
      // 树表格配置
      if (this.$isNotEmpty(initParams.treeConfig)) {
        Object.assign(this.treeConfig, initParams.treeConfig)
      }
      this.editContent = initParams.editContent
      this.renderDropOptionsBefore = initParams.renderDropOptionsBefore
      this.customSelectOptionsSupplier = initParams.customSelectOptionsSupplier
      this.isBlockViewEdit = initParams.isBlockViewEdit
      this.handleBeforeBlockRef = initParams.handleBeforeBlockRef
      this.handlebeforeFilldataBlockRef = initParams.handlebeforeFilldataBlockRef
      this.handleAfterBlockRef = initParams.handleAfterBlockRef
      this.callbackRowDblclick = initParams.callbackRowDblclick || (() => {
        this.initInjectParams.disabled = false
      })
      this.cellRowsOnBlur = initParams.cellRowsOnBlur || (() => {
      })
      this.callbackHeaderClick = initParams.callbackHeaderClick
      this.callbackHeaderCellClassName = initParams.callbackHeaderCellClassName
      this.rowCellChanged = initParams.rowCellChanged
      this.rowCellSelectChanged = initParams.rowCellSelectChanged
      this.rowCellClickCallback = initParams.rowCellClickCallback
      this.rowCellClickBefore = initParams.rowCellClickBefore
      this.aotuSummaryPmAmount = initParams.aotuSummaryPmAmount
      this.rowCopyEX = initParams.rowCopyEX
      this.rowRadioCallback = initParams.rowRadioCallback
      this.dataVo = initParams.dataVo
      this.level3DetailCellClicked = initParams.level3DetailCellClicked
      this.afterRowCalculation = initParams.afterRowCalculation
      this.fileBlAudit = initParams.fileBlAudit
      this.current = 1
      if (this.$isNotEmpty(initParams.pageSizes)) {
        this.pageSizes = initParams.pageSizes
        this.size = this.pageSizes[0]
      }

      this.historySelectOptions = new Map()
      this.refreshButtonsDisableDataKey =
        initParams.refreshButtonsDisableDataKey || ''
      this.refreshButtonsDisableDataParams =
        initParams.refreshButtonsDisableDataParams || {}
      this.$saveInitParams(this, initParams)

      this.listContentNoSelectionClass = ''
      this.noSelection = false
      if (initParams.noSelection) {
        this.noSelection = initParams.noSelection
        this.listContentNoSelectionClass = 'listContentNoSelection'
      } else if (this.$isNotEmpty(initParams.selectionColumnWidth)) {
        this.selectionColumnWidth = initParams.selectionColumnWidth
      }
      this.noRowHighlight = initParams.noRowHighlight || false // 默认有行高亮背景
      this.supportRowUpAndDown = initParams.supportRowUpAndDown || false
      this.editableAny = initParams.editableAny || false
      this.tableExStyleClass = initParams.tableExStyleClass || ''

      // 返填，在initParams没有这个值时有意义
      initParams.noRowHighlight = this.noRowHighlight

      // 导出按钮设置哪个属性都可以：导出文件或exportExcelName
      if (this.$isNotEmpty(initParams.导出文件)) {
        initParams.exportExcelName = initParams.导出文件
      }

      if (this.$isNotEmpty(initParams.exportExcelName)) {
        initParams.buttons = initParams.buttons || []
        initParams.buttons.push({
          text: '导出',
          icon: 'el-icon-aliiconxiazai',
          enabledType: '-1',
          click: (bt) => {
            var params = this.getParams4ReloadTable(initParams.params)
            params['exportExcelName'] = initParams.exportExcelName + '.xls'
            this.$excelExport(
              initParams.params.dataApiKey,
              this.$refs.table,
              params
            )
          }
        })
      }
      if (this.$isNotEmpty(initParams.multiChoice)) {
        initParams.buttons = initParams.buttons || []
        initParams.buttons.push({
          text: '设置多选',
          enabledType: '-1',
          click: (bt) => {
            this.isMultiChoice = !this.isMultiChoice
            bt.text = this.isMultiChoice ? '设置单选' : '设置多选'
          }
        })
      }

      if (this.supportRowUpAndDown) {
        initParams.buttons.push({
          text: '上移', icon: 'el-icon-top', enabledType: '1+',
          click: bt => {
            this.rowUpOrDown(true)
          }
        })
        initParams.buttons.push({
          text: '下移', icon: 'el-icon-bottom', enabledType: '1+',
          click: bt => {
            this.rowUpOrDown(false)
          }
        })
        initParams.buttons.push({
          text: '复制', icon: 'el-icon-document-copy', enabledType: '1+',
          click: bt => {
            this.rowCopy()
          }
        })
      }

      if (this.$isNotEmpty(initParams.orderNumber)) {
        this.orderNumber = initParams.orderNumber
      }
      if (this.$isNotEmpty(initParams.isShowOrderNumber)) {
        this.orderNumber.isShow = initParams.isShowOrderNumber
      }
      if (this.$isNotEmpty(initParams.rowCheckedCallback)) {
        this.rowCheckedCallback = initParams.rowCheckedCallback
      }

      if (typeof initParams.getSummaries === 'function') {
        this.showSummary = true
      } else if (initParams.showSummary) {
        this.showSummary = initParams.showSummary
      }

      // 递归查询menuId 有则不需要给menuId赋值
      var selectMenuId = (object) => {
        if (object.menuId) {
          initParams.menuId = object.menuId
          initParams.childrenContent = true
          initParams.childrenContentType = object.childrenContentType
          initParams.tabName = object.tabName
          return true
        } else {
          if (object.$parent === undefined) {
            return false
          } else {
            return selectMenuId(object.$parent)
          }
        }
      }
      if (!selectMenuId(this)) {
        initParams.menuId = store.get('menuData').getMenuId() // 菜单id
      }

      // 只有主列表加载时，才会加载子列表
      this.loadTable = this.$isNotEmpty(initParams.loadTable) ? initParams.loadTable : true
      if (this.loadTable === true && this.$isNotEmpty(initParams.listContentSubId)) {
        this.listContentSubId = initParams.listContentSubId
        var listContentExClass = `${initParams.listContentSubId}-main`
        this.listContentClass = `listContent ${listContentExClass}`
        this.$nextTick(() => {
          if (this.$refs.listContentSub.init) {
            this.$refs.listContentSub.childrenContentType = 'table'
            if (this.$refs.listContentSub.$el.querySelector('.el-tabs')) {
              this.$refs.listContentSub.childrenContentType = 'tabs'
            }
            this.$refs.listContentSub.menuId = initParams.menuId
            this.$refs.listContentSub.init(initParams, this)
          }

          if (this.$isEmpty(initParams.listContentSubHeight)) {
            initParams.listContentSubHeight = 700 // 下层内容默认高度是300
          }
          this.listContentSubStyle = `height:${initParams.listContentSubHeight}px;`
        })
      }
      if (this.$isNotEmpty(initParams.listContentClass)) {
        this.listContentClass = 'listContent ' + initParams.listContentClass
      }

      // 环境对象有reloadTable方便业务使用
      initParams.reloadTable = this.reloadTable
      var funcReloadTable = () => {
        this.reloadTable((result) => {
        }, initParams, { 'isReloadByInit': true })
      }

      if (this.$isNotEmpty(initParams.fileName)) {
        initParams.buttons = initParams.buttons || []
        initParams.buttons.push({
          text: '导入',
          icon: 'el-icon-aliiconshangchuan',
          enabledType: '0',
          click: (bt) => {
            this.$showImportExcelDlg(
              initParams.params.impExcelKey,
              initParams.fileName + '.xls',
              initParams.params.tableColumn,
              {
                FORM_TYPE_eq: initParams.params.formType,
                excelImpExpParams: initParams.params.excelImpExpParams,
                // 预算编制导入
                sheetList: initParams.params.sheetList || false,
                isOneUp: initParams.params.isOneUp || '是',
                refData1: initParams.params.refData1 || undefined,
                onSuccess: () => {
                  funcReloadTable()
                }
              }
            )
          }
        })
      }

      var leftTreeNodeClickEx = initParams.leftTreeNodeClick
      initParams.leftTreeNodeClick = (treeNode) => {
        // 默认由点击左树引发的列表筛选后，列表勾选对应树节点id的行
        var checkThisRowAfterSearch = treeNode.id
        if (initParams.noCheckAfterLeftTreeChick === true) {
          // 外界指定左树点击后，不要勾选列表中的行
          checkThisRowAfterSearch = ''
        }

        // 左树点击节点时由外界添加查询参数，这个方法不为空时，表明左树点击会触发查询列表
        var exParams = { checkThisRowAfterSearch: checkThisRowAfterSearch }
        if (initParams.addParamsLeftTreeNodeClick) {
          initParams.addParamsLeftTreeNodeClick(exParams, treeNode)
          this.doSearch(exParams)
        }

        // 左树点击节点时，调用外界额外处理方法
        if (leftTreeNodeClickEx) {
          leftTreeNodeClickEx(treeNode)
        }
      }

      this.isSingleHighlighting = initParams.isSingleHighlighting
      this.$refs.basePage.init(initParams)
      initParams.doLoadTable = async() => {
        this.dataApiKey = initParams.params.dataApiKey
        if (this.$isEmpty(this.dataApiKey)) {
          this.$message.error('initParams.params.dataApiKey 不能为空')
        }
        // 拿搜索条件（取消searchForm中的setTimeOut）
        const searchParam = await initParams.getSearchParam?.()
        const initParam = this.$getInitParams(this) || {}
        Object.assign(initParam.params, { searchParam: { ...initParam.params?.searchParam, ...searchParam }})
        this.showPager = this.$isNotEmpty(initParams.showPager)
          ? initParams.showPager
          : true
        if (this.dataApiKey !== '占位符') {
          funcReloadTable()
        }

        if (!this.showPager || !this.loadTable) {
          this.listContentNoPager = 'listContentNoPager'
        }
      }
    },
    recordColData(colItems) { // 递归处理收集列定义哈希表
      colItems.forEach(item => {
        const initParams = this.$getInitParams(this)
        this.columnsDataPropMap[item.prop] = item
        this.columnsPropMap[item.label] = item.prop
        // 行编辑
        if (this.$isNotEmpty(initParams.编辑针对的实体)) {
          this.remindEdit(item)
        }
        if (initParams.multiChoiceConf === item.label && this.$isEmpty(this.choiceProp)) {
          // 设置需要处理的多选条件
          this.choiceProp = item.prop
        }
        if (this.$isNotEmpty(item.children)) {
          this.recordColData(item.children)
        }
      })
    },
    setEditable(isEditable) { // 动态改变行表单是否可编辑
      this.columnsData.forEach(item => { item.editable = isEditable })
    },
    getParams4ReloadTable(params) {
      // 查询列表数据时使用的参数，提供给列表查询和导出使用
      params = params || {}
      params.size = this.size
      params.current = this.current
      this.$event(this, 'getDataApiParams', params)
      return params
    },
    fillTable(
      result, lastHeightLightId, callback,
      initParams, exParams, isSearchBtn, afterCallBack) {
      initParams = initParams || this.$getInitParams(this)
      if (typeof initParams.reloadTableCallbackBeforeFillTable === 'function') {
        initParams.reloadTableCallbackBeforeFillTable(result, this.$refs.table, this, initParams)
      }

      this.$nextTick(() => {
        this.tableInitialized = true
        this.loadTable = true
        this.columnsData = result.data.columns
        this.mergeKey = result.attributes.mergeKey || ''
        this.customMergeAll = result.attributes.customMergeAll || ''
        this.columnsDataPropMap = {}
        this.recordColData(this.columnsData)

        this.rowsData = result.data.rows
        if (initParams.selectPageAsync !== '1') {
          // selectPage的同步查询总数, 需要设置总页数, 异步查询不需要设置总数
          this.dataTotal = result.data.page?.total || 0
        }
        this.mergeMap = {}
        this.resultAttributes = result.attributes

        // 补齐ID或id
        this.rowsData.forEach(row => {
          var value = this.$getRowId(row)
          row.ID = value
          row.id = value
        })

        // 添加临时数据
        if (this.$isNotEmpty(initParams.tableTempData)) {
          this.rowsData.push(...initParams.tableTempData)
        }

        if (callback) {
          callback(result)
        }

        this.$nextTick(() => {
          var this_ = this
          // var $trs = $(`#${this.tableId} tr.vxe-body--row`)
          let checkRow = null

          // for (let i = 0; i < $trs.length; i++) {
          //   var rowId = this.$getRowId(this_.rowsData[i])
          //   $($trs[i]).attr('rowId', rowId)
          //   $($trs[i]).attr('id', 'rowId' + rowId)
          // }

          if (
            this.$isNotEmpty(lastHeightLightId)
          ) {
            const checkRows = this_.rowsData.filter(row => this_.$getRowId(row) === lastHeightLightId)
            if (checkRows.length) {
              checkRow = checkRows[0]
              this.$refs.table.toggleRowSelection(checkRow, true)
              if (this.$isNotEmpty(this.listContentSubId)) {
                // 双层列表时，下层列表只针对当前最新点击的行
                const row = { row: checkRow }
                exParams = Object.assign(exParams, row)
              }
            }
          }

          if (afterCallBack) {
            afterCallBack()
          }

          // 绑定点击弹出详情框的事件
          // this.$bindShowDetailLinks()
          // this.bindLevel3CellClicked()
          if (typeof initParams.reloadTableCallback === 'function') {
            initParams.reloadTableCallback(result, this.$refs.table, this)
          }

          if (this.$isNotEmpty(this.listContentSubId)) {
            this_.mainTableReload(initParams, exParams)
          }

          this_.loading = false

          const menuHasBtn = result.attributes['menuHasBtn']
          const showButtons = result.attributes['showButtons']
          if (menuHasBtn) {
            this.handleShowButtons(initParams, showButtons)
          }

          this.refreshButtonsDisableData(checkRow)
          // 表格空状态时是否显示空状态图片
          let height = 160
          if (this.$isNotEmpty(initParams.headerLevel)) {
            height += 40 * (initParams.headerLevel - 1) // 是否有多级表头
          }
          if (this.$refs.table) {
            this.showEmptyImg = this.$refs.table.$el.offsetHeight > height
          }
          this.setFixedWrapper()
          this.$refs.table.doLayout()
        })
      })

      return true
    },
    loadTableData(data = []) {
      this.rowsData = data
    },
    handleShowButtons(initParams, showButtons) {
      let hiddenButtons = []
      if (this.$isNotEmpty(initParams.hiddenButtons)) {
        hiddenButtons = initParams.hiddenButtons
      }
      initParams.buttons.forEach(bt => {
        if (showButtons.indexOf(bt.text) === -1) {
          hiddenButtons.push(bt.text)
        }
      })
      this.$refs.basePage.reRenderBtns(hiddenButtons)
    },
    reloadTable(callback, initParams, exParams, isSearchBtn, afterCallBack) {
      // 重新加载表格数据 不是区块表才菜重新加载
      if (!initParams?.isRformBlock) {
        this.loading = true
      }
      if (this.$isNotEmpty(this.dataApiKey)) {
        initParams = initParams || this.$getInitParams(this)
        initParams = initParams || { params: {}}
        exParams = exParams || {}
        if (
          initParams.params.exportExcelName &&
          initParams.params.doExcelExport
        ) {
          delete initParams.params.exportExcelName
          delete initParams.params.doExcelExport
        }
        var params = this.getParams4ReloadTable(initParams.params)
        params = Object.assign(params, exParams)
        if (isSearchBtn) {
          this.current = 1
          params.current = 1
        }
        if (this.showPager === false) {
          params.size = 100000
        }
        if (initParams.menuId) {
          // 新加menuId
          params.menuId = initParams.menuId
        }
        if (initParams.childrenContent) { // 子列表
          params.childrenContent = true
          params.childrenContentType = initParams.childrenContentType
          params.tabName = initParams.tabName
        }

        // 可通过在initParams中指定highlightRowId来实现刷新列表后保持高亮显示指定行
        var lastHeightLightId
        if (initParams.highlightRowId) {
          lastHeightLightId = initParams.highlightRowId
          initParams.highlightRowId = undefined // 只使用一次
        } else {
          const seletions = this.$getTableCheckedIds(this.$refs.table)

          if (seletions.length > 0) {
            lastHeightLightId = seletions[0]
          }
        }

        this.reqTimestamp = Date.now()
        params.reqTimestamp = this.reqTimestamp
        const reqParams = { ...params,
          selectPageAsync: initParams?.selectPageAsync
        }
        this.$callApiParams(
          this.dataApiKey,
          reqParams,
          (result) => {
            if (reqParams.reqTimestamp === this.reqTimestamp) {
              this.fillTable(result, lastHeightLightId, callback,
                initParams, exParams, isSearchBtn, afterCallBack)
            }
            return true
          },
          (result) => {
            this.loading = false
          }
        )
        // 异步查询总数
        if (initParams.selectPageAsync === '1') {
          this.selectPageCountAsync(reqParams)
        }
      } else if (this.$isNotEmpty(this.columnsData)) {
        this.loading = false
      }
    },
    // 对于异步请求获取总页数的处理,此处的后台接口只返回总页数,不会查询每页的记录的详细信息
    selectPageCountAsync(reqParams) {
      this.$callApiParams(
        this.dataApiKey,
        Object.assign(reqParams, { 'selectCount': '1' }),
        (result) => {
          this.dataTotal = result.data.page?.total || 0
          return true
        },
        (result) => {
          this.loading = false
        }
      )
    },
    refreshButtonsDisableData(checkRow) {
      // 后端刷新按钮禁用数据
      if (this.$isNotEmpty(this.refreshButtonsDisableDataKey)) {
        var ids = this.$getTableCheckedIdsSelection(this.rowsData)
        this.$nextTick(() => {
          var params = this.refreshButtonsDisableDataParams
          params.ids = ids.join(',')
          this.$callApiParams(
            this.refreshButtonsDisableDataKey,
            params,
            (result) => {
              if (this.$refs.basePage) {
                this.$refs.basePage.refreshButtonsDisableDataResult = result
                this.$refs.basePage.buttonsDisableData = result.data
              }
              // 从后端刷新回按钮的禁用数据之后，需要同步UI按钮的可用
              if (checkRow) {
                this.$refs.table.toggleRowSelection(checkRow, true)
              }

              this.$nextTick(() => {
                var rows = this.$getTableSelection(this.$refs.table)
                this.rowChecked(rows)
              })
              return true
            }
          )
        })
      } else {
        const checks = this.$getTableSelection(this.$refs.table)
        this.$refs.basePage.rowChecked(checks, !this.tableData?.length)
      }
    },
    syncRowHeightLightWithCheckRows() {
      if (this.noRowHighlight) { // 页面指定没有高亮效果
        return
      }

      var checkedRowsAll = this.$getTableSelection(this.$refs.table)
      if (this.$isEmpty(checkedRowsAll)) {
        return
      }
      var checkedRows = [checkedRowsAll[0]]
      var $trs = $(`#${this.tableId} tr.vxe-body--column`)
      $trs.removeClass('checkedTR')

      var indexMap = {}
      for (let rIndex = 0; rIndex < this.rowsData.length; rIndex++) {
        var rowId = this.$getRowId(this.rowsData[rIndex])
        indexMap[rowId] = rIndex
      }

      // for (let i = 0; i < checkedRows.length; i++) {
      //   var index = this.$getRowId(checkedRows[i])
      //   var rIndex = indexMap[index]
      //   $($trs[rIndex]).addClass('checkedTR')
      //   break // one row highlight only
      // }
      if (checkedRows.length > 0) {
        var index = this.$getRowId(checkedRows[0])
        var rIndex = indexMap[index]
        $($trs[rIndex]).addClass('checkedTR')
      }
    },
    handleCurrentChange(cpage) {
      this.current = cpage
      const initParam = this.$getInitParams(this)
      if (initParam?.isRformBlock) {
        return
      }
      this.reloadTable((result) => {
      }, this.$getInitParams(this))
      this.$emit('handleSearch')
    },
    handleSizeChange(psize) {
      const initParam = this.$getInitParams(this)
      if (initParam?.isRformBlock) {
        return
      }
      this.size = psize
      this.reloadTable((result) => {
      }, this.$getInitParams(this))
      this.$emit('handleSearch')
    },
    baseListBtClick(button) {
      // 点击任何按钮时，向按钮对象中填充数据，提供给业务场景使用
      if (this.loadTable || this.$isNotEmpty(this.rowsData)) {
        button.params.baseListObj = this
        button.params.rows = this.$getTableSelection(this.$refs.table)
      }
      this.$emit('baseListBtClick', button)
    },
    handleSearch(params, callback) {
      const thisVue = this
      new Promise(function(resolve) {
        const initParam = thisVue.$getInitParams(thisVue)
        if (initParam.params.exportExcelName &&
          initParam.params.doExcelExport) {
          delete initParam.params.exportExcelName
          delete initParam.params.doExcelExport
        }
        initParam.params = Object.assign(
          initParam.params, { searchParam: params })
        thisVue.$saveInitParams(thisVue, initParam)
        resolve(thisVue)
      }).then(function(r) {
        // 外界可以通过捕获handleSearch事件，设置cancelReloadTable来终止本次查询数据
        var checkThisRowAfterSearch
        if (thisVue.$isNotEmpty(params)) {
          checkThisRowAfterSearch = params.checkThisRowAfterSearch
        }

        if (thisVue.$isEmpty(params) || params['cancelReloadTable'] !== true) {
          var callbackBefore = callback
          callback = () => {
            // 重新查询后默认去除原来的勾选行
            thisVue.uncheckedAllRows()

            if (checkThisRowAfterSearch) {
              thisVue.toggleRowSelection(checkThisRowAfterSearch, true)
            }

            if (callbackBefore) {
              callbackBefore()
            }
          }

          var exParams = { 'isReloadByhandleSearch': true }
          r.reloadTable((result) => {},
            r.$getInitParams(r), exParams, true, callback)
        }
      })

      this.$emit('handleSearch', params)
    },
    checkboxAll({ records }) {
      this.rowChecked(records)
    },
    async clickRow(data) {
      const $event = data.$event
      let row = null
      let isCheck = true
      // 由于vxetable的api传进来的第一个参数与element的table第一参数不一样，需要兼容外部调用当前方法的
      if ($event) {
        if ($event.currentTarget?._prevClass?.includes('col--tree-node')) return
        row = data.row
      } else {
        row = data
      }

      // Vue.prototype.$setRowCannotChecked设置不能勾选时，行不响应单击事件
      if (!this.$isRowCanChecked(row) || !this.checCheckboxkMethod({ row })) {
        return
      }

      // 阻止单击复选框单元格事件冲突
      if (!data.triggerCheckbox || !this.isVxe) {
        // 单击列表行，切换勾选和高亮效果
        this.uncheckedAllRows()
        this.$refs.table.toggleRowSelection(row)
      } else {
        isCheck = data.cell.parentNode.classList.contains('row--checked')
      }

      const initParam = this.$getInitParams(this)
      // 双层列表时绑定点击行高亮效果
      // 高亮效果支持刷新之后重新勾选之前高亮行
      // 单层列表时，不在这处理，有列表的rowChecked处理高亮
      if (this.$isNotEmpty(this.listContentSubId) && isCheck && !this.isVxe) { // !this.isVxe, 是vxe时走handleRowSelect，点击的是checkbox时可能多选，这里只传了当前行
        // 双层列表时，下层列表只针对当前最新点击的行
        this.mainTableSelectRowChanged([row])
      }
      if (typeof initParam.clickRowCallback === 'function') {
        initParam.clickRowCallback(row, this.$refs.table)
      }
    },
    uncheckedAllRows() {
      return this.$refs.table.clearSelection()
    },
    rowDblclick(row) {
      if (this.isVxe) {
        if (row?.$event?.currentTarget?._prevClass?.includes('col--tree-node')) return
        row = row.row
      }
      if (this.callbackRowDblclick) {
        if (this.fileBlAudit) {
          this.$showDetail(row['单据ID'], undefined, undefined, row.metaName)
        } else {
          this.callbackRowDblclick(row, this.checCheckboxkMethod({ row }))
        }
      }
    },
    disableAllBts() {
      this.$refs.basePage.disableAllBts()
    },
    disableAllBtsResume() {
      this.rowChecked([])
    },
    // 列表多选优化 需要initParams传入 multiChoiceConf multiChoice 配置
    handleRowSelect(selection, row) {
      if (this.$isNotEmpty(this.listContentSubId)) {
        // 处理取消勾选时(点击的是checkbox)，listContentSubId不刷新问题
        // el点击checkbox时不触发row-click
        this.mainTableSelectRowChanged(selection)
      }
      if (!this.isMultiChoice) {
        return
      }
      const fieldVal = row[this.choiceProp]
      const selects = []
      let isCheck = false
      let isClickOther = false

      selection.forEach((item) => {
        if (fieldVal !== item[this.choiceProp]) {
          isClickOther = true
        }
        if (this.$getRowId(item) === this.$getRowId(row)) {
          isCheck = true
        }
      })
      if (isClickOther) {
        this.$refs.table.toggleRowSelection(row, false)
        return this.$message.error(`选择的${this.choiceProp}必须一致`)
      }
      this.$refs.table.clearSelection()

      this.$nextTick(() => {
        this.rowsData.forEach((item) => {
          if (item[this.choiceProp] === fieldVal) {
            this.$refs.table.toggleRowSelection(item, isCheck)
            isCheck && selects.push(item)
          }
        })
        if (this.$refs.basePage) {
          this.$refs.basePage.rowChecked(selects, this.$isEmpty(this.rowsData), this)
        }
      })
    },
    rowChecked(rows) {
      const initialRow = rows && rows.row
      if (!Array.isArray(rows)) {
        rows = this.$getTableSelection(this.$refs.table)
      }

      if (this.isVxe && this.$isNotEmpty(initialRow)) {
        this.handleRowSelect(rows, initialRow)
      }
      // 处理列表勾选时，按钮可用性联动
      if (this.$refs.basePage) {
        this.$refs.basePage.rowChecked(rows, this.$isEmpty(this.rowsData), this)
      }

      this.$nextTick(() => {
        // this.syncRowHeightLightWithCheckRows()
        if (this.supportRowUpAndDown) {
          this.refreshUpDownBtDisabled()
        }
      })
    },
    getRow(rowId) {
      for (let i = 0; i < this.rowsData.length; i++) {
        var rid = this.$getRowId(this.rowsData[i])
        if (rid === rowId) {
          return this.rowsData[i]
        }
      }
    },
    toggleRowSelection(row, selected) { // row可以是行的数据对象或rowId
      if (typeof (row) === 'string') {
        row = this.getRow(row)
      }

      // 设置行的选中状态，这个方法与rowChecked的区别是，
      // 这个方法能实现表格UI上勾选效果，然后后续会触发rowChecked
      // 而rowChecked不能实现勾选效果
      if (row) {
        this.getTable().toggleRowSelection(row, selected)
      }
    },
    refreshUpDownBtDisabled() { // 上移和下移先都设置不可用
      var checkedRows = this.$getTableSelection(this.$refs.table)
      this.setBtProperty('上移', 'disabled', true)
      this.setBtProperty('下移', 'disabled', true)
      this.$rowCheckedCallback4RowUpOrDown(
        checkedRows, this.$refs.basePage, this.rowsData)
    },
    rowUpOrDown(isUp) {
      const checkedRowIds = this.$getTableCheckedIds(this.$refs.table)
      const bt = {
        getRowId: () => checkedRowIds[0],
        params: { baseListObj: this.$refs.basePage },
        getTable: () => this.$refs.table
      }
      this.$rowUpOrDown(bt, isUp, this.rowsData)
      this.$nextTick(() => {
        this.refreshUpDownBtDisabled()
        // this.syncRowHeightLightWithCheckRows()
      })
    },
    rowCopy() {
      var rowData = this.$getTableSelection(this.$refs.table)
      var newRow = this.$clone(rowData)
      newRow.forEach((item, index) => {
        const randomId = 'newRow' + new Date().getTime() + getUUID()
        item.id = randomId
        item.ID = randomId
        if (this.rowCopyEX) {
          this.rowCopyEX(item, this, rowData[index])
        }
        this.rowsData.push(item)
      })
      this.$nextTick(() => {
        var scope = {}
        scope.$index = this.rowsData.length - 1
        this.formula(scope)
        // this.bindLevel3CellClicked()
      })
    },
    // 存在双层内容，主列表当前选择行变化时，通知子列表
    mainTableSelectRowChanged(rows) {
      if (this.$isNotEmpty(this.listContentSubId) &&
        typeof this.$refs.listContentSub.mainTableSelectRowChanged === 'function') {
        this.$refs.listContentSub.mainTableSelectRowChanged(rows)
      }
    },
    // 存在双层内容，主列表重新加载表格时，通知子表
    mainTableReload(initParams, exParams) {
      if (this.$isNotEmpty(this.listContentSubId) &&
        this.$refs.listContentSub &&
        typeof this.$refs.listContentSub.mainTableReload === 'function') {
        this.$refs.listContentSub.mainTableReload(initParams, exParams)
      }
    },
    setScope(scope) { // eltable有,但是VXETable中没有的参数
      if (this.isVxe) {
        scope.$index = scope.$rowIndex
        scope.column.label = scope.column.title
      }
    },
    rowCellChangedFireManually(row, prop) { // 手动触发单元格值变化事件
      const scope = { row: row }
      const colItem = { label: prop, labelOrigin: prop, prop: prop }
      this.rowCellChangedFire(scope, colItem)
    },
    rowCellChangedFire(scope, colItem) {
      this.setScope(scope)
      // 处理表单组件管理必填字段 isRequiredStr
      if (this.$isNotEmpty(scope.row.isRequiredStr)) {
        const isRequired = scope.row.isRequiredStr === '是'
        scope.row.isRequired = isRequired
      }
      if (this.rowCellChanged) {
        this.rowCellChanged(scope, colItem, this)
      }
      if (this.rowRadioCallback) {
        var trueMap = { isTrue: false }
        this.rowRadioCallback(scope, colItem, this.columnsData, trueMap, this.rowsData)
        if (trueMap.isTrue) {
          for (var i = 0; i < this.columnsData.length; i++) {
            this.addRedStar(this.columnsData[i])
            this.$forceUpdate()
          }
        }
      }
      if (this.dataVo !== undefined) {
        var statusBarPurAmountValue = this.dataVo.extData['状态栏采购金额取值']
        if (statusBarPurAmountValue) {
          this.formula(scope, colItem, statusBarPurAmountValue)
        }
      }
      this.cellChangeVerify(scope, colItem)
    },
    cellChangeVerify(scope, colItem) {
      let isVerify = true
      if (this.isColType({ item: colItem, row: scope.row }, '没有列定义的数值列')) {
        const excludeOnChange = {
          'numInput': true,
          'input': true
        }
        isVerify = !excludeOnChange[colItem.editableType]
      } else if (this.isColType({ item: colItem, row: scope.row }, '文本金额数值')) {
        const excludeOnChange = {
          'input': true
        }
        isVerify = !excludeOnChange[colItem.editableType]
      }
      if (isVerify) {
        this.verifyRequired(scope, colItem)
      }
    },
    verifyRequired(scope, colItem) {
      const prop = colItem.prop
      const row = scope.row || {}
      const value = row[prop]
      const isRequired = colItem.columnEntity?.isRequired || false
      if (isRequired && !value) {
        const index = scope.$index
        const labelAlias = colItem.columnEntity?.labelAlias || colItem.label
        this.showBlistError(index, labelAlias, '不能为空')
      } else {
        this.removeErrorTip(scope, colItem)
      }
    },
    // 在渲染表格时使用 addRedStar 方法生成表头的渲染函数
    renderHeader: function(h, { column }) {
      var item = this.columnsData.find((col) => col.prop === column.property)
      if (item) {
        return this.addRedStar(item)(h)
      } else {
        return h('span', column.label)
      }
    },
    rowCellBlurFire(scope, colItem) {
      if (colItem.colType === '数值') {
        scope.row[colItem.prop] = Number(scope.row[colItem.prop])
      }
      this.verifyRequired(scope, colItem)
    },
    // 下拉框change事件
    rowCellSelectChangedFire(scope, colItem, value) {
      this.setScope(scope)
      if (this.rowCellSelectChanged) {
        this.rowCellSelectChanged(scope, colItem, this, value)
      }
      this.verifyRequired(scope, colItem)
    },
    level3CellClicked(item, row) {
      if (!this.isColType({ item, row }, '三级明细列')) {
        return
      }
      if (this.level3DetailCellClicked) {
        this.level3DetailCellClicked(this.$getRowId(row), item.prop)
      }
    },
    bindLevel3CellClicked() {
      if (this.level3DetailCellClicked) {
        var this_ = this
        var $level3DetailColSpan = $('#' + this.tableId + ' .level3DetailColSpan')
        $level3DetailColSpan.unbind()
        $level3DetailColSpan.click(function() {
          var rowId = $(this).attr('rowId')
          var colProp = $(this).attr('colProp')
          this_.level3DetailCellClicked(rowId, colProp)
        })
      }
    },
    formula(scope, colItem = {}, statusBarPurAmountValue) {
      var formulas = []
      this.columnsData.forEach(columnItem => {
        if (columnItem.prop !== colItem.prop && this.$isNotEmpty(columnItem.columnEntity) &&
          (columnItem.columnEntity.colType === '数值' || columnItem.columnEntity.colType === '金额') &&
          this.$isNotEmpty(columnItem.columnEntity.dataRef)) {
          var data = {}
          data.label = columnItem.prop
          var dataRef1 = columnItem.columnEntity.dataRef.replace(/（/g, '(')
          var dataRef2 = dataRef1.replace(/）/g, ')')
          data.formulaName = dataRef2
          const pattern = /[\d\.]|[\u4e00-\u9fa5]+|[+\-\*/\(\)]|\w+/g
          data.formulaSplit = data.formulaName.match(pattern)
          formulas.push(data)
        }
      })

      const keyObs = {}
      formulas.forEach(item => {
        if (this.$isNotEmpty(keyObs[item.label])) {
          keyObs[item.label].push(item)
        } else {
          keyObs[item.label] = []
          keyObs[item.label].push(item)
        }
      })

      if (typeof scope.$index !== 'number' &&
        scope.$index !== undefined) {
        for (var i = 0; i < this.rowsData.length; i++) {
          if (this.rowsData[i].id === scope.$index) {
            scope.$index = i
          }
        }
      }

      for (let j = 0; j < Object.keys(keyObs).length; j++) {
        var formulaSplit = Object.values(keyObs)[j][0].formulaSplit
        var label = Object.values(keyObs)[j][0].label
        for (let k = 0; k < formulaSplit.length; k++) {
          if (this.$isNotEmpty(this.rowsData[scope.$index]?.[formulaSplit[k]])) {
            formulaSplit[k] = this.$fixMoney(this.rowsData[scope.$index][formulaSplit[k]])
          }
        }
        Object.values(keyObs)[j][0].formulaSplit = formulaSplit.join('')
        var reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g') // 判断是否包含中文
        if (!reg.test(Object.values(keyObs)[j][0].formulaSplit)) {
          // if (this.rowsData[scope.$index][statusBarPurAmountValue] !== '0.00') {
          // eslint-disable-next-line no-eval
          var value = eval(Object.values(keyObs)[j][0].formulaSplit)
          this.$set(this.rowsData[scope.$index], label, this.$formatMoney(value))
          // }
        }
      }

      if (this.afterRowCalculation) {
        this.afterRowCalculation(scope)
      }
    },
    setBtEnabledForRowChecked(row, btText, disableValue) {
      this.$refs.basePage.setBtEnabledForRowChecked(row, btText, disableValue)
    },
    removeErrorTip(scope, colItem = {}) {
      this.setScope(scope)
      // 点击有错误的单元格删除错误边框
      const labelAlias = colItem.columnEntity?.labelAlias || colItem.label
      if (this.errorTip[scope.$index] && this.errorTip[scope.$index][labelAlias]) {
        this.$set(this.errorTip[scope.$index], labelAlias, '')
      }
    },
    rowCellClick(scope, colItem, iconType) {
      this.setScope(scope)
      const initParams = this.$getInitParams(this)

      // 用于判断弹框渲染是否需要code
      const isRowCellMap = { isRowCellCode: false }
      if (this.$isNotEmpty(colItem.columnEntity) &&
        colItem.columnEntity.colType === '弹框') {
        if (scope.row[colItem.label + '_禁用'] === true) {
          return
        }

        if (colItem.columnEntity.isRowFormButton) {
          if (this.$isEmpty(initParams.rowFormButtonClick)) {
            this.$message.error('没有设置响应方法：<br/>initParams.rowFormButtonClick')
            return
          }
          initParams.rowFormButtonClick(scope, colItem, this)
          return
        }

        if (iconType === 'el-icon-search' || iconType === undefined) {
          // 为了隔离影响，单独处理区块制单的弹框
          if (this.isBlockViewEdit === true) {
            const refData = { colType: '弹框',
              dataRef: colItem.columnEntity.dataRef,
              label: colItem.prop,
              labelOrigin: colItem.prop }
            const params = { multiple: false, isBlock: true }
            if (this.handleBeforeBlockRef) {
              const isCancel = this.handleBeforeBlockRef(refData, params, scope.row)
              if (isCancel) {
                return
              }
            }

            // 单选时，可能存在其他行已经选择的数据，这些数据不能再次选择，
            // 所以参照列表里不再显示，需要将已选择的数据ID传递到后端，让
            // 后端根据ID进行过滤
            if (params.multiple === false &&
              params['排除已选数据'] === true) {
              const excludedIds = []
              let currentId = scope.row[colItem.prop + 'Id']
              currentId = this.$getRealValueRefID(currentId)
              this.rowsData.forEach(row => {
                let rowId = row[colItem.prop + 'Id']
                if (rowId !== undefined) {
                  rowId = this.$getRealValueRefID(rowId)
                  if (currentId !== rowId) { // 当前参照的行对应的参照行不能排除
                    excludedIds.push(rowId)
                  }
                }
              })
              if (this.$isNotEmpty(excludedIds)) {
                params['排除这些ID'] = excludedIds
              }
            }

            var isRef = (propOrLabel) => { // 判断是否是参照要素
              // 区块行表单规则：参照ID的key由ID变为Id
              return (propOrLabel === colItem.prop + 'ID')
            }

            var wrapProp = (propOrLabel) => {
              // 通过列标题找到prop
              if (this.columnsPropMap[propOrLabel] !== undefined) {
                return this.columnsPropMap[propOrLabel]
              }

              if (isRef(propOrLabel)) {
                // 区块行表单规则：参照ID的key由ID变为Id
                return colItem.prop + 'Id'
              }
              return propOrLabel
            }

            params.callbackBeforeRefComplete =
              (selectedData, params, callbackCloseRefDlg, setBtnUnLoad) => {
                callbackCloseRefDlg()
                if (this.handleAfterBlockRef) {
                  this.handleAfterBlockRef(refData, scope.row, selectedData, this.getColumnsData(this.columnsData))
                }
              }

            if (this.addRformExparamsCallback) {
              const exParams = this.addRformExparamsCallback({ row: scope.row }) || {}
              Object.assign(params, exParams)
            }
            if (this.rowCellClickBefore) {
              this.rowCellClickBefore(scope, colItem, params)
            }
            this.$refData(this.columnsData, refData,
              (label, iscForm) => {
                label = wrapProp(label)
                // iscForm initParams?.getFormValu（只有在行表单时才有改方法，获取cform的value）
                // $refData中传递部门指标id
                return iscForm ? initParams?.getFormValue(label) : scope.row[label]
              },
              (label, value) => {
                const isRefItem = isRef(label)
                label = wrapProp(label)
                if (isRefItem) { // 如果是参照要素本身，其值去掉[tab]xxx数据
                  value = this.$getRealValueRefID(value)
                }
                // const item = this.columnsDataPropMap[label]
                // if (item?.colType === '金额') {
                //   value = this.$formatMoney(value)
                // }
                scope.row[label] = value
              },
              (selectedData, setBtnUnLoad) => {
                if (this.handlebeforeFilldataBlockRef) {
                  // 区块行制单的参照填充数据之前，额外处理数据
                  this.handlebeforeFilldataBlockRef(selectedData, scope.row, setBtnUnLoad, refData)
                }
              }, () => {
                this.verifyRequired(scope, colItem)
              }, params)
            return
          }

          const refData = { colType: '弹框', dataRef: colItem.columnEntity.dataRef }
          const params = { multiple: false }
          // 用于选择基础数据年度选择（默认是当年年度）
          if (this.rowCellClickBefore) {
            this.rowCellClickBefore(colItem, params)
          }
          this.$refData(undefined, refData,
            () => scope.row[colItem.prop + '_tagData'] ? scope.row[colItem.prop + '_tagData'].split(' ')[0] : undefined,
            () => {},
            selectedData => {
              if (this.rowCellClickCallback) {
                this.rowCellClickCallback(scope, colItem, selectedData, isRowCellMap)
              }
              const selectRow = {}
              const list = selectedData.list || []
              // 如果多选把两行合并成一行，用逗号分割
              list.forEach(item => {
                Object.keys(item).forEach(key => {
                  if (!selectRow[key]) {
                    selectRow[key] = item[key]
                  } else {
                    selectRow[key] = selectRow[key] + ',' + item[key]
                  }
                })
              })
              if (selectRow.hasOwnProperty('treeLable')) {
                Object.keys(selectRow).forEach(key => {
                  if (key === 'label') {
                    if (!isRowCellMap.isRowCellCode) {
                      scope.row[colItem.prop] = selectRow[key]
                    } else {
                      scope.row[colItem.prop] = selectRow.treeLable
                    }
                  }
                })
                scope.row[colItem.prop + '_tagData'] = selectRow.itemKey
              } else {
                Object.keys(selectRow).forEach(key => {
                  if (scope.row.hasOwnProperty(key) && key !== 'id') {
                    if (!isRowCellMap.isRowCellCode) {
                      scope.row[colItem.prop] = selectRow[key]
                    } else {
                      scope.row[colItem.prop] = selectRow.treeLable
                    }
                  }
                })
                scope.row[colItem.prop + '_tagData'] = selectRow.id
              }
              if (this.rowCellClickCallback) {
                this.rowCellClickCallback(scope, colItem, selectedData, isRowCellMap)
              }
            },
            {},
            params
          )
        } else {
          if (this.clearRowDataCallback) {
            this.clearRowDataCallback(scope, this.getColumnsData(this.columnsData))
          }
          scope.row[colItem.prop] = ''
          scope.row[colItem.prop + '_tagData'] = ''
          scope.row[colItem.prop + 'Id'] = ''

          this.rowCellChangedFire(scope, colItem)
        }
      }

      if (this.$isNotEmpty(colItem.columnEntity) &&
        colItem.columnEntity.colType === '下拉框') {
        // 下拉的数据由调用方的自行提供
        if (this.customSelectOptionsSupplier) {
          var returnData = this.customSelectOptionsSupplier(scope, colItem)
          if (returnData) {
            this.selectOptions = returnData
            return
          }
        }

        const isHasKey = this.historySelectOptions.has(colItem.columnEntity.dataRef)
        if (isHasKey) {
          const options = this.$clone(this.historySelectOptions.get(colItem.columnEntity.dataRef))
          this.renderDropOptionsBefore?.(colItem, scope, options)
          this.selectOptions = options
        } else {
          // 置空，防止点击第二个下拉框显示上一个的值
          this.selectOptions = []
          // 下拉框的参照设置可能是：选择中标供应商#采购申请单ID,申请时间
          // 此时需要分解出：采购申请单ID和申请时间，然后从row中获取对应的列值传递到后端
          const paramsRefLabelValues = { 'dataRef': colItem.columnEntity.dataRef }
          const tokensLevel1 = colItem.columnEntity.dataRef.split('#')
          if (tokensLevel1.length > 1) {
            const tokensLevel1Str = tokensLevel1[1].replace('，', ',')
            if (this.$isNotEmpty(tokensLevel1Str)) {
              const tokensLevel2 = tokensLevel1Str.split(',')
              tokensLevel2.forEach(tk => {
                paramsRefLabelValues[tk] = scope.row[tk]
              })
            }
          }

          this.$callApiParams('refLabelValuesDynamic',
            paramsRefLabelValues,
            result => {
              // 过滤父级下拉选项
              const filterOptionObj = {}
              result.data?.forEach(opt => {
                if (opt.parentId) {
                  filterOptionObj[opt.parentId] = true
                }
              })
              const options = result.data?.filter(opt => !filterOptionObj[opt.id])
              this.historySelectOptions.set(colItem.columnEntity.dataRef, this.$clone(options))
              this.renderDropOptionsBefore?.(colItem, scope, options)
              this.selectOptions = options
              return true
            })
        }
      }
    },
    getHistoryOptions() {
      return this.historySelectOptions
    },
    indexMethod(index) {
      return ++index
    },
    getTableCheckedIdsStr() {
      // 获取当前列表选择行的id逗号串
      return this.$getTableCheckedIdsStr(this.$refs.table)
    },
    getTableCheckedIds() {
      return this.$getTableCheckedIds(this.$refs.table)
    },
    getTableCheckedRows() {
      return this.$getTableSelection(this.$refs.table)
    },
    getTable() {
      // 获取当前列表的表格对象
      return this.$refs.table
    },
    getRowsData() {
      // 获取当前列表表格的数据对象
      return this.rowsData
    },
    addRowsData(newRow) {
      // 如果是数组(一次性推入多行)
      if (Array.isArray(newRow)) {
        this.rowsData.push(...newRow)
        return
      }
      this.rowsData.push(newRow)
    },
    doActionByIds(params, initParams) {
      // 依据列表勾选的行获取ID逗号分隔串，传递到后端执行相关动作
      const rows = this.$getTableSelection(this.$refs.table)
      if (params.doActionBefore) {
        var doActionBeforeResult = params.doActionBefore(rows)
        if (doActionBeforeResult === false) { // 返回false表明要终止操作
          return
        }
      }

      var idsStr = this.$getTableCheckedIdsStr(this.$refs.table)
      var apiExParams = params.apiExParams || {}
      if (this.$isNotEmpty(apiExParams.idsStr)) { // 静态自定义ids
        idsStr = apiExParams.idsStr
      }
      if (this.$isNotEmpty(params.getIdsStr4doActionByIds)) { // 动态自定义ids
        idsStr = params.getIdsStr4doActionByIds(this, rows)
      }

      var message = params.message
      if (this.$isEmpty(message)) {
        // 通过冒号可传递业务名称
        var op = params.actionName
        var itemName = '条数据'
        if (op.indexOf(':') >= 0) {
          var arr = op.split(':')
          op = arr[0]
          itemName = arr[1]
        }
        message = `确认要${op} ${this.$refs.table.selection.length} ${itemName}吗？`
      }

      var delIdsArr = []
      var tempIdsArr = []
      var idsArr = idsStr.split(',')
      if (this.$isNotEmpty(initParams.tableTempData)) {
        // 过滤临时的ids
        initParams.tableTempData.forEach((item) => {
          if (idsArr.indexOf(item.id) > -1) {
            tempIdsArr.push(item.id)
          }
        })
        // 过滤存在的ids
        delIdsArr = idsArr.filter((item) => {
          if (tempIdsArr.indexOf(item) === -1) {
            return item
          }
        })
        idsStr = delIdsArr.join(',')
      } else {
        delIdsArr = idsArr
      }

      var callbackSuccess = (result) => {
        if (params.callbackSuccess) {
          params.callbackSuccess(result, delIdsArr)
          this.$message.success(result.resultMessage)
        }
        if (params.callbackTableTempDataDelete && tempIdsArr.length > 0) {
          params.callbackTableTempDataDelete(result, tempIdsArr)
          tempIdsArr.forEach((tempId) => {
            // 从临时数据过滤删除的数据
            const delIndex = initParams.tableTempData.findIndex(
              (item) => item.id === tempId
            )
            initParams.tableTempData.splice(delIndex, 1)
          })
          delIdsArr = []
          tempIdsArr = []
        }
        if (params.isReloadTable) {
          this.reloadTable(params.callbackReloadTable, initParams)
        }
        if (params.isReloadTree) {
          this.$event(this, 'projectInit')
        }
        this?.$refreshCount(this)
      }

      var callbackFailed = (result) => {
        if (tempIdsArr.length > 0 && delIdsArr.length === 0) {
          if (params.callbackTableTempDataDelete) {
            params.callbackTableTempDataDelete(result, tempIdsArr)
          }
          tempIdsArr.forEach((tempId) => {
            // 从临时数据过滤删除的数据
            const delIndex = initParams.tableTempData.findIndex(
              (item) => item.id === tempId
            )
            initParams.tableTempData.splice(delIndex, 1)
          })
          delIdsArr = []
          tempIdsArr = []
          if (params.isReloadTable) {
            this.reloadTable(params.callbackReloadTable, initParams)
          }
          return true
        }
      }

      var actionParams = { ids: idsStr }
      Object.assign(actionParams, apiExParams)
      this.$callApiParamsConfirm(
        message,
        undefined,
        params.apiKey,
        actionParams,
        callbackSuccess,
        callbackFailed
      )
    },
    // 获取表格序号
    getIndex($index) {
      return (this.current - 1) * this.size + $index + 1
    },
    setBtProperty(btText, property, value) {
      this.$refs.basePage.setBtProperty(btText, property, value)
    },
    getIconClass(status) {
      if (this.$isNotEmpty(status)) {
        return 'el-icon-circle-close'
      } else {
        return 'el-icon-search'
      }
    },
    radioSearch(param = {}, refresh = true) {
      this.loadTable = false
      this.radioParam = param
      this.$call(this, 'searchForm', 'changeRadioParam', param)
      if (!refresh) {
        return
      }
      this.$call(this, 'searchForm', 'handleSearch', 'ruleForm')
    },
    setCellDisable(rowIdOrRowIndex, label, disabled) {
      if (this.$isNotEmpty(this.rowsData) && this.$isNotEmpty(rowIdOrRowIndex)) {
        for (let i = 0; i < this.rowsData.length; i++) {
          var row = this.rowsData[i]
          if (this.$getRowId(row) === rowIdOrRowIndex ||
            (i + '') === (rowIdOrRowIndex + '')) {
            this.$set(row, `${label}_禁用`, disabled)
            break
          }
        }
      }
    },
    setCellValue(rowIdOrRowIndex, propOrLabel, value) {
      if (this.$isNotEmpty(this.rowsData) && this.$isNotEmpty(rowIdOrRowIndex)) {
        var getPropByLabel = (label) => { // 通过列标题找到prop
          if (this.columnsPropMap[label] !== undefined) {
            return this.columnsPropMap[label]
          }
        }

        for (let i = 0; i < this.rowsData.length; i++) {
          var row = this.rowsData[i]
          if (this.$getRowId(row) === rowIdOrRowIndex ||
            (i + '') === (rowIdOrRowIndex + '')) {
            var propByLabel = getPropByLabel(propOrLabel)
            if (propByLabel) {
              propOrLabel = propByLabel
            }

            this.$set(row, propOrLabel, value)
            var formatedValue = this.formatColData(row, propOrLabel)
            this.$set(row, propOrLabel, formatedValue)
            break
          }
        }
      }
    },
    getCellValue(rowIdOrRowIndex, propOrLabel) {
      if (this.$isNotEmpty(this.rowsData) && this.$isNotEmpty(rowIdOrRowIndex)) {
        var getPropByLabel = (label) => { // 通过列标题找到prop
          if (this.columnsPropMap[label] !== undefined) {
            return this.columnsPropMap[label]
          }
        }

        for (let i = 0; i < this.rowsData.length; i++) {
          var row = this.rowsData[i]
          if (this.$getRowId(row) === rowIdOrRowIndex ||
            (i + '') === (rowIdOrRowIndex + '')) {
            var propByLabel = getPropByLabel(propOrLabel)
            if (propByLabel) {
              propOrLabel = propByLabel
            }
            return row[propOrLabel]
          }
        }
      }
      return undefined
    },
    aotuPmAmountMethod() {
      if (this.aotuSummaryPmAmount) {
        this.aotuSummaryPmAmount(this.rowsData)
      }
    },
    setTableData(data) {
      this.rowsData = data
    },
    /**
     * 根据什么字段 删除rowsData的数据
     * @param {*} field 字段(行中的字段 每种场景可能需要的字段不同)
     * @param {*} id 传入的id
     */
    deleteTableDataByField(field, id) {
      if (typeof field === 'object') {
        let isMatch = true
        for (let i = this.rowsData.length - 1; i >= 0; i--) {
          const row = this.rowsData[i]
          for (const key in field) {
            if (field.hasOwnProperty(key) && row.hasOwnProperty(key) && row[key] === field[key]) {
              isMatch = true
              continue
            } else {
              isMatch = false
              break
            }
          }
          if (isMatch) {
            this.rowsData.splice(i, 1)
          }
        }
      } else {
        for (let i = this.rowsData.length - 1; i >= 0; i--) {
          if (this.rowsData[i][field] === id) {
            this.rowsData.splice(i, 1)
          }
        }
      }
    },
    clearTableData() {
      this.rowsData.splice(0, this.rowsData.length)
    },
    reRenderBtns(hiddenButtons) {
      this.$refs.basePage.reRenderBtns(hiddenButtons)
    },
    doSearch(exParams) { // 手动触发搜索
      this.$refs.basePage.doSearch(exParams)
    },
    resetSearch() {
      this.$refs.basePage.resetSearch()
    },
    initLeftTree() {
      this.$refs.basePage.initLeftTree()
    },
    getBaseListExData() {
      return this.exData
    },
    // 行表单编辑时，显示错误信息
    // errorData.key = 行索引，errorData.value = map
    // map.key = 列标题（col.label），map.value = 错误消息
    showError(errorData, isBlock) {
      this.clearError()
      var errorCount = 0
      if (errorData) {
        var indexOffset = 0
        if (this.noSelection === false) {
          indexOffset++
        }
        if (this.orderNumber.isShow === true) {
          indexOffset++
        }

        let cols = this.columnsData
        if (isBlock) {
          // 如果是区块表单的错误
          cols = cols.filter(col => col.columnEntity.isEnabled === '是')
        }
        var labelIndexMap = {}
        const aliasLabel = {}
        for (var i = 0; i < cols.length; i++) {
          // 同时存放别名label和原始label
          labelIndexMap[cols[i].columnEntity.labelOrigin] = indexOffset + i
          labelIndexMap[cols[i].columnEntity.labelAlias] = indexOffset + i
          aliasLabel[cols[i].columnEntity.labelOrigin] = cols[i].columnEntity.labelAlias ? cols[i].columnEntity.labelAlias : cols[i].columnEntity.labelOrigin
          aliasLabel[cols[i].columnEntity.labelAlias] = cols[i].columnEntity.labelAlias ? cols[i].columnEntity.labelAlias : cols[i].columnEntity.labelOrigin
        }
        const errTextObj = {}
        for (let rowIndex = 0; rowIndex < this.rowsData.length; rowIndex++) {
          var rowErrorData = errorData[rowIndex + '']
          if (!rowErrorData) {
            continue
          }
          var errorKeys = Object.keys(rowErrorData)
          errorKeys.forEach(label => {
            var colIndex = labelIndexMap[label]
            if (colIndex) {
              var message = label + ': ' + rowErrorData[label]
              if (this.$isNotEmpty(message)) {
                if (aliasLabel[label]) {
                  this.showBlistError(rowIndex, aliasLabel[label], ' ')
                }
                // 如果一行中有多个错误 则追加 否则新建
                if (errTextObj[rowIndex]) {
                  errTextObj[rowIndex] += `<br />${message}`
                } else {
                  errTextObj[rowIndex] = `${message}`
                }
                errorCount++
              }
            }
          })
        }
        let errorText = `检查发现错误:<br />`
        if (Object.keys(errTextObj).length) {
          Object.keys(errTextObj)?.map(errObj => {
            errorText += errTextObj[errObj] + '<br />'
          })
        }
        this.$message.error(errorText)
      }
      return errorCount
    },
    clearError() {
      this.errorTip = {}
    },
    headerClick(data, e) {
      if (this.callbackHeaderClick) {
        this.callbackHeaderClick(data, e)
      }
    },
    clearCurrentRFormTable() {
      this.currentRFormTable = ''
    },
    setCurrentRFormTable() {
      this.currentRFormTable = 'currentSelectedTable'
    },
    changeShowSummary(show) {
      this.showSummary = show
    },
    changeShowEmptyImg(show) {
      this.showEmptyImg = show
    },
    /**
     * 处理base-list数据
     * @param {Object} obj 需要修改的数据对象
     * { aaa: false } 修改base-list的aaa数据为false
     */
    changeBaseListData(obj) {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) { // 确保只遍历对象自身的属性
          this[key] = obj[key]
        }
      }
    },
    changePagerConfig(config) {
      const showPager = config?.showPager
      const pageSizes = config?.pageSizes
      const dataTotal = config?.dataTotal
      const blockConfig = config?.blockConfig
      // 上限显示行数为0 或者不启用分页 重置分页数据
      if (blockConfig?.upperLimitLine === 0 || blockConfig?.page === false) {
        this.showPager = false
        this.pageSizes = [100000]
        this.size = 100000
        return
      }
      this.showPager = showPager
      this.pageSizes = [pageSizes]
      this.size = pageSizes
      this.dataTotal = dataTotal
      // 如果总数小于等于上限显示行数 则要回到第一页
      if (this.dataTotal <= blockConfig?.upperLimitLine) {
        this.current = 1
      } else {
        // 设置当前页码
        this.current = blockConfig?.mode === '设计' ? Math.ceil(this.dataTotal / blockConfig?.upperLimitLine) : 1
      }
    },
    getInitParams() {
      return this.$getInitParams(this)
    },
    reInitLayout() {
      this.$refs.basePage?.reInitLayout()
    }
  },
}
</script>

<style>
  .listContent {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }
  .listContentMain {
    overflow: hidden;
    flex: 1 1 100%;
    position: relative;
  }
  .listContentMain .el-checkbox { margin-right: 5px; }
  .listContentMain .el-checkbox__label { padding-left: 5px; }
  .listContentMain .el-checkbox-group { padding-top: 5px; }

  .level3DetailColSpan .el-input__prefix {
    top: 1px !important;
  }
  .danger-color {
    color: red;
  }
</style>
<style lang="scss">
  .listContentPagination {
    .el-pagination__sizes {
      margin: 0 12px 0 0;
    }
    .el-pagination__jump {
      .el-pagination__editor {
        margin-left: 8px;
      }
      margin-left:0px ;
    }
    flex: 1;
    color: #666666 !important;
    text-align: right;
    .el-input {
      border: 1px transparent solid !important;
    }
    .is-focus{
      border: #478bfe 1px solid !important;
      box-sizing: border-box;
    }
    .btn-prev,
    .btn-next{
      color: #999999 !important;
      &:disabled{
         background-color: #f5f5f5 !important;
      }
    }
    button{
      border: #dddddd 1px solid !important;
    }
  }
  .BLDefInput{
    &>div{
      position: relative;
    }
    span {
      &.el-input__prefix{
        display: flex;
        -webkit-box-align: center;
        align-items: center;
        top: 0px !important;
        left: 5px;
      }
    }
    #tkInput{
      padding-left: 30px;
    }
    .el-icon-search:before{
      font-size: 20px;
    }
    .el-icon-circle-close:before{
      font-size: 20px;
    }
  }
</style>
<style lang="scss" scoped>
  // 可能会同时命中isItemEditable 和文本 所以取当前class下的span
  .edit-container > span {
    display: inline-block;
    // 减1px（show-overflow-tooltip在可编辑的不需要）
    width: calc(100% - 1px);
  }
  .pagination {
    position: relative;
    &-absolute {
      position: absolute;
      right: 0;
      top:-15px;
    }
    &-container {
      z-index: 10;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin: 20px 0 0;
    }
    &-info{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
      letter-spacing: 0;
      font-weight: 400;
      margin-right: 10px;
    }
  }
  .rowFormError {
    position: absolute;
    top: 0px;
    right: 1px;
    width: calc(100% - 8px);
    height: 80%;
    color: #F56C6C;
    text-align: right;
    line-height: normal;
    margin: 5px 4px;
    border: 1px solid #F56C6C;
    border-radius: 2px;
    font-size: 12px;
    pointer-events: none;
  }
  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .rowFormButton .el-button { padding: 9px 12px;font-size: 14px; }
  .rowFormButton .el-input { width: calc(100% - 78px);}
  .rowFormButton /deep/.el-input .el-input__inner {
    // text-align: left !important;
    border: none !important;
    padding-left: 5px !important;
    padding-right: 0;
  }
  .rowFormButton {
    display: flex;
    flex-wrap: nowrap;
  }
</style>
