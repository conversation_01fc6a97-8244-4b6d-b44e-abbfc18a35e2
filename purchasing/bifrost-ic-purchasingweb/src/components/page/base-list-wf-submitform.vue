<template>
  <el-dialog
    append-to-body
    title="送审"
    :visible.sync="isdialog"
    width="640px"
    :close-on-click-modal='false'
    @close="handleClose(true)">
    <div v-loading="loading"
         element-loading-spinner="el-icon-loading"
         class="loading-size common-page submitForm">
      <el-form label-width="96px" :model="auditData" :rules='rules'>
        <el-form-item label="指定类型" v-show="showAuditNext">
          <el-radio-group v-model="typeRadio" @change="typeChange"
                          :disabled='!showAuditNext'>
            <el-radio label="1">指定用户</el-radio>
            <el-radio label="2">指定角色</el-radio>
          </el-radio-group>
        </el-form-item>
        <div style="display: flex">
          <el-form-item label="下一环节人:" style="flex: 1;" v-show="showAuditNext && typeRadio == 1" prop="nextUserId">
            <el-select label="下一环节人:" :loading="nextAuditUsersLoad" filterable  v-model="auditData.nextUserId" clearable style="width: 149px;">
              <el-option
                v-for="item in nextAuditUsers"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="下一环节人:" style="flex: 1;" v-show="showAuditNext && typeRadio == 2" prop="nextRoleId">
            <el-select label="下一环节人:" :loading="nextAuditUsersLoad" filterable  v-model="auditData.nextRoleId" clearable style="width: 149px;">
              <el-option
                v-for="item in nextAuditRoles"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="紧急程度:" style="flex: 1" v-show="showEmeDegrees">
            <el-select label="紧急程度:" v-model="auditData.emeDegree" clearable style="width: 149px;">
              <el-option
                v-for="(item, index) in emeDegrees"
                :key="index"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div style="display: flex" v-if="showAuditCountersign">
          <el-form-item label="会签人员:" style="flex: 1">
            <el-input  v-model="auditCountersignVal" placeholder="请点击指定会签人员" readonly
                       v-on:click.native="countersignClick">
              <el-button slot="append" icon="el-icon-search"></el-button>
            </el-input>
          </el-form-item>
        </div>
        <el-form-item  style="margin-bottom: 18px" v-show="isShowAudit" class="blod-checkbox">
          <el-checkbox v-model="auditData.checkAudit">后续流程节点出现我审核时,自动跳过审核</el-checkbox>
        </el-form-item>
        <el-form-item label="常用语:" class="audit-common-words useFulTexts">
          <el-button round
                     v-for="(item, index) in useFulTexts"
                     :title="item.title"
                     :key="index"
                     @click="setUseful(item.title)" >{{item.text}}</el-button>
          <el-button type="primary" icon="el-icon-d-arrow-right"  @click="setUsefulData" class="useFulTextBt">更多</el-button>
        </el-form-item>
        <el-form-item label="备注说明:" style="margin-bottom: 10px;">
          <!--这里不能使用类型为textarea的el-input，因为输入会删除文字时会造成页面闪烁-->
          <div class="el-textarea el-input--small">
            <el-input
              type="textarea"
              rows="4"
              v-model="auditData.opinion"
              style="resize: none; min-height: 32px;"
              maxlength="100"
              show-word-limit
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button class="btn-normal" @click="isdialog = false" > 取消</el-button>
      <el-button class="btn-normal" :loading="submitLoading" :disabled="loading" type="primary" @click="handleSumbit"> 送审 </el-button>
    </template>
    <wf-useful-list-detail ref="appWfUsefulListDetail" @usefulChange="usefulChange" @setUseful="setUseful" />
    <wf-countersign-userlist ref="appWfCountersignUserlist" @conUserListChange="conUserListChange"></wf-countersign-userlist>
  </el-dialog>
</template>

<script>
export default {
  name: 'base-list-wf-submitform',
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    }
  },
  data() {
    return {
      submitSuccess: false,
      paramsOutside: {},
      auditData: this.makeDefaultAuditData(),
      isdialog: this.dialog,
      showAuditNext: true,
      showAuditNextRequired: false,
      nextAuditUsers: [],
      showEmeDegrees: true,
      emeDegrees: ['常规', '紧急', '特急'],
      showAuditCountersign: false,
      auditCountersignVal: '',
      useFulTexts: [],
      useFuls: [],
      rules: {
        nextUserId: [
          { required: false, message: '请选择下一环节人', trigger: 'change' }
        ] },
      dataApiKey: '',
      dataType: '',
      currtNodeName: '',
      currtNodeBizid: '',
      currtNodeMetaId: '',
      table: undefined,
      initParams: undefined,
      parentVu: undefined,
      disabledCountersignBtn: false, // 是否加载完成下一环节人
      submitLoading: false,
      getRowIdsOutside: null,
      loading: false,
      nextAuditUsersLoad: false,
      typeRadio: '1',
      nextAuditRoles: [], // 指定角色的数据
      isShowAudit: false
    }
  },
  methods: {
    init(parentVu, dataType, paramsOutside) {
      this.loading = true
      this.submitLoading = false
      paramsOutside = paramsOutside || {}
      this.paramsOutside = paramsOutside
      this.getRowIdsOutside = paramsOutside.getRowIds

      this.dataType = dataType
      // this.table = parentVu.getBaseListTable()
      this.table = parentVu.rows
      this.parentVu = parentVu

      var rowIds = this.table.map(item => item.bizid)
      if (dataType !== 'CformDataEntity') {
        this.dataApiKey = 'wfAction' + dataType
      } else {
        this.dataApiKey = parentVu.dataApiKey
      }
      if (rowIds.length === 1) {
        var checkedRows = this.table
        if (paramsOutside.noBackType) {
          checkedRows = []
        }
        // eslint-disable-next-line no-empty

        var params = {
          id: rowIds[0],
          actionKey: 'getCurrentNodeAuditOfInfo' }
        this.$callApiParams(this.dataApiKey,
          params, result => {
            // this.auditData = result.data.remark
            this.showAuditNext = result.data.currtNode.isNextAuthUser === '启用'
            this.showAuditNextRequired = result.data.currtNode.isNextAuthUserRequired === '启用'
            // this.showAuditSpecify = result.data.currtNode.isBackNode === '启用'
            this.showAuditCountersign = result.data.isCountersign === '启用'
            this.isShowAudit = result.data.isShowAudit
            // this.showElectronicSeal = result.data.currtNode.isElectronicSeal === '启用'
            if (this.showAuditCountersign) {
              this.showAuditNext = false
            }
            if (result.data.isNotExistNextNode === '1') {
              this.showAuditNext = this.showAuditCountersign = false
            }
            if (this.showAuditNext || this.showAuditCountersign) {
              this.typeRadio = '1'
              this.getNextNodeAssign(rowIds[0])
            }
            if ((this.showAuditNext || this.showAuditCountersign) &&
              result.data.currtNode.isNextAuthUserRequired === '启用') {
              this.rules.nextUserId = [
                { required: true, message: '请选择下一环节人', trigger: 'change' }
              ]
              this.rules.nextRoleId = [
                { required: true, message: '请选择下一环节人', trigger: 'change' }
              ]
              this.showAuditNextRequired = true
            }
            this.currtNodeName = result.data.currtNode.name
            this.currtNodeBizid = result.data.currtNode.bizid
            this.currtNodeMetaId = result.data.currtNode.wfMetaId
            this.initUsefulData(rowIds[0])
            if (checkedRows[0]?.backType === '退送后再次送审跳至本岗审核') {
              this.showAuditNext = this.showAuditCountersign = false
            }
            this.loading = false
            return true
          })
      } else {
        this.loading = false
        this.showAuditNext = this.showAuditCountersign = false
      }
      this.initUsefulData()
    },
    // getRowIds() {
    //   if (this.getRowIdsOutside) {
    //     return this.getRowIdsOutside()
    //   }
    //   return this.$getTableCheckedIds(this.table)
    // },
    getNextNodeAssign(dataId) {
      // ====== TODO: 获取下个节点审核人
      var nextParams = {}
      nextParams.actionKey = 'getNextNodeAssign'
      nextParams.id = dataId
      nextParams.showAuditCountersign = this.showAuditCountersign
      this.nextAuditUsersLoad = true
      this.$callApiParams(this.dataApiKey,
        nextParams, result => {
          this.nextAuditUsers = result.data.nextNodeAssigns
          this.nextAuditRoles = result.data.nextNodeRoles
          this.nextAuditUsersLoad = false
          this.disabledCountersignBtn = true
          this.typeChange()
          return true
        }, () => {
          this.nextAuditUsersLoad = false
        })
    },
    conUserListChange(obj, data) {
      let nextUserIdStr = ''
      let nextUserIdVal = ''
      let mainAuditId = ''// 主审核人
      for (let i = 0; i < obj.length; i++) {
        nextUserIdStr += obj[i].key + ','
        nextUserIdVal += obj[i].label + ','
        if (obj[i].isMainUser) {
          mainAuditId = obj[i].key
        }
      }
      this.auditData.mainAuditId = mainAuditId
      this.auditData.nextUserId = nextUserIdStr
      this.auditCountersignVal = nextUserIdVal.substring(0, nextUserIdVal.length - 1)
    },
    usefulChange() {
      this.initUsefulData()
    },
    initUsefulData(dataId) {
      this.auditData.opinion = ''
      this.isUsefuled = false
      this.$callApiParams('getCurrentNodeWfNodeUsefuls',
        { nodeName: this.currtNodeName, isUsefulPage: true, size: 20, current: 1 },
        result => {
          // if (!result.attributes.isSubmitDefault) {
          //   this.auditData.opinion = ''
          // } else {
          //   this.auditData.opinion = ''
          // }
          var wfNodeUsefuls = result.data.rows
          this.useFuls = wfNodeUsefuls
          const arr = []
          for (var i = 0; i < wfNodeUsefuls.length; i++) {
            if (i === 0) {
              if (!result.attributes.isSubmitDefault) {
                // this.auditData.opinion = wfNodeUsefuls[i].usefulText
              }
            }
            const bt = {}
            if (i < 15) {
              bt.title = wfNodeUsefuls[i].usefulText
              bt.text = wfNodeUsefuls[i].usefulText
              if (wfNodeUsefuls[i].usefulText.length > 15) {
                bt.text = wfNodeUsefuls[i].usefulText.slice(0, 14) + '...'
              }
              arr.push(bt)
            } else {
              this.isUsefuled = true
              break
            }
          }
          this.useFulTexts = arr
          // this.isUseful = this.useFuls.length > 0
          if (dataId) {
            this.getCurrtUseful(dataId)
          }
          return true
        })
    },
    getCurrtUseful(dataId) {
      // ====== TODO: 获取当前节点的节点常用语
      var backParams = {}
      backParams.actionKey = 'getCurrtUseful'
      backParams.id = dataId
      this.$callApiParams(this.dataApiKey,
        backParams, result => {
          if (result.data.currtUsefulText) {
            this.auditData.opinion = result.data.currtUsefulText
          }
          return true
        })
    },
    setUsefulData() {
      var node = {}
      node.usefuls = this.useFuls
      node.name = this.currtNodeName
      node.bizid = this.currtNodeBizid
      node.metaId = this.currtNodeMetaId
      // 设置页面为用户常用语页面
      this.$refs.appWfUsefulListDetail.isUsefulUser = true
      this.$refs.appWfUsefulListDetail.isUsefulPage = true
      if (this.auditData.opinion && this.auditData.opinion !== '同意') {
        var istext = 0
        for (let i = 0; i < this.useFuls.length; i++) {
          if (this.useFuls[i].usefulText === this.auditData.opinion) {
            istext = 1
            break
          }
        }
        if (istext === 0) {
          this.$confirm('是否需要将审核意见框的数据添加到常用语', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          }).then(() => {
            this.$refs.appWfUsefulListDetail.initMeta(node.name, node, true, this.auditData.opinion)
          }).catch(() => {
            this.$refs.appWfUsefulListDetail.initMeta(node.name, node, true)
          })
          return true
        }
      }
      this.$refs.appWfUsefulListDetail.initMeta(node.name, node, true)
    },
    setUseful(title) {
      this.auditData.opinion = title
    },
    countersignClick() {
      if (!this.disabledCountersignBtn) {
        return
      }
      this.$refs.appWfCountersignUserlist?.init(this.nextAuditUsers)
    },
    makeDefaultAuditData() {
      return {
        nodeName: '',
        auditUser: '',
        opinion: '',
        commonWords: '',
        nextUserId: '',
        nextRoleId: '',
        emeDegree: '常规',
        checkAudit: false
      }
    },
    handleSumbit() {
      // 存在下一环节人时必须选
      // eslint-disable-next-line no-empty
      if (this.showAuditNext) {
        if (this.auditData.nextUserId === '' && this.showAuditNextRequired && this.typeRadio === '1') {
          this.$message.error('下一环节人不能为空')
          return
        }
      }

      let rowIds = this.table.map(item => item.bizid)?.join(',')
      var params = this.auditData
      params.ids = rowIds
      params.dataType = this.dataType
      this.submitLoading = true
      this.$callApiParams('WFSUBMIT'
        , params, result => {
          if (result.success) {
            // 保存并送审
            this.submitSuccess = true
            if (this.paramsOutside.saveSuccess) {
              this.paramsOutside.saveSuccess?.(this.submitSuccess)
            }
            // if (this.parentVu.activeTab === this.parentVu.tabTitleTodo) {
            //   const dataTypeMap = {
            //     'CformDataEntity': true,
            //     'BaBizEntity': true,
            //     'PerformanceMonitorEntity': true,
            //     'PerformanceEvalInfoEntity': true,
            //     'RefundRechangeBizEntity': true,
            //     'PurPolicyEntity': true,
            //     'FileBlEntity': true
            //   }
            //   if (dataTypeMap[this.dataType]) {
            //     try {
            //       // this.parentVu.reloadTable()
            //       this.parentVu.changeActiveTab({ name: '已办' })
            //     } catch (error) {
            //       this.parentVu.$parent.reLoad()
            //     }
            //   } else if (this.parentVu.changeActiveTab) {
            //     this.parentVu.changeActiveTab({ name: '已办' })
            //   } else if (this.parentVu.reloadTable) {
            //     this.parentVu.reloadTable()
            //   }
            // } else {
            //   if (this.parentVu.changeActiveTab) {
            //     this.parentVu.changeActiveTab({ name: '已办' })
            //   } else if (this.parentVu.reloadTable) {
            //     this.parentVu.reloadTable()
            //   } else if (this.parentVu.$reInit) {
            //     this.parentVu.$reInit(this)
            //   }
            // }
            this.isdialog = false
            this.$refreshCount(this)
          } else {
            this.submitLoading = false
          }
        }, () => {
          this.submitLoading = false
        })
    },
    handleClose(isClose) {
      this.isdialog = false
      this.isDetails = false
      this.auditData = this.makeDefaultAuditData()
      // this.$refs.appWfCountersignUserlist?.resetData()
      this.auditCountersignVal = ''
      // 保存并送审,只有在关闭的回调调
      if (isClose && typeof isClose === 'boolean' && !this.submitSuccess) {
        this.paramsOutside.saveSuccess?.(this.submitSuccess)
      }
      this.submitSuccess = false
      this.$emit('update:dialog', false)
      this.$parent.init()
    },
    show(parentVu, dataType, paramsOutside) {
      this.init(parentVu, dataType, paramsOutside)
      this.dialog = true
    },
    typeChange(val) {
      if (this.showAuditNext) {
        this.auditData.nextUserId = ''
        this.auditData.nextRoleId = ''
      }
    }
  }
}
</script>

<style lang="scss">
.submitForm .el-form .el-form-item .el-form-item__label {
  color: #303133;
  font-weight: 600;
}
</style>
