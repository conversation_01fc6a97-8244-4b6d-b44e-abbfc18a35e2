<template>
  <div id="audit-file-invoice-tab" class="audit-file-invoice-tab">
    <attach-audit-extend ref="attachAudiExtend"/>
    <e-invoice ref="eInvoice"/>
  </div>
</template>

<script>
import AttachAuditExtend from '../bizz/file/attach-audit-extend'
export default {
  name: 'audit-file-invoice-tab',
  components: { AttachAuditExtend },
  data() {
    return {
    }
  },
  methods: {
    init(dummyObj, params) {
      if (params.dataVo) {
        params.dataVo.eInvoiceCount = () => { // 获取发票列表total
          return this.$refs.eInvoice.$children[0].attList.length
        }
        this.$refs.attachAudiExtend.setTableSizeMedium()
        this.$refs.attachAudiExtend.init(params.dataVo, params.nodeName, true)
        this.$refs.eInvoice.$children[0].isDetail = true
        this.$refs.eInvoice.$children[0].initMeta(params.dataVo)
      }
    }
  }
}
</script>
