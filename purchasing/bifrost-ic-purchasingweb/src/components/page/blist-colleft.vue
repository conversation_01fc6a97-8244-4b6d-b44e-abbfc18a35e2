<template>
  <easyTree
    class="blist-colleft"
    ref="easyTree"
    v-model="treeCheck"
    :nodes="treeNode"
    default-expand-all
    isGetChild
    :multiple="false"
    :treeProps="treeProps"
    @node-click="nodeClick"
  />
</template>

<script>

export default {
  name: 'blist-colleft',
  props: {
    colItem: {
      type: Object,
      default: () => {}
    },
    exParams: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      treeCheck: [],
      treeNode: [],
      treeProps: {
        label: 'codeLabel',
        children: 'children',
        idKey: 'itemKey',
        id: 'id',
        parentId: 'parentId'
      }
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    reset() {
      this.treeCheck = []
      this.$refs.easyTree.reset()
      this.$refs.easyTree.clearCheck()
    },
    nodeClick(data) {
      const searData = {}
      const searchList = []
      const composeDatta = (list = []) => {
        if (!list.length) return
        list.forEach(item => {
          searchList.push(item.codeLabel)
          composeDatta(item.children)
        })
      }
      composeDatta([data])

      searData[`${this.colItem.prop}_in`] = searchList.join(',')
      this.$emit('nodeClick', searData)
    },
    getTreeData() {
      const dataRef = this.colItem.columnEntity?.treeQueryCondition
      if (!dataRef) return
      const refData = { colType: '弹框', dataRef: this.colItem.columnEntity?.treeQueryCondition }
      this.$refData(undefined, refData,
        () => {}, () => {},
        () => {
        },
        () => {},
        {
          querySearchFn: (params) => {
            const reqParams = { ...params, size: 99999, current: 1, ...(this.exParams || {}) }
            this.$callApiParams(params.apiKey, reqParams, (result) => {
              // 树
              if (!result?.attributes?.columns?.length) {
                const list = result.data || []
                list.forEach((item, index) => {
                  item.codeLabel = item.itemKey + ' ' + item.label
                  item.children = []
                })
                this.treeNode = list
              } else {
                // 表格
                const list = result.attributes.rows || []
                list.forEach((item, index) => {
                  item.codeLabel = item.targetRef
                  item.itemKey = item.targetRef
                  item.children = []
                })

                this.treeNode = list
              }
              return true
            }, () => {})
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.blist-colleft ::v-deep .el-tree-node__expand-icon.is-leaf {
    display: none;
  }
</style>
