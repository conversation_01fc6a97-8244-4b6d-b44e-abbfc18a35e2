<template>
  <b-list-wf ref="baseListWf" :buttonsEx="buttonsEx" :isShowDetail="isShowDetail" @bListWfTabClick="bListWfTabClick">
    <template #listTodo>
      <b-list v-show="!isShowDetail" ref="listTodoNotDetail">
        <template #tableRowSlot="{row, column}">
          <slot name="tableRowSlot" :row="row" :column="column"/>
        </template>
      </b-list>
      <b-page v-show="isShowDetail" ref="listTodo" class="listTodoDetail" @baseListBtClick="baseListBtClick">
        <template #dbColLeft>
          <!-- 这个id是为了加css权重 -->
          <div class="tableAuditingContainer">
            <div id="tableAuditingSearch">
              <el-select v-model="searchType" placeholder="请选择" size="mini" class="searchTypeSelect" @change="searchTypeChange">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-select v-model="moneyType" placeholder="请选择" size="mini" class="moneySelect" v-show="showMoneySelect" @change="moneyTypeChange">
                <el-option
                  v-for="item in moneyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-input v-model="search" placeholder="Enter搜索" style="font-size:14px" size="mini" :type="changeInputType" @keyup.enter.native="enterSearch" v-show="isSelect"></el-input>
              <sup-tree
                v-show="showTreeInput"
                ref='classParentTree'
                :setting="treeData.treeSetting"
                :btnSwitch="treeData.btnSwitch"
                :is-get-child="false"
                :nodes="treeData.optionsData"
                :checked-values="[]"
                :modKey="treeData.prop"
                :isFilterData="true"
                placeholder="选择过滤条件"
                :hidePopoverFlag="searchType"
                @getCheckObjs="checkClassParent"
                @handleFilter="handleFilter"
                @clearCheck="resetData">
              </sup-tree>
            </div>
            <!-- <div class="mini-table tableAuditingList" style="height: 100%"> -->
            <div class="mini-table tableAuditingList">
              <el-table
                ref="tableAuditing"
                  :data="tableData"
                @row-click="clickRow"
                :id="tableId"
                @selection-change="tableAuditingListCheck">
                <el-table-column type="selection" width="25" />
                <el-table-column label="编码/类型/环节" width="120">
                  <template slot-scope="{ row }">
                    <div class="auditingInnerRow" :title="row.业务编码">
                      {{ row.业务编码 }}
                    </div>
                    <div class="auditingInnerRow" :title="row.name">
                      {{ row.name }}
                    </div>
                    <div class="auditingInnerRow" :title="row.当前节点">
                      环节: {{ row.当前节点 }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="申请人/时间/金额" width="114">
                  <template slot-scope="{ row }">
                    <div class="auditingInnerRow">{{ row.创建人名称 }}</div>
                    <div class="auditingInnerRow">{{ row.创建日期 }}</div>
                    <div class="auditingInnerRow auditingAmount">
                      [{{ $formatMoney(row.申请金额) }}]
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="其他"></el-table-column>
              </el-table>
            </div>
          </div>
        </template>
        <template #mainContent>
          <div
            id="wf-audit-content"
            :class="`wf-audit-content
                  ${isAuditHasTab? ' wf-audit-content-multiple-tabs' : ''}
                  ${showRegularFormHeader === true? ' showRegularFormHeader': ''}`">
            <div class="wf-audit-content-empty" v-show="isAuditContentEmpty" >
              <img class="empty_img" src="@/assets/image/empty.png" alt="">
              <p class="empty_p">暂无数据</p>
            </div>
            <div class="wf-audit-detail" v-show="!isAuditContentEmpty">
              <b-page ref="basePageWfAuditDetail" @baseListBtClick="baseListBtClick">
                <template #mainContent>
                  <div class="no-detail-content-block" v-show="!showDetailAuditContent">
                    <el-button plain icon="el-icon-caret-left" disabled class="content-message">
                      {{ detailAuditContentMessage }}
                    </el-button>
                  </div>
                  <div class="wf-audit-detail-final" ref="tabsBox" v-show="showDetailAuditContent">
                    <!-- 区块 -->
                    <div style="height: 100%;" v-if="isUsingBlockView">
                      <el-tabs
                        v-model="blockValue"
                        :type="tabsType"
                        class="formAudit-tabs"
                        :id="blockTabsStatic?'':'hiddenTab'"
                        style="height: 100%">
                          <el-tab-pane v-if="blockTabsStatic" label="审核详情" name='details'>
                            <block-view ref="blockViews" v-show="showTabs === 'blockView'"/>
                            <form-canvas ref="againCanvas" v-show="showTabs === 'formCanvas'"/>
                          </el-tab-pane>
                          <el-tab-pane :label="blockTabsStatic ? '单据详情' : '基本信息'" name='first'>
                            <block-view ref="blockView" @modifyTabs="modifyTabs"/>
                          </el-tab-pane>
                        </el-tabs>
                    </div>

                    <component ref="auditDetailComponent" :is="theComponentName" :keyId="billId"
                               v-if="!isUsingBlockView && !isMultipleAuditTabs"/>
                    <el-tabs
                      v-show="wrongBlockTabs"
                      v-model="wrongBlockValue"
                      :type="tabsType"
                      class="formAudit-tabs"
                      :id="blockTabsStatic?'':'hiddenTab'"
                      style="height: 100%"
                      @tab-click="handleBlockTabs">
                          <el-tab-pane v-if="blockTabsStatic" label="审核详情" name='details'>
                            <block-view ref="blockViews" v-show="showTabs === 'blockView'"/>
                            <form-canvas ref="againCanvas" v-show="showTabs === 'formCanvas'"/>
                          </el-tab-pane>
                          <el-tab-pane label="单据详情" name='first'>
                            <el-tabs ref="formAuditTabs"
                             v-model="formAuditTabsActiveName"
                             :type="tabsType"
                             class="formAudit-tabs"
                             style="height: 100%"
                             v-if="!isUsingBlockView && isMultipleAuditTabs"
                             @tab-click="handleTabClick">
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                class="formAuditTab1st" ref="formAuditTab1st" style="height: 100%"
                                :label="formAuditTabLabel0" :name="formAuditTabName0">
                                <component ref="auditDetailComponent" :is="theComponentName" :keyId="billId"/>
                              </el-tab-pane>

                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab1"
                                :label="formAuditTabLabel1"
                                :name="formAuditTabName1"
                                v-if="formAuditTabsVisible1"
                                key="formEditTabLabel1"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent1"
                                    :is="formAuditTabComponentId1"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab2"
                                :label="formAuditTabLabel2"
                                :name="formAuditTabName2"
                                v-if="formAuditTabsVisible2"
                                key="formEditTabLabel2"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent2"
                                    :is="formAuditTabComponentId2"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab3"
                                :label="formAuditTabLabel3"
                                :name="formAuditTabName3"
                                v-if="formAuditTabsVisible3"
                                key="formEditTabLabel3"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent3"
                                    :is="formAuditTabComponentId3"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab4"
                                :label="formAuditTabLabel4"
                                :name="formAuditTabName4"
                                v-if="formAuditTabsVisible4"
                                key="formEditTabLabel4"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent4"
                                    :is="formAuditTabComponentId4"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab5"
                                :label="formAuditTabLabel5"
                                :name="formAuditTabName5"
                                v-if="formAuditTabsVisible5"
                                key="formEditTabLabel5"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent5"
                                    :is="formAuditTabComponentId5"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab6"
                                :label="formAuditTabLabel6"
                                :name="formAuditTabName6"
                                v-if="formAuditTabsVisible6"
                                key="formEditTabLabel6"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent6"
                                    :is="formAuditTabComponentId6"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab7"
                                :label="formAuditTabLabel7"
                                :name="formAuditTabName7"
                                v-if="formAuditTabsVisible7"
                                key="formEditTabLabel7"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent7"
                                    :is="formAuditTabComponentId7"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab8"
                                :label="formAuditTabLabel8"
                                :name="formAuditTabName8"
                                v-if="formAuditTabsVisible8"
                                key="formEditTabLabel8"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent8"
                                    :is="formAuditTabComponentId8"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                              <el-tab-pane
                                :disabled="auditDetailDisabled"
                                ref="formAuditTab9"
                                :label="formAuditTabLabel9"
                                :name="formAuditTabName9"
                                v-if="formAuditTabsVisible9"
                              >
                                <keep-alive>
                                  <component
                                    ref="formAuditTabComponent9"
                                    :is="formAuditTabComponentId9"
                                  />
                                </keep-alive>
                              </el-tab-pane>
                            </el-tabs>
                          </el-tab-pane>
                    </el-tabs>
                  </div>

                  <div class="retract-block retract-block-audit" style="cursor: pointer; top: 39.3%" title="显示审核区域">
                    <i class="el-icon-arrow-left" style="font-size: 13px; cursor: pointer"/>
                  </div>
                </template>
              </b-page>
            </div>
            <div class="wf-audit-right-action mleft-20" v-show="!isAuditContentEmpty">
              <div class="wf-audit-extend">

<!--                <component-->
<!--                  ref="auditExtendComponent"-->
<!--                  :is="auditExtendName"-->
<!--                  :keyId="`auditExtend${billId}`"-->
<!--                  v-show="showAuditExtend"-->
<!--                />-->
                <div
                  class="wf-audit-block"
                  style="height: 100%"
                  id="wf-audit-block"
                >

                  <b-page ref="basePageWfAuditBlock">
                    <template #mainContent>
                      <blockTab
                        ref="blockTabRef"
                        :type="tabsType"
                        :dataType="dataType"
                        :billId="billId"
                        :metaId="metaId"
                        :tabs.sync="blockTabs"
                        @initCallBack="initCallBack"
                        :getAuditTable="getBaseListTable">
                      <div
                        class="wf-audit-block-form"
                        style="
                          height: 350px;
                          border: 1px solid #DDDDDD;
                          padding: 15px 15px 10px 10px;
                          margin-top: 20px;
                          overflow: scroll;
                        "
                      >
                        <audit-comp ref="auditComp" :auditFiles="auditFiles" :isUsingBlockView="isUsingBlockView" @wrapDataAndAction="wrapDataAndAction" @setBlockTabs="setBlockTabs"/>
                      </div>
                      </blockTab>
                    </template>
                  </b-page>
                </div>

              </div>
            </div>
          </div>

          <!-- <wf-useful-list-detail ref="WfUsefulListDetail" @usefulChange="usefulChange" @setUseful="setUseful"/>
          <wf-countersign-userlist ref="WfCountersignUserlist" @conUserListChange="conUserListChange"/> -->
        </template>
      </b-page>
      <audit-review ref="auditReview" />
      <audit-make-bill-missing
        ref="makeBillMissing"
        :useFulTexts="useFulTexts"
        :show="isMakeBillMissing"/>
      <audit-bill-missing-record
        ref="billMissingRecord"
        :show="showBillMissingRecordDlg"
      />

      <base-edit-dlg ref="baseEditDlgObj">
        <template #bizContent>
          <component ref="baseEditDlgObjContent" :is="baseEditDlgObjContentName"/>
        </template>
      </base-edit-dlg>
    </template>
    <template #tableRowSlot="{row, column}">
      <slot name="tableRowSlot" :row="row" :column="column"/>
    </template>
  </b-list-wf>
</template>

<script>
import $ from 'jquery'
import BListWf from './base-list-wf'
import BPage from './base-page'
import BList from './base-list'
import WfUsefulListDetail from '../wf/wf-useful-list-detail'
import WfCountersignUserlist from '../list/wf-countersign-userlist'
import { getTabsType } from '@/utils'
import { mapState } from 'vuex'
import AuditMakeBillMissing from '@/views/billMissing/audit-make-billMissing.vue'
import AuditBillMissingRecord from '@/views/billMissing/audit-billMissing-record.vue'
import BaseEditDlg from './base-edit-dlg'

export default {
  name: 'b-list-wf-audit',
  components: {
    AuditBillMissingRecord,
    AuditMakeBillMissing,
    BaseEditDlg,
    WfCountersignUserlist,
    WfUsefulListDetail,
    BList,
    BPage,
    BListWf
  },
  props: {
    hideTicketButton: {
      default: false,
      type: Boolean
    }
  },
  provide() {
    return {
      currentInstance: () => this.isShowDetail ? this.$refs.listTodo : this.$refs.listTodoNotDetail,
      tableAuditing: () => this.$refs.tableAuditing,
      dataApiKey: () => this.dataApiKey,
      dataType: () => this.dataType,
      syncAuditData: () => this.syncAuditData,
      changeIsShowDetail: (show, row) => this.changeIsShowDetailFunc(show, row),
      todoListAuditReview: () => this.todoListAuditReview(),
      getSaveApiExtraObj: (ids, pass) => this.getSaveApiExtraObj(ids, pass),
      showErrorIfForm: (result) => this.showErrorIfForm(result),
      reLoadAnyway: () => this.reLoadAnyway(),
      getVo4AuditEditSave: () => this.vo4AuditEditSave(),
      showAuditDetailCallback: (show) => this.showAuditDetailCallback(show),
      getBlockTabRef: () => this.$refs.blockTabRef,
      outCheckPatchAudit: (checkedRows, showErrorMessage) => this.checkPatchAudit(checkedRows, showErrorMessage)
    }
  },
  created() {
    this.$onEvent(this, {
      reLoadAnyway: () => {
        this.reLoadAnyway()
      }
    })
  },
  data() {
    return {
      tabsOne: false,
      tabsTow: false,
      oneInfo: {},
      towInfo: {},
      blockValue: 'first',
      tabsValue: '',
      showTabs: '',
      blockTabsStatic: false,
      wrongBlockValue: 'first',
      wrongBlockTabs: false,
      baseEditDlgObjContentName: '',
      isWfBackDialog: false,
      nextAuditUsers: [],
      currAuditUsers: [], // 当前节点审核人加签时使用
      backHisNodes: [],
      usefuls: [],
      useFulTexts: [],
      auditComponentName: '',
      callbackInitAuditDetailComponent: undefined,
      theComponentName: '',
      auditContent: '',
      getVo4AuditEditSave: undefined,
      newDataVoLockVersion: undefined,
      canAuditEdit: false, // 当前是否可以编辑
      billId: '',
      metaId: '',
      auditFiles: [],
      row: {},
      currentRow: undefined,
      showDetailAuditContent: false, // 是否显示详情内容：选中单个审核对象时显示
      showAuditExtend: false, // 是否显示审核项目扩展内容
      auditExtendName: '',
      noAuditSelectedContentMessage: '请选择要审核的项目',
      detailAuditContentMessage: this.noAuditSelectedContentMessage,
      FORM_TYPE_eq: '',
      dataApiKey: '',
      dataType: '',
      dataParams: {},
      tableAuditingList: [],
      dataInited: false, // 是否已查询过审核列表
      reloadTableCallback: undefined,
      showAuditNext: false, // 显示下一环节人
      showAuditCountersign: false, // 显示会签人员
      showAuditAddCountersign: false, // 显示加签人员
      isEnd: false, // 是否结束流程
      countersignNext: '', // 加签会签时存储下一环节人区别下一环节人
      auditCountersignVal: '',
      showAuditSpecify: false, // 显示指定退回
      isUsefuled: false, // 是否显示常用语
      electronicSealMsg: '电子签章认证失败！',
      showElectronicSeal: false, // 是否显示电子签章认证
      ShowElectronicSealSucceed: false, // 是否显示电子签章认证成功
      showRegularFormHeader: false,
      isWebJsCert: false,
      webCertType: '',
      showAuditCustom: true, // 显示审核自定义条件
      showAuditUploadButton: true, // 显示上传审核依据按钮
      auditData: this.makeDefaultAuditData(),
      auditFormDisabled: false,
      syncAuditData: undefined, // 这个方法由外界指定，用于取回节点数据时提供外界使用
      isAuditContentEmpty: true,
      exData: {}, // 用于额外存储数据
      tableId: new Date().getTime() + '',
      buttonsEx: [],
      refreshButtonsDisableDataKey: '',
      refreshButtonsDisableDataParams: {},

      // 如果是审核多tab模式，则formAuditTabs[0]=主表单tab标题，
      // formAuditTabs[x]=其他tab组件名称
      formAuditTabs: [],
      isNoAuditFileOnTab: false,
      isNoAuditFileOnTabAnyway: undefined, // 由其他组件强制设置审核时没有tab附件
      formAuditTabsExcludeMain: [], // 除去第一个表单后的其他的tab的元数据
      formAuditTabsIndexes: {}, // tabKey对应索引映射表
      tabNamesHideByConfig: [],
      auditTabParams: undefined,
      formAuditTabsComponents: {}, // tabKey对应组件映射表
      formAuditTabsObjects: {}, // tabKey对应el-tab-pane对象映射表
      formAuditTabsActiveName: '',
      isShowAuditFileTabWhenInit: false, // 单个审核对象加载时，是否直接显示附件页签
      auditFileTabName: '', // 审核附件页签的名称
      auditFileObjOnRight: undefined, // 审核界面中，右下方位的审核附件组件
      auditFileObjOnTab: undefined, // 审核界面中，在审核主区域页签的审核附件组件
      isNoAuditFileOnRight: false, // 隐藏右侧审核附件区域
      isSelectedMoreThanOneRow: false,
      isUsingCformTab: false,
      isUsingBlockView: false, // 是否使用区块制单展示单据详情
      isMakeBillMissing: false, // 缺票提醒窗口控制
      showBillMissingRecordDlg: false, // 缺票提醒记录窗口控制
      // 审核多tab时，涉及动态设置tab可见性时，vue的双向绑定不能使
      // 用数组实现可见性动态设置，所以使用预置支持10个固定tab的方案
      formAuditTabsVisible0: false,
      formAuditTabsVisible1: false,
      formAuditTabsVisible2: false,
      formAuditTabsVisible3: false,
      formAuditTabsVisible4: false,
      formAuditTabsVisible5: false,
      formAuditTabsVisible6: false,
      formAuditTabsVisible7: false,
      formAuditTabsVisible8: false,
      formAuditTabsVisible9: false,

      formAuditTabComponentId1: '',
      formAuditTabComponentId2: '',
      formAuditTabComponentId3: '',
      formAuditTabComponentId4: '',
      formAuditTabComponentId5: '',
      formAuditTabComponentId6: '',
      formAuditTabComponentId7: '',
      formAuditTabComponentId8: '',
      formAuditTabComponentId9: '',

      formAuditTabName0: '',
      formAuditTabName1: '',
      formAuditTabName2: '',
      formAuditTabName3: '',
      formAuditTabName4: '',
      formAuditTabName5: '',
      formAuditTabName6: '',
      formAuditTabName7: '',
      formAuditTabName8: '',
      formAuditTabName9: '',

      formAuditTabLabel0: '',
      formAuditTabLabel1: '',
      formAuditTabLabel2: '',
      formAuditTabLabel3: '',
      formAuditTabLabel4: '',
      formAuditTabLabel5: '',
      formAuditTabLabel6: '',
      formAuditTabLabel7: '',
      formAuditTabLabel8: '',
      formAuditTabLabel9: '',
      bindEventsNot: true,
      auditAttTab: 'audit-file-tab', // 审核附件组件tab名称 默认只显示附件
      tabsType: '',
      rules: {
        opinion: [
          { required: true, message: '请填写审批意见', trigger: 'blur' }
        ]
      },
      // 新增搜索组件需要的数据 ===start
      options: [
        {
          value: '业务编码',
          label: '单据编码'
        },
        {
          value: '申请金额',
          label: '金额'
        },
        // {
        //   value: '用途/摘要',
        //   label: '用途/摘要'
        // },
        {
          value: '创建人ID',
          label: '申请人'
        },
        {
          value: '创建部门编码',
          label: '申请部门'
        }
      ],
      search: '',
      searchType: '业务编码',
      moneyOptions: [
        {
          value: 'gte',
          label: '>='
        },
        {
          value: 'lte',
          label: '<='
        },
        {
          value: 'eq',
          label: '='
        }
      ],
      moneyType: 'gte',
      isSearch: false,
      searchData: [],
      showTreeInput: false,
      treeData: {},
      checkIds: [],
      // 新增搜索组件需要的数据 ===end,
      status: '',
      blockTabs: [],
      isShowDetail: false,
      todoListcheckedRows: [],
      auditDetailParams: {},
      auditDetailDisabled: true,
      auditDetailButtons: [] // 审核详情按钮
    }
  },
  computed: {
    ...mapState({ applicant: (store) => store.user.applicant, applicationDept: (store) => store.user.applicationDept }),
    isSelect() {
      return ['业务编码', '申请金额'].includes(this.searchType)
    },
    showMoneySelect() {
      return this.searchType === '申请金额'
    },
    tableData() {
      return this.isSearch ? this.searchData : this.tableAuditingList
    },
    changeInputType() {
      return this.searchType === '申请金额' ? 'number' : 'text'
    },
    isAuditHasTab() {
      // 审核区域是否有多tab：普通多tab，或者是CformTab机制
      return this.isUsingCformTab ||
        (this.isMultipleAuditTabs && !this.isSelectedMoreThanOneRow)
    },
    isMultipleAuditTabs() {
      return !this.isUsingCformTab &&
        (this.formAuditTabs !== undefined && this.formAuditTabs.length > 1)
    },
    auditBlockHeight() {
      var maxHeight = 315 // 2022/04/29-huyujia
      if (!this.showAuditCustom) {
        maxHeight -= 37
      }
      if (!this.showAuditNext && !this.showAuditSpecify) {
        maxHeight -= 37
      }
      if (!this.showAuditCountersign) {
        maxHeight -= 37
      }
      return maxHeight
    },
    getTable() {
      return this.isShowDetail ? this.$refs.tableAuditing : this.$refs.listTodoNotDetail.getTable()
    }
  },
  destroyed() {
    window.sessionStorage.removeItem(this.$route.path.split('/').pop() + '_pathParams')
  },
  deactivated() {
    window.sessionStorage.removeItem(this.$route.path.split('/').pop() + '_pathParams')
  },
  mounted() {
    // 如果父组件实现了initParams方法，自动初始化非表单业务审核列表
    if (this.isNotCformBiz()) {
      const initParams = this.$parent.initParams()
      if (this.$isEmpty(initParams)) {
        this.$message.error('initParams方法返回空数据')
        return
      }
      this.init(initParams)
    } else {
      this.tabsType = getTabsType(this.$refs.tabsBox)
    }
  },
  methods: {
    init(initParams) {
      // 初始化时请求系统人员和部门 用于申请人和申请部门的下拉数据
      this.getApplicationData()
      if (initParams.codeSearch) {
        this.search = initParams.codeSearch
      }
      this.$parent.isListObj = true

      // 如果是第一次初始之后，点击tab切换页签，此时已经有一个保存的initParams
      // 这个保存的initParams可能存储了额外的信息，需要将这些信息传递给后续的过程
      var $root = this.$getObjRoot(this)
      var initParamsSaved = this.$getInitParams($root)
      if (this.$isNotEmpty(initParamsSaved)) {
        initParams = Object.assign(initParamsSaved, initParams)
      }

      this.refreshButtonsDisableDataKey = initParams.refreshButtonsDisableDataKey
      this.refreshButtonsDisableDataParams = initParams.refreshButtonsDisableDataParams
      this.auditComponentName = initParams.auditComponentName
      this.callbackInitAuditDetailComponent = initParams.callbackInitAuditDetailComponent
      if (!initParams.noNeedAuditDetail) {
        if (this.$isEmpty(this.auditComponentName) && !this.isNotCformBiz()) {
          this.$message.error('审核待办页面时，initParams.auditComponentName不能为空')
          return
        }

        if (typeof this.callbackInitAuditDetailComponent !== 'function' && !this.isNotCformBiz()) {
          this.$message.error('审核待办页面时，initParams.callbackInitAuditDetailComponent必须是函数')
          return
        }
      }

      // 审核业务这两个值固化
      initParams.isApply = false
      initParams.supportProcess = true
      this.isNoAuditFileOnRight = false
      this.isNoAuditFileOnTabAnyway = initParams.isNoAuditFileOnTabAnyway
      this.showRegularFormHeader = initParams.showRegularFormHeader
      this.reloadTableCallback = initParams.reloadTableCallback
      // 缺票提醒首页跳转到报销审核详情问题
      const query = this.$getPathQuery()
      if (this.$isNotEmpty(query)) {
        const reloadTableCallback = initParams.reloadTableCallback
        initParams.reloadTableCallback = (result, table, baseList) => {
          // 获取提跳转参数
          const rows = result.data?.rows || []
          const pathQuery = this.$getPathQuery()
          if (rows.length && this.$isNotEmpty(pathQuery)) {
            this.changeIsShowDetailFunc(true, rows[0])
          }
          // 删除提跳转参数
          this.$removePathQuery()
          reloadTableCallback?.(result, table, baseList)
        }
      }

      this.auditContent = initParams.auditContent

      initParams.rowCheckedCallback = (rows) => {
        this.rowCheckedCallback(rows)
      }

      initParams.params.needProcessParams =
        initParams.params.needProcessParams || {}
      initParams.params.needProcessParams['isAudit'] = true
      var needProcessCallBack = initParams.needProcessCallBack
      initParams.needProcessCallBack = (result) => {
        if (needProcessCallBack) {
          needProcessCallBack(result, initParams)
        }

        // 由后端返回是否审核附件使用页签形式
        this.isNoAuditFileOnTab = (result.attributes['审核附件不使用页签'] === true)
        if (this.isNoAuditFileOnTabAnyway) {
          this.isNoAuditFileOnTab = true
        }

        this.isShowAuditFileTabWhenInit =
          result.attributes['审核附件页签优先显示'] === true

        this.isNoAuditFileOnRight =
          result.attributes['隐藏右侧审核附件'] === true

        this.initAuditTabs(initParams)
        this.setIsNoAuditFileOnRight()
      }

      // initParams.showTree = false // 审核列表默认不显示左侧，“待办”会单独设置显示左侧
      initParams.params = initParams.params || {}
      if (this.$isEmpty(this.buttonsEx)) {
        this.buttonsEx = initParams.buttons || [] // 记录外界设置的额外按钮
      }
      this.dataType = initParams.params.dataType
      if (this.dataType === 'CformDataEntity') { // 是表单时才增加
        this.FORM_TYPE_eq = initParams.params.FORM_TYPE_eq
      }
      this.dataParams = initParams.params
      this.getVo4AuditEditSave = initParams.getVo4AuditEditSave
      this.$saveInitParams(this, initParams)

      var isNotTabTodo = initParams.isNotTabTodo || false
      // 如果是补录审核页面, 按钮就不置空
      if (!initParams.fileBlAudit) {
        if (!isNotTabTodo) {
          // 审核待办时，主工具类没有按钮，注意主工具栏不是审核详情工具栏
          initParams.buttons = []
        } else {
          initParams.buttons = this.buttonsEx
        }
      }

      // 处理额外隐藏的按钮，比如非表单业务时，必须隐藏打印按钮
      let hiddenBtsEx = []
      if (this.isNotCformBiz()) {
        hiddenBtsEx = hiddenBtsEx.concat(this.getNotCformHiddenBts())
      }
      initParams.hiddenButtons = initParams.hiddenButtons || []
      initParams.hiddenButtons = initParams.hiddenButtons.concat(hiddenBtsEx)
      this.wrapBtDetailClick(initParams)
      this.$saveInitParams(this, initParams)
      this.$saveInitParams(this.$parent, initParams)
      this.$refs.baseListWf.init(initParams)

      // 可能baseListWf.init方法设置了dataApiKey，所以这个值需要在init之后设置
      this.dataApiKey = initParams.params.dataApiKey
      if (!isNotTabTodo) {
        // 设审核右侧的tab数据
        if (initParams.setRightBlockTab) {
          this.blockTabs = initParams.setRightBlockTab(this.blockTabs)
        }
        this.$nextTick(() => {
          var buttons = [
            {
              text: '返回',
              icon: 'el-icon-d-arrow-left',
              enabledType: '0',
              click: (bt) => this.changeIsShowDetailFunc(false)
            },
            {
              text: '保存修改',
              icon: 'el-icon-edit',
              enabledType: '1',
              click: (bt) => this.doSaveWhenAudit()
            },
            {
              text: '缺票提醒',
              enabledType: '1',
              dropDowns: [
                {
                  text: '缺票提醒',
                  icon: 'el-icon-edit',
                  enabledType: '1',
                  click: (bt) => this.doBillMissing()
                },
                {
                  text: '缺票提醒记录',
                  icon: 'el-icon-edit',
                  enabledType: '1',
                  click: (bt) => this.doBissMissingRecord()
                }
              ]
            },

            {
              text: '打印',
              icon: 'el-icon-printer',
              enabledType: '1',
              click: (bt) => this.btPrint(initParams, bt)
            },
            {
              text: '导出PDF',
              icon: 'el-icon-back',
              enabledType: '1',
              click: (bt) => this.btExportPdf(initParams, bt)
            },
            {
              text: '作废',
              icon: 'el-icon-document-delete',
              enabledType: '1+',
              click: (bt) => this.cancelBills()
            },
            {
              text: '导出',
              icon: 'el-icon-aliiconxiazai',
              enabledType: '0',
              click: (bt) => this.exportExcel()
            },
            {
              text: '审核历史',
              icon: 'el-icon-time',
              enabledType: '1',
              click: (bt) => this.btAuditHistory()
            }
          ]
          this.buttonsEx.forEach((bt) => {
            if (this.$isEmpty(bt.show) || bt.show?.includes('待办')) {
              buttons.push(bt)
            }
          })
          if (initParams.params.hasOwnProperty('isPrintButton')) {
            if (!initParams.params.isPrintButton) {
              buttons.splice(1, 1)
            }
          }
          var auditDetailParams = {
            hiddenButtons: ['保存修改'],
            buttons: buttons
          }
          if (this.isNotCformBiz()) { // 非标单业务不需要通用的打印和导出PDF
            auditDetailParams.hiddenButtons =
              auditDetailParams.hiddenButtons.concat(hiddenBtsEx)
          }
          this.auditDetailParams = auditDetailParams

          this.$refs.basePageWfAuditDetail.init(auditDetailParams)
          this.$refs.listTodo.setButtonNormalNoPaddingTop(true)
          this.refreshButtonsDisableData([])
          this.$refs.basePageWfAuditDetail.setButtonBarVisible(true)
          let hideBtn = () => {}
          this.$nextTick(() => {
            if (this.bindEventsNot) {
              $('.retract-block-audit').click(() => {
                $('.wf-audit-right-action').toggleClass(
                  'wf-audit-right-action-fold'
                )
                $('.wf-audit-right-action').toggleClass(
                  'mleft-20'
                )
                $('.retract-block-audit').hide()
                this.$nextTick(hideBtn)
              })
              this.bindEventsNot = false
            }
          })

          this.$refs.basePageWfAuditBlock.init({
            buttons: [
              {
                text: '隐藏',
                // icon: 'el-icon-caret-right',
                enabledType: '0',
                click: (bt) => {
                  $('.wf-audit-right-action').toggleClass(
                    'wf-audit-right-action-fold'
                  )
                  $('.wf-audit-right-action').toggleClass(
                    'mleft-20'
                  )
                  $('.retract-block-audit').show()
                  bt.visible = false
                  hideBtn = () => {
                    bt.visible = true
                  }
                }
              }
              // , { text: '加签', icon: 'el-icon-plus', enabledType: '1', click: bt => {} },
              // { text: '汇签', icon: 'el-icon-d-arrow-left', enabledType: '1', click: bt => {} },
              // { text: '会审', icon: 'el-icon-finished', enabledType: '1', click: bt => {} },
              // { text: '协审', icon: 'el-icon-chat-line-square', enabledType: '1', click: bt => {} },
              // { text: '退回修改', icon: 'el-icon-edit', enabledType: '1+', click: bt => {} }
            ]
          })
          this.$refs.basePageWfAuditBlock.setButtonBarVisible(true)
          this.reLoad(true)
        })
      }
      // 浏览器兼容
      this.$nextTick(() => {
        this.$browserCompatibility('b-list-wf-audit')
      })
    },
    // 控制区块单tabs显示隐藏
    modifyTabs(flag, data) {
      this.blockTabsStatic = flag
      this.blockValue = this.blockTabsStatic ? 'details' : 'first'
      if (flag) {
        this.tabsOne = !!data.data.colItems
        this.oneInfo = data.data
        this.handleTabs(data)
      }
    },
    // 调用接口
    handleTabs(data) {
      const apikey = 'specialAuditSctreen&isBlockView=' + data.attributes.isBlockView + '&auditScreenMetaId=' + data.attributes.auditScreenMetaId
      this.$callApi(apikey, {
        ...data.data
      },
      result => {
        if (result.attributes.specialAudit.containers) {
          this.tabsTow = false
          this.showTabs = 'blockView'
          const params = {
            jumpToSaveFormData: {
              cFormVo: result.attributes.specialAudit
            },
            isApply: false,
            isEdit: false,
            mode: '详情'
          }
          this.$nextTick(() => {
            this.$refs.blockViews.init(undefined, params)
          })
        } else if (result.attributes.specialAudit.colItems) {
          this.tabsTow = true
          this.towInfo = result.attributes.specialAudit
          this.showTabs = 'formCanvas'
          this.$nextTick(() => {
            this.$refs.againCanvas.initByDataVo(
              result.attributes.specialAudit,
              '详情',
              undefined, undefined,
              undefined, undefined)
          })
        }
        return true
      })
    },
    // 区块tabs点击事件
    handleBlockTabs() {
      if (!this.wrongBlockTabs) {
        return
      }
      if (this.tabsTow && this.tabsOne) {
        window.luckysheet.destroy()
        this.$nextTick(() => {
          this.$refs.againCanvas.initByDataVo(
            this.wrongBlockValue === 'details' ? this.towInfo : this.oneInfo,
            '详情',
            undefined, undefined,
            undefined, undefined)
        })
      }
    },
    saveButton() {
      return this.$isNotEmpty(this.auditDetailParams.buttons) ? this.auditDetailParams.buttons : []
    },
    vo4AuditEditSave() {
      if (typeof this.getVo4AuditEditSave !== 'function') {
        this.$message.error('this.getVo4AuditEditSave 必须是函数')
        return
      }
      return this.getVo4AuditEditSave(this)
    },
    btExportPdf(initParams, bt) {
      initParams.button = '导出Pdf'
      var row = bt.params.rows[0]
      initParams.btExportWps.click(row, row, initParams)
    },
    btPrint(initParams, bt) {
      // if (initParams.printTemplateFormType) {
      //   this.$showPrint(bt.params.rows[0], initParams.printTemplateFormType)
      // } else {
      //   initParams.btPrint.click(bt.params.rows[0])
      // }
      initParams.button = '打印'
      var row = bt.params.rows[0]
      if (initParams.printTemplateFormType) {
        row.formType = initParams.printTemplateFormType
      }
      initParams.btExportWps.click(row, row, initParams)
    },
    getBaseListTable() {
      // 返回表格对象
      return this.$refs.tableAuditing
    },
    initAuditTabs(initParams) {
      this.formAuditTabs = initParams.formAuditTabs || []
      if (this.isNotCformBiz()) { // 非表单业务不需要通用的tab机制
        return
      }

      if (initParams.params.FORM_TYPE_eq === '报销单') { // 报销单显示附件和发票
        this.auditAttTab = 'audit-file-invoice-tab'
      }

      if (this.isNoAuditFileOnTab === false) {
        if (this.$isEmpty(this.formAuditTabs)) {
          this.formAuditTabs.push('基本信息')
        }
        this.formAuditTabs.push(this.auditAttTab + ':附件')
      }

      if (initParams.useFormDetailsAsAuditTabs === true) {
        // 使用表单详情tab作为审核多tab，如果设置审核附件使用tab，
        // 则审核附件会代替详情中的附件tab
        var tabsFromDetailDlg = this.$resolveFormAuditTabs(
          initParams.params.FORM_TYPE_eq)
        if (this.isNoAuditFileOnTab === false) {
          this.formAuditTabs = []
          tabsFromDetailDlg.forEach((tab) => {
            if (tab.indexOf(':附件') < 0) {
              this.formAuditTabs.push(tab)
            } else {
              this.formAuditTabs.push(this.auditAttTab + ':附件')
            }
          })
        } else {
          this.formAuditTabs = tabsFromDetailDlg
        }
      }

      this.formAuditTabsExcludeMain = []
      this.formAuditTabsIndexes = {}
      if (this.isMultipleAuditTabs) {
        // 多页签审核时，设置页签索引与名称、标题的对应关系
        this.formAuditTabsIndexes[this.formAuditTabs[0]] = 0
        var keyNoIndex = this.$resolveTabNameNoIndex(this.formAuditTabs[0])
        this.formAuditTabsIndexes[keyNoIndex] = 0
        this.setAuditTabNameLabelIndex(
          0,
          this.formAuditTabs[0],
          this.formAuditTabs[0]
        )
        this.setAuditTabVisibleIndex(0, true)

        this.auditFileTabName = ''
        for (let i = 1; i < this.formAuditTabs.length; i++) {
          var tabName = this.formAuditTabs[i]
          var tabLabel = tabName
          if (tabName.indexOf(':') > -1) {
            var tabTokens = tabName.split(':')
            tabName = tabTokens[0]
            tabLabel = tabTokens[1]
          }
          this.formAuditTabsExcludeMain.push({
            name: tabName,
            label: tabLabel
          })
          this.formAuditTabsIndexes[tabName] = i
          var tabNameNoIndex = this.$resolveTabNameNoIndex(tabName)
          this.formAuditTabsIndexes[tabNameNoIndex] = i
          if (tabLabel === '附件') {
            // 审核附件页签的label固定是“附件”
            this.auditFileTabName = tabName
          }

          this.setAuditTabNameLabelIndex(i, tabName, tabLabel)
          this.setAuditTabVisibleIndex(i, true)
        }

        // 设置初始时显示哪个页签
        this.setFormAuditTabsActiveName()
      }
    },
    setFormAuditTabsActiveName() { // 初始化时设置显示的页签
      var activeTab = this.formAuditTabName0
      if (this.isShowAuditFileTabWhenInit &&
        this.$isNotEmpty(this.auditFileTabName)) {
        activeTab = this.auditFileTabName
      }
      this.formAuditTabsActiveName = activeTab
    },
    setAuditTabVisibleIndex(index, isVisible) {
      // 多tab审核时设置页签的可见性
      if (index !== undefined) {
        // vue双向绑定问题，只能这样穷举处理
        if (index === 0 && this.formAuditTabsVisible0 !== isVisible) {
          this.formAuditTabsVisible0 = isVisible
          return true
        }

        if (index === 1 && this.formAuditTabsVisible1 !== isVisible) {
          this.formAuditTabsVisible1 = isVisible
          return true
        }

        if (index === 2 && this.formAuditTabsVisible2 !== isVisible) {
          this.formAuditTabsVisible2 = isVisible
          return true
        }

        if (index === 3 && this.formAuditTabsVisible3 !== isVisible) {
          this.formAuditTabsVisible3 = isVisible
          return true
        }

        if (index === 4 && this.formAuditTabsVisible4 !== isVisible) {
          this.formAuditTabsVisible4 = isVisible
          return true
        }

        if (index === 5 && this.formAuditTabsVisible5 !== isVisible) {
          this.formAuditTabsVisible5 = isVisible
          return true
        }

        if (index === 6 && this.formAuditTabsVisible6 !== isVisible) {
          this.formAuditTabsVisible6 = isVisible
          return true
        }

        if (index === 7 && this.formAuditTabsVisible7 !== isVisible) {
          this.formAuditTabsVisible7 = isVisible
          return true
        }

        if (index === 8 && this.formAuditTabsVisible8 !== isVisible) {
          this.formAuditTabsVisible8 = isVisible
          return true
        }

        if (index === 9 && this.formAuditTabsVisible9 !== isVisible) {
          this.formAuditTabsVisible9 = isVisible
          return true
        }
      }
    },
    setAuditTabNameLabelIndex(index, name, label) {
      // 多tab审核时设置页签的name和label
      if (index !== undefined) {
        // vue双向绑定有问题，只能这样穷举处理
        if (index === 0) this.formAuditTabName0 = name
        if (index === 1) this.formAuditTabName1 = name
        if (index === 2) this.formAuditTabName2 = name
        if (index === 3) this.formAuditTabName3 = name
        if (index === 4) this.formAuditTabName4 = name
        if (index === 5) this.formAuditTabName5 = name
        if (index === 6) this.formAuditTabName6 = name
        if (index === 7) this.formAuditTabName7 = name
        if (index === 8) this.formAuditTabName8 = name
        if (index === 9) this.formAuditTabName9 = name

        if (index === 0) this.formAuditTabLabel0 = label
        if (index === 1) this.formAuditTabLabel1 = label
        if (index === 2) this.formAuditTabLabel2 = label
        if (index === 3) this.formAuditTabLabel3 = label
        if (index === 4) this.formAuditTabLabel4 = label
        if (index === 5) this.formAuditTabLabel5 = label
        if (index === 6) this.formAuditTabLabel6 = label
        if (index === 7) this.formAuditTabLabel7 = label
        if (index === 8) this.formAuditTabLabel8 = label
        if (index === 9) this.formAuditTabLabel9 = label

        if (index === 1) this.formAuditTabComponentId1 = name
        if (index === 2) this.formAuditTabComponentId2 = name
        if (index === 3) this.formAuditTabComponentId3 = name
        if (index === 4) this.formAuditTabComponentId4 = name
        if (index === 5) this.formAuditTabComponentId5 = name
        if (index === 6) this.formAuditTabComponentId6 = name
        if (index === 7) this.formAuditTabComponentId7 = name
        if (index === 8) this.formAuditTabComponentId8 = name
        if (index === 9) this.formAuditTabComponentId9 = name
      }
    },
    setAuditTabLabelBayName(name, label) {
      // vue双向绑定有问题，只能这样穷举处理
      if (this.formAuditTabName0 === name) this.formAuditTabLabel0 = label
      if (this.formAuditTabName1 === name) this.formAuditTabLabel1 = label
      if (this.formAuditTabName2 === name) this.formAuditTabLabel2 = label
      if (this.formAuditTabName3 === name) this.formAuditTabLabel3 = label
      if (this.formAuditTabName4 === name) this.formAuditTabLabel4 = label
      if (this.formAuditTabName5 === name) this.formAuditTabLabel5 = label
      if (this.formAuditTabName6 === name) this.formAuditTabLabel6 = label
      if (this.formAuditTabName7 === name) this.formAuditTabLabel7 = label
      if (this.formAuditTabName8 === name) this.formAuditTabLabel8 = label
      if (this.formAuditTabName9 === name) this.formAuditTabLabel9 = label
    },
    setEditTabVisible(tabKey, isVisible, isByConfig) {
      // 如果是系统参数已经隐藏的tab，则不再响应setEditTabVisible的处理
      if (this.tabNamesHideByConfig.indexOf(tabKey) > -1) {
        return
      }

      // 记录系统参数决定隐藏的tab
      if (isVisible === false && isByConfig === true) {
        this.tabNamesHideByConfig.push(tabKey)
      }
      // 多tab制单时设置页签的可见性
      // 注意：第一个页签的tabKey与标题相同，除第一个之外，tabKey=页签的组件name
      var index = this.formAuditTabsIndexes[tabKey]
      var isHit = this.setEditTabVisibleIndex(index, isVisible)
      if (isHit === true && isVisible === true) {
        this.$nextTick(() => {
          var tabComp = this.$refs[`formAuditTabComponent${index}`]
          if (tabComp && this.auditTabParams) {
            tabComp.init(undefined, this.auditTabParams)
          }
        })
      }
    },
    setEditTabVisibleIndex(index, isVisible) {
      // 多tab制单时设置页签的可见性
      if (index !== undefined) {
        if (index === 0) {
          this.$message.error('不能隐藏主表单页签')
          return
        }

        // vue双向绑定问题，只能这样穷举处理
        if (index === 1 && this.formAuditTabsVisible1 !== isVisible) {
          this.formAuditTabsVisible1 = isVisible
          return true
        }

        if (index === 2 && this.formAuditTabsVisible2 !== isVisible) {
          this.formAuditTabsVisible2 = isVisible
          return true
        }

        if (index === 3 && this.formAuditTabsVisible3 !== isVisible) {
          this.formAuditTabsVisible3 = isVisible
          return true
        }

        if (index === 4 && this.formAuditTabsVisible4 !== isVisible) {
          this.formAuditTabsVisible4 = isVisible
          return true
        }

        if (index === 5 && this.formAuditTabsVisible5 !== isVisible) {
          this.formAuditTabsVisible5 = isVisible
          return true
        }

        if (index === 6 && this.formAuditTabsVisible6 !== isVisible) {
          this.formAuditTabsVisible6 = isVisible
          return true
        }

        if (index === 7 && this.formAuditTabsVisible7 !== isVisible) {
          this.formAuditTabsVisible7 = isVisible
          return true
        }

        if (index === 8 && this.formAuditTabsVisible8 !== isVisible) {
          this.formAuditTabsVisible8 = isVisible
          return true
        }

        if (index === 9 && this.formAuditTabsVisible9 !== isVisible) {
          this.formAuditTabsVisible9 = isVisible
          return true
        }
      }
    },
    checkPatchAudit(checkedRows, showErrorMessage) {
      // 检查是否可以批量审核
      var checkResult = true
      checkedRows =
        checkedRows || this.$getTableSelection(this.getTable)
      if (checkedRows.length > 1) {
        var currentNodeId
        for (var i = 0; i < checkedRows.length; i++) {
          if (!currentNodeId) {
            currentNodeId = checkedRows[i].currentNodeId
          } else if (checkedRows[i].currentNodeId !== currentNodeId) {
            checkResult = false
            if (showErrorMessage) {
              this.$message.error('不同业务或不同节点不能进行批量操作')
            }
            break
          }
        }
      }
      return checkResult
    },
    cancelBills() {
      var checkedRows = this.$getTableSelection(this.getTable)
      if (this.checkPatchAudit(checkedRows, true)) {
        var params = {
          ids: this.$getTableCheckedIdsStrBy(checkedRows),
          dataType: this.dataType
        }

        var message = `确认要作废 ${checkedRows.length} 条数据吗？`
        this.$callApiParamsConfirm(
          message,
          null,
          'WFCANCEL',
          params,
          (result) => {
            if (this.isShowDetail) {
              this.reLoadAnyway()
            } else {
              this.showAuditList()
            }
            this.$refreshCount(this)
          }
        )
      }
    },
    exportExcel() {
      var params = this.getReloadListParams()
      params.exportExcelName = '审核列表.xls'
      params.sheetList = this.dataParams.sheetList
      params.formType = this.dataParams.formType
      this.$excelExport(this.dataApiKey, this.$refs.tableAuditing, params)
    },
    btAuditHistory() {
      this.$showWfHistory(
        this.$getTableSelection(this.getTable)[0].id,
        null,
        null,
        this.dataApiKey
      )
    },
    baseListBtClick(button) {
      button.params.rows = this.$getTableSelection(this.$refs.tableAuditing)
    },
    tableAuditingListCheck() {
      // 刷新审核内容
      var rows = this.$getTableSelection(this.$refs.tableAuditing)
      this.$refs.basePageWfAuditDetail.rowChecked(
        rows,
        this.$isEmpty(this.tableData)
      )
      this.$refs.basePageWfAuditBlock.rowChecked(
        rows,
        this.$isEmpty(this.tableData)
      )

      this.isSelectedMoreThanOneRow = false
      this.isAuditContentEmpty = rows.length === 0
      this.setAuditTabLabelBayName(this.auditAttTab, '附件')
      if (rows.length === 1) {
        this.showDetailAuditContent = true
        this.billId = rows[0].id
        this.metaId = rows[0].metaId
        this.currentRow = rows[0]
        this.row = rows[0]
        this.formAuditTabsComponents = {}

        var viewId = rows[0].viewId
        this.isUsingBlockView = this.$isNotEmpty(viewId)
        if (this.isUsingBlockView) {
          this.canAuditEdit = false
          this.setAuditBtVisible('保存修改', false)
          this.$refs.basePageWfAuditDetail.setBtProperty('打印', 'disabled', rows[0].isPrint === '否')
          var $root = this.$getObjRoot(this)
          var initParamsSaved = this.$getInitParams($root)
          var params = {
            isApply: false,
            isEdit: true,
            isAuditMode: true,
            mode: '审核',
            dataId: this.billId,
            viewId: viewId,
            isShowTabs: initParamsSaved?.isShowTabs || false,
            callbackAfterFormLoaded: (dataVo) => {
              if (this.$isNotEmpty(dataVo.extData['审核编辑数据'])) {
                this.canAuditEdit = true
                this.setAuditBtVisible('保存修改', true)
              }
            }
          }
          this.$nextTick(() => { this.$refs.blockView.init(undefined, params) })
        } else {
          // 可设置auditComponentName为函数，
          // 动态获取审核组件名称之后再回调加载详情组件
          var callbackAfterComponentName = (componentName) => {
            this.setAuditBtVisible('保存修改', false)
            this.$refs.basePageWfAuditDetail.setBtProperty('打印', 'disabled', rows[0].isPrint === '否')
            this.theComponentName = componentName
            this.$nextTick(() => {
              if (this.isNotCformBiz()) { // 非表单业务处理
                this.showNotCformBiz(rows[0])
              } else {
                this.callbackInitAuditDetailComponent(
                  this,
                  (auditExtendName, exData, specialAudit) => {
                    // dataVo是新查询出来的表单数据
                    exData = exData || {}
                    var auditTabParams = exData.auditTabParams || {}
                    var dataVo = auditTabParams.dataVo

                    this.tabNamesHideByConfig = []
                    this.canAuditEdit = false
                    // specialAudit接口传递过来的tabs页数据
                    if (specialAudit?.attributes.auditScreenMetaId) {
                      this.tabsOne = true
                      this.oneInfo = specialAudit.data
                      this.blockTabsStatic = true
                      this.wrongBlockTabs = true
                      this.wrongBlockValue = 'details'
                      this.handleTabs(specialAudit)
                    } else {
                      this.blockTabsStatic = false
                      this.wrongBlockTabs = true
                      this.wrongBlockValue = 'first'
                    }
                    if (dataVo) {
                      dataVo.extData.tabNames = this.formAuditTabs
                      dataVo.extData.setEditTabVisible = this.setEditTabVisible
                      dataVo.extData.setEditTabLabel = this.setAuditTabLabelBayName
                      this.$setDetailDlgAndAuditTabVisible(dataVo)
                      if (this.$isNotEmpty(dataVo.extData['审核编辑数据'])) {
                        this.canAuditEdit = true
                        this.setAuditBtVisible('保存修改', true)
                      }
                    }

                    if (this.$isNotEmpty(auditExtendName) &&
                      this.$isNotEmpty(window.$viewNames[auditExtendName])) {
                      // 表明当前环境有审核扩展组件
                      this.showAuditExtend = true
                      this.auditExtendName = auditExtendName
                      // this.$refs.auditDetailComponent.nodeName =
                      // this.auditData.nodeName
                      this.$refs.auditDetailComponent.nodeName = this.$refs.auditComp.auditData.nodeName
                      this.$nextTick(() => {
                        if (
                          typeof this.$refs.auditExtendComponent.init === 'function'
                        ) {
                          if (dataVo) {
                            this.$refs.auditDetailComponent.dataVo = dataVo
                          }
                          this.$refs.auditExtendComponent.init(
                            this.$refs.auditDetailComponent,
                            this.showAuditUploadButton,
                            exData)
                          this.setIsNoAuditFileOnRight()

                          // 获取auditFileObjOnRight，使其auditFileObjOnTab关联
                          this.$nextTick(() => {
                            var refObjs = this.$refs.auditExtendComponent.$refs
                            if (this.$isNotEmpty(refObjs)) {
                              var refObjsKeys = Object.keys(refObjs)
                              refObjsKeys.forEach((k) => {
                                var obj = refObjs[k]
                                if (this.$getComponentName(obj) === 'attach-audit-extend') {
                                  this.auditFileObjOnRight = obj
                                  if (this.$isNotEmpty(this.auditFileObjOnTab)) {
                                    this.auditFileObjOnTab.anotherAuditFileObj =
                                      this.auditFileObjOnRight
                                    this.auditFileObjOnRight.anotherAuditFileObj =
                                      this.auditFileObjOnTab
                                  }
                                }
                              })
                            }
                          })
                        }
                      })
                    } else {
                      this.showAuditExtend = false
                      this.auditExtendName = ''
                    }

                    // 处理多tab审核的初始化
                    if (this.isMultipleAuditTabs) {
                      var tname
                      this.formAuditTabsComponents[this.formAuditTabs[0]] =
                        this.$refs.auditDetailComponent

                      for (let index = 0;
                        index < this.formAuditTabsExcludeMain.length; index++) {
                        tname = this.formAuditTabsExcludeMain[index].name
                        this.formAuditTabsComponents[tname] =
                          this.$refs[`formAuditTabComponent${index + 1}`]
                      }

                      // 初始化除主界面之外的其他页签
                      for (let i = 0; i < this.formAuditTabsExcludeMain.length; i++) {
                        tname = this.formAuditTabsExcludeMain[i].name
                        var tabComp = this.formAuditTabsComponents[tname]
                        if (tabComp && tabComp.init) {
                          // 审核时页签使用init，与制单多页签有区别，是由于审核的页签
                          // 可以直接使用业务详情弹框上的页签，而这些页签的方法之前已经是init
                          // 所以此处使用这些页签需要保持与详情使用兼容
                          // auditTabParams.nodeName = this.auditData.nodeName
                          auditTabParams.nodeName = this.$refs.auditComp.auditData.nodeName
                          tabComp.init(undefined, auditTabParams, this.$refs.auditDetailComponent)
                          if (dataVo) {
                            // 记录这个对象，以便在setEditTabVisible中使用
                            this.auditTabParams = auditTabParams
                          }

                          // 获取auditFileObjOnTab，使其auditFileObjOnRight关联
                          var tabLabel = this.formAuditTabsExcludeMain[i].label
                          if (tabLabel === '附件') {
                            // 获取auditFileObjOnTab
                            this.$nextTick(() => {
                              if (tabComp) {
                                var refObjs = tabComp.$refs
                                if (this.$isNotEmpty(refObjs)) {
                                  var refObjsKeys = Object.keys(refObjs)
                                  refObjsKeys.forEach((k) => {
                                    var obj = refObjs[k]
                                    if (this.$getComponentName(obj) === 'attach-audit-extend') {
                                      this.auditFileObjOnTab = obj
                                      if (this.$isNotEmpty(this.auditFileObjOnRight)) {
                                        this.auditFileObjOnTab.anotherAuditFileObj =
                                          this.auditFileObjOnRight
                                        this.auditFileObjOnRight.anotherAuditFileObj =
                                          this.auditFileObjOnTab
                                      }
                                    }
                                  })
                                }
                              }
                            })
                          }
                        }
                      }
                      this.setFormAuditTabsActiveName()
                    }
                  }
                )
              }
            })
          }

          if (typeof this.auditComponentName === 'function') {
            var newName = this.auditComponentName(this, callbackAfterComponentName)
            if (typeof newName === 'function') {
              this.theComponentName = newName
            }
          } else {
            callbackAfterComponentName(this.auditComponentName)
          }
        }
      } else {
        this.isSelectedMoreThanOneRow = true
        this.showDetailAuditContent = false
        this.billId = ''
        this.theComponentName = ''

        this.showAuditExtend = false
        this.auditExtendName = ''

        this.detailAuditContentMessage =
          rows.length > 1
            ? `已选择 ${rows.length} 个审核项目`
            : this.noAuditSelectedContentMessage
      }

      this.$refs.auditComp.selectAuditData(rows)
      this.syncRowHeightLightWithCheckRows(rows)
    },
    setIsNoAuditFileOnRight() {
      this.$nextTick(() => {
        if (
          this.$refs.auditExtendComponent &&
          this.$refs.auditExtendComponent.setIsNoAuditFileOnRight
        ) {
          this.$refs.auditExtendComponent.setIsNoAuditFileOnRight(
            this.isNoAuditFileOnRight
          )
        }
      })
    },
    syncRowHeightLightWithCheckRows(checkRows) {
      // 把勾选行进行高亮背景显示
      var $trs = $(`#${this.tableId} tr.el-table__row`)
      $trs.removeClass('checkedTR')

      var indexMap = {}
      for (let rIndex = 0; rIndex < this.tableData.length; rIndex++) {
        var rowId = this.$getRowId(this.tableData[rIndex])
        indexMap[rowId] = rIndex
      }

      for (let i = 0; i < checkRows.length; i++) {
        var index = this.$getRowId(checkRows[i])
        var rIndex = indexMap[index]
        $($trs[rIndex]).addClass('checkedTR')
      }
    },
    makeDefaultAuditData() {
      return {
        nodeName: '',
        auditUser: '',
        opinion: '',
        commonWords: '',
        nextUserId: '',
        returnNodeId: ''
      }
    },
    setBlockTabs(data = {}, isReset = false) {
      if (isReset) {
        this.blockTabs = []
      }
      if (this.$isNotEmpty(data)) {
        this.blockTabs.unshift(data)
      }
    },
    getSaveApiExtraObj(ids, pass) {
      let extra
      if (this.canAuditEdit) {
        extra = {
          getExParamsCallApiSave: (data) => {
            var passParamStr = ''
            if (pass !== undefined) {
              passParamStr = `&_PASS=${pass}`
            }
            return `&ids=${ids}&dataType=${this.dataType}${passParamStr}`
          }
        }
      } else {
        if (this.isUsingBlockView && !extra) { // 区块表单指标模块有默认可修改项
          const vo = this?.$refs?.blockView?.blockView
          const handlers = vo?.containers[0]?.blocks?.filter(e => e.handlerKey === '行表单')
          if (handlers) {
            handlers.forEach(h => {
              h.pageData.columns.forEach(e => {
                if (e.editable && !this.canAuditEdit) { // 只要有可修改的元素
                  this.canAuditEdit = true
                  extra = this.getSaveApiExtraObj(ids)
                }
              })
            })
          }
        }
      }
      return extra
    },
    showErrorIfForm(result) {
      if (this.isUsingBlockView) {
        return this.$refs.blockView.showError(result)
      } else {
        if (this.$refs.auditDetailComponent && typeof this.$refs.auditDetailComponent.showError === 'function') {
          return this.$refs.auditDetailComponent.showError(result)
        } else {
          return false
        }
      }
    },
    doSaveWhenAudit() {
      var doActionHandler = (vo) => {
        if (vo.formType === '合同') {
          vo.extData.baDetail = this.$refs.formAuditTabComponent1.$refs.cmRelevantBaList.baDetail
          vo.extData.contracpartyInfo = this.$refs.formAuditTabComponent2.$refs.contracpartylist.extData.contracparty
          vo.extData.planInfo = this.$refs.formAuditTabComponent2.$refs.contractPlan.planData
        } else if (vo.formType === '报销单') {
          if (vo.extData.bas.length === 1) {
            vo.extData.incPayeeData = []
            this.$refs.formAuditTabComponent1.$refs.baPayeelist.extData.incPayeeData.map((item, index) => {
              vo.extData.incPayeeData.push({
                ...item,
                baId: vo.extData.bas[0].baId
              })
            })
          }
        }
        var checkedRows = this.$getTableSelection(this.$refs.tableAuditing)
        var ids = this.$getTableCheckedIdsStrBy(checkedRows)
        const extra = this.getSaveApiExtraObj(ids)
        if (this.$isNotEmpty(this.newDataVoLockVersion) && !this.isUsingBlockView) {
          vo.data.lockVersion = this.newDataVoLockVersion
        } else if (this.$isNotEmpty(this.newDataVoLockVersion) && this.isUsingBlockView) {
          vo.containers[0].blocks.forEach(block => {
            if (block.handlerKey === '表单') {
              block.data.data.lockVersion = this.newDataVoLockVersion
              vo.containers[0].dataVo.data.lockVersion = this.newDataVoLockVersion
            }
          })
        }
        this.$refs.basePageWfAuditDetail.setBtProperty('保存修改', 'loading', true)
        this.$callApi(
          'WFSAVEWHENAUDIT', vo,
          result => {
            this.$refs.basePageWfAuditDetail.setBtProperty('保存修改', 'loading', false)
            this.newDataVoLockVersion = result.attributes.lockVersion
            if (this.$isEmpty(this.newDataVoLockVersion)) {
              this.$message.error('保存成功后返回的版本号为空')
            }
          }, result => {
            this.$refs.basePageWfAuditDetail.setBtProperty('保存修改', 'loading', false)
            return this.showErrorIfForm(result)
          }, extra)
      }
      this.$refs.auditComp.auditOrSaveWithEditVo(doActionHandler)
    },
    reLoadAnyway() {
      // 强制刷新审核列表
      this.dataInited = false
      this.reLoad(true)
    },
    clickRow(row) {
      var isChecked = false
      var rows = this.$getTableSelection(this.$refs.tableAuditing)
      rows.forEach(item => {
        if (item.ID === row.ID) {
          isChecked = true
          return false // break
        }
      })
      if (!isChecked) {
        // 原先是选中状态时，不改变勾选项。
        // 否则，先把之前的勾选项清除，然后再勾选当前行
        this.$refs.tableAuditing.clearSelection()
        this.$nextTick(() => { this.$refs.tableAuditing.toggleRowSelection(row) })
      }
    },
    reLoad(isTabClick) {
      // 切换页签，在已经加载过待审核数据时，不会重新加载
      var rows = this.$getTableSelection(this.$refs.tableAuditing)

      if (isTabClick === true && !this.dataInited) {
        this.$callApiParams(
          this.dataApiKey,
          this.getReloadListParams(),
          (result) => {
            this.tableAuditingList = result.data.rows
            var param = this.getReloadListParams()
            param.dataApiKey = this.dataApiKey
            this.$refs.baseListWf.setTabName(param)
            this.dataInited = true
            this.isAuditContentEmpty = !this.$isNotEmpty(this.tableAuditingList)
            if (!this.isAuditContentEmpty) {
              this.$nextTick(() => {
                // eslint-disable-next-line no-unused-vars
                var toggleRowId = this.$getRowId(this.tableAuditingList[0])
                if (this.reloadTableCallback) {
                  var callbackData = this.reloadTableCallback(result, this.$refs.tableAuditing)
                  if (callbackData && callbackData.toggleRowId) {
                    toggleRowId = callbackData.toggleRowId
                  }
                }
                if (this.search) {
                  this.enterSearch()
                } else {
                  this.updateSelectedRow(this.tableAuditingList)
                }
              })
            }
            return true
          }
        )
      } else if (this.$isNotEmpty(this.tableAuditingList) && this.$isNotEmpty(rows)) {
        this.tableAuditingListCheck()
      }
    },
    setAuditBtVisible(btText, isVisible) { // 设置主审核区域按钮的可见性
      this.$refs.basePageWfAuditDetail.setBtProperty(btText, 'visible', isVisible)
    },
    refreshButtonsDisableData(ids, callback) { // 后端刷新按钮额外数据
      if (this.$isNotEmpty(this.refreshButtonsDisableDataKey) && this.dataType === 'CformDataEntity') {
        this.$nextTick(() => {
          var params = Object.assign({}, this.refreshButtonsDisableDataParams)
          params.ids = ids.join(',')
          params.isAudit = true
          this.$callApiParams(
            this.refreshButtonsDisableDataKey,
            params,
            (result) => {
              // 以前使用的basePageWfAuditDetail，改爲列表頁后使用listTodoDetail
              this.$refs.listTodo.refreshButtonsDisableDataResult = result
              this.$refs.basePageWfAuditDetail.refreshButtonsDisableDataResult = result

              if (this.$isNotEmpty(ids)) {
                // ids是空表明是初始化还没填充数据，不要设置禁用数据
                this.$refs.listTodo.buttonsDisableData = result.data || {}
                this.$refs.basePageWfAuditDetail.buttonsDisableData = result.data || {}
              }

              // 设置按钮可见性
              this.$nextTick(() => {
                var hiddenButtons = result.attributes.hiddenButtons || []
                this.$refs.listTodo.hiddenButtons = hiddenButtons
                this.$refs.basePageWfAuditDetail.hiddenButtons = hiddenButtons

                hiddenButtons.forEach(btText => {
                  this.setAuditBtVisible(btText, false)
                })
                if (callback) {
                  callback()
                }
              })
              return true
            }
          )
        })
      } else {
        if (callback) {
          callback()
        }
      }
    },
    getReloadListParams() {
      return {
        dataType: this.dataType,
        FORM_TYPE_eq: this.FORM_TYPE_eq,
        extParam: this.dataParams.extParam ? this.dataParams.extParam : {},
        size: 10000,
        current: 1,
        STATUS: '未审核',
        actionKey: 'selectWfList'
      }
    },
    searchTypeChange(searchType) {
      this.searchData = []
      this.showTreeInput = false
      if (this.$isNotEmpty(this.search)) {
        this.search = ''
        this.enterSearch()
      }
      if (this.$isNotEmpty(this.checkIds)) {
        this.checkIds = []
        this.resetData()
      }
      // 切换申请人 和 申请部门不同的下拉数据
      const toggleData = {
        '创建人ID': {
          optionsData: this.applicant,
          prop: 'CREATE_USER_NAME_like'
        },
        '创建部门编码': {
          optionsData: this.applicationDept,
          prop: '部门_like'
        }
      }
      if (Object.keys(toggleData).includes(searchType)) {
        this.handleTreeData({
          optionsData: toggleData[searchType].optionsData,
          prop: toggleData[searchType].prop
        })
        this.showTreeInput = true
      }
    },
    // 切换金额范围 搜索输入框不为空则重新筛选数据
    moneyTypeChange() {
      if (this.$isNotEmpty(this.search)) {
        this.enterSearch()
      }
    },
    // 提取默认选中第一行的方法
    updateSelectedRow(data) {
      var ids = this.$getTableCheckedIdsOrCformId(data)
      this.refreshButtonsDisableData(ids, () => {
        // 初始化后勾选数据行的处理，会触发对按钮可用性和可见性的处理
        if (this.$isNotEmpty(this.$getRowId(data[0])) && this.$refs.tableAuditing) {
          this.$refs.tableAuditing.clearSelection()
          for (let i = 0; i < data.length; i++) {
            if (this.$getRowId(data[i]) === this.$getRowId(data[0])) {
              this.$refs.tableAuditing.toggleRowSelection(data[i], true)
              break
            }
          }
        }
      })
    },
    // 回车对数据进行筛选
    enterSearch() {
      const list = [...this.tableAuditingList]
      if (this.$isNotEmpty(this.search)) {
        this.isSearch = true
        this.searchData = list.filter(item => {
          if (this.searchType === '申请金额') {
            // 金额处理
            const map = {
              'gte': +item[this.searchType] >= +this.search,
              'lte': +item[this.searchType] <= +this.search,
              'eq': +item[this.searchType] === +this.search
            }
            return map[this.moneyType]
          } else {
            return item[this.searchType].indexOf(this.search) !== -1
          }
        })
        this.updateSelectedRow(this.searchData)
      } else {
        this.resetData()
      }
    },
    getApplicationData() {
      this.$store.dispatch('getApplicant')

      this.$store.dispatch('getDept')
    },
    checkClassParent(nodes) {
      // this.searchType 目前只有2种情况 '创建人ID' || '创建部门编码'
      this.checkIds = nodes.filter(node => !node.isParent).map(item => this.searchType === '创建人ID' ? item.id : item.code)
    },
    handleTreeData(data) {
      const { optionsData, prop } = data
      const treeData = {
        treeSetting: {
          check: {
            enable: true
          },
          data: {
            simpleData: {
              enable: true,
              idKey: 'id',
              pIdKey: 'parentId'
            },
            key: {
              name: 'name'
            }
          }
        },
        btnSwitch: {
          showEdit: false,
          showRefresh: false,
          showExpandAll: false
        },
        optionsData, // applicationDept || applicant
        prop // prop: 'CREATE_USER_NAME_like' || 部门_like
      }
      this.treeData = treeData
    },
    handleFilter() {
      if (this.$isNotEmpty(this.checkIds)) {
        this.isSearch = true
        const list = [...this.tableAuditingList]
        this.searchData = list.filter(item => {
          return this.checkIds.includes(item[this.searchType])
        })
        this.updateSelectedRow(this.searchData)
      } else {
        this.resetData()
      }
    },
    resetData(request = true) {
      this.isSearch = false
      request && this.updateSelectedRow(this.tableAuditingList)
    },
    bListWfTabClick(tag) {
      if (this.isShowDetail) {
        if (tag === '待办') {
          this.$refs.auditDetailComponent && this.$refs.auditDetailComponent.$refs.formFormat.addEventListeners && this.$refs.auditDetailComponent.$refs.formFormat.addEventListeners()
        } else {
          this.$refs.auditDetailComponent && this.$refs.auditDetailComponent.$refs.formFormat.removeEventListeners && this.$refs.auditDetailComponent.$refs.formFormat.removeEventListeners()
        }
      }
    },
    handleTabClick() {
      if (this.isShowDetail) {
        // 切换到基本信息所在的tab则绑定事件 反之解绑事件
        this.$nextTick(() => {
          if (this.$isNotEmpty(this.$refs.formAuditTab1st)) {
            if (this.$refs.formAuditTab1st.$el.style.display === 'none') {
              this.$refs.auditDetailComponent && this.$refs.auditDetailComponent.$refs.formFormat.removeEventListeners && this.$refs.auditDetailComponent.$refs.formFormat.removeEventListeners()
            } else {
              this.$refs.auditDetailComponent && this.$refs.auditDetailComponent.$refs.formFormat.addEventListeners && this.$refs.auditDetailComponent.$refs.formFormat.addEventListeners()
            }
          }
        })
      }
    },
    doBillMissing() {
      var checkedRows = this.$getTableSelection(this.$refs.tableAuditing)
      if (checkedRows === undefined) {
        return
      }
      this.$refs.makeBillMissing.show(checkedRows)
    },
    doBissMissingRecord() {
      this.$refs.billMissingRecord.show()
      if (this.isShowDetail) {
        // 切换到基本信息所在的tab则绑定事件 反之解绑事件
        this.$nextTick(() => {
          if (this.$isNotEmpty(this.$refs.formAuditTab1st)) {
            if (this.$refs.formAuditTab1st.$el.style.display === 'none') {
              this.$refs.auditDetailComponent && this.$refs.auditDetailComponent.$refs.formFormat.removeEventListeners && this.$refs.auditDetailComponent.$refs.formFormat.removeEventListeners()
            } else {
              this.$refs.auditDetailComponent && this.$refs.auditDetailComponent.$refs.formFormat.addEventListeners && this.$refs.auditDetailComponent.$refs.formFormat.addEventListeners()
            }
          }
        })
      }
    },
    showAuditList() {
      // 进入审核列表 将审核详情组件顶部tab的disabled置为true
      this.auditDetailDisabled = true
      // 审核列表页显示searchForm
      this.$refs.listTodoNotDetail.$refs.basePage.changeShowButtonContainer(true)
      this.$refs.listTodoNotDetail.$refs.basePage.$refs.searchForm.changeShowSearch(true)
      // 清除列表选中
      const listTable = this.$refs.listTodoNotDetail.$refs.table
      listTable.clearSelection()
      this.$removeTableRowHeightlight(listTable)
      // 重新赋值
      // this.$refs.listTodoNotDetail.setTableData(this.tableData)
      this.$refs.listTodoNotDetail.reloadTable()
    },
    changeIsShowDetailFunc(show, row = undefined) {
      this.isShowDetail = show
      var $root = this.$getObjRoot(this)
      var initParamsSaved = this.$getInitParams($root)
      if (!this.isShowDetail) {
        //  返回时把搜索内容情况，防止在列表页点击被详情页过滤的数据查看详情，详情页找不到你那天条数据
        this.search = ''
        this.enterSearch()
        initParamsSaved.loadTable = true
        this.showAuditList()
        return
      }
      this.$nextTick(() => {
        if (this.hideTicketButton && !initParamsSaved.hiddenButtons?.includes('缺票提醒')) {
          initParamsSaved.hiddenButtons.push('缺票提醒')
        }
        this.$refs.baseListWf.init(initParamsSaved)
        this.tableAuditingList = this.$refs.listTodoNotDetail.rowsData
        // 进入审核详情 审核详情组件顶部tab切换到第一个
        this.formAuditTabsActiveName = this.formAuditTabName0
        this.$nextTick(() => {
          this.$refs.basePageWfAuditDetail.changeShowButtonContainer(false)
          this.$refs.tableAuditing.clearSelection()
          for (let i = 0; i < this.tableData.length; i++) {
            if (this.tableData[i].id === row.id) {
              this.$refs.tableAuditing.toggleRowSelection(this.tableData[i], true)
              break
            }
          }
        })
      })
    },
    // 审核
    todoListAuditReview() {
      const rows = this.todoListcheckedRows
      this.$refs.auditReview.handleOpen(rows)
    },
    rowCheckedCallback(rows) {
      this.todoListcheckedRows = rows
      if (!this.isShowDetail && this.$refs.listTodoNotDetail) {
        const batchAuditDiasbaled = !(rows.length > 1)
        this.$refs.listTodoNotDetail.setBtProperty('批量审核', 'disabled', batchAuditDiasbaled)
      }
    },
    showAuditDetailCallback(show) {
      this.auditDetailDisabled = show
    },
    wrapDataAndAction(bool, doActionHandlerFinal) {
      this.$refs.blockView.wrapDataAndAction(bool, doActionHandlerFinal)
    },
    isNotCformBiz() { // 判断是否是非表单业务
      return (this.$parent && this.$parent.initParams)
    },
    showNotCformBiz(row, isDlg) { // 非表单业务显示审核详情
      if (this.$isEmpty(this.auditContent)) {
        this.$message.error('非表单业务必须设置auditContent')
        return
      }

      this.theComponentName = ''
      this.baseEditDlgObjContentName = ''
      this.$nextTick(() => {
        let getFormComponent
        if (!isDlg) { // 非弹框详情
          this.theComponentName = this.auditContent
          getFormComponent = () => {
            return this.$refs.auditDetailComponent
          }
        } else {
          this.baseEditDlgObjContentName = this.auditContent
          getFormComponent = () => {
            return this.$refs.baseEditDlgObjContent
          }
        }

        this.$nextTick(() => {
          const params = {
            titlePrefix: '',
            isEdit: false,
            isNoDlg: !isDlg,
            row: row,
            getFormComponent: getFormComponent
          }
          this.$refs.baseEditDlgObj.show(params)
        })
      })
    },
    getNotCformHiddenBts() { // 获取非表单业务需要隐藏的按钮集合
      return ['打印', '导出PDF', '导出打印文件', '缺票提醒']
    },
    wrapBtDetailClick(initParams) { // 完善“详情”按钮的响应
      if (this.isNotCformBiz()) { // 新的非表单业务机制，使用对应的弹框详情
        initParams.btDetailClick = {
          click: (row) => {
            this.showNotCformBiz(row, true)
          }
        }
      }
    },
    initCallBack(data) {
      if (this.$isNotEmpty(data.auditFiles)) {
        this.auditFiles = data.auditFiles
      } else {
        this.auditFiles = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.formAudit-tabs {
  display: flex;
  flex-direction: column;
  /deep/.el-tabs__content{
    flex: 1;
  }
}
.wf-audit-content {
  display: flex;
  height: 100%;
}
.mleft-20 {
  margin-left: 20px;
}
.wf-audit-content .wf-audit-detail {
  height: 100%;
  flex: 1;
  // margin-right: 10px;
  overflow: hidden;
}
.wf-audit-content .wf-audit-right-action {
  height: 100%;
  width: 390px;
  transition: all 0.5s ease-in-out;
}
.wf-audit-content .wf-audit-right-action-fold {
  width: 0px;
}
.wf-audit-content /deep/ .buttons-normal {
  padding-top: 0px !important;
}

.wf-audit-content /deep/ .formTabSaveFile .buttons-normal {
  padding-bottom: 9px !important;
}

.wf-audit-content /deep/ .column-bottom {
  height: calc(100% - 32px) !important;
}
.wf-audit-right-action {
  display: flex;
  flex-direction: column;
}
.wf-audit-right-action .wf-audit-block {
  margin-bottom: 10px;
}
.wf-audit-right-action .wf-audit-block .wf-audit-block-form {
  height: 100%;
  border: 1px solid #bbb;
  padding: 15px 15px 10px 10px;
}
.wf-audit-right-action /deep/ .wf-audit-block .el-form-item__label {
  font-size: 14px;
}
.wf-audit-right-action
  /deep/
  .wf-audit-block
  .el-input--small
  .el-input__inner {
    font-size: 14px;
    padding: 0px 5px;
    height: 28px;
    line-height: 28px;
  }
.wf-audit-right-action /deep/ .wf-audit-block .el-input__suffix {
  right: -1px;
}
.wf-audit-right-action /deep/ .wf-audit-block .el-form-item--mini.el-form-item,
.wf-audit-right-action
  /deep/
  .wf-audit-block
  .el-form-item--small.el-form-item {
  margin-bottom: 6px;
}
.wf-audit-right-action /deep/ .wf-audit-block .audit-common-words button {
  font-size: 14px;
  margin-right: -4px;
  padding: 5px 8px;
}
.wf-audit-content-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100% + 20px);
  width: 100%;
  margin-top: -20px;
  background: #e9e9e9;
  border: 1px solid #DDDDDD;
  .empty_img {
    width: 64px;
    height: 64px;
  }
  .empty_p {
    font-size: 14px;
    color: #999999;
    margin-top: 5px;
  }
}

.wf-audit-right-action .wf-audit-extend {
  overflow: hidden;
  flex: 1;
}
.no-detail-content-block {
  height: 100%;
  border: 1px solid #ddd;
  background: #e9e9e9;
  padding: 20px;
}
.wf-audit-content .retract-block-audit {
  position: absolute !important;
  z-index: 100000 !important;
  right: 1px !important;
  top: 14px;
  left: unset;
  display: none;
}
.wf-audit-content button.content-message {
  border: 1px solid #DDDDDD !important;
  border-color: #DDDDDD !important;
}
button.content-message /deep/ span,
button.content-message /deep/ i {
  color: #666 !important;
}
.tableAuditingList /deep/ .el-table {
  height: 100% !important;
  font-size: 12px;
  border: none !important;
}
.tableAuditingList /deep/ .el-table .el-table__body-wrapper {
  height: calc(100% - 30px) !important;
}
.tableAuditingList /deep/ .el-table .auditingInnerRow {
  height: 18px;
  line-height: 18px;
  overflow: hidden;
}
.tableAuditingList /deep/ .el-table .auditingAmount {
  font-weight: 800;
}
.tableAuditingList .el-table /deep/.cell {
  font-size: 12px;
}
.wfca-el-alert {
  margin: 0;
  padding: 0;
}

// /deep/.el-timeline-item__tail{
//   border-left: 2px solid #0bbd87;
// }
/deep/.el-timeline-item__timestamp{
  line-height: 2;
}
.tableAuditingContainer {
  display: flex;
  flex-direction: column;
  height: inherit;
  overflow: hidden;
  .tableAuditingList {
    flex: 1;
    overflow: hidden;
  }
}
  .auditForm{
    /deep/.el-form-item.is-error {
      .el-textarea {
        .el-textarea__inner {
          border-color: #F56C6C !important;
        }
      }
    }
  }

.wf-audit-block /deep/.common-page .receptacle-border .column-top {
  position: absolute; right: 0px; z-index: 10000;
}
.wf-audit-block /deep/.single-main .main-border {
  padding-top: 0px;
}
#tableAuditingSearch {
  display: flex;
  align-items: center;
  padding: 5px;
  #supTree {
    flex: 1;
    /deep/ .el-input__inner {
      font-size: 14px;
      border-radius: 0 2px 2px 0;
      border-left: transparent !important;
    }
  }
  .searchTypeSelect {
    $width: 85px;
    width: $width;
    flex: 0 0 $width;
    /deep/ .el-input__inner {
      font-size: 14px;
      border-radius: 2px 0 0 2px;
    }
  }
  .moneySelect {
    $width: 55px;
    width: $width;
    flex: 0 0 $width;
    /deep/ .el-input__inner {
      font-size: 14px;
      border-radius: 0 2px 2px 0;
      border-left: transparent !important;
    }
  }
  .el-input {
    flex: 1;
    margin: 0;
    /deep/ .el-input__inner {
      border-radius: 0 2px 2px 0;
      border-left: transparent !important;
    }
  }
}

</style>
<style lang="scss">
#hiddenTab >.el-tabs__header:nth-child(1) {
  display: none;
}
</style>
