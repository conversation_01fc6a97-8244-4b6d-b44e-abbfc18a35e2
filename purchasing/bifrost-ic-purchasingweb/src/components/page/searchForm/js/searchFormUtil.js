import request from '@/utils/request'
import Vue from 'vue'
import { getUserInfo } from '@/utils/auth'

let limitTip = true // 提示次数限制
let newObj = {} // 改造的下拉/树 用于存放改造后所需的数据
let saveType = '' // 存储类型 下拉 | 树

/**
 * 查询条件构造函数
 * @returns {{treeSetting: *, jsonData: any | Array, prop: *, lable: *, remark: *, inputType: *, style: any | string, isShow: any | boolean}}
 *
 * @param {String} isShowDefaultVal 初始化值
 * 部门名称:纪工委(a3e4f5085b20403cbd35bf83c8e58f6a2023) 存在多个相同部门名称，则补充部门父级ID
 * @param { Object } searchForm 搜索组件集合
 * @param { String } searchFormStyle 搜索组件样式
 * "合同名称#height:auto;width:200px;'
 * @param { Number } searchFormNum 搜索组件一行个数，超过啧自动换行
 * @param { Function } searchFormSelectChange 自定义事件
 *
 */
export function paramCreate(searchData, isShowDefaultVal, initParams) {
  // 说明
  // 'lable:prop:type:#optionsData:focus',
  const searchFormData = []
  // !! 影响指标业务申报 新增调剂是转换出错
  // initParams = JSON.parse(JSON.stringify(initParams))
  if (searchData) {
    searchData = JSON.parse(JSON.stringify(searchData))
    const argumentsList = JSON.parse(JSON.stringify(searchData))
    if (argumentsList) {
      for (let i = 0; i < argumentsList.length; i++) {
        if (argumentsList[i].indexOf(':') === -1) {
          searchFormData.push({
            lable: argumentsList[i],
            remark: argumentsList[i],
            prop: argumentsList[i] + '_eq',
            inputType: 'input',
            format: '',
            jsonData: arguments[3] || [],
            treeSetting: arguments[4],
            isShow: arguments[6] || true,
            style: arguments[7] || 'height:auto;width:100px;',
            newObj
            // values: '123'
          })
        } else {
          const multipleConditions = argumentsList[i].split(':')
          const commonConst = (status, label = undefined, type = undefined) => {
            let params
            if (argumentsList[i].indexOf('#') !== -1) {
              params = {
                prefix: argumentsList[i].split('#')[0] + '#',
                // 存在逗号 则是静态的 返回undefined 否则返回apikey
                apiKey: !(argumentsList[i].split('#')[1].includes(',')) ? argumentsList[i].slice(argumentsList[i].indexOf('#') + 1) : undefined
              }
            } else {
              newObj = {}
              params = undefined
            }

            return argumentsList[i].indexOf('#') !== -1 ? dataConversion(argumentsList[i].substring(argumentsList[i].indexOf('#') + 1).split(':##'), status, label, type, params) : []
          }
           const data = {
            lable: multipleConditions[0],
            remark: multipleConditions[0],
            prop: multipleConditions[1] ? multipleConditions[1] : multipleConditions[0] + '_eq',
            inputType: typeConversion(multipleConditions[2]),
            jsonData: arguments[3] || [],
            optionsData: commonConst(true, multipleConditions[0], multipleConditions[2]),
            treeSetting: commonConst(false),
            isShow: arguments[6] || true,
            style: getCustomStyle(initParams, multipleConditions[0]) || getStyle(multipleConditions),
            values: dateProcessing(multipleConditions[3], typeConversion(multipleConditions[2]), multipleConditions[0], isShowDefaultVal),
            checkedValues: initializationValue(
              isShowDefaultVal,
              multipleConditions[0],
              typeConversion(multipleConditions[2]),
              commonConst(true)
            ),
            newObj
          }
          // 如果是 日期 且不是树或者下拉
          // 如果是 日期 日期区间 且不是树或者下拉
          const filter = ['日期', '日期区间', '下拉']
          const dateCondition = filter.includes(multipleConditions[2])

          if (dateCondition) {
            const dateParam = initParams?.searchExParams?.[multipleConditions[0]] || {}
            data.dateParams = {
              type: dateParam?.type || '',
              format: dateParam?.format || ''
            }
            Object.assign(data, dateParam)
            if (multipleConditions[2] === '下拉' && data.multiple || multipleConditions[2] === '日期区间') {
              const deVal = data.values || ''
              data.values = Array.isArray(deVal) ? deVal : deVal.split('&')
            }
          }
          searchFormData.push(data)
        }
        limitTip = true
      }
    }
  }
  if (initParams.isAll && initParams.isFiltrateShow) {
    var defaultData = [] // 处理是否显示筛选下拉框
    if (initParams.isApply) { // 申请单据筛选
      defaultData = [
        { lable: '全部', value: '全部' },
        { lable: '草稿', value: '草稿' },
        { lable: '被退回', value: '被退回' },
        { lable: '未送审', value: '未送审' },
        { lable: '已送审', value: '已送审' },
        { lable: '已作废', value: '已作废' },
        { lable: '审核结束', value: '审核结束' }]
    } else { // 审核单据筛选
      defaultData = [
        { lable: '全部', value: '全部' },
        { lable: '被退回', value: '被退回' },
        { lable: '未审核', value: '未审核' },
        { lable: '已审核', value: '已审核' },
        { lable: '已作废', value: '已作废' },
        { lable: '审核结束', value: '审核结束' }]
    }
    const FiltrateData = {
      inputType: 'combobox',
      isShow: true,
      jsonData: [],
      lable: '状态',
      options: [],
      optionsData: initParams.FiltrateData || defaultData,
      prop: 'STATUS_eq',
      style: 'height:auto;width:110px;',
      treeSetting: initParams.FiltrateData || defaultData,
      values: '',
      id: 'stateWidth'
    }
    searchFormData.unshift(FiltrateData)
  } else if (searchData?.findIndex(i => i.lable === '筛选') !== -1) {
    // isFiltrateShow为False时 清除筛选
    searchData?.splice(searchData?.findIndex(i => i.lable === '筛选'), 1)
  }
  return searchFormData
}

function typeConversion(data) {
  switch (data) {
    case '树':
      return 'combotree'
    case '下拉':
      return 'combobox'
    case '日期':
      return 'datebox'
    case '无限制日期':
      return 'unlimiteddate'
    case '文本':
      return 'input'
    case '日期区间':
      return 'dateInterval'
    case '金额筛选':
      return 'moneyInterval'
    default:
      return 'input'
  }
}

function getStyle(multipleConditions) {
  switch (typeConversion(multipleConditions[2])) {
    case 'dateInterval':
      return 'height:auto;width:250px;'
    case 'moneyInterval':
      return 'height:auto;max-width:210px;min-width:130px;'
    default:
      return 'height:auto;width:180px;'
  }
}

function getCustomStyle(initParams, lable) {
  const searchFormStyle = initParams.searchFormStyle
  if (!searchFormStyle) return
  const searchStyle = searchFormStyle.split(',').reduce((obj, item) => {
    const [key, value] = item.split('#')
    obj[key] = value
    return obj
  }, {})
  if (!searchStyle[lable]) return
  return searchStyle[lable]
}

function dateProcessing(data, type, lable, isShowDefaultVal) {
  const getPathQuery = Vue.prototype.$getPathQuery()
  const currentYear = getUserInfo().currentContext.extInfo.booksetYear
  let dateprocessing = ''
  let defaultData = ''
  if (data && (type === 'unlimiteddate' || type === 'datebox')) {
    var now = new Date() // 当前日期
    var nowMonth = now.getMonth() // 当前月
    var nowYear = now.getFullYear() // 当前年
    // 本月的开始时间
    var monthStartDate = new Date(nowYear, nowMonth, 1)
    // 本月的结束时间
    var monthEndDate = new Date(nowYear, nowMonth + 1, 0)

    const designatedYear = new Date(currentYear).getFullYear()
    const startTime = new Date(designatedYear, 0, 1) // 当前年第一天
    const endTime = new Date(designatedYear, 11, 31) // 当前年最后一天

    if (lable.includes('开始时间')) {
      dateprocessing = Number(currentYear) === now.getFullYear()
        ? monthStartDate.toLocaleDateString().replace(/\//g, '-') : transformTime(startTime)
    } else if (lable.includes('结束时间')) {
      dateprocessing = Number(currentYear) === now.getFullYear()
        ? monthEndDate.toLocaleDateString().replace(/\//g, '-') : transformTime(endTime)
    }
  } else if (Vue.prototype.$isNotEmpty(getPathQuery) && lable === getPathQuery.codeSearch) { // 处理首页通知公告跳转
    new Promise((resolve, reject) => {
      defaultData = getPathQuery.code
      resolve()
    }).then(() => {
      // 调用搜索组件查询方法
      const searchBtn = document.getElementById('searchBtn')
      RAFSetTimeout_(() => {
        searchBtn.click()
      }, 100)
    })
    return defaultData
  } else if (isShowDefaultVal) {
    // 初始化下拉框默认值
    dateprocessing = initializationValue(isShowDefaultVal, lable)
  } else if (type === 'moneyInterval') {
    return { startAmount: '', amountCondition: '', endAmount: '' }
  } else {
    // 不是日期置空data 不然显示错误
    dateprocessing = ''
  }
  return dateprocessing
}

/**
 * 处理默认值字符串
 * @param {String} str 默认值字符串
 * @returns 返回处理后的数组
 */
const handleDetfaultValue = (str) => {
  let temp = ''
  let insideBracket = false
  const result = []

  for (let i = 0; i < str.length; i++) {
    const char = str[i]
    if (char === '[') {
      insideBracket = true
    } else if (char === ']') {
      insideBracket = false
      result.push(temp.trim())
      temp = ''
    } else if (char === ',' && !insideBracket) {
      result.push(temp.trim())
      temp = ''
    } else {
      temp += char
    }
  }
  if (temp !== '') {
    result.push(temp.trim())
  }
  return result
}

function initializationValue(isShowDefaultVal, lable, type, optionsData) {
  // 初始化下拉框默认值
  if (!isShowDefaultVal) return

  const result = handleDetfaultValue(isShowDefaultVal)
  const searchData = result?.reduce((obj, item) => {
    const [key, value] = item.split(':')
    let hanleValue
    if (value?.includes(',')) {
      hanleValue = value?.split(',')?.map(v => v)
    }
    obj[key] = hanleValue || value

    return obj
  }, {})

  const { [lable]: selectedValue } = searchData

  // 防止所有下拉全部赋上默认值
  if (!selectedValue) return

  let text = ''
  let id = ''
  if (type === 'combotree') {
    const regex = /^(\S+)\((\w+)\)$/
    const match = regex.exec(selectedValue)
    // 多个选项时 需提供父级ID
    if (match) {
      text = match[1]
      id = match[2]
    }
    const assignId = optionsData.find((item) => {
      if (id && text) {
        return item.name === text && item.parentId === id
      } else {
        return item.name === selectedValue
      }
    })
    const checkedValue = assignId ? assignId.id : ''
    return type === 'combotree' ? [checkedValue] : checkedValue
  } else {
    // 默认值下拉（以后如果其他为多选也下面处理）的多选当前是字符串，会在下面同步是否多选时分割为数组
    return selectedValue
  }
}
// 数据转换
function dataConversion(data, boolean, label, type, params) {
  // 2023.8.25 下拉+树改造
  // data[0] 是树或者下拉数据 data[1] 是树配置
  newObj = {}
  if (data.length > 1 && !boolean) {
    return JSON.parse(data[1])
  } else {
    try {
      return JSON.parse(boolean ? data[0] : '[]')
    } catch (error) {
      const { prefix, apiKey } = params
      if (type) {
        saveType = type
      }
      newObj = {
        prefix,
        apiKey,
        type: saveType
      }
      const selectDown = () => {
        // 如果报错 就是证明传递的是 下拉:#全部,是,否 | 下拉:#全部: '', 是: 是, 否: 否
        // 没有逗号证明是请求
        if (!data[0].includes(',')) {
          return []
        }
        const filterData = data[0].split(',').filter(item => Vue.prototype.$isNotEmpty(item))
        if (filterData.some(item => item.split(':').length > 2)) {
          limitTip && label && Vue.prototype.$message({ type: 'error', message: `${label}下拉框传值格式有误`, duration: 6000 })
          limitTip = false
          return
        }
        const optionsData = filterData.map(filterItem => {
          const hasColon = filterItem.includes(':')
          const originLabel = filterItem.split(':')[0]
          const originValue = filterItem.split(':')[1]

          // `采购方式:采购方式_like:下拉:#全部:全部的值,是,否`
          // 全部的值会出现"" | '' 所以需要处理 如果能转JSON证明是空 否则不是空值
          let handleValue
          try {
            handleValue = hasColon ? JSON.parse(originValue.trim()) : filterItem.trim()
          } catch (error) {
            handleValue = hasColon ? originValue.trim() : filterItem.trim()
          }
          // originLabel 表示 有冒号的情况 取 冒号前的值 即 全部
          // originValue 表示 有冒号的情况 取 冒号后的值 即 全部的值
          const label = hasColon ? originLabel.trim() : filterItem.trim()
          const value = handleValue
          return {
            label,
            value: label !== '全部' ? value : '' // 如果下拉的值是全部 则不传值
          }
        })
        return optionsData
      }
      return type === '下拉' ? selectDown() : []
    }
  }
}

/**
 * 查询参数类型常量
 * @type {{input: string, combotree: string, datebox: string, combobox: string}}
 */
export const INPUT_TYPE = {
  input: 'input', // 文本输入框
  combobox: 'combobox', // 下拉列表框
  combotree: 'combotree', // 树形选择框
  datebox: 'datebox', // 日期输入框
  unlimiteddate: 'unlimiteddate',
  dateInterval: 'dateInterval', // 日期区间
  moneyInterval: 'moneyInterval'// 金额区间
}

/**
 * 获取当前用户授权机构
 * @returns {Promise<unknown>}
 */
export function getLoginUserAuthOrgList() {
  return new Promise(function(resolve) {
    request({
      url: `${process.env.VUE_APP_API_PLATFORM}/user/v1/loginUserAuthOrgList`,
      method: 'get'
    }).then(data => {
      if (data.data.success) {
        resolve(data.data.data)
      }
    })
  })
}

/**
 * 数据字典(下拉)
 * 'lable':'apiKey,data返回'
 * data返回：例如:result.data.conType获取相应数据
 */
export const dataAllocationSelect = {
  '合同类型': 'selectConType,conType',
  '合同业务类别': 'seleConBusinessType,conBusinessType'
}

/**
 * 数据字典(下拉树)
 * 'lable':'apiKey'
 */
export const dataAllocationSelectTree = {
  '经办人': '选择系统人员'
}

function RAFSetTimeout_(callback, dalay) {
  // 第一次的时间戳
  const timestampFirst = Date.now()

  // 操作
  function handel() {
    // 每一次的时间戳
    const timestamp = Date.now()
    // 当时间戳减去后大于延迟时间
    if ((timestamp - timestampFirst) >= dalay) {
      callback()
    } else {
      requestAnimationFrame(handel)
    }
  }

  // 初次调用  requestAnimationFrame-请求动画帧
  requestAnimationFrame(handel)
}

// 标准日转化为年月日
function transformTime(date) {
  var d = new Date(date)
  var datetime = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
  return datetime
}
