<style lang="scss">
  @import 'styles/searchFrom';
</style>
<template>
  <div class="search-from" id="searchFrom" v-if="showSearch" :style="searchFormNum ? sfStyle : sfStyle1" ref="searchForm" >
    <el-form
      :inline="true"
      ref="ruleForm"
      class="search-style-1"
      :model="searchForm"
      id="searchFromMenu"
      @keyup.enter.native="handleSearch('ruleForm')"
    >
      <el-form-item
        v-show="item.isShow"
        :prop="item.prop"
	      :label="item.lable"
        v-for="item in getSearchParam"
        :key="item.key"
        :id="item.id"
      >
          <div :style="item.style">
            <el-input
              v-if="item.inputType === itype.input"
              clearable
              :placeholder="'请输入'+item.lable"
              @clear= "dropDownValueClear(item)"
              v-model="item.values"
              :disabled="item.isReadonly === 1"
            ></el-input>
            <el-date-picker
              v-if="item.inputType === itype.datebox"
              align="right"
              v-model="item.values"
              type="date"
              :value-format="dateFormat"
              :format="dateFormat"
              :placeholder="item.lable"
            >
            </el-date-picker>
            <el-date-picker
              v-if="item.inputType === itype.unlimiteddate"
              align="right"
              v-model="item.values"
              type="date"
              :value-format="dateFormat"
              :format="dateFormat"
              :placeholder="item.lable"
            >
            </el-date-picker>
            <el-select
              v-if="item.inputType === itype.combobox"
              v-model="item.values"
              filterable
              clearable
              :placeholder="'请选择'+item.lable"
              @clear="dropDownValueClear(item)"
              @change="handleCurrentChange(item.lable)"
              @visible-change="handleVisibleChange"
              >
              <el-option
                v-for="it in item.optionsData"
                :key="it.id"
                :label="it.label"
                :value="it.value"
              >
              </el-option>
            </el-select>
            <div v-if="item.inputType === itype.moneyInterval" style="display:flex;">
              <el-input v-model="item.values.startAmount" v-if="item.values.amountCondition === '2'"  :style="{width:'110px'}"></el-input>
              <el-select
                :style="{width:'80px'}"
                v-model="item.values.amountCondition"
                clearable
              >
                <el-option
                  v-for="(it,index) in amountQuery"
                  :key="index"
                  :label="it.label"
                  :value="it.value"
                >
                </el-option>
              </el-select>
              <el-input v-model="item.values.endAmount" :placeholder="item.lable" :style="{width:'110px'}"></el-input>
            </div>
            <div v-if="item.inputType === itype.combotree">
              <sup-tree :setting="item.treeSetting"
                       :btnSwitch="{
                          showEdit: false,
                          showRefresh: false,
                          showExpandAll: false
                        }"
                        @getCheckObjs="checkClassParent"
                        ref='classParentTree'
                        :is-get-child="false"
                        :nodes="item.optionsData"
                        :checked-values="item.checkedValues?item.checkedValues:[]"
                        :placeholder="'请选择' + item.lable"
                        :modKey="item.prop"
                        :topLabel="item.lable"/>
            </div>
            <div v-if="item.inputType === itype.dateInterval">
              <el-date-picker
                v-model="item.values"
                type="daterange"
                range-separator="——"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </div>
          </div>
      </el-form-item>
      <span id="buttonArea">
        <el-form-item v-if="isFilterShow">
          <el-button
            id="more-search-button"
            size='small'
            style="color: #006CFF !important;"
            :class="`iconfont ${isExtend ? 'el-icon-aliiconxiangshangshuangjiantou':'el-icon-aliiconxiangxiashuangjiantou'}`"
            title="更多搜索条件"
            @click="searchExtend"
          ></el-button>
          <!-- <el-button
          id="more-search-button"
            icon="el-icon-arrow-up"
            v-if="isExtend"
            @click="searchExtend"
          ></el-button> -->
        </el-form-item>
        <el-form-item>
          <el-tooltip
            effect="dark"
            placement="top"
            :disabled="showSearchTip"
          >
            <div slot="content">
              <div style="width:300px; font-size:14px; text-align: justify;color: #fff !important;">
                {{ tooltipLineFirst }}<br/>
                {{ tooltipLineSecond }}
              </div>
            </div>
            <el-button
              size="small"
              type="primary"
              id="searchBtn"
              style="background: #006CFF !important;
              border: 1px solid #006CFF !important;
              position: relative;
              color: #fff; font-weight:700;
              border-radius: 7px !important;
              "
              @click="handleSearch('ruleForm')"
              :disabled="loading"
            >
              <i class="el-icon-aliiconsousuo pointer"></i>&nbsp;查询
            </el-button>
          </el-tooltip>
          <!-- <el-button size='small' type="primary" id="searchBtn" @click="handleSearch('ruleForm')" :disabled="loading">
            <i class="el-icon-aliiconsousuo pointer"></i>&nbsp;查询
          </el-button> -->
          <el-button size='small' @click="resetForm()" :disabled="loading"
          style="background: #fff !important;
          color: #006CFF !important;
          border: 1px solid #006CFF !important;font-weight:700;
          border-radius: 7px !important;
          "
          >
            <i class="el-icon-aliiconzhongzhi pointer" :class="{rotate: loading}"
            ></i>&nbsp;重置
          </el-button>
        </el-form-item>
      </span>
    </el-form>
    <!-- 拓展多个搜索条件 -->
  </div>
</template>

<script>
import $ from 'jquery'
import { INPUT_TYPE } from '../searchForm/js/searchFormUtil.js'
import supTree from '../../gianttree/supTree'
export default {
  name: 'searchForm',
  components: { supTree },
  props: {
    isFilterShow: {
      // 是否显示搜索区域
      type: Boolean,
      default: false
    },
    loading: {
      // 表格loading状态
      type: Boolean,
      default: false
    }
  },
  inject: {
    hasSearchParams: { default: undefined }
  },
  data() {
    return {
      searchFormNum: 0,
      searchFormData: [],
      saSearchFormNum: '',
      isExtend: false,
      searchForm: {
        name: ''
      },
      amountCondition: '',
      sfStyle: {
        // display: 'flex',
        overflow: 'visible',
        //width: '100%'
        // whiteSpace: 'nowrap'
      },
      sfStyle1: {
        // display: 'flex',
        overflow: 'visible',
        // margin: 0,
        width: '100%'
        // whiteSpace: 'nowrap'
      },
      amountQuery: [{
        value: '1',
        label: '大于'
      },
      {
        value: '-1',
        label: '小于'
      },
      {
        value: '0',
        label: '等于'
      },
      {
        value: '2',
        label: '至'
      }],
      itype: INPUT_TYPE,
      showSearch: false,
      dateFormat: 'yyyy-MM-dd',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', new Date())
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', date)
            }
          },
          {
            text: '一周前',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            }
          }
        ]
      },
      unlimitedpickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now()
        // },
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', new Date())
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', date)
            }
          },
          {
            text: '一周前',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            }
          }
        ]
      },
      treeValues: new Map(),
      searchParam: [],
      changeEvent: [],
      tempNode: this.makeNewNode(),
      treesetting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'name'
          }
        }
      },
      initParams: {},
      radioParam: {},
      tooltipLineFirst: `
        温馨提示：在本模块能够被查询到、被选择到、并且可以操作的项目都是在新建采购项目时，选择了“在本系统编制、审核、备案合同”的项目，并非全部采购项目。`,
      tooltipLineSecond: `根据贵单位的管理规定，如果该项目不需要在本系统编制、审核、备案合同，请联系系统管理员更正之前的选择。`
    }
  },
  watch: {
    searchFormNum(value) {
      if (parseInt(value)) {
        this.$set(this.sfStyle, 'margin-top', '20px')
      }
    }
  },
  computed: {
    getSearchParam() {
      this.$emit('seacrchData', this.searchParam.slice(this.searchFormNum, this.searchParam.length))
      return this.searchParam.slice(0, this.searchFormNum)
    },
    showSearchTip() {
      return !this.initParams?.params?.pageRoute?.includes('contract')
    }
  },
  directives: {
    resize: { // 指令的名称
      bind(el, binding) { // el为绑定的元素，binding为绑定给指令的对象
        let width = ''
        let height = ''
        function isReize() {
          const style = document.defaultView.getComputedStyle(el)
          if (width !== style.width || height !== style.height) {
            binding.value({ width: style.width, height: style.height }) // 关键(这传入的是函数,所以执行此函数)
          }
          width = style.width
          height = style.height
        }
        el.__vueSetInterval__ = setInterval(isReize, 300)
      },
      unbind(el) {
        clearInterval(el.__vueSetInterval__)
      }
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleWindowResize)
  },
  methods: {
    // 下拉选项打开 关闭树的弹出层
    handleVisibleChange(visible) {
      if (visible && this.$refs.classParentTree) {
        this.$refs.classParentTree.forEach(tree => {
          tree.showSelectHidePopover()
        })
      }
    },
    // 更新对应树的数据
    handleSearchParam(handleData) {
      handleData.forEach(data => {
        this.searchParam.forEach(search => {
          if (data.lable === search.lable) {
            search.optionsData = data.optionsData
          }
        })
      })
    },
    changeRadioParam(radioParam = {}) {
      this.radioParam = radioParam
    },
    handleSearch(formName, exParams, isInit = false, reslove) {
      // 部分页面没有加载搜索组件 无需执行搜索方法
      if (this.$isEmpty(this.$refs[formName])) return
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const param = {}
          this.searchParam.forEach((e) => {
            if (e.values) {
              if (typeof (e.values) === 'object' && e.inputType === 'moneyInterval') {
                const key = e.values.amountCondition.toString()
                const enumObject = {
                  '-1': '_lt',
                  '0': '_eq',
                  '1': '_gt',
                  '2': '_ge:_le'
                }
                if (enumObject[key]) {
                  if (key === '2') {
                    enumObject[key].split(':').forEach(item => {
                      if (item === '_ge') {
                        param[`${e.prop}${item}`] = `${e.values.startAmount}`
                      } else {
                        param[`${e.prop}${item}`] = `${e.values.endAmount}`
                      }
                    })
                  } else {
                    param[`${e.prop}${enumObject[key]}`] = `${e.values.endAmount}`
                  }
                }
              } else {
                var searchValueStr = e.values.toString()
                // 字段中包含几个_or_，查询值就拼接成几个逗号分隔的值
                // 最终形成的查询参数如：申请人_eq_or_NAME_eq = 张三,张三
                if (e.prop.indexOf('_or_')) {
                  var searchTokens = e.prop.split('_or_')
                  if (searchTokens.length > 0) {
                    var searchValueStrArray = []
                    for (let i = 0; i < searchTokens.length; i++) {
                      searchValueStrArray.push(searchValueStr)
                    }
                    const searchValueStrData = searchValueStrArray.join(',')
                    if (/^[0-9T:\.\-]+Z?,[0-9T:\.\-]+Z?$/.test(searchValueStrData)) {
                      const dateArea = searchValueStrData?.split(',')
                      if (dateArea.length === 2) {
                        param[`${e.prop}_ge`] = `${dateArea[0]} 00:00:00`
                        param[`${e.prop}_le`] = `${dateArea[1]} 23:59:59`
                      }
                    }
                    searchValueStr = searchValueStrArray.join(',')
                  }
                }
                if (e.inputType !== this.itype.dateInterval) {
                  param[e.prop] = searchValueStr
                }
              }
            }
            if (this.treeValues && e.inputType === INPUT_TYPE.combotree) {
              if (this.treeValues.get(e.prop)) {
                param[e.prop] = this.treeValues.get(e.prop).join(',')
              }
            }

            // 优化调整第二行下拉树组件的渲染、获取选中值内容
            if (e.treeSetting && e.inputType === 'combotree') {
              const classParentTreeCurrent = this.$refs.classParentTree
              const classParentTreeParent = this.$parent.$parent.$parent.$refs.classParentTree
              if (this.$isNotEmpty(classParentTreeCurrent)) {
                this.setTreeSearchParam(classParentTreeCurrent, param)
              }
              if (this.$isNotEmpty(classParentTreeParent)) {
                this.setTreeSearchParam(classParentTreeParent, param)
              }
            }
          })
          if (param['STATUS_eq'] === '全部') {
            delete param['STATUS_eq']
          }
          // 查询数据
          exParams = exParams || {}
          if (this.$isNotEmpty(this.radioParam)) {
            Object.assign(param, this.radioParam)
          }
          if (isInit && this.$isEmpty(param)) {
            return
          }
          Object.assign(param, exParams)
          if (reslove) { // 获取搜索条件
            return reslove(param)
          }
          this.$emit('handleSearch', param)
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    setTreeSearchParam(tree, param) {
      tree.forEach((item) => {
        if (item.setting && item.setting.data && item.setting.data.key && item.setting.data.key.id) {
          const value = item.objValues.map(item1 => item1[item.setting.data.key.id])
          if (value.length) {
            param[item.modKey] = value
          }
        } else if (item.showValue) {
          param[item.modKey] = item.showValue
        }
      })
    },
    resetForm() {
      const DefaultVa = this.handlingDefaultData()
      this.searchParam.forEach((e) => {
        if (e.inputType === 'moneyInterval') {
          Object.keys(e.values).forEach(item => {
            e.values[item] = ''
          })
        } else {
          if (e.values && !DefaultVa[e.lable] && e.inputType) {
            e.values = undefined
          } else if (DefaultVa[e.lable] && e.inputType) {
            e.values = DefaultVa[e.lable]
          }
          // 触发联动事件
          if (e.inputType === this.itype.combobox) {
            this.handleCurrentChange(e.lable)
          }
          if (this.treeValues && e.inputType === this.itype.combotree) {
            this.treeValues = new Map()
            this.$refs.classParentTree && this.$refs.classParentTree.map((item) => item.clearCheck())
            if (this.$isNotEmpty(this.$parent.$parent.$parent.$refs.classParentTree)) {
              this.$parent.$parent.$parent.$refs.classParentTree.map((item) => item.clearCheck())
            }
          }
        }
      })
      this.handleSearch('ruleForm')
      this.$emit('resetForm')
      this.$event(this, 'projectReset')
    },
    changeShowSearch(show) {
      if (this.hasSearchParams?.()) {
        this.showSearch = show
      }
    },
    init(data, searchFormNum, changeEvent, initParams) {
      data = this.$clone(data)
      // this.initParams = JSON.parse(JSON.stringify(initParams))
      this.initParams = $.extend(true, {}, initParams)
      this.dateFormat = initParams.dateFormat || 'yyyy-MM-dd'
      this.changeEvent = changeEvent
      this.searchFormNum = searchFormNum !== '' ? searchFormNum : undefined
      const fromHome = JSON.parse(sessionStorage.getItem('apply-tab_pathParams'))
      if (initParams.isFiltrateShow && fromHome && fromHome.from === 'home') {
        data.forEach(item => {
          if (item.prop === 'STATUS_eq') {
            item.values = '已送审'
          }
        })
      }
      this.searchFormData = data
      this.saSearchFormNum = searchFormNum
      if (data && data.length > 0 && data.some((e) => e.isShow !== false)) {
        this.assembleFilterData(data)
        this.showSearch = true
      } else {
        this.showSearch = false
      }
      if (this.$isNotEmpty(initParams.isShowSearchForm)) {
        this.changeShowSearch(initParams.isShowSearchForm)
      }
      initParams.getSearchParam = async() => {
        return await new Promise(reslove => {
          return this.handleSearch('ruleForm', {}, false, reslove)
        })
      }
      setTimeout(() => {
        this.handleSearch('ruleForm', undefined, true)
        const searchFromDom = document.getElementById('searchFromMenu')
        searchFromDom?.dispatchEvent(new Event('resize'))
      })
    },
    cloneParams(initParams) {
      const obj = {}
      for (const key in initParams) {
        if (initParams[key] instanceof Object) {
          obj[key] = initParams[key]
          this.cloneParams(initParams[key])
        } else if (initParams[key] instanceof Array) {
          obj[key] = []
          initParams[key].forEach(item => {
            obj[key].push(item)
          })
          this.cloneParams(initParams[key])
        } else {
          obj[key] = initParams[key]
        }
      }
      return obj
    },
    getCheckValues(data) {
      this.treeValues.set(data.key, data.value)
    },
    async assembleFilterData(data) {
      this.searchParam = await Promise.all(
        data.map(async(e) => {
          const tempEntity = e
          tempEntity['jsonData'] = tempEntity['jsonData'] || []
          tempEntity['isShow'] = tempEntity['isShow'] !== false || false
          tempEntity['style'] =
              tempEntity['style'] || 'height:auto;width:120px;'
          tempEntity.key = parseInt(Math.random() * 1e10)
          if (
            tempEntity.inputType === INPUT_TYPE.combobox ||
              tempEntity.inputType === INPUT_TYPE.combotree
          ) {
            if (e.jsonData instanceof Promise) {
              tempEntity['options'] = await e.jsonData
            } else {
              tempEntity['options'] = e.jsonData
            }
          }
          if (tempEntity.inputType === INPUT_TYPE.combotree) {
            const defaultSetting = {
              check: { enable: false },
              data: {
                simpleData: {
                  enable: true,
                  idKey: 'id',
                  pIdKey: 'parent'
                }
              }
            }
            tempEntity['treeSetting'] = tempEntity.treeSetting
              ? Object.assign(defaultSetting, tempEntity.treeSetting)
              : defaultSetting
          }
          e = tempEntity
          return e
        })
      )
    },
    searchExtend() {
      this.isExtend = !this.isExtend
      this.$emit('getIsExtend', this.isExtend)
    },
    // 样式处理
    styleDealWith() {
      const mainTable = document.querySelector('.column-bottom') // 获取主表dom元素
      const state = document.getElementsByClassName('el-tabs__nav-scroll') // 获取状态栏（已办待办）dom元素
      const searchExtensions = document.getElementsByClassName('ExtendDiv') // 获取扩展搜索展开dom元素
      const billsInput = document.querySelector('#billsInput')
      const billsInputHeight = billsInput && billsInput.clientHeight || 0

      if (mainTable && state.length && searchExtensions.length) {
        // 如果有状态栏和扩展搜索条件展开，设置主表高度为页面高度减去状态栏高度和搜索条件高度
        mainTable.style = `height: calc(100% - 105px - ${billsInputHeight}px) !important`
      } else if (mainTable && state.length) {
        // 如果有状态栏但没有扩展搜索条件展开，设置主表高度为页面高度减去状态栏高度
        mainTable.style = `height: calc(100% - 53px - ${billsInputHeight}px) !important`
      } else if (mainTable && searchExtensions.length) {
        // 如果没有状态栏但有扩展搜索条件展开，设置主表高度为页面高度减去搜索条件高度
        mainTable.style = `height: calc(100% - 105px - ${billsInputHeight}px) !important`
      } else if (mainTable) {
        // 如果既没有状态栏也没有扩展搜索条件展开，设置主表高度为页面高度减去默认的高度
        mainTable.style = `height: calc(100% - 53px - ${billsInputHeight}px) !important`
      }
    },
    handleCurrentChange(label) {
      if (this.$isNotEmpty(this.changeEvent)) {
        for (let i = 0; i < this.changeEvent.length; i++) {
          if (this.changeEvent[i].text === label) {
            this.changeEvent[i].change(this.searchParam)
            this.handleSearch('ruleForm')
          }
        }
      }
    },
    checkClassParent(nodes, _this) {
      if (this.$isNotEmpty(nodes)) {
        this.tempNode.parentId = nodes[0].id
        // tId
        if (this.$isNotEmpty(this.$refs.classParentTree)) {
          this.$refs.classParentTree.map((item) => {
            if (!item.treeSetting.check.enable && this.$isNotEmpty(item.treeSetting.check.enable)) {
              // 若组件设置为可以多选，则在选中之后，不自动关闭选择弹窗
              item.visible = false
            } else {
              // 关闭树行选择弹窗
              item.visible = true
            }
          })
        } else if (this.$isNotEmpty(_this)) {
          _this.map((item) => {
            if (!item.treeSetting.check.enable && this.$isNotEmpty(item.treeSetting.check.enable)) {
              // 若组件设置为可以多选，则在选中之后，不自动关闭选择弹窗
              item.visible = false
            } else {
              // 关闭树行选择弹窗
              item.visible = true
            }
          })
        }
      } else {
        this.tempNode.parentId = ''
      }
    },
    makeNewNode() {
      return {
        id: '',
        label: '新建分类',
        children: [],
        parentId: '',
        isLeaf: false,
        fiscalYear: '2022',
        isClass: true
      }
    },
    setSearchFormNum(num) {
      this.searchFormNum = num
    },
    dropDownValueClear(item) {
      // // 清除下拉框值以及有默认值情况下重置默认值
      // const selectDefault = this.handlingDefaultData()
      // // 有默认值情况
      // if (this.$isEmpty(selectDefault)) return
      // item.values = selectDefault[item.lable]
      item.values = ''
    },
    handlingDefaultData() {
      // 处理默认值数据
      if (this.$isNotEmpty(this.initParams.isShowDefaultVal)) {
        const DefaultVal = this.initParams.isShowDefaultVal.split(',').reduce((obj, item) => {
          const [key, value] = item.split(':')
          obj[key] = value
          return obj
        }, {})
        return DefaultVal
      } else {
        return {}
      }
      // return this.$isNotEmpty(this.initParams.isShowDefaultVal) ? this.initParams.isShowDefaultVal.split(':') : {}
    }
  }
}
</script>
<style lang="scss" scoped>
  .search-from {
    white-space: nowrap;
  }
  #more-search-button {
    &::before {
      display: inline-block;
      width: 100%;
    }
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  #searchFromMenu {
    .rotate {
      animation: rotate 2s linear infinite;
    }
  }
</style>
