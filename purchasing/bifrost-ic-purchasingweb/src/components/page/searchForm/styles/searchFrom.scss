#searchFrom {
  height: 32px;
  // border: 1px solid white;
  display: inline-block;
  margin-bottom: 20px;
  // padding: 0 0 0 6px;
  #searchFromMenu,
  #buttonArea {
    display: flex;
    // column-gap: 6px;
    .el-form-item {
      margin-right: 6px;
    }
  }
  .el-input--mini .el-input__icon{
    line-height: 30px !important;
  }
  .el-range__icon,
  .el-range__close-icon {
    color: #999999 !important;
  }
  .el-range-editor--small {
    .el-range__close-icon,
    .el-range__icon {
      line-height: 32px;
    }
  }
  .el-range-editor--mini .el-range__close-icon {
    line-height: 30px !important;
  }
  display: inline-block;
  #more-search-button {
    min-width: 48px !important;
    width: 32px !important;
  }
  .el-button--default {
    border: 1px solid #ddd !important;
    &:hover {
      // border: 1px #478bfe solid !important;
      // color: #478bfe !important;
      background: '#fff';
      color: #006CFF;
      border: '1px solid #006CFF !important';
      background: #fff;
    }
    &:focus {
      color: #666666;
      background: #fff;
    }
    &:active {
      border: 1px #1070e1 solid !important;
      color: #1070e1 !important;
      background: #fff;
    }
  }
  .search-style-1 {
    .el-form-item--mini.el-form-item,
    .el-form-item--small.el-form-item {
      margin-bottom: 0px;
      height: 34px;
      // margin-right: 6px !important;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      .el-input__inner {
        background-color: #fff;
        border-radius: 2px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
        color: #606266;
        padding: 0 15px;
        //height: 30px;
        //line-height: 27px;
      }
      .el-input__inner:focus {
        border-color: #1f7fff !important;
        outline: 0 !important;
      }
      // .el-input__inner {
      //   background-color: #fff;
      //   border-radius: 2px;
      //   border-top-left-radius: 0;
      //   border-bottom-left-radius: 0;
      //   border: 1px solid #dcdfe6;
      //   box-sizing: border-box;
      //   color: #606266;
      //   padding: 0 15px;
      // }
      // .el-form-item__label {
      //   background-color: #f5f7fa;
      //   color: #909399;
      //   //vertical-align: middle;
      //   display: inline-block;
      //   position: relative;
      //   border: 1px solid #dcdfe6;
      //   border-right: 0px;
      //   border-radius: 2px;
      //   border-top-right-radius: 0;
      //   border-bottom-right-radius: 0;
      //   padding: 0 10px;
      //   height: 28px;
      //   line-height: 28px;
      // }
      .el-form-item__content {
        line-height: 24px;
        .el-range-editor--mini .el-range-separator {
          line-height: 26px !important;
        }
        .el-range-editor--mini .el-range__icon {
          line-height: 26px !important;
        }
      }
    }
    #buttonArea {
      .el-form-item:last-child .el-form-item__content {
        display: flex;
        column-gap: 6px;
        &::before,
        &::after {
          display: none;
        }
      }
    }
  }
  .popover_search {
    .box {
      margin-bottom: 0px;
      .box-header {
        .title {
          font-size: 18px;
        }
        .close {
          cursor: pointer;
          &:hover {
            background: #e4e4e4;
          }
        }
      }
      .box-footer {
        padding-top: 0px;
      }
      .el-form-item--small.el-form-item {
        //margin-bottom: 14px;
      }
      .el-range-editor--small.el-input__inner {
        .el-range-separator {
          padding-right: 10px;
        }
      }
    }
  }
  .more-search {
    display: none;
  }
  .search-button {
    .el-button + .el-button {
      /* margin-left: 10px; */
      margin: 0px;
    }
    .el-button--small,
    .el-button--small.is-round {
      padding: 9px;
    }
  }
}
@media screen and (max-width: 780px) {
  .search-from {
    .hiddenForm {
      display: none;
    }
    .more-search {
      display: block;
    }
  }
}
