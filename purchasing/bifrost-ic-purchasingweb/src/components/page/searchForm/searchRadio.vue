<template>
  <ul v-if="list.length" class="search-radio">
    <li v-for="radio in radioList"
      :key="radio.label"
      :class="{'radio-active': active === radio.label}"
      @click="handleClick(radio)">
      {{radio.label}}
    </li>
  </ul>
</template>

<script>
export default {
  name: 'search-radio',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    list: {
      handler() {
        if (this.$isEmpty(this.radioList)) {
          this.active = ''
          return
        }
        if (this.$isNotEmpty(this.active)) {
          const isInclude = this.radioList.some(item => item.label === this.active)
          if (isInclude) {
            return
          }
        }
        this.changeActive(this.radioList[0], false)
      },
      immediate: true
    }
  },
  data() {
    return {
      active: ''
    }
  },
  computed: {
    radioList() {
      const radioList = this.list.map(element => {
        const isStr = Object.prototype.toString.call(element) === '[object String]'
        if (isStr) {
          return {
            label: element
          }
        }
        return { ...element }
      })
      return radioList || []
    }
  },
  methods: {
    handleClick(item) {
      if (this.active === item.label) {
        return
      }
      this.changeActive(item)
    },
    changeActive(item, refresh = true) {
      this.active = item.label
      const param = {}
      param[item.label] = item.label
      if (item.click) {
        const otherParams = item.click()
        Object.assign(param, otherParams)
      }
      this.$emit('search', param, refresh)
    }
  }
}
</script>

<style lang="scss" scoped>
.search-radio {
  display: flex;
  margin-bottom: 5px;
  li {
    color: #ffffff;
    font-size: 12px;
    padding: 4px 20px;
    background: #cdcdcd;
    cursor: pointer;
    box-sizing: border-box;
  }
  .radio-active {
    color: #1479ee;
    background: #ffffff;
    border: solid 1px #cdcdcd;
    // border-bottom-color: transparent;
  }
}
</style>
