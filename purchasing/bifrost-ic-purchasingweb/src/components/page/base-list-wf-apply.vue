<template>
    <b-list-wf ref="baseListWf">
        <template #listTodo>
            <b-curd ref="listTodo" @handleSearch="handleSearch">
              <template #dbColLeft>
                <slot name="dbColLeft"/>
              </template>
              <template #tableRowSlot="{row, column}">
                <slot name="tableRowSlot" :row="row" :column="column"/>
              </template>
              <template #contentHeader>
                <slot name="contentHeader"/>
              </template>
            </b-curd>
            <apply-handle-billMissing ref="applyHandleBillMissing"/>
        </template>
        <template #tableRowSlot="{row, column}">
          <slot name="tableRowSlot" :row="row" :column="column"/>
        </template>
    </b-list-wf>
</template>

<script>
import BListWf from './base-list-wf'
import BCurd from './base-curd'

export default {
  name: 'b-list-wf-apply',
  components: { BCurd, BListWf },
  provide() {
    return {
      currentInstance: () => this.$refs.listTodo,
      billMissingNumCount: this.billMissingNumCount
    }
  },
  data() {
    return {
      dataType: '',
      billMissingNum: 0,
      changeNum: () => {}
    }
  },
  mounted() {
    this.billMissingNumCount()
  },
  methods: {
    handleSearch(param) {
      this.$emit('handleSearch', param)
    },
    init(initParams) {
      this.$parent.isListObj = true
      this.$saveInitParams(this, initParams)
      this.dataType = initParams.params.dataType

      initParams.isApply = true
      initParams.params.STATUS = '未送审'
      initParams.params.deleteApiKey = 'WFDELETE'

      var deleteApiExParams = {}
      deleteApiExParams.dataType = initParams.params.dataType
      initParams.params.deleteApiExParams = deleteApiExParams

      initParams.params.needProcessParams =
        initParams.params.needProcessParams || {}
      initParams.params.needProcessParams['isApply'] = true

      initParams.params.needDraft =
        initParams.params.needDraft || true // 是否需要草稿
      var needProcessCallBack = initParams.needProcessCallBack
      initParams.needProcessCallBack = (result) => {
        // 由后端返回参数决定使用制单样式
        initParams.isMultipleTabsTab4FillForm = (result.attributes['制单样式'] === '分tab')
        if (needProcessCallBack) {
          needProcessCallBack(result, initParams)
        }
      }
      const exHandleTabButtonsFn = initParams.exHandleTabButtons
      initParams.exHandleTabButtons = (buttons, tabName) => {
        const index = buttons.findIndex(i => i.text === '缺票提醒')
        if (tabName === '已办') { // 除了已办tab 其他tab不显示缺票提醒
          const item = { text: '缺票提醒', icon: '', enabledType: '0', num: this.billMissingNum ? this.billMissingNum : 0, click: bt => { this.billMissing() } }
          const index = buttons.findIndex(i => i.text === '缺票提醒')
          if (index === -1) {
            buttons.push(item)
          }
          this.changeNum = (num) => {
            item.num = num
          }
        } else {
          if (index !== -1) {
            buttons.splice(buttons.findIndex(i => i.text === '缺票提醒'), 1)
          }
        }
        exHandleTabButtonsFn?.(buttons, tabName)
      }
      this.$refs.baseListWf.init(initParams)
      this.$refs.baseListWf.setTabName(initParams.params)
    },
    // 执行流程保存动作
    doSave(params, callback, callbackFailed,
      message, callbackBeforeCallApi, extra, customConfirmSetting) {
      message = message || '确定执行保存吗?'
      var apiKey = 'WFSAVE&dataType=' + this.dataType

      if (customConfirmSetting) {
        customConfirmSetting.message =
          customConfirmSetting.message || message
        this.$callApiConfirmCustom(
          customConfirmSetting, apiKey, params, callback, callbackFailed, extra)
      } else {
        this.$callApiConfirm(
          message, callbackBeforeCallApi, apiKey,
          params, callback, callbackFailed, extra)
      }
    },
    btOfflineAuditClick(initParams) {
      var checkedRows = this.$getTableSelection(this.$refs.listTodo.getTable())
      var params = {
        ids: this.$getTableCheckedIdsStrBy(checkedRows),
        dataType: this.dataType
      }

      var message = `确认要线下审核 ${checkedRows.length} 条数据吗？`
      this.$callApiParamsConfirm(message,
        null, 'WFOFFLINEAUDIT', params,
        result => {
          this.reLoad()
        })
    },
    getTable() {
      return this.$refs.baseListWf.getBaseListTable()
    },
    reLoad() {
      this.$nextTick(() => {
        this.$reInit(this)
      })
    },
    setAuditBtVisible(btText, isVisible) { // 设置主审核区域按钮的可见性
      this.$refs.basePageWfAuditDetail.setBtProperty(btText, 'visible', isVisible)
    },
    billMissingNumCount() {
      this.$callApiParams('selectApplyWaitDoneNum', {},
        result => {
          this.billMissingNum = result.data ? result.data : 0
          this.changeNum(this.billMissingNum)
          return true
        })
    },
    billMissing() {
      this.$refs.applyHandleBillMissing.show()
    }
  }
}
</script>

<style scoped>

</style>
