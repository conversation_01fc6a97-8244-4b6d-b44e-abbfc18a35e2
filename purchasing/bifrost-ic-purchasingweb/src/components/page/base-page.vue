<template>
  <page ref="basePagePageObj">
    <template #pageContent>
      <LayoutTem
        ref="LayoutTem"
        :isDbCol="showTree"
        :isPageShow="false"
        :isFilterShow="false"
        style="overflow: hidden;"
        :buttons="buttons"
        :visible="visible"
        :recalculate="recalculate"
        :isFold="isFold"
        @showPopover="showPopover"
        @changePopover="changePopover"
        @changeRecalculate="changeRecalculate"
        >
        <!-- 表单相关采招系统无此场景 -->
        <!-- <template #blockName>
          <el-button
            v-if="isShowBlockName"
            class="blockName"
            :type="buttons[0].type"
            :icon="buttons[0].icon"
            :plain="buttons[0].plain"
            :size="buttons[0].size"
            :disabled="buttons[0].disabled"
            :loading="buttons[0].loading"
            @click="btClick(buttons[0])"
            >
          {{ buttons[0].text }}
          </el-button>
        </template> -->
        <template #filter>
          <search-form
              ref="searchForm"
              :style="buttons.length ? '' : 'padding-left: 0'"
              :isFilterShow="searchisFilterShow"
              :loading="loading"
              @handleSearch="handleSearch"
              @getIsExtend="getIsExtend"
              @seacrchData="getSearchData"
              @resetForm="onResetSearchForm"/>
              <!-- 下拉后的搜索 -->
              <slot
          ><div
            class="ExtendPurDiv"
            style="border-radius: 5px;"
            v-if="perisFilterShow"
          >
            <el-form
              :inline="true"
              ref="ruleForm"
              class="search-style-1"
              :model="searchForm"
              @keyup.enter.native="enterHandleSearch('ruleForm')"
            >
              <el-form-item
                v-show="item.isShow"
                :prop="item.prop"
                v-for="item in getSearchParam"
                :label="item.lable"
                :key="item.key"
              >
                  <div :style="item.style">
                    <el-input
                      v-if="item.inputType === itype.input"
                      clearable
                      v-model="item.values"
                      :placeholder="'请输入'+item.lable"
                      :disabled="item.isReadonly === 1"
                    ></el-input>
                    <el-date-picker
                      v-if="item.inputType === itype.datebox"
                      align="right"
                      v-model="item.values"
                      type="date"
                      :value-format="dateFormat"
                      :placeholder="item.lable"
                      clearable
                    >
                    </el-date-picker>
                    <el-date-picker
                      v-if="item.inputType === itype.unlimiteddate"
                      align="right"
                      v-model="item.values"
                      type="date"
                      :value-format="dateFormat"
                      :picker-options="unlimitedpickerOptions"
                    >
                    </el-date-picker>
                    <el-select
                      v-if="item.inputType === itype.combobox"
                      v-model="item.values"
                      filterable
                      clearable
                      :placeholder="'请选择'+item.lable"
                      @clear= "refDropDownValueClear(item)"
                      @change="handleCurrentChange(item.lable)"
                      >
                      <!-- @focus.self.once="universalFocus(item.lable,$event)" -->
                      <el-option
                        v-for="it in item.optionsData"
                        :key="it.id"
                        :label="it.label"
                        :value="it.value"
                      >
                      </el-option>
                    </el-select>
                    <div v-if="item.inputType === itype.combotree">
                      <sup-tree :setting="item.treeSetting"
                                :btnSwitch="{
                                            showEdit: false,
                                            showRefresh: false,
                                            showExpandAll: false
                                          }"
                                @getCheckObjs="checkClassParent"
                                ref='classParentTree'
                                :is-get-child="false"
                                :nodes="item.optionsData"
                                :placeholder="'请选择' + item.lable"
                                :checked-values="tempNode.parentId?[tempNode.parentId]:[]"
                                :modKey="item.prop"
                      ></sup-tree>
                    </div>
                    <div v-if="item.inputType === itype.dateInterval">
                      <el-date-picker
                        v-model="item.values"
                        type="daterange"
                        range-separator="——"
                        value-format="yyyy-MM-dd"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                      </el-date-picker>
                    </div>
                  </div>
              </el-form-item>
            </el-form>
          </div>
              </slot>
        </template>
        <template #button>
          <div class="button-list pur-button-list" v-show="showButtonContainer">
            <div ref="buttonContainer" class="buttonContainer">
              <div class="notMoreButton">
                <b-button
                  v-for="(bt, index) in buttons"
                  v-show="handleButtonVisible(bt, index)"
                  :key="index"
                  :bt="bt"
                  style="margin-right: 6px"
                  @btClick="btClick"
                  @dropdownClick="dropdownClick"
                />
              </div>
              <span class="toolbar-button moreButton" :style="`margin-left: ${isShowPopover ? '6px' : '0px'}`">
                <el-popover
                  placement="bottom-end"
                  width="auto"
                  trigger="manual"
                  v-model="visible"
                >
                  <div class="moreButtonContainer">
                    <b-button
                      v-for="(bt, index) in buttons"
                      v-show="bt.more && bt.visible"
                      :key="index"
                      :bt="bt"
                      @btClick="btClick"
                      @dropdownClick="dropdownClick"
                    />
                  </div>
                  <el-button
                    slot="reference"
                    size="small"
                    plain
                    v-show="moreButtonVisible"
                    @click="changePopover()"
                  >
                    更多
                  </el-button>
                </el-popover>
              </span>
            </div>

            <slot name="toolbarExtend" />
          </div>
          <component ref="listBillsInput" :is="listBillsInputId" />
        </template>

        <template #dbColLeft>
          <slot name="dbColLeft" v-if="showTree && $isEmpty(leftTreeApiKey)"/>
          <div style="height: 100%;padding:10px;" v-if="$isNotEmpty(leftTreeApiKey)">
            <sup-tree :setting="leftTreeSetting"
              ref="supTree"
              :nodes="leftTreeData"
              :is-popover="false"
              :edit-enable="false"/>
          </div>
        </template>
      <template #main>
          <slot name="mainContent" />
        </template>
      </LayoutTem>
    </template>
  </page>
</template>

<script>
import $ from 'jquery'
import SearchForm from './searchForm/searchForm'
import { INPUT_TYPE, paramCreate } from './searchForm/js/searchFormUtil.js'
import SupTree from '../gianttree/supTree'
export default {
  name: 'b-page',
  components: { SupTree, SearchForm },
  props: {
    loading: {
      type: Boolean,
      default: false
    }
  },
  provide() {
    return {
      hasSearchParams: () => this.hasSearchParams
    }
  },
  inject: {
    basePageInited: { default: undefined }
  }, // provide+inject 实现跨组件传值
  data() {
    return {
      hiddenButtons: [],
      newSearchFormNum: 0,
      searchisFilterShow: true,
      showTree: false, // 是否显示树区域dbColLeft
      oneRowCheckedOnly: false,
      checkedRow: undefined,
      buttons: [],
      buttonsMap: {},
      buttonsDisableData: {}, // 按钮可用禁用数据，这个数据来自后端
      btsAll: [], // 隐藏按钮+显示按钮,在一些可以动态切换按钮场景中,需要把所有的按钮都缓存起来
      refreshButtonsDisableDataResult: {}, // refreshButtonsDisableData接口的result对象
      perisFilterShow: false,
      getSearchParam: [],
      searchForm: {
        name: ''
      },
      itype: INPUT_TYPE,
      dateFormat: 'yyyy-MM-dd',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', new Date())
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', date)
            }
          },
          {
            text: '一周前',
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            }
          }
        ]
      },
      tempNode: this.makeNewNode(),
      listBillsInputId: '',
      saveInitParams: {},
      isShowPopover: false,
      doms: [],
      visible: false,
      recalculate: false,
      debounceInit: null,
      showButtonContainer: true,
      hasSearchParams: true,
      leftTreeApiKey: '',
      leftTreeParams: {},
      leftTreeData: [],
      leftTreeCurrentNode: {},
      leftTreeNodeClick: undefined,
      leftTreeSetting: {
        callback: {
          onClick: this.treeNodeClick
        }
      },
      isRformBlock: false, // 是否是区块
      isFold: true, // 区块 行表单是否展开
      hasBtInMore: false, // 区块 是否有按钮在更多按钮中
      isFiltrateShow: false,
      initParams: {} // 组件传递参数
    }
  },
  watch: {
    buttons: {
      handler(newV, oldV) {
        const oldLen = oldV.filter(item => item.visible).length
        const newLen = newV.filter(item => item.visible).length
        if (newLen > oldLen || (newV.length !== oldV.length && newV.length > oldV.length)) {
          this.recalculate = newLen > oldLen || (newV.length !== oldV.length && newV.length > oldV.length)
          this.$nextTick(() => {
            this.$refs.LayoutTem.init()
          })
        }
      },
      deep: true
    }
  },
  computed: {
    isShowBlockName() {
      // 是区块表单 且是隐藏了按钮的情况下 且第一个按钮类型是文字按钮
      return this.isRformBlock && !this.showButtonContainer && this.buttons[0]?.type === 'text'
    },
    moreButtonVisible() {
      this.$nextTick(() => {
        if (this.newSearchFormNum >= 0) {
          this.setSearchNum(this.newSearchFormNum)
        }
      })
      return this.isShowPopover && this.isFold && this.hasBtInMore
    },
    visibleButtons() {
      return this.buttons.filter(button => button.visible)
    }
  },
  methods: {
    init(initParams) {
      this.initParams = initParams || {}
      // 区块判断
      this.isRformBlock = this.initParams?.isRformBlock || false
      this.oneRowCheckedOnly = this.initParams.oneRowCheckedOnly || false
      this.leftTreeApiKey = this.initParams.leftTreeApiKey || ''
      this.leftTreeParams = this.initParams.leftTreeParams || {}
      this.leftTreeNodeClick = this.initParams.leftTreeNodeClick || undefined
      this.dateFormat = this.initParams.dateFormat || 'yyyy-MM-dd'
      if (this.$isNotEmpty(this.leftTreeApiKey)) {
        this.showTree = true
      } else {
        this.showTree =
          this.$isNotEmpty(this.initParams.showTree) ? this.initParams.showTree : false
      }

      if (this.$isNotEmpty(this.initParams.listBillsInputId)) {
        this.listBillsInputId = this.initParams.listBillsInputId
        this.$nextTick(() => {
          this.$refs.listBillsInput.init()
        })
      }
      // 处理下拉和树的数据
      let handleData = []
      this.initParams.isAll = this.$parent.$parent.name === '全部'
      this.isFiltrateShow = this.initParams.isAll && this.initParams.isFiltrateShow
      if (this.$isNotEmpty(this.initParams.searchForm) || this.isFiltrateShow) {
        this.$nextTick(() => {
          // 功能区总宽
          if (this.$isNotEmpty(this.initParams.searchForm)) {
            if (this.initParams.searchForm.length <= this.initParams.searchFormNum) {
              this.searchisFilterShow = false
            } else {
              this.searchisFilterShow = true
            }
          } else {
            this.searchisFilterShow = false
          }
        })
        const num = this.initParams.oldSearchFormNum ? this.initParams.oldSearchFormNum : this.initParams.searchFormNum
        this.initParams.searchFormNum = this.$isEmpty(this.initParams.searchFormNum)
          ? this.initParams.searchForm?.length || 0 : num
        if (this.initParams.isFiltrateShow) {
          this.initParams.searchFormNum = Number(num) + 1
          this.initParams.oldSearchFormNum = num
        }
        handleData = paramCreate(this.initParams.searchForm, this.initParams.isShowDefaultVal, this.initParams)
        this.$refs.searchForm.init(
          handleData,
          this.initParams.searchFormNum,
          this.initParams.searchFormSelectChange,
          this.initParams
        )
        this.hasSearchParams = true
      } else {
        this.hasSearchParams = false
      }

      var buttonArray = this.initParams.buttons || []
      this.initParams.btTexts = this.initParams.btTexts || {}
      this.initParams.btIcons = this.initParams.btIcons || {}
      buttonArray.forEach((bt) => {
        bt.loading = false
        bt.text = this.$isEmpty(bt.text) ? '按钮' : bt.text
        if (this.$isNotEmpty(this.initParams.btTexts[bt.text])) {
          // 自定义按钮标题的方式
          bt.text = this.initParams.btTexts[bt.text]
          // 如果是新增或者修改按钮，默认设置为主要按钮
          if (['新增', '修改', '登记采购结果'].some(item => bt.text.includes(item))) {
            bt.mainButton = true
          }
        }

        bt.icon = this.$isEmpty(bt.icon) ? '' : bt.icon
        if (this.$isNotEmpty(this.initParams.btIcons[bt.text])) {
          // 自定义按钮图标的方式
          bt.icon = this.initParams.btIcons[bt.text]
        }

        // bt.loading = this.$isEmpty(bt.loading) ? false : bt.loading
        const btLoading = this.$isEmpty(bt.loading) ? false : bt.loading
        this.$set(bt, 'loading', btLoading)
        bt.plain = this.$isEmpty(bt.plain) ? true : bt.plain
        bt.type = this.$isEmpty(bt.type) ? '' : bt.type
        bt.size = this.$isEmpty(bt.size) ? 'small' : bt.size
        // bt.disabled = this.$isEmpty(bt.disabled) ? false : bt.disabled // 默认按钮可用
        const btDisabled = this.$isEmpty(bt.disabled) ? false : bt.disabled // 默认按钮可用
        this.$set(bt, 'disabled', btDisabled)
        // bt.visible = this.$isEmpty(bt.visible) ? true : bt.visible
        const btVisible = this.$isEmpty(bt.visible) ? true : bt.visible // 默认按钮可用
        this.$set(bt, 'visible', btVisible)
        bt.click = this.$isEmpty(bt.click) ? function() {} : bt.click
        bt.dropDowns = this.$isEmpty(bt.dropDowns) ? [] : bt.dropDowns
        bt.hasDropDown = bt.dropDowns.length > 1

        // 按钮点击时的额外参数，比如列表的当前选中行数据
        bt.params = {}
        bt.dropDowns.forEach((item) => {
          item.params = {}
        })

        bt.setParam = (key, value) => {
          bt.params[key] = value
        }
        bt.getParam = (key) => {
          return bt.params[key]
        }

        // 文件上传处理按钮，非导入excel，导入excel操作额外处理
        // 文件上传按钮必须设置：upload
        bt.fileLimit = this.$isEmpty(bt.fileLimit) ? 1 : bt.fileLimit
        bt.filemultiple = this.$isEmpty(bt.filemultiple) ? false : bt.filemultiple
        bt.isUpload = this.$isNotEmpty(bt.upload)
        if (bt.isUpload) {
          bt.fileOnChange = (file, fileList, uploadRef) => {
            bt.upload(bt, file, fileList, [uploadRef])
          }
        }
        bt.getRowData = () => {
          if (this.$isNotEmpty(bt.params) && this.$isNotEmpty(bt.params.rows)) {
            return bt.params.rows[0]
          }
          return null
        }
        bt.getRowDataAll = () => {
          if (this.$isNotEmpty(bt.params) && this.$isNotEmpty(bt.params.rows)) {
            return bt.params.rows
          }
          return []
        }

        // 点击按钮响应函数内部，获取当前列表选中第一行的指定列的值
        bt.getRowValue = (colKey) => {
          if (this.$isNotEmpty(bt.params) && this.$isNotEmpty(bt.params.rows)) {
            var row = bt.params.rows[0]
            return row[colKey]
          }
          return null
        }

        // 点击按钮响应函数内部，获取当前列表所有选中行的指定列的值
        bt.getRowValues = (colKey) => {
          var values = []
          if (this.$isNotEmpty(bt.params) && this.$isNotEmpty(bt.params.rows)) {
            bt.params.rows.forEach((row) => {
              var val = row[colKey]
              if (this.$isNotEmpty(val)) {
                values.push(val)
              }
            })
          }
          return values
        }

        // 更新当前列表选中第一行的指定列的值
        bt.updateRowValue = (colKey, value) => {
          if (this.$isNotEmpty(bt.params) && this.$isNotEmpty(bt.params.rows)) {
            var row = bt.params.rows[0]
            if (row) {
              row[colKey] = value
            }
          }
        }

        // 获取当前列表选中第一行的ID，即业务ID
        bt.getRowId = () => {
          return bt.getRowValue('id')
        }

        // 获取当前列表选中所有行的ID集合
        bt.getRowIds = () => {
          var ids = []
          if (this.$isNotEmpty(bt.params) && this.$isNotEmpty(bt.params.rows)) {
            bt.params.rows.forEach((row) => {
              var id = this.$isNotEmpty(row.ID) ? row.ID : row.id
              ids.push(id)
            })
          }
          return ids
        }

        bt.getLeftTreeCurrentNode = () => {
          return this.leftTreeCurrentNode
        }

        bt.getRefreshButtonsDisableDataResult = () => {
          return this.refreshButtonsDisableDataResult
        }

        // 如果只有一个下拉菜单，则按钮的事件直接使用下拉菜单的事件
        // 这里就是新增按钮没有下拉菜单时，点击事件响应的方法：
        // 即是响应第一个下拉对象的click函数
        if (bt.dropDowns.length === 1) {
          bt.click = bt.dropDowns[0].click
        }
        this.buttonsMap[bt.text] = bt
      })

      // btAfters主要处理自定义按钮位置
      // btAfters: {'A':'B'}表示按钮A紧跟在按钮B后面
      // 如果btAfters的设置导致数据链存在环，则结果未知
      // 加工成 “按钮” -> [紧跟的按钮数组]
      var hasOrderDataBtKeys = {}
      var btAftersData = {}
      var btsTextAtHead = [] // 对于{'B':'HEAD'}时，B放在按钮列表的前面
      var btsTextAtLast = [] // 对于{'A':'LAST'}时，A放在按钮列表的后面
      this.initParams.btAfters = this.initParams.btAfters || {}
      var btAftersKeys = Object.keys(this.initParams.btAfters)
      for (var i = 0; i < btAftersKeys.length; i++) {
        var btTextPost = btAftersKeys[i]
        var btTextPre = this.initParams.btAfters[btTextPost]

        if (btTextPre === 'HEAD') {
          btsTextAtHead.push(btTextPost)
          continue
        }
        if (btTextPre === 'LAST') {
          btsTextAtLast.push(btTextPost)
          continue
        }

        if (this.$isEmpty(this.buttonsMap[btTextPre])) {
          console.log('按钮位置数据中存在无效按钮：' + btTextPre)
          continue
        }

        if (this.$isEmpty(this.buttonsMap[btTextPost])) {
          console.log('按钮位置数据中存在无效按钮：' + btTextPost)
          continue
        }

        if (this.$isEmpty(btAftersData[btTextPre])) {
          btAftersData[btTextPre] = []
        }
        btAftersData[btTextPre].push(btTextPost)
        hasOrderDataBtKeys[btTextPost] = btTextPost
      }

      // 开始组织按钮顺序
      // 加入没有指定位置的按钮
      var btsTemp = []
      buttonArray.forEach((bt) => {
        if (this.$isEmpty(hasOrderDataBtKeys[bt.text]) &&
          btsTextAtHead.indexOf(bt.text) === -1 &&
          btsTextAtLast.indexOf(bt.text) === -1) {
          btsTemp.push(bt)
        }
      })

      // 针对btAftersData数据，把按钮按指定位置添加
      // 这里主要解决的问题是，可能存在指定的位置又互相依赖，
      // 需要先添加某个按钮，才能再添加其他按钮。所以循环处
      // 理位置数据，每次循环找到能添加的按钮，直到不能再新
      // 的按钮后结束循环。此时如果还有按钮没有添加添加进来，
      // 则统一都添加到最后的位置。
      var btsFinal = btsTemp
      var hasNewAdd = true
      while (hasNewAdd && this.$isNotEmpty(Object.keys(btAftersData))) {
        btsFinal = []
        hasNewAdd = false

        btsTemp.forEach((bt) => {
          btsFinal.push(bt)
          var postBts = btAftersData[bt.text]
          if (this.$isNotEmpty(postBts)) {
            postBts.forEach((b) => btsFinal.push(this.buttonsMap[b]))
            hasNewAdd = true
            delete btAftersData[bt.text]
          }
        })
        btsTemp = btsFinal
      }
      if (this.$isNotEmpty(Object.keys(btAftersData))) {
        var btsAddAtTheEnd = Object.values(btAftersData)
        btsAddAtTheEnd.forEach((b) => btsFinal.push(this.buttonsMap[b]))
      }

      // 把{'B':'HEAD'}的按钮添加到前面
      if (this.$isNotEmpty(btsTextAtHead)) {
        var btTemp4Head = btsFinal
        btsFinal = []
        btsTextAtHead.forEach((b) => {
          if (this.$isNotEmpty(this.buttonsMap[b])) {
            btsFinal.push(this.buttonsMap[b])
          }
        })
        btTemp4Head.forEach((b) => {
          btsFinal.push(b)
        })
      }

      // 把{'A':'LAST'}的按钮添加到最后
      if (this.$isNotEmpty(btsTextAtLast)) {
        btsTextAtLast.forEach((b) => {
          if (this.$isNotEmpty(this.buttonsMap[b])) {
            btsFinal.push(this.buttonsMap[b])
          }
        })
      }

      this.buttons = []
      this.hiddenButtons = this.initParams.hiddenButtons || []
      btsFinal.forEach((bt) => {
        bt.loading = false
        if (this.hiddenButtons.indexOf(bt.text) > -1) { // 隐藏按钮
          bt.visible = false
        } else {
          bt.visible = true
        }
        this.buttons.push(bt)
      })
      this.rowChecked([], true)

      this.$nextTick(() => {
        const dbColLeftPadding = this.initParams.dbColLeftPadding
        // 调整左边树区域的样式
        var isWfCformDesign = $('.version-relate-buttons').length > 0
        if (!isWfCformDesign && !dbColLeftPadding) {
          $('#dbclo-left').css('cssText', 'padding:0px')
          $('.dbColLeft').css('cssText', 'height:100% !important')
        }

        if (this.buttons.length > 0) {
          this.setButtonBarVisible((this.buttons.length > 0))
        }

        // 特殊场景需要重新渲染按钮,先把所有的按钮记录下来
        this.btsAll = btsFinal

        // 这个需要写在 initParams最终值的地方 (即不再使用 this.initParams.xxx = aaa)
        this.saveInitParams = () => this.initParams
        // 发出初始化完成事件 必须要有顶部的searchForm 用于触发需要请求的ztree下拉树
        this.basePageInited && this.$isNotEmpty(this.initParams.searchForm) && this.basePageInited(this)

        this.initParams.updateTree = this.updateTree
        if (this.initParams.buttonsInitedCallback) {
          // 按钮init后的回调
          this.initParams.buttonsInitedCallback()
        }

        // 树|下拉 改造
        if (this.$isNotEmpty(this.initParams.searchForm)) {
          handleData.forEach(item => {
            // 判断是否有newObj 表示改造后的树/下拉 如果apiKey不为空则是需要请求
            if (this.$isNotEmpty(item.newObj) && this.$isNotEmpty(item.newObj.apiKey)) {
              const finallyParams = {}
              if (item.newObj.type === '下拉') {
                finallyParams.dataRef = item.newObj.apiKey
                // 下拉的apiKey是固定的
                item.newObj.apiKey = 'refLabelValuesDynamic'
              }
              // 判断是否有传递 searchFormRequireParams 配置
              // 如果有 获取searchForm对应的配置项 放到请求参数中
              const params = this.$isNotEmpty(this.initParams.searchFormBeforeCallApiParams) ? this.initParams.searchFormBeforeCallApiParams[item.lable] : {}
              Object.assign(finallyParams, params)
              const data = {
                prefix: item.newObj.prefix,
                apiKey: item.newObj.apiKey,
                type: item.newObj.type,
                params: finallyParams
              }
              this.requestData(data)
            }
          })
        }
      })
      this.recalculate = true
      this.$nextTick(() => {
        this.$refs.LayoutTem.init()
        if (this.initParams.doLoadTable) { // 到最后才加载列表数据
          this.initParams.doLoadTable()
        }

        this.initLeftTree()
        // 区块相关
        this.hasBtInMore = false
        // 保存区块组件管理配置
        if (this.initParams?.isRformBlock && this.initParams?.isSaveBlockRformConfig) {
          this.$message.success(`保存配置成功`)
          this.initParams.changeSaveBlockRformConfig?.(false)
        }
      })
    },
    setSearchNum(value) {
      let handleData = []
      this.initParams.isAll = this.$parent.$parent.name === '全部'
      this.isFiltrateShow = this.initParams.isAll && this.initParams.isFiltrateShow
      if (this.$isNotEmpty(value) || this.isFiltrateShow) {
        this.$nextTick(() => {
          // 功能区总宽
          if (this.$isNotEmpty(value) && this.$isNotEmpty(this.initParams.searchForm)) {
            if (value < this.initParams.searchForm.length) {
              this.searchisFilterShow = true
            } else {
              this.searchisFilterShow = false
            }
          }
        })
        this.initParams.searchFormNum = value
        this.$refs.searchForm?.setSearchFormNum?.(value)
        this.hasSearchParams = true
      } else {
        this.hasSearchParams = false
      }
    },
    handleButtonVisible(bt, index) {
      let isFold = true
      if (!this.isFold && index !== 0) {
        // 如果是折叠 且是第一个按钮 目前只存在区块表单一种情况
        isFold = false
      }
      return !bt.more && bt.visible && isFold
    },

    /**
     * 请求数据
     *
     * 改造后的数据
     * '是否政府集中采购:是否政府集中采购_like:下拉:#全部:"全部",是,否'
     * '采购部门:部门_like:树:#基础数据'
     * @param {Object} data prefix 前缀(是否政府集中采购) apikey(基础数据) type(树|下拉) params(请求所需参数)
     *
     */
    requestData(data) {
      const { prefix, apiKey, type, params = {}} = data
      this.$callApiParams(apiKey, params, result => {
        const res = []
        try {
          if (type === '树') {
            result.data.forEach(val => {
              const requiredProperties = ['id', 'label', 'parentId', 'itemKey']
              if (this.$hasProperties(val, requiredProperties)) {
                res.push({
                  id: val.id,
                  name: val.label,
                  parentId: val.parentId,
                  code: val.itemKey
                })
              } else {
                throw new Error() // 抛出异常结束循环
              }
            })
          } else {
            result.data.forEach(val => {
              const requiredProperties = ['eleName']
              const defaultFormat = ['label', 'value']
              if (typeof val === 'string') {
                res.push({ value: val, label: val })
              } else if (this.$hasProperties(val, requiredProperties)) {
                res.push({ value: val.eleName, label: val.eleName })
              } else if (this.$hasProperties(val, defaultFormat)) {
                res.push({ value: val.value, label: val.label })
              } else {
                throw new Error() // 抛出异常结束循环
              }
            })
          }
        } catch (error) {
          const label = prefix.includes(':') && prefix.split(':')[0]
          this.$message.error({ message: `接口返回的${label}${type}数据格式有误`, duration: 6000 })
          return true
        }
        this.updateTree([prefix + JSON.stringify(res)])
        return true
      })
    },
    handleCallbackData() {

    },
    // 需要更新树的时候传值
    updateTree(treeData) {
      const handleData = paramCreate(treeData, this.saveInitParams().isShowDefaultVal, this.saveInitParams())
      if (this.$refs.searchForm !== undefined) {
        // 将处理后的数据传到searchForm中处理
        this.$refs.searchForm.handleSearchParam(handleData)
      }
    },
    // 重新渲染按钮
    reRenderBtns(hiddenButtons) {
      // 这里使用btsAll处理隐藏按钮的方式有问题
      // this.buttons = []
      // this.$nextTick(() => {
      //   this.hiddenButtons = hiddenButtons
      //   this.btsAll.forEach((bt) => {
      //     bt.more = false
      //     if (this.hiddenButtons.indexOf(bt.text) > -1) { // 隐藏按钮
      //       bt.visible = false
      //     } else {
      //       bt.visible = true
      //     }
      //     this.buttons.push(bt)
      //   })
      // })

      this.$nextTick(() => {
        this.hiddenButtons = hiddenButtons || []
        this.buttons.forEach((bt) => {
          if (this.hiddenButtons?.indexOf(bt.text) > -1) { // 隐藏按钮
            bt.visible = false
          } else {
            bt.visible = true
          }
        })
      })
    },
    getIsExtend(bool) {
      this.perisFilterShow = bool
    },
    getSearchData(data) {
      this.getSearchParam = data
    },
    setButtonBarVisible(visible, isNoPaddingTop) {
      this.$nextTick(() => {
        if (this.$refs.basePagePageObj === undefined) {
          return
        }
        this.changeShowButtonContainer(visible)
        this.$refs.basePagePageObj.commonPageClassEx =
            this.$refs.basePagePageObj.commonPageClassEx.replace('column-top-hide-no-padding-top', '')
        this.$refs.basePagePageObj.commonPageClassEx =
            this.$refs.basePagePageObj.commonPageClassEx.replace('column-top-show', '')
        this.$refs.basePagePageObj.commonPageClassEx =
            this.$refs.basePagePageObj.commonPageClassEx.replace('column-top-hide', '')

        var exClass = (visible === true) ? ' column-top-show' : ' column-top-hide'
        if (exClass === ' column-top-hide' && isNoPaddingTop) {
          exClass = ' column-top-hide-no-padding-top'
        }

        this.$refs.basePagePageObj.commonPageClassEx =
            this.$refs.basePagePageObj.commonPageClassEx + exClass
      })
    },
    setButtonNormalNoPaddingTop(isNoPaddingTop) {
      this.$refs.basePagePageObj.commonPageClassEx =
          this.$refs.basePagePageObj.commonPageClassEx.replace('buttons-normal-no-padding-top', '')

      var exClass = (isNoPaddingTop === true) ? ' buttons-normal-no-padding-top' : ''
      this.$refs.basePagePageObj.commonPageClassEx =
          this.$refs.basePagePageObj.commonPageClassEx + exClass
    },
    setNoPager() {
      if (this.$refs.basePagePageObj.commonPageClassEx.indexOf('no-pager') < 0) {
        this.$refs.basePagePageObj.commonPageClassEx =
            this.$refs.basePagePageObj.commonPageClassEx + ' no-pager'
      }
    },
    // 通过按钮text执行点击按钮的处理，params是传递到button.params的额外参数
    doBtClick(btText, params) {
      var button = this.buttonsMap[btText]
      if (this.$isNotEmpty(button)) {
        this.btClick(button, () => {
          button.params.doBtClickParams = params || {}
        })
      }
    },
    // 手动设置当单选一行，按钮的可用和可见性
    // disableValue为空或false，表明不可用；
    // disableValue=‘hidden’表明隐藏
    // disableValue=‘visible’或true表明可用（如果原先隐藏，则显示并可用）
    setBtEnabledForRowChecked(row, btText, disableValue) {
      let rowId
      if (typeof row === 'string') {
        rowId = row
      } else {
        rowId = this.$getRowId(row)
      }
      if (this.$isEmpty(rowId)) {
        this.$message.error('setBtEnabledForRowChecked不能获取到rowId')
        return
      }

      if (typeof disableValue === 'boolean') {
        disableValue = disableValue ? 'visible' : ''
      } else {
        disableValue = disableValue || ''
      }

      let disableData = row['按钮禁用和显示数据']
      if (disableData === undefined) {
        disableData = this.buttonsDisableData[rowId] || {}
      }

      disableData[btText] = disableValue
      this.buttonsDisableData[rowId] = disableData
      row['按钮禁用和显示数据'] = disableData
    },
    btClick(button, exHandleButtonBeforeClick) {
      // 当浮层显示和按钮不是下拉框时
      if (this.visible && !button.hasDropDown) {
        this.changePopover()
      }
      // if (this.$isNotEmpty(button.dropDowns)) {
      //   button.dropDowns[0].click()
      //   return
      // }
      if (typeof button.click === 'function') {
        button.params.doBtClickParams = {} // 每次都清空代码调用点击存储的参数

        // 抛出事件让父组件有填充button.params参数的机会
        button.params.basePageObj = this
        this.$emit('baseListBtClick', button)

        if (this.$isNotEmpty(button.params) &&
            this.$isNotEmpty(button.params.rows)) {
          const rowMap = this.getRowMap(button.params.rows)
          var rowIds = this.$getTableCheckedIdsSelection(button.params.rows)
          // buttonsDisableData的key=行的ID。value是一个map，
          // 其中value.key=按钮text，value.value=按钮不可用的提示。
          // 如果value.value不为空，则点击按钮时提示该消息。
          for (let i = 0; i < rowIds.length; i++) {
            var disableData = this.getRowBtDisableData(rowMap, rowIds[i])
            if (this.$isNotEmpty(disableData)) {
              if (disableData[button.text] !== undefined &&
                  disableData[button.text] !== '' &&
                  disableData[button.text] !== 'hidden' &&
                  disableData[button.text] !== 'visible') {
                this.$message.info(disableData[button.text])
                return
              }
            }
          }
        }

        if (exHandleButtonBeforeClick) {
          exHandleButtonBeforeClick()
        }
        button.click(button)
      }
    },
    handleSearch(param) {
      this.$emit('handleSearch', param)
    },
    enterHandleSearch(formName) {
      this.$refs.searchForm.handleSearch(formName)
    },
    doSearch(exParams) { // 手动触发搜索
      this.$refs.searchForm.handleSearch('ruleForm', exParams)
    },
    resetSearch() {
      this.$refs.searchForm.resetForm()
    },
    fileUploadError(res) {
      this.$message.error('文件上传错误，请重试')
    },
    dropdownClick(dropdownItem) {
      this.btClick(dropdownItem)
    },
    setBtProperty(btText, property, value) {
      var setBtPropertyByBt = (bt, property, value) => {
        if (this.$isNotEmpty(bt)) {
          bt[property] = value
          if (property === 'dropDowns') {
            bt.hasDropDown = value.length > 1
          }
        }
      }

      // 有些时候，buttons和buttonsMap中相同的bt.text却是不同的bt对象
      // 目前不清楚错误原因
      setBtPropertyByBt(this.buttonsMap[btText], property, value)
      this.buttons.forEach(bt => {
        if (bt.text === btText) {
          setBtPropertyByBt(bt, property, value)
        }
      })
    },
    disableAllBts() {
      this.buttons.forEach(bt => {
        this.setBtProperty(bt.text, 'disabled', true)
      })
    },
    getRowMap(rows) {
      const rowMap = {}
      rows.forEach(row => {
        const rId = this.$getRowId(row)
        rowMap[rId] = row
      })
      return rowMap
    },
    // 按钮禁用的动态数据有两个来源：
    // 1、row对象中的“按钮禁用和显示数据”
    // 2、额外接口获取的buttonsDisableData数据
    getRowBtDisableData(rowMap, rowId) {
      const row = rowMap[rowId]
      let disableData = row['按钮禁用和显示数据']
      if (this.$isEmpty(disableData) &&
        this.$isNotEmpty(this.buttonsDisableData)) {
        disableData = this.buttonsDisableData[rowId]
      }
      return (disableData === undefined) ? {} : disableData
    },
    // 处理列表勾选时，按钮可用性联动。rows表示当前勾选的行，isNoRow表明当前没有数据
    rowChecked(rows, isNoRow, baseListObj) {
      this.$nextTick(() => {
        var rowIds = this.$getTableCheckedIdsSelection(rows)
        this.buttons.forEach(bt => {
          // 整体处理：重新隐藏原来不显示的按钮，其他按钮则重新显示
          if (this.hiddenButtons.indexOf(bt.text) > -1) {
            this.setBtProperty(bt.text, 'visible', false)
          } else {
            this.setBtProperty(bt.text, 'visible', true)
          }

          // 有些按钮不管有没有选中行都要显示
          if (this.$isNotEmpty(this.buttonsDisableData) &&
              this.buttonsDisableData[bt.text] !== undefined &&
              this.hiddenButtons.indexOf(bt.text) === -1) {
            this.setBtProperty(bt.text, 'visible', true)
          }

          var disabled = true // 没有设置是否禁用时 默认按钮不可用
          if (bt.enabledType === '0') { // 表明按钮任何时候都可用，即使列表没有数据
            disabled = false
          } else if (bt.enabledType === '-1') { // 按钮在列表有数据时可用
            disabled = isNoRow
          } else if (bt.enabledType === '1' && rows.length === 1) { // 按钮在选中1行且仅选中1行时可用
            disabled = false
          } else if (bt.enabledType === '1+' && rows.length > 0) { // 按钮在至少选中1行时可用
            disabled = false
          } else if (bt.enabledType === undefined) { // 没有定义enabledType时
            disabled = bt.disabled
          }

          // 根据当前选中的行数据，处理按钮禁用和显示
          let visible
          if (rowIds.length > 0) {
            // buttonsDisableData的key=行的ID。value是一个map，
            // 其中value.key=按钮text，value.value=按钮不可用的提示。
            // 如果value.value为空，则直接设置对应的按钮不可用，否则是点击按钮时提示该消息。
            const rowMap = this.getRowMap(rows)
            for (let i = 0; i < rowIds.length; i++) {
              var disableData = this.getRowBtDisableData(rowMap, rowIds[i])
              if (this.$isNotEmpty(disableData)) {
                if (disableData[bt.text] !== undefined) {
                  if (disableData[bt.text] === '' && disabled === false) {
                    // 如果按上面规则，按钮可用，则再根据每行业务规则判断按钮可用
                    disabled = true
                  }

                  // 只要有一行指定隐藏，就需要隐藏按钮
                  if (disableData[bt.text] === 'hidden') {
                    visible = false
                  }

                  // 其他行没有指定隐藏，当前行指定显示，才可以显示按钮
                  if (disableData[bt.text] === 'visible' && visible !== false) {
                    visible = true
                  }
                }
              }
            }
          }

          if (visible !== undefined) {
            this.setBtProperty(bt.text, 'visible', visible)
          }
          this.setBtProperty(bt.text, 'disabled', disabled)
        })

        if (baseListObj) {
          if (this.oneRowCheckedOnly === true) {
            this.controlOneRowCheckedOnly(baseListObj)
          }
          this.$nextTick(() => {
            var realRows = this.$getTableSelection(baseListObj.$refs.table)
            if (typeof baseListObj.rowCheckedCallback === 'function') {
              baseListObj.rowCheckedCallback(realRows, baseListObj.$refs.table, this)
            }
          })
        }
      })
    },
    controlOneRowCheckedOnly(baseListObj) {
      var table = baseListObj.$refs.table
      var checkedRows = this.$getTableSelection(baseListObj.$refs.table)
      if (this.checkedRow && checkedRows.length > 1) {
        var newCheckedRow = (checkedRows[0].ID === this.checkedRow.ID)
          ? checkedRows[1] : checkedRows[0]
        table.clearSelection()
        table.toggleRowSelection(newCheckedRow, true)
      } else {
        this.checkedRow = checkedRows[0]
        if (checkedRows.length > 1) {
          table.clearSelection()
          table.toggleRowSelection(this.checkedRow, true)
        }
      }
    },
    customizeButtons(customizeButtons) { // 自定义按钮可用
      const _this = this
      customizeButtons.forEach(buttonName => {
        if (!_this.$isEmpty(_this.buttonsMap[buttonName])) {
          _this.buttonsMap[buttonName].disabled = false
        }
      })
    },
    checkClassParent(nodes) {
      this.$refs.searchForm.checkClassParent(nodes, this.$refs.classParentTree)
    },
    makeNewNode() {
      return {
        id: '',
        label: '新建分类',
        children: [],
        parentId: '',
        isLeaf: false,
        fiscalYear: '2022',
        isClass: true,
        name: ''
      }
    },
    handleCurrentChange(lable) {
      this.$refs.searchForm.handleCurrentChange(lable)
    },
    // universalFocus(lable, $event) {
    //   this.$refs.searchForm.universalFocus(lable, $event, this.getSearchParam)
    // },
    refDropDownValueClear() {
      this.$ref.searchForm.dropDownValueClear()
    },
    showPopover(value, buttons, state, searchFormNum) {
      this.isShowPopover = value
      this.buttons = [...buttons]
      this.newSearchFormNum = searchFormNum
      if (!state && searchFormNum > 0) {
        this.setSearchNum(this.newSearchFormNum)
      } else if (state && searchFormNum > 0) {
        this.setSearchNum(this.newSearchFormNum)
      }
      this.hasBtInMore = state
    },
    changePopover() {
      this.visible = !this.visible
    },
    changeRecalculate(value) {
      this.recalculate = value
    },
    changeShowButtonContainer(show) {
      this.showButtonContainer = show
    },
    onResetSearchForm() {
      this.leftTreeCurrentNode = undefined
      if (this.$refs.supTree) {
        // 如果左树显示，重置时清空高亮节点
        this.$refs.supTree.cancelSelectedNode()
      }
    },
    initLeftTree() {
      var selectedNodeIdBefore
      if (this.$refs.supTree) {
        var values = this.$refs.supTree.getSelectedNodes()
        if (this.$isNotEmpty(values)) {
          selectedNodeIdBefore = values[0].id
        }
      }

      if (this.$isNotEmpty(this.leftTreeApiKey)) {
        this.$callApiParams(
          this.leftTreeApiKey, this.leftTreeParams,
          result => {
            this.leftTreeData = result.data

            // 刷新后重新选中
            if (selectedNodeIdBefore) {
              setTimeout(() => {
                this.$refs.supTree.selectAndCheckNode(selectedNodeIdBefore)
              }, 1000)
            }
            return true
          })
      }
    },
    treeNodeClick(event, treeId, treeNode) {
      this.leftTreeCurrentNode = treeNode
      if (this.$isNotEmpty(this.leftTreeNodeClick)) {
        this.leftTreeNodeClick(treeNode)
      }
    },
    /**
     * 改变区块的折叠状态
     * @param {Boolean} isFold 是否折叠
     */
    changeIsFold(isFold) {
      if (arguments.length > 0) {
        this.isFold = isFold
        return
      }
      this.isFold = !this.isFold
    },
    reInitLayout() {
      this.recalculate = true
      this.$nextTick(() => {
        this.$refs.LayoutTem.init()
      })
    }
  }
}
</script>

<style lang="scss">
.ExtendPurDiv{
  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 20px !important;
  }
  .pur-search-style1 {
      .el-form-item__label {
        padding: 0px 5px 0 20px!important;
      }
  }
  .el-form--inline .el-form-item{
    margin-right:6px !important;
  }
}

.ExtendDiv {
  margin-top: 15px ;
  .search-style-1 {
    .el-form-item--mini.el-form-item,
    .el-form-item--small.el-form-item {
       //margin-bottom: -10px;
      // height: 34px;
      padding: 5px 0 0px 0;
      margin-bottom: 0px !important;
      margin-right: 6px !important;
      // padding-left: 10px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      .el-input__inner {
        background-color: #fff;
        border-radius: 2px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
        color: #606266;
        padding: 0 15px;
        //height: 30px;
        //line-height: 27px;
      }
      .el-input__inner:focus {
        border-color: #1F7FFF !important;
        outline: 0 !important;
      }
      .el-form-item__label {
        background-color: #f5f7fa;
        color: #909399;
        //vertical-align: middle;
        display: inline-block;
        position: relative;
        border: 1px solid #dcdfe6;
        border-right: 0px;
        border-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        padding: 0 10px;
        height: 28px;
        line-height: 28px;
      }
    }
    .el-form-item__content{
        line-height: 24px;
        .el-range-editor--mini .el-range-separator{
          line-height: 30px !important;
        }
        .el-range-editor--mini .el-range__icon {
          line-height: 30px !important;
        }
      }
    }
  }

  .column-top-hide .column-top:first-child { display: none; }
  .column-top-show .column-top:first-child { display: block; }
  .column-top-hide-no-padding-top .column-top:first-child { display: none; }

  .el-dropdown-menu{
    max-height: 400px;//最大高度
    overflow-x: hidden; // 隐藏X轴上的滚动条
  }

  .pur-button-list{
    margin-bottom: 20px;
    .notMoreButton{
      column-gap:16px !important;
    }
    .el-button{
      // color:#fff;
      // border: 1px solid #409EFF !important;
      // background-color:#409EFF;
      font-weight: bold;
      background: '#fff';
      color: #006CFF;
      border: 1px solid #006CFF !important
    }
  }

</style>
<style scoped lang="scss">
  .blockName {
    width: min-content;
    line-height: 32px;
    font-size: 14px;
  }
  .button-list {
    display: flex;
    overflow-y: hidden;
    overflow-x: auto;
    //margin-bottom:20px;
    /* 设置滚动条的宽度和高度 */
    &::-webkit-scrollbar {
      height: 0px!important;
    }
    .buttonContainer {
      display: flex;
      white-space: nowrap;
    }
  }

</style>
