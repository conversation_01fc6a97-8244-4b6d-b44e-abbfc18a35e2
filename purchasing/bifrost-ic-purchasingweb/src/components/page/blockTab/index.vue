<template>
  <el-tabs v-show="(isShowMain) || tabs.length"
           class="block-tabs"
           :class="{'block-tabs-detail': isDetail, 'block-tabs-audit': !isDetail}"
           :type="type"
           v-model="active"
           ref='blockTab'
           >
    <el-tab-pane v-if="showMainPanel" :label="mainLabel" name="main">
      <div class="flex-column" style="height: 100%">
        <div class="wf-audit-history">
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in auditHistory"
              :key="index"
              :icon="activity.icon"
              :type="activity.type"
              :color="activity.color"
              :size="activity.size"
              :timestamp="activity.createTime">
              <div>
                <div class="font-bold">{{activity.nodeName}}
                  <span v-if="activity.action" >{{`【${activity.action}】`}}</span>
                </div>
                <div class="node-card" v-if="activity.timestamp || activity.auditUser"
                     :class="getCardClass(activity.action)">
                  <div  v-if="activity.auditUser">
                    <span v-if="activity.auditUser" class="font-bold" style="margin-right: 5px">{{ activity.auditUser }}</span>
                  </div>
                  <div v-if="composeAction(activity.action)" class="node-action mTop-3 font-bold">{{ composeAction(activity.action) }}</div>
                  <div v-if="activity.timestamp" class="mTop-3">
                    审核意见： <span class="font-bold">{{ activity.timestamp }}</span>
                  </div>
                  <i class="el-icon-circle-check status-icon"></i>
                  <i class="el-icon-circle-close status-icon"></i>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <slot/>
      </div>
    </el-tab-pane>
    <el-tab-pane v-for="(tab) in tabs" :key="tab.name" :label="tab.label" :name="tab.name" lazy>
      <component :ref="tab.ref"
        :is="tab.component"
        :data="data"
        :dataType="dataType"
        :billId="billId"
        :isDetail="isDetail"
        :selectedData=selectedData
        :getAuditTable="getAuditTable"/>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import { AUDIT_RULE_TYPE, AUDIT_LOG_CONTROL, AUDIT_LOG_LEVEL, AUDIT_LOG_STATUS, AUDIT_LOG_TYPE } from '@/constant'

export default {
  name: 'blockTab',
  props: {
    type: {
      type: String,
      default: ''
    },
    showMainPanel: {
      type: Boolean,
      default: true
    },
    mainLabel: {
      type: String,
      default: '单据审核'
    },
    tabs: {
      type: Array,
      default: () => []
    },
    dataType: {
      type: String,
      default: 'CformDataEntity'
    },
    metaName: {
      type: String,
      default: ''
    },
    metaId: {
      type: String,
      default: ''
    },
    formType: {
      type: String,
      default: ''
    },
    billId: {
      type: String,
      default: ''
    },
    nodeId: {
      type: String,
      default: ''
    },
    apiKey: {
      type: String,
      default: 'auditAnalyzeDetail'
    },
    needRequest: {
      type: Boolean,
      default: true
    },
    isDetail: {// 分为历史详情和审核
      type: Boolean,
      default: false
    },
    getAuditTable: {// 获取审核左侧表格实例
      type: Function,
      default: () => {}
    },
    selectedData: {// 获取审核左侧表格实例
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      active: 'main',
      data: null,
      auditHistory: [],
      projectId: ''
    }
  },
  watch: {
    billId: {
      handler() {
        this.init()
      },
      immediate: true
    }
  },
  mounted() {
    this.setActiveTab()
  },
  computed: {
    isShowMain() {
      return this.showMainPanel && this.billId
    }
  },
  methods: {
    setActiveTab() {
      if ((!this.isShowMain) && this.tabs.length) {
        this.active = this.tabs[0].name
      }
    },
    init() {
      if (!this.billId && !this.metaName) {
        return
      }
      this.getAnalyzeDetail('', '', '')
      this.getMainData()
    },
    // 修复bar不显示问题
    setBarWidth() {
      this.$nextTick(() => {
        const tabDom = this.$refs.blockTab?.$el
        const firstTab = tabDom?.querySelector('.el-tabs__item')
        const tabBar = tabDom?.querySelector('.el-tabs__active-bar') || {}
        const firstTabWidth = firstTab?.clientWidth || 0
        if (tabBar.style && !tabBar.clientWidth) {
          tabBar.style.width = firstTabWidth - 20 + 'px'
        }
      })
    },
    getAnalyzeDetail(attTempId, cformId = '', dataType) {
      if (!this.needRequest) {
        return
      }
      if (this.showMainPanel) {
        this.active = 'main'
      }
      const params = {
        dataType: this.dataType,
        ids: this.billId,
        metaId: this.metaId,
        metaName: this.metaName,
        formType: this.formType,
        projectId: this.projectId,
        attTempId: attTempId,
        cformId: cformId
      }
      if (this.isDetail) {
        params.nodeId = this.nodeId
        params.isHisDetail = true
      }
      const callBack = (result) => {
        if (result.success) {
          if (this.billId === params.ids) {
            this.data = result.data || {}
            if (dataType !== '项目配置') {
              this.addRuleBlock()
            }
          }
        }
        return true
      }
      const failCallBack = () => {
        // return true
      }
      this.$callApiParams(this.apiKey, params, callBack, failCallBack)
    },
    addRuleBlock() {
      this.$emit('initCallBack', this.data)
      this.$nextTick(this.setActiveTab)
    },
    getMainData() {
      if (!this.isShowMain) { // 审核时没有传nodeid
        return
      }
      // 加载审核历史数据
      this.$callApiParams('getWfRemarkByDataId',
        { dataId: this.billId, dataType: this.dataType }, (result) => {
          this.auditHistory = result.data
          return true
        })
    },
    getData() {
      // 后续可拓展
      if (this.data) {
        const data = Object.assign({}, this.data)
        return data
      }
      return {}
    },
    verifyAudit() {
      const allData = this.getData()
      const { BAN_WARNING, BAN, WARNING, ALL } = AUDIT_LOG_CONTROL
      if (this.$isEmpty(allData)) {
        return
      }
      let result = true // false阻止comfirm执行
      allData.auditRule.forEach(item => {
        let rowRes = true
        switch (item.auditCtrCondition) {
          case BAN_WARNING: {
            const levels = [AUDIT_LOG_LEVEL.BAN, AUDIT_LOG_LEVEL.WARNING]
            rowRes = this.verifyRow(item, levels)
            break
          }
          case BAN: {
            const levels = [AUDIT_LOG_LEVEL.BAN]
            rowRes = this.verifyRow(item, levels)
            break
          }
          case WARNING: {
            const levels = [AUDIT_LOG_LEVEL.BAN, AUDIT_LOG_LEVEL.WARNING]
            rowRes = this.verifyRow(item, levels)
            break
          }
          case ALL: {
            const levels = [AUDIT_LOG_LEVEL.BAN, AUDIT_LOG_LEVEL.WARNING, AUDIT_LOG_LEVEL.PROMPT]
            rowRes = this.verifyRow(item, levels)
            break
          }
        }
        if (!rowRes) {
          result = rowRes
        }
      })
      if (!result) {
        this.$message.error('辅助审核存在校验项不通过！请检查')
      }
      return result
    },
    verifyRow(row, levels) {
      const { TEXT } = AUDIT_RULE_TYPE
      const isInclude = levels.includes(row.auditLevel)
      if (isInclude && row.type !== TEXT) {
        if (row.operationResult === AUDIT_LOG_STATUS.PASS) {
          return true
        }
        return false
      } else if (isInclude && row.type === TEXT && row.auditType === AUDIT_LOG_TYPE.AUTO) {
        if (row.auditResult === AUDIT_LOG_STATUS.PASS) {
          return true
        }
        return false
      }
      return true
    },
    submit(auditData) {
      const { ids = '', nodeId } = auditData
      const dataIds = ids.split(',')
      const allData = this.getData()
      if (dataIds.length > 1 || this.$isEmpty(dataIds) || this.$isEmpty(nodeId) || this.$isEmpty(allData)) {
        return
      }
      const logsEntityList = allData.auditRule?.map(item => {
        if (item.type === AUDIT_RULE_TYPE.TEXT) {
          return { ...item }
        }
        if (item.type === AUDIT_RULE_TYPE.CHECK) {
          return { ...item, auditResult: item.value.join(',') }
        }
        return { ...item, auditResult: item.value }
      }) || []

      const params = {
        dataIds: ids,
        nodeId,
        logsEntityList: logsEntityList
      }
      const callback = () => {
        return true
      }
      const failCallBack = () => {
        return true
      }
      this.$callApi('saveAuditLogsAndAiAudit', params, callback, failCallBack)
    },
    formColItemChange(theColItem, selects, dataVo) {
      if (this.$isNotEmpty(theColItem.dataRef) &&
        theColItem.dataRef === '选择财政项目#{部门ID}') {
        if (this.$isNotEmpty(selects)) {
          var list = selects.list
          var idArr = []
          list.forEach(row => {
            idArr.push(row.id)
          })
          this.projectId = idArr.join(',')
          this.getAnalyzeDetail(dataVo?.extData?.attTempId, dataVo?.data?.bizid, '项目配置')
        }
      }
    },
    composeAction(action) {
      let status = ''
      switch (action) {
        case '审核' || '自动通过':
          status = '同意'
          break
        case '退回':
          status = '退回'
          break
        case '撤销':
          status = '撤销'
          break
      }
      return status
    },
    getCardClass(action) {
      let className = ''
      switch (action) {
        case '审核' || '自动通过':
          className = 'success-card success-icon'
          break
        case '退回':
          className = 'fail-card fail-icon'
          break
        case '待审核':
          className = 'wait-card'
          break
        case '撤销':
          className = 'revoke-card'
          break
        default:
          className = 'success-card'
          break
      }
      return className
    }
  }
}
</script>

<style lang='scss' scoped>
.block-tabs {
  height: 100%;
  /deep/.el-tabs__header.is-top {
    margin-bottom: 10px;
  }
  /deep/.el-tab-pane {
    height: 100%;
  }
}
.block-tabs-audit /deep/.el-tabs__header.is-top .el-tabs__item {
  padding: 0px 10px;
}
.block-tabs-audit /deep/.el-tabs__nav-wrap {
  max-width: 320px;
}
.block-tabs-detail /deep/.el-tabs__header.is-top {
  margin-bottom: 20px;
}
.wf-audit-history {
  flex: 1;
  border: 1px solid #DDDDDD;
  padding: 20px;
  overflow: scroll;
  .font-bold {
    font-weight: bold;
  }
  .mTop-3 {
    margin-top: 3px;
  }
  .node-card {
    overflow: hidden;
    position: relative;
    font-size: 13px;
    padding: 5px 8px;
    margin-top: 5px;
    box-sizing: border-box;
    border-radius: 5px;
  }

  .success-card {
    background: #e9fbe8;
    .node-action {
      color: #4bc848;
    }
  }
  .fail-card {
    background: #feeded;
    .node-action {
      color: #f78081;
    }
  }
  .wait-card {
    background: #ebf5ff;
    .node-action {
      color: #71b7fa;
    }
  }
  .revoke-card {
    background: rgba(247, 136, 0,.1);
    .node-action {
      color: rgba(247, 136, 0,.5);
    }
  }
  .status-icon {
    display: none;
    position: absolute;
    right: 0px;
    top: 0px;
    font-size: 42px;
  }

  .fail-icon .el-icon-circle-close {
    display: block;
    color: #f78081;
  }
  .success-icon .el-icon-circle-check {
    display: block;
    color: #4bc848;
  }
  /deep/ .el-timeline-item__icon {
    font-size: 12px;
  }
  /deep/.el-timeline-item__wrapper {
    padding-left: 22px;
  }
  /deep/.el-timeline-item__timestamp.is-bottom {
    margin-top: 0px;
    line-height: 20px;
  }
}
</style>
