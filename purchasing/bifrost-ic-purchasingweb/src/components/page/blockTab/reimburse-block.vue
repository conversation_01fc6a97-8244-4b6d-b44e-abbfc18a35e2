<!--
 * @Description: 页面名称
 * @version:
 * @Author: zhangshaowu <EMAIL>
 * @Date: 2024-05-14 17:53:57
 * @LastEditors: zhangshaowu <EMAIL>
 * @LastEditTime: 2024-05-14 17:56:08
-->
<template>
  <div class="containerWarp">
    <div class="container" v-if="this.selectedData.list">
      <el-tag size='medium' type="info" class="title">会议费标准</el-tag>
      <div class="standard-warp">
          <p>{{standard.infoName}}</p>
          <p>标准天数：{{standard.standard}}</p>
          <p>标准费用：每人每天 {{standard.standardFee}}</p>
          <p>说明：{{standard.remark}}</p>
      </div>
      <div class="system" v-if="this.selectedData.list[0].remindInfo">
          <p>相关制度:</p>
          <li v-for="(item,index) in standard.remindInfo.fileList" :key="index">
            <span class="btn-operat" @click="preview(item)">{{item.tempAttName}}</span>
          </li>
      </div>
    </div>
    <file-view ref="fileView"></file-view>
  </div>
</template>

<script>

export default {
  name: 'reimburseBlock',
  props: {
    selectedData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    'selectedData'(value) {
      this.standard = value.list[0]
    }
  },
  data() {
    return {
      standard: []
    }
  },
  methods: {
    preview(row) {
      this.$refs.fileView.open({ fileIds: [row.attId], bizDataId: null })
    }
  }
}
</script>

<style lang="scss" scoped>
.containerWarp {
    overflow: hidden;
    height: 100%;
    padding: 15px;
    border:1px solid #ccc;
    .container{
      position: relative;
      .title{
        position:absolute;
        top: -10px;
      }
    }
    p{
        margin-bottom:0 ;
    }
    .standard-warp{
        padding: 20px 10px 10px 10px;
        margin-top: 10px;
        border: 1px solid #ccc;
        // box-shadow: 5px 5px 10px rgba(0,0,0,0.5)
    }
    .system{
        margin-top: 20px;
        .btn-operat {
          color: #0000ff;
          cursor: pointer;
        }
    }
}
</style>

