<template>
  <div class="container" :class="{'mini-table': !isDetail}">
    <el-table
      ref="multipleTable"
      :data="tableData"
      v-loading="loading"
      border
      tooltip-effect="dark"
      :row-class-name="getClass"
      style="width: 100%;height: 100%;"
    >
      <el-table-column
        type="index"
        align="center"
        width="25">
      </el-table-column>
      <el-table-column
        prop="attName"
        label="规章制度文件名称"
        align="left"
        min-width="150px"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="80px"
      >
      <template slot-scope="{row}">
        <div class="flex-around">
          <span class="btn-operat" @click="preview(row)">预览</span>
          <span class="btn-operat" @click="download(row)">下载</span>
        </div>
      </template>
      </el-table-column>
    </el-table>
    <file-view ref="fileView"></file-view>
  </div>
</template>

<script>
import { downloadFile } from '@/api/file/file'

export default {
  name: 'ruleBlock',
  props: {
    dataType: {
      type: String,
      default: ''
    },
    billId: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: null
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      loading: true
    }
  },
  watch: {
    billId: {
      handler() {
        if (!this.billId) {
          this.loading = true
        }
      },
      immediate: true
    },
    data: {
      handler() {
        if (this.data) {
          this.tableData = this.data.auditFiles || []
          this.loading = false
        }
      },
      immediate: true
    }
  },
  methods: {
    getClass({ row }) {
      switch (row.color) {
        case 'pass':
          return 'pass-color'
        default:
          return ''
      }
    },
    preview(row) {
      this.$refs.fileView.open({ fileIds: [row.attId], bizDataId: null })
    },
    download(row) {
      if (!row.attId) {
        return this.$message.error('attId不能为空')
      }
      downloadFile([row.attId]).then((res) => {
        const str = res.headers['content-disposition']
        if (str) {
          const index = str.lastIndexOf('=')
          const str1 = window.decodeURI(str.substring(index + 1, str.length))
          this.handleFileDownloadRes(res, str1)
        } else {
          this.$message.error('文件信息不存在')
        }
      })
    },
    handleFileDownloadRes(res, str1) {
      if (!res.data) {
        this.$message.error('文件信息不存在')
        return
      }
      var filename = str1 || undefined
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // 检测是否在IE浏览器打开
        window.navigator.msSaveOrOpenBlob(new Blob([res.data]), filename)
      } else {
        // 谷歌、火狐浏览器
        let url = ''
        if (
          window.navigator.userAgent.indexOf('Chrome') >= 1 ||
          window.navigator.userAgent.indexOf('Safari') >= 1
        ) {
          url = window.webkitURL.createObjectURL(new Blob([res.data]))
        } else {
          url = window.URL.createObjectURL(new Blob([res.data]))
        }
        const link = document.createElement('a')
        const iconv = require('iconv-lite')
        iconv.skipDecodeWarning = true // 忽略警告
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', filename)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  overflow: hidden;
  height: 100%;
  .btn-operat {
    color: #0000ff;
    cursor: pointer;
  }
  /deep/.pass-color {
    color: #006600;
  }
}
.mini-table .el-table{
  font-size: 12px;
}
.flex-around {
  display: flex;
  justify-content: space-around;
}
</style>

