<template>
  <div class="container" :class="{'mini-table': !isDetail}">
    <el-table
      ref="table"
      :data="tableData"
      border
      tooltip-effect="dark"
      v-loading="loading"
      :row-class-name="getClass"
      style="width: 100%;height: 100%;"
    >
      <el-table-column
        prop="ruleName"
        label="事项名称"
        align="center"
        min-width="150px"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="auditType"
        label="类型"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="auditLevel"
        label="审核级别"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="审核结果"
        :width="resWidth + 'px'"
      >
        <template slot-scope="{row}">
          <!-- <span v-if="row.type === AUDIT_RULE_TYPE.TEXT">{{row.auditResult}}</span> -->
          <div v-if="row.type === AUDIT_RULE_TYPE.BUTTON" class="result-box">
            <el-button
              v-for="btn in row.auditResult"
              :key="btn.value" :size="getSize"
              :type="getBtnType(btn)"
              round
              @click="btnClick(row, btn)">{{btn.label}}</el-button>
          </div>
          <div v-else-if="row.type === AUDIT_RULE_TYPE.SELECT" class="result-box">
            <el-select v-model="row.value" :size="getSize" popper-class="mini-select" clearable placeholder="请选择" @change="commonChange(row)">
              <el-option
                v-for="item in row.auditResult"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div v-else-if="row.type === AUDIT_RULE_TYPE.RADIO" class="result-box">
            <el-radio-group v-model="row.value" :size="getSize" @change="commonChange(row)">
              <el-radio v-for="item in row.auditResult"
                        :label="item.value"
                        :key="item.value"
              >{{item.label}}</el-radio>
            </el-radio-group>
          </div>
          <div v-else-if="row.type === AUDIT_RULE_TYPE.CHECK" class="result-box">
            <el-checkbox-group v-model="row.value" @change="checkChange(row)">
              <el-checkbox v-for="item in row.auditResult" :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </div>
          <span v-else>{{row.auditResult}}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="riskWarning"
        label="风险"
        align="center"
      >
      <template slot-scope="{row}">
        <el-tooltip v-if="row.riskWarning" popper-class="risk-tip" class="item" :content="row.riskWarning" placement="top">
          <i style="font-size: 20px;color: #ff0066" class="el-icon-info"></i>
        </el-tooltip>
      </template>
      </el-table-column>
  </el-table>
  </div>
</template>

<script>
import { AUDIT_RULE_TYPE, AUDIT_LOG_STATUS } from '@/constant'

export default {
  name: 'assistBlock',
  props: {
    dataType: {
      type: String,
      default: ''
    },
    billId: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: null
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      AUDIT_RULE_TYPE,
      resValue: 1,
      tableData: [],
      loading: true,
      resWidth: 110
    }
  },
  watch: {
    billId: {
      handler() {
        if (!this.billId) {
          this.loading = true
        }
      },
      immediate: true
    },
    data: {
      handler() {
        if (this.data) {
          const auditRule = this.data.auditRule || []
          this.tableData = auditRule.map(item => {
            if (item.type === AUDIT_RULE_TYPE.TEXT || !item.type) {
              return item
            }
            const resIndex = item.auditResult.findIndex((res) => res.value === item.value)
            if (resIndex !== -1) {
              this.$set(item, 'operationResult', item.auditResult[resIndex].operationResult)
            }
            if (item.type === AUDIT_RULE_TYPE.CHECK) {
              item.value = item.value.split(',')
            }
            return item
          })
          this.loading = false
        }
      },
      immediate: true
    },
    tableData: {
      handler() {
        this.$nextTick(this.resetWidth)
      },
      immediate: true
    }
  },
  computed: {
    getSize() {
      const size = this.isDetail ? 'small' : 'mini'
      return size
    }
  },
  methods: {
    resetWidth() {
      try {
        const tableRef = this.$refs.table
        const tableEl = tableRef.$el
        const resultBoxs = Array.from(tableEl.querySelectorAll('.result-box'))
        resultBoxs.forEach(el => {
          if (el.children[0].clientWidth > this.resWidth) {
            this.resWidth = el.children[0].clientWidth
          }
        })
      } catch (error) {
        console.log(error)
      }
    },
    getBtnType(btn) {
      switch (btn.operationResult) {
        case AUDIT_LOG_STATUS.PASS:
          return 'primary'
        case AUDIT_LOG_STATUS.NOPASS:
          return 'danger'
        default:
          return ''
      }
    },
    getClass({ row }) {
      let field = row.auditResult
      if (row.type === AUDIT_RULE_TYPE.TEXT) {
        field = row.auditResult
      } else if (row.type === AUDIT_RULE_TYPE.CHECK) {
        field = row.operationResult
      } else {
        field = row.operationResult
      }
      switch (field) {
        case AUDIT_LOG_STATUS.PASS:
          return 'pass-color'
        case AUDIT_LOG_STATUS.NOPASS:
          return 'no-pass-color'
        default:
          return ''
      }
    },
    btnClick(row, data) {
      row.value = data.value
      row.auditResult = [data]
      this.$set(row, 'operationResult', data.operationResult)
    },
    commonChange(row) {
      const auditResult = row.auditResult || []
      auditResult.forEach(data => {
        if (data.value === row.value) {
          this.$set(row, 'operationResult', data.operationResult)
        }
      })
    },
    checkChange(row) {
      const auditResult = row.auditResult
      const value = row.value
      let result = ''
      auditResult.forEach(item => {
        if (value.includes(item.value) && result !== AUDIT_LOG_STATUS.NOPASS) {
          result = item.operationResult
        }
      })
      row.operationResult = result
    }
  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
.container {
  overflow: hidden;
  height: 100%;
}
.el-table /deep/.pass-color {
  color: #1f7fff;
}
.el-table /deep/.no-pass-color {
  color: red;
}
.result-box {
  display: flex;
  justify-content: flex-start;
  padding: 0 2px;
  flex-wrap: wrap;
  line-height: normal;
  box-sizing: border-box;
}
.result-box /deep/.el-button--mini {
  padding: 5px 6px!important;
}
.el-radio /deep/.el-radio__input.is-checked+.el-radio__label {
  color: #606266;
}
.mini-table .el-table {
  font-size: 12px;
}
.mini-table .el-radio /deep/.el-radio__inner {
  width: 14px;
  height: 14px;
}
.mini-table /deep/.el-radio__label {
  font-size: 12px;
}
.mini-table /deep/.el-checkbox__label {
   font-size: 12px;
 }
.result-box /deep/.el-checkbox {
  margin-right: 3px;
  .el-checkbox__label {
    padding-left: 1px;
  }
}
</style>
<style lang='scss'>
.mini-select.el-select-dropdown .el-select-dropdown__item {
  height: 21px!important;
  line-height: 21px!important;
}
.risk-tip.el-tooltip__popper.is-dark {
  color: #000000;
  background: #facf4e;
  .popper__arrow::after {
    border-top-color: #facf4e;
    color: #facf4e;
  }
}
</style>
