<template>
  <common-dialog
    ref="commonDialog"
    v-model="visible"
    title="列设置"
    customWidth="800px"
    >
    <template #dialogContent>
      <div class="common-page" style="height: 500px">
        <el-tabs :tab-position="tabPosition" style="height: 500px;">
          <el-tab-pane label="列设置">
            <e-table
              :data="columnsData"
              border
              :tableColumn="tableColumn"
              row-key="id"
              :loading="loading"
              showIndex>
              <template #slotColumn="{item, row, index}">
                <el-input
                  v-if="item.prop === 'label'"
                  v-model="row.label"
                  size="mini"
                />
                <el-input-number
                  v-else-if="item.prop === 'width'"
                  v-model="row.width"
                  size="mini"
                  :controls="false"
                  :precision="0"
                  :min="0"
                />
                <div v-else-if="item.prop === 'isEnabled'" class="flex-row-center">
                  <el-checkbox v-model="row.isEnabled"></el-checkbox>
                </div>
                <div v-else-if="item.prop === 'operate'" class="flex-row-center">
                  <span type="text" style="color: #1890ff; cursor: pointer; font-size: 14px; margin-right: 15px;" v-show="index !== 0" @click="moveUp(index)">上移</span>
                  <span type="text" style="color: #1890ff; cursor: pointer; font-size: 14px;" v-show="index !== columnsData.length - 1" @click="moveDown(index)">下移</span>
                </div>
              </template>
            </e-table>
          </el-tab-pane>
          <el-tab-pane v-if="isAudit" label="其他设置"><el-checkbox v-model="enableCustomBackList"  style="padding-bottom: 5px">详情模式下，单据审核结束后，返回到列表模式</el-checkbox></el-tab-pane>
        </el-tabs>

      </div>
    </template>
    <template #customFooter>
      <el-button @click="resetSetting">恢复设置</el-button>
      <el-button @click="handleClose">取消</el-button>
      <el-button :loading="submitLoading" type="primary" @click="submit">确定</el-button>
    </template>
  </common-dialog>
</template>

<script>
export default {
  name: 'column-config-dialog',
  data() {
    return {
      isAudit: false,
      submitLoading: false,
      showMap: {},
      loading: false,
      visible: false,
      columnsData: [],
      dataType: '',
      enableCustomBackList: false,
      tableColumn: [
        {
          prop: 'prop',
          aliasLabel: '默认列名',
          align: 'center',
          tooltip: false,
          minWidth: '130',
          type: 'str'
        },
        {
          prop: 'label',
          aliasLabel: '别名',
          align: 'center',
          tooltip: false,
          slotColumn: true,
          minWidth: '130',
          type: 'str'
        },
        {
          prop: 'width',
          aliasLabel: '列宽',
          align: 'center',
          tooltip: false,
          minWidth: '130',
          slotColumn: true,
          type: 'str'
        },
        {
          prop: 'isEnabled',
          aliasLabel: '是否显示',
          align: 'center',
          tooltip: false,
          slotColumn: true,
          aliasWidth: '100',
          type: 'str'
        },
        {
          prop: 'operate',
          aliasLabel: '操作',
          align: 'center',
          type: 'str',
          tooltip: false,
          slotColumn: true,
          aliasWidth: '100'
        }
      ]
    }
  },
  methods: {
    show({ columnsData = [], colType, isAudit = false }) {
      this.isAudit = isAudit
      columnsData = this.$clone(columnsData)
      this.dataType = ''
      columnsData.forEach(item => {
        if (item.columnEntity) {
          this.dataType = item.columnEntity.dataType
        }
      })
      if (!this.dataType) {
        this.dataType = colType
      }
      if (!this.dataType) return this.$message.warning('dataType不能为空')
      this.columnsData = columnsData.map(item => {
        const columnEntity = item.columnEntity
        return {
          dataType: this.dataType,
          prop: item.prop,
          isEnabled: columnEntity ? columnEntity.isEnabled === '是' : item.isEnabled,
          width: item.width,
          label: item.label
        }
      })
      this.visible = true
      this.$callApiParams('selectFormEnableConditionsListByUser', {
        DATA_TYPE_eq: this.dataType
      }, (result) => {
        const data = result.data || {}
        if (data['enableCustomBackList'] === '是') {
          this.enableCustomBackList = true
        } else {
          this.enableCustomBackList = false
        }
        return true
      }, () => {
      })
      // this.getData()
    },
    getData() {
      this.loading = true
      this.$callApiParams('selectCustomTableColumnList', {
        'DATA_TYPE_eq': this.dataType
      }, (res) => {
        if (res.success) {
          this.columnsData = res.data || []
        }
        this.loading = false
        return true
      }, () => {
        this.loading = false
      })
    },
    submit() {
      const cols = this.columnsData.map((row, index) => ({
        dataType: this.dataType,
        defaultCol: row.prop,
        isEnabled: row.isEnabled,
        width: row.width,
        labelAlias: row.label,
        orderNo: index
      }))
      this.submitLoading = true
      const _enableCustomBackList = this.enableCustomBackList ? '是' : '否'
      this.$callApi(`saveCustomTableColumn&DATA_TYPE_eq=${this.dataType}&enableCustomBackList=` + _enableCustomBackList, {
        cols
      }, result => {
        if (result.success) {
          this.visible = false
          this.$emit('refresh')
        }
        this.submitLoading = false
      }, () => {
        this.submitLoading = false
      })
    },
    moveDown(index) {
      if (index < this.columnsData.length - 1) {
        const temp = this.columnsData[index]
        this.columnsData.splice(index, 1)
        this.columnsData.splice(index + 1, 0, temp)
      }
    },
    moveUp(index) {
      if (index > 0) {
        const temp = this.columnsData[index]
        this.columnsData.splice(index, 1)
        this.columnsData.splice(index - 1, 0, temp)
      }
    },
    handleClose() {
      this.visible = false
    },
    resetSetting() {
      const beforeClose = (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '执行中...'
          this.$callApiParams('resetCustomTableColumn', {
            'DATA_TYPE_eq': this.dataType
          }, (result) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (result.success) {
              this.getData()
              done()
              this.handleClose()
              this.$emit('refresh')
            }
          }, () => {
            instance.confirmButtonText = '确定'
            instance.confirmButtonLoading = false
          })
        } else {
          done()
        }
      }

      this.$confirm('是否确定要恢复设置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: beforeClose
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-input-number ::v-deep input {
  text-align:left;
}
</style>
