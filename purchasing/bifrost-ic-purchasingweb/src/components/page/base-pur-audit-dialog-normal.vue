<template>
    <div>
      <el-dialog
        ref="auditDialog"
        :title="titleText"
        append-to-body
        :width="dialogWidth"
        class="auditDialogStyle dialog-style1"
        :close-on-click-modal="false"
        :visible.sync="auditVisible"
      >
        <div
          class="auditDialogBox"
          :style="audiuBigSize ? 'height:720px' : 'height:530px'"
        >
          <div class="auditDialogLeft" style="padding: 10px 10px 10px 10px">
            <slot name="infoLeft" />
          </div>
          <div class="auditDialogRight">
            <div
              class="auditLogBox"
              :style="audiuBigSize ? 'height:360px' : 'height:250px'"
            >
              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in activities"
                  :key="index"
                  :icon="activity.icon"
                  :type="activity.type"
                  :color="activity.color"
                  :size="activity.size"
                  :timestamp="activity.timestamp"
                >
                  <div :style="getColor(activity.nodeName)">
                    {{ activity.nodeName }}
                    <span style="color: #606266"
                      >【{{ activity.auditUser }}】
                      {{ activity.createTime }}</span
                    >
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div class="dialogBox" style="padding: 40px 20px 20px">
              <el-form
                ref="auditForm"
                :rules="rules"
                :model="auditForm"
                label-width="80px"
              >
                <el-form-item label="审批意见" prop="opinion">
                  <el-input
                    type="textarea"
                    resize="none"
                    v-model="auditForm.opinion"
                    :rows="audiuBigSize ? '6' : '4'"
                    maxlength="200"
                    placeholder="请输入审批意见"
                    :disabled="auditDialogRight"
                  ></el-input>
                </el-form-item>
                <el-form-item label="审核结果" prop="_PASS">
                  <el-radio-group v-model="auditForm._PASS" :disabled="auditDialogRight">
                    <el-radio label="1">通过</el-radio>
                    <el-radio label="0">不通过</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label-width="110px" v-if="auditForm._PASS == '0' && isCusAudit" label="再次送审模式" prop="toCurrentNode">
                  <el-radio-group v-model="auditForm.toCurrentNode" :disabled="auditDialogRight">
                    <el-radio label="0">重新审核</el-radio>
                    <el-radio label="1">直接送审至本节点</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
              <div style="margin-top: 30px; text-align: center">
                <el-button
                  class="btn-normal"
                  type="primary"
                  icon="el-icon-edit"
                  @click="saveAudit"
                  :loading="auditDialogLoading"
                  :disabled="auditDialogRight"
                  >确定</el-button
                >
                <!-- <el-button class="btn-normal" @click="close">返回</el-button> -->
              </div>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  export default {
    name: "base-pur-audit-dialog-normal",
    props: {
      dialogWidth: {
        type: String,
        default: "1350px",
      },
      isExpert: {
        type: String,
        default: "",
      },
      //审核页给大小2套样式。true默认大的样式,false小样式
      audiuBigSize: {
        type: Boolean,
        default: true,
      },
      auditDialogRight: {
        type: Boolean,
        default: false,
      },
      titleText: {
        type: String,
        default: "审核",
      },
      isCusAudit: {
        type: Boolean,
        default: false,
      }
    },
    data() {
      return {
        // titleText:"审核",
        auditVisible: false,
        ids: "",
        dataType: "",
        auditForm: {
          opinion: "",
          _PASS: "1",
          toCurrentNode: "0"
        },
        auditDialogLoading: false,
        rules: {
          opinion: [
            { required: true, message: "请输入审批意见", trigger: "blur" },
          ],
          _PASS: [
            { required: true, message: "请选择审核结果", trigger: "change" },
          ],
          toCurrentNode: [
            { required: true, message: "请选择审核结果", trigger: "change" },
          ],
        },
  
        activities: [],
      };
    },
  
    mounted() {},
  
    methods: {
      show(row, dataType) {
        this.clearData();
        this.auditDialogLoading = false
        //
        this.auditVisible = true;
        this.dataType = dataType;
        this.ids = row.getRowData().bizid;
        this.initAuditLog(this.ids);
          this.$nextTick(() => {
              this.$refs.auditForm.clearValidate();
          });
      },
  
      initAuditLog(id) {
        let fetchApi = ['BidRegistrationEntity','SupplierInfoEntity','ExpertInfoEntity', 'EvaluationSeparationEntity'].includes(this.dataType)  ? 'getWfCustomByDataId' : 'getWfRemarkByDataId'
        this.$callApiParams(
          fetchApi,
          { dataId: id, dataType: this.dataType },
          (result) => {
            this.activities = result.data;
            return true;
          }
        );
      },
  
      clearData() {
        this.auditForm = {
          opinion: "",
          _PASS: "1",
          toCurrentNode: "0"
        };
      },
  
      getColor(content) {
        if (content.indexOf("退回") !== -1) {
          return { color: "red" };
        }
        if (content.indexOf("撤销") !== -1) {
          return { color: "#c4cad5" };
        }
      },
  
      saveAudit() {
        this.$refs["auditForm"].validate((valid) => {
          if (valid) {
            this.auditDialogLoading = true
            let params = Object.assign(
              {
                ids: this.ids,
                dataType: this.dataType,
              },
              this.auditForm
            );
            const expertType = this.isExpert ? this.isExpert : "WFAUDIT";
            this.$callApiParams(expertType, params, (result) => {
              if (result.success) {
                this.$message({
                  message: "审核已完成",
                  type: "success",
                });
                this.auditDialogLoading = false
                this.$emit("auditSucceed");
                this.auditVisible = false;
                return true;
              }else {
                this.auditDialogLoading = false
              }
            });
          } else {
            this.$message.warning("请填写必填项!");
          }
        });
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  </style>
  