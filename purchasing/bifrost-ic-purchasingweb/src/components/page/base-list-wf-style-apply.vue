<template>
    <b-list-wf-style ref="baseListWf">
        <template #listTodo>
            <b-curd ref="listTodo" @handleSearch="handleSearch">
                <template #dbColLeft>
                    <slot name="dbColLeft"/>
                </template>
            </b-curd>
        </template>
    </b-list-wf-style>
</template>

<script>
import BListWfStyle from './base-list-wf-style'
import BCurd from './base-curd'

export default {
  name: 'b-list-wf-style-apply',
  components: { BCurd, BListWfStyle },
  data() {
    return {
      dataType: ''
    }
  },
  methods: {
    handleSearch(param) {
      this.$emit('handleSearch', param)
    },
    init(initParams) {
      this.$parent.isListObj = true
      this.$saveInitParams(this, initParams)
      this.dataType = initParams.params.dataType

      initParams.isApply = true
      initParams.params.STATUS = '未送审'
      initParams.params.deleteApiKey = 'WFDELETE'

      var deleteApiExParams = {}
      deleteApiExParams.dataType = initParams.params.dataType
      initParams.params.deleteApiExParams = deleteApiExParams

      initParams.params.needProcessParams =
        initParams.params.needProcessParams || {}
      initParams.params.needProcessParams['isApply'] = true

      initParams.params.needDraft =
        initParams.params.needDraft || true // 是否需要草稿
      var needProcessCallBack = initParams.needProcessCallBack
      initParams.needProcessCallBack = (result) => {
        // 由后端返回参数决定使用制单样式
        initParams.isMultipleTabsTab4FillForm = (result.attributes['制单样式'] === '分tab')
        if (needProcessCallBack) {
          needProcessCallBack(result, initParams)
        }
      }

       this.$refs.baseListWf.init(initParams)
       this.$refs.baseListWf.setTabName(initParams.params)
    },
    // 执行流程保存动作
    doSave(params, callback, callbackFailed,
      message, callbackBeforeCallApi, extra, customConfirmSetting) {
      message = message || '确定执行保存吗?'
      var apiKey = 'WFSAVE&dataType=' + this.dataType

      if (customConfirmSetting) {
        customConfirmSetting.message =
          customConfirmSetting.message || message
        this.$callApiConfirmCustom(
          customConfirmSetting, apiKey, params, callback, callbackFailed, extra)
      } else {
        this.$callApiConfirm(
          message, callbackBeforeCallApi, apiKey,
          params, callback, callbackFailed, extra)
      }
    },
    btOfflineAuditClick(initParams) {
      var checkedRows = this.$getTableSelection(this.$refs.listTodo.getTable())
      var params = {
        ids: this.$getTableCheckedIdsStrBy(checkedRows),
        dataType: this.dataType
      }

      var message = `确认要线下审核 ${checkedRows.length} 条数据吗？`
      this.$callApiParamsConfirm(message,
        null, 'WFOFFLINEAUDIT', params,
        result => {
          this.reLoad()
        })
    },
    getTable() {
      return this.$refs.baseListWf.getBaseListTable()
    },
    reLoad() {
      this.$nextTick(() => {
        this.$reInit(this)
      })
    }
  }
}
</script>

<style scoped>

</style>
