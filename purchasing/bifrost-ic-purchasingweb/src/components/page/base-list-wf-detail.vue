<template>
  <b-page
    v-show="isShowDetail"
    ref="listTodo"
    class="listTodoDetail"
    @baseListBtClick="baseListBtClick"
    @leftCollapsedClick="leftCollapsedClick"
  >
    <template #dbColLeft>
      <div class="tableAuditingContainer">
        <div id="tableAuditingSearch">
          <el-select
            v-model="searchType"
            placeholder="请选择"
            size="mini"
            class="searchTypeSelect"
            @change="searchTypeChange"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="moneyType"
            placeholder="请选择"
            size="mini"
            class="moneySelect"
            v-show="showMoneySelect"
            @change="moneyTypeChange"
          >
            <el-option
              v-for="item in moneyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model="search"
            placeholder="Enter搜索"
            style="font-size: 14px"
            size="mini"
            :type="changeInputType"
            @keyup.enter.native="enterSearch"
            v-show="isSelect"
          ></el-input>
          <sup-tree
            v-show="showTreeInput"
            ref="classParentTree"
            :setting="treeData.treeSetting"
            :btnSwitch="treeData.btnSwitch"
            :is-get-child="false"
            :nodes="treeData.optionsData"
            :checked-values="[]"
            :modKey="treeData.prop"
            :isFilterData="true"
            placeholder="选择过滤条件"
            :hidePopoverFlag="searchType"
            @getCheckObjs="checkClassParent"
            @handleFilter="handleFilter"
            @clearCheck="resetData"
          >
          </sup-tree>
        </div>
        <div class="mini-table tableAuditingList">
          <el-table
            border
            ref="tableAuditing"
            :data="tableData"
            @row-click="clickRow"
            :id="tableId"
            @selection-change="tableAuditingListCheck"
          >
            <el-table-column type="selection" width="25" />
            <el-table-column
              label="编码/类型/环节"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <div class="auditingInnerRow" :title="row.业务编码">
                  {{ row.业务编码 }}
                </div>
                <div class="auditingInnerRow" :title="row.name">
                  {{ row.name }}
                </div>
                <div class="auditingInnerRow" :title="row.当前节点">
                  环节: {{ row.当前节点 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="申请人/时间/金额"
              width="114"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <div class="auditingInnerRow">{{ row.创建人名称 }}</div>
                <div class="auditingInnerRow">{{ row.创建日期 }}</div>
                <div class="auditingInnerRow auditingAmount">
                  [{{ $formatMoney(row.申请金额) }}]
                </div>
              </template>
            </el-table-column>
            <el-table-column label="其他"></el-table-column>
          </el-table>
        </div>
      </div>
    </template>
    <template #mainContent>
      <div
        id="wf-audit-content"
        :class="`wf-audit-content
                    ${isAuditHasTab ? ' wf-audit-content-multiple-tabs' : ''}
                    ${
                      showRegularFormHeader === true
                        ? ' showRegularFormHeader'
                        : ''
                    }`"
      >
        <div class="wf-audit-content-empty" v-show="isAuditContentEmpty">
          <img class="empty_img" src="@/assets/image/empty.png" alt="" />
          <p class="empty_p">暂无数据</p>
        </div>
        <div class="wf-audit-detail" v-show="!isAuditContentEmpty">
          <div class="no-detail-content-block" v-show="!showDetailAuditContent">
            <el-button
              plain
              icon="el-icon-caret-left"
              disabled
              class="content-message"
            >
              {{ detailAuditContentMessage }}
            </el-button>
          </div>
          <div
            class="wf-audit-detail-final"
            ref="tabsBox"
            v-show="showDetailAuditContent"
          >
            <div style="height: 100%" v-if="isUsingBlockView">
              <el-tabs
                v-model="blockValue"
                :type="tabsType"
                class="formAudit-tabs"
                :id="blockTabsStatic ? '' : 'hiddenTab'"
                style="height: 100%"
              >
                <el-tab-pane
                  v-if="blockTabsStatic"
                  label="审核详情"
                  name="details"
                >
                  <block-view
                    ref="blockViews"
                    v-show="showTabs === 'blockView'"
                  />
                  <form-canvas
                    ref="againCanvas"
                    v-show="showTabs === 'formCanvas'"
                  />
                </el-tab-pane>
                <el-tab-pane
                  :label="blockTabsStatic ? '单据详情' : '基本信息'"
                  name="first"
                >
                  <block-view v-if="isShowDetail" ref="blockView" @modifyTabs="modifyTabs" />
                </el-tab-pane>
              </el-tabs>
            </div>
            <component
              v-if="!isUsingBlockView && !isMultipleAuditTabs"
              ref="auditDetailComponent"
              :is="loadDynamicComp(theComponentName)"
              :keyId="billId"
            />
            <el-tabs
              v-show="wrongBlockTabs"
              v-model="wrongBlockValue"
              :type="tabsType"
              class="formAudit-tabs"
              :id="blockTabsStatic ? '' : 'hiddenTab'"
              style="height: 100%"
              @tab-click="handleBlockTabs"
            >
              <el-tab-pane
                v-if="blockTabsStatic"
                label="审核详情"
                name="details"
              >
                <block-view
                  ref="blockViews"
                  v-show="showTabs === 'blockView'"
                />
                <form-canvas
                  ref="againCanvas"
                  v-show="showTabs === 'formCanvas'"
                />
              </el-tab-pane>
              <el-tab-pane label="单据详情" name="first">
                <el-tabs
                  ref="formAuditTabs"
                  v-model="formAuditTabsActiveName"
                  :type="tabsType"
                  class="formAudit-tabs"
                  style="height: 100%"
                  v-if="!isUsingBlockView && isMultipleAuditTabs"
                  @tab-click="handleTabClick"
                >
                  <el-tab-pane
                    v-for="tab in filterTabs"
                    :key="tab.name"
                    :ref="tab.ref"
                    :label="tab.label"
                    :name="tab.name"
                    :disabled="tab.disabled"
                    style="height: 100%"
                  >
                    <component
                      :ref="tab.compRef"
                      :is="loadDynamicComp(tab.comp)"
                      v-bind="bindCompAttr[tab.compRef]"
                    />
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <div style="position: relative">
          <div
            class="retract-block retract-block-audit"
            style="cursor: pointer; top: 41%"
            title="显示审核区域"
            @click="isExpend = !isExpend"
          >
            <i
              class="el-icon-arrow-left"
              v-if="isExpend"
              style="font-size: 13px; cursor: pointer"
            />
            <i
              class="el-icon-arrow-right"
              v-else
              style="font-size: 13px; cursor: pointer"
            ></i>
          </div>
        </div>
        <div
          class="wf-audit-right-action mleft-20"
          :class="{ 'wf-audit-right-action-fold': isExpend }"
          v-show="!isAuditContentEmpty"
        >
          <div class="wf-audit-extend">
            <div
              class="wf-audit-block"
              style="height: 100%"
              id="wf-audit-block"
            >
              <b-page ref="basePageWfAuditBlock">
                <template #mainContent>
                  <blockTab
                    ref="blockTabRef"
                    :currentRow="currentRow"
                    :dataType="dataType"
                    :billId="billId"
                    :metaId="metaId"
                    :tabs.sync="blockTabs"
                    isAudit
                    @initCallBack="initCallBack"
                    :getAuditTable="getBaseListTable"
                  >
                    <div
                      v-show="composeShowAuditBlockForm"
                      class="wf-audit-block-form"
                      style="
                        height: 360px;
                        border: 1px solid #dddddd;
                        padding: 10px;
                        margin-top: 10px;
                        overflow: auto;
                      "
                    >
                      <audit-comp
                        ref="auditComp"
                        :auditFiles="auditFiles"
                        :isUsingBlockView="isUsingBlockView"
                        @wrapDataAndAction="wrapDataAndAction"
                        @setBlockTabs="setBlockTabs"
                        @changeShowUploadFile="changeShowUploadFile"
                      />
                    </div>
                  </blockTab>
                </template>
              </b-page>
            </div>
          </div>
        </div>
      </div>
    </template>
  </b-page>
</template>

<script>
import $ from 'jquery'
import { getTabsType } from '@/utils'
import { mapState, mapGetters } from 'vuex'
import vueFiles from '@/constant/vueFiles.json'
import { transferCamelCaseName, containsChineseOrDigit } from '@/utils'
import { store } from '@/utils/sunmei-data'
export default {
  name: 'b-list-wf-detail',
  props: {
    isShowDetail: {
      type: Boolean,
      default: false
    },
    dataType: {
      type: String,
      default: 'CformDataEntity'
    },
    dataApiKey: {
      type: String,
      default: ''
    },
    showAuditBlockForm: {
      type: Boolean,
      default: true
    },
    getBaseListWf: {
      type: Function,
      default: function() {}
    },
    getBaseList: {
      type: Function,
      default: function() {}
    },
    isNotCformBiz: {
      type: Function,
      default: function() {}
    },
    showBaseEditDlg: {
      type: Function,
      default: function() {}
    },
    getBaseEditDlgObjContent: {
      type: Function,
      default: function() {}
    },
    changeBaseEditDlgObjContentName: {
      type: Function,
      default: function() {}
    }
  },
  provide() {
    return {
      getShowAuditBlockForm: () => this.composeShowAuditBlockForm && this.showUploadFile,
      tableAuditing: () => this.$refs.tableAuditing,
      syncAuditData: () => this.syncAuditData,
      getSaveApiExtraObj: (ids, pass) => this.getSaveApiExtraObj(ids, pass),
      showErrorIfForm: (result) => this.showErrorIfForm(result),
      reLoadAnyway: () => this.reLoadAnyway(),
      getVo4AuditEditSave: () => this.vo4AuditEditSave(),
      showAuditDetailCallback: (show) => this.showAuditDetailCallback(show),
      getBlockTabRef: () => this.$refs.blockTabRef
    }
  },
  inject: {
    changeIsShowDetail: { default: undefined },
    currentInstance: { default: undefined },
    provideMenuId: { default: undefined }
  },
  // destroyed() {
  //   window.sessionStorage.removeItem(
  //     this.$route.path.split('/').pop() + '_pathParams'
  //   )
  // },
  // deactivated() {
  //   window.sessionStorage.removeItem(
  //     this.$route.path.split('/').pop() + '_pathParams'
  //   )
  // },
  data() {
    return {
      // 是否显示审核详情的上传审核附件按钮
      showUploadFile: false,
      isExpend: false,
      tabsOne: false,
      tabsTow: false,
      oneInfo: {},
      towInfo: {},
      blockValue: 'first',
      showTabs: '',
      blockTabsStatic: false,
      wrongBlockValue: 'first',
      wrongBlockTabs: false,
      auditComponentName: '',
      callbackInitAuditDetailComponent: undefined,
      theComponentName: '',
      auditContent: '',
      getVo4AuditEditSave: undefined,
      newDataVoLockVersion: undefined,
      canAuditEdit: false, // 当前是否可以编辑
      billId: '',
      metaId: '',
      auditFiles: [],
      row: {},
      currentRow: undefined,
      showDetailAuditContent: false, // 是否显示详情内容：选中单个审核对象时显示
      noAuditSelectedContentMessage: '请选择要审核的项目',
      detailAuditContentMessage: this.noAuditSelectedContentMessage,
      FORM_TYPE_eq: '',
      dataParams: {},
      tableAuditingList: [],
      dataInited: false, // 是否已查询过审核列表
      reloadTableCallback: undefined,
      showRegularFormHeader: false,
      syncAuditData: undefined, // 这个方法由外界指定，用于取回节点数据时提供外界使用
      isAuditContentEmpty: true,
      exData: {}, // 用于额外存储数据
      tableId: new Date().getTime() + '',
      buttonsEx: [],
      refreshButtonsDisableDataKey: '',
      refreshButtonsDisableDataParams: {},

      // 如果是审核多tab模式，则formAuditTabs[0]=主表单tab标题，
      // formAuditTabs[x]=其他tab组件名称
      formAuditTabs: [],
      isNoAuditFileOnTab: false,
      isNoAuditFileOnTabAnyway: undefined, // 由其他组件强制设置审核时没有tab附件
      formAuditTabsIndexes: {}, // tabKey对应索引映射表
      tabNamesHideByConfig: [],
      auditTabParams: undefined,
      formAuditTabsComponents: {}, // tabKey对应组件映射表
      formAuditTabsActiveName: '',
      isShowAuditFileTabWhenInit: false, // 单个审核对象加载时，是否直接显示附件页签
      auditFileTabName: '', // 审核附件页签的名称
      auditFileObjOnRight: undefined, // 审核界面中，右下方位的审核附件组件
      auditFileObjOnTab: undefined, // 审核界面中，在审核主区域页签的审核附件组件
      isNoAuditFileOnRight: false, // 隐藏右侧审核附件区域
      isSelectedMoreThanOneRow: false,
      isUsingCformTab: false,
      isUsingBlockView: false, // 是否使用区块制单展示单据详情
      auditAttTab: 'audit-file-tab', // 审核附件组件tab名称 默认只显示附件
      tabsType: '',
      // 新增搜索组件需要的数据 ===start
      options: [
        {
          value: '业务编码',
          label: '单据编码'
        },
        {
          value: '申请金额',
          label: '金额'
        },
        {
          value: '创建人ID',
          label: '申请人'
        },
        {
          value: '创建部门编码',
          label: '申请部门'
        }
      ],
      search: '',
      searchType: '业务编码',
      moneyOptions: [
        {
          value: 'gte',
          label: '>='
        },
        {
          value: 'lte',
          label: '<='
        },
        {
          value: 'eq',
          label: '='
        }
      ],
      moneyType: 'gte',
      isSearch: false,
      searchData: [],
      showTreeInput: false,
      treeData: {},
      checkIds: [],
      // 新增搜索组件需要的数据 ===end,
      blockTabs: [],
      auditDetailParams: {},
      tabs: [],
      bindCompAttr: {
        auditDetailComponent: {
          keyId: this.billId
        }
      },
      detailButtons: [],
      detailBtAfters: {},
      detailHiddenButtons: []
    }
  },
  computed: {
    ...mapState({
      applicant: (store) => store.user.applicant,
      applicationDept: (store) => store.user.applicationDept
    }),
    ...mapGetters([
      'menuInfoMap'
    ]),
    isSelect() {
      return ['业务编码', '申请金额'].includes(this.searchType)
    },
    showMoneySelect() {
      return this.searchType === '申请金额'
    },
    tableData() {
      return this.isSearch ? this.searchData : this.tableAuditingList
    },
    changeInputType() {
      return this.searchType === '申请金额' ? 'number' : 'text'
    },
    isAuditHasTab() {
      // 审核区域是否有多tab：普通多tab，或者是CformTab机制
      return (
        this.isUsingCformTab ||
        (this.isMultipleAuditTabs && !this.isSelectedMoreThanOneRow)
      )
    },
    isMultipleAuditTabs() {
      return (
        !this.isUsingCformTab &&
        this.formAuditTabs !== undefined &&
        this.formAuditTabs.length > 1
      )
    },
    getTable() {
      return this.isShowDetail
        ? this.$refs.tableAuditing
        : this.getBaseList?.()?.getTable()
    },
    filterTabs() {
      return this.tabs.filter((tab) => tab.show)
    },
    formAuditTabsExcludeMain() {
      // 除去第一个表单后的其他的tab的元数据
      return this.tabs.slice(1) || []
    },
    composeShowAuditBlockForm() {
      const hiddenStatuMap = {
        '已作废': true,
        '被退回': true,
        '审核结束': true
      }
      return this.showAuditBlockForm && !hiddenStatuMap[this.row?.status]
    }
  },
  methods: {
    changeShowUploadFile(showUploadFile) {
      this.showUploadFile = showUploadFile
    },
    // 控制区块单tabs显示隐藏
    modifyTabs(flag, data) {
      this.blockTabsStatic = flag
      this.blockValue = this.blockTabsStatic ? 'details' : 'first'
      if (flag) {
        this.tabsOne = !!data.data.colItems
        this.oneInfo = data.data
        this.handleTabs(data)
      }
    },
    // 调用接口
    handleTabs(data) {
      const apikey =
        'specialAuditSctreen&isBlockView=' +
        data.attributes.isBlockView +
        '&auditScreenMetaId=' +
        data.attributes.auditScreenMetaId
      this.$callApi(
        apikey,
        {
          ...data.data
        },
        (result) => {
          if (result.attributes.specialAudit.containers) {
            this.tabsTow = false
            this.showTabs = 'blockView'
            const params = {
              jumpToSaveFormData: {
                cFormVo: result.attributes.specialAudit
              },
              isApply: false,
              isEdit: false,
              mode: '详情'
            }
            this.$nextTick(() => {
              this.$refs.blockViews.init(undefined, params)
            })
          } else if (result.attributes.specialAudit.colItems) {
            this.tabsTow = true
            this.towInfo = result.attributes.specialAudit
            this.showTabs = 'formCanvas'
            this.$nextTick(() => {
              this.$refs.againCanvas.initByDataVo(
                result.attributes.specialAudit,
                '详情',
                undefined,
                undefined,
                undefined,
                undefined
              )
            })
          }
          return true
        }
      )
    },
    // 区块tabs点击事件
    handleBlockTabs() {
      if (!this.wrongBlockTabs) {
        return
      }
      if (this.tabsTow && this.tabsOne) {
        window.icLuckysheet.destroy()
        this.$nextTick(() => {
          this.$refs.againCanvas.initByDataVo(
            this.wrongBlockValue === 'details' ? this.towInfo : this.oneInfo,
            '详情',
            undefined,
            undefined,
            undefined,
            undefined
          )
        })
      }
    },
    vo4AuditEditSave() {
      if (typeof this.getVo4AuditEditSave !== 'function') {
        this.$message.error('this.getVo4AuditEditSave 必须是函数')
        return
      }
      return this.getVo4AuditEditSave(this)
    },
    getBaseListTable() {
      // 返回表格对象
      return this.$refs.tableAuditing
    },
    setTheComponentName(name = '') {
      this.theComponentName = name
      if (this.theComponentName && this.$isNotEmpty(this.tabs)) {
        this.tabs[0].comp = this.theComponentName
      }
    },
    initAuditTabs(initParams) {
      if (this.$isEmpty(initParams.formAuditTabs)) {
        initParams.formAuditTabs = []
      }
      this.formAuditTabs = this.$clone(initParams.formAuditTabs) || []
      if (this.isNotCformBiz?.()) {
        // 非表单业务不需要通用的tab机制
        return
      }

      if (initParams.params.FORM_TYPE_eq === '报销单') {
        // 报销单显示附件和发票
        this.auditAttTab = 'audit-file-invoice-tab'
      }
      if (this.isNoAuditFileOnTab === false) {
        if (this.$isEmpty(this.formAuditTabs)) {
          this.formAuditTabs.push('基本信息')
        }
        this.formAuditTabs.push(this.auditAttTab + ':附件')
      }

      if (initParams.useFormDetailsAsAuditTabs === true) {
        // 使用表单详情tab作为审核多tab，如果设置审核附件使用tab，
        // 则审核附件会代替详情中的附件tab
        var tabsFromDetailDlg = this.$resolveFormAuditTabs(
          initParams.params.FORM_TYPE_eq
        )
        if (this.isNoAuditFileOnTab === false) {
          this.formAuditTabs = []
          tabsFromDetailDlg.forEach((tab) => {
            if (tab.indexOf(':附件') < 0) {
              this.formAuditTabs.push(tab)
            } else {
              this.formAuditTabs.push(this.auditAttTab + ':附件')
            }
          })
        } else {
          this.formAuditTabs = tabsFromDetailDlg
        }
      }

      this.tabs = []
      this.formAuditTabsIndexes = {}
      if (this.isMultipleAuditTabs) {
        // 多页签审核时，设置页签索引与名称、标题的对应关系
        this.auditFileTabName = ''
        for (let i = 0; i < this.formAuditTabs.length; i++) {
          let name = this.formAuditTabs[i]
          let label = name
          if (name.indexOf(':') > -1) {
            const tabTokens = name.split(':')
            name = tabTokens[0]
            label = tabTokens[1]
          }
          const formAuditTabFlag = 'formAuditTab' + i
          const compRef = i
            ? 'formAuditTabComponent' + i
            : 'auditDetailComponent'
          const comp = i ? name : this.theComponentName
          const tab = {
            key: name,
            ref: formAuditTabFlag,
            label,
            name,
            compRef,
            comp,
            disabled: true,
            show: false
          }
          this.tabs.push(tab)
          this.formAuditTabsIndexes[name] = i
          const tabNameNoIndex = this.$resolveTabNameNoIndex(name)
          this.formAuditTabsIndexes[tabNameNoIndex] = i
          if (label === '附件') {
            // 审核附件页签的label固定是“附件”
            this.auditFileTabName = name
          }

          this.setAuditTabNameLabelIndex(i, name, label)
          this.setAuditTabVisibleIndex(i, true, false)
        }

        // 设置初始时显示哪个页签
        this.setFormAuditTabsActiveName()
      }
    },
    setFormAuditTabsActiveName() {
      // 初始化时设置显示的页签
      let activeTab = this.tabs[0]?.name
      if (
        this.isShowAuditFileTabWhenInit &&
        this.$isNotEmpty(this.auditFileTabName)
      ) {
        activeTab = this.auditFileTabName
      }
      this.formAuditTabsActiveName = activeTab
    },
    setAuditTabVisibleIndex(index, isVisible, showError = true) {
      // 多tab审核时设置页签的可见性
      if (this.$isNotEmpty(index)) {
        if (showError && index === 0) {
          this.$message.error('不能隐藏主表单页签')
          return
        }
        if (this.tabs[index].show !== isVisible) {
          this.tabs[index].show = isVisible
          return true
        }
      }
    },
    setAuditTabNameLabelIndex(index, name, label) {
      // 多tab审核时设置页签的name和label
      if (this.$isNotEmpty(index)) {
        this.tabs[index].name = name
        this.tabs[index].label = label
      }
    },
    setAuditTabLabelBayName(name, label) {
      for (let i = 0; i < this.tabs.length; i++) {
        const tab = this.tabs[i]
        if (tab.name === name) {
          tab.label = label
          break
        }
      }
    },
    setEditTabVisible(tabKey, isVisible, isByConfig) {
      // 如果是系统参数已经隐藏的tab，则不再响应setEditTabVisible的处理
      if (this.tabNamesHideByConfig.indexOf(tabKey) > -1) {
        return
      }

      // 记录系统参数决定隐藏的tab
      if (isVisible === false && isByConfig === true) {
        this.tabNamesHideByConfig.push(tabKey)
      }
      // 多tab制单时设置页签的可见性
      // 注意：第一个页签的tabKey与标题相同，除第一个之外，tabKey=页签的组件name
      var index = this.formAuditTabsIndexes[tabKey]
      var isHit = this.setAuditTabVisibleIndex(index, isVisible)
      if (isHit === true && isVisible === true) {
        this.$nextTick(() => {
          var tabComp = this.$refs[`formAuditTabComponent${index}`]
          if (tabComp && this.auditTabParams) {
            this.$tabsGetRef(tabComp).init(undefined, this.auditTabParams)
          }
        })
      }
    },
    checkPatchAudit(checkedRows, showErrorMessage) {
      // 检查是否可以批量审核
      var checkResult = true
      checkedRows = checkedRows || this.$getTableSelection(this.getTable)
      if (checkedRows.length > 1) {
        var currentNode
        var wfMeatId
        for (var i = 0; i < checkedRows.length; i++) {
          if (!wfMeatId) {
            wfMeatId = checkedRows[i].wfMetaId
          } else if (checkedRows[i].wfMetaId !== wfMeatId) {
            checkResult = false
            if (showErrorMessage) {
              this.$message.error('不同流程不能进行批量操作')
            }
            break
          }
          if (!currentNode) {
            currentNode = checkedRows[i].currentNode
          } else if (checkedRows[i].currentNode !== currentNode) {
            checkResult = false
            if (showErrorMessage) {
              this.$message.error('不同业务或不同节点不能进行批量操作')
            }
            break
          }
        }
      }
      return checkResult
    },
    baseListBtClick(button) {
      button.params.rows = this.$getTableSelection(this.$refs.tableAuditing)
    },
    resetAuditEdit(dataVo) {
      if (this.$isNotEmpty(dataVo.extData['审核编辑数据'])) {
        this.canAuditEdit = true
        this.setAuditBtVisible('保存修改', true)
      }
    },
    tableAuditingListCheck() {
      // 刷新审核内容
      var rows = this.$getTableSelection(this.$refs.tableAuditing)
      // init审核
      this.$refs.listTodo.rowChecked(rows, this.$isEmpty(this.tableData))
      this.$refs.basePageWfAuditBlock.rowChecked(
        rows,
        this.$isEmpty(this.tableData)
      )

      this.isSelectedMoreThanOneRow = false
      this.isAuditContentEmpty = rows.length === 0
      this.setAuditTabLabelBayName(this.auditAttTab, '附件')
      if (rows.length === 1) {
        this.showDetailAuditContent = true
        this.billId = rows[0].id
        this.metaId = rows[0].metaId
        this.currentRow = rows[0]
        this.row = rows[0]
        this.formAuditTabsComponents = {}

        const viewId = rows[0].viewId
        const cformId = rows[0].cformId
        this.isUsingBlockView = this.$isNotEmpty(viewId)
        if (this.isUsingBlockView) {
          this.canAuditEdit = false
          this.setAuditBtVisible('保存修改', false)
          this.$refs.listTodo.setBtProperty(
            '打印',
            'disabled',
            rows[0].isPrint === '否'
          )
          var $root = this.$getObjRoot(this)
          var initParamsSaved = this.$getInitParams($root)
          var params = {
            isApply: false,
            isEdit: true,
            isAuditMode: true,
            mode: '审核',
            dataId: cformId || this.billId,
            viewId: viewId,
            isShowTabs: initParamsSaved?.isShowTabs || true,
            callbackAfterFormLoaded: (dataVo) => {
              if (this.$isNotEmpty(dataVo.extData['审核编辑数据'])) {
                this.canAuditEdit = true
                this.setAuditBtVisible('保存修改', true)
              }
            }
          }
          this.$nextTick(() => {
            this.$refs.blockView.init(undefined, params)
          })
        } else {
          // 可设置auditComponentName为函数，
          // 动态获取审核组件名称之后再回调加载详情组件
          var callbackAfterComponentName = (componentName) => {
            this.setAuditBtVisible('保存修改', false)
            this.$refs.listTodo.setBtProperty(
              '打印',
              'disabled',
              rows[0].isPrint === '否'
            )
            this.setTheComponentName(componentName)
            this.$nextTick(() => {
              if (this.isNotCformBiz?.()) {
                // 非表单业务处理
                this.showNotCformBiz(rows[0])
              } else {
                this.callbackInitAuditDetailComponent(
                  this,
                  (auditExtendName, exData, specialAudit) => {
                    // dataVo是新查询出来的表单数据
                    exData = exData || {}
                    var auditTabParams = exData.auditTabParams || {}
                    var dataVo = auditTabParams.dataVo

                    this.tabNamesHideByConfig = []
                    this.canAuditEdit = false
                    // specialAudit接口传递过来的tabs页数据
                    if (specialAudit?.attributes.auditScreenMetaId) {
                      this.tabsOne = true
                      this.oneInfo = specialAudit.data
                      this.blockTabsStatic = true
                      this.wrongBlockTabs = true
                      this.wrongBlockValue = 'details'
                      this.handleTabs(specialAudit)
                    } else {
                      this.blockTabsStatic = false
                      this.wrongBlockTabs = true
                      this.wrongBlockValue = 'first'
                    }
                    if (dataVo) {
                      dataVo.extData.tabNames = this.formAuditTabs
                      dataVo.extData.setEditTabVisible = this.setEditTabVisible
                      dataVo.extData.setEditTabLabel =
                        this.setAuditTabLabelBayName
                      this.$setDetailDlgAndAuditTabVisible(dataVo)
                      this.resetAuditEdit(dataVo)
                    }
                    const hasAuditExtend = containsChineseOrDigit(auditExtendName) ? vueFiles[auditExtendName] : vueFiles[transferCamelCaseName(auditExtendName)]

                    if (
                      this.$isNotEmpty(auditExtendName) &&
                      this.$isNotEmpty(hasAuditExtend) &&
                      this.$refs.auditDetailComponent
                    ) {
                      // 表明当前环境有审核扩展组件
                      this.$tabsGetRef(
                        this.$refs.auditDetailComponent
                      ).nodeName = this.$refs.auditComp.auditData.nodeName
                    }

                    // 处理多tab审核的初始化
                    if (this.isMultipleAuditTabs) {
                      var tname
                      this.formAuditTabsComponents[this.formAuditTabs[0]] =
                        this.$tabsGetRef(this.$refs.auditDetailComponent)

                      for (
                        let index = 0;
                        index < this.formAuditTabsExcludeMain.length;
                        index++
                      ) {
                        tname = this.formAuditTabsExcludeMain[index].name
                        this.formAuditTabsComponents[tname] =
                          this.$refs[`formAuditTabComponent${index + 1}`]
                      }

                      // 初始化除主界面之外的其他页签
                      for (
                        let i = 0;
                        i < this.formAuditTabsExcludeMain.length;
                        i++
                      ) {
                        tname = this.formAuditTabsExcludeMain[i].name

                        var tabComp = this.$tabsGetRef(
                          this.formAuditTabsComponents[tname]
                        )
                        if (tabComp?.init) {
                          // 审核时页签使用init，与制单多页签有区别，是由于审核的页签
                          // 可以直接使用业务详情弹框上的页签，而这些页签的方法之前已经是init
                          // 所以此处使用这些页签需要保持与详情使用兼容
                          const $root = this.$getObjRoot(this)
                          const initParamsSaved = this.$getInitParams($root)
                          auditTabParams.isApply = initParamsSaved.isApply
                          auditTabParams.nodeName =
                            this.$refs.auditComp.auditData.nodeName
                          tabComp.init(
                            undefined,
                            auditTabParams,
                            this.$tabsGetRef(this.$refs.auditDetailComponent)
                          )
                          if (dataVo) {
                            // 记录这个对象，以便在setEditTabVisible中使用
                            this.auditTabParams = auditTabParams
                          }

                          // 获取auditFileObjOnTab，使其auditFileObjOnRight关联
                          var tabLabel = this.formAuditTabsExcludeMain[i].label
                          if (tabLabel === '附件') {
                            // 获取auditFileObjOnTab
                            this.$nextTick(() => {
                              var refObjs = tabComp.$refs
                              if (this.$isNotEmpty(refObjs)) {
                                var refObjsKeys = Object.keys(refObjs)
                                refObjsKeys.forEach((k) => {
                                  var obj = refObjs[k]
                                  if (
                                    this.$getComponentName(obj) ===
                                    'attach-audit-extend'
                                  ) {
                                    this.auditFileObjOnTab = obj
                                    if (
                                      this.$isNotEmpty(this.auditFileObjOnRight)
                                    ) {
                                      this.auditFileObjOnTab.anotherAuditFileObj =
                                        this.auditFileObjOnRight
                                      this.auditFileObjOnRight.anotherAuditFileObj =
                                        this.auditFileObjOnTab
                                    }
                                  }
                                })
                              }
                            })
                          }
                        }
                      }
                      this.setFormAuditTabsActiveName()
                    }
                  }
                )
              }
            })
          }
          if (typeof this.auditComponentName === 'function') {
            var newName = this.auditComponentName(
              this,
              callbackAfterComponentName
            )
            if (typeof newName === 'function') {
              this.setTheComponentName(newName)
            }
          } else {
            callbackAfterComponentName(this.auditComponentName)
          }
        }
      } else {
        this.isSelectedMoreThanOneRow = true
        this.showDetailAuditContent = false
        this.billId = ''
        this.setTheComponentName()

        this.detailAuditContentMessage =
          rows.length > 1
            ? `已选择 ${rows.length} 个审核项目`
            : this.noAuditSelectedContentMessage
      }
      this.$refs.auditComp.selectAuditData(rows)
      this.syncRowHeightLightWithCheckRows(rows)
    },
    syncRowHeightLightWithCheckRows(checkRows) {
      // 把勾选行进行高亮背景显示
      var $trs = $(`#${this.tableId} tr.el-table__row`)
      $trs.removeClass('checkedTR')

      var indexMap = {}
      for (let rIndex = 0; rIndex < this.tableData.length; rIndex++) {
        var rowId = this.$getRowId(this.tableData[rIndex])
        indexMap[rowId] = rIndex
      }

      for (let i = 0; i < checkRows.length; i++) {
        var index = this.$getRowId(checkRows[i])
        var rIndex = indexMap[index]
        $($trs[rIndex]).addClass('checkedTR')
      }
    },
    setBlockTabs(data = {}, isReset = false) {
      if (isReset) {
        this.blockTabs = []
      }
      if (this.$isNotEmpty(data)) {
        this.blockTabs.unshift(data)
      }
    },
    getSaveApiExtraObj(ids, pass) {
      let extra
      if (this.canAuditEdit) {
        extra = {
          getExParamsCallApiSave: (data) => {
            var passParamStr = ''
            if (pass !== undefined) {
              passParamStr = `&_PASS=${pass}`
            }
            return `&ids=${ids}&dataType=${this.dataType}${passParamStr}`
          }
        }
      } else {
        if (this.isUsingBlockView && !extra) {
          // 区块表单指标模块有默认可修改项
          const vo = this?.$refs?.blockView?.blockView
          const handlers = vo?.containers[0]?.blocks?.filter(
            (e) => e.handlerKey === '行表单'
          )
          if (handlers) {
            handlers.forEach((h) => {
              h.pageData.columns.forEach((e) => {
                const editableType = e.editableType
                const notEditableTypeMap = {
                  'a': true,
                  'html': true
                }
                if (e.editable && !this.canAuditEdit && !notEditableTypeMap[editableType]) {
                  // 只要有可修改的元素
                  this.canAuditEdit = true
                  extra = this.getSaveApiExtraObj(ids, pass)
                }
              })
            })
          }
        }
      }
      return extra
    },
    showErrorIfForm(result) {
      if (this.isUsingBlockView) {
        return this.$refs.blockView.showError(result)
      } else {
        if (
          this.$refs.auditDetailComponent &&
          typeof this.$tabsGetRef(this.$refs.auditDetailComponent).showError ===
            'function'
        ) {
          return this.$tabsGetRef(this.$refs.auditDetailComponent).showError(
            result
          )
        } else {
          return false
        }
      }
    },
    reLoadAnyway() {
      // 强制刷新审核列表
      this.dataInited = false
      this.reLoad(true)
    },
    clickRow(row) {
      var isChecked = false
      var rows = this.$getTableSelection(this.$refs.tableAuditing)
      rows.forEach((item) => {
        if (item.ID === row.ID) {
          isChecked = true
          return false // break
        }
      })
      if (!isChecked) {
        // 原先是选中状态时，不改变勾选项。
        // 否则，先把之前的勾选项清除，然后再勾选当前行
        this.$refs.tableAuditing.clearSelection()
        this.$nextTick(() => {
          this.$refs.tableAuditing.toggleRowSelection(row)
        })
      }
    },
    reLoad(isTabClick) {
      // 切换页签，在已经加载过待审核数据时，不会重新加载
      var rows = this.$getTableSelection(this.$refs.tableAuditing)

      if (isTabClick === true && !this.dataInited) {
        this.$callApiParams(
          this.dataApiKey,
          this.getReloadListParams(),
          (result) => {
            this.tableAuditingList = result.data.rows
            var param = this.getReloadListParams()
            param.dataApiKey = this.dataApiKey
            this.getBaseListWf?.()?.setTabName(param)
            this.dataInited = true
            this.isAuditContentEmpty = this.$isEmpty(this.tableAuditingList)
            if (!this.isAuditContentEmpty) {
              this.$nextTick(() => {
                if (!this.isShowDetail) return
                // eslint-disable-next-line no-unused-vars
                var toggleRowId = this.$getRowId(this.tableAuditingList[0])
                if (this.reloadTableCallback) {
                  var callbackData = this.reloadTableCallback(
                    result,
                    this.$refs.tableAuditing
                  )
                  if (callbackData && callbackData.toggleRowId) {
                    toggleRowId = callbackData.toggleRowId
                  }
                }
                if (this.search) {
                  this.enterSearch()
                } else {
                  this.updateSelectedRow(this.tableAuditingList)
                }
              })
            } else if (this.isShowDetail) {
              this.backToList()
            }
            return true
          }
        )
      } else if (
        this.$isNotEmpty(this.tableAuditingList) &&
        this.$isNotEmpty(rows)
      ) {
        this.tableAuditingListCheck()
      }
    },
    setAuditBtVisible(btText, isVisible) {
      // 设置主审核区域按钮的可见性
      this.$refs.listTodo.setBtProperty(btText, 'visible', isVisible)
    },
    refreshButtonsDisableData(ids, callback) {
      // 后端刷新按钮额外数据
      if (
        this.$isNotEmpty(this.refreshButtonsDisableDataKey) &&
        this.dataType === 'CformDataEntity'
      ) {
        this.$nextTick(() => {
          var params = Object.assign({}, this.refreshButtonsDisableDataParams)
          params.ids = ids.join(',')
          this.$callApiParams(
            this.refreshButtonsDisableDataKey,
            params,
            (result) => {
              const listTodo = this.$refs.listTodo
              // 以前使用的basePageWfAuditDetail，改爲列表頁后使用listTodoDetail
              listTodo.refreshButtonsDisableDataResult = result

              if (this.$isNotEmpty(ids)) {
                // ids是空表明是初始化还没填充数据，不要设置禁用数据
                listTodo.buttonsDisableData = result.data || {}
              }

              // 设置按钮可见性
              this.$nextTick(() => {
                var hiddenButtons = result.attributes.hiddenButtons || []
                listTodo.hiddenButtons = hiddenButtons

                hiddenButtons.forEach((btText) => {
                  this.setAuditBtVisible(btText, false)
                })
                if (callback) {
                  callback()
                }
              })
              return true
            }
          )
        })
      } else {
        if (callback) {
          callback()
        }
      }
    },
    getReloadListParams() {
      var $root = this.$getObjRoot(this)
      var initParamsSaved = this.$getInitParams($root)
      const statusText = initParamsSaved.isApply ? '送审' : '审核'
      const statusMap = {
        '待办': `未${statusText}`,
        '已办': `已${statusText}`,
        '全部': '全部'
      }
      const STATUS = statusMap[initParamsSaved.tab]
      return {
        ...initParamsSaved.params,
        dataType: this.dataType,
        FORM_TYPE_eq: this.FORM_TYPE_eq,
        extParam: this.dataParams.extParam ? this.dataParams.extParam : {},
        size: 20,
        current: 1,
        STATUS,
        actionKey: 'selectWfList'
      }
    },
    searchTypeChange(searchType) {
      this.searchData = []
      this.showTreeInput = false
      if (this.$isNotEmpty(this.search)) {
        this.search = ''
        this.enterSearch()
      }
      if (this.$isNotEmpty(this.checkIds)) {
        this.checkIds = []
        this.resetData()
      }
      // 切换申请人 和 申请部门不同的下拉数据
      const toggleData = {
        创建人ID: {
          optionsData: this.applicant,
          prop: 'CREATE_USER_NAME_like'
        },
        创建部门编码: {
          optionsData: this.applicationDept,
          prop: '部门_like'
        }
      }
      if (Object.keys(toggleData).includes(searchType)) {
        this.handleTreeData({
          optionsData: toggleData[searchType].optionsData,
          prop: toggleData[searchType].prop
        })
        this.showTreeInput = true
      }
    },
    // 切换金额范围 搜索输入框不为空则重新筛选数据
    moneyTypeChange() {
      if (this.$isNotEmpty(this.search)) {
        this.enterSearch()
      }
    },
    // 提取默认选中第一行的方法
    updateSelectedRow(data, row) {
      var ids = this.$getTableCheckedIdsOrCformId(data)
      this.refreshButtonsDisableData(ids, () => {
        // 初始化后勾选数据行的处理，会触发对按钮可用性和可见性的处理
        if (this.$refs.tableAuditing) {
          this.$refs.tableAuditing.clearSelection()
          if (this.$isNotEmpty(row)) {
            this.$refs.tableAuditing.toggleRowSelection(row, true)
            return
          }
          if (this.$isNotEmpty(this.$getRowId(data[0]))) {
            for (let i = 0; i < data.length; i++) {
              if (this.$getRowId(data[i]) === this.$getRowId(data[0])) {
                this.$refs.tableAuditing.toggleRowSelection(data[i], true)
                break
              }
            }
          }
        }
      })
    },
    // 回车对数据进行筛选
    enterSearch(request = true) {
      const list = [...this.tableAuditingList]
      if (this.$isNotEmpty(this.search)) {
        this.isSearch = true
        this.searchData = list.filter((item) => {
          if (this.searchType === '申请金额') {
            // 金额处理
            const map = {
              gte: +item[this.searchType] >= +this.search,
              lte: +item[this.searchType] <= +this.search,
              eq: +item[this.searchType] === +this.search
            }
            return map[this.moneyType]
          } else {
            return item[this.searchType].indexOf(this.search) !== -1
          }
        })
        this.updateSelectedRow(this.searchData)
      } else {
        this.resetData(request)
      }
    },
    getApplicationData() {
      this.$store.dispatch('getApplicant')
      this.$store.dispatch('getDept')
    },
    checkClassParent(nodes) {
      // this.searchType 目前只有2种情况 '创建人ID' || '创建部门编码'
      this.checkIds = nodes
        .filter((node) => !node.isParent)
        .map((item) => (this.searchType === '创建人ID' ? item.id : item.code))
    },
    handleTreeData(data) {
      const { optionsData, prop } = data
      const treeData = {
        treeSetting: {
          check: {
            enable: true
          },
          data: {
            simpleData: {
              enable: true,
              idKey: 'id',
              pIdKey: 'parentId'
            },
            key: {
              name: 'name'
            }
          }
        },
        btnSwitch: {
          showEdit: false,
          showRefresh: false,
          showExpandAll: false
        },
        optionsData, // applicationDept || applicant
        prop // prop: 'CREATE_USER_NAME_like' || 部门_like
      }
      this.treeData = treeData
    },
    handleFilter() {
      if (this.$isNotEmpty(this.checkIds)) {
        this.isSearch = true
        const list = [...this.tableAuditingList]
        this.searchData = list.filter((item) => {
          return this.checkIds.includes(item[this.searchType])
        })
        this.updateSelectedRow(this.searchData)
      } else {
        this.resetData()
      }
    },
    resetData(request = true) {
      this.isSearch = false
      request && this.updateSelectedRow(this.tableAuditingList)
    },
    handleTabClick() {
      // 切换到基本信息所在的tab则绑定事件 反之解绑事件
      this.$nextTick(() => {
        if (
          this.$isNotEmpty(this.$refs.formAuditTab0) &&
          this.$refs.auditDetailComponent
        ) {
          if (this.$refs.formAuditTab0[0].$el.style.display === 'none') {
            this.$tabsGetRef(
              this.$refs.auditDetailComponent
            ).$refs?.formFormat?.removeEventListeners?.()
          } else {
            this.$tabsGetRef(
              this.$refs.auditDetailComponent
            ).$refs?.formFormat?.addEventListeners?.()
          }
        }
      })
    },
    backToList() {
      //  返回时把搜索内容情况，防止在列表页点击被详情页过滤的数据查看详情，详情页找不到你那天条数据
      this.search = ''
      this.enterSearch(false)
      this.changeTabDisabled()
      this.changeIsShowDetail(false)
    },
    init(initParams) {
      // 初始化时请求系统人员和部门 用于申请人和申请部门的下拉数据
      this.getApplicationData()
      if (initParams.codeSearch) {
        this.search = initParams.codeSearch
      }

      this.refreshButtonsDisableDataKey =
        initParams.refreshButtonsDisableDataKey
      this.refreshButtonsDisableDataParams =
        initParams.refreshButtonsDisableDataParams
      this.auditComponentName = initParams.auditComponentName
      this.callbackInitAuditDetailComponent =
        initParams.callbackInitAuditDetailComponent
      if (!initParams.noNeedAuditDetail) {
        if (this.$isEmpty(this.auditComponentName) && !this.isNotCformBiz()) {
          this.$message.error(
            '审核待办页面时，initParams.auditComponentName不能为空'
          )
          return
        }

        if (
          typeof this.callbackInitAuditDetailComponent !== 'function' &&
          !this.isNotCformBiz()
        ) {
          this.$message.error(
            '审核待办页面时，initParams.callbackInitAuditDetailComponent必须是函数'
          )
          return
        }
      }

      this.isNoAuditFileOnTabAnyway = initParams.isNoAuditFileOnTabAnyway
      this.showRegularFormHeader = initParams.showRegularFormHeader
      this.reloadTableCallback = initParams.reloadTableCallback
      // 缺票提醒首页跳转到报销审核详情问题
      const query = this.$getPathQuery()
      if (this.$isNotEmpty(query)) {
        const reloadTableCallback = initParams.reloadTableCallback
        initParams.reloadTableCallback = (result, table, baseList) => {
          // 获取提跳转参数
          const rows = result.data?.rows || []
          const pathQuery = this.$getPathQuery()
          if (
            rows.length &&
            this.$isNotEmpty(pathQuery) &&
            pathQuery.tab === '待办'
          ) {
            initParams.jumpRow = rows[0]
            table.toggleRowSelection(rows[0], true)
            this.changeIsShowDetailFunc(true, rows[0])
          }
          // 删除提跳转参数
          this.$removePathQuery()
          reloadTableCallback?.(result, table, baseList)
        }
      }

      this.auditContent = initParams.auditContent

      const needProcessCallBackResult = initParams.needProcessCallBackResult
      if (initParams.needProcessCallBackResult) {
        // 由后端返回是否审核附件使用页签形式
        this.isNoAuditFileOnTab =
          needProcessCallBackResult.attributes['审核附件不使用页签'] === true
        if (this.isNoAuditFileOnTabAnyway) {
          this.isNoAuditFileOnTab = true
        }

        this.isShowAuditFileTabWhenInit =
          needProcessCallBackResult.attributes['审核附件页签优先显示'] === true

        this.isNoAuditFileOnRight =
          needProcessCallBackResult.attributes['隐藏右侧审核附件'] === true

        this.initAuditTabs(initParams)
      }

      if (this.dataType === 'CformDataEntity') {
        // 是表单时才增加
        this.FORM_TYPE_eq = initParams.params.FORM_TYPE_eq
      }
      this.dataParams = initParams.params
      this.getVo4AuditEditSave = initParams.getVo4AuditEditSave

      const isNotTabTodo = initParams.isNotTabTodo || false
      if (!isNotTabTodo) {
        // 设审核右侧的tab数据
        if (initParams.setRightBlockTab) {
          this.blockTabs = initParams.setRightBlockTab(this.blockTabs)
        }
      }
    },
    saveDetailButtons(initParamsSaved) {
      this.detailButtons = this.$isNotEmpty(initParamsSaved.buttons) ? [...initParamsSaved.buttons] : []
      this.detailBtAfters = this.$isNotEmpty(initParamsSaved.btAfters) ? { ...initParamsSaved.btAfters } : {}
      this.detailHiddenButtons = this.$isNotEmpty(initParamsSaved.hiddenButtons) ? [...initParamsSaved.hiddenButtons] : []
      if (this.$isEmpty(this.detailHiddenButtons)) {
        const hiddenButtons = []
        this.detailHiddenButtons = this.detailButtons.map(item => {
          if (!item.visible) {
            hiddenButtons.push(item.text)
          }
        })
        this.detailHiddenButtons = hiddenButtons
      }
    },
    showWfDetail(initParamsSaved, row, isReload, exData = {}) {
      // 设置详情初始化所需参数
      this.init(initParamsSaved)
      this.$nextTick(() => {
        if (!isReload) {
          this.saveDetailButtons(initParamsSaved)
        }
        // 初始化按钮
        this.tabsType = getTabsType(this.$refs.tabsBox)
        const saveButtons = this.detailButtons
        const saveBtAfters = this.detailBtAfters
        const saveHiddenButtons = this.detailHiddenButtons
        const params = {
          btAfters: { ...saveBtAfters },
          showTree: true,
          buttons: [...saveButtons],
          hiddenButtons: [
            ...saveHiddenButtons,
            '详情',
            '列设置',
            '高级查询'
          ]
        }
        const detailButtons = [
          {
            text: '返回',
            icon: 'el-icon-arrow-left',
            enabledType: '0',
            click: (bt) => this.backToList()
          }
        ]
        const hiddenBtnFunc = (btns) => {
          btns.forEach(btn => {
            if (!params.hiddenButtons.includes(btn)) {
              params.hiddenButtons.push(btn)
            }
          })
        }
        if (initParamsSaved.isApply) {
          const hiddenApplyButtons = [
            '新增',
            '修改',
            '送审',
            '删除'
          ]
          hiddenBtnFunc(hiddenApplyButtons)
        } else {
          const hiddenAuditButtons = [
            '审核',
            '详情审核',
            '批量审核'
          ]
          hiddenBtnFunc(hiddenAuditButtons)
          detailButtons.push({
            text: '保存修改',
            icon: 'el-icon-edit',
            enabledType: '1',
            click: (bt) => this.doSaveWhenAudit()
          })
        }
        if (this.$isNotEmpty(params.buttons)) {
          params.buttons.unshift(...detailButtons)
        }
        if (this.isShowDetail) {
          const index = params.buttons.findIndex(item => item.text === '显示模式' && item.visible)
          if (index !== -1) {
            params.buttons[index].disabled = false
          }
        }
        this.$saveInitParams(this, params)
        const menuId = this.provideMenuId?.() || store.get('menuData').getMenuId() // 菜单id

        if (this.$isNotEmpty(this.menuInfoMap[menuId]?.leftCollapsed)) {
          params.leftCollapsed = this.menuInfoMap[menuId].leftCollapsed
        } else {
          params.leftCollapsed = true
        }
        this.$refs.listTodo.init(params)
        this.currentInstance()?.setButtonNormalNoPaddingTop(true)
        this.currentInstance()?.$refs.searchForm.changeShowSearch(false)

        // 进入审核详情 审核详情组件顶部tab切换到第一个
        this.formAuditTabsActiveName = this.tabs[0]?.name
        if (isReload) {
          this.reLoadAnyway()
          return
        }
        let rowsData = this.getBaseList?.()?.$refs?.baseList
          ? this.getBaseList?.()?.$refs?.baseList.rowsData
          : this.getBaseList?.()?.rowsData
        if (this.$isNotEmpty(exData.auditingList)) {
          rowsData = exData.auditingList
        }
        this.tableAuditingList = rowsData || []
        this.$nextTick(() => {
          this.updateSelectedRow(this.tableAuditingList, row)
        })
      })
    },
    showAuditDetailCallback(show) {
      this.changeTabDisabled(show)
    },
    wrapDataAndAction(bool, doActionHandlerFinal) {
      this.$refs.blockView.wrapDataAndAction(bool, doActionHandlerFinal)
    },
    showNotCformBiz(row, isDlg) {
      // 非表单业务显示审核详情
      if (this.$isEmpty(this.auditContent)) {
        this.$message.error('非表单业务必须设置auditContent')
        return
      }

      this.setTheComponentName()
      this.changeBaseEditDlgObjContentName?.()
      this.$nextTick(() => {
        let getFormComponent
        if (!isDlg) {
          // 非弹框详情
          this.setTheComponentName(this.auditContent)
          getFormComponent = () => {
            return this.$tabsGetRef(this.$refs.auditDetailComponent)
          }
        } else {
          this.changeBaseEditDlgObjContentName?.(this.auditContent)
          getFormComponent = () => {
            return this.getBaseEditDlgObjContent?.()
          }
        }

        this.$nextTick(() => {
          const params = {
            titlePrefix: '',
            isEdit: false,
            isNoDlg: !isDlg,
            row: row,
            getFormComponent: getFormComponent
          }
          this.showBaseEditDlg?.(params)
        })
      })
    },
    initCallBack(data) {
      if (this.$isNotEmpty(data.auditFiles)) {
        this.auditFiles = data.auditFiles
      } else {
        this.auditFiles = []
      }
    },
    changeTabDisabled(disabled = true) {
      this.filterTabs?.forEach((tab) => {
        tab.disabled = disabled
      })
    },
    doSaveWhenAudit() {
      var doActionHandler = (vo) => {
        if (vo.formType === '合同') {
          vo.extData.baDetail = this.$tabsGetRef(
            this.$refs.formAuditTabComponent1
          )?.$refs?.cmRelevantBaList?.baDetail
          vo.extData.contracpartyInfo = this.$tabsGetRef(
            this.$refs.formAuditTabComponent2
          )?.$refs?.contracpartylist?.extData?.contracparty
          vo.extData.planInfo = this.$tabsGetRef(
            this.$refs.formAuditTabComponent2
          )?.$refs?.contractPlan?.planData
        } else if (vo.formType === '报销单') {
          if (vo.extData.bas.length === 1) {
            vo.extData.incPayeeData = []
            this.$tabsGetRef(
              this.$refs.formAuditTabComponent1
            )?.$refs?.baPayeelist?.extData?.incPayeeData?.map((item, index) => {
              vo.extData.incPayeeData.push({
                ...item,
                baId: vo.extData.bas[0].baId
              })
            })
          }
        }
        var checkedRows = this.$getTableSelection(this.$refs.tableAuditing)
        var ids = this.$getTableCheckedIdsStrBy(checkedRows)
        const extra = this.getSaveApiExtraObj(ids)
        if (
          this.$isNotEmpty(this.newDataVoLockVersion) &&
          !this.isUsingBlockView
        ) {
          vo.data.lockVersion = this.newDataVoLockVersion
        } else if (
          this.$isNotEmpty(this.newDataVoLockVersion) &&
          this.isUsingBlockView
        ) {
          vo.containers[0].blocks.forEach((block) => {
            if (block.handlerKey === '表单') {
              block.data.data.lockVersion = this.newDataVoLockVersion
              vo.containers[0].dataVo.data.lockVersion =
                this.newDataVoLockVersion
            }
          })
        }
        if (vo.containers?.[0]?.blocks?.[0]?.data?.extData && this.isUsingBlockView) {
          vo.containers[0].blocks[0].data.extData['审核编辑保存'] = '是'
        }
        this.$refs.listTodo.setBtProperty('保存修改', 'loading', true)
        this.$callApi(
          'WFSAVEWHENAUDIT',
          vo,
          (result) => {
            this.$refs.listTodo.setBtProperty('保存修改', 'loading', false)
            this.newDataVoLockVersion = result.attributes.lockVersion
            if (this.$isEmpty(this.newDataVoLockVersion)) {
              this.$message.error('保存成功后返回的版本号为空')
            }
          },
          (result) => {
            this.$refs.listTodo.setBtProperty('保存修改', 'loading', false)
            return this.showErrorIfForm(result)
          },
          extra
        )
      }
      this.$refs.auditComp.auditOrSaveWithEditVo(doActionHandler)
    },
    leftCollapsedClick(leftCollapsed) {
      const menuId = this.provideMenuId?.() || store.get('menuData').getMenuId() // 菜单id
      this.$store.commit('SET_MENU_INFO', {
        menuId,
        leftCollapsed
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.formAudit-tabs {
  display: flex;
  flex-direction: column;
  ::v-deep .el-tabs__content {
    flex: 1;
  }
}
.wf-audit-content {
  display: flex;
  height: 100%;
}
.mleft-20 {
  margin-left: 20px;
}
.wf-audit-content .wf-audit-detail {
  height: 100%;
  flex: 1;
  // margin-right: 10px;
  overflow: hidden;
}
.wf-audit-content .wf-audit-right-action {
  height: 100%;
  width: 390px;
  transition: all 0.5s ease-in-out;
}
.wf-audit-content .wf-audit-right-action-fold {
  width: 0px;
}
.wf-audit-content ::v-deep .buttons-normal {
  padding-top: 0px !important;
}

.wf-audit-content ::v-deep .formTabSaveFile .buttons-normal {
  padding-bottom: 9px !important;
}

.wf-audit-content ::v-deep .column-bottom {
  height: calc(100% - 32px) !important;
}
.wf-audit-right-action {
  display: flex;
  flex-direction: column;
  margin-top: -4px;
  ::v-deep .el-tabs__nav-scroll {
    padding-top: 4px;
  }
}
.wf-audit-right-action .wf-audit-block {
  margin-bottom: 10px;
}
.wf-audit-right-action .wf-audit-block .wf-audit-block-form {
  height: 100%;
  border: 1px solid #bbb;
  padding: 15px 15px 10px 10px;
}
.wf-audit-right-action ::v-deep .wf-audit-block .el-form-item__label {
  font-size: 14px;
}
.wf-audit-right-action
  ::v-deep
  .wf-audit-block
  .el-input--small
  .el-input__inner {
  font-size: 14px;
  padding: 0px 5px;
  height: 28px;
  line-height: 28px;
}
.wf-audit-right-action ::v-deep .wf-audit-block .el-input__suffix {
  right: -1px;
}
.wf-audit-right-action ::v-deep .wf-audit-block .el-form-item--mini.el-form-item,
.wf-audit-right-action
  ::v-deep
  .wf-audit-block
  .el-form-item--small.el-form-item {
  margin-bottom: 2px;
}
.wf-audit-right-action ::v-deep .wf-audit-block .audit-common-words button {
  font-size: 14px;
  margin-right: -4px;
  padding: 5px 8px;
}
.wf-audit-content-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100% + 20px);
  width: 100%;
  margin-top: -20px;
  background: #e9e9e9;
  border: 1px solid #dddddd;
  .empty_img {
    width: 64px;
    height: 64px;
  }
  .empty_p {
    font-size: 14px;
    color: #999999;
    margin-top: 5px;
  }
}

.wf-audit-right-action .wf-audit-extend {
  overflow: hidden;
  flex: 1;
}
.no-detail-content-block {
  height: 100%;
  border: 1px solid #ddd;
  background: #e9e9e9;
  padding: 20px;
}
.wf-audit-content .retract-block-audit {
  position: absolute !important;
  z-index: 100000 !important;
  right: -20px !important;
  top: 14px;
  left: unset;
}
.wf-audit-content button.content-message {
  border: 1px solid #dddddd !important;
  border-color: #dddddd !important;
}
button.content-message ::v-deep span,
button.content-message ::v-deep i {
  color: #666 !important;
}
.tableAuditingList ::v-deep .el-table {
  height: 100% !important;
  font-size: 12px;
  border: none !important;
}
.tableAuditingList ::v-deep .el-table .el-table__body-wrapper {
  height: calc(100% - 30px) !important;
}
.tableAuditingList ::v-deep .el-table .auditingInnerRow {
  height: 18px;
  line-height: 18px;
  overflow: hidden;
}
.tableAuditingList ::v-deep .el-table .auditingAmount {
  font-weight: 800;
}
.tableAuditingList .el-table ::v-deep .cell {
  font-size: 12px;
}

// ::v-deep .el-timeline-item__tail{
//   border-left: 2px solid #0bbd87;
// }
::v-deep .el-timeline-item__timestamp {
  line-height: 2;
}
.tableAuditingContainer {
  display: flex;
  flex-direction: column;
  height: inherit;
  overflow: hidden;
  .tableAuditingList {
    flex: 1;
    overflow: hidden;
  }
}
.auditForm {
  ::v-deep .el-form-item.is-error {
    .el-textarea {
      .el-textarea__inner {
        border-color: #f56c6c !important;
      }
    }
  }
}

.wf-audit-block ::v-deep .common-page .receptacle-border .column-top {
  position: absolute;
  right: 0px;
  z-index: 10000;
}
.wf-audit-block ::v-deep .single-main .main-border {
  padding-top: 0px;
}
#tableAuditingSearch {
  display: flex;
  align-items: center;
  padding: 5px;
  #supTree {
    flex: 1;
    ::v-deep .el-input__inner {
      font-size: 14px;
      border-radius: 0 2px 2px 0;
      border-left: transparent !important;
    }
  }
  .searchTypeSelect {
    $width: 85px;
    width: $width;
    flex: 0 0 $width;
    ::v-deep .el-input__inner {
      font-size: 14px;
      border-radius: 2px 0 0 2px;
    }
  }
  .moneySelect {
    $width: 55px;
    width: $width;
    flex: 0 0 $width;
    ::v-deep .el-input__inner {
      font-size: 14px;
      border-radius: 0 2px 2px 0;
      border-left: transparent !important;
    }
  }
  .el-input {
    flex: 1;
    margin: 0;
    ::v-deep .el-input__inner {
      border-radius: 0 2px 2px 0;
      border-left: transparent !important;
    }
  }
}
</style>
<style lang="scss">
#hiddenTab > .el-tabs__header:nth-child(1) {
  display: none;
}
</style>
