<template>
  <div id="audit-file-tab" class="audit-file-tab">
    <attach-audit-extend ref="attachAudiExtend"/>
  </div>
</template>

<script>
import AttachAuditExtend from '../bizz/file/attach-audit-extend'
export default {
  name: 'audit-file-tab',
  components: { AttachAuditExtend },
  data() {
    return {
    }
  },
  methods: {
    init(dummyObj, params) {
      this.$refs.attachAudiExtend.setTableSizeMedium()
      this.$refs.attachAudiExtend.init(params.dataVo, params.nodeName, true)
    }
  }
}
</script>
