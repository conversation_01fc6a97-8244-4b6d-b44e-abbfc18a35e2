<template>
  <el-dialog
    append-to-body
    title="退回"
    :visible.sync="isDialog"
    width="420px"
    :close-on-click-modal='false'
    @close="handleClose">
    <div class="common-page">
      <el-form label-width="86px" :model="auditData" :rules="rules" ref="backForm">
        <div style="display: flex" v-show = "showAuditSpecify">
          <el-form-item label="退回节点:" style="flex: 1;" prop="returnNodeId">
            <el-select  v-model="auditData.returnNodeId" clearable style="width: 254px;">
              <el-option
                v-for="item in backHisNodes"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                >
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <div style="display: flex">
          <el-form-item label="退送模式:" style="flex: 1;" prop="backType">
            <el-select label="退送模式:" filterable v-model="auditData.backType" clearable style="width: 254px;">
              <el-option
                v-for="(item, index) in backTypes"
                :key="index"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <template #footer>
      <el-button class="btn-normal" @click="handleClose()"> 取消</el-button>
      <el-button class="btn-normal" type="primary" @click="handleSubmit()"> 退回修改</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'base-list-wf-back',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    backTypes: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    dialog(bool) {
      this.isDialog = bool
    }
  },
  data() {
    return {
      isDialog: this.dialog,
      auditData: { returnNodeId: '' },
      backHisNodes: [],
      showAuditSpecify: true,
      rules: {
        returnNodeId: [
          { required: this.showAuditSpecify, message: '请选退回节点', trigger: 'change' }
        ],
        backType: [
          { required: true, message: '请选择退送模式', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init(backHisNodes, auditData, showAuditSpecify) {
      this.backHisNodes = backHisNodes
      this.$set(this.auditData, 'returnNodeId', '')
      this.$set(this.auditData, 'backType', '')
      // this.auditData.returnNodeId = ''
      // this.auditData.backType = ''
      if (this.$isNotEmpty(auditData)) {
        this.auditData = { ...this.auditData, ...auditData }
      }
      // for (const [key, value] of Object.entries(auditData)) {
      //   // this.$set(auditData, key, value)
      // }
      this.showAuditSpecify = showAuditSpecify
    },
    handleClose() {
      this.dialog = false
      this.$emit('update:dialog', false)
    },
    handleSubmit() {
      this.$refs['backForm'].validate((valid) => {
        if (valid) {
          this.$emit('submit', this.auditData, this.auditData.backType)
          this.handleClose()
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
