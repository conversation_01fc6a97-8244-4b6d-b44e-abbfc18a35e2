<template>
  <el-form
    label-width="120px"
    :model="auditExtendData"
    class="auditForm"
    :disabled="auditFormDisabled"
    :rules="rules"
    :ref="ref"
  >
    <!-- 扩展表单内容 -->
    <el-form-item label="aaa" prop="aaa">
      <el-input
        v-model="auditExtendData.aaa"
        placeholder="请输入aaa"
      />
    </el-form-item>
    <el-form-item label="bbb" prop="bbb">
      <el-select label="bbb" filterable v-model="auditExtendData.bbb" clearable>
        <el-option
          v-for="item in items"
          :key="item.id"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'audit-extend-form',
  data() {
    return {
      ref: 'auditExtendRef',
      auditFormDisabled: false,
      items: [{ id: 1, name: 'b1' }, { id: 2, name: 'b2' }],
      auditExtendData: {
        aaa: '',
        bbb: ''
      }, // 扩展表单数据
      rules: {
        aaa: [
          { required: true, message: '请输入aaa', trigger: 'blur' }
        ],
        bbb: [
          { required: true, message: '请选择bbb', trigger: 'change' }
        ]
      } // 扩展表单校验规则
    }
  },
  methods: {
    init(params) {
      // auditFormDisabled 表单disabled状态
      // result wfActionCformDataEntity接口返回的数据
      const { auditFormDisabled, result } = params
      this.auditFormDisabled = auditFormDisabled
      console.log('初始化审核扩展表单组件', auditFormDisabled, result)
    },
    /**
     * 校验表单(支持异步处理)
     * @return {Boolean} 返回true校验通过 false不通过
     */
    validForm() {
      // 额外校验表单逻辑处理（非异步处理）
      return true
      // 额外校验表单逻辑处理（异步处理）
      // return new Promise((reslove, reject) => {
      //   try {
      //     // 校验条件 resolve(true) 校验成功 resolve(false) 校验失败
      //     if (Math.random() > 0.5) {
      //       reslove(true)
      //     } else {
      //       this.$message.error('校验表单失败')
      //       reslove(false)
      //     }
      //   } catch (error) {
      //     // 报错处理
      //     reject(error)
      //   }
      // })
    },
    // 获取扩展表单数据
    getExtendData() {
      return this.auditExtendData
    }
  }
}
</script>

<style></style>
