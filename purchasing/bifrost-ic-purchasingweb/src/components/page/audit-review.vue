<template>
  <el-dialog
    append-to-body
    title="审核"
    width="50%"
    :visible.sync="dialogVisible"
    :close-on-click-modal='false'
    :before-close="handleClose"
  >
    <audit-comp
      ref="auditComp"
      :key="auditCompkey"
      :isDialog="true"
      :isBatch="isBatch"
      @changeAuditFormDisabled="changeAuditFormDisabled"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleOk" :disabled="buttonDisabled">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'audit-review',
  provide() {
    return {
      handleDialogClose: () => this.handleClose()
    }
  },
  data() {
    return {
      auditCompkey: Date.now(),
      dialogVisible: false,
      buttonDisabled: false,
      isBatch: false, // 是否是批量审核
      submitData: {}
    }
  },
  methods: {
    getData() {
      const auditCompRef = this.$refs.auditComp
      const auditStatus = auditCompRef.auditData.auditResult
      // 审核状态映射表
      const auditStatusMap = {
        '退回修改': '0',
        '审核通过': '1',
        '加签': '2'
      }
      auditCompRef.$refs['audit'].validate((valid) => {
        // eslint-disable-next-line no-empty
        if (valid) {
          if (auditStatusMap[auditStatus] === '0') {
            auditCompRef.backAudit()
          } else {
            auditCompRef.doAudit(auditStatusMap[auditStatus])
          }
        }
      })
    },
    changeAuditFormDisabled(disabled) {
      this.buttonDisabled = disabled
    },
    handleOk() {
      this.getData()
    },
    handleClose(done) {
      this.dialogVisible = false
      this.$refs.auditComp.$refs.uploadFile.resetRemoveFileIds()
      this.auditCompkey = Date.now()
    },
    handleOpen(rows) {
      this.isBatch = rows.length > 1
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.auditComp.selectAuditData(rows)
      })
    }
  }
}
</script>

<style></style>
