<template>
  <base-list-cform ref="baseListCform"/>
</template>
<script>
import BaseListCform from './base-list-cform'
export default {
  name: 'base-list-bizflow-cform-sublist',
  components: { BaseListCform },
  data() {
    return {
      parentListObj: undefined
    }
  },
  methods: {
    init(params, parentListObj) {
      params.deleteSubData = () => {
        const listTodo = this.$refs.baseListCform.$refs.baseListWfApply?.$refs?.listTodo || {}
        const selsects = this.$getTableSelection(listTodo.getTable?.())
        const ids = this.$getTableCheckedIdsStrBy(selsects)
        listTodo.doActionByIds(`撤销:个` + params.子业务表单类型, 'WFDELETE',
          {ids: ids, dataType: 'CformDataEntity'})
      }
      // params是主列表的initParams
      const subBizType = params.子业务表单类型
      if (this.$isEmpty(subBizType)) {
        this.$message.error('子业务类型不能为空')
        return
      }
      this.parentListObj = parentListObj

      // 占位符实现没有对应子业务ID时，后端返回空列表
      const subBizId = params.子业务ID ? params.子业务ID : '占位符'
      const initParamsSubList = {}
      initParamsSubList.showPager = false
      initParamsSubList.hideTabHeader = true

      // 克隆是避免子列表的处理影响到主列表
      initParamsSubList.params = this.$cloneDeep(params.params) || {}
      initParamsSubList.params.当前是业务流转的子列表查询 = '是'
      initParamsSubList.params.同时查询申请和审核数据 = '是'
      initParamsSubList.params.dataApiKey = 'wfActionCformDataEntity'
      initParamsSubList.params.FORM_TYPE_eq = subBizType
      initParamsSubList.params.extParam = initParamsSubList.params.extParam || {}
      initParamsSubList.params.extParam.BIZID_eq = subBizId

      initParamsSubList.buttons = []
      initParamsSubList.buttons.push(
        { text: '撤回', icon: 'el-icon-back', enabledType: '1+', click: bt => {
          this.$refs.baseListCform.doActionByIds(
            '撤回', 'WFWITHDRAW',
            undefined, { dataType: params.子业务类型 })
        } })
      initParamsSubList.hiddenButtons = ['新增', '打印', '导出', '导出打印文件', '导出PDF', '导入']
      const subListHiddenButtons = params.subListHiddenButtons || []
      subListHiddenButtons.forEach(item => initParamsSubList.hiddenButtons.push(item))
      initParamsSubList.btAfters = {
        '修改': 'HEAD', '删除': 'HEAD',
        '送审': '删除', '撤回': '送审', '审核历史': '撤回', '详情': '审核历史' }

      // 点击修改按钮的响应，实现跳转去执行编辑
      initParamsSubList.btModifyClick = {}
      initParamsSubList.btModifyClick.click = (row, bt) => {
        if (params.jumpToEditForm) {
          params.jumpToEditForm(this.$getRowId(row), bt)
        }
      }

      // 删除之后不自动刷新子列表，而是刷新主列表
      initParamsSubList.donotReloadTableAfterDelete = true
      initParamsSubList.callbackSuccessDelete = result => {
        this.parentListObj.reloadTable()
      }

      this.$saveInitParams(this, initParamsSubList)
      this.$refs.baseListCform.init(initParamsSubList)
    },
    mainTableSelectRowChanged(rows) {
      let subBizId = '占位符'
      const initParamsSubList = this.$getInitParams(this)
      if (this.$isNotEmpty(rows) && rows[0]['子业务ID'] !== undefined) {
        subBizId = rows[0]['子业务ID']
        // 如果已经存在对应的子业务，初始化后自动勾选
        initParamsSubList.highlightRowId = subBizId
      }
      initParamsSubList.params.extParam.BIZID_eq = subBizId
      initParamsSubList.reloadTable?.()
    },
    mainTableReload(initParams, exParams) {
      // 主列表重新加载，子列表也需要重新加载
      this.mainTableSelectRowChanged([])
    }
  }
}
</script>
