<template>
  <b-list ref="baseList" @handleSearch="handleSearch" id="report">
    <template v-if="showTtitle" #contentHeader>
      <div class="contentHeader">
        <div class="reportTitle">
          {{
            DataMounth && initParams.showTitleMonth ? reportTitle.replace('#Month', DataMounth)
              : reportTitle.replace('#Month', '')
          }}
        </div>
        <div class="enterpriseName"><span>{{ initParams.isSupplier ? '供应商名称：' : '编制单位：' }}{{ enterpriseName }}</span>
          <span style="margin-right:10px">金额单位：元</span></div>
      </div>
    </template>
    <template #contentFooter>
      <div class="contentFooter">
        <div class="contentFooterLevel" v-for="(sumLevel, index) in sumData" :key="index">
          <div class="reportSumCell"
               v-for="(sumCell, index) in sumLevel"
               :key="index"
               :style="`width:${sumCell.width}px; ${sumCell.style};`">
            {{ sumCell.text }}
          </div>
        </div>
      </div>
    </template>
  </b-list>
</template>

<script>
import printJS from 'print-js'
import $ from 'jquery'
import BList from './base-list'
import domtoimage from '../../utils/domToImage'

export default {
  name: 'b-report',
  components: { BList },
  props: {
    showTtitle: {
      type: Boolean,
      default: true
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      reportTitle: '',
      enterpriseName: '',
      bottomContentHeight: 0,
      sumData: [],
      initParams: {},
      DataMounth: '',
      isPrint: true
    }
  },
  methods: {
    init(initParams) {
      initParams = initParams || {}
      this.$callApiParams('getAccountName', {}, result => {
        this.enterpriseName = result.data
        return true
      })
      initParams.enterpriseName = initParams.isSupplier ? '' : initParams.enterpriseName
      initParams.params = initParams.params || {}
      initParams.buttons = initParams.buttons || []
      this.isPrint = this.$isEmpty(initParams.isPrint)
      if (this.isPrint) {
        initParams.buttons.push({
          text: '打印', icon: 'el-icon-printer', enabledType: '1',
          click: bt => this.$showExportWps(bt.getRowId(), bt.getRowData().formType, '', '打印')
        })
      }

      if (initParams.btDetailClick) {
        initParams.callbackRowDblclick = initParams.btDetailClick.click || (() => {
        })
      }
      this.reportTitle = initParams.reportTitle || '报表标题'
      this.enterpriseName = initParams.enterpriseName || ''

      this.bottomContentHeight = initParams.bottomContentHeight || 141

      initParams.exportExcelName = this.reportTitle
      this.$saveInitParams(this.$parent, initParams)
      this.$refs.baseList.init(initParams)
      this.$refs.baseList.listContentClass = 'listContent listReport'
      this.initParams = initParams
      initParams.reloadTableCallback = async(result) => { // 列表加载后才有分页参数给到params
        if (result?.attributes['报表']) {
          this.sumData = result?.attributes['报表']
        } else {
          const params = await initParams.getSearchParam()
          this.selectSunData(params)
        }
      }
    },
    selectSunData(param) {
      if (this.$isNotEmpty(this.initParams.params.dataApiKey)) {
        // 报表合计数据查询的ApiKey统一为：数据ApiKey + SumData
        var reportSumDataApi = this.initParams.params.dataApiKey + 'SumData'
        if (this.initParams.isSupplier) {
          this.enterpriseName = param && param['SUPPLIER_NAME_eq'] ? param['SUPPLIER_NAME_eq'] : ''
        }
        this.DataMounth = param && param['月份'] ? param['月份'] : ''
        var params = Object.assign(
          {}, this.initParams.params)
        if (params.apiKey) {
          delete params.apiKey
        }
        this.$callApiParams(reportSumDataApi, params, result => {
          this.sumData = result.data

          return true
        })
      }
    },
    handleSearch(param) {
      // this.selectSunData(param)
    }

  }
}
</script>

<style scoped lang='scss'>
#report {
  /deep/.listContent {
    flex: 1;
  }
}

.enterpriseName {
  display: flex;
  justify-content: space-between;
}
</style>
