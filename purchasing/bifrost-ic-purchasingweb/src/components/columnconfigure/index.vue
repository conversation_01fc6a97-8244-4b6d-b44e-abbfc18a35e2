<template>
  <b-page ref="page">
    <template #dbColLeft >
      <classify-ztree
        ref="classifytree"
        dataType="TableColumn"
        style="padding: 1px"
        :showNodeType="true"
        deleteApiKey="deleteClassifyTreeNode"
        :deleteExtraParams="{actionKey:'DELETE'}"
        :saveExtraParams="{actionKey: 'SAVE'}"
        @nodeClick="nodeClick"
        @leafNodeAdded="setCurrentItem"
        @nodeDeleted="nodeDeleted"/>
    </template>
    <template #mainContent>
      <div ref="columnPage" style="width: 100%;height: 100%">
        <div style="width: 100%;height: 100%" class="tableListSettingInPage tableListSetting mini-table">
          <div style="width: 100%;height: 100%" class="tableListSettingColSelected">
            <el-table
              ref="tableListSettingColAll"
              :data="tableListSettingColAllShow"
              border
              @selection-change="tableListSettingColAllChange"
              @row-click='tableListSettingColAllClick'
              style="width: 280px;height: 100%;">
              <el-table-column type="selection" width="25"></el-table-column>
              <el-table-column type="index" :label="orderNumber.label" align="center"/>
              <el-table-column label="表格列名" width="130" prop="prop" :editable="true">
                <template slot-scope="scope">
                  <el-input
                    :ref="scope.row.index + ',' + scope.column.index"
                    v-model="scope.row.label"
                  />
                </template>
              </el-table-column>
              <el-table-column label="要素名称" width="130" prop="prop" :editable="false">
                <template slot-scope="scope">
                  <el-input
                    :ref="scope.row.index + ',' + scope.column.index"
                    v-model="scope.row.prop"
                    :disabled='true'
                  />
                </template>
              </el-table-column>
              <el-table-column label="类型" width="150" prop="colType" :editable="true">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.colType" @change="() => colTypeChange(scope)"
                  >
                    <el-option
                      v-for="(item, index) in commonData.colTypes"
                      :key="index"
                      :label="item"
                      :value="item"/>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="列序号" width="120" prop="order" :editable="true">
                <template slot-scope="scope">
                  <el-input
                    :ref="scope.row.index + ',' + scope.column.index"
                    v-model="scope.row.order"
                  />
                </template>
              </el-table-column>
              <el-table-column label="列宽度" width="120" prop="width" :editable="true">
                <template slot-scope="scope">
                  <el-input
                    :ref="scope.row.index + ',' + scope.column.index"
                    v-model="scope.row.width"
                  />
                </template>
              </el-table-column>
              <el-table-column label="对齐方式" width="150" prop="align" :editable="true">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.align">
                    <el-option key="left" value="left" label="靠左对齐"/>
                    <el-option key="right" value="right" label="靠右对齐"/>
                    <el-option key="center" value="center" label="居中对齐"/>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="最小值" width="120" prop="min">
                <template slot-scope="scope">
                  <el-input
                    :ref="scope.row.index + ',' + scope.column.index"
                    oninput ="value=value.replace(/[^\d]/g,'')"
                    v-model="scope.row.min"
                  />
                </template>
              </el-table-column>
              <el-table-column label="最大值" width="120" prop="max" :editable="true">
                <template slot-scope="scope">
                  <el-input
                    :ref="scope.row.index + ',' + scope.column.index"
                    oninput ="value=value.replace(/[^\d]/g,'')"
                    v-model="scope.row.max"
                  />
                </template>
              </el-table-column>
              <el-table-column label="父级名称" width="120" prop="parentName" :editable="true">
                <template slot-scope="scope">
                  <el-input
                    :ref="scope.row.index + ',' + scope.column.index"
                    v-model="scope.row.parentName"
                  />
                </template>
              </el-table-column>
              <el-table-column label="是否排序" width="100" prop="sortable" :editable="true">
                <template slot-scope="scope">
                  <div style="display: flex;">
                    <div style="flex: 2;width: 65px;display: flex;">
                      <el-radio v-model="scope.row.sortable" :label="true">是</el-radio>
                      <el-radio v-model="scope.row.sortable" :label="false">否</el-radio>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="是否固定" width="100" prop="fixable" :editable="true">
                <template slot-scope="scope">
                  <div style="display: flex;">
                    <div style="flex: 2;width: 65px;display: flex;">
                      <el-radio v-model="scope.row.fixable" :label="true">是</el-radio>
                      <el-radio v-model="scope.row.fixable" :label="false">否</el-radio>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="是否合并" width="100" prop="isMerge" :editable="true">
                <template slot-scope="scope">
                  <div style="display: flex;">
                    <div style="flex: 2;width: 65px;display: flex;">
                      <el-radio v-model="scope.row.isMerge" :label="true">是</el-radio>
                      <el-radio v-model="scope.row.isMerge" :label="false">否</el-radio>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="是否导出" width="100" prop="exportable" :editable="true">
                <template slot-scope="scope">
                  <div style="display: flex;">
                    <div style="flex: 2;width: 65px;display: flex;">
                      <el-radio v-model="scope.row.exportable" :label="true">是</el-radio>
                      <el-radio v-model="scope.row.exportable" :label="false">否</el-radio>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="是否必填" width="100" prop="isRequired" :editable="true">
                <template slot-scope="scope">
                  <div style="display: flex;">
                    <div style="flex: 2;width: 65px;display: flex;">
                      <el-radio v-model="scope.row.isRequired" :label="true">是</el-radio>
                      <el-radio v-model="scope.row.isRequired" :label="false">否</el-radio>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="showPublicInfoAmount"
                               label="公示金额" prop="isSystem">
                <template slot-scope="scope">
                  <div style="display: flex;">
                    <div style="flex: 2;width: 65px;display: flex;">
                      <el-radio v-model="scope.row.isSystem" :label="true">是</el-radio>
                      <el-radio v-model="scope.row.isSystem" :label="false">否</el-radio>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </template>
  </b-page>
</template>
<script>
export default {
  name: 'columnconfigure-sd',
  data() {
    return {
      colItem: {}, // 当前编辑的列
      commonData: this.newCommonData(),
      btTableListSettingColText: '新增表格列', // 表格列设置保存按钮文本
      columnData: {}, // 列数据
      selectNodeId: '',
      orderNumber: { label: '序号', isShow: false },
      tableListSettingColAll: [], // 表格列数据
      tableListDataShow: [], // 选中的数据行
      showPublicInfoAmount: false,
      currentCell: null
    }
  },
  computed: {
    tableListSettingColAllShow() { // 表格列定义实际显示的数据
      var items = []
      if (this.$isNotEmpty(this.tableListSettingColAllSearchText)) {
        items = this.tableListSettingColAll.filter(item => {
          return String(item.label.toLowerCase())
            .match(this.tableListSettingColAllSearchText.toLowerCase())
        })
      } else {
        items = this.tableListSettingColAll
      }
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      const orderTem = Math.max.apply(Math, items.map(item => { return item.order })) + 1
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.order = this.$isEmpty(items) ? 0 : orderTem
      return this.$sortColumnList(items)
    }
  },
  mounted() {
    this.$refs.page.commonPageClassEx =
        this.$refs.page.commonPageClassEx + ' column-top-hide'
    this.$selectColSettingCommonData(result => {
      this.commonData.colTypes = result.attributes.colTypes
    })
    const buttons = [{ text: '新增行', icon: 'el-icon-circle-plus-outline',
      enabledType: '0', type: 'primary',
      click: bt => {
        this.btAddClick()
      }
    },
    { text: '删除行', icon: 'el-icon-delete', enabledType: '0',
      click: bt => {
        this.btDeleteClick()
      }
    },
    { text: '保存', icon: 'el-icon-folder', enabledType: '0',
      click: bt => {
        this.btTableListSettingSaveCol()
      }
    },
    { text: '导入', icon: 'el-icon-upload2', enabledType: '0',
      click: bt => {
        this.tableColumnDeptImport()
      }
    },
    { text: '导出', icon: 'el-icon-download', enabledType: '0',
      click: bt => {
        this.downLoadTableColumnExcelImp()
      }
    }]
    const params = {
      showTree: true,
      buttons
    }
    this.$refs.page.init(params)
  },
  methods: {
    colTypeChange(scope) {
      if (scope.row.colType === '文本') {
        scope.row.align = 'left'
      } else {
        scope.row.align = 'right'
      }
    },
    nodeClick(data, exData) { // needToReloadFreeJson表明是通过手动点击打开
      exData = exData || {}
      exData.needToReloadFreeJson = true
      this.selectNodeId = data.id
      this.$selectTableColumnList(this.selectNodeId,
        result => {
          this.tableListSettingColAll = result.data
        }, result => { })

      this.showPublicInfoAmount = false
      if (data.label.indexOf('公示：') > -1 || data.label.indexOf('公示:') > -1) {
        this.showPublicInfoAmount = true
      }
    },
    setCurrentItem(nodeId, itemKey) { // 设置当前对象
      this.currentId = (itemKey == null) ? '' : itemKey
      this.$refs.classifytree.setCurrentItem(nodeId, itemKey)
    },
    btAddClick() {
      if (this.$isEmpty(this.selectNodeId)) {
        this.$message.error('请先选择分类节点！')
        return
      }
      this.isTableListSettingColEditing = true
      this.colItem = {
        dataType: this.selectNodeId,
        label: '新增表格列',
        prop: '',
        order: this.order,
        align: 'left',
        colType: '文本',
        sortable: false,
        fixable: false,
        exportable: true,
        isRequired: false,
        isSystem: false,
        isMerge: false,
        min: 0,
        max: 30
      }
      this.tableListSettingColAll.push(this.colItem)
    },
    btDeleteClick() { // 删除表格列
      if (this.$isEmpty(this.tableListDataShow)) {
        this.$message.error('请先选择要删除的列定义！')
        return
      }
      this.deleteTableDta = this.getDeleteTableData() // 后端删除
      this.deleteRowTableData = this.tableListDataShow.filter((item) => item.id == null) // 前端删除
      if (this.$isNotEmpty(this.deleteRowTableData)) {
        this.deleteRowId = this.deleteRowTableData.map((item) => item.order)
        this.deleteRowId.forEach((items, index, arr) => {
          this.tableListSettingColAll = this.tableListSettingColAll.filter((item, index) => item.order !== items)
        })
      }
      if (this.$isNotEmpty(this.deleteTableDta)) {
        var $table = { selection: this.deleteTableDta }
        this.deleteColItems($table)
      }
      // this.deleteColItems(this.deleteTableDta)
    },
    tableColumnDeptImport() { // 导入
      if (this.$isEmpty(this.selectNodeId)) {
        this.$message.error('请先选择分类节点！')
        return
      }
      var apiKey = 'template/列定义导入模板.xls'
      var fileName = '列定义配置导入模板.xls'
      var tableColumn = []
      this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
        DATA_TYPE: this.selectNodeId,
        columnExcelType: '2',
        showPublicInfoAmount: this.showPublicInfoAmount,
        onSuccess: () => {
          this.selectTableColumnList()
        } })
    },
    downLoadTableColumnExcelImp() { // 导出
      if (this.$isEmpty(this.selectNodeId)) {
        this.$message.error('请先选择分类节点！')
        return
      }
      var idList = []
      for (let i = 0; i <this.tableListDataShow.length; i++) {
        var list = this.$getTableCheckedIds(this.tableListDataShow[i])
        if (this.$isNotEmpty(list)) {
          idList.push(list)
        }
      }
      var idStr = idList.join(',')
      var params = {
        doExcelExport: 'doExcelExport',
        exportExcelName: '列定义配置导出.xls',
        DATA_TYPE: this.selectNodeId,
        columnExcelType: '2'
      }
      if (this.$isNotEmpty(idStr)) {
        params.BIZID_in = idStr
      }
      this.$fileDownloadBykey('exportTabColumnDataExcel', params)
    },
    getDeleteTableData() {
      return this.tableListDataShow.filter((item) => item.id != null)
    },
    tableListSettingColAllChange(checkedRows) {
      this.tableListDataShow = checkedRows
    },
    tableListSettingColAllClick(row) {
      this.$refs.tableListSettingColAll.toggleRowSelection(row)
    },
    tableListSettingColAllDblclick(row, column, event) { // 双击表格列表行，则删除该表格列
      const $table = { selection: [{ id: row.id }] } // 构造模拟的table
      this.deleteColItems($table)
    },
    deleteColItems($table) { // 删除表格列
      this.isDeleting = true
      this.$deleteTableColumn(
        $table,
        result => {
          this.selectTableColumnList(re => {
            this.isDeleting = false
          })
        }
      )
    },
    selectTableColumnList() { // 刷新表格列列表
      this.tableListSettingColAllChange([]) // 清除原先编辑数据
      this.$selectTableColumnList(this.selectNodeId,
        result => {
          this.tableListSettingColAll = result.data
        }, result => {})
    },
    nodeDeleted(node) {
      if (this.selectNodeId === node.id) {
        this.tableListSettingColAll = []
        this.selectNodeId = ''
      }
    },
    btTableListSettingSaveCol(returnValue, callback) {
      this.errListDta = []
      const saveDataTableList = []
      if (this.$isEmpty(this.tableListSettingColAll)) {
        this.$message.error('请先新增列定义再保存！')
        return
      }
      this.tableListSettingColAll.forEach((items) => {
        if (this.$isEmpty(items.label)) {
          this.errListDta.push(items.order)
        }
        items.labelOrigin = items.label
        items.labelAlias = items.label
        // 清空ID bizid 重新新增数据
        saveDataTableList.push({ ...items, id: null, bizid: null })
      })
      if (this.$isNotEmpty(this.errListDta)) {
        this.$message.error('列序号：' + this.errListDta.toString() + ' 列定义请添加表格列名！')
        return
      }
      this.saveCols(saveDataTableList, returnValue, callback)// 保存有ID的
    },
    saveCols(cols, returnValue, callback) {
      this.columnListVo = { cols: [] }
      this.columnListVo.cols = cols
      this.$callApi('saveTableColumn', this.columnListVo,
        result => {
          this.selectTableColumnList(rt => {
            this.isTableListSettingColEditing = false
            if (typeof (callback) === 'function') {
              callback(rt)
            }
          })
          return returnValue
        }, undefined, {
          getExParamsCallApiSave: () => {
            return `&先清空相同dataType的列定义=` + this.selectNodeId
          }
        })
    },
    newCommonData() {
      return {
        cformCols: [], // 表单要素列表
        colTypes: [] // 表格列类型
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .click-el-card-border{
    border: 1px solid #5C96BC !important;
  }
  .tableListSettingColAll, .tableListSettingColSelected {
    margin-right: 10px;
  }
  .tableListSettingColEditButton { margin-bottom: 7px; }
  .tableListSetting { display: flex; }
  .tableListSettingColEditForm {
    border: 1px solid #bbb;
    height: calc(100% - 34px);
    width: 290px;
    padding: 10px;
    position: relative;
  }
  .common-page /deep/ .tableListSettingInPage { height: 100%; }
  .common-page /deep/ .tableListSettingInPage .tableListSettingColSelected .el-table {
    height: 100% !important;
    width: 100% !important;
  }
  .common-page /deep/ .tableListSettingInPage .el-icon-search { display: none; }
  thead .el-table-column--selection .cell {
    display: none!important;
  }
</style>

