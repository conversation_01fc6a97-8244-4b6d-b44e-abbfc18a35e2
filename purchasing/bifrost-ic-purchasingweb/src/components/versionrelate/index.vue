<template>
  <page ref="page">
    <template #pageContent>
      <LayoutTem isDbCol :isPageShow="false" ref="LayoutTem" :isFilterShow="false">
        <template #button>
          <div class="version-relate-buttons" ref="buttonArea" :style="buttonAreaStyle">
            <slot name="versionCommonButtons">
              <slot name="btNew">
                <el-button plain icon="el-icon-document-add" size="small" @click="btNewGuide(false)"
                  :disabled="allButtonDisabled"> 新增</el-button>
              </slot>
              <slot name="btSave">
                <el-button plain icon="el-icon-edit" size="small"
                  @click="btSave" :loading="isSaving"
                  :disabled="!canvasOpened || allButtonDisabled"> 保存</el-button>
                <el-popover
                  class="toolbar-button"
                  placement="bottom-end"
                  width="auto"
                  trigger="click"
                >
                  <div class="moreButtonContainer">
                    <el-button plain icon="el-icon-copy-document" size="small" @click="btCopy"
                        :disabled="!canvasOpened || allButtonDisabled || !currentId"> 复制</el-button>
                      <el-button plain icon="el-icon-copy-document" size="small" @click="btCopyToOtherBookSet" style="margin: 0;"
                                :disabled="!canvasOpened || allButtonDisabled || !currentId"> 复制到其他账套</el-button>
                  </div>
                  <el-button
                    slot="reference"
                    size="small"
                    plain
                  >
                    复制
                  </el-button>
                </el-popover>
              </slot>
              <slot name="btSwitchStatus">
                <el-button plain size="small"
                  @click="btSwitchStatus" :disabled="!canvasOpened || allButtonDisabled || !currentId">
                    <span :class="versionStatusClass"/>{{versionStatus}}
                </el-button>
              </slot>
              <slot name="btVersionList">
                <el-button plain icon="el-icon-document-copy" size="small"
                  :disabled="!canvasOpened || !isMultipleVersions || allButtonDisabled"> 版本列表</el-button>
              </slot>
              <slot name="moreButtons"/>
            </slot>
          </div>
        </template>
        <template #dbColLeft>
          <classify-ztree
                  ref="classifytree"
                  :dataType="dataType"
                  :deleteApiKey="versionAction"
                  :deleteExtraParams="{actionKey:'DELETE'}"
                  :saveExtraParams="saveExtraParams"
                  :isBlockForm="isBlockForm"
                  @nodeClick="nodeClick"
                  @nodeDeleted="nodeDeleted"
                  @leafNodeAdded="leafNodeAdded"/>
        </template>
        <template #main>
          <div style="width: 100%;height: 100%">
            <!-- <loading ref="loadingFlag"/> -->
            <div v-show="canvasOpened" style="width: 100%;height: 100%">
              <slot name="mainCanvas" v-bind="{nodes, filterNodes, getNodeIdFromItemKey}" />
            </div>
            <div v-show="!canvasOpened" class="openGuideContainer">
              <slot name="openGuide">
                <div class="guide-title">{{bizName}}模板</div>
                <el-row style="overflow: auto;height: calc(100% - 70px)">
                  <el-col :span="4"
                          v-for="(o, index) in metaTemplates"
                          :key="o.id"
                          style="margin-bottom: 20px;padding:0px 10px">
                    <el-card :body-style="{ padding: '0px' }"
                             @dblclick.native = 'cardOnDbClick(o)'
                             @click.native="cardOnClick(o, index)">
                      <img :src="require('@/assets/' + o.imageUrl)" class="image">
                      <div style="padding: 14px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                          <el-tooltip class="item" effect="dark" :content = o.name placement="top-start">
                            <span>{{o.name}}</span>
                          </el-tooltip>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
                <div style="text-align: right;padding:0px 10px 20px 0px">
                  <el-button size="medium"  type="primary"
                      :disabled="tempBtndisabled"
                     style="padding: 10px 40px;"
                     @click="btOpenTemplate">使用模板新增{{bizName}}</el-button>
                </div>
              </slot>
            </div>
          </div>
        </template>
      </LayoutTem>
    </template>
  </page>
</template>

<script>
import Loading from '../loading/index'
import $ from 'jquery'
import ClassifyZtree from '../classifytree/classifyZtree'

export default {
  name: 'versionRelate',
  components: { ClassifyZtree, Loading },
  inject: {
    formCanvas: { default: undefined },
    clearBlocks: { default: undefined },
    changeLoading: { default: undefined },
    commonSetColItems: { default: undefined }
  },
  props: {
    bizName: '', // 业务名称：表单/流程
    dataType: { type: String, default: '' },
    parentSaveButton: { type: String, default: '保存' }, // 父组件保存按钮名称
    wrapSwitch: { type: Boolean, default: false }
  },
  data() {
    return {
      hasBa: false, // 多指标参照与报单多指标冲突
      canvasOpened: true, // 是否已经打开设计界面，false=已打开向导，true=已打开画布
      currentId: '',
      isMultipleVersions: false, // 存在多个版本
      versionStatus: '未启用', // 启用中,未启用,未启用新版本
      versionAction: `versionAction${this.dataType}`,
      isSaving: false,
      metaTemplates: [],
      allButtonDisabled: false, // 禁用所有按钮，比如预览时
      data: {}, // node点击参数
      exData: {}, // node点击值参数
      tempBtndisabled: true,
      debounceResize: null, // 存放返回的防抖函数
      isWrap: false,
      lastActrualWidth: 0,
      formTopWrapWidth: 0,
      isBlockForm: false,
      actualKey: '',
      saveExtraParams: { actionKey: 'SAVE' },
      blockCopyContainers: [], // 区块复制的接口传参
      nodes: []
    }
  },
  created() {
    this.canvasOpened = true
    this.debounceResize = this.$debounce(this.calculate, 300)
    this.btNewGuide(true)
  },
  mounted() {
    $('#dbclo-left').css('cssText', 'width:210px') // 左侧树区域宽度
    this.debounceResize?.()
  },
  activated() {
    window.$event.$on('windowResize', this.debounceResize)
  },
  deactivated() {
    window.$event.$off('windowResize', this.debounceResize)
  },
  watch: {
    'canvasOpened': {
      handler(newValue) {
        this.$emit('canvasOpenedChange', this.canvasOpened)
      },
      immediate: true
    }
  },
  computed: {
    versionStatusClass() {
      return {
        'item-status-sign': true,
        'item-status-enabled': this.versionStatus === '启用中',
        'item-status-disabled': this.versionStatus === '未启用',
        'item-status-disablednew': this.versionStatus === '新版本未启用'
      }
    },
    buttonAreaStyle() {
      return `flex-wrap: ${this.isWrap ? 'wrap' : 'nowrap'};}`
    }
  },
  methods: {
    calculate() {
      const buttonAreaChildren = Array.from(this.$refs.buttonArea.children)
      let add = false
      const formTopWrap = document.querySelector('.form-top-wrap')
      if (formTopWrap) {
        // 当form-top-wrap 初始化时 记录form-top-wrap宽度 隐藏时 将add标志置为true 表示添加form-top-wrap宽度到总宽度中
        if (!isNaN(+(Number(getComputedStyle(formTopWrap)['width'].replace('px', '')).toFixed(2)))) {
          this.formTopWrapWidth = +(Number(getComputedStyle(formTopWrap)['width'].replace('px', '')).toFixed(2))
        } else {
          add = true
        }
      }
      const actrualWidth = +(Number(getComputedStyle(this.$refs.buttonArea)['width'].replace('px', '')).toFixed(2))
      let width = buttonAreaChildren.map((item, index) => {
        const itemWidth = +(Number(getComputedStyle(item)['width'].replace('px', '')).toFixed(2))
        let mlSum = 0 // marginleft总和
        if (!isNaN(itemWidth)) {
          // 最后一项和第一项不加margin
          if (index === 0 || buttonAreaChildren.length - 1 === index) {
            mlSum += 0
          } else {
            mlSum += 12
          }
          return mlSum + itemWidth
        } else {
          return 0
        }
      }).reduce((a, b) => a + b, 0)
      if (actrualWidth > this.lastActrualWidth) {
        width = width - (this.$refs.buttonArea.querySelector('#showWrap') ? 60 : 0)
      }
      width = width + (add ? this.formTopWrapWidth : 0)
      const needWrap = actrualWidth < width
      this.lastActrualWidth = actrualWidth
      this.isWrap = needWrap
      this.$emit('changeStyle', needWrap, this.wrapSwitch)
    },
    pageHideLeftRight() { // 隐藏主界面的左右内容，主要用于表单的制单预览样式
      this.$refs.page.pageHideLeftRight()
    },
    pageHideLeftRightResume() { // 恢复显示主界面的左右内容，主要用于结束表单的制单预览
      this.$refs.page.pageHideLeftRightResume()
    },
    btSave(callbackBefore, callbackAfter, callbackFailed, extCallbackBefore, exCallbackBefore, exArg = {}) { // 保存元数据
      let isCopy = false
      if (arguments.length === 0) {
        // 证明是复制调用
        isCopy = true
      }
      const metaContainer = {}
      let exdata = {}
      if (this.$isNotEmpty(extCallbackBefore)) {
        exdata = extCallbackBefore()
      }
      this.$emit('getMetaToSave', metaContainer)
      if (metaContainer.meta.main.templateType === '系统模板') {
        this.$message.error('系统模板不能编辑或保存')
        return
      }
      let isCopyToOtherBookSet = false
      if (exdata.hasOwnProperty('copyToBookSetId')) {
        isCopyToOtherBookSet = true
      }

      if (metaContainer.meta.main.templateType === '用户模板') {
        metaContainer.meta.main.imageUrl = 'image/zidingyi.png'
      }
      this.isSaving = true
      var isSliderCauseSave = false

      if (typeof (callbackBefore) === 'function') {
        isSliderCauseSave = callbackBefore()
      }
      var itemMap = []
      if (this.$isNotEmpty(extCallbackBefore)) {
        // metaContainer.meta.exData = Object.assign(metaContainer.meta.exData, exdata)
        this.versionAction !== 'versionActionWfMetaEntity'
          ? metaContainer.meta.exData = { ...metaContainer.meta.exData, ...exdata }
          : metaContainer.meta.extData = { ...metaContainer.meta.extData, ...exdata } // wfMetaVo的额外数据为extData
        if (metaContainer.meta.hasOwnProperty('colItems')) {
          metaContainer.meta.colItems.forEach(item => {
            if (item.defaultValue === '审核签章') {
              item.dataValue = ''
              delete item.showSignature
            }
            itemMap.push(item.labelOrigin)
          })
        }
      }

      this.hasBa = this.$isFormContainsRepeatrepeatValues(itemMap, '指标')
      var values = []
      if (this.$isNotEmpty(metaContainer.meta.cformSettings)) {
        metaContainer.meta.cformSettings.forEach(item => {
          values.push(item.optionName)
        })
      }
      if (this.hasBa && values.indexOf('多指标参照') !== -1) {
        this.$message.error('表单设置中勾选多指标参照，表单不能绑定多个指标！')
        this.$parent.isSaving = false
        callbackFailed && callbackFailed()
      } else {
        // 区块表单保存
        const blockData = metaContainer.meta.blockData
        if (this.$isNotEmpty(blockData)) {
          this.blockSave(isCopy, metaContainer.meta, callbackAfter, callbackFailed)
        } else {
          if (this.$isNotEmpty(exArg)) {
            this.saveExtraParams.actionKey = exArg.actionKey
          }
          this.isBlockForm = false
          this.$callApi(this.versionAction,
            metaContainer.meta,
            result => {
              this.isSaving = false
              if (isCopyToOtherBookSet) {
                return callbackAfter(result)
              }
              this.$emit('afterMetaSaved', result)
              this.versionStatus = result.attributes['versionStatus']
              // 插入图片

              if (typeof (exCallbackBefore) === 'function') {
                exCallbackBefore()
              }
              let returnValue
              if (callbackAfter) { // callbackAfter可能会添加theAddingColLabel
                returnValue = callbackAfter(result)
              }
              this.openCanvas(result.data.itemKey, {
                isSliderCauseSave: isSliderCauseSave,
                theAddingColLabel: result.theAddingColLabel })
              return returnValue
            }, result => {
              this.isSaving = false
              if (callbackAfter) {
                callbackAfter(result)
              }
              if (typeof callbackFailed === 'function') {
                callbackFailed(result, {})
              }
            }, this.$refs.classifytree)
          this.saveExtraParams.actionKey = 'SAVE'
        }
      }
    },
    // 主tabs保存
    saveMainTabs(params) {
      const {
        blocks = [],
        cForm = {},
        blockData = {},
        assembleData = {},
        isCopy = false
      } = params

      const label = blockData.labels[0]
      // 表单数据处理
      blocks.push(cForm)
      // 如果blockdata长度大于1 则代表有行表单
      if (blockData[label].length > 1) {
        blockData[label].forEach((block, index) => {
          // 排除掉第一个表单
          if (index !== 0) {
            // 清空设计时填充的区块数据
            block.pageData.rows = []
            if (block.handlerKey === '行表单') {
              // 格式化pageData中的dataRef defaultValue
              this.$transferCheckboxData(block.pageData, true)
              // 处理组件管理的配置
              // 前端通过block.config保存配置 需要在保存的时候把config删除并把对应的配置赋值
              block = { ...block, ...block?.config }
              delete block.config
            }
            blocks.push(block)
          }
        })
      }
      assembleData.containers.push({
        blocks
      })
      if (isCopy) {
        // 解决没保存前 添加区块 会把添加的区块一起带到复制后的表单中
        assembleData.containers[0].blocks = this.blockCopyContainers
        assembleData.id = ''
      } else {
        assembleData.id = this.actualKey
      }
    },
    // 区块表单保存
    blockSave(isCopy, metaData, callbackAfter, callbackFailed) {
      const blockData = this.$clone(metaData.blockData)
      const assembleData = {
        containers: []
      }
      const blocks = []
      // const cloneCformData = clone(metaData)
      // 把克隆数据中的blockData去除 后续出现问题可以直接使用metaData
      delete metaData.blockData
      // 区块表单第一个表单(有且仅有一个)
      const cForm = {
        data: metaData
      }
      // tabs的标题不为空 (目前第一版只有一个tab 一个tab默认不显示)
      if (this.$isNotEmpty(blockData.labels)) {
        const mainTabsParams = {
          blocks,
          cForm,
          blockData,
          assembleData,
          isCopy
        }
        if (blockData.labels.length === 1) {
          this.saveMainTabs(mainTabsParams)
          // const label = blockData.labels[0]
          // // 表单数据处理
          // blocks.push(cForm)
          // // 如果blockdata长度大于1 则代表有行表单
          // if (blockData[label].length > 1) {
          //   blockData[label].forEach((block, index) => {
          //     // 排除掉第一个表单
          //     if (index !== 0) {
          //       // 清空设计时填充的区块数据
          //       block.pageData.rows = []
          //       if (block.handlerKey === '行表单') {
          //         // 格式化pageData中的dataRef defaultValue
          //         this.$transferCheckboxData(block.pageData, true)
          //         // 处理组件管理的配置
          //         // 前端通过block.config保存配置 需要在保存的时候把config删除并把对应的配置赋值
          //         block = { ...block, ...block?.config }
          //         delete block.config
          //       }
          //       blocks.push(block)
          //     }
          //   })
          // }
          // assembleData.containers.push({
          //   blocks
          // })
          // if (isCopy) {
          //   // 解决没保存前 添加区块 会把添加的区块一起带到复制后的表单中
          //   assembleData.containers[0].blocks = this.blockCopyContainers
          //   assembleData.id = ''
          // } else {
          //   assembleData.id = this.actualKey
          // }
        } else if (blockData.labels.length > 1) {
          // 多个tab逻辑
          blockData.labels?.forEach((tabLabel, index) => {
            const tabBlock = []
            if (!index) {
              // 第一个tab 主表单
              this.saveMainTabs(mainTabsParams)
            } else {
              if (this.$isNotEmpty(blockData[tabLabel])) {
                tabBlock.push(blockData[tabLabel])
              }
              if (tabBlock?.[0]) {
                const { name, relate, ...tabData } = tabBlock?.[0]
                assembleData.containers.push({
                  blocks: [tabData],
                  name,
                  relate
                })
              } else {
                assembleData.containers.push({
                  blocks: [],
                  name: '',
                  relate: ''
                })
              }
            }
          })
        }
      }

      this.isSaving = false

      this.$callApi(
        'saveBlockMeta',
        assembleData,
        result => {
          this.blockCopyContainers = []
          this.$emit('afterMetaSaved', result)
          this.actualKey = result.data.actualKey
          this.isSaving = false
          this.isBlockForm = true
          this.versionStatus = result.attributes['versionStatus']

          let returnValue
          if (callbackAfter) { // callbackAfter可能会添加theAddingColLabel
            returnValue = callbackAfter(result)
          }
          this.actualKey = result.data.actualKey
          this.openCanvas(this.actualKey, {}, this.isBlockForm, metaData, true)
          // this.openCanvas(result.data.actualKey, {}, this.isBlockForm, metaData, true)
          return returnValue
        },
        err => {
          this.blockCopyContainers = []
          this.isSaving = false
          if (callbackAfter) {
            callbackAfter(err)
          }
          if (callbackFailed) {
            callbackFailed(err, {})
          }
        },
        this.$refs.classifytree)
    },
    btSwitchStatus() {
      var op = (this.versionStatus === '启用中') ? '停用' : '启用'
      this.$callApiParamsConfirm(
        `确定要执行${op}操作吗?`, null,
        this.versionAction,
        { 'ids': this.currentId, 'actionKey': 'switchStatus' },
        result => {
          this.versionStatus = result.attributes['versionStatus']
          this.$emit('btSwitchStatusAfter', result)
          if (this.isBlockForm) {
            this.openCanvas(this.actualKey, this.exData, this.isBlockForm)
          } else {
            this.openCanvas(this.currentId, this.exData)
          }
        })
    },
    // 区块表单跳转到关联表单
    blockJumpToForm(itemKey, isFree) {
      if (this.$refs.classifytree) {
        const data = this.$refs.classifytree.zTreeObj?.getNodeByParam('itemKey', itemKey, null)
        if (data) {
          this.$refs.classifytree.$refs.supTree?.refresh()
          if (itemKey) {
            this.$refs.classifytree.changeCurrentItemKey(itemKey)
            setTimeout(() => {
              this.$refs.classifytree.$refs.supTree?.selectAndCheckNode(itemKey, 'itemKey')
            })
          }
          // 如果是从自由表单跳转的需要销毁luckysheet实例
          isFree && window.luckysheet?.destroy()
          this.nodeClick(data, {})
        } else {
          this.$message.error('跳转关联表单失败')
        }
      }
    },
    nodeClick(data, exData) { // needToReloadFreeJson表明是通过手动点击打开
      this.data = data
      this.exData = exData
      exData = exData || {}
      exData.needToReloadFreeJson = true
      this.$emit('nodeClick', data, exData)

      const isBlockForm = this.$isNotEmpty(data.actualKey)
      this.isBlockForm = isBlockForm
      if (isBlockForm) {
        this.actualKey = data.actualKey
        this.openCanvas(data.actualKey, exData, isBlockForm)
        this.$emit('setBlockFormId', data.actualKey)
      } else {
        this.openCanvas(data.itemKey, exData)
      }
    },
    nodeDeleted(node) { // 当前编辑的表单执行删除后，这时打开新建表单向导
      if (node.isLeaf && node.itemKey === this.currentId) {
        this.btNewGuide()
        this.$emit('nodeDeleted', node.itemKey)
      }
    },
    leafNodeAdded(nodeId, itemKey) { // 新增叶子节点后，将其设置为当前节点
      this.setCurrentItem(nodeId, itemKey)
    },
    setCurrentItem(nodeId, itemKey) { // 设置当前对象
      this.currentId = (itemKey == null) ? '' : itemKey
      this.$refs.classifytree.setCurrentItem(nodeId, itemKey)
    },
    reloadCurrentNode(oldColItemLabel) { // 重新装载当前树节点数据
      if (this.isBlockForm) {
        this.openCanvas(this.actualKey, { oldColItemLabel }, true)
      } else {
        if (this.$isNotEmpty(this.currentId)) {
          this.openCanvas(this.currentId, { oldColItemLabel })
        }
      }
    },
    openCanvas(id, exData, isBlockForm = false, meta = null, isSave = false) { // id是实体ID(区块表单的id是actualKey)，exData备用
      // 打开表单前先清空blocks 保存时不用清除
      if (isBlockForm) {
        this.changeLoading?.(true)
      }
      if (!isSave) {
        this.clearBlocks?.()
      }
      exData = exData || {}
      if (this.$isNotEmpty(id)) {
        if (isBlockForm) {
          /**
           * 不是保存时才走查询区块表单接口 保存的时候这里不做处理
           * 这里的if (!isSave) 不能放到上面if (isBlockForm)中
           * 等提交后效果不好再打开 目前的效果是保存和切换都会loading
           */
          // if (!isSave) {
          const callApiParams = {
            apiKey: 'selectBlockMeta',
            params: { ids: id }
          }
          this.openBlockForm(id, exData, callApiParams)
          // } else {
          //   this.commonSetColItems && this.commonSetColItems(meta)
          // }
        } else {
          this.openCanvasByCallApi(id, 'QUERY', exData)
        }
        this.$refs.classifytree.isOpenNew = false
      } else { // 新增操作时
        this.currentId = id
        this.versionStatus = '未启用'
        this.$emit('beforeOpenCanvas', id, exData)
        this.$emit('openNew', exData)
        this.$refs.classifytree.isOpenNew = true
      }
      // 打开表单 将左侧树节点存储到变量中 目前供区块使用
      this.nodes = this.$refs.classifytree.getDialogTreeData()
    },
    colSettingCpts() {
      return this.$parent
    },
    openCanvasByCallApi(id, actionKey, exData, exParams, callback) {
      exParams = exParams || {}
      exData = exData || {}
      const params = { ids: id, actionKey: actionKey }
      Object.assign(params, exParams)

      this.$emit('beforeOpenCanvas', id, exData)
      // this.$refs.loadingFlag.show = true
      this.canvasOpened = true
      this.$callApiParams(this.versionAction, params,
        result => {
          exData = exData || {}
          // this.$refs.loadingFlag.show = false
          result.data.needToReloadFreeJson = exData.needToReloadFreeJson
          result.data.isSliderCauseSave = exData.isSliderCauseSave
          result.data.theAddingColLabel = exData.theAddingColLabel
          result.data.oldColItemLabel = exData.oldColItemLabel
          this.$emit('afterMetaQuery', result)

          this.canvasOpened = true
          this.debounceResize()
          this.currentId = result.data.main.id
          this.versionStatus = result.data.main.versionStatus
          if (callback) {
            callback(result)
          }
          return true
        }, result => {
          // this.$refs.loadingFlag.show = false
          this.currentId = ''
          this.versionStatus = '未启用'
        })
    },
    btNewGuide(isCreate = false) { // 执行打开向导，如果当前已经是向导，则不执行
      if (this.canvasOpened) {
        this.clearBlocks?.()
        // isCreate 判断是否是在create钩子中调用
        !isCreate && this.debounceResize()
        this.actualKey = ''
        this.canvasOpened = false
        this.versionStatus = '未启用'
        this.tempBtndisabled = true
        $('.click-el-card-border').removeClass('click-el-card-border')
        this.$callApiParams(this.versionAction,
          { actionKey: 'queryMetaTemplateList' },
          result => {
            var templateData = [{ 'id': 'new', 'name': '空白模板', 'imageUrl': 'image/kongbai.png' }]
            templateData = templateData.concat(result.data)
            templateData.forEach(templat => {
              if (!templat.imageUrl) {
                templat.imageUrl = 'image/kongbai.png'
              }
            })
            this.metaTemplates = templateData
            this.setCurrentItem(null, '')
            return true
          })
      }
      this.$emit('btNewGuide')
    },
    cardOnDbClick(o) {
      this.openTemplate(o.id)
    },
    cardOnClick(o, index) {
      $('.click-el-card-border').removeClass('click-el-card-border')
      var $selectedCard = $('.el-card')[index]
      $selectedCard.id = o.id
      this.tempBtndisabled = false
      $selectedCard.className = 'el-card is-always-shadow click-el-card-border'
    },
    btOpenTemplate() {
      var $selectedCard = $('.click-el-card-border')[0]
      if ($selectedCard) {
        var id = $selectedCard.id
        this.tempBtndisabled = false
        this.openTemplate(id)
      } else {
        this.tempBtndisabled = true
      }
    },
    openTemplate(templateId) {
      if (templateId === 'new') {
        this.openCanvas('', { needToReloadFreeJson: false })
      } else {
        this.openCanvasByCallApiCopy(templateId, { operation: 'new' })
      }
    },
    copyToOtherBookSet() {
      const refData = { colType: '弹框', dataRef: '选择用户账套' }
      this.$refData(undefined,
        refData,
        () => {},
        () => {},
        () => {},
        selectedData => {
          if (selectedData) {
            this.copyProcess(selectedData)
          }
        },
        { IS_ENABLED_eq: '是',
          multiple: true,
          isPrompt: false,
          filterSelf: true, // filterSelf是否需要过滤当前账套
          checkSelf: false // checkSelf是否默认选中当前账套
        })
    },
    copyProcess(selectedData) {
      const node = this.$refs.classifytree.getCurrentNode()
      if (node) {
        const selectedBookSetIds = []
        if (selectedData.hasOwnProperty('list')) {
          selectedData.list.forEach(data => {
            selectedBookSetIds.push(data.id)
          })
        }
        selectedBookSetIds.forEach(selectedBookSetId => {
          this.openCanvasByCallApiCopy(node.itemKey, { 'copyToBookSetId': selectedBookSetId },
            result => {
              this.btSave(undefined, () => {
                this.$message.success('操作成功')
                this.openCanvasByCallApi(node.itemKey, 'QUERY')
              }, () => {
                result.success = false
                this.openCanvasByCallApi(node.itemKey, 'QUERY')
              },
              () => { return { 'copyToBookSetId': selectedBookSetId } }
              )
            })
        })
      }
    },
    btCopy() {
      var node = this.$refs.classifytree.getCurrentNode()
      if (node) {
        const id = this.isBlockForm ? node.actualKey : node.itemKey
        this.openCanvasByCallApiCopy(id, {}, () => { this.btSave() })
      }
    },
    btCopyToOtherBookSet() {
      var node = this.$refs.classifytree.getCurrentNode()
      if (node) {
        // 目前跨账套复制只有流程和表单的，表单的在form/index.vue里面
        if (node.hasOwnProperty('dataType') && node.dataType === 'WfMetaEntity') {
          this.copyToOtherBookSet()
        } else {
          this.openCanvasByCallApiCopy(node.itemKey, {},
            result => { this.btSave() })
        }
      }
    },
    openCanvasByCallApiCopy(id, exParams, callback) {
      var exData = { needToReloadFreeJson: true }
      if (this.isBlockForm) {
        const callApiParams = {
          apiKey: 'copyBlockMeta',
          params: { viewId: id }
        }
        this.openBlockForm(id, exData, callApiParams, callback)
      } else {
        if (exParams && exParams.hasOwnProperty('copyToBookSetId')) {
          exData.needToReloadFreeJson = false
        }
        this.openCanvasByCallApi(id, 'COPY', exData, exParams, callback)
      }
      this.$refs.classifytree.isOpenNew = true
    },
    /**
     * 渲染blockForm
     * @param {String} id 区块表单id
     * @param {Object} exData 额外数据
     * @param {Object} callApiParams 请求所需参数(apiKey, params)
     * @param {Object} callback 请求成功的回调
     */
    openBlockForm(id, exData, callApiParams, callback) {
      this.$emit('beforeOpenCanvas', id, exData)
      // this.$refs.loadingFlag.show = true
      this.canvasOpened = true
      const { apiKey, params } = callApiParams
      this.$callApiParams(apiKey, params,
        result => {
          this.blockCopyContainers = result.data.containers[0].blocks
          const cloneResult = this.$clone(result.data)
          const cFormData = result.data.containers[0].blocks[0]
          cFormData.data.blockView = cloneResult
          exData = exData || {}
          // this.$refs.loadingFlag.show = false
          cFormData.data.needToReloadFreeJson = exData.needToReloadFreeJson
          cFormData.data.isSliderCauseSave = exData.isSliderCauseSave
          cFormData.data.theAddingColLabel = exData.theAddingColLabel
          this.$emit('afterMetaQuery', cFormData)

          this.canvasOpened = true
          this.debounceResize()
          this.currentId = cFormData.data.main.id
          this.versionStatus = cFormData.data.main.versionStatus
          if (callback) {
            callback()
          }
          return true
        },
        () => {
          // this.$refs.loadingFlag.show = false
          this.currentId = ''
          this.versionStatus = '未启用'
        })
    },
    setAllButtonDisabled(disabled) {
      this.allButtonDisabled = disabled
    },
    // 用于区块关联表单过滤表单节点
    filterNodes(filterKeys) {
      this.nodes = this.$refs.classifytree.getDialogTreeData()?.filter(node => !filterKeys?.includes(node.itemKey))
    },
    // 根据itemKey返回节点id
    getNodeIdFromItemKey(itemKey) {
      return itemKey ? this.$refs.classifytree.zTreeObj?.getNodeByParam('itemKey', itemKey)?.id : ''
    }
  }
}
</script>
<style lang="scss" scoped>
  .vue-bifrostIcApp .common-page .version-relate-buttons {
    display: flex;
  }

  .vue-bifrostIcApp .common-page .version-relate-buttons .upload-import {
    margin: 0px 0px 0px 5px;
  }
  .vue-bifrostIcApp .common-page span.item-status-sign {
    display: inline-grid;
    margin: 0px 3px 0px 0px;
  }
  .guide-title{
    padding: 0px 0px 15px 10px;
    font-size: 14px;
    font-weight: bolder;
    color: #333;
  }
  .openGuideContainer {
    padding: 20px;
    border: 1px solid #ddd;
    width: 100%;
    height: 100%;
  }
  .image {
    width: 100%;
    display: block;
    height: 120px;
    border-bottom: 1px solid #ddd;
    /*height:auto*/
  }
  .vue-bifrostIcApp{
    .el-card__body {
      border: 1px solid #ddd;
    }
  }

  .el-card.is-always-shadow, .el-card.is-hover-shadow:focus, .el-card.is-hover-shadow:hover {
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.2) !important;
  }
  .el-card {
    border-radius: 2px;
    border: 1px solid #DDDDDD !important;
  }
  .el-card:hover {
     cursor:pointer;
     border: 1px solid #c5c5c5 !important;
     /* transform属性 */
     /*transform:translate(0,-20px)*/
     /* 第一个参数指定X轴的位移量,必填, 第二个参数指定Y轴的位移量,不必填 默认0*/
   }
  .click-el-card-border{
    border: 1px solid #5C96BC !important;
  }
  .el-dialog--center .el-dialog__body {
    padding: 5px 25px 30px !important;
  }
  .version-relate-buttons #showWrap {
    margin-right: 12px;
    min-width: 48px;
  }
  .version-relate-buttons #showWrap:hover,
  .version-relate-buttons #showWrap:focus,
  .version-relate-buttons #showWrap:active {
    border: 1px solid #1070e1;
    color: #1070e1 !important;
    background: #fff;
  }
</style>
<style lang="less" scoped>
.version-relate-buttons .el-button {
  margin-right: 6px;
}
</style>
