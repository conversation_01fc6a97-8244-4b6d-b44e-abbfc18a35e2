<template>
  <div id="accommodationdialog">
    <el-dialog
          append-to-body
          width="640px"
          :title='`${btEditType + tabName}`'
          :before-close="beforeClose"
          :visible.sync="showbxbaseeditdialog"
          :close-on-click-modal="false">
      <el-form :label-width="labelWidth" :model="baseInfo">
        <el-form-item label="编码" required>
          <el-input v-model="baseInfo.infoCode" :disabled="true" />
        </el-form-item>
        <el-form-item label="父级名称" v-show="showParent">
          <el-select v-model="baseInfo.parentId" filterable placeholder="请选择" @change="changeCode" >
            <el-option
              v-for="item in cityList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :inCode="item.code"
              >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="infoNameLabel" required>
          <el-input v-model="baseInfo.infoName"/>
        </el-form-item>
        <el-form-item label="洲际" v-show="showContinent">
          <el-input v-model="baseInfo.continent"/>
        </el-form-item>
        <el-form-item label="币种" v-show="showCurrency">
            <el-select v-model="baseInfo.currency">
              <el-option
                v-for="item in currencyOptions"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item :label="standardLabel" v-show="showStandard">
          <el-input v-model="baseInfo.standard" @keyup.native="proving('standard')"/>
        </el-form-item>
        <el-form-item :label="standardFeeLabel" v-show="showStandardFee">
          <el-input v-model="baseInfo.standardFee" :disabled="disabledStandardFee"
                    @keyup.native="proving('standardFee')"/>
        </el-form-item>
        <el-form-item label="住宿费" v-show="showZsf">
          <el-input v-model="baseInfo.zsf" @keyup.native="proving('zsf')"/>
        </el-form-item>
        <el-form-item label="伙食费" v-show="showHsf">
          <el-input v-model="baseInfo.hsf" @keyup.native="proving('hsf')"/>
        </el-form-item>
        <el-form-item label="场地、资料、交通费" v-show="showVmtFee">
          <el-input v-model="baseInfo.vmtFee" @keyup.native="proving('vmtFee')"/>
        </el-form-item>
        <el-form-item label="其他费用" v-show="showOtherFee">
          <el-input v-model="baseInfo.otherFee" @keyup.native="proving('otherFee')"/>
        </el-form-item>
        <el-form-item label="综合定额" v-show="showSynthesisFee">
          <el-input v-model="baseInfo.synthesisFee" :disabled="true" @keyup.native="proving('synthesisFee')"/>
        </el-form-item>
        <el-form-item label="费用调整比例" v-show="showCostRatio">
          <el-input v-model="baseInfo.costRatio" @keyup.native="proving('costRatio')"/>
        </el-form-item>
        <el-form-item style="margin-bottom: 0px" label="说明">
          <el-input v-model="baseInfo.remark" type="textarea" :rows="3"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="btn-normal" @click="beforeClose">取消</el-button>
        <el-button class="btn-normal" type="primary" @click="saveBaseInfo">{{btEditType}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'bx-base-edit-dialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    editableTabs: {
      type: Array,
      default: () => []
    },
    multipleSelection: {
      type: String,
      default: () => ''
    }
  },
  watch: {
    dialog() {
      this.showbxbaseeditdialog = this.dialog
      this.buildForm()// 构建form表单
      this.selectCurrency()
      this.selectParentCity()
    }
  },
  data() {
    return {
      showbxbaseeditdialog: false,
      btEditType: '新增',
      labelWidth: '100px',
      tabName: '',
      cityList: [],
      infoNameLabel: '名称',
      standardLabel: '标准',
      standardFeeLabel: '每人每天费用',
      showStandard: false,
      showStandardFee: false,
      showZsf: false,
      showHsf: false,
      showOtherFee: false,
      showSynthesisFee: false,
      showCostRatio: false,
      showVmtFee: false,
      showContinent: false,
      showParent: false,
      showCurrency: false,
      disabledStandardFee: true,
      baseInfo: {
        id: '',
        infoCode: '',
        infoName: '数据名称',
        baseTypeId: '',
        baseType: 0.00,
        standardFee: 0.00,
        standard: 0.00,
        zsf: 0.00,
        hsf: 0.00,
        otherFee: 0.00,
        vmtFee: 0.00,
        synthesisFee: 0.00,
        costRatio: 0.00,
        remark: ''
      },
      currencyOptions: []
    }
  },
  methods: {
    changeBaseInfo(info) {
      this.baseInfo = {
        ...this.baseInfo,
        ...info
      }
    },
    proving(key) { // 只能输⼊数字且只有二位⼩数
      // 先把⾮数字的都替换掉，除了数字和.
      this.baseInfo[key] = this.baseInfo[key].replace(/[^\d.]/g, '')
      // 必须保证第⼀个为数字⽽不是.
      this.baseInfo[key] = this.baseInfo[key].replace(/^\./g, '')
      // 保证只有出现⼀个.⽽没有多个.
      this.baseInfo[key] = this.baseInfo[key].replace(/\.{2,}/g, '')
      // 保证.只出现⼀次，⽽不能出现两次以上
      this.baseInfo[key] = this.baseInfo[key].replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      let index = -1
      for (const i in this.baseInfo[key]) {
        if (this.baseInfo[key][i] === '.') {
          index = i
        }
        if (index !== -1) {
          if (i - index > 2) {
            this.baseInfo[key] = this.baseInfo[key].substring(0, this.baseInfo[key].length - 1)
          }
        }
      }
      var totalFee = 0
      if (this.tabName === '会议费标准') {
        if (this.$isNotEmpty(this.baseInfo.zsf)) {
          totalFee = parseFloat(totalFee) + parseFloat(this.baseInfo.zsf)
        }
        if (this.$isNotEmpty(this.baseInfo.hsf)) {
          totalFee = parseFloat(totalFee) + parseFloat(this.baseInfo.hsf)
        }
        if (this.$isNotEmpty(this.baseInfo.otherFee)) {
          totalFee = parseFloat(totalFee) + parseFloat(this.baseInfo.otherFee)
        }
      }
      if (totalFee>0) {
        this.baseInfo.standardFee = totalFee.toFixed(2)
      }
      this.baseInfo.synthesisFee = totalFee.toFixed(2)
      if (this.tabName === '培训费标准') {
        var pxTotalFee = parseFloat(this.baseInfo.zsf) + parseFloat(this.baseInfo.hsf) +
          parseFloat(this.baseInfo.otherFee) + parseFloat(this.baseInfo.vmtFee)
        this.baseInfo.standardFee = pxTotalFee.toFixed(2)
        this.baseInfo.synthesisFee = pxTotalFee.toFixed(2)
      } else if (this.tabName === '出国标准') {
        const totalFee = parseFloat(this.baseInfo.zsf) + parseFloat(this.baseInfo.hsf) +
          parseFloat(this.baseInfo.standardFee)
        this.baseInfo.synthesisFee = totalFee.toFixed(2)
      }
      if (key === 'costRatio') { // 费用调整比例，不能大于100%
        if (this.baseInfo[key] > 100) {
          this.baseInfo.costRatio = '100.00'
        }
      }
    },
    buildForm() {
      this.labelWidth = '100px'
      this.infoNameLabel = '名称'
      this.standardLabel = '标准'
      this.standardFeeLabel = '每人每天费用'
      this.showStandard = false
      this.showStandardFee = false
      this.showZsf = false
      this.showHsf = false
      this.showOtherFee = false
      this.showSynthesisFee = false
      this.showCostRatio = true
      this.showVmtFee = false
      this.showContinent = false
      this.showCurrency = false
      this.showParent = false
      this.disabledStandardFee = true
      if (this.tabName === '会议费标准') {
        this.labelWidth = '100px'
        this.standardLabel = '标准天数'
        this.showStandard = true
        this.showStandardFee = true
        this.showZsf = true
        this.showHsf = true
        this.showOtherFee = true
        this.showSynthesisFee = true
      } else if (this.tabName === '培训费标准') {
        this.labelWidth = '140px'
        this.standardLabel = '标准天数'
        this.showStandard = true
        this.showStandardFee = true
        this.showZsf = true
        this.showHsf = true
        this.showOtherFee = true
        this.showVmtFee = true
        this.showSynthesisFee = true
      } else if (this.tabName === '培训讲师级别') {
        this.labelWidth = '100px'
        this.standardLabel = '标准学时'
        this.showStandard = true
      } else if (this.tabName === '接待级别') {
        this.standardFeeLabel = '宴请费'
        this.showStandardFee = true
        this.showZsf = true
        this.showHsf = true
      } else if (this.tabName === '出国标准') {
        this.labelWidth = '100px'
        this.infoNameLabel = '国家'
        this.standardFeeLabel = '公杂费'
        this.showStandardFee = true
        this.showZsf = true
        this.showHsf = true
        this.showContinent = true
        this.showCurrency = true
        this.disabledStandardFee = false
        this.showSynthesisFee = true
      } else if (this.tabName === '伙食费标准' || this.tabName === '市内交通费') {
        this.labelWidth = '100px'
        this.showStandardFee = true
        this.showCurrency = true
        this.disabledStandardFee = false
      } else if (this.tabName === '人员职别') {
        this.showCostRatio = false
      } else if (this.tabName === '城市名称') {
        this.showCostRatio = false
        this.showParent = true
      }
    },
    saveBaseInfo() {
      this.$callApi('saveReiBaseInfo', this.baseInfo, result => {
        if (result.success) {
          this.showbxbaseeditdialog = false
          this.$emit('update:dialog', false)
          if (this.tabName === '出国标准') {
            // this.$parent.reload()
            this.$emit('reloadBaseInfo')
          } else {
            // 如果是tableTree子级则走更新tree子级的方法
            this.$emit('reloadBaseInfo')
            // if(this.$isEmpty(this.baseInfo.parentId)) {
            //    this.$parent.$parent.$parent.$parent.reloadBaseInfo()
            // } else {
            //   console.log(11111,this.baseInfo.parentId,this.$parent.$parent.$parent.$parent)
            //   this.$emit('refreshTable', this.baseInfo.parentId, true)
            //   // this.$parent.$parent.$parent.$parent.refreshTable(this.baseInfo.parentId, true);
            // }
          }
        }
      })
    },
    beforeClose() {
      this.showbxbaseeditdialog = false
      this.$emit('update:dialog', false)
      this.$parent.$parent.$parent.$parent.reloadBaseInfo()
    },
    selectCurrency() {
      this.$callApi('getAccSetEle&tableName=ELE_CURRENCY_CODE', {}, result => {
        if (result.success) {
          this.currencyOptions = result.data
          this.baseInfo.currency = this.currencyOptions[0].eleName
        }
        return true
      })
    },
    selectParentCity() {
      this.cityList = []
      if (this.tabName === '城市名称') {
        this.editableTabs.forEach(item => {
          const baseName = item.baseName
          if (baseName === this.tabName) {
            const baseInfoList = item.allBaseInfoList
            baseInfoList.filter((info) => {
              var laber = info.infoName
              var value = info.id
              var hasChild = !!this.$isNotEmpty(info.parentId)
              var parentId = info.parentId
              if (this.$isNotEmpty(laber)) {
                this.cityList.push({ label: laber, value: value, code: info.infoCode, hasChild: hasChild, parentId: parentId })
              }
            })
          }
        })
      }
    }, changeCode(val) {
      this.$emit('changeInfoCode', val)
    }
  }
}
</script>

<style>

</style>
