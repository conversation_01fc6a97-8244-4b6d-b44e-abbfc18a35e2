<template>
    <div class="hasVerticalTabs" style="height: 100%">
        <b-page ref="basePage">
            <template #dbColLeft>
                <slot name="dbColLeft"/>
            </template>
            <template #toolbarExtend>
                <slot name="toolbarExtend"/>
            </template>
            <template #mainContent>
                <loading ref="loading"/>
                <slot name="mainContent">
                  <div id="baseTabs">
                    <el-tabs v-model="editableTabsValue" :tab-position="tabPosition" @tab-click="handleTabsClick">
                    <el-tab-pane
                      v-for="(item) in editableTabs"
                      :key="item.id"
                      :label="item.baseName"
                      :name="item.id"
                    >
                      <el-table
                        border
                        ref="multipleTable"
                        :key="time"
                        :data="item.baseInfoList"
                        row-key="id"
                         lazy
                        :load="loadCityInfo"
                        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                        tooltip-effect="dark"
                        style="width: 100%"
                        @selection-change="handleSelectionChange">
                        <el-table-column type="selection" width="35" align="left"></el-table-column>
                        <el-table-column
                          v-for="(herder, index) in item.herderList"
                          :key='index'
                          :label="herder.label"
                          :prop="herder.prop"
                          :width="herder.width"
                          :align="herder.align"
                          :sortable="herder.sortable"
                          header-align="center"
                          show-overflow-tooltip
                        ></el-table-column>
                      </el-table>
                    </el-tab-pane>
                  </el-tabs>
                  </div>
                  <el-dialog
                        append-to-body
                        width="640px"
                        :title='`${btEditType}标准类型`'
                        :close-on-click-modal="false"
                        :visible.sync="addNodeDlgVisible">
                    <el-form label-width="80px" :model="tempNode">
                      <el-form-item label="类型名称" required>
                        <el-input v-model="tempNode.baseName"/>
                      </el-form-item>
                      <el-form-item style="margin-bottom: 0px;" label="类型序号" required>
                        <el-input-number v-model="tempNode.baseType" :min="0" :max="1000" label="标准类型序号"></el-input-number>
                      </el-form-item>
                    </el-form>
                    <div slot="footer" class="dialog-footer">
                      <el-button class="btn-normal" @click="addNodeDlgVisible = false">取消</el-button>
                      <el-button class="btn-normal" type="primary" @click="saveNode">{{btEditType}}</el-button>
                    </div>
                  </el-dialog>
                  <bx-base-edit-dialog ref='bxbaseeditdialog' :dialog.sync='showbxbaseeditdialog' @reloadBaseInfo='reloadBaseInfo'
                                       :editableTabs="editableTabs" :tabName="tabName" @refreshTable="refreshTable" @changeInfoCode="changeInfoCode"/>
                  <accommodationdialog :dialog.sync='showaccommodationdialog' :cityId='cityId' :positionId='positionId' />
                  <goabroadfeedialog :dialog.sync='showgoabroadfeedialog' />
                </slot>
            </template>
        </b-page>
  </div>
</template>
<script>
import $ from 'jquery'
export default {
  name: 'reibase',
  data() {
    return {
      tabName: '人员职别',
      tabPosition: 'left',
      editableTabsValue: '',
      editableTabs: [],
      parentId: '',
      tabIndex: 2,
      addNodeDlgVisible: false,
      btEditType: '',
      tempNode: {
        id: '',
        baseName: '新建类型',
        baseType: 0
      },
      baseTypeMax: 0,
      baseInfo: {
        id: '',
        infoCode: '',
        infoName: '数据名称',
        baseTypeId: '',
        baseType: 0,
        remark: ''
      },
      multipleSelection: [],
      showaccommodationdialog: false,
      cityId: '',
      positionId: '',
      showaccommodation: true,
      isUpdateBaseInfo: true, // 是否可以修改基础信息
      isDeleteBaseInfo: true, // 是否可以修改基础信息
      showbxbaseeditdialog: false,
      showgoabroadfeedialog: false,
      loadMap: {},
      time: Date.now()
    }
  },
  mounted() {
    this.editableTabsValue = ''
    this.selectReiBaseTypeList() // 查询基础类型数据
  },
  methods: {
    init() {
      const typeDisabled = this.editableTabsValue.length === 0
      this.$refs.basePage.init({
        params: {
          dataApiKey: 'selectAttachmentPage',
          deleteApiKey: 'deleteAttachment'
        },
        showPager: false,
        buttons: [
          { text: '新增报销类别', icon: 'el-icon-circle-plus-outline', disabled: false, click: this.btAddClick },
          { text: '修改报销类别', icon: 'el-icon-edit', disabled: typeDisabled, click: this.btEditClick },
          { text: '删除报销类别', icon: 'el-icon-delete', disabled: typeDisabled, click: this.deleteReiBaseType },

          { text: '新增基础数据', icon: 'el-icon-document-add', disabled: typeDisabled, click: this.addBaseInfo },
          { text: '修改基础数据', icon: 'el-icon-edit-outline', disabled: this.isUpdateBaseInfo, click: this.editBaseInfo },
          { text: '删除基础数据', icon: 'el-icon-document-delete', disabled: this.isDeleteBaseInfo, click: this.deleteBaseInfo },

          { text: '导入', icon: 'el-icon-aliiconshangchuan', disabled: typeDisabled, click: this.showExcelImp },
          { text: '导出', icon: 'el-icon-aliiconxiazai', disabled: typeDisabled, click: this.excelDownload },
          { text: '住宿费标准', icon: 'el-icon-setting', disabled: !this.showaccommodation, click: this.accommodationDialog }
          /* { text: '出国标准', icon: 'el-icon-setting', disabled: false, click: this.showGoaBroadFeeDialog }*/
        ]
      })
      const customizeButtons = ['新增报销类别'] // ['新增报销类别', '出国标准']

      if (!typeDisabled) {
        customizeButtons.push('修改报销类别')
        customizeButtons.push('删除报销类别')
        if (this.tabName === '城市名称') { // 城市名称新增因为父级不能操作两条
          if (this.multipleSelection.length <= 1) {
            customizeButtons.push('新增基础数据')
          }
        } else {
          customizeButtons.push('新增基础数据')
        }
        customizeButtons.push('导入')
        customizeButtons.push('导出')
      }
      if (!this.isUpdateBaseInfo) {
        customizeButtons.push('修改基础数据')
      }
      if (!this.isDeleteBaseInfo) {
        customizeButtons.push('删除基础数据')
      }
      if (this.showaccommodation) {
        customizeButtons.push('住宿费标准')
      }
      this.$nextTick(() =>
        this.$refs.basePage.customizeButtons(customizeButtons))
    },
    showExcelImp() { // 导入
      var columnMeta = {}
      // 根据this.tabName获取需要传入的columnMeta
      columnMeta = this.getColumnMeta(columnMeta, false)
      var apiKey = 'template/标准数据模板.xls'
      if (this.tabName === '出国标准') {
        apiKey = 'template/出国标准模板.xls'
      }
      var fileName = this.tabName ? this.tabName + '模板.xls' : '标准数据模板.xls'
      var tableColumn = columnMeta
      this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
        FORM_TYPE_eq: this.tabName,
        id: this.editableTabsValue,
        columnMeta: columnMeta,
        onSuccess: () => {
          this.selectReiBaseTypeList()
        } })
    },
    handleTabsClick(tab, event) { // tabs点击事件
      // 切换tabs 重置选择的数据
      if (this.tabName !== tab.label) {
        this.multipleSelection = []
        this.$refs.multipleTable.forEach(table => table.clearSelection())// 删除选择的数据
        this.isUpdateBaseInfo = true
        this.isDeleteBaseInfo = true
        this.init()
      }
      this.tabName = tab.label
    },
    excelDownload() { // 导出
      const rowIds = this.$getTableCheckedIdsStrBy(this.multipleSelection)
      var columnMeta = {}
      columnMeta = this.getColumnMeta(columnMeta, true)
      var params = {
        文件名称: '报账基础数据.xls', exportExcelName: this.tabName + '基础数据.xls',
        columnMeta: columnMeta, BASE_TYPE_ID_eq: this.editableTabsValue, ascs: 'INFO_CODE'
      }
      if (rowIds) {
        params.BIZID_in = rowIds
      }
      this.$fileDownloadBykey('报账基础数据导出表单', params)
    },
    accommodationDialog() { // 更多设置弹框
      this.showaccommodationdialog = true
    },
    showGoaBroadFeeDialog() {
      this.showgoabroadfeedialog = true
    },
    handleSelectionChange(val) { // 表单类型选择变化
      this.multipleSelection = val
      if (this.$isNotEmpty(this.multipleSelection)) {
        if (this.multipleSelection.length === 1) {
          this.isUpdateBaseInfo = false
        } else {
          this.isUpdateBaseInfo = true
        }
        this.isDeleteBaseInfo = false
      } else {
        this.isUpdateBaseInfo = true
        this.isDeleteBaseInfo = true
      }
      this.init()
    },
    addBaseInfo() {
      this.baseInfo = this.makeNewBaseInfo()
      this.tempNode = this.editableTabs.filter(tab => tab.id === this.editableTabsValue)[0]
      if (this.tabName === '城市名称' && this.multipleSelection.length === 1) {
        this.changeInfoCode()
      } else {
        this.getInfoCode()
      }
      this.baseInfo.baseTypeId = this.tempNode.id
      this.baseInfo.baseType = this.tempNode.baseType
      this.$refs.bxbaseeditdialog.baseInfo = Object.assign({}, this.baseInfo)
      this.$refs.bxbaseeditdialog.btEditType = '新增'
      this.$refs.bxbaseeditdialog.tabName = this.tabName
      this.$refs.bxbaseeditdialog.saveBaseInfoDisabled = false
      this.showbxbaseeditdialog = true
    },
    makeNewBaseInfo() {
      return {
        id: '',
        infoCode: '',
        infoName: '数据名称',
        baseTypeId: '',
        baseType: 0.00,
        standardFee: 0.00,
        standard: 0.00,
        zsf: 0.00,
        hsf: 0.00,
        otherFee: 0.00,
        vmtFee: 0.00,
        synthesisFee: 0.00,
        costRatio: 0.00,
        remark: ''
      }
    },
    editBaseInfo() {
      if (this.multipleSelection.length === 0 || this.multipleSelection > 1) {
        this.$message.error('请选择一条基础数据！')
      } else {
        this.baseInfo = Object.assign(this.baseInfo, this.multipleSelection[0])
        this.$refs.bxbaseeditdialog.baseInfo = Object.assign({}, this.baseInfo)
        this.$refs.bxbaseeditdialog.btEditType = '修改'
        this.$refs.bxbaseeditdialog.tabName = this.tabName
        this.$refs.bxbaseeditdialog.saveBaseInfoDisabled = false
        this.showbxbaseeditdialog = true
      }
    },
    async reloadBaseInfo() {
      this.isUpdateBaseInfo = true
      this.isDeleteBaseInfo = true
      await this.selectReiBaseTypeList()
      this.time = Date.now()
      this.showbxbaseeditdialog = false
    },
    deleteBaseInfo() { // 删除基础类型
      if (this.multipleSelection.length === 0) {
        this.$message.error('请选择需要删除的基础数据！')
      } else {
        this.$confirm(`确认要删除选中的数据吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            var ids = ''
            this.multipleSelection.forEach(item => {
              ids += ',' + item.id
            })
            this.$callApiParams('deleteReiBaseInfo', { ids: ids, baseName: this.tabName }, async result => {
              const parentIdObj = {}
              for (let i = 0; i < this.multipleSelection.length; i++) {
                const item = this.multipleSelection[i]
                if (!item.parentId) {
                  await this.reloadBaseInfo()
                } else {
                  parentIdObj[item.parentId] = item.parentId
                }
              }
              const ids = Object.keys(parentIdObj)
              ids.forEach(async id => {
                await this.refreshTable(id, true)
              })
              const tableIndex = this.editableTabs.findIndex(item => this.editableTabsValue === item.id)
              this.$refs.multipleTable[tableIndex].clearSelection()
              return true
            })
          })
          .catch(() => {})
      }
    },
    btAddClick() {
      this.tempNode = this.makeNewNode()
      this.baseTypeMax = this.editableTabs.length > 0
        ? Math.max.apply(Math, this.editableTabs.map(item => { return item.baseType })) + 1 : this.baseTypeMax
      this.tempNode.baseType = this.baseTypeMax
      this.btEditType = '新增'
      this.addNodeDlgVisible = true
    },
    makeNewNode() {
      return {
        id: '',
        baseName: '新建类型',
        baseType: 0
      }
    },
    btEditClick() {
      this.tempNode = this.editableTabs.filter(tab => tab.id === this.editableTabsValue)[0]
      this.btEditType = '修改'
      this.addNodeDlgVisible = true
    },
    deleteReiBaseType() { // 删除基础类型
      this.$confirm(`确认要删除选中的数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$callApiParams('deleteReiBaseType', { ids: this.editableTabsValue }, result => {
            this.selectReiBaseTypeList()
            return true
          })
        })
        .catch(() => {})
    },
    getColumnMeta(columnMeta, isExport) {
      const selectColumn = this.editableTabs.find(item => item.baseName === this.tabName)
      if (this.$isNotEmpty(selectColumn)) {
        const $exportHerderList = selectColumn.herderList
        if (this.$isNotEmpty($exportHerderList)) {
          $.each($exportHerderList, (index, item) => {
            const key = item.label
            const value = item.prop
            Object.defineProperty(columnMeta,
              key,
              { value: value, writable: true, enumerable: true, configurable: true })
          })
          if ((this.tabName === '城市名称' || this.tabName === '伙食费标准' || this.tabName === '市内交通费') &&
            !isExport) {
            Object.defineProperty(columnMeta, '区域类型', { value: 'areaType', writable: true, enumerable: true, configurable: true })
          }
        }
      }
      if (this.$isEmpty(columnMeta)) { // 前面获取为空最后容错
        columnMeta = {
          '编码': 'infoCode',
          '名称': 'infoName',
          '说明': 'remark'
        }
      }
      return columnMeta
    },
    selectReiBaseTypeList() {
      return new Promise(resolve => {
        this.$callApiParams('selectReiBaseTypeList', { 'ascs': 'BASE_TYPE' }, result => {
          this.editableTabs = result.data
          this.$set(this.editableTabs)
          if (this.editableTabs.length > 0) {
            this.tempNode = this.editableTabs[0]
            var cityFlag = false
            var positionFlag = false
            this.editableTabs.forEach(item => {
              if (item.baseName === '城市名称') {
                this.cityId = item.id
                cityFlag = true
              }
              if (item.baseName === '人员职别') {
                this.positionId = item.id
                positionFlag = true
              }
            })
            this.showaccommodation = cityFlag && positionFlag
          }
          if (this.editableTabsValue.length === 0 && this.$isNotEmpty(this.editableTabs)) {
            this.editableTabsValue = this.editableTabs[0].id
            this.tabName = this.editableTabs[0].baseName
          }
          this.init()
          resolve()
          return true
        })
      })
    },
    async refreshTable(id, isParentId = false) {
      const tableIndex = this.editableTabs.findIndex(item => this.editableTabsValue === item.id)
      let tar = null
      const keys = Object.keys(this.loadMap)
      if (!isParentId) {
        keys.forEach(item => {
          const isHas = this.loadMap[item].childIds.includes(id)
          if (isHas) {
            tar = this.loadMap[item]
          }
        })
      } else {
        tar = this.loadMap[id]
      }
      if (!tar) {
        return
      }
      this.$set(this.$refs.multipleTable[tableIndex].store.states.lazyTreeNodeMap, tar.tree.id, [])
      await this.loadCityInfo(tar.tree, tar.treeNode, tar.resolve)
    },
    saveNode() {
      this.$callApi('saveReiBaseType', this.tempNode, result => {
        if (result.success) { // 保存以后修改
          this.tabName = this.tempNode.baseName
        }
        this.selectReiBaseTypeList()
        this.addNodeDlgVisible = false
      })
    },
    addTab(targetName) {
      const newTabName = ++this.tabIndex + ''
      this.editableTabs.push({
        baseName: 'New Tab',
        id: newTabName
      })
      this.editableTabsValue = newTabName
    },
    loadCityInfo(tree, treeNode, resolve) {
      const parentId = tree.bizid
      this.selectReiBaseTypeList()
      return new Promise(res => {
        // 请求后台服务，获取懒加载数据
        this.$callApiParams('selectReiBaseInfoByParentId', { 'ascs': 'BASE_TYPE', 'parent_id_eq': parentId }, result => {
          if (this.$isNotEmpty(result.data)) {
            this.loadMap[parentId] = {
              tree, treeNode, resolve,
              childIds: result.data.map(item => item.id)
            }
            resolve(result.data)
          } else {
            resolve([])
          }
          res()
          return true
        })
      })
    }, getInfoCode() {
      const infoCodeMax = !this.$isEmpty(this.tempNode.baseInfoList)
        ? Math.max.apply(Math, this.tempNode.baseInfoList.map(item => { return item.infoCode })) + 1 : 1
      if (infoCodeMax > 0 && infoCodeMax < 10) {
        this.baseInfo.infoCode = '00' + infoCodeMax
      } else if (infoCodeMax > 9 && infoCodeMax < 100) {
        this.baseInfo.infoCode = '0' + infoCodeMax
      } else {
        this.baseInfo.infoCode = infoCodeMax
      }
    },
    changeInfoCode(parentInfo) {
      var selectInfo = null
      // 获取所有的集合数据
      const allBase = this.tempNode.allBaseInfoList
      if (this.$isNotEmpty(parentInfo)) {
        // 手动切换父级Id
        selectInfo = allBase.filter(item => item.id === parentInfo)[0]
      } else {
        // 如果是城市名称，那么需要父级操作
        selectInfo = this.multipleSelection[0]
      }
      const selectId = selectInfo.id
      const selectInfoCode = selectInfo.infoCode
      const hasChildren = selectInfo.hasChildren
      const info = {
        parentId: selectId
      }

      if (hasChildren) { // 如果存在子级，那么就将子级中的数据都过滤
        const childrenCode = []
        allBase.map(item => {
          if (item.parentId === selectId) {
            childrenCode.push(item.infoCode)
          }
        })
        info.infoCode = Math.max.apply(Math, childrenCode) + 1
      } else {
        info.infoCode = selectInfoCode + '001'
      }
      this.baseInfo = { ...this.baseInfo, ...info }
      this.$refs.bxbaseeditdialog.changeBaseInfo(this.baseInfo)
    }
  }
}
</script>

<style lang="scss">
  // #baseTabs{
  //   overflow: auto;
  //   float:left;
  //   width:100%;
  //   height: calc(100% - 10px);
  //   padding: 8px;
  //   border: 1px solid #bbb;
  // }
  // #baseTabs .el-tabs__content{
  //   height:100%;
  // }
  #baseTabs{
    width:100%;
    height: calc(100% - 10px);
  }
  #baseTabs .el-tab-pane{
    overflow: auto;
    float:left;
    width:calc(100% - 20px);
    margin-left: 20px;
  }
  #baseTabs .el-tabs--left .el-tabs__header.is-left{
    border: 1px solid #DDDDDD;
  }
  #baseTabs .el-tabs__content{
    height:100%;
  }
  #baseTabs .el-tabs__item{
    // height: 35px !important;
    // padding: 0px 10px !important;
  }

  .hasVerticalTabs .el-tabs--left .el-tabs__header.is-left { margin-right: 0px; }
  .hasVerticalTabs .el-tabs__nav-wrap::after {background-color: unset; }
  .hasVerticalTabs #baseTabs { height: 100%; }
</style>
