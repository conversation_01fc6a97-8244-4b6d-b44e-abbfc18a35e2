<template>
   <div id="goabroadfeedialog">
    <el-dialog title="出国标准设置" append-to-body :visible.sync="goabroadfeedialog" :before-close="beforeClose" width="52%" id="goabroadfeedialog-wrap">
      <b-curd ref="curdList"/>
    </el-dialog>
    <bx-base-edit-dialog ref='bxbaseeditdialog' :dialog.sync='showbxbaseeditdialog' @reload='reload' />
  </div>
</template>

<script>
export default {
  name: 'goabroadfeedialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(n, o) {
      this.goabroadfeedialog = this.dialog
      if (n) {
        this.$nextTick(() => {
          this.init()
        })
      }
    }
  },
  data() {
    return {
      goabroadfeedialog: this.dialog,
      btEditBaseInfo: '',
      addBaseInfoDlgVisible: false,
      showbxbaseeditdialog: false,
      baseInfo: {
        id: '',
        infoCode: '',
        infoName: '数据名称',
        baseTypeId: '',
        baseType: 0,
        remark: ''
      },
      baseTypeId: '999',
      baseType: '999'
    }
  },
  methods: {
    init() {
      this.$refs.curdList.init({
        params: {
          dataApiKey: 'selectReiBaseInfoPageData',
          deleteApiKey: 'deleteReiBaseInfo',
          BASE_TYPE_ID_eq: this.baseTypeId,
          BASE_TYPE_eq: this.baseType,
          ascs: 'INFO_CODE'
        },
        searchForm: ['国家:info_name_like', '洲际:continent_like', '币种:currency_like'],
        showPager: false,
        hideCurdButton: ['新增', '修改', '详情', '删除'],
        buttons: [
          { text: '导入', icon: 'el-icon-aliiconshangchuan', enabledType: '0', click: this.showExcelImp },
          { text: '导出', icon: 'el-icon-aliiconxiazai', enabledType: '0', click: this.excelDownload }
        ],
        callbackRowDblclick: (row) => {
          this.editBaseInfo(row)
        }
      })
    },
    editBaseInfo(row) {
      this.baseInfo = Object.assign(this.baseInfo, row)
      this.$refs.bxbaseeditdialog.baseInfo = Object.assign({}, this.baseInfo)
      this.$refs.bxbaseeditdialog.btEditType = '修改'
      this.$refs.bxbaseeditdialog.tabName = '出国标准'
      this.showbxbaseeditdialog = true
    },
    reload() {
      this.init()
    },
    showExcelImp() { // 导入
      var apiKey = 'template/出国标准模板.xls'
      var fileName = '出国标准模板.xls'
      var tableColumn = []
      this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
        FORM_TYPE_eq: '出国标准',
        onSuccess: () => {
          this.init()
        } })
    },
    excelDownload(data) { // 导出
      const rowIds = this.$getTableCheckedIdsStrBy(data.params.rows)
      var columnMeta = {
        '编码': '编码',
        '国家': '国家',
        '洲际': '洲际',
        '币种': '币种',
        '住宿费': '住宿费',
        '伙食费': '伙食费',
        '公杂费': '公杂费'
      }
      var params = {
        文件名称: '报账基础数据.xls', exportExcelName: '出国标准基础数据.xls', BIZID_in: rowIds,
        columnMeta: columnMeta, BASE_TYPE_ID_eq: this.baseTypeId, ascs: 'INFO_CODE'
      }
      this.$fileDownloadBykey('报账基础数据导出表单', params)
    },
    saveReiBaseInfo() {
      this.$callApi('saveReiBaseInfo', this.relationForm, result => {
        this.init()
        this.addBaseInfoDlgVisible = false
      })
    },
    beforeClose() { // 弹出框关闭
      this.showExcelUpload = false
      this.goabroadfeedialog = false
      this.$emit('update:dialog', false)
      this.searchContent = ''
    },
    handleBaseDataList() {
      this.goabroadfeedialog = false
      this.$emit('update:dialog', false)
    }
  }
}
</script>

<style lang="scss">
#goabroadfeedialog-wrap{
  .el-dialog{
    height: 80%;
    display: flex;
    flex-direction: column;
    .el-dialog__body{
      display: flex;
    height: 100%;
    flex-direction: column;
    }
    .el-table{
      overflow: scroll
    }
  }
 .el-dialog__body  .el-table .el-table__body-wrapper .select-row {
    background: #f0f9eb;
  }
}

</style>
