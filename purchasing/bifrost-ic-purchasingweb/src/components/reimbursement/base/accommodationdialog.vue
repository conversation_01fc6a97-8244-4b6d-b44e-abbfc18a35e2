<template>
   <div id="accommodationdialog">
      <el-dialog title="住宿费标准设置" append-to-body :visible.sync="accommodationdialog" :before-close="beforeClose" width="1060px"
                 id="accommodationdialog-wrap">
        <!-- <dynamic-table :table-data="tableData" :table-header="tableConfig" v-if="dynamicTableShow"></dynamic-table> -->
        <template>
          <div style="display:flex;align-items: center;margin-bottom: 20px">
            <el-input v-model="searchContent" placeholder="搜索" size="medium" style="width:200px;"></el-input>
            <el-button plain icon="el-icon-aliiconshangchuan" size="mini" @click="showExcelImp" style="margin-left:10px;"> 导入</el-button>
          </div>
          <el-table :data="filterData" border @cell-dblclick="bccelldblclick"
                    ref="standardTable"
                    lazy
                    row-key="id"
                    :load="loadStandard"
                    :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <el-table-column type="index" width="50" align="center"></el-table-column>
            <template v-for="(item,index) in tableConfig">
              <table-column v-if="item.children && item.children.length" :key="item.id" :coloumn-header="item"></table-column>
              <el-table-column v-else :key="index" :label="item.label" :prop="item.prop" :width="item.width"  align="center" sortable></el-table-column>
            </template>
          </el-table>
        </template>
        <div slot="footer" class="dialog-footer">
          <el-button class="btn-normal" @click="beforeClose">取 消</el-button>
          <el-button class="btn-normal" type="primary" @click="handleBaseDataList">确 定</el-button>
        </div>
    </el-dialog>
    <el-dialog
            append-to-body
            width="740px"
            :title='`${btEditRelation}住宿费`'
            :close-on-click-modal="false"
            :visible.sync="addRelationDlgVisible">
      <el-form label-width="80px" :model="relationForm">
        <el-form-item label="城市名称">
            <el-input v-model="relationForm.city" :disabled="true"></el-input>
        </el-form-item>
        <div class="zfsStandard" v-if="!isHsf">
        <el-form-item label="人员职别">
            <el-input v-model="relationForm.position" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="淡季金额">
            <el-input
              oninput="value=value.replace(/[^\d.]/g,'')"
              v-model="relationForm.money1"
              placeholder="请输入淡季金额"
              clearable
            ></el-input>
        </el-form-item>
        <el-form-item label="旺季金额" style="margin-bottom: 0px">
          <el-input
              oninput="value=value.replace(/[^\d.]/g,'')"
              v-model="relationForm.money2"
              placeholder="请输入旺季金额"
              clearable
            ></el-input>
        </el-form-item>
        </div>
        <!-- 伙食费 -->
        <div class="hsfStandard" v-if="isHsf">
          <el-form-item label="伙食费">
            <el-input
              oninput="value=value.replace(/[^\d.]/g,'')"
              v-model="relationForm.hsfMoney"
              placeholder="请输入伙食费"
              clearable
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="btn-normal" @click="addRelationDlgVisible = false">取消</el-button>
        <el-button class="btn-normal" type="primary" @click="saveRelation">{{btEditRelation}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableColumn from './TableColumn'
export default {
  name: 'accommodationdialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    cityId: {
      type: String,
      default: () => []
    },
    positionId: {
      type: String,
      default: () => []
    }
  },
  watch: {
    dialog() {
      this.selectAccommodationTableData()
      this.accommodationdialog = this.dialog
    }
  },
  // 搜索数据
  computed: {
    filterData: function() {
      var input = this.searchContent && this.searchContent.toLowerCase()
      var items = this.tableData
      var data
      if (input) {
        data = items.filter(function(item) {
          return Object.keys(item).some(function(key) {
            var isResult = false
            isResult = String(item[key]).toLowerCase().match(input)
            if (key === 'relationList') {
              item[key].forEach(element => {
                if (!isResult) {
                  isResult = String(element.money1).toLowerCase().match(input) || String(element.money2).toLowerCase().match(input)
                }
                if (isResult) {
                  return isResult
                }
              })
            }
            return isResult
          })
        })
      } else {
        data = items
      }
      return data
    }
  },
  components: {
    TableColumn
  },
  data() {
    return {
      accommodationdialog: this.dialog,
      dynamicTableShow: true, // 使得DynamicTable组件重新渲染变量
      // 表数据
      tableData: [],
      loadStandardMap: {},
      // 表头数据
      tableConfig: [],
      btEditRelation: '',
      addRelationDlgVisible: false,
      isHsf: false,
      relationForm: {
        id: '',
        money1: 0,
        money2: 0,
        currentElementId: '',
        currentElementCode: '',
        currentElementType: '',
        nextElementId: '',
        nextElementCode: '',
        nextElementType: '',
        orderNo: 0,
        city: '',
        position: '',
        hsfMoney: '',
        cityId: ''
      },
      searchContent: ''
    }
  },
  methods: {
    showExcelImp() { // 导入
      var apiKey = 'template/住宿费模板.xls'
      var fileName = '住宿费模板.xls'
      var tableColumn = []
      this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
        FORM_TYPE_eq: '住宿费',
        cityId: this.cityId,
        positionId: this.positionId,
        onSuccess: () => {
          this.selectAccommodationTableData()
        } })
    },
    saveRelation() {
      const parentId = this.relationForm.parentId
      this.$callApi('saveReiBaseInfoRelation', this.relationForm, result => {
        this.addRelationDlgVisible = false
        if (this.$isEmpty(parentId)) {
          this.selectAccommodationTableData()
        } else {
          this.refreshStandardTable(parentId, true)
        }
      })
    },
    bccelldblclick(row, column, cell, event) { // 单元格双击
      // 获取点击的单元格属性值
      const property = column.property
      var relation = {}
      // 增加伙食费处理
      if (this.$isNotEmpty(property) && property === 'hsfMoney') {
        relation.cityId = row.id
        relation.hsfMoney = row.hsfMoney
        relation.city = row.city
        this.isHsf = true
      } else {
        const startIndex = property.indexOf('[')
        const endIndex = property.indexOf(']')
        // 获取对象信息
        const listName = property.substring(0, startIndex)
        const listIndex = property.substring(startIndex + 1, endIndex)
        if (listName.length > 0) {
          relation = row[listName][listIndex]
          if (this.$isEmpty(relation.city)) {
            relation.city = row.city
          }
        }
        this.isHsf = false
      }
      if (relation) {
        if (relation.id) {
          this.btEditRelation = '修改'
        } else {
          this.btEditRelation = '新增'
        }
        this.relationForm = Object.assign(this.relationForm, relation)
        this.addRelationDlgVisible = true
      }
    },
    beforeClose() { // 弹出框关闭
      this.showExcelUpload = false
      this.accommodationdialog = false
      this.$emit('update:dialog', false)
      this.searchContent = ''
    },
    handleBaseDataList() {
      this.accommodationdialog = false
      this.$emit('update:dialog', false)
    },
    selectAccommodationTableData() {
      this.$callApiParams('selectAccommodationTableData', { cityId: this.cityId, positionId: this.positionId }, result => {
        this.tableData = result.data.tableData
        this.tableConfig = result.data.tableConfig
        return true
      })
    },
    loadStandard(tree, treeNode, resolve) {
      const parentId = tree.id
      // 请求后台服务，获取懒加载数据
      this.$callApiParams('selectAccommodationTableData', {
        cityId: this.cityId,
        positionId: this.positionId,
        parentId: parentId
      }, result => {
        if (this.$isNotEmpty(result.data.tableData)) {
          this.loadStandardMap[parentId] = {
            tree, treeNode, resolve,
            childIds: result.data.tableData.map(item => item.id)
          }
          resolve(result.data.tableData)
        } else {
          resolve([])
        }
        return true
      })
    },
    async refreshStandardTable(id, isParentId = false) {
      let tar = null
      const keys = Object.keys(this.loadStandardMap)
      if (!isParentId) {
        keys.forEach(item => {
          const isHas = this.loadStandardMap[item].childIds.includes(id)
          if (isHas) {
            tar = this.loadStandardMap[item]
          }
        })
      } else {
        tar = this.loadStandardMap[id]
      }
      if (!tar) {
        return
      }
      this.$set(this.$refs.standardTable.store.states.lazyTreeNodeMap, tar.tree.id, [])
      await this.loadStandard(tar.tree, tar.treeNode, tar.resolve)
    }
  }
}
</script>

<style lang="scss">
#accommodationdialog-wrap{
  .el-dialog{
    height: 80%;
    display: flex;
    flex-direction: column;
    .el-dialog__body{
      display: flex;
    height: 100%;
    flex-direction: column;
    }
    .el-table{
      overflow: scroll
    }
  }
 .el-dialog__body  .el-table .el-table__body-wrapper .select-row {
    background: #f0f9eb;
  }
}

</style>
