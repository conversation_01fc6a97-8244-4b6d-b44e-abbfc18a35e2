<template>
  <el-dialog
    ref="auditEditDlg"
    class="auditEditDlg"
    :visible.sync="isShowDlg"
    :close-on-click-modal='false'
    append-to-body>
    <template slot="title">
      <div class="titleWarp">
        <span>{{`审核编辑设置 (当前节点：${nodeName})`}}</span>
        <el-select v-model="setOption" placeholder="请选择节点">
          <el-option v-for="(item,index) in nodeList"
            :key="index"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
        <el-button :disabled='loading' @click="copyNode">复制</el-button>
      </div>
    </template>
    <div style="height: 100%;">
      <div class="funcStrip">
        <el-input
          placeholder="Enter搜索"
          v-model="filterText">
        </el-input>

        <el-tree
          class="filter-tree"
          :data="editVo"
          :props="defaultProps"
          v-loading="loading"
          :filter-node-method="filterNode"
          ref="tree">
          <template slot-scope="{ node, data }">
            <span v-if="data.children.length > 0">
              {{ node.label }}
              <el-checkbox v-if="node.level === 2"
                v-model="data.isChecked"
                style="margin-left: 5px"
                @change="(value) => checkAll(value, data)">
                全选
              </el-checkbox>
              <el-checkbox v-if="node.level === 2 && node.label !== '基本要素'"
                v-model="data.isShowButton"
                style="margin-left: 5px"
              >
                显示按钮
              </el-checkbox>
            </span>
            <div class="filter-item" v-else>
              <el-checkbox @change="modifyAttribute(data)" v-model="data.isChecked">
                {{ node.label }}
                <span v-if="data.itemKey">({{data.itemKey}})</span>
              </el-checkbox>
              <p v-if="data.modify">
                (
                  {{data.exData.colItem.isShow === '是' ? '隐藏' : '显示'}},
                  {{data.exData.colItem.isEnabled === '是' ? '编辑' : '不可编辑'}},
                  {{data.exData.colItem.isRequired ? '必填' : '非必填'}}
                )
              </p>
            </div>
          </template>
        </el-tree>
        <!-- <sup-tree ref="colItemsTree"
                  :nodes="colItems" :is-popover="false" :check-enable="true"
                  :uncheckedWhenInit = "true"
                  @afterChecked="colItemChecked"
                  @afterClicked="colItemChecked"/> -->
      </div>
      <div class="funcStrip formListAction">
        <!-- <div>
          <sup-tree ref="formsTree"
                    :nodes="forms" :is-popover="false" :check-enable="true"
                    @afterChecked="formChecked"/>
        </div> -->
        <div>
          <el-form label-width="73px" size="mini">
            <el-row type="flex">
              <el-col :span="8">
                <el-form-item label="是否隐藏">
                  <el-radio v-model="colItem.isShow" label="是" :disabled="isDisabledRequired">是</el-radio>
                  <el-radio v-model="colItem.isShow" label="否" :disabled="isDisabledRequired">否</el-radio>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否编辑">
                  <el-radio v-model="colItem.isEnabled" label="是" :disabled="isDisabledRequired">是</el-radio>
                  <el-radio v-model="colItem.isEnabled" label="否" :disabled="isDisabledRequired">否</el-radio>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否必填">
                  <el-radio v-model="colItem.isRequired" :label="true" :disabled="isDisabledRequired">是</el-radio>
                  <el-radio v-model="colItem.isRequired" :label="false" :disabled="isDisabledRequired">否</el-radio>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="错误提示" style="width: 100%">
              <el-input :disabled="isDisabledRequired" type="textarea" :rows="2" show-word-limit
                        placeholder="请输入内容" maxlength="50" v-model="colItem.validateMessage">
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="btn-normal" @click="quit">取消</el-button>
      <el-button class="btn-normal" type="primary" @click="saveEditVo">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'wf-audit-edit',
  props: {
    editNodes: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      loading: false,
      nodeList: [],
      nodeOption: [], // 选项框
      copyNodeList: [], // 合并的节点
      setOption: '',
      formsOrigin: [],
      forms: [],
      colItems: [],
      colItem: this.getEmptyColItem(),
      editVo: undefined,
      isShowDlg: false,
      nodeId: '',
      nodeName: '',
      metaId: '',
      metaVersionId: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      filterText: '',
      isCopy: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  computed: {
    hasSelectedItem() {
      return (this.colItem && this.colItem.id)
    },
    isColText() { // 要素是否是显示文本
      return this.colItem.colType && this.colItem.colType.startsWith('文本')
    },
    isColNumber() { // 要素类型是否是数值
      return (this.colItem.colType === '整数' ||
        this.colItem.colType === '小数' ||
        this.colItem.colType === '百分比' ||
        this.colItem.colType === '金额')
    },
    isDisabledMinMax() { // 可用性：最大最小值
      return (!this.hasSelectedItem ||
        (!this.isColNumber && !this.isColText) ||
        this.colItem.colType === '公示')
    },
    isDisabledRequired() { // 可用性：是否必填
      if (this.nodeOption.length < 1) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.colItem = {
          ...this.colItem,
          isShow: '否',
          isEnabled: '是',
          isRequired: false,
          validateMessage: ''
        }
        return true
      } else if (this.nodeOption.length > 0) {
        var isShow = ''
        var isEnabled = ''
        var isRequired = ''
        var validateMessage = ''
        let status = false
        this.nodeOption.map((item, index) => {
          if (index === 0) {
            isShow = item.exData.colItem.isShow
            isEnabled = item.exData.colItem.isEnabled
            isRequired = item.exData.colItem.isRequired
            validateMessage = item.exData.colItem.validateMessage
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.colItem = {
              ...this.colItem,
              isShow: item.exData.colItem.isShow,
              isEnabled: item.exData.colItem.isEnabled,
              isRequired: item.exData.colItem.isRequired,
              validateMessage: item.exData.colItem.validateMessage
            }
            if (this.nodeOption.length === 1) {
              status = false
              return
            }
          } else {
            if (item.exData.colItem.isShow !== isShow || item.exData.colItem.isEnabled !== isEnabled ||
              item.exData.colItem.isRequired !== isRequired || item.exData.colItem.validateMessage !== validateMessage) {
              status = true
              return
            }
          }
        })
        return status
      }
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    initMeta(node) {
      this.nodeOption = []
      this.nodeList = []
      if (this.editNodes) {
        const arr = Object.keys(this.editNodes)
        Object.values(this.editNodes).map((item, index) => {
          if (item.bizid && node.name !== item.name) {
            this.nodeList.push({
              ...item,
              id: arr[index]
            })
          }
        })
      }
      this.isShowDlg = true
      this.setOption = ''
      this.nodeId = node.bizid
      this.nodeName = node.name
      this.metaVersionId = node.metaVersionId
      this.metaId = node.metaId

      this.forms = []
      this.formsOrigin = []
      this.colItems = []
      this.colItem = this.getEmptyColItem()
      this.loading = true
      this.$callApiParams('selectNodeFromEditSetting',
        {
          wfMetaId: this.metaId,
          nodeName: this.nodeName },
        result => {
          this.editVo = result.data
          this.loading = false
          // this.colItems = this.editVo.colsForm
          // this.formsOrigin = this.editVo.forms
          this.initChecked()
          return true
        })

      this.$nextTick(() => { // 这里设置弹框大小
        this.$setDlgSize(this, 'auditEditDlg', 840, 650)
      })
    },
    initChecked() {
      setTimeout(() => {
        for (let i = 0; i < this.colItems.length; i++) {
          // 初始化时高亮选中第一个checked节点，并且加载第一个节点的审核编辑设置
          if (this.colItems[i].isChecked &&
            this.colItems[i].id !== 'ROOT_ID') {
            this.$refs.colItemsTree.selectAndCheckNode(this.colItems[i].id)
            break
          }
        }
      }, 1000)
    },
    getEmptyColItem() {
      return {
        isShow: '否',
        isEnabled: '否',
        isRequired: false,
        validateMessage: '',
        mix: 0,
        max: 0,
        reserveData: '' }
    },
    formChecked(event, treeId, treeNode) {
      // 表单列表每次勾选时，刷新当前要素排除表单数据
      var excludeFormIds = []
      if (this.hasSelectedItem) {
        if (treeNode.id !== 'ROOT_ID') {
          excludeFormIds = this.colItem.reserveData.split(',')
          var formNodeIndex = excludeFormIds.indexOf(treeNode.id)
          if (treeNode.checked === true) { // 点击勾选单个表单
            if (formNodeIndex > -1) { // 如果原来已经记录排除这个表单，此时需要修改记录
              excludeFormIds.splice(formNodeIndex, 1)
            }
          } else if (formNodeIndex < 0) {
            // 点击不勾选单个表单，并且原来并没有排除这个表单，则要修改记录添加排除
            excludeFormIds.push(treeNode.id)
          }
          this.colItem.reserveData = excludeFormIds.join(',')
        } else if (treeNode.checked) { // 点击父级节点全选表单，清空排除记录
          this.colItem.reserveData = ''
        } else { // 点击父级节点全不选表单，则记录排除所有表单
          this.forms.forEach(item => {
            if (item.id !== 'ROOT_ID') {
              excludeFormIds.push(item.id)
            }
          })
          this.colItem.reserveData = excludeFormIds.join(',')
        }
      }
    },
    colItemSelectAll() {
      // 全选要素后，高亮选中第一个要素，索引0节点是父节点
      if (this.colItems.length > 1) {
        this.$nextTick(() => {
          this.$refs.colItemsTree.selectAndCheckNode(this.colItems[1].id)
        })
      }
    },
    clearColItemAndForms() {
      this.colItem = this.getEmptyColItem()
      this.forms = []
    },
    colItemChecked(event, treeId, treeNode) {
      if (treeNode.id !== 'ROOT_ID') {
        if (treeNode.checked === true) {
          // 需要先设置为空[]，然后再nextTick中设置，
          // 否则会出现表单列表勾选不能正常刷新的情况
          this.forms = []
          this.colItem = treeNode.exData.colItem

          // 勾选一个要素时，同步刷新勾选的表单
          var excludeFormIds = this.colItem.reserveData.split(',')
          this.formsOrigin.forEach(item => {
            item.isChecked = (excludeFormIds.indexOf(item.id) < 0)
          })
          this.$nextTick(() => { this.forms = this.formsOrigin })
        } else {
          this.clearColItemAndForms()
        }
      } else {
        treeNode.checked ? this.colItemSelectAll() : this.clearColItemAndForms()
      }
    },
    // 修改配置
    modifyAttribute(data) {
      const arr = []
      if (this.nodeOption.length < 1) {
        this.nodeOption.push(data)
      } else {
        this.nodeOption.map((item, index) => {
          arr.push(item.id)
        })
        this.nodeOption.map((item, index) => {
          if (item.id === data.id) {
            this.nodeOption.splice(index, 1)
          } else {
            if (!arr.includes(data.id)) {
              arr.push(data.id)
              this.nodeOption.push(data)
            }
          }
        })
      }
    },
    copyNode() {
      if (!this.setOption) return this.$message.error('请选择节点')
      const params = {
        sourceName: this.setOption,
        wfMetaId: this.metaId
      }
      this.loading = true
      this.$callApiParams('copyEditSetting', params,
        async result => {
          this.nodeOption = []
          this.loading = false
          this.editVo = result.data
          // await result.data.map((item, index) => {
          //   if (item.id === this.editVo[index].id) {
          //     item.children.map((items, flag) => {
          //       if (items.id === this.editVo[index].children[flag].id) {
          //         items.children.map((data, flags) => {
          //           if (this.editVo[index].children[flag].children[flags].id === data.id) {
          //             this.$set(this.editVo[index].children[flag].children[flags].exData, 'colItem', data.exData.colItem)
          //           }
          //         })
          //       }
          //     })
          //   }
          // })
        })
    },
    // 数据暂存
    staging() {
      this.editVo.map((item, index) => {
        if (item.children.length > 0) {
          this.stagingMap(item.children)
        }
      })
      this.nodeOption = []
    },
    // 暂存的循环回调
    stagingMap(item) {
      item.map((items, index) => {
        if (items.children.length > 0) {
          this.stagingMap(items.children)
        } else {
          if (items.isChecked || items.modify) {
            item[index].exData.colItem = {
              ...items.exData.colItem,
              isShow: this.colItem.isShow,
              isEnabled: this.colItem.isEnabled,
              isRequired: this.colItem.isRequired,
              validateMessage: this.colItem.validateMessage
            }
            this.$set(item[index], 'isChecked', false)
            this.$set(item[index], 'modify', true) // 自定义属性是否修改过值
          }
        }
      })
    },
    // 保存的循环回调
    mapItem(item) {
      item.map((items, index) => {
        if (items.children.length > 0) {
          this.mapItem(items.children)
        } else {
          if (!items.modify && !items.isChecked) {
            delete item[index]
          } else if (items.isChecked) {
            item[index].exData.colItem = {
              ...items.exData.colItem,
              isShow: this.colItem.isShow,
              isEnabled: this.colItem.isEnabled,
              isRequired: this.colItem.isRequired,
              validateMessage: this.colItem.validateMessage
            }
          }
        }
      })
    },
    saveEditVo() {
      const obj = JSON.parse(JSON.stringify(this.editVo))
      obj.map((item, index) => {
        if (item.children.length > 0) {
          this.mapItem(item.children)
        }
      })
      // nodesAll分两级，第一级是根节点，第二级才是要素节点集合
      // 特别的，nodesAll中包含有由于搜索关键字导致没有显示的节点
      // var nodesAll = this.$refs.colItemsTree.treeObj.getNodes()
      // nodesAll[0].children.forEach(n => {
      //   if (n.checked === true && n.id !== 'ROOT_ID') {
      //     var colItem = n.exData.colItem
      //     colItem.min = this.$isNumber(colItem.min) ? parseFloat(colItem.min) : 0
      //     colItem.max = this.$isNumber(colItem.max) ? parseFloat(colItem.max) : 0

      //     // 防止最小值不可编辑，然后又必填，导致后端校验不通过不能保存
      //     if (colItem.isRequired &&
      //       colItem.min === 0 &&
      //       !this.isColNumber) {
      //       colItem.min = 1
      //     }
      //     this.editVo.cols.push(colItem)
      //   }
      // })
      const params = {
        wfMetaId: this.metaId,
        nodeName: this.nodeName,
        editVos: obj
      }
      this.$callApi('storeNodeFromEditSetting', params,
        result => {
          if (result.success) {
            this.quit()
          }
        })
    },
    quit() {
      this.isShowDlg = false
      this.setOption = ''
      this.nodeOption = []
      this.copyNodeList = []
    },
    checkAll(isCheck, node) {
      const children = node.children || []
      children.forEach(item => {
        if (item.isChecked !== isCheck) {
          this.$set(item, 'isChecked', isCheck)
          this.modifyAttribute(item)
        }
      })
    }
  }
}
</script>

<style lang="scss" scope>
.auditEditDlg .el-dialog{
  width: 80%!important;
  height: 90%!important;
  overflow-y:hidden ;
}
.auditEditDlg .el-dialog__body {
  height: calc(100% - 112px) !important;
}
.auditEditDlg .funcStrip {
  border: #AAB7C4 solid 1px;
  height: 80%;
  padding: 5px;
  .el-tree {
    overflow-y: scroll;
    height: calc( 100% - 50px);
    .el-tree-node__children .el-tree-node__children {
      display: flex;
      flex-wrap: wrap;
      // width: 760px;
    }
  }
  .el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node >.el-tree-node__children > .el-tree-node {
    width: 20%;
  }
}
.titleWarp{
  .el-select{
    margin: 0 20px;
  }
}
.auditEditDlg .el-tree .el-tree-node__content {
  height: auto;
  .filter-item{
    p{
      margin-bottom: 0;
      font-size: 12px;
      color: #409eff;
    }
  }
}
.auditEditDlg .formListAction {
  display: flex;flex-direction: column;height: 20%;
  border: none;margin-top: 20px; padding: 0;}
.auditEditDlg .formListAction > div { border: #AAB7C4 solid 1px;padding: 5px; }
.auditEditDlg .formListAction > div:first-child { flex:1; margin-bottom: 10px;overflow: auto; }
.auditEditDlg .formListAction > div:last-child { height: 135px; overflow: hidden; }
.auditEditDlg .formListAction > div:last-child .el-form-item { margin-bottom: 8px; }
.auditEditDlg .formListAction > div:last-child .el-form-item:first-child { margin-bottom: 2px; }
.auditEditDlg .formListAction > div:last-child textarea { height: 56px; width: 100%;line-height: 18px; }
.auditEditDlg .formListAction > div:last-child textarea:disabled {
  background-color: #F5F7FA;border-color: #E4E7ED;color: #C0C4CC;cursor: not-allowed;}
.auditEditDlg .formListAction > div:last-child .el-radio { margin-right: 10px; }
.auditEditDlg .formListAction > div:last-child .el-radio__label { padding-left: 2px; }
</style>
