<template>
  <div style="height: 100%">
    <el-dialog
      width="10"
      height="10"
      ref="modifyWfUsefulDataDlg"
      class="modifyWfUsefulDataDlg"
      :title="`节点部门设置 (当前节点：${nodeName})`"
      :visible.sync="isUserDept"
      :close-on-click-modal='false'
      append-to-body>
      <div style="height: 100%;">
        <div style="float: left;border: #AAB7C4 solid 1px;height: 100%;padding: 5px;width: 49%">
          <sup-tree :setting="userSetting"
                    ref="userSupTree"
                    :nodes="userData"
                    :is-popover="false"
                    :edit-enable="true"></sup-tree>
        </div>
        <div style="float: right;border: #AAB7C4 solid 1px;height: 100%;padding: 5px;width: 49%;">
          <sup-tree :setting="depSetting"
                    ref="depSupTree"
                    :nodes="deptData"
                    :is-popover="false"
                    :edit-enable="true"></sup-tree>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button style="float: left;" :disabled="isOutDisabled" @click="optOutexamine">不参与审核</el-button>
        <el-button style="float: left;" :disabled="isCancelOutDisabled" @click="cancelOptOutexamine">取消不参与审核</el-button>
        <el-button class="btn-normal" @click="userDeptImport ">导入</el-button>
        <el-button class="btn-normal" @click="isUserDept = false">取消</el-button>
        <el-button class="btn-normal" type="primary" @click="selectUseful">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'wf-userdept-list',
  data() {
    return {
      isUserDept: false,
      userData: [],
      deptData: [],
      userSetting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'userCode',
            pIdKey: 'parentCode'
          },
          key: {
            name: 'userName'
          }
        },
        view: {
          showIcon: true,
          showLine: true
        },
        callback: {
          onClick: this.userNodeClick

        }
      },
      depSetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'deptCode',
            pIdKey: 'parentCode'
          },
          key: {
            checked: 'isCheck',
            name: 'deptName'
          }
        },
        view: {
          showIcon: true,
          showLine: true
        },
        callback: {
          onClick: this.depNodeClick,
          onCheck: this.depCheckChange
        }
      },
      nodeId: '',
      nodeName: '',
      metaId: '',
      metaVersionId: '',
      WfUserDeptVo: {},
      isOutDisabled: true,
      isCancelOutDisabled: true,
      wfNodeExcludeUserEntity: {
        metaId: '',
        metaVersionId: '',
        nodeId: '',
        nodeName: '',
        userCode: '',
        userName: ''
      },
      userDept: [],
      currUserCode: '', // 当前选中用户编码
      currUserName: ''// 当前选中用户名称
    }
  },
  methods: {
    initMeta(node) {
      this.isUserDept = true
      this.nodeId = node.bizid
      this.nodeName = node.name
      this.metaVersionId = node.metaVersionId
      this.metaId = node.metaId
      this.userDept = []
      this.deptData = []
      this.$callApiParams('selectAuditUserByNode', { nodeId: this.nodeId, metaId: this.metaId,
        metaVersionId: this.metaVersionId, nodeName: this.nodeName }, result => {
        this.userData = result.data
        return true
      })

      this.$nextTick(() => {
        // 这里设置弹框大小
        this.$setDlgSize(this, 'modifyWfUsefulDataDlg', 800, 650)
      })
    },
    // 导入
    userDeptImport() {
      var apiKey = 'template/节点审核权限导入模板.xls'
      var fileName = '节点审核权限导入模板.xls'
      var tableColumn = []
      this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
        metaVersionId: this.metaVersionId,
        metaId: this.metaId,
        onSuccess: () => {
          this.init()
        } })
    },

    // 保存
    selectUseful() {
      this.WfUserDeptVo.metaId = this.metaId
      this.WfUserDeptVo.nodeId = this.nodeId
      this.WfUserDeptVo.metaVersionId = this.metaVersionId
      this.WfUserDeptVo.nodeName = this.nodeName
      this.WfUserDeptVo.userDept = this.userDept
      this.$callApi('saveWfUserDept', this.WfUserDeptVo, result => {
        this.isUserDept = false
        this.$emit('useDeptChange')
      }, result => {

      })
    },
    // 不参与审核
    optOutexamine() {
      this.wfNodeExcludeUserEntity.metaId = this.metaId
      this.wfNodeExcludeUserEntity.nodeId = this.nodeId
      this.wfNodeExcludeUserEntity.metaVersionId = this.metaVersionId
      this.wfNodeExcludeUserEntity.nodeName = this.nodeName
      this.wfNodeExcludeUserEntity.userCode = this.currUserCode
      this.wfNodeExcludeUserEntity.userName = this.currUserName
      this.$callApi('saveWfNodeExcludeUser', this.wfNodeExcludeUserEntity, result => {
        this.userData = []
        this.$callApiParams('selectAuditUserByNode', { nodeId: this.nodeId, metaId: this.metaId,
          metaVersionId: this.metaVersionId, nodeName: this.nodeName }, result => {
          this.userData = result.data
          this.isOutDisabled = true
          this.deptData = []
          this.userDept = []
          return true
        })
      }, result => {
      })
    },
    // 取消不参与审核
    cancelOptOutexamine() {
      this.wfNodeExcludeUserEntity.metaId = this.metaId
      this.wfNodeExcludeUserEntity.nodeId = this.nodeId
      this.wfNodeExcludeUserEntity.metaVersionId = this.metaVersionId
      this.wfNodeExcludeUserEntity.nodeName = this.nodeName
      this.wfNodeExcludeUserEntity.userCode = this.currUserCode
      this.wfNodeExcludeUserEntity.userName = this.currUserName
      this.$callApi('cancelWfNodeExcludeUser', this.wfNodeExcludeUserEntity, result => {
        this.userData = []
        this.$callApiParams('selectAuditUserByNode', { nodeId: this.nodeId, metaId: this.metaId,
          metaVersionId: this.metaVersionId, nodeName: this.nodeName }, result => {
          this.userData = result.data
          this.isCancelOutDisabled = true
          this.deptData = []
          this.userDept = []
          return true
        })
      }, result => {

      })
    },
    // 用户点击事件
    userNodeClick(event, treeId, treeNode) {
      this.currUserCode = treeNode.userCode
      this.currUserName = treeNode.userName
      var _this = this
      if (treeNode.userName.includes('[不参与审核]')) {
        this.isCancelOutDisabled = false
        this.isOutDisabled = true
        this.currUserName = treeNode.userName.replace('[不参与审核]', '')
        this.deptData = []
        this.userDept = []
      } else {
        this.isOutDisabled = false
        this.isCancelOutDisabled = true
        this.currUserName = treeNode.userName
        var isUser = false// 是否第一次点击用户第一次点击加载后台选中数据
        var deps = []
        for (var i = 0; i < _this.userDept.length; i++) {
          if (_this.userDept[i].userCode === _this.currUserCode) {
            _this.deptData = _this.userDept[i].dep
            deps = _this.userDept[i].deps
            isUser = true
          }
        }
        if (deps.length > 0) {
          for (let i = 0; i < deps.length; i++) {

          }
        }
        if (isUser) {
          return
        }

        this.$callApiParams('selectUserDept', {
          userCode: treeNode.userCode,
          nodeId: this.nodeId,
          nodeName:this.nodeName,
          metaId:this.metaId
        },
        result => {
          _this.deptData = result.data
          if (!isUser && _this.deptData.length > 0) { // 第一次加载时需要加载后台数据
            this.userDept.push({
              userCode: _this.currUserCode, userName: _this.currUserName,
              deps: deps, dep: result.data
            })
            for (let i = 0; i < _this.deptData.length; i++) {
              if (_this.deptData[i].isCheck) {
                deps.push({
                  deptName: _this.deptData[i].deptName,
                  deptCode: _this.deptData[i].deptCode
                })
              }
            }
          }

          return true
        })
      }
    },
    sleep(time) {
      return new Promise((resolve) => setTimeout(resolve, time))
    },
    depNodeClick(event, treeId, treeNode, clickFalg) {
      this.$refs.depSupTree.treeObj.checkNode(treeNode, !treeNode.checked, true, true)
    },
    // 部门选择发生改变事件
    depCheckChange(event, treeId, treeNode) {
      var deps = []
      var isUser = false // 数组中是否存在当前用户
      if (this.userDept.length >= 0) {
        for (var i = 0; i < this.userDept.length; i++) {
          if (this.userDept[i].userCode === this.currUserCode) {
            deps = this.userDept[i].deps
            isUser = true
          }
        }
      }
      if (!isUser) {
        this.userDept.push({
          userCode: this.currUserCode, userName: this.currUserName,
          deps: deps, dep: this.deptData
        })
      }
      if (treeNode.isCheck) { // 新增选中
        deps.push({ deptName: treeNode.deptName, deptCode: treeNode.deptCode })
      } else {
        for (let i = 0; i < deps.length; i++) {
          if (deps[i].deptCode === treeNode.deptCode) {
            var index = deps.indexOf(deps[i])
            deps.splice(index, 1)
          }
        }
      }
      if (this.deptData.length>0) {
        for (let i = 0; i < this.deptData.length; i++) {
          this.deptData[i].isCheck = false
          for (let j = 0; j < deps.length; j++) {
            if (this.deptData[i].deptCode === deps[j].deptCode) {
              this.deptData[i].isCheck = true
            }
          }
        }
      }
    }
  }
}
</script>

<style>
.modifyWfUsefulDataDlg .el-dialog__body { height: calc(100% - 112px) !important; }
</style>
