<template>
    <el-dialog width="420px" title="选择退回节点" :visible.sync="dialogFormVisible">
      <el-form  style="padding-top: 5px">
        <el-form-item label="选择节点" :label-width="formLabelWidth">
          <el-select v-model="nodeId" placeholder="请选择" :popper-append-to-body="false">
            <el-option
              v-for="item in nodes"
              :key="item.label"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" :label-width="formLabelWidth">
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
            v-model="opinion">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click=submit>确 定</el-button>
      </div>
    </el-dialog>
</template>
<script>
export default {
  name: 'wf-select-node-dialog',
  data() {
    return {
      dialogFormVisible: false,
      nodes: [{ value: '', label: '' }],
      form: {},
      formLabelWidth: '80px',
      nodeId: '',
      dataType: '',
      dataId: '',
      onSuccess: undefined,
      opinion: ''// 审核意见
    }
  },
  methods: {
    showDlg(dataId, dataType, onSuccess) {
      this.dialogFormVisible = true
      this.dataType = dataType
      this.dataId = dataId
      var params = {}
      params.ids = dataId
      params.dataType = dataType
      this.nodes = []
      this.nodeId = ''
      this.onSuccess = onSuccess
      this.$callApiParams('getWfNodes', params, (result) => {
        for (let i = 0; i < result.data.length; i++) {
          this.nodes.push({ value: result.data[i].bizid, label: result.data[i].name })
        }
        return true
      })
    },
    submit() {
      var params = {}
      params.ids = this.dataId
      params.dataType = this.dataType
      params.nodeId = this.nodeId
      params.opinion = this.opinion
      this.$callApiParams('WFWITHDRAW', params, (result) => {
        if (typeof this.onSuccess === 'function') {
          this.onSuccess()
        }
        this.dialogFormVisible = false
      })
    }
  }
}
</script>

<style scoped>

</style>
