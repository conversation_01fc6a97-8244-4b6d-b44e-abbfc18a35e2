<template>
  <div style="width:100%;height:100%">
    <div style="width:100%;height:100%" class="commonWarp" ref="commonWarp">
      <div v-if="showCanvas" class="formWarp" :style="{height:modifyH ? wfCanvasHeight + 'px':''}">
        <el-form ref="form"
                 label-width="90px"
                 size="mini"
                 :class="`${elFormClass}`"
                 :model="this">
          <el-form-item style="margin-bottom:2px;border: 1px solid #bbb;">
            <template slot="label">
              当前所在环节:
            </template>
            {{ currentNode }}{{isCanceled? '(已作废)' : ''}}
          </el-form-item>
          <el-form-item style="border: 1px solid #bbb;" v-if="!isEndOfAudit">
            <template slot="label">
              待办人:
            </template>
            <el-tooltip class="item" effect="dark" :disabled="!readyRoleUser" :content="readyRoleUser" placement="top">
              <el-input type="textarea"
                v-model="readyRoleUser"
                :readonly="true">
              </el-input>
            </el-tooltip>
          </el-form-item>
          <el-form-item label-width="100%" style="border: 1px solid #bbb;width: 100%" v-if="!isEndOfAudit">
            <template slot="label">
              提示:送审/审核后可看到下环节处理人
            </template>
          </el-form-item>
        </el-form>
        <wfcanvas
                ref="wfcanvass"
                :currentNodeId="currentNodeId"
                :auditNodeList="auditNodeList"
                :auditNodeConectionList="auditNodeConectionList"
                :readOnly="true"
                canDrag
                :canvasId="wfCanvasId"/>
        <div class="extra" v-if="isDesignView">
          <div class="extraSlider" v-if="true">
            <span class="extraSliderLabel">详情弹框宽度</span>
            <el-slider v-model="meta.instance.detailDlgWidth"
                       :min="800" :max="2200" @change="sliderChange"/>
          </div>
          <!-- <div class="extraSlider" v-if="true">
            <span class="extraSliderLabel">详情弹框高度</span>
            <el-slider v-model="meta.instance.detailDlgHeight"
                       :min="500" :max="960" @change="sliderChange"/>
          </div> -->
        </div>
      </div>
      <div class="tableWarp" :style="{height: showCanvas ?
      `${topHeight}px` : '100%'}">
        <drag-y @handleMouseUp="handleMouseUp"></drag-y>
        <el-table
          ref="table"
          :data="rowsData"
          border
          style="width: 100%;height: 100%;">
          <el-table-column prop="createTimeStr" label="操作时间" align="center" width="160px"/>
          <el-table-column prop="nodeName" label="业务环节" width="200px"/>
          <el-table-column prop="action" label="操作类型" width="80px"/>
          <el-table-column prop="auditUser" label="操作人/审核人" width="120px"/>
          <el-table-column prop="opinion" label="备注/审核意见" show-overflow-tooltip/>
          <el-table-column label="操作" width="120px" align="center">
            <!-- v-if="row.aiAuditHis" -->
             <template slot-scope="{row}">
              <a v-if="row.aiAuditHis" href="javaScript:;" style="text-decoration: underline;cursor: pointer;" @click="auditHisShow(row)">日志详情</a>
               <a v-else-if="enableEdit" href="javaScript:;" style="text-decoration: underline;cursor: pointer;" slot-scope="{row}" @click="toEditDialog(row)">修改</a>
             </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog
      title="日志详情"
      :visible.sync="logVisible"
      width="60%"
      append-to-body
      >
        <blockTab class="common-page"
          v-if="logVisible"
          style="height: 500px"
          isDetail
          isHisDetail
          :tabs="logTabs"
          :billId="currentId"
          :nodeId="nodeId"
          :showMainPanel="false"></blockTab>
      <span slot="footer" class="dialog-footer">
        <el-button class="btn-normal" @click="logVisible = false">取 消</el-button>
        <el-button class="btn-normal" type="primary" @click="logVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="修改内容"
      :visible.sync="editHistoryVisible"
      width="30%"
      append-to-body
      >
      <el-form
        label-width="100px">
        <el-row>
          <el-form-item label="审核意见">
            <el-input v-model="editHistoryData.opinion" :disabled="this.editHistoryData.action === '保存'"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="审核时间" prop="auditTime">
            <el-date-picker type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                            v-model="editHistoryData.auditTime"
                            @change="validateDate"
                            placeholder="选择时间"
                            clearable/>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button class="btn-normal" @click="editHistoryVisible = false">取 消</el-button>
        <el-button class="btn-normal" type="primary" @click="editAuditHistory">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getUUID } from '../../utils'
export default {
  name: 'wf-audit-history',
  data() {
    return {
      topHeight: 235,
      modifyH: false,
      wfCanvasHeight: 0,
      rowsData: [],
      wfCanvasId: 'bifrost_workflow_canvas_view',
      isHistoryDlgVisible: false,
      isDesignView: true,
      currentNodeId: '',
      currentNode: '',
      readyRoleUser: '',
      readyRoleUserArr: [],
      auditNodeList: [],
      auditNodeConectionList: [],
      meta: { instance: { detailDlgHeight: 500, detailDlgWidth: 500 }},
      dataId: '',
      wfMetaId: '',
      wfMetaVersionId: '',
      dlg: null,
      parentObj: null,
      apiKey: '',
      logVisible: false,
      currentId: '',
      nodeId: '',
      logTabs: [],
      editHistoryVisible: false,
      enableEdit: false,
      editHistoryData: {
        auditTime: '',
        opinion: '',
        action: ''
      },
      auditStatus: ''
    }
  },
  computed: {
    showCanvas() {
      return this.wfMetaId || this.$isEmpty(this.dataId)
    },
    isEndOfAudit() {
      return this.auditStatus === '审核结束' || this.auditStatus === '已作废'
    },
    isCanceled() {
      return this.auditStatus === '已作废'
    },
    elFormClass() {
      if (this.auditStatus === '审核结束') {
        return 'wf-current-node wf-current-node-end'
      }
      if (this.auditStatus === '已作废') {
        return 'wf-current-node wf-current-node-cancel'
      }
      return 'wf-current-node wf-current-node-auditing'
    }
  },
  methods: {
    init(dlg, params) {
      // this.$setDlgSize(dlg, 'globalDialog', 1900, 960)
      this.dlg = dlg
      this.dataId = params.dataId
      this.apiKey = params.apiKey
      this.enableEdit = params.enableEdit
      this.wfCanvasId = 'bifrost_workflow_canvas_view' + getUUID()
      if (this.$isNotEmpty(this.dataId)) {
        this.isDesignView = false
        this.wfAuditList()
      } else {
        this.initMeta(params.metaData)
        this.parentObj = params.parentOjb
      }
    },
    initDlgSize(meta) {
      if (!this.dlg) return
      this.$setDlgSize(this.dlg, 'globalDialog',
        meta.instance.detailDlgWidth,
        meta.instance.detailDlgHeight)
    },
    // 开始垂直拖动
    handleMouseUp(eventH) {
      this.modifyH = true
      this.topHeight -= eventH
      this.topHeight = Math.max(this.topHeight, 235)
      this.topHeight = Math.min(this.topHeight, this.$refs.commonWarp.getBoundingClientRect().height - 3)
      if (this.topHeight === this.$refs.commonWarp.getBoundingClientRect().height - 3) {
        this.wfCanvasHeight = 0
      } else {
        const totalHeight = this.$refs.commonWarp.getBoundingClientRect().height // 显示区域总高
        this.wfCanvasHeight = totalHeight - this.topHeight - 10
      }
    },
    wfAuditList() {
      var params = {}
      params.actionKey = 'selectWfRemarkList'
      params.id = this.dataId
      let apiKey = this.apiKey
      if (!apiKey) {
        apiKey = 'wfActionCformDataEntity'
      }
      this.$callApiParams(apiKey,
        params, result => {
          this.auditStatus = result.data.status
          this.rowsData = result.data.remarkList
          this.auditNodeList = result.data.auditNodeList
          this.auditNodeConectionList = result.data.auditNodeConectionList
          this.currentNodeId = result.data.currentNodeId
          this.currentNode = result.data.currentNode
          this.readyRoleUser = result.data.nodeAssigns
          if (result.data.nodeAssigns) {
            this.readyRoleUserArr = result.data.nodeAssigns.split(',')
            this.auditStatus = '已送审'
          }
          this.wfMetaId = result.data.wfMetaId
          this.wfMetaVersionId = result.data.wfMetaVersionId
          this.wfCanvas()
          return true
        }, () => {
          this.$event(this, 'refDataInitFailed')
        })
    },
    wfCanvas() {
      if (!this.showCanvas) {
        this.meta.instance.detailDlgWidth = 800
        this.meta.instance.detailDlgHeight = 700
        this.initDlgSize(this.meta)
        return
      }
      this.$callApiParams('versionActionWfMetaEntity',
        { 'ids': this.wfMetaId,
          'versionId': this.wfMetaVersionId,
          actionKey: 'QUERYBYVERSIONID' }, result => {
          this.meta.instance.detailDlgWidth = result.data.instance.detailDlgWidth
          this.meta.instance.detailDlgHeight = result.data.instance.detailDlgHeight
          this.initDlgSize(result.data)
          this.$nextTick(() => {
            this.$refs.wfcanvass?.loadMeta(result.data)
          })
          return true
        })
    },
    initMeta(metaData) {
      var meta = JSON.parse(JSON.stringify(metaData.meta))
      this.$nextTick(() => {
        this.$nextTick(() => {
          this.meta.instance.detailDlgWidth = meta.instance.detailDlgWidth
          this.meta.instance.detailDlgHeight = meta.instance.detailDlgHeight
          if (meta.main.templateType === '系统模板') {
            this.isDesignView = false
          } else {
            this.isDesignView = true
          }
          this.initDlgSize(meta)
          this.$nextTick(() => {
            this.$refs.wfcanvass?.loadMeta(meta)
          })
        })
      })
    },
    sliderChange() {
      this.initDlgSize(this.meta)
      if (this.parentObj) {
        this.parentObj.sliderChange(this.meta)
      }
    },
    auditHisShow(row) {
      this.currentId = row.id
      this.nodeId = row.nodeId
      this.logTabs = []
      if (row.showAuditHis) {
        this.logTabs.push({
          label: '辅助审核',
          name: '辅助审核',
          component: 'assistBlock'
        })
      }
      if (row.showAuditFiles) {
        this.logTabs.push({
          label: '规章制度',
          name: '规章制度',
          component: 'ruleBlock'
        })
      }
      this.logVisible = true
    },
    toEditDialog(row) { // 修改时间弹窗
      this.currentId = row.id
      this.nodeId = row.nodeId
      this.logTabs = []
      this.editHistoryVisible = true
      this.$callApiParams('getAuditHistoryInfoById', { auditHistoryId: this.currentId },
        result => {
          if (result.success) {
            this.editHistoryData.auditTime = result.data.createTimeStr
            this.editHistoryData.opinion = result.data.opinion
            this.editHistoryData.action = result.data.action
            return true
          }
        })
    },
    validateDate(newDate) { // 校验要修改的时间范围
      let maxDate = null
      let minDate = null
      const index = this.rowsData.findIndex(obj => obj.id === this.currentId)
      if (index === 0) {
        if (this.rowsData.length > 1) {
          minDate = this.rowsData[index + 1].createTimeStr
        }
      } else if (index === this.rowsData.length - 1) {
        maxDate = this.rowsData[index - 1].createTimeStr
      } else {
        maxDate = this.rowsData[index - 1].createTimeStr
        minDate = this.rowsData[index + 1].createTimeStr
      }
      const startDate = new Date(newDate)
      let message = null
      if (maxDate && new Date(maxDate) < startDate) {
        message = '审核时间不能大于' + maxDate
      } else if (minDate && new Date(minDate) > startDate) {
        message = '审核时间不能小于' + minDate
      }
      const currDate = new Date()
      const applyDate = new Date(newDate)
      if (currDate < applyDate) {
        message = '审核时间不能大于当前时间'
      }
      if (message) {
        this.$message({
          message: message,
          type: 'warning'
        })
        return false
      }
      return true
    },
    editAuditHistory() { // 修改审核
      if (this.editHistoryData.action !== '保存' &&
        this.editHistoryData.action !== '送审' &&
        this.$isEmpty(this.editHistoryData.opinion)) {
        this.$message({
          message: '审核意见不能为空',
          type: 'warning'
        })
        return
      }
      const result = this.validateDate(this.editHistoryData.auditTime)
      if (!result) {
        return
      }
      this.$callApiParams('editWfAuditHistory',
        { auditHistoryId: this.currentId,
          newAuditDate: this.editHistoryData.auditTime,
          opinion: this.editHistoryData.opinion },
        result => {
          if (result.success) {
            this.wfAuditList()
            this.editHistoryVisible = false
          }
        })
    }
  }
}
</script>
<style scoped>
.tableWarp{
  width: 100%;
  position: relative;
  z-index: 999;
}
.formWarp{
  width: 100%;
  height: calc(100% - 265px);
  border: 1px solid #bbb;
  position: relative;
  z-index: 999;
}
.commonWarp{
  display: flex;
  flex-wrap: wrap;
}
.el-form.wf-current-node-end {
  border: 1px solid rgb(59, 176, 95) !important;
  background: rgb(120, 202, 144) !important;
  color: rgb(255, 255, 255) !important;
}
.el-form.wf-current-node-end ::v-deep .el-form-item--mini{
  border: 1px solid rgb(255, 255, 255) !important;
  margin-bottom: 0px !important;
}
.el-form.wf-current-node-end ::v-deep .el-form-item .el-form-item__label{
  color: rgb(255, 255, 255) !important;
}

.el-form.wf-current-node-cancel {

}
.el-form.wf-current-node-auditing {
  border: 1px solid rgb(121, 197, 252) !important;
  background: rgb(121, 197, 252) !important;
  color: rgb(255, 255, 255) !important;
  border-right: 1px solid white !important;
  padding-right: 1px !important;
}
.el-form.wf-current-node-auditing ::v-deep .el-form-item--mini{
  border: 1px solid rgb(255, 255, 255) !important;
}
.el-form.wf-current-node-auditing ::v-deep .el-form-item .el-form-item__label{
  color: rgb(255, 255, 255) !important;
}
.el-form.wf-current-node-auditing ::v-deep .el-textarea__inner {
  background: rgb(121, 197, 252) !important;
  color: rgb(255, 255, 255) !important;
}

</style>
<style>
  .extra {
    position: absolute;
    z-index: 1;
    right: 35px;
    bottom:30px;
  }
  .extraSlider {
    width: 200px;
    height: 30px;
  }
  .extraSlider .extraSliderLabel {
    font-size: 14px;
    color: #8492a6;
    line-height: 20px;
  }
  .extraSlider .extraSliderLabel+.el-slider{
    float: right;
    width: 50%;
    margin-top: 3px;
  }
  .extraSlider .el-slider__button {width: 14px; height: 14px;}
  .extraSlider .el-slider__runway {margin: 5px 0px;}

  .wf-current-node {
    position: absolute;
    z-index: 10;
    background-color: #fff;
    right: 3px;
    top: 3px;
    width: 220px;
  }
  .wf-current-node .el-form-item__label,
  .wf-current-node .el-form-item__content { font-size: 12px; }
  .wf-current-node .el-textarea__inner {
    min-height: 60px;
    margin: 1px;
    border-radius: 0px;
    width: 124px;
    padding: 4px 5px 5px 0px !important;
    border: none !important;
  }
  .wf-current-node .el-form-item--mini.el-form-item { margin-bottom: 0px; }
</style>
