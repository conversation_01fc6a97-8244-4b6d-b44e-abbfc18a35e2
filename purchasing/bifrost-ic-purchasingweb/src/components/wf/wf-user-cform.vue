<template>
  <div style="height: 100%">
    <el-dialog
      width="10"
      height="10"
      ref="modifyWfUsefulDataDlg"
      class="modifyWfUsefulDataDlg"
      :title="`表单权限设置 (当前节点：${nodeName})`"
      :visible.sync="isUserDept"
      :close-on-click-modal='false'
      append-to-body>
      <div style="height: 100%;">
        <div style="float: left;border: #AAB7C4 solid 1px;height: 100%;padding: 5px;width: 49%">
          <sup-tree :setting="CformSetting"
                    ref="userSupTree"
                    :nodes="CfromData"
                    :is-popover="false"
                    :edit-enable="true"></sup-tree>
        </div>
        <div style="float: right;border: #AAB7C4 solid 1px;height: 100%;padding: 5px;width: 49%;">
          <sup-tree :setting="userSetting"
                    ref="userSupTree"
                    :nodes="userData"
                    :is-popover="false"
                    :edit-enable="true"></sup-tree>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button class="btn-normal" @click="isUserDept = false">取消</el-button>
        <el-button class="btn-normal" type="primary" @click="selectUseful">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'wf-user-cform',
  data() {
    return {
      isUserDept: false,
      userData: [],
      CfromData: [],
      userSetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'userCode',
            pIdKey: 'parentCode'
          },
          key: {
            name: 'userName'
          }
        },
        view: {
          showIcon: true,
          showLine: true
        },
        callback: {
          onCheck: this.CheckChange
        }
      },
      CformSetting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'id'
          },
          key: {
            name: 'name'
          }
        },
        view: {
          showIcon: true,
          showLine: true
        },
        callback: {
          onClick: this.CformNodeClick

        }
      },
      nodeId: '',
      nodeName: '',
      metaId: '',
      metaVersionId: '',
      WfUserCformVo: {},
      userCform: [],
      currCformName: '' // 当前选中用户编码
    }
  },
  methods: {
    initMeta(node, bindObjitems) {
      this.CfromData = []
      this.userCform = []
      if (bindObjitems.length>0) {
        for (let i = 0; i<bindObjitems.length; i++) {
          var bind = []
          bind.id = i
          bind.name = bindObjitems[i]
          this.CfromData.push(bind)
        }
      }
      this.isUserDept = true
      this.nodeId = node.bizid
      this.nodeName = node.name
      this.metaVersionId = node.metaVersionId
      this.metaId = node.metaId
      this.userDept = []
      this.$callApiParams('selectUserByNode', { nodeId: this.nodeId },
        result => {
          this.userData = result.data
          return true
        })

      this.$nextTick(() => {
        // 这里设置弹框大小
        this.$setDlgSize(this, 'modifyWfUsefulDataDlg', 840, 650)
      })
    },
    // 点击
    CformNodeClick(event, treeId, treeNode) {
      this.$refs.userSupTree.clearCheck()
      this.currCformName = treeNode.name
      var isUser = false // 数组中是否存在当前用户
      var users = []
      if (this.userCform.length >= 0) {
        for (var i = 0; i < this.userCform.length; i++) {
          if (this.userCform[i].CformName === this.currCformName) {
            isUser = true
            users = this.userCform[i].users
          }
        }
      }
      // 不存在需要去后台加载
      if (!isUser) {
        this.$callApiParams('loadWfUserType', { nodeName: this.nodeName,
          metaId: this.metaId, currCformName: this.currCformName },
        result => {
          users = result.data
          for (var i = 0; i < users.length; i++) {
            const node = this.$refs.userSupTree.treeObj.getNodeByParam('userCode', users[i].userCode, null)
            this.$refs.userSupTree.treeObj.checkNode(node, true)
          }
          this.userCform.push({
            CformName: this.currCformName,
            users: users
          })
          return true
        })
      }

      if (users.length > 0) {
        for (let i = 0; i < users.length; i++) {
          const node = this.$refs.userSupTree.treeObj.getNodeByParam('userCode', users[i].userCode, null)
          this.$refs.userSupTree.treeObj.checkNode(node, true)
        }
      }
    },
    selectUseful() {
      this.WfUserCformVo.metaId = this.metaId
      this.WfUserCformVo.nodeId = this.nodeId
      this.WfUserCformVo.metaVersionId = this.metaVersionId
      this.WfUserCformVo.nodeName = this.nodeName
      this.WfUserCformVo.userCform = this.userCform
      this.$callApi('saveWfUserType', this.WfUserCformVo, result => {
        this.isUserDept = false
        this.$emit('useDeptChange')
      }, result => {

      })
    },
    // 选中
    CheckChange(event, treeId, treeNode) {
      var users = []
      var isUser = false // 数组中是否存在当前用户
      if (this.userCform.length >= 0) {
        for (var i = 0; i < this.userCform.length; i++) {
          if (this.userCform[i].CformName === this.currCformName) {
            users = this.userCform[i].users
            isUser = true
          }
        }
      }
      if (!isUser) {
        this.userCform.push({
          CformName: this.currCformName,
          users: users
        })
      }
      if (treeNode.checked) { // 新增选中
        users.push({ userName: treeNode.userName, userCode: treeNode.userCode })
      } else {
        for (let i = 0; i < users.length; i++) {
          if (users[i].userCode === treeNode.userCode) {
            var index = users.indexOf(users[i])
            users.splice(index, 1)
          }
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
