<template>
  <el-dialog ref="wfMoreSettingDlg" append-to-body class="wfMoreSettingDlg" :close-on-click-modal="false" :visible.sync="moreSettingVisible">
    <page>
      <template #pageContent>
        <el-tabs v-model="moreSettingTab" @tab-click="moreSettingTabClick">
          <el-tab-pane label="流程名称" name="流程名称_800:800">
            <wfSetting ref="wfSetting" :wfMetaId="wfMetaId"/>
          </el-tab-pane>
          <el-tab-pane :disabled="!isShowCwfNodeName" label="节点名称" name="节点名称_800:800">
            <nodeSetting ref="nodeSetting" :wfMetaId="wfMetaId" :wfNodeName="wfNodeName" :isShowCwfNodeName="isShowCwfNodeName"/>
          </el-tab-pane>

        </el-tabs>
        <div slot="footer" class="dialog-footer" style="margin-top:20px">
          <el-button class="btn-normal" @click="moreSettingVisible = false">取消</el-button>
          <el-button class="btn-normal" :loading="submitLoading" type="primary" v-lockButton @click="saveWfSetting">确定</el-button>
        </div>
      </template>
    </page>
  </el-dialog>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'wf-more-setting',
  data() {
    return {
      submitLoading: false,
      isShowCwfNodeName: false,
      moreSettingVisible: false, // 更多设置弹框可见性
      moreSettingTab: '', // 更多设置当前页签
      wfMetaId: '',
      wfNodeId: '',
      wfNodeName: '',
      wfMetaSetting: '',
      wfCurrtNodeSetting: '',
      rules: {
        wfLimitAuditDate: [
          { required: true, message: '请输入审核期限', trigger: 'blur' }
        ],
        wfIsAutoAudit: [
          { required: true, message: '请选择是否超时自动处理', trigger: 'change' }
        ],
        wfSendMessageType: [
          { required: true, message: '请选择超时通知', trigger: 'change' }
        ],
        nodeLimitAuditDate: [
          { required: true, message: '请输入审核期限', trigger: 'blur' }
        ],
        nodeIsAutoAudit: [
          { required: true, message: '请选择是否超时自动处理', trigger: 'change' }
        ],
        nodeSendMessageType: [
          { required: true, message: '请选择超时通知', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
  },
  methods: {
    initSetting(metaId, wfFrom, nodeDisabled, formtype) {
      this.moreSettingVisible = true
      this.wfMetaId = metaId
      this.wfNodeId = wfFrom.currentNode.id
      this.wfNodeName = wfFrom.currentNode.name

      if (!nodeDisabled) {
        this.isShowCwfNodeName = true
      } else {
        this.isShowCwfNodeName = false
      }
      this.showMoreSetting()
      this.$nextTick(() => {
        this.$refs.wfSetting.init(metaId, wfFrom, nodeDisabled, formtype)
        this.$refs.nodeSetting.init(metaId, wfFrom, nodeDisabled, formtype)
      })
    },
    saveWfSetting() {
      const nodeData = this.$refs.nodeSetting.save()
      const wfData = this.$refs.wfSetting.save()
      this.submitLoading = true
      this.$callApi('saveSetting',
        { ...nodeData.noticeVo, ...nodeData.auditConditionVo, relatedBookset: wfData.relatedBookset },
        result => {
          if (result.success) {
            this.$emit('setWfMetaSetting', wfData.selWfSetting)
            this.$emit('setWfCurrtNodeSetting', nodeData.selWfNodeSetting, nodeData.selectRecAcctTypeOptions,
              nodeData.auxiliaryAuditRuleId, nodeData.aiAuditRuleIds, nodeData.auditRuleFiles,
              nodeData.auditScreenMetaName, nodeData.auditScreenMetaId)
            this.moreSettingVisible = false
            const msg = result.attributes?.['提示']
            if (msg) {
              this.$message.warning(msg)
            } else {
              this.$message.success('设置成功')
            }
          }
          this.submitLoading = false
          return true
        }, result => {
          this.submitLoading = false
        }, {
          getExParamsCallApiSave: (data) => {
            return `&wfMetaId=` + this.wfMetaId + '&nodeName=' + this.wfNodeName +
              '&&selWfSettingStr=' + wfData.selWfSetting + '&selWfNodeSettingStr=' + nodeData.selWfNodeSetting +
              '&selectRecAcctTypeOptions=' + nodeData.selectRecAcctTypeOptions + '&appointUser=' +
              nodeData.appointUser + '&formNode=' + nodeData.formNode + '&printType=' + wfData.printTypeValue +
              '&printNode=' + wfData.checkedNode.join(',') + '&selectProcessBackTypeOptions=' +
              wfData.selectProcessBackTypeOptions + '&selectNodeBackTypeOptions=' + nodeData.selectNodeBackTypeOptions +
              '&wfLimitAuditDate=' + wfData.wfLimitAuditDate + '&wfIsAutoAudit=' + wfData.wfIsAutoAudit +
              '&wfSendMessageType=' + wfData.wfSendMessageType + '&nodeLimitAuditDate=' +
              nodeData.nodeLimitAuditDate + '&nodeSendMessageType=' + nodeData.nodeSendMessageType +
              '&nodeIsAutoAudit=' + nodeData.nodeIsAutoAudit + '&auxiliaryAuditRuleId=' + nodeData.auxiliaryAuditRuleId +
              '&aiAuditRuleIds=' + nodeData.aiAuditRuleIds + '&auditRuleFiles=' + nodeData.auditRuleFiles +
              '&auditScreenMetaName=' + nodeData.auditScreenMetaName + '&auditScreenMetaId=' + nodeData.auditScreenMetaId +
              '&checkedAssigner=' + nodeData.checkedAssigner + '&autoAuditType=' + wfData.autoAuditType + '&messageContent=' +
              wfData.messageContent
          } })
    },
    showMoreSetting() { // 显示更多设置弹框
      var isTabInit = this.$isNotEmpty(this.moreSettingTab)
      this.$nextTick(() => {
        if (!isTabInit) {
          var elTabs = $('.wfMoreSettingDlg .el-tabs__item:first')
          var firstTabName = elTabs.attr('id').replace('tab-', '')
          this.moreSettingTab = firstTabName
          this.moreSettingTabClick({ name: this.moreSettingTab })
        }
      })
    },
    moreSettingTabClick(tab) {
      // 更多设置标签名称的规则：xxx_width:height
      var widthHeightPart = tab.name.split('_')[1]
      var widthHeight = widthHeightPart.split(':')
      if (widthHeight.length === 2) { // 用页签name设置更多设置弹框大小
        this.$setDlgSize(this, 'wfMoreSettingDlg',
          parseInt(widthHeight[0]), parseInt(widthHeight[1]))
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.wfMoreSettingDlg .el-tabs__header { margin: 0 0 15px !important; }
.wfMoreSettingDlg .el-tab-pane { height: 100%; }
.tableListSetting { display: flex; margin-top: 5px; }
.tableListSettingColAll, .tableListSettingColSelected { margin-right: 10px; }
.tableListSettingColEditButton { margin-bottom: 5px; }

.tableListSettingColEditForm {
  border: 1px solid #DDDDDD;
  height: 495px;
  width: 300px;
  padding: 10px;
  position: relative;
}
.common-page ::v-deep .tableListSetting .el-icon-search { display: none; }
.common-page ::v-deep .tableListSetting .colSettingFilterInput { width: 100px; }

</style>
