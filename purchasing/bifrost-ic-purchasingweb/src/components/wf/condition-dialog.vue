<template>
  <common-dialog
    v-model="visible"
    title="条件设置"
    @closeDlg="handleClose"
    @submit="handleSumbit">
    <template #dialogContent>
      <div class="container">
        <el-radio-group v-model="mode" @change="clear">
          <el-radio label="自定义模式">自定义模式</el-radio>
          <el-radio label="选择模式">选择模式</el-radio>
        </el-radio-group>
        <div v-if="mode === '选择模式'" class="flex-row mtop-20">
          <div>
            <p>查询项目</p>
            <el-select v-model="project" size="mini" placeholder="请选择" filterable @change="projectChange">
              <el-option
                v-for="item in projectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="mleft-10">
            <p>要素名称</p>
            <el-select v-model="element" size="mini" placeholder="请选择" filterable @change="elementChange">
              <el-option
                v-for="item in elements"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="mleft-10">
            <p>比较符</p>
            <el-select v-model="compareVal" size="mini" placeholder="请选择">
              <el-option
                v-for="item in compareOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="mleft-10 flex-1">
            <p>项目值</p>
            <el-input v-if="projectMap[element]?.dataType === '弹框'" v-model="projectVal" size="mini" style="width: 100%" placeholder="请输入内容" readonly @focus="refData"></el-input>
            <el-select v-else-if="projectMap[element]?.dataType === '下拉框'" v-model="projectVal" size="mini"
                       style="width: 100%" placeholder="请选择" filterable  @visible-change="projectSelect">
              <el-option
                v-for="item in projectValOptions"
                :key="item.value"
                :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
            <el-input v-else v-model="projectVal" size="mini" style="width: 100%" placeholder="请输入内容"></el-input>
          </div>
        </div>
        <div class="flex-row mtop-20">
          <span>条件内容：</span>
          <el-input
            class="flex-1"
            type="textarea"
            :readonly="mode === '选择模式'"
            :rows="20"
            placeholder="请输入内容"
            v-model="content">
          </el-input>
          <div v-if="mode === '选择模式'" class="flex-column mleft-10" style="width: 100px">
            <el-button @click="add">添加</el-button>
            <el-select v-model="link" placeholder="请选择" class="mtop-10">
              <el-option
                v-for="item in linkOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-button class="mtop-10" style="margin-left: 0px" @click="addString(' ( ')">(</el-button>
            <el-button class="mtop-10" style="margin-left: 0px" @click="addString(' ) ')">)</el-button>
            <el-button class="mtop-10" style="margin-left: 0px" @click="delelString">删除</el-button>
            <el-button class="mtop-10" style="margin-left: 0px" @click="clear">清除</el-button>
          </div>
        </div>
      </div>
    </template>
  </common-dialog>
</template>

<script>
const compareVals = [' 等于 ', ' 大于 ', ' 大于等于 ', ' 小于 ', ' 小于等于 ', ' 不等于 '] // , ' 包含 ', ' 不包含 '
export default {
  name: 'condition-dialog',
  props: {
    wfMeta: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      type: '',
      visible: false,
      mode: '选择模式',
      project: '',
      element:'',
      projectOptions: [],
      compareVal: ' 等于 ',
      compareOptions: [
        {
          label: '等于',
          value: ' 等于 '
        },
        {
          label: '大于',
          value: ' 大于 '
        },
        {
          label: '大于等于',
          value: ' 大于等于 '
        },
        {
          label: '小于',
          value: ' 小于 '
        },
        {
          label: '小于等于',
          value: ' 小于等于 '
        },
        {
          label: '不等于',
          value: ' 不等于 '
        }
        // {
        //   label: '包含',
        //   value: ' 包含 '
        // },
        // {
        //   label: '不包含',
        //   value: ' 不包含 '
        // }
      ],
      projectVal: '',
      projectValOptions: [],
      content: '',
      link: '\n 并且 \n',
      linkOptions: [
        {
          label: '并且',
          value: '\n 并且 \n'
        },
        {
          label: '或者',
          value: '\n 或者 \n'
        }
      ],
      projectMap: {},
      dataTypeMaps:{},
      elements:[]

    }
  },
  methods: {
    init() {
      if (!this.wfMeta.id) {
        return
      }
      this.$callApiParams('getWfFormula', {
        metaId: this.wfMeta.id
      }, (result) => {
        if (result.success) {
          const data = result.data || {}
          this.dataTypeMaps =data.dataTypeMaps;
          const keys = Object.keys(data.blockAndClassNames)
          this.projectOptions = keys.map(key => ({
            label: key,
            value: data.blockAndClassNames[key]
          }))
        }
        return true
      }, () => {})
    },
    projectChange() {
      this.projectVal = ''
      this.projectValOptions = []
      this.element =''
      this.projectMap = this.dataTypeMaps[this.project]
      const keys = Object.keys(this.projectMap)
      this.elements = keys.map(key => ({
        label: key,
        value: key
      }))
    },
    elementChange(){
      this.projectVal = ''
      this.projectValOptions = []
    },
    refData() {
      const colItem = this.projectMap[this.element]
      this.$refData(undefined, { ...colItem, colType: colItem.dataType }, () => {}, () => {}, () => {}, (selectedData) => {
        const list = selectedData.list || []
        const values = []
        list.forEach(item => {
          if (item.targetRef) {
            values.push(item.targetRef)
          } else if (item.codeLabel) {
            values.push(item.codeLabel)
          } else {
            values.push(item.label)
          }
        })
        this.projectVal = values.join(',')
      }, {
        multiple: false
      })
    },
    projectSelect(visible) {
      if (visible && !this.projectValOptions.length) {
        const colItem = this.projectMap[this.element]
        let dataRef = colItem?.dataRef || ''
        dataRef = dataRef.replace('，', ',')
        if (dataRef.includes(',')) {
          const list = dataRef.split(',')
          this.projectValOptions = list.map(label => ({
            label,
            value: label
          }))
          return
        }
        this.$callApiParams('refLabelValuesDynamic', { ...colItem }, (result) => {
          if (result.success) {
            const data = result.data || []
            this.projectValOptions = data
          }
          return true
        })
      }
    },
    delelString() {
      const linkSymbols = ['\n 并且 \n', '\n 或者 \n', ' ( ', ' ) ']
      let lastIndex = -1

      // 找到最后一个连接符的位置
      for (const symbol of linkSymbols) {
        const index = this.content.lastIndexOf(symbol)
        if (index > lastIndex) {
          lastIndex = index
        }
      }
      if (lastIndex === -1) {
        this.content = ''
        return
      }
      this.content = this.content.slice(0, lastIndex)
    },
    clear() {
      this.content = ''
    },
    add() {
      if (!this.project || !this.projectVal) {
        const message = this.project ? '项目值不能为空' : '查询项目不能为空'
        return this.$message.error(message)
      }
      const hasCompare = compareVals.some(compare => this.content.includes(compare))
      if (this.content && hasCompare) {
        this.content += this.link
      }
      const colItem = this.projectMap[this.element] || {}
      const val = colItem.dataType === '金额' ? this.projectVal : `'${this.projectVal}'`
      if (this.project==='基本信息') {
        this.content += `${this.element}${this.compareVal}${val}`
      } else{
        let value = ''
        this.projectOptions.forEach(item => {
          if (item.value===this.project){
            value = item.label
          }
        })
        this.content += `${value}_${this.element}${this.compareVal}${val}`
      }
    },
    addString(string) {
      if (string === ')' && !this.$content) {
        return this.$message.error('不能以)开头！')
      }
      this.content += string
    },
    show(data = {}) {
      const { type = '', content = '', expressionType = '自定义模式' } = data
      this.mode = expressionType
      this.type = type
      this.content = content
      this.init()
      this.visible = true
    },
    handleClose() {
      this.visible = false
      this.content = ''
      this.project = ''
      this.projectVal = ''
      this.mode = '选择模式'
      this.projectValOptions = []
      this.projectOptions = []
    },
    handleSumbit() {
      this.$emit('submit', {
        content: this.content,
        type: this.type,
        expressionType: this.mode
      })
      this.handleClose()
    },
    conditionSubmit(data) {

    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  p {
    margin-bottom: 0px;
  }
  &_search {

  }
  .color-red {
    color: red;
  }
  .mtop-20 {
    margin-top: 20px;
  }
  .mleft-10 {
    margin-left: 10px;
  }
  .mtop-10 {
    margin-top: 10px;
  }
}
</style>
