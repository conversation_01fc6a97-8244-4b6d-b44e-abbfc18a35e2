<template>
  <div class="wffWrap">
      <versionRelate
              ref="versionRelate"
              bizName="流程"
              dataType='WfMetaEntity'
              @beforeOpenCanvas="beforeOpenCanvas"
              @afterMetaQuery="afterMetaQuery"
              @openNew="openNew"
              @getMetaToSave="getMetaToSave"
              @afterMetaSaved="afterMetaSaved"
              @btSwitchStatusAfter="btSwitchStatusAfter"
              @nodeClick="nodeClick"
              @canvasOpenedChange="canvasOpenedChange">
          <template #moreButtons>
              <el-button class="mright-6" plain icon="el-icon-view" size="small" @click="btPreview" :disabled="!canvasOpened"> 预览</el-button>
              <el-button class="mright-6" plain icon="el-icon-connection" size="small" @click="btBindObject" :disabled="!canvasOpened"> 业务绑定</el-button>
              <el-upload ref="fileDomJson" :on-change="wfImport" class="upload-import" action :auto-upload='false' :show-file-list="false" :limit="1" >
                <el-button class="mright-6" plain icon="el-icon-aliiconshangchuan" size="small"> 导入</el-button>
              </el-upload>
              <el-button class="mright-6" plain icon="el-icon-aliiconxiazai" size="small" :disabled="!canvasOpened" @click="wfExport" > 导出</el-button>
              <el-button class="mright-6" plain icon="el-icon-circle-plus-outline" size="small" @click="btAddNode" :disabled="!canvasOpened"> 添加节点</el-button>
              <el-button class="mright-6" plain icon="el-icon-document" size="small" @click="btUseFul" :disabled="nodeDisabled"> 常用语</el-button>
              <el-button class="mright-6" plain icon="el-icon-collection-tag" size="small" @click="btnUserDet" :disabled="nodeDisabled"> 审核权限</el-button>
              <el-button class="mright-6" plain icon="el-icon-document-checked" size="small" @click="btnUserCform" :disabled="nodeDisabled"> 表单权限</el-button>
              <el-button class="mright-6" plain icon="el-icon-edit-outline" size="small" @click="btnAuditEdit" :disabled="nodeDisabled" v-show="showBtAuditEdit"> 审核编辑</el-button>
              <el-button class="mright-6" plain icon="el-icon-guide" size="small" @click="btDataSetting"
                         :disabled="!bindObjitems.length"> 切换流程</el-button>
            <el-button class="mright-6" plain icon="el-icon-circle-check" size="small" @click="checkWf"
                       :disabled="!bindObjitems.length || wfMeta.versionStatus !== '启用中'">流程检查</el-button>
              <el-button class="mright-6" plain icon="el-icon-setting" size="small" @click="showWfMoreSetting"
                         :disabled="!bindObjitems.length || wfMeta.versionStatus !== '启用中'"> 更多设置</el-button>
          </template>
          <template #mainCanvas>
              <div class="wf-main-content" ref="wfMainContent">
                  <div class="wf-canvas" :style="{height:modifyH ? wfCanvasHeight + 'px':''}">
                      <loading ref="loading"/>
                      <div class="wf-title wf-title-font">{{ wfFrom.name }}</div>
                      <wfcanvas
                              ref="wfcanvas"
                              @addNode="addNode"
                              @removeNode="removeNode"
                              @updateNodeName="updateNodeName"
                              @setCurrentNode="setCurrentNode"
                              @addConnection="addConnection"
                              @removeConnection="removeConnection"
                              @setConnection="setConnection"
                              @updateConnectionName="updateConnectionName"/>
                  </div>
                  <el-form ref="form"
                          label-width="80px"
                          size="small"
                          class="wf-property-block"
                          :style="{height: topHeight + 'px'}"
                          :model="wfFrom">
                      <!-- 向上拖动热区 -->
                      <drag-y @handleMouseUp="handleMouseUp"></drag-y>
                      <div class="wf-form-container" id="firstFormContainer" style="margin-left: 0;flex: 0.8">
                          <el-form-item label="流程名称">
                              <el-input v-model="wfFrom.name"></el-input>
                          </el-form-item>
<!--                            <el-form-item label="流程描述">-->
<!--                                <el-input></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="审核天数">-->
<!--                                <el-col :span="4">-->
<!--                                    <el-input></el-input>-->
<!--                                </el-col>-->
<!--                                <el-col :span="4">&nbsp;天</el-col>-->
<!--                                &lt;!&ndash;<el-col :span="7">流程状态</el-col>-->
<!--                                <el-col :span="1">-->
<!--                                  <div class="item-status-sign"/>-->
<!--                                </el-col>-->
<!--                                <el-col :span="8">&nbsp;正常使用</el-col>&ndash;&gt;-->
<!--                                <el-col :span="6">是否模板</el-col>-->
<!--                                <el-col :span="9">-->
<!--                                    <el-radio-group v-model="wfFrom.templateType" :disabled="radioDisabled">-->
<!--                                        <el-radio label="用户模板">是</el-radio>-->
<!--                                        <el-radio label="">否</el-radio>-->
<!--                                    </el-radio-group>-->
<!--                                </el-col>-->
<!--                            </el-form-item>-->
                          <el-form-item label="业务绑定">
                              <ol class="wf-form-bind-objects" @click="btBindObject">
                                  <li v-for="(item,index) in bindObjitems" :key="item+index">
                                      {{item}}
                                  </li>
                              </ol>
                          </el-form-item>
                          <el-form-item label="开始条件" :title="wfFrom.startCondition">
                              <el-input
                                :title="wfFrom.startCondition"
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 3}"
                                v-model="wfFrom.startCondition">
                              </el-input>
                          </el-form-item>
                      </div>
                      <div class="wf-form-container" id="secondFormContainer">
                          <el-form-item label="环节名称">
                            <el-col :span="10">
                              <el-input v-model="wfFrom.currentNode.name" :disabled='nodeDisabled' maxlength="16"></el-input>
                            </el-col>

                            <el-col :span="13">
                              <el-form-item label="环节描述" style="margin-bottom: 0px">
                                <el-input v-model.trim="wfFrom.currentNode.description" :disabled='nodeDisabled' maxlength="45"></el-input>
                              </el-form-item>
                            </el-col>
                          </el-form-item>
<!--                          <el-form-item label="连线描述">-->
<!--                            <el-col :span="10">-->
<!--                              <el-input v-model="wfFrom.currentConnection.name"-->
<!--                                        :disabled='connectionDisabled' maxlength="45"></el-input>-->
<!--                            </el-col>-->
<!--                            <el-col :span="13">-->
<!--                              <el-form-item label="连线条件">-->
<!--                                <el-input-->
<!--                                  v-model="wfFrom.currentConnection.expression"-->
<!--                                  :disabled='connectionDisabled'>-->
<!--                                </el-input>-->
<!--                              </el-form-item>-->
<!--                            </el-col>-->
<!--                          </el-form-item>-->

                        <el-form-item label="连线描述">
                          <el-col :span="23">
                            <el-input
                              v-model="wfFrom.currentConnection.name"
                              :disabled='connectionDisabled' maxlength="45">
                            </el-input>
                          </el-col>
                        </el-form-item>
                        <el-form-item label="连线条件">
                          <el-col :span="23">
                            <el-input
                              v-model="wfFrom.currentConnection.expression"
                              :disabled='connectionDisabled'
                              readonly
                              @focus="expressionFocus('连线条件', wfFrom.currentConnection.expression)">
                            </el-input>
                          </el-col>
                        </el-form-item>

                        <el-form-item label="结束条件">
                          <el-col :span="23" class="flex">
                            <el-tooltip effect="dark" :content="wfFrom.currentNode.expression" :disabled="!wfFrom.currentNode.expression" placement="top">
                              <el-input
                                class="flex-1"
                                v-model="wfFrom.currentNode.expression"
                                readonly
                                :disabled='nodeDisabled'
                                @focus="expressionFocus('结束条件', wfFrom.currentNode.expression)">
                              </el-input>
                            </el-tooltip>

                            <div class="flex" style="margin-left: 5px">
                              <span class="el-form-item__label radio-title">选人必填</span>
                              <el-radio-group v-model="wfFrom.currentNode.isNextAuthUserRequired" :disabled='nodeDisabled'
                                              @change="nextAuthUserRequiredChange">
                                <el-radio label="启用">启用</el-radio>
                                <el-radio label="">禁用</el-radio>
                              </el-radio-group>
                            </div>
                          </el-col>
                        </el-form-item>
<!--                            <el-form-item label="环节描述">-->
<!--                                <el-input v-model.trim="wfFrom.currentNode.description" :disabled='nodeDisabled' maxlength="45"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="环节意见">-->
<!--                                <el-input :disabled='nodeDisabled'></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="审核天数">-->
<!--                                <el-col :span="4">-->
<!--                                    <el-input :disabled='nodeDisabled'></el-input>-->
<!--                                </el-col>-->
<!--                                <el-col :span="6">&nbsp;天</el-col>-->
<!--                                <el-col :span="6">-->
<!--                                    环节设置-->
<!--                                </el-col>-->
<!--                            </el-form-item>-->
                        <el-form-item label="退回环节" style="margin-bottom:3px">
                          <el-col :span="23" style="display:flex;justify-content: space-between;">
                            <div class="flex">
                              <el-radio-group v-model="wfFrom.currentNode.isBackNode" :disabled='nodeDisabled'
                                              @change="backNodeChange">
                                <el-radio label="启用" >启用</el-radio>
                                <el-radio label="">禁用</el-radio>
                              </el-radio-group>
                            </div>
                            <div class="flex">
                              <span class="el-form-item__label radio-title">下环节人</span>
                              <el-radio-group v-model="wfFrom.currentNode.isNextAuthUser" :disabled='nodeDisabled'
                                              @change="nextAuthUserChange">
                                <el-radio label="启用" >启用</el-radio>
                                <el-radio label="">禁用</el-radio>
                              </el-radio-group>
                            </div>
                            <div class="flex">
                              <span class="el-form-item__label radio-title">加签功能 </span>
                              <el-radio-group v-model="wfFrom.currentNode.isAddCountersign"
                                              :disabled="nodeDisabled || wfFrom.currentNode.isCountersign === '启用'"
                                              @change="addCountersignChange">
                                <el-radio label="启用">启用</el-radio>
                                <el-radio label="">禁用</el-radio>
                              </el-radio-group>
                            </div>
                          </el-col>
                        </el-form-item>

                        <el-form-item label="会签功能" style="margin-bottom:3px">
                          <el-col :span="23" style="display:flex;justify-content: space-between;">
                            <div class="flex">
                              <el-radio-group v-model="wfFrom.currentNode.isCountersign" :disabled='nodeDisabled'
                                              @change="countersignChange">
                                <el-radio label="启用">启用</el-radio>
                                <el-radio label="">禁用</el-radio>
                              </el-radio-group>
                            </div>
                            <div class="flex" style="width: 33.3%">
                              <span class="el-form-item__label radio-title">会签模式</span>
                              <el-select v-model="wfFrom.currentNode.countersignType"
                                         :disabled="wfFrom.currentNode.isCountersign!=='启用'"
                                         @change="countersignTypeChange">
                                <el-option
                                  v-for="item in countersignTypes"
                                  :key="item.value"
                                  :label="item.name"
                                  :value="item.value">
                                </el-option>
                              </el-select>
                            </div>
                            <div class="flex radio-input" style="width: 33.3%">
                              <span class="el-form-item__label radio-title">会签比例</span>
                              <el-input type="number"
                                v-model="wfFrom.currentNode.countersignPro"
                                @input="wfFrom.currentNode.countersignPro = wfFrom.currentNode.countersignPro.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')"
                                :disabled="wfFrom.currentNode.countersignType !=='比例模式的会签'"
                                @blur="handleInput">
                                <template slot="append">%</template>
                              </el-input>
                            </div>
                          </el-col>
                        </el-form-item>

<!--                          <el-form-item label="加签功能">-->
<!--                            <div style="display:flex">-->
<!--                              <div style="width:41%">-->
<!--                                <el-radio-group v-model="wfFrom.currentNode.isAddCountersign" :disabled='nodeDisabled'-->
<!--                                                @change="addCountersignChange">-->
<!--                                  <el-radio label="启用">启用</el-radio>-->
<!--                                  <el-radio label="">禁用</el-radio>-->
<!--                                </el-radio-group>-->
<!--                              </div>-->
<!--                              <div style="width:23%">-->
<!--                                电子签章-->
<!--                              </div>-->
<!--                              <div style="width:36%">-->
<!--                                <el-radio-group v-model="wfFrom.currentNode.isElectronicSeal" :disabled='nodeDisabled'>-->
<!--                                  <el-radio label="启用">启用</el-radio>-->
<!--                                  <el-radio label="">禁用</el-radio>-->
<!--                                </el-radio-group>-->
<!--                              </div>-->

<!--                              <div style="width:23%">-->
<!--                                自动审核-->
<!--                              </div>-->
<!--                              <div style="width:36%">-->
<!--                                <el-radio-group v-model="wfFrom.currentNode.isAutoAudit" :disabled='nodeDisabled'>-->
<!--                                  <el-radio label="启用">启用</el-radio>-->
<!--                                  <el-radio label="">禁用</el-radio>-->
<!--                                </el-radio-group>-->
<!--                              </div>-->
<!--                            </div>-->
<!--                          </el-form-item>-->
                          <!--<el-form-item label="环节设置">
                            <el-checkbox-group v-model="wfFrom.currentNode.options">
                              <el-checkbox label="必传附件"></el-checkbox>
                              <el-checkbox label="可修改/删除"></el-checkbox>
                              <el-checkbox label="必选指标"></el-checkbox>
                              <el-checkbox label="发送短信"></el-checkbox>
                            </el-checkbox-group>
                          </el-form-item>-->
<!--                            <el-form-item label="结束条件">-->
<!--                                <el-input-->

<!--                                        v-model="wfFrom.currentNode.expression"-->
<!--                                        :disabled='nodeDisabled'>-->
<!--                                </el-input>-->
<!--                            </el-form-item>-->
                      </div>
                      <div class="wf-form-container" style="flex: 1">
                          <el-form-item label="任务指派">
                              <el-radio-group v-model="typeRadio" @change="typeChange"
                                              :disabled='nodeDisabled || nodeUserRo'>
                                  <el-radio label="1">按角色</el-radio>
                                  <el-radio label="2">按用户</el-radio>
                              </el-radio-group>
                          </el-form-item>
                          <el-form-item label="" class="wf-task-assign">
                              <el-transfer
                                      filterable
                                      v-model="userOrRoleValue"
                                      :data="userOrRoleData"
                                      :props="transferProps"
                                      @change="transferChange"
                                      @left-check-change="selectPanel"
                                      @right-check-change="selectPanel"
                                      @mouseover.native="addSupTitle">
                              </el-transfer>
                          </el-form-item>
                      </div>
<!--                        <div class="wf-form-container">-->
<!--                            <el-form-item label="环节参数">-->
<!--                                &lt;!&ndash;<el-input  v-model="wfFrom.currentNode.extraParam" :disabled='nodeDisabled'/>&ndash;&gt;-->
<!--                                <el-input-->
<!--                                        type="textarea"-->
<!--                                        :autosize="{ minRows: 2, maxRows: 4}"-->
<!--                                        v-model="wfFrom.currentNode.extraParam"-->
<!--                                        :disabled='nodeDisabled'>-->
<!--                                </el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="环节动作">-->
<!--                                <el-input-->
<!--                                        type="textarea"-->
<!--                                        :autosize="{ minRows: 2, maxRows: 4}"-->
<!--                                        v-model="nodeActions"-->
<!--                                        :readonly="true"-->
<!--                                        :disabled='nodeDisabled'-->
<!--                                        @focus="btNodeAction">-->
<!--                                </el-input>-->
<!--&lt;!&ndash;                            </el-form-item>&ndash;&gt;-->
<!--                            <el-form-item label="连线描述">-->
<!--                                <el-input v-model="wfFrom.currentConnection.name"-->
<!--                                          :disabled='connectionDisabled' maxlength="45"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="连线条件">-->
<!--                                <el-input-->
<!--                                        type="textarea"-->
<!--                                        :autosize="{ minRows: 3, maxRows: 4}"-->
<!--                                        v-model="wfFrom.currentConnection.expression"-->
<!--                                        :disabled='connectionDisabled'>-->
<!--                                </el-input>-->
<!--                            </el-form-item>-->
<!--                        </div>-->
                  </el-form>

                  <el-dialog width="640px" title="业务绑定" append-to-body :visible.sync="bindObjDlgVisible" center>
                      <div style="max-height: 400px">
                          <checkboxtree
                                  ref="checkboxtree"
                                  selectApiKey="getBindObject"
                                  nodeKey="label"
                                  :isNewData="true"
                                  :checkedKeys="bindObjitems"
                                  :propsVaule="{children: 'children',label:'label'}"
                          />
                      </div>
                      <div slot="footer" class="dialog-footer" style="margin-top: 32px">
                          <el-button class="btn-normal" @click="cancelBindObj">取消</el-button>
                          <el-button class="btn-normal" type="primary" @click="confirmBindObj">确定</el-button>
                      </div>
                  </el-dialog>

                  <el-dialog width="640px" title="节点动作" append-to-body :visible.sync="nodeActionDlgVisible" center>
                    <page>
                      <template #pageContent>
                      <div style="max-height: 500px">
                          <el-table ref="actionTable" :data="tableDatas" border>
                              <el-table-column type="selection" width="40"></el-table-column>
                              <el-table-column
                                      prop="name"
                                      label="节点动作名称">
                              </el-table-column>
                              <el-table-column
                                      prop="date"
                                      label="审核类型"
                                      width="230"
                                      align="center">
                                  <template slot-scope="scope">
                                      <el-radio v-model="scope.row.actionPassType" :label="1">审核通过</el-radio>
                                      <el-radio v-model="scope.row.actionPassType" :label="2">审核不通过</el-radio>
                                  </template>
                              </el-table-column>
                              <el-table-column
                                      prop="date"
                                      label="执行时间点"
                                      align="center">
                                  <template slot-scope="scope">
                                      <el-radio v-model="scope.row.actionPeriod" :label="1">审核前</el-radio>
                                      <el-radio v-model="scope.row.actionPeriod" :label="2">审核后</el-radio>
                                  </template>
                              </el-table-column>
                          </el-table>
                      </div>
                      </template>
                    </page>
                      <div slot="footer" class="dialog-footer">
                          <el-button @click="cancelNodeAction">取消</el-button>
                          <el-button type="primary" @click="confirmNodeAction">确定</el-button>
                      </div>
                  </el-dialog>

                  <wf-useful-list-detail ref="WfUsefulListDetail" @usefulChange="usefulChange" />
                  <wf-more-setting ref="WfMoreSetting" @setWfMetaSetting="setWfMetaSetting"
                                   @setWfCurrtNodeSetting="setWfCurrtNodeSetting"/>
                  <wf-userdept-list ref="wfUserDept" @useDeptChange="useDeptChange"/>

                  <wf-user-cform ref="wfUserCform" @useDeptChange="useDeptChange"/>
                  <wf-data-setting ref="wfDataSetting"></wf-data-setting>
                  <wf-data-check ref="wfCheck"></wf-data-check>
                  <wf-audit-edit ref="wfAuditEdit" :editNodes="wfMeta.nodes"/>
                  <condition-dialog ref="conditionDialog" :wfMeta="wfMeta" @submit="conditionSubmit"></condition-dialog>
              </div>
          </template>
      </versionRelate>
  </div>
</template>

<script>
import request from '@/utils/request'
export default {
name: 'wff', // 多加一个f两个避免与工作流重新渲染问题冲突
data() {
  return {
    topHeight: 250,
    modifyH: false,
    wfCanvasHeight: 0,
    countersignTypes: [{ value: '协作模式的会签', name: '协作模式的会签' },
      { value: '比例模式的会签', name: '比例模式的会签' },
      { value: '一票决定模式的会签', name: '一票决定模式的会签' },
      { value: '单组长模式的会签', name: '单组长模式的会签' }],
    wfMeta: { id: '', nodes: {}, connections: {}, versionStatus: '' }, // 节点name和连线id为索引
    wfFrom: {
      name: '',
      templateType: '',
      startCondition: '',
      selWfSettingStr: '',
      currentNode: {},
      currentConnection: {}
    },
    systemOptions: {}, // 系统参数集合
    bindObjDlgVisible: false,
    nodeActionDlgVisible: false,
    bindObjitems: [], // '其他费用报销单','市外差旅费报销单','出国（境）费用报销单'
    index: 0,
    typeRadio: '1',
    userOrRoleData: [], // 穿梭框左边数据
    userOrRoleValue: [], // 穿梭框右边数据
    userAndRoleData: [], // 角色与用户的全部数据
    roleData: [], // 角色数据
    userData: [], // 用户数据
    transferProps: { key: 'userRoleId', label: 'userRoleName' },
    nodeDisabled: true, // 是否禁用环节的参数
    nodeUserRo: true, // 是否禁用任务指派的单选框
    connectionDisabled: true, // 是否禁用连线的参数
    actionTableDatas: [], // 节点动作源数据列表
    tableDatas: [], // 节点动作弹框展示列表（包含已选数据）
    nodeActions: '', // 节点动作input框数据值
    currentNodeActions: [], // 当前节点的节点动作数据
    canvasOpened: true, // 是否已经打开设计界面
    radioDisabled: false
  }
},
activated() {
  this.$refs.versionRelate.calculate()
},
mounted() {
  if (!this.typeRadio || this.typeRadio === '') {
    this.$message.error('分类组件的数据类型不能为空')
  } else {
    this.selectAssignUserOrRole('1') // 查询角色信息
    this.selectAssignUserOrRole('2') // 查询用户信息
  }
  this.getWfActionList()
},
computed: {
  showBtAuditEdit() {
    return this.systemOptions['显示审核编辑按钮'] === true
  }
},
watch: {
  'wfFrom.currentNode.name': function(val, oldval) { // 通过属性框修改节点名称，同步到节点
    this.$refs.wfcanvas.resetNodeName(this.wfFrom.currentNode.id, val)
  },
  'wfFrom.currentConnection.name': function(val, oldval) { // 通过修改连线描述，生成或同步连线描述节点
    if (this.$isNotEmpty(val)) {
      this.setConnectionDescription(val, oldval)
    } else {
      const value = this.wfFrom.currentConnection.expression.slice(0, 45)// 与连线描述的maxlength="45"呼应（227行）
      this.setConnectionDescription(value, oldval)
    }
  },
  'wfFrom.currentConnection.expression': function(val, oldval) { // 通过修改连线条件，生成或同步连线描述节点
    if (this.$isEmpty(this.wfFrom.currentConnection.name)) {
      var newVal = val.slice(0, 45)// 与连线描述的maxlength="45"呼应（227行）
      this.setConnectionDescription(newVal, oldval)
    }
  },
  'wfFrom.currentNode.description': function(val, oldval) { // 通过修改节点描述，生成或同步节点描述节点
    if (this.$isNotEmpty(val)) {
      this.setNodeDescription(val, oldval)
    } else {
      const value = this.wfFrom.currentNode.expression.slice(0, 45)// 与环节描述的maxlength="45"呼应（97行）
      this.setNodeDescription(value, oldval)
    }
  },
  'wfFrom.currentNode.expression': function(val, oldval) { // 通过修改节点条件，生成或同步节点描述节点
    if (this.$isEmpty(this.wfFrom.currentNode.description)) {
      var newVal = val.slice(0, 45)// 与环节描述的maxlength="45"呼应（97行）
      this.setNodeDescription(newVal, oldval)
    }
  }
},
methods: {
  // 开始垂直拖动
  handleMouseUp(eventH) {
    this.modifyH = true
    this.topHeight -= eventH
    this.topHeight = Math.max(this.topHeight, 250)
    this.topHeight = Math.min(this.topHeight, this.$refs.wfMainContent.getBoundingClientRect().height)
    const totalHeight = this.$refs.wfMainContent.getBoundingClientRect().height // 显示区域总高
    this.wfCanvasHeight = totalHeight - this.topHeight - 10
  },
  setConnectionDescription(val, oldval) {
    var connectionNodeId = this.wfFrom.currentConnection.connectionNodeId
    if (this.$isNotEmpty(connectionNodeId)) {
      this.$refs.wfcanvas.resetNodeName(connectionNodeId, val)
      this.updateNodeName(connectionNodeId, val)
    } else if (this.wfFrom.currentConnection.id && ((oldval !== '' && val !== '') || val !== '')) {
      var node = this.$refs.wfcanvas.addConnectionName(this.wfFrom.currentConnection)
      this.wfFrom.currentConnection.connectionNodeId = node.id
    }
  },
  setNodeDescription(val, oldval) {
    var descriptionNodeId = this.wfFrom.currentNode.descriptionNodeId
    if (this.$isNotEmpty(descriptionNodeId)) {
      this.$refs.wfcanvas.resetNodeName(descriptionNodeId, val, 3) // 3表示节点描述节点
      this.updateNodeName(descriptionNodeId, val)
    } else if (this.wfFrom.currentNode.id && val && !oldval) {
      var node = this.$refs.wfcanvas.addNodeDescription(this.wfFrom.currentNode)
      this.wfFrom.currentNode.descriptionNodeId = node.id
    }
  },
  addNode(n) {
    this.wfMeta.nodes[n.id] = n
  },
  removeNode(id) {
    delete this.wfMeta.nodes[id]
    this.resetCurrentNode()
  },
  updateNodeName(id, newName) {
    this.wfMeta.nodes[id].name = newName
  },
  sliderChange(meta) {
    this.wfFrom.detailDlgWidth = meta.instance.detailDlgWidth
    this.wfFrom.detailDlgHeight = meta.instance.detailDlgHeight
    if (this.wfMeta.id !== '') { // 修改时才调自动保存，自动保存不要提示保存成功
      this.btSave()
    }
  },
  usefulChange(meta) {
    // var _this = this
    // var nodeId = _this.wfFrom.currentNode.id
  },
  btSave() {
    this.$refs.versionRelate.btSave(undefined, () => { return true })
  },
  setCurrentNode(id) {
    this.$set(this.wfFrom, 'currentNode', this.wfMeta.nodes[id])
    this.currentNode = this.wfFrom.currentNode.name
    if (this.wfFrom.currentNode.isCountersign === '启用') {
      this.typeRadio = '2'
    } else {
      this.typeRadio = '1'
      this.userOrRoleData = this.roleData
      this.userOrRoleValue = []
    }
    this.typeChange(this.typeRadio)
    this.nodeDisabled = this.nodeUserRo = false
    this.countersignChange()
    var data = []
    var assign = this.wfFrom.currentNode.assign
    if (assign) {
      for (var i = 0; i < assign.length; i++) {
        data.push(assign[i].userRoleId)
      }
    }
    this.userOrRoleValue = data
    if (this.wfFrom.currentNode.isCountersign === '启用') {
      this.nodeUserRo = true
    }
    // eslint-disable-next-line no-unused-vars
    var nodeActions = ''
    this.currentNodeActions = this.wfFrom.currentNode.actions
    if (this.currentNodeActions) {
      for (var j = 0; j < this.currentNodeActions.length; j++) {
        if (j === this.currentNodeActions.length - 1) {
          nodeActions += this.currentNodeActions[j].name
        } else {
          nodeActions += this.currentNodeActions[j].name + '\n'
        }
      }
    }
    this.nodeActions = nodeActions
    this.resetCurrentConnection()
  },
  updateConnectionName(id, name) {
    this.wfFrom.currentConnection.name = name
  },
  addConnection(c) {
    this.wfMeta.connections[c.id] = {
      id: c.id,
      sourceId: c.sourceId,
      targetId: c.targetId,
      expression: '',
      name: ''
    }
  },
  removeConnection(id) {
    delete this.wfMeta.connections[id]
    this.resetCurrentConnection()
  },
  setConnection(id) {
    this.wfFrom.currentConnection = this.wfMeta.connections[id]
    this.connectionDisabled = false
    this.currentNode = ''
    this.resetCurrentNode()
  },
  nodeClick(data, exData) { // 分类树节点点击事件
    this.currentNode = ''
  },
  nodeDeleted(node) { // 当前编辑的流程图执行删除后，这时打开新建流程向导
    if (node.isLeaf &&
      node.itemKey === this.wfMeta.id) {
      this.btNew()
    }
  },
  resetCurrentNode() { // 清空当前节点
    this.wfFrom.currentNode = {}
    this.userOrRoleData = []
    this.userOrRoleValue = []
    this.nodeActions = ''
    this.nodeDisabled = true
  },
  resetCurrentConnection() { // 清空当前连线
    this.wfFrom.currentConnection = {}
    this.connectionDisabled = true
  },

  resetMeta() { // 清空元数据
    this.resetCurrentNode()
    this.resetCurrentConnection()
    if (this.wfMeta) {
      if (this.wfMeta.nodes) {
        for (var nodeId in this.wfMeta.nodes) {
          this.removeNode(nodeId)
        }
      }

      if (this.wfMeta.connections) {
        for (var cnnId in this.wfMeta.connections) {
          this.removeConnection(cnnId)
        }
      }
    }
  },
  afterMetaQuery(result) {
    var meta = result.data

    this.systemOptions = {}
    this.systemOptions['显示审核编辑按钮'] = meta.extData['显示审核编辑按钮']

    this.wfMeta.id = meta.main.id
    this.wfMeta.lockVersion = meta.main.lockVersion
    this.wfMeta.existBizData = meta.main.existBizData
    this.wfMeta.versionStatus = meta.main.versionStatus
    this.wfFrom.name = meta.main.name
    this.wfFrom.templateType = meta.main.templateType
    this.wfFrom.startCondition = meta.main.startCondition
    this.wfFrom.selWfSettingStr = meta.main.selWfSettingStr
    if (meta.main.templateType === '系统模板') {
      this.radioDisabled = true
    } else {
      this.radioDisabled = false
    }
    this.wfFrom.detailDlgWidth = meta.instance.detailDlgWidth
    this.wfFrom.detailDlgHeight = meta.instance.detailDlgHeight
    var binds = meta.instance.binds
    var bindObject = []
    for (var i = 0; i < binds.length; i++) {
      bindObject.push(binds[i].bindName)
    }
    this.bindObjitems = bindObject
    this.wfMeta.connections = this.$refs.wfcanvas.loadMeta(meta)
    if (this.currentNode !== '') {
      var currenNodeId = this.$refs.wfcanvas.getNodeIdByName(this.currentNode)
      if (currenNodeId !== undefined) {
        this.setCurrentNode(currenNodeId)
        this.$refs.wfcanvas.selectCurrentNode(currenNodeId)
      } else {
        this.currentNode = ''
      }
    }

    this.$refs.loading.show = false
    const wfcanvas = this.$refs.wfcanvas
    this.$nextTick(() => {
      meta.instance.connections?.forEach(item => {
        // wfcanvas.addConnectionName(item)
      })
    })
  },
  beforeOpenCanvas() {
    this.$refs.loading.show = true
    this.typeRadio = '1'
    this.userOrRoleValue = []
    this.userOrRoleData = []
    this.resetMeta()
  },
  openNew() {
    var metaContainer = {}
    this.wfMeta.id = ''
    this.wfMeta.versionStatus = '未启用'
    this.wfMeta.existBizData = false
    this.wfFrom.name = '新建流程'
    this.wfFrom.templateType = ''
    this.wfFrom.startCondition = ''
    this.wfFrom.selWfSettingStr = ''
    this.wfMeta.lockVersion = ''
    this.wfFrom.detailDlgWidth = 1200
    this.wfFrom.detailDlgHeight = 800
    this.radioDisabled = false
    this.getMetaToSave(metaContainer)
    this.$refs.wfcanvas.loadMeta(metaContainer.meta)
    this.$refs.loading.show = false
    this.bindObjitems = [] // 置空绑定对象
    this.$refs.versionRelate.canvasOpened = true
    if (this.$isNotEmpty(this.$refs.checkboxtree)) { // 对话框可能还没有加载
      this.$refs.checkboxtree.setCheckedKeys([]) // 置空勾选的树节点
    }
  },
  getMetaToSave(metaContainer) {
    var meta = {
      main: { id: this.wfMeta.id,
        name: this.wfFrom.name,
        lockVersion: this.wfMeta.lockVersion,
        existBizData: this.wfMeta.existBizData,
        versionStatus: this.wfMeta.versionStatus,
        templateType: this.wfFrom.templateType,
        startCondition: this.wfFrom.startCondition,
        selWfSettingStr: this.wfFrom.selWfSettingStr
      },
      instance: {
        nodes: [],
        connections: [],
        binds: [],
        versionStatus: this.wfMeta.versionStatus,
        detailDlgWidth: this.wfFrom.detailDlgWidth,
        detailDlgHeight: this.wfFrom.detailDlgHeight }
    }

    // 同步节点位置
    var xyData = this.$refs.wfcanvas.getNodesXY()
    for (var nodeId in this.wfMeta.nodes) {
      // 判断连线节点名称的名称是否为空，为空的话就不插入节点数据
      if (!this.$isNotEmpty(this.wfMeta.nodes[nodeId].name) && this.$isNotEmpty(this.wfMeta.nodes[nodeId].connectionId)) {
        continue
      }
      var xy = xyData[nodeId]
      this.wfMeta.nodes[nodeId].x = xy.x
      this.wfMeta.nodes[nodeId].y = xy.y
      meta.instance.nodes.push(this.wfMeta.nodes[nodeId])
    }

    // 服务端需要连线的source和target，而不是sourceId和targetId
    for (var cnnId in this.wfMeta.connections) {
      var cnn = this.wfMeta.connections[cnnId]
      var sourceNode = this.wfMeta.nodes[cnn.sourceId]
      var targetNode = this.wfMeta.nodes[cnn.targetId]
      cnn.source = sourceNode.name
      cnn.target = targetNode.name
      meta.instance.connections.push(cnn)
    }

    for (var i = 0; i < this.bindObjitems.length; i++) {
      var bind = {}
      bind.metaName = this.wfFrom.name
      bind.bindName = this.bindObjitems[i]
      meta.instance.binds.push(bind)
    }
    metaContainer.meta = meta
  },
  afterMetaSaved(result) {
    this.wfMeta.id = result.attributes['id']
    this.wfMeta.lockVersion = result.attributes['metaLockVersion']
    // this.$refs.versionRelate.openCanvas(this.wfMeta.id)
  },

  btSwitchStatusAfter(result) {
    this.wfMeta.lockVersion = result.attributes['metaLockVersion']
    this.wfMeta.versionStatus = result.attributes['versionStatus']
  },

  btAddNode() { // 添加节点
    this.$refs.wfcanvas.addNode({ x: 30, y: 30 })
  },
  btnUserDet() {
    this.initMetaByNode(this.$refs.wfUserDept)
  },
  btnUserCform() {
    this.initMetaByNode(this.$refs.wfUserCform)
  },
  btnAuditEdit() {
    this.initMetaByNode(this.$refs.wfAuditEdit)
  },
  initMetaByNode(funcObj) {
    if (this.$isEmpty(funcObj) || this.$isEmpty(funcObj.initMeta)) {
      this.$message.error('功能对象或者initMeta不能为空')
      return
    }

    var nodeId = this.wfFrom.currentNode.id
    var nodeMeta = this.wfMeta.nodes[nodeId]
    if (nodeMeta && nodeMeta.bizid) {
      var node = {}
      node.usefuls = nodeMeta.usefuls
      node.name = nodeMeta.name
      node.bizid = nodeMeta.bizid
      node.metaVersionId = nodeMeta.metaVersionId
      node.metaId = this.wfMeta.id
      funcObj.initMeta(node, this.bindObjitems)
    }
  },
  useDeptChange() {
    this.$refs.versionRelate.openCanvas(this.wfMeta.id)
  },
  btUseFul() {
    var nodeId = this.wfFrom.currentNode.id
    var nodeMeta = this.wfMeta.nodes[nodeId]
    if (nodeMeta.bizid) {
      var node = {}
      node.usefuls = nodeMeta.usefuls
      node.name = nodeMeta.name
      node.bizid = nodeMeta.bizid
      node.metaId = this.wfMeta.id
    }
    this.$refs.WfUsefulListDetail.initMeta(this.currentNode, node)
  },
  showWfMoreSetting() {
    if (this.$isNotEmpty(this.bindObjitems)) {
      if (!this.wfMeta.id) {
        this.$message.error('未获取到流程id,请检查是否保存流程')
        return
      }
      this.$callApiParams('getBindObject', {},
        result => {
          var formtype = ''
          if (result.success) {
            var chiArr = []
            for (let i = 0; i < result.data.length; i++) {
              for (let j = 0; j < result.data[i].children.length; j++) {
                chiArr.push(result.data[i].children[j])
              }
            }
            for (let i = 0; i < chiArr.length; i++) {
              if (this.bindObjitems[0] === chiArr[i].label) {
                formtype = chiArr[i].parentId
                if (chiArr[i].label === '指标调剂' || chiArr[i].label === '指标调整') { // 调整调剂特殊处理（非表单）
                  formtype = chiArr[i].label
                }
              }
            }
          }
          if (this.$isNotEmpty(formtype)) {
            this.wfFrom.nodes = this.wfMeta.nodes
            this.$refs.WfMoreSetting.initSetting(this.wfMeta.id, this.wfFrom, this.nodeDisabled, formtype)
          } else {
            this.$message.error('未获取到对应的表单类型')
          }
          return true
        })
    } else {
      this.$message.error('请先进行业务绑定')
    }
  },
  setWfMetaSetting(value) {
    this.wfFrom.selWfSettingStr = value
    this.wfMeta.lockVersion++
  },
  setWfCurrtNodeSetting(value, selectRecAcctTypeOptions, auxiliaryAuditRuleId, aiAuditRuleIds, auditRuleFiles
    , auditScreenMetaName, auditScreenMetaId) {
    this.wfFrom.currentNode.selWfNodeSettingStr = value
    this.wfFrom.currentNode.selectRecAcctTypeOptions = selectRecAcctTypeOptions
    this.wfFrom.currentNode.auxiliaryAuditRuleId = auxiliaryAuditRuleId
    this.wfFrom.currentNode.aiAuditRuleId = aiAuditRuleIds
    this.wfFrom.currentNode.auditRuleFilesId = auditRuleFiles
    this.wfFrom.currentNode.auditScreenMetaName = auditScreenMetaName
    this.wfFrom.currentNode.auditScreenMetaId = auditScreenMetaId
    if (this.wfFrom.currentNode.lockVersion) {
      this.wfFrom.currentNode.lockVersion++
    }
  },
  // 流程数据版本设置
  btDataSetting() {
    if (!this.wfMeta.id) {
      this.$message.error('未获取到流程id,请检查是否保存流程')
      return
    }
    this.$refs.wfDataSetting.isDialog = true
    this.$refs.wfDataSetting.init(this.wfMeta.id)
  },
  // 检查流程
  checkWf() {
    if (!this.wfMeta.id) {
      this.$message.error('未获取到流程id,请检查是否保存流程')
      return
    }
    var metaContainer = {}
    this.getMetaToSave(metaContainer)
    this.$refs.wfCheck.isDialog = true
    this.$refs.wfCheck.init(metaContainer, this.wfMeta.id)
  },
  btPreview() {
    var metaContainer = {}
    this.getMetaToSave(metaContainer)
    this.$nextTick(() => {
      this.$showWfHistory(null, metaContainer, this)
    })
  },
  btBindObject() {
    this.bindObjDlgVisible = true
    if (this.index === 0) {
      setTimeout(() => {
        this.$refs.checkboxtree.setCheckedKeys(this.bindObjitems)
        this.$refs.checkboxtree.filterText = ''
      }, 500)
      this.index = 1
    } else {
      this.$refs.checkboxtree.setCheckedKeys(this.bindObjitems)
      this.$refs.checkboxtree.filterText = ''
    }
  },
  wfImport(file) {
    this.$refs.fileDomJson.clearFiles()
    const fd = new FormData()
    fd.append('file', file.raw, file.name)
    request({
      url: `${process.env.VUE_APP_API_IC}/bifrost/cloud/api.do?apiKey=versionActionWfMetaEntity&actionKey=importJson`,
      method: 'post',
      data: fd
    }).then(res => {
      const result = res.data
      if (result && result.success) {
        const callBack = () => {
          this.$refs.versionRelate.nodeClick(result.data, {})
          setTimeout(() => {
            const node = result.data
            const itemKey = node.itemKey
            this.$refs.versionRelate?.$refs?.classifytree.setCurrentItem(node.id, itemKey)
          }, 300)
        }
        this.$refs.versionRelate?.$refs?.classifytree.init(callBack)
        this.$message.success('导入成功')
      } else {
        this.$message.warning(result.msg)
      }
    })
  },
  wfExport() {
    if (this.wfMeta.id) {
      this.$fileDownloadBykey('versionActionWfMetaEntity', {
        actionKey: 'exportJson',
        ids: this.wfMeta.id
      })
    } else {
      this.$message.error('先保存成功后再导出')
    }
  },
  cancelBindObj() {
    this.bindObjDlgVisible = false
  },
  confirmBindObj() {
    this.bindObjDlgVisible = false
    var result = this.$refs.checkboxtree.getCheckedNodesData()
    this.bindObjitems = result
    // 当流程存在时，点击绑定对象弹出框的“确定”按钮便直接保存数据
    if (this.wfMeta.id !== '') {
      this.btSave() // 流程元数据整体保存
      /* var bindsObject = { mainId: this.wfMeta.id, binds: [] }
      for (var i = 0; i < this.bindObjitems.length; i++) {
        var bind = {}
        bind.metaName = this.wfFrom.name
        bind.bindName = this.bindObjitems[i]
        bindsObject.binds.push(bind)
      }
      this.$callApi('saveWfBinds', bindsObject, result => {
        return true
      })*/
    }
  },
  typeChange(val) {
    if (this.userData.length > 0 && this.roleData.length > 0) {
      if (val === '1') {
        this.userOrRoleData = this.roleData
      } else if (val === '2') {
        this.userOrRoleData = this.userData
      }
    } else {
      this.selectAssignUserOrRole(val)
    }
  },
  selectAssignUserOrRole(type) {
    this.$callApiParams('selectAssignUserOrRole',
      { type: type }, result => {
        if (type === '1') {
          this.roleData = result.data
        } else if (type === '2') {
          this.userData = result.data
        }
        this.userAndRoleData = this.userAndRoleData.concat(result.data)
        return true
      })
  },
  countersignChange(radio) {
    if (radio === '启用') {
      this.typeRadio = '2'
      this.nodeUserRo = true
      this.typeChange(this.typeRadio)
    } else {
      this.nodeUserRo = false
    }
    if (radio === '') {
      this.wfFrom.currentNode.countersignType = ''
    }
    this.openMsgAndUse('1', radio)
  },
  countersignTypeChange(countersignType) {
    if (countersignType != '比例模式会签') {
      this.wfFrom.currentNode.countersignPro = ''
    }
    if (countersignType === '单组长模式的会签') {
      this.wfFrom.currentNode.isAddCountersign = '启用'
    } else {
      this.wfFrom.currentNode.isAddCountersign = ''
    }
  },
  addCountersignChange(radio) {
    if (radio === '启用') {
      this.openMsgAndUse('4', radio)
    }
  },
  backNodeChange(radio) {
    this.openMsgAndUse('2', radio)
  },
  nextAuthUserChange(radio) {
    this.openMsgAndUse('3', radio)
  },
  nextAuthUserRequiredChange(radio) {
    if (radio === '启用') {
      this.openMsgAndUse('5', radio)
    }
  },
  openMsgAndUse(type, radio) {
    if (radio === '启用') {
      var _this = this
      var lastNodes = []
      var nextNodes = []
      for (var cnnId in this.wfMeta.connections) {
        var cnn = this.wfMeta.connections[cnnId]
        var sourceNode = this.wfMeta.nodes[cnn.sourceId]
        var targetNode = this.wfMeta.nodes[cnn.targetId]
        if (targetNode.name === _this.wfFrom.currentNode.name) {
          lastNodes.push(sourceNode)
        }
        if (sourceNode.name === _this.wfFrom.currentNode.name) {
          nextNodes.push(targetNode)
        }
      }
      if (type === '1') {
        // 会签功能
        if (!lastNodes || lastNodes.length === 0) {
          this.wfFrom.currentNode.isCountersign = ''
          this.$message.error('查不到上一个节点，无法启用该功能')
        }
        // if (this.wfFrom.currentNode.isAddCountersign && this.wfFrom.currentNode.isAddCountersign === '启用') {
        //   _this.wfFrom.currentNode.isCountersign = ''
        //   this.wfFrom.currentNode.isCountersign = ''
        //   this.$message({
        //     message: '加签功能和会签不能同时启用',
        //     type: 'warning'
        //   })
        // }
        lastNodes.forEach(row => {
          if (row.isNextAuthUser && row.isNextAuthUser === '启用') {
            this.$confirm('该功能与上个节点启用的\'下环节人\'存在冲突，如两者都开启时则优先使用\'会签功能\'，是否继续？', '提示', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {

            }).catch(() => {
              this.wfFrom.currentNode.isCountersign = ''
            })
          }
        })
      } else if (type === '2') {
        // 退回环节
        if (!lastNodes || lastNodes.length === 0) {
          this.wfFrom.currentNode.isBackNode = ''
          this.$message.error('查不到上一个节点，无法启用该功能')
        }
      } else if (type === '3') {
        if (!nextNodes || nextNodes.length === 0) {
          this.wfFrom.currentNode.isNextAuthUser = ''
          this.$message.error('查不到下一个节点，无法启用该功能')
        }
        // 下环节人
        nextNodes.forEach(row => {
          if (row.isCountersign && row.isCountersign === '启用') {
            this.$confirm('该功能与下个节点启用的\'会签功能\'存在冲突，如两者都开启时则优先使用\'会签功能\'，是否继续？', '提示', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {

            }).catch(() => {
              this.wfFrom.currentNode.isNextAuthUser = ''
            })
          }
        })
      } else if (type === '4') { // 加签功能
        // if (_this.wfFrom.currentNode.isCountersign && _this.wfFrom.currentNode.isCountersign === '启用') {
        //   this.$message({
        //     message: '加签功能和会签不能同时启用',
        //     type: 'warning'
        //   })
        // }
        _this.wfFrom.isAddCountersign = ''
      } else if (type === '5') { // 下一还节选择用户是否必填
        if (!nextNodes || nextNodes.length === 0 || this.wfFrom.currentNode.isNextAuthUser !== '启用') {
          this.wfFrom.currentNode.isNextAuthUserRequired = ''
          this.$message.error('下一环节人没有开启，无法启用该功能')
        }
      }
    } else {
      // 下一环节禁用时，选择用户功能同时禁用
      if (type === '3') {
        this.wfFrom.currentNode.isNextAuthUserRequired = ''
        this.nodeUserRequired = false
      }
    }
  },
  transferChange() {
    var _this = this
    var nodeId = _this.wfFrom.currentNode.id
    if (nodeId) {
      _this.wfMeta.nodes[nodeId].assign = []
      for (var i = 0; i < _this.userOrRoleValue.length; i++) {
        for (var j = 0; j < _this.userAndRoleData.length; j++) {
          if (_this.userOrRoleValue[i] === _this.userAndRoleData[j].userRoleId) {
            var assgin = JSON.parse(JSON.stringify(_this.userAndRoleData[j]))
            assgin.nodeName = _this.wfFrom.currentNode.name
            _this.wfMeta.nodes[nodeId].assign.push(assgin)
          }
        }
      }
      // this.saveNodeAssigns(nodeId)
      if (this.wfMeta.id !== '') {
        this.btSave() // 流程元数据整体保存
      }
    }
  },
  saveNodeAssigns(nodeId) {
    var _this = this
    var nodeMeta = _this.wfMeta.nodes[nodeId]
    if (nodeMeta.bizid) {
      var node = {}
      node.assign = nodeMeta.assign
      node.name = nodeMeta.name
      node.bizid = nodeMeta.bizid
      node.metaId = this.wfMeta.id
      this.$callApi('saveWfNodeAssigns', node, result => {
        return true
      })
    }
  },
  // 穿梭框双击选中
  selectPanel() {
    var _this = this
    window.setTimeout(() => {
      const list1 = document.getElementsByClassName('el-transfer-panel__list')[0].children
      for (let i = 0; i < list1.length; i++) {
        list1[i].ondblclick = (e) => {
          _this.userOrRoleData.forEach((item) => {
            if (item.userRoleName === e.toElement.innerText) {
              _this.userOrRoleValue.push(item.userRoleId)
              _this.transferChange()
            }
          })
        }
      }
      const list2 = document.getElementsByClassName('el-transfer-panel__list')[1].children
      for (let i = 0; i < list2.length; i++) {
        list2[i].ondblclick = (e) => {
          _this.userOrRoleData.forEach((item) => {
            if (item.userRoleName === e.toElement.innerText) {
              const ind = _this.userOrRoleValue.findIndex((item1) => {
                return item1 === item.userRoleId
              })
              _this.userOrRoleValue.splice(ind, 1)
              _this.transferChange()
            }
          })
        }
      }
    }, 100)
  },
  getWfActionList() {
    this.$callApiParams('getWfActionList',
      {}, result => {
        this.actionTableDatas = result.data
        return true
      })
  },
  btNodeAction() {
    this.nodeActionDlgVisible = true
    this.tableDatas = JSON.parse(JSON.stringify(this.actionTableDatas))
    if (this.currentNodeActions) {
      for (var i = 0; i < this.currentNodeActions.length; i++) {
        for (var j = 0; j < this.tableDatas.length; j++) {
          if (this.currentNodeActions[i].name === this.tableDatas[j].name) {
            this.tableDatas[j].actionPassType = this.currentNodeActions[i].actionPassType
            this.tableDatas[j].actionPeriod = this.currentNodeActions[i].actionPeriod
            break
          }
        }
      }
    }
    this.toggle(this.tableDatas)
  },
  toggle(data) {
    var _this = this
    if (data.length) {
      _this.$nextTick(function() {
        data.forEach(item => {
          for (var i = 0; i < _this.currentNodeActions.length; i++) {
            if (_this.currentNodeActions[i].name === item.name) {
              // actionTable 是这个表格的ref属性 true为选中状态
              _this.$refs.actionTable.toggleRowSelection(item, true)
            }
          }
        })
      })
    }
  },
  cancelNodeAction() {
    this.nodeActionDlgVisible = false
  },
  confirmNodeAction() {
    var selectRows = this.$refs.actionTable.selection
    var nodeActions = ''
    // eslint-disable-next-line no-empty
    for (var i = 0; i < selectRows.length; i++) {
      if (i === selectRows.length - 1) {
        nodeActions += selectRows[i].name
      } else {
        nodeActions += selectRows[i].name + '\n'
      }
      selectRows[i].nodeName = this.wfFrom.currentNode.name
    }
    this.nodeActions = nodeActions
    var nodeId = this.wfFrom.currentNode.id
    this.wfMeta.nodes[nodeId].actions = selectRows
    this.currentNodeActions = selectRows
    this.nodeActionDlgVisible = false
    if (this.wfMeta.id !== '') {
      this.btSave()
    }
  },
  addSupTitle(e) {
    const target = e.target
    if (target.title) return
    target.title = target.innerText
  },
  canvasOpenedChange(canvasOpened) {
    this.canvasOpened = canvasOpened
  },
  handleInput() {
    let value = Math.ceil(this.wfFrom.currentNode.countersignPro)
    // 处理输入值，确保它在最大值和最小值之间
    if (value < 0) {
      value = 0
    } else if (value > 100) {
      value = 100
    }
    this.wfFrom.currentNode.countersignPro = value
  },
  expressionFocus(type, content) {
    let expressionType = ''
    switch (type) {
      case '结束条件':
        expressionType = this.wfFrom.currentNode.expressionType || '自定义模式'
        break
      case '连线条件':
        expressionType = this.wfFrom.currentConnection.expressionType || '自定义模式'
        break
      default:
        break
    }
    this.$refs.conditionDialog.show({
      expressionType,
      type,
      content
    })
  },
  conditionSubmit(data = {}) {
    const { type, content = '', expressionType = '' } = data
    switch (type) {
      case '结束条件':
        this.wfFrom.currentNode.expression = content
        this.wfFrom.currentNode.expressionType = expressionType
        break
      case '连线条件':
        this.wfFrom.currentConnection.expression = content
        this.wfFrom.currentConnection.expressionType = expressionType
        break
      default:
        break
    }
  }
}
}
</script>

<style lang="scss" scoped>
.mright-6 {
  margin-right: 6px;
  margin-left: 0px!important;
}
.vue-bifrostIcApp .wf-container {height: 100%;}
// .vue-bifrostIcApp .common-page {padding: 10px;}
.vue-bifrostIcApp .wf-main-content {
  position: relative;
  height: 100%;
}
.radio-input ::v-deep .el-input-group__append {
  padding: 0 5px;
}
.vue-bifrostIcApp .wf-title {
    position: absolute;
    top: 5px;
    left: 0px;
    right: 10px;
    text-align: right;
}
.vue-bifrostIcApp .wf-title-font {
    display:inline-block;
    font-weight:bold;
    color:#def;
    text-shadow:0 0 1px currentColor,
    -1px -1px 1px #000,
    0 -1px 1px #000,
    1px -1px 1px #000,
    1px 0 1px #000,
    1px 1px 1px #000,
    0 1px 1px #000,
    -1px 1px 1px #000,
    -1px 0 1px #000;
    -webkit-filter:url(#wf-title-font );
    filter:url(#wf-title-font );
    font-size: 28px;
    opacity: 0.5;
    z-index: 1;
}
.vue-bifrostIcApp .wf-property-block {
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 100%;
  display:flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background: white;
  z-index: 999;
}
.vue-bifrostIcApp .wf-canvas {
  position: relative;
  height: calc(100% - 260px);
  width: 100%;
  border: 1px solid #DDDDDD;
}
.vue-bifrostIcApp .wf-form-container {
  height: 100%;
  padding: 15px 8px 0px 0px;
  border: 1px solid #DDDDDD;
  flex: 1.3;
  overflow: auto;
}
.vue-bifrostIcApp .wf-form-container ::v-deep .el-radio__input .el-radio__inner {
  width: 14px;
  height: 14px;
}
.vue-bifrostIcApp .wf-form-container:not(:first-child) {
  margin-left:5px
}
.vue-bifrostIcApp .wf-form-container .el-form-item--mini.el-form-item,
.vue-bifrostIcApp .wf-form-container .el-form-item--small.el-form-item {
  margin-bottom: 6px;
}
.vue-bifrostIcApp .wf-form-container .wf-form-bind-objects {
  margin: 0px;
  border: 1px solid #DDDDDD;
  padding: 0px 5px;
  border-radius: 4px;
  height: 105px;
  overflow: scroll;
  white-space: nowrap;
  color: #666666;
}
.vue-bifrostIcApp .wf-form-container .wf-form-bind-objects li {height: 20px;}
.vue-bifrostIcApp .wf-form-container .wf-form-node-actions {
    margin: 0px;
    border: 1px solid #DDDDDD;
    padding: 0px 5px;
    border-radius: 4px;
    height: 80px;
    overflow: scroll;
}
.vue-bifrostIcApp .wf-form-container .wf-form-node-actions li {height: 20px;}
.flex {
  display: flex;
  align-items: center;
  .radio-title {
    white-space: nowrap;
    padding-left: 0px!important;
  }
}

</style>
<style lang="scss">
.wffWrap{
  // min-width: 1700px;
  overflow-x: auto;
  overflow-y: hidden;
}
.wffWrap > .pageWarp{
  min-width: 1700px;
}
.vue-bifrostIcApp .wf-form-container .el-transfer-panel {width: calc((100% - 32px)/2);}
.vue-bifrostIcApp .wf-form-container .el-transfer-panel__header {display: none;}
.vue-bifrostIcApp .wf-form-container .wf-task-assign .el-form-item__content {margin-left: 12px !important;}
.vue-bifrostIcApp .wf-form-container .el-form-item__content .el-radio-group .el-radio{padding-bottom: 2px}
.vue-bifrostIcApp .wf-form-container .el-transfer-panel__body {
  height: 180px;
  border: 1px solid #DDDDDD;
}
.vue-bifrostIcApp .wf-form-container .el-button+.el-button {margin-left: 0px;}
.vue-bifrostIcApp .wf-form-container .el-transfer__button:first-child{margin-bottom: 5px;}
.vue-bifrostIcApp .wf-form-container .wf-task-assign .el-button {padding: 3px !important;}
.vue-bifrostIcApp .wf-form-container .wf-task-assign .el-transfer__buttons {
    width: 32px;
    padding: 0px 5px;
    line-height: 18px;
}
.vue-bifrostIcApp .wf-form-container .el-radio__inner,
.vue-bifrostIcApp .wf-form-container .el-textarea__inner,
.vue-bifrostIcApp .wf-form-container .el-checkbox__inner{
  border: 1px solid #DDDDDD;
}
.vue-bifrostIcApp .wf-form-container .el-textarea__inner {padding: 5px;}
.vue-bifrostIcApp .common-page .dbcol-right {overflow: hidden !important;}
.vue-bifrostIcApp .wf-form-container .el-transfer .el-transfer-panel__filter {
    margin: 0px;
    padding: 5px;
    padding-bottom: 3px;
}
.vue-bifrostIcApp .wf-form-container .el-transfer .el-transfer-panel__filter .el-input__inner {
    height: 25px;
    padding-left: 20px;
}
.vue-bifrostIcApp .wf-form-container .el-transfer .el-input__prefix .el-input__icon {
    padding: 5px 0px
}
.vue-bifrostIcApp .wf-form-container .el-transfer-panel__list .el-transfer-panel__item{
    height: 24px;
    padding-left: 5px;
}
.vue-bifrostIcApp .wf-form-container .el-transfer div:nth-child(3) .el-transfer-panel__body .el-transfer-panel__list label:nth-child(1){
    margin-top: 4px;
}
.vue-bifrostIcApp .wf-form-container .el-transfer div:nth-child(3) .el-transfer-panel__body .el-transfer-panel__filter{
    display: none;
}
.vue-bifrostIcApp .wf-form-container .el-transfer div:nth-child(3) .el-transfer-panel__body .el-transfer-panel__list.is-filterable{
    height: 178px;
}
.vue-bifrostIcApp .wf-form-container .el-transfer-panel__list.is-filterable{
    height: 143px;
}
.vue-bifrostIcApp .wf-form-container .el-transfer-panel__item .el-checkbox__input{
    top: 4px
}
.vue-bifrostIcApp .wf-form-container .el-transfer-panel__item.el-checkbox .el-checkbox__label{
    line-height: 22px;
}
.vue-bifrostIcApp .wf-form-container .el-transfer-panel__list .el-checkbox{
    margin-right: 5px;
}
.vue-bifrostIcApp #secondFormContainer textarea{
  height: 70px !important;
}
.vue-bifrostIcApp #firstFormContainer textarea{
  height: 70px !important;
}
.vue-bifrostIcApp #secondFormContainer .el-cascader-node>.el-radio, .el-radio:last-child{
  margin-right: 0px !important;
}
</style>
