<template>
  <el-dialog append-to-body ref="auditViewDetail" width="490px" title="设置常用语" :visible.sync="isUseFulVisible"  >
      <el-table
        class="table-border"
        :data="tableData"
        height="520"
        border
        style="width: 100%;padding-left: 1px;">
        <el-table-column v-if="isShowAc"
          prop="code"
          label="操作"
          width="60px">
          <template slot-scope="{ row }">
            <el-button
              @click.native.prevent="setUseful(row)"
              type="text"
              size="small" style="margin-left: -18px">
              选中
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="usefulOrder"
          label="序号"
          width="80px" >
          <template slot-scope="{ row }">
            <el-input clearable v-model="row.usefulOrder" placeholder="请输入序号" maxlength="3"  @input="(val)=>veInputSupdepCode(val,row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="usefulText"
          label="常用语"
          width="250px">
          <template slot-scope="{ row }">
            <el-input type="textarea" :autosize="{ minRows: 1, maxRows: 4}" clearable v-model="row.usefulText" placeholder="请输入常用语" maxlength="100"></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="state"
          label="状态" width="60px">
          <template slot-scope="{ row }">
            <el-switch v-model="row.state"></el-switch>
          </template>
        </el-table-column>
<!--        <el-table-column-->
<!--          prop="expression"-->
<!--          label="表达式"-->
<!--          width="170px" >-->
<!--          <template slot-scope="{ row }">-->
<!--            <el-input clearable v-model="row.expression" placeholder="请输入表达式" ></el-input>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column
          prop="code"
          label="新增"
          width="58px">
          <template slot="header">
            <el-button
              @click.native.prevent="handleAdd(tableData)"
              type="text"
              size="small" style="margin-left: -18px">
              新增
            </el-button>
          </template>
          <template slot-scope="scope">
            <el-button
              @click.native.prevent="deleteRow(scope.$index, tableData)"
              type="text"
              size="small" style="margin-left: -18px">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    <span slot="footer" class="dialog-footer">
    <el-button @click="isUseFulVisible = false">取 消</el-button>
    <el-button type="primary" @click="usefulChange">保 存</el-button>
  </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'wf-useful-list',
  props: {
    dataApiKey: { type: String, default: '' },
    checkedRows: { type: Object, default: () => {} }
  },
  created() {
    this.reload()
  },
  data() {
    return {
      tableData: [],
      isUseFulVisible: false,
      isShowAc: false,
      isUsefulUser: false,
      isUsefulPage: false,
      node: {
        usefuls: [],
        name: '',
        bizid: '',
        metaId: ''
      }
    }
  },
  methods: {
    reload() {
      if (this.$isNotEmpty(this.checkedRows)) {
        this.isUseFulVisible = true
      }
    },
    initMeta(currentNode, node, isShowAc, opinion) {
      this.isUseFulVisible = true
      this.currentNode = currentNode
      this.node = node
      this.isShowAc = isShowAc
      if (isShowAc) {
        this.$setDlgWidthSize(this, 'auditViewDetail', 550)
      }
      this.$callApiParams('getCurrentNodeWfNodeUsefuls',
        { nodeName: currentNode, isUsefulPage: this.isUsefulPage }, result => {
          this.tableData = result.data.wfNodeUsefuls
          this.node.usefuls = result.data.wfNodeUsefuls
          if (opinion) {
            this.handleAdd(this.tableData, opinion)
          }
          return true
        })
    },
    veInputSupdepCode(value, rows) {
      // 只能输入数字
      const newValue = value.replace(/[^\d]/g, '')
      this.$set(rows, 'date', newValue)
    },
    handleAdd(rows, opinion) {
      const data = {
        usefulOrder: rows.length,
        usefulText: opinion || '',
        state: true
      }
      rows.push(data)
    },
    deleteRow(index, rows) {
      rows.splice(index, 1)
    },
    setUseful(rows) {
      this.isUseFulVisible = false
      this.$emit('setUseful', rows.usefulText)
    },
    usefulChange() {
      for (const item of this.tableData) {
        item.nodeName = this.currentNode
      }
      this.$callApi('saveWfNodeUsefuls',
        this.node, result => {
          if (result.success) {
            this.$emit('usefulChange', this.tableData)
            this.isUseFulVisible = false
          }
        }, null, this)
    },
    getExParamsCallApiSave(data) {
      return `&isUsefulUser=${this.isUsefulUser}`
    },
    checkBeforeCallApiSave(data) {
      return true
    }

  }
}
</script>
