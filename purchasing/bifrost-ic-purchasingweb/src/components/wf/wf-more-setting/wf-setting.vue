<template>
  <div class="container"
       style="height: 100%"
       v-loading="composeLoading"
       element-loading-spinner="el-icon-loading"
       element-loading-text="加载中...">
    <div id="cwfName">
      流程名称：{{wfName}}
    </div>
    <div id='wfOption'>
      <el-checkbox-group v-model="checkedWfList"  @change="handleCheckedCSChange">
        <el-checkbox v-for="wfOption in wfOptionList" :label="wfOption.tname" :key="wfOption.tname">
          {{wfOption.tname}}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div id='print' v-show="printSetting">
      <div style="width: 100%">打印设置</div>
      <el-select v-model="printTypeValue"  style="width:49%" @change="printTypeChange">
        <el-option
          v-for="item in printType"
          :key="item.name"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
      <el-select v-model="checkedNode"  :disabled="isPrintNode"  multiple  style="width:49%;margin-left: 13px">
        <el-option
          v-for="item in nodes"
          :key="item.name"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
      <div id='message' v-show="!isPrintNode">
        <span>消息内容</span>
        <el-input v-model="messageContent" ></el-input>
      </div>
    </div>

    <div id='wfTimeOut' v-show="isShowWfTimeOutSetting">
      <div style="width: 100%">超时配置</div>
      <el-row style="padding: 5px">
        <el-col :span="12">
          <el-input v-model="wfLimitAuditDate" style="width: 98%" placeholder="请输入审核期限"
                    @input="(val)=>inputNumbersFormat(val)" maxlength="3" >
            <template v-if="true" slot="append">天</template>
          </el-input>

        </el-col>

        <el-col :span="12">
          <el-select v-model="wfIsAutoAudit"  clearable style="width:98%;margin-left: 10px" placeholder="请选择超时是否自动处理">
            <el-option
              v-for="item in auditTypes"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row style="padding: 5px">
        <el-col :span="12">
          <el-select v-model="wfSendMessageType"  multiple style="width:98%" placeholder="请选择超时通知">
            <el-option
              v-for="item in messageTypes"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
    </div>

    <div id='recRelatedBooks' v-show="isShowRecRelatedBooks">
      <span>关联账套</span>
      <el-input v-model="relatedBooksetNames" :readonly="true" @click.native="btRelatedBooks" placeholder="请选择数据"></el-input>
    </div>
    <!--流程退回再提交设置-->
    <div id='isProcessBackSetting' v-show="isShowProcessBackSetting">
      <span>退回再提交设置</span>
      <el-select v-model="checkedProcessBackOptions" multiple  style="width:100%">
        <el-option
          v-for="item in wfBackTypeOptions"
          :key="item.name"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
    </div>

    <div id='autoAudit' v-show="isAutoAudit">
      <span>自动审核类型</span>
      <el-select v-model="autoAuditType"   style="width:100%">
        <el-option
          v-for="item in autoAuditTypeData"
          :key="item.name"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
    </div>

    <el-dialog width="4" height="10" ref="modifyWfRelatedBooksDlg"
               class="modifyWfRelatedBooksDlg" title="关联账套"
               :visible.sync="relatedBooksDlgVisible"
               :close-on-click-modal='false'
               append-to-body>
      <div style="height: 100%;">
        <sup-tree :setting="booksSetting"
                  v-if="relatedBooksDlgVisible"
                  style="height: 90%"
                  ref="orgSupTree"
                  :nodes="orgData"
                  :is-popover="false"
                  :edit-enable="true"></sup-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="relatedBooksDlgVisible = false">取消</el-button>
        <el-button type="primary" @click="selectRelatedBooks">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'wfSetting',
  props: {
    wfMetaId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      wfName: '',
      checkedWfList: [],
      wfOptionList: [], // 表单选项数据
      printSetting: false,
      isShowProcessBackSetting: false, // 是否显示流程退回再提交设置
      isAutoAudit:false, //是否显示自动审核类型
      autoAuditType:'',
      autoAuditTypeData:[],
      isShowRecRelatedBooks: false,
      isShowWfTimeOutSetting: false, // 是否显示流程超时配置
      printTypeValue: '',
      isPrintNode: false,
      checkedNode: [], // 选中的节点
      messageContent: '', //消息内容
      printType: [{ name: '审核结束' }, { name: '任意节点' }, { name: '某节点审核通过后' },{name:'到某节点'}],
      relatedBookset: {},
      relatedBooksDlgVisible: false,
      orgData: [], // 账套数据
      wfLimitAuditDate: '',
      wfIsAutoAudit: '',
      wfSendMessageType: [],
      auditTypes: [{ id: 1, name: '是' }, { id: 0, name: '否' }],
      messageTypes: [{ id: 1, name: '发送系统通知' }, { id: 2, name: '发送短信' }],
      wfBackTypeOptions: [{ name: '退送后再次送审需依次从头审批' }, { name: '退送后再次送审跳至本岗审核' }], // 退回再提交下拉数据
      checkedProcessBackOptions: [], // 已选择的流程退回再提交类型
      nodes: [],
      booksSetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'orgCode',
            pIdKey: 'parentCode'
          },
          key: {
            checked: 'isCheck',
            name: 'orgName'
          }
        },
        view: {
          showIcon: true,
          showLine: true
        }
      },
      loadingMap: {}
    }
  },
  computed: {
    relatedBooksetNames() {
      if (this.$isNotEmpty(this.relatedBookset)) {
        const val = Object.values(this.relatedBookset)
        return val.join(',')
      }
      return ''
    },
    composeLoading() {
      const loading = Object.values(this.loadingMap).some(value => value)
      return loading
    }
  },
  methods: {
    init(metaId, wfFrom, nodeDisabled, formtype) {
      this.wfName = wfFrom.name
      this.nodes = wfFrom.nodes
      this.setOptionList(wfFrom)
      this.selectWfOptionList() // 查询表单选项设置列表
      this.loadPrintSetting()
      this.loadRelatedBookset() // 查询关联账套
      this.loadWfTimeOutSetting()
      this.loadProcessBackSetting()
      this.getAutoAuditType()
    },
    setOptionList(wfFrom) { // 设置已经选中的选项
      this.checkedWfList = []
      if (wfFrom.selWfSettingStr) {
        var strArr = wfFrom.selWfSettingStr.split(',')
        for (let i = 0; i < strArr.length; i++) {
          this.checkedWfList.push(strArr[i])
        }
      }
      this.handleCheckedCSChange()
    },
    handleCheckedCSChange() {
      this.printSetting = this.$isNotEmpty(this.checkedWfList.filter(item => item === '打印设置'))
      this.isShowProcessBackSetting = this.$isNotEmpty(this.checkedWfList.filter(item => item === '退回再提交设置'))
      // 节点退回再提交设置
      this.isShowRecRelatedBooks = this.$isNotEmpty(this.checkedWfList.filter(item => item === '关联账套'))
      this.isShowWfTimeOutSetting = this.$isNotEmpty(this.checkedWfList.filter(item => item === '超时配置'))
      this.isAutoAudit = this.$isNotEmpty(this.checkedWfList.filter(item => item === '自动审核'))
    },
    getAutoAuditType() {
      this.$set(this.loadingMap, 'autoAuditType', true)
      this.$callApiParams('getAutoAuditType', { metaId: this.wfMetaId },
        result => {
          if (result.success) {
            this.autoAuditTypeData = result.data.data
            this.autoAuditType = result.data.selectData
          }
          this.$set(this.loadingMap, 'autoAuditType', false)
          return true
        }, () => {
          this.$set(this.loadingMap, 'autoAuditType', false)
        })
    },
    selectWfOptionList() { // 查询表单选项设置列表
      this.$set(this.loadingMap, 'optionList', true)
      this.$callApiParams('selectDictResultList', { tparentCode: '10021' },
        result => {
          if (result.success) {
            this.wfOptionList = result.data
          }
          this.$set(this.loadingMap, 'optionList', false)
          return true
        }, () => {
          this.$set(this.loadingMap, 'optionList', false)
        })
    },
    loadWfTimeOutSetting() {
      this.$set(this.loadingMap, 'timeOutSetting', true)
      this.$callApiParams('selectWfAuditTimeOutSetting', { metaId: this.wfMetaId
      }, result => {
        if (result.success) {
          const { limitAuditDate, isAutoAudit, sendMessageType } = result.data.auditWfTimeOutEntity || {}
          this.wfLimitAuditDate = limitAuditDate
          this.wfIsAutoAudit = isAutoAudit
          let wfMessageTypeTmp = []
          if ((!Array.isArray(sendMessageType)) && sendMessageType) {
            wfMessageTypeTmp = [sendMessageType]
          }
          this.wfSendMessageType = wfMessageTypeTmp
        }
        this.$set(this.loadingMap, 'timeOutSetting', false)
        return true
      }, () => {
        this.$set(this.loadingMap, 'timeOutSetting', false)
      })
    },
    loadRelatedBookset() {
      this.$set(this.loadingMap, 'relatedBookset', true)
      this.$callApiParams('loadRelatedBookset', { metaId: this.wfMetaId
      }, result => {
        if (result.success) {
          this.relatedBookset = result.data
        }
        this.$set(this.loadingMap, 'relatedBookset', false)
        return true
      }, () => {
        this.$set(this.loadingMap, 'relatedBookset', false)
      })
    },
    loadProcessBackSetting() {
      this.checkedProcessBackOptions = []
      if (this.isShowProcessBackSetting) {
        this.$set(this.loadingMap, 'processBackSetting', true)
        this.$callApiParams('selectProcessBackSetting', {
          metaId: this.wfMetaId
        }, result => {
          if (result.success && this.$isNotEmpty(result.data)) {
            var typeNames = result.data.typeName.split(',')
            for (let i = 0; i < typeNames.length; i++) {
              this.checkedProcessBackOptions.push(typeNames[i])
            }
          }
          this.$set(this.loadingMap, 'processBackSetting', false)
          return true
        }, () => {
          this.$set(this.loadingMap, 'processBackSetting', false)
        })
      }
    },
    printTypeChange() {
      if (this.printTypeValue === '某节点审核通过后'||this.printTypeValue === '到某节点') {
        this.isPrintNode = false
      } else {
        this.isPrintNode = true
        this.checkedNode = []
      }
    },
    loadPrintSetting() {
      this.$set(this.loadingMap, 'printSetting', true)
      this.$callApiParams('selectPrintSetting', { metaId: this.wfMetaId,
        nodeName: this.wfNodeName }, result => {
        if (result.success) {
          this.printTypeValue = result.data?.type
          this.checkedNode = result.data?.nodeName.split(',')
          this.messageContent = result.data?.messageContent
          this.printTypeChange()
        }
        this.$set(this.loadingMap, 'printSetting', false)
        return true
      }, () => {
        this.$set(this.loadingMap, 'printSetting', false)
      })
    },
    selectRelatedBooks() {
      const nodes = this.$refs.orgSupTree.treeObj.getCheckedNodes(true)
      this.relatedBookset = {}
      if (nodes.length > 0) {
        for (var i = 0; i < nodes.length; i++) {
          if (nodes[i].isBook) {
            this.relatedBookset[nodes[i].orgCode] = nodes[i].orgName
          }
        }
      }
      this.relatedBooksDlgVisible = false
    },
    btRelatedBooks() {
      this.relatedBooksDlgVisible = true
      if (this.$isEmpty(this.orgData)) {
        this.$callApiParams('selectWfRelBooksetTree',
          { metaId: this.wfMetaId }, (result) => {
            this.orgData = result.data
            return true
          })
      } else {
        for (let j = 0; j < this.orgData.length; j++) {
          if (this.$isNotEmpty(this.relatedBookset[this.orgData[j].orgCode])) {
            this.orgData[j].isCheck = true
          } else {
            this.orgData[j].isCheck = false
          }
        }
      }
      this.$nextTick(() => {
        // 这里设置弹框大小
        this.$setDlgSize(this, 'modifyWfRelatedBooksDlg', 450, 550)
      })
    },
    inputNumbersFormat(value) {
      // 只能输入数字
      const newValue = value.replace(/[^\d]/g, '')
      this.wfLimitAuditDate = newValue
    },
    save() {
      var selWfSetting = ''
      this.checkedWfList.forEach(item => {
        selWfSetting = selWfSetting + item + ','
      })
      if (selWfSetting) {
        selWfSetting = selWfSetting.substring(0, selWfSetting.length - 1)
      }

      // 流程退回再提交设置
      var selectProcessBackTypeOptions = ''
      this.checkedProcessBackOptions.forEach(item => {
        selectProcessBackTypeOptions = selectProcessBackTypeOptions + item + ','
      })
      if (selectProcessBackTypeOptions) {
        selectProcessBackTypeOptions = selectProcessBackTypeOptions.substring(0, selectProcessBackTypeOptions.length - 1)
      }

      return {
        relatedBookset: this.relatedBookset,
        selWfSetting,
        printTypeValue: this.printTypeValue,
        checkedNode: this.checkedNode,
        selectProcessBackTypeOptions,
        wfLimitAuditDate: this.wfLimitAuditDate,
        wfIsAutoAudit: this.wfIsAutoAudit,
        wfSendMessageType: this.wfSendMessageType,
        autoAuditType: this.autoAuditType,
        messageContent:this.messageContent,
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#cwfName {
  padding: 5px;
}
#cwfNodeName {
  padding: 5px;
}
#wfOption {
  border: 1px solid #bbb;
  padding: 5px;
}
#print {
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}
#wfTimeOut {
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}
#isProcessBackSetting {
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}
.modifyWfRelatedBooksDlg .el-dialog__body { height: calc(100% - 104px) !important; }
::v-deep .el-checkbox__label {
  width: 118px;
  //overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
