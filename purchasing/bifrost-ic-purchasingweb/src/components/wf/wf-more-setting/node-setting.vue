<template>
  <div class="container"
       style="height: 100%"
       v-loading="composeLoading"
       element-loading-spinner="el-icon-loading"
       element-loading-text="加载中...">
    <div id="cwfNodeName" v-if="isShowCwfNodeName">
      节点名称：{{wfNodeName}}
    </div>
    <div id='wfNodeOption' v-if="isShowCwfNodeName">
      <el-checkbox-group v-model="checkedWfNodeList"  @change="handleCheckedCSChange" >
        <el-checkbox v-for="wfOption in wfNodeOptionList" :label="wfOption.tname" :key="wfOption.tname">
          {{wfOption.tname}}
        </el-checkbox>
      </el-checkbox-group>
    </div>

    <div id='nodeTimeOut' v-show="isShowNodeTimeOutSetting">
      <div style="width: 100%">节点超时配置</div>
      <el-row style="padding: 5px">
        <el-col :span="12">
          <el-input v-model="nodeLimitAuditDate" style="width: 98%" placeholder="请输入审核期限"
                    @input="(val)=>inputNumbersFormat(val)" maxlength="3" >

            <template v-if="true" slot="append">天</template>
          </el-input>

        </el-col>

        <el-col :span="12">
          <el-select v-model="nodeIsAutoAudit" clearable style="width:98%;margin-left: 10px" placeholder="请选择超时是否自动处理">
            <el-option
              v-for="item in auditTypes"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row style="padding: 5px">
        <el-col :span="12">
          <el-select v-model="nodeSendMessageType"  multiple  style="width:98%" placeholder="请选择超时通知">
            <el-option
              v-for="item in messageTypes"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
    </div>
    <div id='auditConditions' v-show="isShowAuditConditions">
      <el-row style="padding-bottom: 5px">
        <span  >审核补充条件设置 :  </span>
        <el-button  @click="addRows()">新增</el-button>
        <el-button  @click="delRows()">删除</el-button>
      </el-row>
      <el-table ref="auditConsTable"
                :data="tableListSettingColAllShow"
                @selection-change="tableListSettingColChange"
                @row-dblclick="tableListSettingColDblclick"
                @row-click='tableListSettingColClick'
                style="width: 100%" border >
        <el-table-column type="selection" width="55"/>
        <el-table-column type="index" :label="orderNumber.label" align="center" v-if="isShowOrderNumber"/>
        <el-table-column :label="列序号" width="120" prop="orderNumber"  v-if="isShowOrder">
          <template slot-scope="scope">
            <el-input
              :ref="scope.row.index + ',' + scope.column.index"
              v-model="scope.row.orderNumber"
            />
          </template>
        </el-table-column>
        <el-table-column prop="elementType" label="要素类型" :editable="true">
          <template slot-scope="scope">
            <el-select v-model="scope.row.elementType" @change="() => colTypeChange(scope)"
                       style="width:98%" placeholder="请选择要素类型">
              <el-option
                v-for="item in elementTypes"
                :key="item.name"
                :label="item.name"
                :value="item.name">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="elementName" label="要素名称" :editable="true">
          <template slot-scope="scope">
            <el-input v-model="scope.row.elementName" style="width: 98%" placeholder="请输入要素名称" maxlength="10">
            </el-input>
          </template>
        </el-table-column >

        <el-table-column prop ="referData" label="参照数据">
          <template slot-scope="scope">
            <el-select v-model="scope.row.referData"   style="width:98%" v-show="scope.row.elementType==='下拉框'">
              <el-option
                v-for="item in dataRefDrop"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <el-input v-model="scope.row.referData" style="width: 98%" placeholder="选项加逗号隔开"
                      v-show="scope.row.elementType==='单选'">
            </el-input>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div id='recAcctTypeOption' v-show="isShowRecAcctTypeOption">
      <span>必须上传附件</span>
      <el-select v-model="checkedRecAcctTypeOptions" multiple  style="width:100%">
        <el-option
          v-for="item in recAcctTypeOptions"
          :key="item.eleName"
          :label="item.eleName"
          :value="item.eleCode+`:`+item.eleName">
        </el-option>
      </el-select>
    </div>

    <div id='enableABRoleExamine' v-show="isShowEnableABRoleExamine">
      <span>AB角审核设置</span>
      <el-select v-model="checkedAssigner" clearable filterable style="width:100%">
        <el-option
          v-for="item in roleList"
          :key="item.bizId"
          :label="item.userName"
          :value="item.bizId+':'+item.userName">
        </el-option>
      </el-select>
    </div>

    <div id='recNodSameUser' v-show="isShowNodSameUser">
      <span>节点同一审核人</span>
      <el-select v-model="checkedNodSameUsers"  style="width:100%">
        <el-option
          v-for="item in NodSameUsers"
          :key="item.nodeName"
          :label="item.nodeName"
          :value="item.nodeName">
        </el-option>
      </el-select>
    </div>

    <div id='isShowAppointUser' v-show="isShowAppointUser">
      <span>指定审核人</span>
      <el-input v-model="appointUser" placeholder="请输入内容"></el-input>
    </div>
    <div id='isShowNotice' v-show="isShowNotice" >
      <span>通知配置</span>
      <el-form ref="form" :model="noticeEntity" label-width="80px">
        <el-form-item label="审核动作:">
          <el-radio-group v-model="currAction"  @change="actionChange">
            <el-radio-button label="审核通过"></el-radio-button>
            <el-radio-button label="退回修改"></el-radio-button>
            <el-radio-button label="作废"></el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否启用:">
          <el-checkbox-group v-model="noticeEntity.noticeTypeList">
            <el-radio v-model="noticeEntity.isEnable" label="禁用">禁用</el-radio>
            <el-radio v-model="noticeEntity.isEnable" label="启用">启用</el-radio>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="通知方式:">
          <el-checkbox-group v-model="noticeEntity.noticeTypeList">
            <el-checkbox label="系统通知" ></el-checkbox>
            <el-checkbox label="短信通知" ></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="通知内容:">
          <el-input maxlength="100" show-word-limit type="textarea" v-model="noticeEntity.content"></el-input>
        </el-form-item>
        <el-form-item label="通知对象:">
          <el-checkbox-group v-model="noticeEntity.noticeUserList">
            <el-checkbox label="制单人" ></el-checkbox>
            <el-checkbox label="下一环节人" ></el-checkbox>
            <el-checkbox label="流程历史审核人" ></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <!--节点退回再提交设置-->
    <div id='isNodeBackSetting' v-show="isShowNodeBackSetting">
      <span>退回再提交设置</span>
      <el-select v-model="checkedNodeBackOptions" multiple  style="width:100%">
        <el-option
          v-for="item in wfBackTypeOptions"
          :key="item.name"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
    </div>
    <div id="aiAudit">
      <span>智能审核配置</span>
      <el-form label-width="80px">
        <el-form-item label="辅助审核:">
          <el-select v-model="checkAuxiliaryAuditRuleId"  multiple  style="width:100%">
            <el-option
              v-for="item in auxiliaryAuditRuleId"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="智能审核:">
          <el-select v-model="checkAiAuditRuleId"  multiple style="width:100%">
            <el-option
              v-for="item in aiAuditRuleId"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规章制度:">
          <el-select v-model="checkAuditRuleFiles"   multiple style="width:100%">
            <el-option
              v-for="item in auditRuleFilesId"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div id='auditScreenId' >
      <span>审核特殊页面</span>
      <el-input v-model="auditScreenMetaName"
                clearable
                @clear="auditScreenClear"
                @click.native="btRelatedAuditScreen"
                placeholder="请选择数据"></el-input>
    </div>
    <el-dialog width="25%" height="10" ref="auditScreenDlg" title="表单绑定"
               :visible.sync="auditScreenDlgVisible"
               :close-on-click-modal='false'
               append-to-body
    >
      <div style="border: #DDDDDD solid 1px;height: 497px;padding: 5px;">
        <sup-tree :setting="setting"
                  ref="supTree"
                  :nodes="treeData"
                  :is-popover="false"
                  :edit-enable="true"
                  destroy-on-close
                  :checkedValues="checkedValues"
                  :btnSwitch="{clearCheck: true}"
                  :autoDeleteChildren="false">
        </sup-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditScreenDlgVisible = false">取消</el-button>
        <el-button type="primary" @click="selectRelatedAuditScreen">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'nodeSetting',
  props: {
    wfMetaId: {
      type: String,
      default: ''
    },
    wfNodeName: {
      type: String,
      default: ''
    },
    isShowCwfNodeName: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isShowNodeBackSetting: false, // 是否显示节点退回再提交设置
      isShowRecAcctTypeOption: false, // 是否显示必须上传附件
      isShowNodeTimeOutSetting: false, // 是否显示节点超时配置
      isShowAuditConditions: false, // 是否显示节点审核补充条件
      isShowNodSameUser: false,
      isShowAppointUser: false,
      isShowNotice: false,
      isShowOrderNumber: false,
      isShowOrder: false,
      isShowEnableABRoleExamine: false, // 是否显示启用AB角审核
      orderNumber: 0,
      colItem: {}, // 当前编辑的列
      tableListSettingColAll: [], //
      isOptionVisible: true, // 是否下拉
      tableListDataShow: [], // 选中的数据行
      checkedWfNodeList: [], // 选中的表单选项value数据
      wfNodeOptionList: [],
      nodeLimitAuditDate: '',
      nodeIsAutoAudit: '',
      auditTypes: [{ id: 1, name: '是' }, { id: 0, name: '否' }],
      dataRefDrop: [],
      elementTypes: [{ id: 1, name: '下拉框' }, { id: 0, name: '单选' }],
      auditConditionVo: { metaId: '', nodeName: '', conditionList: [] },
      conditionEntity: { metaId: '', nodeName: '', elementType: '', elementName: '', referData: '', orderNumber: '' },
      nodeSendMessageType: [],
      messageTypes: [{ id: 1, name: '发送系统通知' }, { id: 2, name: '发送短信' }],
      checkedRecAcctTypeOptions: [], // 已选择的附件类型
      recAcctTypeOptions: [], // 附件类型下拉数据
      checkedNodSameUsers: '',
      NodSameUsers: [],
      appointUser: '',
      noticeEntity: { action: '审核通过', noticeType: '', noticeTypeList: [], content: '', noticeUserList: [],
        noticeUser: '', isEnable: '禁用' },
      currAction: '',
      checkedNodeBackOptions: [], // 已选择的节点退回再提交类型
      wfBackTypeOptions: [{ name: '退送后再次送审需依次从头审批' }, { name: '退送后再次送审跳至本岗审核' }], // 退回再提交下拉数据
      formName: '',
      noticeVo: { metaId: '', nodeName: '', noticeEntities: {}},
      attTypeTableName: '',
      action: '',
      auxiliaryAuditRuleId: [], // 辅助审核下拉数据
      checkAuxiliaryAuditRuleId: [], // 辅助审核已选择规则
      aiAuditRuleId: [], // 智能审核下拉数据
      checkAiAuditRuleId: [], // 智能审核已选择规则
      auditRuleFilesId: [], // 规章制度下拉数据
      checkAuditRuleFiles: [], // 规章制度 已选择规则
      checkedAssigner: '',
      roleList: [],
      auditScreenDlgVisible: false,
      auditScreenMetaName: '',
      auditScreenMetaId: '',
      setting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'label'
          }
        },
        view: {
          selectedMulti: false, // 按住ctrl是否可以多选
          showIcon: true,
          showLine: true,
          fontCss: function(treeId, treeNode) {
            return (treeNode.searchNode) ? { 'color': '#A60000', 'font-weight': 'bold' } : ''
          }
        },
        callback: { // 回调操作
          // onClick: this.auditScreenClick
        }
      },
      treeData: [],
      dataType: 'CformMetaEntity',
      checkedValues: [],
      loadingMap: {}
    }
  },
  computed: {
    tableListSettingColAllShow() { // 表格列定义实际显示的数据
      var items = []
      items = this.tableListSettingColAll
      const orderTem = Math.max.apply(Math, items.map(item => { return item.orderNumber })) + 1
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.orderNumber = this.$isEmpty(items) ? 0 : orderTem
      return items
    },
    composeLoading() {
      const loading = Object.values(this.loadingMap).some(value => value)
      return loading
    }
  },
  mounted() {
    this.selectColBaseData() // 查找数据列基础数据信息
  },
  methods: {
    init(metaId, wfFrom, nodeDisabled, formtype) {
      this.isOptionVisible = true
      this.formName = formtype
      this.noticeVo.metaId = metaId
      this.noticeVo.nodeName = wfFrom.currentNode.name
      this.selectClassifyList()
      this.auditConditionVo.metaId = metaId
      this.auditConditionVo.nodeName = wfFrom.currentNode.name
      this.setOptionList(wfFrom)
      this.selectAttTypeList() // 查询附件类型列表
      this.selectWfOptionList() // 查询表单选项设置列表
      this.loadNodeBackSetting()
      this.loadAppointUser()
      this.loadNotice()
      this.loadAuditRule()
      this.selectFromNode()
      this.loadNodeTimeOutSetting()
      this.loadAuditConditions()
      this.selectExamineAssigner()
      $('#auditScreenId input').attr('readOnly', 'readOnly')
    },
    setOptionList(wfFrom) {
      this.checkedNodSameUsers = ''
      this.checkedWfNodeList = []
      this.checkedRecAcctTypeOptions = []
      this.checkAuxiliaryAuditRuleId = []
      this.checkAiAuditRuleId = []
      this.checkAuditRuleFiles = []
      this.checkedAssigner = ''

      if (wfFrom.currentNode && wfFrom.currentNode.selWfNodeSettingStr) {
        const strnodeArr = wfFrom.currentNode.selWfNodeSettingStr.split(',')
        for (let i = 0; i < strnodeArr.length; i++) {
          this.checkedWfNodeList.push(strnodeArr[i])
        }
      }
      if (wfFrom.currentNode && wfFrom.currentNode.selectRecAcctTypeOptions) {
        const strAcctNodeArr = wfFrom.currentNode.selectRecAcctTypeOptions.split(',')
        for (let i = 0; i < strAcctNodeArr.length; i++) {
          this.checkedRecAcctTypeOptions.push(strAcctNodeArr[i])
        }
      }
      if (wfFrom.currentNode && wfFrom.currentNode.auxiliaryAuditRuleId) {
        const auditRuleNodeArr = wfFrom.currentNode.auxiliaryAuditRuleId.split(',')
        for (let i = 0; i < auditRuleNodeArr.length; i++) {
          this.checkAuxiliaryAuditRuleId.push(auditRuleNodeArr[i])
        }
      }
      if (wfFrom.currentNode && wfFrom.currentNode.aiAuditRuleId) {
        const aiAuditRuleNodeArr = wfFrom.currentNode.aiAuditRuleId.split(',')
        for (let i = 0; i < aiAuditRuleNodeArr.length; i++) {
          this.checkAiAuditRuleId.push(aiAuditRuleNodeArr[i])
        }
      }
      if (wfFrom.currentNode && wfFrom.currentNode.auditRuleFilesId) {
        const auditRuleFiles = wfFrom.currentNode.auditRuleFilesId.split(',')
        for (let i = 0; i < auditRuleFiles.length; i++) {
          this.checkAuditRuleFiles.push(auditRuleFiles[i])
        }
      }
      // 处理特殊审核界面
      this.auditScreenMetaId = wfFrom.currentNode.auditScreenMetaId
      this.auditScreenMetaName = wfFrom.currentNode.auditScreenMetaName
      this.handleCheckedCSChange()
    },
    handleCheckedCSChange() {
      this.isShowRecAcctTypeOption = this.$isNotEmpty(this.checkedWfNodeList.filter(item => item === '必须上传附件'))
      this.isShowNodSameUser = this.$isNotEmpty(this.checkedWfNodeList.filter(item => item === '节点同一审核人'))
      this.isShowAppointUser = this.$isNotEmpty(this.checkedWfNodeList.filter(item => item === '指定审核人'))
      this.isShowNotice = this.$isNotEmpty(this.checkedWfNodeList.filter(item => item === '是否启用审核通知'))
      // 节点退回再提交设置
      this.isShowNodeBackSetting = this.$isNotEmpty(this.checkedWfNodeList.filter(item => item === '退回再提交设置'))
      this.isShowNodeTimeOutSetting = this.$isNotEmpty(this.checkedWfNodeList.filter(item => item === '超时配置'))
      this.isShowAuditConditions = this.$isNotEmpty(this.checkedWfNodeList.filter(item => item === '审核补充条件'))
      this.isShowEnableABRoleExamine = this.$isNotEmpty(this.checkedWfNodeList.filter(item => item === '启用AB角审核'))
    },
    loadNodeBackSetting() {
      this.checkedNodeBackOptions = []
      if (this.isShowNodeBackSetting) {
        this.$set(this.loadingMap, 'backSetting', true)
        this.$callApiParams('selectNodeBackSetting', {
          metaId: this.wfMetaId, nodeName: this.wfNodeName
        }, result => {
          if (result.success && this.$isNotEmpty(result.data)) {
            var typeNames = result.data.typeName.split(',')
            for (let i = 0; i < typeNames.length; i++) {
              this.checkedNodeBackOptions.push(typeNames[i])
            }
          }
          this.$set(this.loadingMap, 'backSetting', false)
          return true
        }, () => {
          this.$set(this.loadingMap, 'backSetting', false)
        })
      }
    },
    selectWfOptionList() { // 查询表单选项设置列表
      this.$set(this.loadingMap, 'optionList', true)
      this.$callApiParams('selectDictResultList', { tparentCode: '10022' },
        result => {
          if (result.success) {
            this.wfNodeOptionList = result.data
          }
          this.$set(this.loadingMap, 'optionList', false)
          return true
        }, () => {
          this.$set(this.loadingMap, 'optionList', false)
        })
    },
    loadNodeTimeOutSetting() {
      if (!this.wfNodeName) return
      if (this.isShowNodeTimeOutSetting) {
        this.$set(this.loadingMap, 'timeOutSetting', true)
        this.$callApiParams('selectNodeAuditTimeOutSetting', {
          metaId: this.wfMetaId,
          nodeName: this.wfNodeName
        }, result => {
          if (result.success) {
            const { limitAuditDate, isAutoAudit, sendMessageType } = result.data.auditNodeTimeOutEntity || {}
            this.nodeLimitAuditDate = limitAuditDate
            this.nodeIsAutoAudit = isAutoAudit
            let sendMessageTypeTmp = []
            if ((!Array.isArray(sendMessageType)) && sendMessageType) {
              sendMessageTypeTmp = [sendMessageType]
            }
            this.nodeSendMessageType = sendMessageTypeTmp
          }
          this.$set(this.loadingMap, 'timeOutSetting', false)
          return true
        }, () => {
          this.$set(this.loadingMap, 'timeOutSetting', false)
        })
      } else {
        this.nodeLimitAuditDate = ''
        this.nodeIsAutoAudit = ''
        this.nodeSendMessageType = []
      }
    },
    loadAuditConditions() { // 查询审核补充条件
      if (!this.wfNodeName) return
      this.tableListSettingColAll = []
      if (this.isShowAuditConditions) {
        this.$set(this.loadingMap, 'auditConditions', true)
        this.$callApiParams('selectAuditCondition', { metaId: this.wfMetaId,
          nodeName: this.wfNodeName }, result => {
          if (result.success) {
            const conditionList = result.data.conditionList
            if (!this.$isEmpty(conditionList)) {
              conditionList.forEach(item => {
                this.colItem = {
                  orderNumber: item.orderNumber,
                  metaId: item.metaId,
                  nodeName: item.nodeName,
                  elementType: item.elementType,
                  elementName: item.elementName,
                  referData: item.referData
                }
                this.tableListSettingColAll.push(this.colItem)
              })
            }
          }
          this.$set(this.loadingMap, 'auditConditions', false)
          return true
        }, () => {
          this.$set(this.loadingMap, 'auditConditions', false)
        })
      }
    },
    // 查找数据列基础数据信息
    selectColBaseData() {
      this.$callApiParams('selectColBaseData', {},
        result => {
          this.dataRefDrop = result.data.dataRefDrop
          return true
        })
    },
    addRows() { // 审核补充条件新增行
      this.colItem = {
        orderNumber: this.orderNumber + 1,
        metaId: this.wfMetaId,
        nodeName: this.wfNodeName,
        elementType: '',
        elementName: '',
        referData: ''
      }
      this.tableListSettingColAll.push(this.colItem)
    },
    delRows() { // 审核补充条件删除行
      if (this.$isEmpty(this.tableListDataShow)) {
        this.$message.error('请先选择要删除的审核条件数据！')
        return
      }
      this.deleteTableDta = this.getDeleteTableData() // 后端删除
      this.deleteRowTableData = this.tableListDataShow.filter((item) => item.id == null) // 前端删除
      if (this.$isNotEmpty(this.deleteRowTableData)) {
        this.deleteRowId = this.deleteRowTableData.map((item) => item.orderNumber)
        this.deleteRowId.forEach((items, index, arr) => {
          this.tableListSettingColAll = this.tableListSettingColAll.filter((item, index) => item.orderNumber !== items)
        })
      }
    },
    colTypeChange(scope) {
      if (scope.row.elementType === '单选') {
        this.isOptionVisible = false
        const tableListTemp = this.tableListSettingColAll.filter(item => item.orderNumber !== scope.row.orderNumber)
        tableListTemp.push({
          orderNumber: scope.row.orderNumber,
          metaId: this.wfMetaId,
          nodeName: this.wfNodeName,
          elementType: scope.row.elementType,
          elementName: '',
          referData: ''
        })
        this.tableListSettingColAll = tableListTemp
      }
    },
    getDeleteTableData() {
      return this.tableListDataShow.filter((item) => item.id != null)
    },
    tableListSettingColChange(checkedRows) {
      this.tableListDataShow = checkedRows
    },
    tableListSettingColDblclick(row, column, event) { // 双击表格列表行，则删除该表格列
      const $table = { selection: [{ id: row.id }] } // 构造模拟的table
      this.deleteColItems($table)
    },
    tableListSettingColClick(row) {
      this.$refs.auditConsTable.toggleRowSelection(row)
    },
    selectAttTypeList() { // 查询附件类型列表
      this.$set(this.loadingMap, 'attTypeList', true)
      this.$callApi('getAttTypeTableName&formType=' + this.formName, {}, result => {
        if (result.success) {
          this.attTypeTableName = result.data
          if (this.$isNotEmpty(this.attTypeTableName)) {
            this.$callApi('getAccSetEle&tableName=' + this.attTypeTableName, {}, result => {
              if (result.success) {
                this.recAcctTypeOptions = result.data
              }
              return true
            })
          }
        }
        this.$set(this.loadingMap, 'attTypeList', false)
        return true
      }, () => {
        this.$set(this.loadingMap, 'attTypeList', false)
      })
    },
    selectFromNode() { // 查询当前节点前面的节点
      if (!this.wfNodeName) return
      this.$set(this.loadingMap, 'fromNode', true)
      this.$callApiParams('loadFrontNode', { metaId: this.wfMetaId,
        nodeName: this.wfNodeName }, result => {
        if (result.success) {
          this.NodSameUsers = result.data
          for (var i = 0; i<this.NodSameUsers.length; i++) {
            if (this.NodSameUsers[i].isCheck) {
              this.checkedNodSameUsers = this.NodSameUsers[i].nodeName
            }
          }
        }
        this.$set(this.loadingMap, 'fromNode', false)
        return true
      }, () => {
        this.$set(this.loadingMap, 'fromNode', false)
      })
    },
    loadAppointUser() {
      if (!this.wfNodeName) return
      this.$set(this.loadingMap, 'appointUser', true)
      this.$callApiParams('loadAppointUser', { metaId: this.wfMetaId,
        nodeName: this.wfNodeName }, result => {
        if (result.success) {
          this.appointUser = result.data
        }
        this.$set(this.loadingMap, 'appointUser', false)
        return true
      }, () => {
        this.$set(this.loadingMap, 'appointUser', false)
      })
    },
    loadNotice() {
      if (!this.wfNodeName) return
      this.$set(this.loadingMap, 'notice', true)
      this.$callApiParams('selectNotice', { metaId: this.wfMetaId,
        nodeName: this.wfNodeName }, result => {
        if (result.success) {
          this.noticeVo = result.data
          if (this.noticeVo.noticeEntities[this.currAction]) {
            this.noticeEntity = this.noticeVo.noticeEntities[this.currAction]
          } else {
            this.noticeEntity = { action: name, noticeType: '', noticeTypeList: [], content: '', noticeUserList: [],
              noticeUser: '', isEnable: '禁用' }
          }
        }
        this.$set(this.loadingMap, 'notice', false)
        return true
      }, () => {
        this.$set(this.loadingMap, 'notice', false)
      })
    },
    loadAuditRule() { // 加载审核规则
      this.$set(this.loadingMap, 'auditRule', true)
      this.$callApiParams('selectRuleList', { metaId: this.wfMetaId,
        nodeName: this.wfNodeName }, result => {
        if (result.success) {
          const ruleList = result.data
          this.auxiliaryAuditRuleId = ruleList.auxiliaryAuditList
          this.aiAuditRuleId = ruleList.aiAuditList
          this.auditRuleFilesId = ruleList.auditFilesList
        }
        this.$set(this.loadingMap, 'auditRule', false)
        return true
      }, () => {
        this.$set(this.loadingMap, 'auditRule', false)
      })
    },
    selectExamineAssigner() { // AB角审核 查询过滤经办人的角色
      this.$set(this.loadingMap, 'examineAssigner', true)
      this.$callApiParams('selectAssignerList',
        {
          wfMetaId: this.wfMetaId,
          nodeName: this.wfNodeName
        },
        result => {
          if (result.success && result.data) {
            this.roleList = result.data.options
            if (result.data.checked) {
              this.checkedAssigner = result.data.checked
            }
          }
          this.$set(this.loadingMap, 'examineAssigner', false)
          return true
        }, () => {
          this.$set(this.loadingMap, 'examineAssigner', false)
        })
    },
    inputNumbersFormat(value) {
      // 只能输入数字
      const newValue = value.replace(/[^\d]/g, '')
      this.nodeLimitAuditDate = newValue
    },
    actionChange(name) {
      this.noticeEntity.noticeUser = ''
      if (this.noticeEntity.noticeUserList.length>0) {
        for (var i = 0; i < this.noticeEntity.noticeUserList.length; i++) {
          this.noticeEntity.noticeUser += this.noticeEntity.noticeUserList[i] + ','
        }
      }
      this.noticeEntity.noticeType = ''
      if (this.noticeEntity.noticeTypeList.length>0) {
        // eslint-disable-next-line no-redeclare
        for (var i = 0; i < this.noticeEntity.noticeTypeList.length; i++) {
          this.noticeEntity.noticeType += this.noticeEntity.noticeTypeList[i] + ','
        }
      }
      this.noticeEntity.metaId = this.wfMetaId
      this.noticeEntity.nodeName = this.wfNodeName
      if (this.action) {
        this.noticeVo.noticeEntities[this.action] = this.$clone(this.noticeEntity)
      }
      if (this.noticeVo.noticeEntities[name]) {
        this.noticeEntity = this.noticeVo.noticeEntities[name]
      } else {
        this.noticeEntity = { action: name, noticeType: '', noticeTypeList: [], content: '', noticeUserList: [],
          noticeUser: '', isEnable: '禁用' }
      }
      this.action = name
    },
    btRelatedAuditScreen(event) {
      if (event?.target?.classList?.contains?.('el-input__icon')) {
        return
      }

      this.checkedValues = []
      const screenMetaId = this.auditScreenMetaId
      if (this.$isNotEmpty(screenMetaId)) {
        // 获取可以绑定的表单数据
        const treeObjData = this.treeData
        if (treeObjData) {
          treeObjData.filter(item => {
            const itemKey = item.itemKey
            const id = item.id
            if (itemKey === screenMetaId) {
              this.checkedValues.push(id)
            }
          })
        }
      }
      this.auditScreenDlgVisible = true
    },
    auditScreenClear() {
      this.auditScreenMetaName = ''
      this.auditScreenMetaId = ''
    },
    selectRelatedAuditScreen() {
      const nodes = this.$refs.supTree.treeObj.getSelectedNodes()
      if (this.$isEmpty(nodes)) {
        this.auditScreenMetaName = ''
        this.auditScreenMetaId = ''
      } else {
        if (nodes.length > 1) {
          this.$message.warning('特殊界面无法绑定两个表单!')
          return
        } else {
          if (this.$isEmpty(nodes[0].itemKey)) {
            this.$message.warning('请选择特殊审核表单!')
            return
          }
          this.auditScreenMetaId = nodes[0].itemKey
          this.auditScreenMetaName = nodes[0].label
        }
      }
      this.auditScreenDlgVisible = false
    },
    selectClassifyList() {
      this.$callApiParams('selectClassifyList',
        { dataType: this.dataType }, result => {
          this.treeData = result.data
          this.treeData.forEach(node => {
            if (this.isTempateNode(node)) {
              // 数据模板节点显示为树的叶子节点
              delete node.children
            }
          })
          return true
        })
    },
    isTempateNode(treeNode) {
      return (treeNode &&
        treeNode.exData &&
        treeNode.exData.isTemplate)
    },
    // 表单点击发生改变事件
    auditScreenClick(event, treeId, treeNode) {
      this.auditScreenMetaName = treeNode.label
      this.auditScreenMetaId = treeNode.itemKey
    },
    save() {
      var selWfNodeSetting = ''
      this.checkedWfNodeList.forEach(item => {
        selWfNodeSetting = selWfNodeSetting + item + ','
      })
      if (selWfNodeSetting) {
        selWfNodeSetting = selWfNodeSetting.substring(0, selWfNodeSetting.length - 1)
      }

      var selectRecAcctTypeOptions = ''
      this.checkedRecAcctTypeOptions.forEach(item => {
        selectRecAcctTypeOptions = selectRecAcctTypeOptions + item + ','
      })
      if (selectRecAcctTypeOptions) {
        selectRecAcctTypeOptions = selectRecAcctTypeOptions.substring(0, selectRecAcctTypeOptions.length - 1)
      }

      // 节点再提交设置
      var selectNodeBackTypeOptions = ''
      this.checkedNodeBackOptions.forEach(item => {
        selectNodeBackTypeOptions = selectNodeBackTypeOptions + item + ','
      })
      if (selectNodeBackTypeOptions) {
        selectNodeBackTypeOptions = selectNodeBackTypeOptions.substring(0, selectNodeBackTypeOptions.length - 1)
      }

      if ((!this.$isNotEmpty(selWfNodeSetting)) || (selWfNodeSetting && !selWfNodeSetting.indexOf('必须上传附件') < 0)) {
        selectRecAcctTypeOptions = ''
        this.checkedRecAcctTypeOptions = []
      }

      // 辅助审核
      var auxiliaryAuditRuleId = ''
      this.checkAuxiliaryAuditRuleId.forEach(item => {
        auxiliaryAuditRuleId = auxiliaryAuditRuleId + item + ','
      })
      if (auxiliaryAuditRuleId) {
        auxiliaryAuditRuleId = auxiliaryAuditRuleId.substring(0, auxiliaryAuditRuleId.length - 1)
      }
      // 智能辅助
      var aiAuditRuleIds = ''
      this.checkAiAuditRuleId.forEach(item => {
        aiAuditRuleIds = aiAuditRuleIds + item + ','
      })
      if (aiAuditRuleIds) {
        aiAuditRuleIds = aiAuditRuleIds.substring(0, aiAuditRuleIds.length - 1)
      }
      // 政策文件
      var auditRuleFiles = ''
      this.checkAuditRuleFiles.forEach(item => {
        auditRuleFiles = auditRuleFiles + item + ','
      })
      if (auditRuleFiles) {
        auditRuleFiles = auditRuleFiles.substring(0, auditRuleFiles.length - 1)
      }

      var formNode = ''
      if (this.checkedNodSameUsers.length>0) {
        formNode = this.checkedNodSameUsers
      }

      var checkedAssigner = ''
      if (this.checkedAssigner) {
        checkedAssigner = this.checkedAssigner
      }

      this.noticeEntity.metaId = this.wfMetaId
      this.noticeEntity.nodeName = this.wfNodeName
      this.noticeVo.noticeEntities[this.action] = this.$clone(this.noticeEntity)
      this.auditConditionVo.conditionList = this.tableListSettingColAll.length>0
        ? this.tableListSettingColAll : []
      return {
        noticeVo: this.noticeVo,
        selWfNodeSetting,
        selectRecAcctTypeOptions,
        appointUser: this.appointUser,
        formNode,
        selectNodeBackTypeOptions,
        nodeLimitAuditDate: this.nodeLimitAuditDate,
        nodeSendMessageType: this.nodeSendMessageType,
        nodeIsAutoAudit: this.nodeIsAutoAudit,
        // 智能审核
        auxiliaryAuditRuleId,
        aiAuditRuleIds,
        auditRuleFiles,
        checkedAssigner,
        // 审核补充条件
        auditConditionVo: this.auditConditionVo,
        auditScreenMetaName: this.auditScreenMetaName,
        auditScreenMetaId: this.auditScreenMetaId
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#isShowNotice, #aiAudit {
  border: 1px solid #bbb;
  padding: 5px;
  margin-top: 5px
}
#wfNodeOption{
  border: 1px solid #bbb;
  padding: 5px;
}
#recNodSameUser{
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}

#nodeTimeOut{
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}
#auditConditions{
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
  .el-table ::v-deep th.el-table__cell > .cell {
    font-size: 14px;
    font-family: "Lato", sans-serif !important;
    color: #434343;
    font-weight: 400;
  }
}
#recAcctTypeOption{
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}

#isNodeBackSetting{
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}
.option-canvas{
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}
::v-deep .el-checkbox__label {
  width: 118px;
  //overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

#enableABRoleExamine{
  border: 1px solid #bbb;
  margin-top: 7px;
  padding: 5px;
}
</style>
