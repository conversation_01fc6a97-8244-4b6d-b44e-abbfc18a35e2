<template>
  <div id="wf-evaluate-tab" class="flex-column" v-loading="loading">
    <div @click.stop="openEvaluate" class="evaluateWarp">
      <el-row type="flex">
        <el-col :span="22">
          <span class="el-icon-chat-dot-round">评论信息</span>
        </el-col>
        <el-col v-if="!isDetail" :span="1">
          <span class="el-icon-plus iconAddWrap"></span>
        </el-col>
      </el-row>
    </div>
    <el-timeline style="flex: 1;overflow: auto;">
      <div class="block">
        <el-timeline class="timeLineWarp"><!--reverse倒序-->
          <el-timeline-item class="timeLineItemWarp"
                            v-for="(evaluate, index) in evaluates"
                            :key="index"
                            :icon="evaluate.icon"
                            :type="evaluate.type"
                            :color="evaluate.color"
                            :size="evaluate.size"
                            :timestamp="evaluate.timestamp"
                            :placement="evaluate.placement">
            <el-card>

              <el-tooltip class="item" effect="dark" :content="'该评论@用户：'+`${evaluate.noticeUsersNames}`"
                          placement="top">
                <h4 class="evaluateContent_H">{{evaluate.evaluateContent_H4}}</h4>
              </el-tooltip>
              <p class="evaluateContent_P">{{evaluate.evaluateContent_P}}</p>
              <el-row type="flex" @click="removeEvaluate" class="removeWrap" v-if="evaluate.canDelete === '是'">
                <el-col :span="23"></el-col>
                <el-col v-if="!isDetail" :span="1">
                  <span class="el-icon-delete iconRemoveWrap" @click="removeEvaluate(evaluate.evaluateId)"></span>
                </el-col>
              </el-row>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-timeline>
    <!--添加评论弹窗dialog-->
    <el-dialog
      title="评论"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="40%"
      :before-close="close">
        <span>
          <el-input type="textarea"
                    class="evaluateInputWarp"
                    resize="none"
                    cols="99%"
                    rows="10"
                    maxlength="200"
                    show-word-limit
                    v-model="evaluateContent"
                    placeholder="请输入评论">
          </el-input>
        </span>
      <el-button type="danger" icon="el-icon-s-custom" round @click="addNoticeUser" class="userBtn">
        @用户+
      </el-button>
      <el-row type="flex" class="userWarp">
        已选中的@用户：
        <el-col :gutter="1" :grabbed="1" v-for="(user, index) in selectedUser" :key="index">{{user.name}}</el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="addEvaluate">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'wf-evaluate-tab',
  props: {
    billId: {
      type: String,
      default: ''
    },
    getAuditTable: {
      type: Function,
      default: () => {}
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: true,
      evaluates: [], // 评论集合
      dialogVisible: false, // 是否显示对话框
      evaluateContent: '', // 评论内容
      selectedUser: [], // 选择@的用户集合
      saveFormEvaluateData: undefined // 这部分参数用于从评论列表页面传入数据后使用
    }
  },
  watch: {
    billId: {
      handler() {
        if (this.billId) {
          this.loading = true
          this.init()
        }
      },
      immediate: true
    }
  },
  mounted() {
    // this.init()
  },
  methods: {
    init() {
      // 根据当前主业务id获取评论信息
      this.$callApiParams('getWfEvaluates', { ids: this.billId }, (result) => {
        this.evaluates = result.data
        this.loading = false
        return true
      })
    },
    init4EvaluateData(id, nodeName) {
      // 根据当前主业务id获取评论信息
      this.$callApiParams('getWfEvaluates', { ids: id }, (result) => {
        this.evaluates = result.data
        this.loading = false
        return true
      })
      this.saveFormEvaluateData = {
        ID: id,
        nodeName: nodeName
      }
    },
    /**
     * 关闭dialog弹窗
     */
    close() {
      this.evaluateContent = ''
      this.dialogVisible = false
      this.selectedUser = []
    },
    /**
     * 打开选择人员弹窗
     */
    addNoticeUser() {
      const refData = {
        colType: '弹框',
        dataRef: '选择系统人员'
      }
      this.$refData(undefined,
        refData,
        () => {
        },
        () => {
        },
        selectedData => {
          this.selectedUser = []
          if (selectedData.hasOwnProperty('list') && selectedData.list.length > 0) {
            selectedData.list.forEach(data => {
              const obj = {}
              obj.id = data.id
              obj.code = data.itemKey
              obj.name = data.label
              this.selectedUser.push(obj)
            })
          }
        },
        () => {
        },
        { multiple: true })// multiple：true多选
    },
    /**
     * 打开评论dialog
     */
    openEvaluate() {
      if (this.isDetail) {
        return
      }
      this.dialogVisible = true
    },
    /**
     * 保存评论信息
     */
    addEvaluate() {
      let params = this.getNodeMessage()
      params = {
        ...params,
        evaluateContent: this.evaluateContent,
        userJson: JSON.stringify(this.selectedUser)
      }
      this.$callApi('saveWfEvaluate', params,
        () => {
          this.close()
          this.init()
        },
        () => {
        })
    },
    /**
     * 删除评论
     * @param id 评论信息id
     */
    removeEvaluate(id) {
      this.$callApiParams('deleteEvaluate', { ids: id },
        () => {
          this.init()
        },
        () => {
        })
    },
    /**
     * 获取父级组件信息
     * @returns {string|{}}
     */
    getNodeMessage() {
      const checked = this.$getTableSelection(this.getAuditTable())
      let node = {}
      if (checked && checked.length > 0) {
        const checkSelection = checked[0]
        if (checkSelection) {
          node = {
            ...node,
            bizid: checkSelection.id, // 业务主id
            nodeName: checkSelection['当前节点'] // 当前节点名称
          }
        }
        return node
      } else if (this.$isNotEmpty(this.saveFormEvaluateData)) {
        node = {
          ...node,
          bizid: this.saveFormEvaluateData.ID,
          nodeName: this.saveFormEvaluateData.nodeName
        }
        return node
      }
      return ''
    }
  }
}
</script>

<style lang="scss">
  #wf-evaluate-tab {
    overflow: hidden;
    height: 100%;
    ::v-deep .el-tabs__header {
      margin: -5px 0 10px 0 !important;
    }

    .evaluateInputWarp {
      font-size: 16px;
    }

    .evaluateWarp {
      color: #db800f;
      margin-bottom: 10px;
      cursor: pointer;
      font-size: 18px;
    }

    .timeLineWarp {
      margin-bottom: 5px;
      margin-left: 10px;

      .timeLineItemWarp {
        color: #db800f;

        .el-card {
          cursor: auto;
        }
      }

      .evaluateContent_P {
        text-align: right;
        font-size: 12px;
        color: #5a6171;
      }

      .evaluateContent_H {
        font-size: 16px;
      }

      .removeWrap {
        color: #db1515;

        .iconRemoveWrap {
          font-size: 16px;
          cursor: pointer;
        }
      }
    }

    .iconAddWrap {
      font-weight: 600;
    }

    .el-card {
      .el-card__body {
        padding: 10px;
      }
    }
  }

  .userWarp {
    flex-wrap: wrap;
    font-size: 16px;

    .el-col {
      width: fit-content;
      color: #409EFF;
      margin-right: 10px;

    }
  }

  .userBtn {
    margin: 5px 0 !important;
  }
</style>
