<template>
  <div id="wfusefuldialog">
    <el-dialog
      append-to-body
      :title='reltitle'
      :visible.sync="isdialog"
      width="25%"
      :close-on-click-modal='false'
      @close="handleClose">
      <page>
        <template #pageContent>
        <el-form
          ref="wfUsefulForm"
          :model="wfUsefulForm"
          label-width="100px"
          :disabled="wfUsefulDetails"
          :rules="rules"
          style="border: 1px solid #ccc;padding-top: 20px;">
            <el-row class="row-marginBottom">
              <el-col :span="22">
                <el-form-item label="排序" prop="usefulOrder">
                  <el-input v-model="wfUsefulForm.usefulOrder" placeholder="请输入排序"  @input="(val)=>veInputSupdepCode(val)" maxlength="2"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          <el-row class="row-marginBottom" v-if="!isUsefulNodePage">
            <el-col :span="22">
              <el-form-item label="表达式" prop="expression">
                <el-input v-model="wfUsefulForm.expression" placeholder="请输入表达式" maxlength="45" ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-marginBottom">
            <el-col :span="22">
              <el-form-item label="常用语" prop="usefulText">
                <el-input v-model="wfUsefulForm.usefulText" placeholder="请输入常用语" maxlength="156" ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        </template>
      </page>
      <template #footer>
        <el-button
          class="btn-normal"
          type="primary"
          @click="handleSumbit('wfUsefulForm')"
          v-if="!wfUsefulDetails"
        >
          保存
        </el-button>
        <el-button class="btn-normal" @click="handleClose('wfUsefulForm')" v-if="!wfUsefulDetails"> 取消</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
export default {
  name: 'wfusefuldialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    },
    isUsefulNodePage: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.wfUsefulDetails = bool
    }
  },
  computed: {
    startPickerOptions() {
      return {
        disabledDate: time => time.getTime() < Date.now() - 86400000
      }
    },
    endPickerOptions() {
      return {
        disabledDate: time => (this.wfUsefulForm.startDate ? time.getTime() <
          new Date(this.wfUsefulForm.startDate).getTime() : time.getTime() > Date.now())
      }
    }
  },
  data() {
    return {
      btType: '新增',
      reltitle: '新增常用语',
      rowsize: 0,
      startDate: null,
      endDate: null,
      isdialog: this.dialog,
      wfUsefulDetails: this.isDetails,
      wfUsefulForm: {
        id: null,
        bizid: null,
        metaId: '',
        metaVersionId: '',
        nodeId: '',
        nodeName: '',
        usefulOrder: '',
        usefulText: '',
        expression: '',
        state: true,
        usefulType: '02',
        userId: ''
      },
      users: [],
      rules: {
        usefulOrder: [
          { required: true, message: '请输入排序', trigger: 'change' }
        ],
        usefulText: [
          { required: true, message: '请输入常用语', trigger: 'change' },
          { validator: this.validateUsefulText, trigger: 'blur' }
        ],
        expression: [
          { required: !this.isUsefulNodePage, message: '请输入表达式', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init(btType, opinion, isUsefulPage, rowsize) {
      this.btType = btType
      this.isUsefulNodePage = isUsefulPage
      if (rowsize) {
        this.rowsize = rowsize
      } else {
        this.rowsize = 0
      }
      this.dialog = true
      if (this.btType === '新增') {
        this.reltitle = '新增常用语'
        this.initdata()
      } else {
        this.reltitle = '编辑常用语'
      }
      if (opinion) {
        this.wfUsefulForm.usefulText = opinion
      }
    },
    veInputSupdepCode(value) {
      // 只能输入数字
      const newValue = value.replace(/[^\d]/g, '')
      this.wfUsefulForm.usefulOrder = newValue
    },
    validateUsefulText(rule, value, callback) {
      if (value.trim() === '') {
        callback(new Error('常用语不能为空'))
      } else {
        callback()
      }
    },
    initdata() {
      this.wfUsefulForm = {
        id: null,
        bizid: null,
        metaId: '',
        metaVersionId: '',
        nodeId: '',
        nodeName: '',
        usefulOrder: this.rowsize,
        usefulText: '',
        expression: '',
        state: true,
        usefulType: '02',
        userId: ''
      }
    },
    // 检查是否为空
    isNull(value) {
      if (value) {
        return false
      }
      return true
    },
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('handleAdd', this.wfUsefulForm)
        } else {
          return false
        }
      })
    },
    resetForm() {
      if (this.$refs['wfUsefulForm']) { // 清空form数据
        this.$nextTick(() => {
          this.$refs['wfUsefulForm'].clearValidate()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
      margin-bottom: 10px;
  }
  ::v-deep .el-table {
    height: 100%;
  }
</style>
<style lang="scss">
    .row-marginBottom {
      margin-bottom: 10px;
    }
    .ba-payee-list {
      height: 250px !important;
    }
</style>
