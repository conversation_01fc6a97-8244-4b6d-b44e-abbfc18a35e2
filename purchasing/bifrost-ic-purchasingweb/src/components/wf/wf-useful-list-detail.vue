<template>
  <el-dialog
        ref="modifyWfUsefulDataDlg"
        class="modifyWfUsefulDataDlg"
        :title="'设置常用语'"
        :visible.sync="isUseFulVisible"
        :close-on-click-modal='false'
        append-to-body>
    <b-curd ref="curdListWfUseful"/>
    <div slot="footer" class="dialog-footer" style="padding-top:20px">
      <el-button class="btn-normal" type="primary" @click="selectUseful">确认</el-button>
      <el-button class="btn-normal" @click="isUseFulVisible = false">取消</el-button>
    </div>
    <wfusefuldialog ref="wfusefuldialog" :dialog.sync="isDialog" @handleAdd='handleAdd'></wfusefuldialog>
  </el-dialog>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'wf-useful-list-detail',
  data() {
    return {
      ids: '',
      isUseFulVisible: false,
      baseListMain: undefined,
      mainBaseListInitParams: null,
      payeeId: '',
      rowsData: [],
      currentNode: '',
      isUsefulPage: false,
      isUsefulUser: false,
      needNodeNameFilter: false,
      isDialog: false,
      node: {
        usefuls: [],
        name: '',
        bizid: '',
        metaId: ''
      },
      opinion: ''
    }
  },
  methods: {
    initMeta(currentNode, node, isShowAc, opinion) {
      this.currentNode = currentNode
      this.node = node
      this.isUseFulVisible = true
      this.isShowAc = isShowAc
      this.opinion = opinion
      this.init()
    },
    init() {
      this.$nextTick(() => {
        this.$setDlgSize(this, 'modifyWfUsefulDataDlg', 800, 650)
        $('.modifyWfUsefulDataDlg .el-dialog__body').css(
          'cssText', 'height: calc(100% - 132px) !important; padding: 0px 20px;')
        $('.modifyWfUsefulDataDlg .el-dialog__footer').css(
          'cssText', 'padding: 0px 20px 20px;')
        this.$refs.curdListWfUseful.init({
          showPager: false,
          hideCurdButton: ['详情'],
          buttons: [],
          params: {
            dataApiKey: 'getCurrentNodeWfNodeUsefuls',
            metaId: this.node.metaId,
            nodeName: this.currentNode,
            isUsefulPage: this.isUsefulPage,
            needNodeNameFilter: this.needNodeNameFilter
          },
          reloadTableCallback: result => {
            this.rowsData = result.data.rows
            this.node.usefuls = result.data.rows
            if (this.opinion) {
              this.isDialog = true
              this.$refs.wfusefuldialog.init('新增', this.opinion, this.isUsefulPage, this.rowsData.length + 1)
            }
            this.$emit('usefulChange')
          },
          btAddClick: { click: bt => {
            this.isDialog = true
            this.$refs.wfusefuldialog.init('新增', this.opinion, this.isUsefulPage, this.rowsData.length + 1)
          } },
          btModifyClick: { click: bt => {
            this.isDialog = true
            this.$refs.wfusefuldialog.wfUsefulForm = {
              id: bt.id,
              bizid: bt.bizid,
              metaId: bt.metaId,
              metaVersionId: bt.metaVersionId,
              nodeId: bt.nodeId,
              nodeName: bt.nodeName,
              usefulOrder: bt.usefulOrder,
              usefulText: bt.usefulText,
              expression: bt.expression,
              state: bt.state,
              usefulType: bt.usefulType,
              userId: bt.userId
            }
            this.$refs.wfusefuldialog.init('编辑', this.opinion, this.isUsefulPage)
          } },
          btDeleteClick: { click: bt => {
            this.$confirm(`确认要删除选中的数据吗？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              var ids = ''
              bt.getRowIds().forEach(item => {
                ids = ids + item + ','
              })
              var params = { ids: ids }
              this.$callApiParams('deleteWfNodeUsefuls',
                params, result => {
                  this.init()
                })
            }).catch(() => {})
          } }
        })
      })
    },
    handleAdd(form) {
      const node = this.$clone(this.node)
      const connode = { ...node, usefuls: { ...node.usefuls }}
      const connodeusefuls = []
      node.usefuls.forEach((item) => connodeusefuls.push(item))
      if (!form.id) {
        form.id = new Date().getTime()
      }
      form.nodeName = this.currentNode
      var isUpdate = false
      connodeusefuls.forEach((item) => {
        if (item.id === form.id) {
          item.usefulOrder = form.usefulOrder
          item.usefulText = form.usefulText
          item.expression = form.expression
          isUpdate = true
        }
      })
      if (!isUpdate) {
        connodeusefuls.push(form)
      }
      connode.usefuls = connodeusefuls
      this.$callApi('saveWfNodeUsefuls',
        connode, result => {
          this.isDialog = false
          this.opinion = ''
          this.init()
        }, null, this)
    },
    setUseful(usefulText) {
      this.isUseFulVisible = false
      this.$emit('setUseful', usefulText)
    },
    selectUseful() {
      if (!this.isUsefulPage) {
        this.isUseFulVisible = false
        return true
      }
      var selection = this.$refs.curdListWfUseful.getTable().selection
      if (selection.length !== 1) {
        this.$message.error('请选中一个常用语')
        return false
      }
      this.setUseful(selection[0].usefulText)
    },
    getExParamsCallApiSave(data) {
      return `&isUsefulUser=${this.isUsefulUser}`
    },
    checkBeforeCallApiSave(data) {
      return true
    }
  }
}
</script>

<style lang="scss">
</style>
