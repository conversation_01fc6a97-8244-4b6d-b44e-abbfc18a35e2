<template>
  <!--<div id="bifrost_workflow" :style="{ backgroundImage:'url(' + wanggeImg + ')'}">-->
  <div id="bifrost_workflow">
    <!--<div :id="canvasId" tabIndex="0" v-drag=isDrag></div>-->
    <div :id="canvasId" style="position: relative;height: 100%;width: 100%;" tabIndex="0" v-drag="({ drag: (isDrag || canDrag), that })" @mousewheel="changeZoom($event)"></div>
  </div>
</template>

<style lang="scss" scoped>
  #bifrost_workflow {
    position: relative;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    border-width: 1px;
    overflow: hidden;
    border-color: rgb(255, 255, 255);
    border-style: solid;
    background-color: rgb(255, 255, 255);
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImdyaWQiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTSAwIDIwIEwgODAgMjAgTSAyMCAwIEwgMjAgODAgTSAwIDQwIEwgODAgNDAgTSA0MCAwIEwgNDAgODAgTSAwIDYwIEwgODAgNjAgTSA2MCAwIEwgNjAgODAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iI2QwZDBkMCIgb3BhY2l0eT0iMC4yIiBzdHJva2Utd2lkdGg9IjEiLz48cGF0aCBkPSJNIDgwIDAgTCAwIDAgMCA4MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZDBkMGQwIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=);
    background-position: -1px -1px;
  }
  #bifrost_workflow_canvas {
    position: relative;
    height: 100%;
    width: 100%;
  }
  #bifrost_workflow_canvas_view{
    position: relative;
    height: 100%;
    width: 100%;
  }
  .vue-bifrostIcApp .wf-title {
    position: absolute;
    top: 5px;
    left: 0px;
    right: 0px;
    text-align: center;
  }
  .vue-bifrostIcApp .wf-title-font {
    display:inline-block;
    font-weight:bold;
    color:#def;
    text-shadow:0 0 1px currentColor,-1px -1px 1px #000,0 -1px 1px #000,1px -1px 1px #000,1px 0 1px #000,1px 1px 1px #000,0 1px 1px #000,-1px 1px 1px #000,-1px 0 1px #000;
    -webkit-filter:url(#wf-title-font );
    filter:url(#wf-title-font );
    font-size: 28px;
    opacity: 0.5;
    z-index: 10;
  }
</style>

<script>
import './szjzwf.css'
import $ from 'jquery'
// import { callApi } from '@/api/common'
// import { jsPlumb } from 'jsplumb'
export default {
  name: 'wfcanvas',
  props: {
    // 是否可以拖动
    canDrag: { type: Boolean, default: false },
    readOnly: { type: Boolean, default: false },
    currentNodeId: { type: String, default: '' },
    auditNodeList: { type: Array, default: () => [] },
    auditNodeConectionList: { type: Array, default: () => [] },
    canvasId: { type: String, default: 'bifrost_workflow_canvas' } // 画布id，设计和预览做区分，防止冲突
  },
  directives: {
    drag: {
      inserted: function(el, binding) {
        if (binding.value.drag) {
          const that = binding.value.that
          const dragBox = el // 获取当前元素
          dragBox.onmousedown = e => {
            // 算出鼠标相对元素的位置
            var disX = e.clientX - dragBox.offsetLeft
            var disY = e.clientY - dragBox.offsetTop
            document.onmousemove = e => {
              // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
              const left = e.clientX - disX
              const top = e.clientY - disY
              disX = e.clientX
              disY = e.clientY
              var lis = dragBox.childNodes
              for (var i = 0; i < lis.length; i++) {
                const newChLeft = parseFloat(lis.item(i).style.left.replace('px', '')) + left
                const newChTop = parseFloat(lis.item(i).style.top.replace('px', '')) + top
                lis.item(i).style.left = newChLeft + 'px'
                lis.item(i).style.top = newChTop + 'px'
              }
              var lists = that.instance.getConnections()
              for (var j = 0; j < lists.length; j++) {
                lists[j].connector.x = lists[j].connector.x + left
                lists[j].connector.y = lists[j].connector.y + top
              }
            }
            document.onmouseup = e => {
              // 鼠标弹起来的时候不再移动
              document.onmousemove = null
              // 预防鼠标弹起来后还会循环（即预防鼠标放上去的时候还会移动）
              document.onmouseup = null
              var lists = that.instance.getConnections()
              for (var i = 0; i < lists.length; i++) {
                that.instance.repaintEverything(lists[i].canvas)
                lists[i]._listeners.mouseout = null
                lists[i]._listeners.mouseover = null
                lists[i].bind('mouseover', function() {
                  that.instance.repaintEverything(this.canvas)
                  this.setHover(true)
                })
                lists[i].bind('mouseout', function() {
                  that.instance.repaintEverything(this.canvas)
                  this.setHover(false)
                })
              }
            }
          }
        }
      }
    }
  },
  data() {
    return {
      that: this,
      instance: null,
      canvas: null,
      zoomNum: 1,
      anchors: [
        'Top', 'Left', 'Bottom', 'Right',
        'TopLeft', 'TopRight', 'BottomLeft', 'BottomRight'],
      /* wanggeImg: require('@/assets/wangge2.jpg') // 获取背景图片的方式*/
      isDrag: !this.readOnly
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initJsPlumb()
    })
  },
  methods: {
    initJsPlumb() { // 初始化
      var _this = this
      // eslint-disable-next-line no-undef
      jsPlumb.ready(function() {
        _this.canvas = document.getElementById(_this.canvasId)
        // eslint-disable-next-line no-undef
        _this.instance = jsPlumb.getInstance({
          Endpoint: ['Dot', { radius: 2 }],
          Connector: 'Straight',
          HoverPaintStyle: { stroke: '#1e8151', strokeWidth: 2 },
          ConnectionOverlays: [
            [
              'Arrow',
              { location: 1, id: 'arrow', width: 12, length: 14, foldback: 1 }
            ]
          ],
          Container: _this.canvasId
        })
        _this.instance.registerConnectionType('basic', {
          anchor: _this.anchors,
          connector: 'Straight',
          endpoint: 'Blank'
        })

        // 决定是否能执行连线
        _this.instance.bind('beforeDrop', function(info) {
          if (info.sourceId === info.targetId) { // 不允许自己连接自己
            return false
          }

          // 不允许连个节点间有多条连线
          var cnns = _this.instance.getConnections({
            source: info.sourceId,
            target: info.targetId
          })
          if (cnns.length > 0) {
            return false
          }
          cnns = _this.instance.getConnections({
            source: info.targetId,
            target: info.sourceId
          })
          if (cnns.length > 0) {
            return false
          }
          return true
        })

        _this.instance.bind('connection', function(info) { // 连线新建后
          _this.$emit('addConnection', info.connection)
        })

        _this.instance.bind('click', function(c) { // 单击连线
          if (!_this.readOnly) {
            _this.unSelectAll()
            _this.selectConnection(c)
            _this.$emit('setConnection', c.id)
          }
        })

        _this.instance.bind('dblclick', function(c) { // 连线双击不产生新的节点
          if (!_this.readOnly) {
            var event = window.event
            if (event) {
              event.isDbClickCnn = true
              event.stopPropagation()
            }
          }
        })

        // 双击增加节点，必须使用jsPlumb.on，否则双击连线也会产生新节点
        // eslint-disable-next-line no-undef
        jsPlumb.on(_this.canvas, 'dblclick', function(e) {
          if (!_this.readOnly) {
            if (e.isDbClickCnn) {
              // 双击连线不增加节点
              e.isDbClickCnn = undefined
              return
            }

            var node = _this.addNodeInner({ x: e.offsetX, y: e.offsetY })
            var $node = $(node)
            $node.attr('isNewNode', 'true')
            $node.click() // 变为选中节点
            $node.dblclick() // 变为编辑状态
          }
        })

        $(_this.canvas).click(function() {
          // 单击结束编辑
          _this.completeNodeEdit()
        })

        // 按键处理
        $(_this.canvas).keyup(function(e) {
          if (!_this.readOnly) {
            var isAltOrShift = (e.altKey || e.shiftKey)
            if (e.keyCode === 8 || e.keyCode === 46) { // Backspace和Delete按键
              _this.deleteSelectedItems()
            } else if (e.keyCode === 27) { // Esc按键
              _this.unSelectAll()
            } else if (isAltOrShift && e.keyCode === 90) { // ALT+Z或SHIFT+Z：撤销
              // _this.loadLastMeta()
            } else if (isAltOrShift && e.keyCode === 65) { // ALT+A或SHIFT+A：全选
              _this.selectAll()
            } else if (isAltOrShift && e.keyCode === 83) { // ALT+S或SHIFT+S：保存
              // _this.saveMeta()
            }
          }
        })
      })
    },

    changeZoom(e) { 
      // if (this.isDrag) return
      // 放大缩小
      const canvasDom = document.getElementById(this.canvasId)
      const p = ['webkit', 'moz', 'ms', 'o']
      const delta =
        (e.wheelDelta && (e.wheelDelta > 0 ? 1 : -1)) || // chrome & ie &其它
        (e.detail && (e.detail > 0 ? -1 : 1)) // firefox
      if (delta > 0) {
        // 向上滚
        for (let i = 0; i < p.length; i++) {
          canvasDom.style[p[i] + 'Transform'] = 'scale(' + this.zoomNum + ')'
        }
        canvasDom.style['transform'] = 'scale(' + this.zoomNum + ')'
        this.zoomNum += 0.1
      } else if (delta < 0) {
        // 向下滚
        if (this.zoomNum > 0.1) {
          this.zoomNum -= 0.1
          for (let i = 0; i < p.length; i++) {
            canvasDom.style[p[i] + 'Transform'] = 'scale(' + this.zoomNum + ')'
          }
          canvasDom.style['transform'] = 'scale(' + this.zoomNum + ')'
        }
      }
      return false
    },

    makeNewNodeName() {
      var $nodeNames = $(this.canvas).find('.w').find('span.node-name')
      var elements = {}
      $.each($nodeNames, function(i, v) {
        var $nodeName = $(this)
        elements[$nodeName.text()] = $nodeName
      })

      for (var i = 1; ; i++) { // 循环处理是避免之前删除节点后再增加，可能引发的重名
        var name = '节点' + i
        if (!elements[name]) {
          return name
        }
      }
    },

    makeNewNodeId() {
      var $nodes = $(this.canvas).find('.w')
      var elements = {}
      $.each($nodes, function(i, v) {
        var $node = $(this)
        elements[$node.attr('id')] = $node
      })

      for (var i = 1; ; i++) { // 避免重复
        var id = 'wfnode_' + this.getRandomStr()
        if (!elements[id]) {
          return id
        }
      }
    },

    getRandomStr(len) {
      len = len || 10
      // 默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1
      var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
      var maxPos = $chars.length
      var str = ''
      for (var i = 0; i < len; i++) {
        str += $chars.charAt(Math.floor(Math.random() * maxPos))
      }
      return str
    },

    addNode(item) { // 增加节点，提供给父组件操作使用：如按钮和双击增加节点
      if (!this.readOnly) {
        return this.addNodeInner(item)
      }
      return undefined
    },
    addNodeInner(item) { // 增加节点：节点的x，y必须指定，提供给子组件使用
      var node = document.createElement('div')
      item.id = this.makeNewNodeId()
      item.type = 1 // 流程节点
      if (item.name === undefined) {
        item.name = this.makeNewNodeName()
      }
      node.id = item.id
      node.className = 'w'

      node.innerHTML =
        "<span class='node-name'>" +
          item.name +
        '</span>' +
        "<div class='ep'></div>" +
        "<textarea  class='node-editor' maxlength='16' />"
      const leftx = item.x % 10
      const topx = item.y % 10
      node.style.left = (item.x - leftx) + 'px'
      node.style.top = (item.y - topx) + 'px'
      node.style.width = '115px'
      node.style.height = '50px'
      if (item.name.length > 8) {
        node.style.setProperty('padding-top', '7px')
      }
      node.style.setProperty('text-align', 'center')
      if (this.readOnly) {
        node.style.cursor = 'default'
        node.style.setProperty('border', '1px solid #AAB7C4', 'important')
        node.style.setProperty('background', '#F9FCFF', 'important')
        node.style.setProperty('color', '#434343', 'important')
        if (this.currentNodeId === item.bizid ||
                this.auditNodeList.indexOf(item.name) > -1) { // 如果当前环节为该流程节点 或者是审核过的节点
          if (this.currentNodeId === item.bizid) {
            node.style.setProperty('color', '#fff', 'important')
            node.style.setProperty('border', '1px solid #79C5FC', 'important')
            node.style.setProperty('background', '#79C5FC', 'important')
          } else {
            node.style.setProperty('color', '#fff', 'important')
            node.style.setProperty('border', '1px solid #3BB05F', 'important')
            node.style.setProperty('background', '#78CA90', 'important')
          }
        }
        $(node).find('.ep').removeClass('ep')
      }
      this.instance.getContainer().appendChild(node)
      if (!this.readOnly) { // 只读时不能拖动节点
        this.instance.draggable(node, {
          grid: [10, 10], // 对齐到网格，数值表示网格线距离
          containment: this.canvas // 节点的移动区域
        })
      }
      if (this.currentNodeId === '' && !this.$isNotEmpty(this.auditNodeList)) {
        this.instance.makeSource(node, {
          filter: '.ep',
          anchor: this.anchors,
          connectorStyle: {
            stroke: '#5c96bc',
            strokeWidth: 2,
            outlineStroke: 'transparent',
            outlineWidth: 4
          },
          connectionType: 'basic',
          extract: {
            action: 'the-action'
          },
          maxConnections: -1
        })
      }

      this.instance.makeTarget(node, {
        dropOptions: { hoverClass: 'dragHover' },
        anchor: this.anchors,
        allowLoopback: true
      })
      this.instance.fire('jsPlumbDemoNodeAdded', node)

      // 单击节点
      var _this = this
      $(node).click(function(e) {
        if (!_this.readOnly) {
          _this.completeNodeEdit() // 结束可能正在执行的编辑
          _this.unSelectAll()
          _this.selectNode($(this))
          $(this.canvas).focus() // 画布获得焦点
          e.stopPropagation()
          _this.$emit('setCurrentNode', node.id)
        }
      })

      $(node).mousedown(function(e) {
        if (!_this.readOnly) {
          var $item = $(this)
          var x = parseInt($item.css('left'), 10)
          var y = parseInt($item.css('top'), 10)
          $item.attr('mousedownX', x)
          $item.attr('mousedownY', y)
        }
      })
      $(node).mouseup(function(e) {
        if (!_this.readOnly) {
          var $item = $(this)
          var x = parseInt($item.css('left'), 10)
          var y = parseInt($item.css('top'), 10)
          var mousedownX = parseInt($item.attr('mousedownX'))
          var mousedownY = parseInt($item.attr('mousedownY'))
          if (x === mousedownX && y === mousedownY) {
            // 节点位置没有变动，删除缓存
          }
        }
      })

      // 双击节点进入编辑
      $(node).dblclick(function(e) {
        if (!_this.readOnly) {
          _this.completeNodeEdit()
          var $editor = $(this).find('.node-editor')
          var oldName = $(this).find('span.node-name').text()
          $editor.addClass('node-editor-editing')
          $editor.val(oldName)
          $editor.select()
          e.stopPropagation()
        }
      })

      // 节点编辑回车
      $(node).find('.node-editor').keyup(function(e) {
        if (!_this.readOnly) {
          if (e.keyCode === 13) {
            _this.completeNodeEdit()
          }
          e.stopPropagation()
        }
      })
      _this.$emit('addNode', item)
      return node
    },

    addConnectionName(connection) { // 增加连线名称节点
      var item = {}
      item.id = this.makeNewNodeId()
      item.type = 2 // 连线名称节点
      item.title = connection.title
      item.name = this.$isNotEmpty(connection.name)
        ? connection.name : this.splitExpression(connection.expression) // 将连线描述或连线条件充当连线描述节点名称
      item.connectionId = connection.id
      if (this.$isNotEmpty(connection.x) && this.$isNotEmpty(connection.y)) {
        item.x = connection.x
        item.y = connection.y
      } else {
        var sourceX // 开始节点x值
        var sourceY // 开始节点y值
        var targetX // 结束节点x值
        var targetY // 结束节点y值
        $('.w').each(function(index, item) {
          var $item = $(this)
          var id = $item.attr('id')
          if (id === connection.sourceId) {
            sourceX = parseInt($item.css('left'), 10)
            sourceY = parseInt($item.css('top'), 10)
          } else if (id === connection.targetId) {
            targetX = parseInt($item.css('left'), 10)
            targetY = parseInt($item.css('top'), 10)
          }
        })
        // 通过开始节点和结束节点 计算出“连线名称节点”的位置
        item.x = targetX > sourceX ? (targetX - sourceX) / 2 + sourceX : (sourceX - targetX) / 2 + targetX
        item.y = targetY > sourceY ? (targetY - sourceY) / 2 + sourceY : (sourceY - targetY) / 2 + targetY
      }
      var node = document.createElement('div')
      node.id = item.id
      node.className = 'cn'
      var cnn = this.getConnection(connection.id) // 当前节点
      cnn.connectionNameId = node.id // 将连线名称节点ID放入 连线中，在删除连线的同时将连线名称节点一起删除
      node.title = item.title
      node.innerHTML =
              "<span class='node-name'>" +
              item.name +
              '</span>' +
              "<div class='ep'></div>" +
              "<input type='text' class='node-editor node-connection-editor'/>"
      node.style.left = item.x + 'px'
      node.style.top = item.y + 'px'
      if (this.readOnly) {
        node.style.cursor = 'default'
        // node.style.setProperty('background', '#fff', 'important')
        node.style.setProperty('color', '#434343', 'important')
        $(node).find('.ep').removeClass('ep')
      }
      this.instance.getContainer().appendChild(node)
      if (!this.readOnly) { // 只读时不能拖动节点
        this.instance.draggable(node, {
          grid: [1, 1], // 对齐到网格，数值表示网格线距离
          containment: this.canvas // 节点的移动区域
        })
      }
      this.instance.fire('jsPlumbDemoNodeAdded', node)

      // 单击节点
      var _this = this
      $(node).click(function(e) {
        if (!_this.readOnly) {
          _this.completeNodeEdit() // 结束可能正在执行的编辑
          _this.unSelectAll()
          // _this.selectNode($(this))
          $(this.canvas).focus() // 画布获得焦点
          e.stopPropagation()
          _this.$emit('setConnection', connection.id) // 选中连线
        }
      })

      $(node).mousedown(function(e) {
        if (!_this.readOnly) {
          var $item = $(this)
          var x = parseInt($item.css('left'), 10)
          var y = parseInt($item.css('top'), 10)
          $item.attr('mousedownX', x)
          $item.attr('mousedownY', y)
        }
      })
      $(node).mouseup(function(e) {
        if (!_this.readOnly) {
          var $item = $(this)
          var x = parseInt($item.css('left'), 10)
          var y = parseInt($item.css('top'), 10)
          var mousedownX = parseInt($item.attr('mousedownX'))
          var mousedownY = parseInt($item.attr('mousedownY'))
          if (x === mousedownX && y === mousedownY) {
            // 节点位置没有变动，删除缓存
          }
        }
      })

      // 双击节点进入编辑
      $(node).dblclick(function(e) {
        if (!_this.readOnly) {
          _this.completeNodeEdit()
          var $editor = $(this).find('.node-editor')
          var oldName = $(this).find('span.node-name').text()
          $editor.addClass('node-editor-editing')
          $editor.val(oldName)
          $editor.select()
          e.stopPropagation()
        }
      })

      // 节点编辑回车
      $(node).find('.node-editor').keyup(function(e) {
        if (!_this.readOnly) {
          if (e.keyCode === 13) {
            _this.completeNodeEdit()
          }
          e.stopPropagation()
        }
      })
      _this.$emit('addNode', item)
      var $node = $(node)
      $node.attr('connectionId', connection.id)
      $node.attr('isNewNode', 'true')
      // $node.click() // 变为选中节点
      // $node.dblclick() // 变为编辑状态
      return node
    },

    addNodeDescription(currentNode) { // 增加节点描述节点
      var item = {}
      item.id = this.makeNewNodeId()
      item.type = 3 // 节点描述节点
      item.title = currentNode.title
      item.name = this.$isNotEmpty(currentNode.description)
        ? currentNode.description : this.splitExpression(currentNode.expression)// 将环节描述或结束条件充当节点描述节点名称
      item.connectionId = currentNode.id
      if (this.$isNotEmpty(currentNode.type) && currentNode.type === 3) {
        item.x = currentNode.x
        item.y = currentNode.y
      }
      var node = document.createElement('div')
      node.id = item.id
      node.className = 'nd'
      var _this = this
      var $nodes = $(this.canvas).find('.w')
      $.each($nodes, function(i, v) {
        if (_this.$isNotEmpty(currentNode.type) && currentNode.type === 1 && v.id === currentNode.id) {
          var $item = $(this)
          // 新增时，将节点描述节点 放在流程节点的下方
          item.x = parseInt($item.css('left'), 10)
          item.y = parseInt($item.css('top'), 10) + 55 // 流程节点的高度为47.8，暂时加55，后续可调整
        }
        if (currentNode.id === v.id) {
          v.descriptionNodeId = node.id // 将节点描述节点ID放入 流程节点中，在删除流程节点的同时将节点描述节点一起删除
        }
      })
      node.title = item.title
      node.innerHTML =
              "<span class='node-name'>" +
              item.name +
              '</span>' +
              "<div class='ep'></div>" +
              "<input type='text' class='node-editor node-connection-editor'/>"
      node.style.left = item.x + 'px'
      node.style.top = item.y + 'px'
      if (this.readOnly) {
        node.style.cursor = 'default'
        node.style.setProperty('background', '#fff', 'important')
        node.style.setProperty('color', '#434343', 'important')
        $(node).find('.ep').removeClass('ep')
      }
      this.instance.getContainer().appendChild(node)
      if (!this.readOnly) { // 只读时不能拖动节点
        this.instance.draggable(node, {
          grid: [1, 1], // 对齐到网格，数值表示网格线距离
          containment: this.canvas // 节点的移动区域
        })
      }
      this.instance.fire('jsPlumbDemoNodeAdded', node)

      // 单击节点
      $(node).click(function(e) {
        if (!_this.readOnly) {
          _this.completeNodeEdit() // 结束可能正在执行的编辑
          _this.unSelectAll()
          // _this.selectNode($(this))
          $(this.canvas).focus() // 画布获得焦点
          e.stopPropagation()
          _this.$emit('setCurrentNode', currentNode.id) // 选中连线
        }
      })

      $(node).mousedown(function(e) {
        if (!_this.readOnly) {
          var $item = $(this)
          var x = parseInt($item.css('left'), 10)
          var y = parseInt($item.css('top'), 10)
          $item.attr('mousedownX', x)
          $item.attr('mousedownY', y)
        }
      })
      $(node).mouseup(function(e) {
        if (!_this.readOnly) {
          var $item = $(this)
          var x = parseInt($item.css('left'), 10)
          var y = parseInt($item.css('top'), 10)
          var mousedownX = parseInt($item.attr('mousedownX'))
          var mousedownY = parseInt($item.attr('mousedownY'))
          if (x === mousedownX && y === mousedownY) {
            // 节点位置没有变动，删除缓存
          }
        }
      })

      // 双击节点进入编辑
      $(node).dblclick(function(e) {
        if (!_this.readOnly) {
          _this.completeNodeEdit()
          var $editor = $(this).find('.node-editor')
          var oldName = $(this).find('span.node-name').text()
          $editor.addClass('node-editor-editing')
          $editor.val(oldName)
          $editor.select()
          e.stopPropagation()
        }
      })

      // 节点编辑回车
      $(node).find('.node-editor').keyup(function(e) {
        if (!_this.readOnly) {
          if (e.keyCode === 13) {
            _this.completeNodeEdit()
          }
          e.stopPropagation()
        }
      })

      _this.$emit('addNode', item)
      var $node = $(node)
      $node.attr('nodeId', currentNode.id)
      $node.attr('isNewNode', 'true')
      // $node.click() // 变为选中节点
      // $node.dblclick() // 变为编辑状态
      return node
    },

    getConnection(cnnId) { // 通过连接id获取连接对象
      var connections = this.instance.getAllConnections()
      if (connections) {
        for (var i = 0; i < connections.length; i++) {
          if (connections[i].id === cnnId) {
            return connections[i]
          }
        }
      }
      return undefined
    },

    getConnectionByNodeId(nodeId) { // 通过节点id获取与节点为终点或起点连接对象
      var targetCnns = {}
      var connections = this.instance.getAllConnections()
      if (connections) {
        for (var i = 0; i < connections.length; i++) {
          if (connections[i].sourceId === nodeId ||
                  connections[i].targetId === nodeId) {
            targetCnns[connections[i].id] = connections[i]
          }
        }
      }
      return targetCnns
    },

    getNodeIdByName(name) { // 通过节点名称获取节点ID
      var id
      var $nodes = $(this.canvas).find('.w')
      $.each($nodes, function(i, v) {
        if (name === $(this).find('span.node-name').text()) {
          id = $(this).attr('id')
        }
      })
      return id
    },

    completeNodeEdit() {
      // 完成编辑，将新的label设置到节点
      var $editor = $('.node-editor-editing')
      if ($editor.length > 0) {
        $editor.removeClass('node-editor-editing')
        var value = $editor.val().trim()
        if (value !== '') {
          var $node = $editor.closest('.w')
          var id = $node.attr('id')
          // eslint-disable-next-line no-unused-vars
          var flag = false
          if (!this.$isNotEmpty(id)) {
            $node = $editor.closest('.cn')
            id = $node.attr('id')
            flag = true
          }
          var isRename = this.resetNodeName(id, value)
          if (isRename) {
            this.$emit('updateNodeName', id, value)
          }
          if (flag) {
            this.$emit('updateConnectionName', id, value)
          }

          if ($node.attr('isNewNode') === 'true') {
            $node.attr('isNewNode', 'false')
          }
          if (value.length > 8) {
            $node.css('padding-top', '7px')
          } else {
            $node.css('padding-top', '13px')
          }
        }
      }
    },

    resetNodeName(id, newName, nodeType) {
      var $node = $(this.canvas).find('#' + id)
      if ($node.length > 0) {
        if (nodeType && nodeType === 3) {
          if (this.$isNotEmpty(newName)) {
            $node.css('border', '1px dashed #2e6f9a')
          } else {
            $node.css('border', '0px')
          }
        }
        var oldName = $node.find('span.node-name').text()
        if (oldName !== newName) {
          $node.find('span.node-name').html(newName)
          this.instance.repaintEverything()
          return true
        }
      }
      return false
    },

    selectAll() { // 全选
      this.selectNode($('.w'))
      var connections = this.instance.getAllConnections()
      if (connections) {
        for (var i = 0; i < connections.length; i++) {
          this.selectConnection(connections[i])
        }
      }
      this.canvas.click() // 使得节点的文本不是选中状态
    },

    selectConnection(connection) { // 选择连线
      var $cnn = $(connection)
      var $svg = $($cnn[0].connector.svg)
      $svg.attr('connection-current', connection.id)
    },

    selectNode($node) { // 选择节点
      $node.addClass('endpoint-current')
    },
    selectCnnNode($node) { // 选择连线节点
      $node.addClass('endpoint-current-cnn')
    },

    unSelectAll() { // 设置不选定任何节点和连线
      var event = window.event
      if (event && !event.ctrlKey) {
        $('.endpoint-current').removeClass('endpoint-current')
        $('svg[connection-current]').removeAttr('connection-current')
      }
    },

    deleteSelectedItems() { // 删除选中的节点和连线
      if (!this.readOnly) {
        var _this = this

        // 删除节点
        var $selectedNodes = $('.endpoint-current')
        var $selectedConnections = $('svg[connection-current]')
        // if ($selectedNodes.length > 0 || $selectedConnections.length > 0) {
        //   _this.cacheMeta() // 删除之前执行缓存
        // }

        var cnnData = {}
        $.each($selectedNodes, function() {
          var id = $(this).attr('id')
          var cnns = _this.getConnectionByNodeId(id)
          $.each(cnns, function(k, v) { // 收集与删除节点有关的连线
            cnnData[k] = v
          })
          if (this.descriptionNodeId) {
            _this.instance.remove(this.descriptionNodeId, true) // 移除节点描述节点
            _this.$emit('removeNode', this.descriptionNodeId)
          }
          _this.instance.remove(id, true) // 移除节点
          _this.$emit('removeNode', id)
        })

        $.each($selectedConnections, function() { // 删除连线
          var id = $(this).attr('connection-current')
          var cnn = _this.getConnection(id)
          cnnData[cnn.id] = cnn
          _this.instance.deleteConnection(cnn)
        })

        $.each(cnnData, function(k, v) { // 触发连线删除事件
          _this.$emit('removeConnection', k)
          var connectionNameId = v.connectionNameId
          if (connectionNameId) {
            _this.instance.remove(connectionNameId, true) // 移除连线名称节点
            _this.$emit('removeNode', connectionNameId)
          }
        })
      }
    },

    getMeta() { // 获取当前流程图数据
      var meta = { nodes: [], connections: [] }
      $('.w').each(function(index, item) {
        var node = {}
        var $item = $(item)
        node.name = $item.find('.node-name').text()
        node.x = parseInt($item.css('left'), 10)
        node.y = parseInt($item.css('top'), 10)
        meta.nodes.push(node)
      })

      var cnns = this.instance.getAllConnections()
      $.each(cnns, function(index, item) {
        var connection = {}
        connection.source = item.sourceId
        connection.target = item.targetId
        meta.connections.push(connection)
      })
      return meta
    },

    // // 在客户端缓存当前流程图，触发缓存的场景：
    // // 节点：增减，移动，标题修改
    // // 连线：增减
    // cacheMeta() {
    //   if (!this.suspendCache) {
    //     var meta = this.getMeta()
    //     this.cache.unshift(meta)
    //     if (this.cache.length > 30) {
    //       this.cache.slice(29, 1) // 删除索引为29的数据
    //     }
    //   }
    // },

    getNodesXY() { // 获取所有节点的位置数据
      var xyData = {}
      $(this.canvas).find('.w').each(function() {
        var $item = $(this)
        var id = $item.attr('id')
        var x = parseInt($item.css('left'), 10)
        var y = parseInt($item.css('top'), 10)
        xyData[id] = { x: x, y: y }
      })
      $(this.canvas).find('.cn').each(function() {
        var $item = $(this)
        var id = $item.attr('id')
        var x = parseInt($item.css('left'), 10)
        var y = parseInt($item.css('top'), 10)
        xyData[id] = { x: x, y: y }
      })
      $(this.canvas).find('.nd').each(function() {
        var $item = $(this)
        var id = $item.attr('id')
        var x = parseInt($item.css('left'), 10)
        var y = parseInt($item.css('top'), 10)
        xyData[id] = { x: x, y: y }
      })
      return xyData
    },

    loadMeta(miniMeta) { // 重新装载流程图
      // 将画布的x、y置为0
      var $canvas = $('#' + this.canvasId)
      $canvas.css('left', '0px')
      $canvas.css('top', '0px')
      // $canvas.style.left = '0px'
      // $canvas.style.top = '0px'
      var _this = this
      var ids2Delete = []
      $(_this.canvas).find('.w').each(function(index, item) {
        ids2Delete.push($(item).attr('id'))
      })
      $(_this.canvas).find('.cn').each(function(index, item) {
        ids2Delete.push($(item).attr('id'))
      })
      $(_this.canvas).find('.nd').each(function(index, item) {
        ids2Delete.push($(item).attr('id'))
      })

      var connections = {}
      _this.instance.batch(function() {
        $.each(ids2Delete, function(index, item) { // 先执行清空
          _this.instance.remove(item, true) // 移除节点
        })

        var nameIdMap = {}
        var nodeArray = [] // 流程节点集合
        $.each(miniMeta.instance.nodes, function(index, item) { // 装载节点
          if (!_this.$isNotEmpty(item.connectionId)) {
            var node = _this.addNodeInner(item)
            nameIdMap[item.name] = node.id
            nodeArray.push(item)
          }
        })
        $.each(nodeArray, function(index, item) {
          $.each(miniMeta.instance.nodes, function(index, node) {
            if (_this.$isNotEmpty(node.connectionId) && node.type === 3 &&
                (item.description === node.name || _this.splitExpression(item.expression) === node.name)) {
              var param = {}
              param.id = nameIdMap[item.name]
              param.description = _this.$isNotEmpty(item.description) ? item.description : _this.splitExpression(item.expression)
              param.x = node.x
              param.y = node.y
              param.type = 3
              param.title = item.description || item.expression
              var descriptionNode = _this.addNodeDescription(param) // 装载节点描述节点
              item.descriptionNodeId = descriptionNode.id
            }
          })
        })
        //
        $.each(miniMeta.instance.connections, function(index, item) { // 装载连线
          var connectionId = item.id
          var source = nameIdMap[item.source]
          var target = nameIdMap[item.target]
          var cnn
          // eslint-disable-next-line no-empty
          if (_this.$isNotEmpty(_this.auditNodeConectionList)) {
            if (_this.auditNodeConectionList.indexOf(item.source + '-' + item.target) > -1) {
              cnn = _this.instance.connect({
                source: source,
                target: target,
                type: 'basic',
                paintStyle: { stroke: '#75C88F', strokeWidth: 2 }
              })
            } else {
              cnn = _this.instance.connect({
                source: source,
                target: target,
                type: 'basic',
                paintStyle: { stroke: '#D1D3D3', strokeWidth: 2 }
              })
            }
          } else {
            cnn = _this.instance.connect({
              source: source,
              target: target,
              type: 'basic',
              paintStyle: { stroke: '#5c96bc', strokeWidth: 2 }
            })
          }
          item.id = cnn.id
          item.sourceId = source
          item.targetId = target
          $.each(miniMeta.instance.nodes, function(index, node) { // 节点
            if (_this.$isNotEmpty(node.connectionId) && node.connectionId === connectionId && node.type === 2) { // 连线名称节点
              var param = {}
              param.id = item.id
              param.name = _this.$isNotEmpty(item.name) ? item.name : _this.splitExpression(item.expression)
              param.x = node.x
              param.y = node.y
              param.title = item.name || item.expression
              var connectionNode = _this.addConnectionName(param) // 装载连接节点
              item.connectionNodeId = connectionNode.id
            }
          })
          connections[cnn.id] = item
        })
      })
      return connections
    },
    selectCurrentNode(id) {
      var $node = $(this.canvas).find('#' + id)
      $node.addClass('endpoint-current')
    },
    // 截切“条件”充当描述节点名称，只截取到前45个字符
    splitExpression(expression) {
      var newVal = expression.slice(0, 45) // 与wf/index.vue的maxlength="45"呼应
      return newVal
    }
  }
}
</script>

