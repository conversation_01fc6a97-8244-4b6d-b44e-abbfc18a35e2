<template>
  <el-dialog
    append-to-body
    :title="'修改内容'"
    :visible.sync="isShowDialog"
    width="30%"
    :close-on-click-modal='false'
    @close="handleClose">
    <el-form
      ref="billData"
      :model="billData"
      label-width="100px">
      <el-row>
        <el-form-item label="审核意见">
          <el-input placeholder="请输入审核意见" v-model="billData.billCode" :disabled="true"></el-input>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="审核时间">
          <el-date-picker type="date" value-format="yyyy-MM-dd"
                          v-model="billData.applyDate"
                          @change="validateDate"
                          placeholder="选择日期"
                          clearable/>
        </el-form-item>
      </el-row>
    </el-form>
    <template #footer>
      <el-button class="btn-normal" type="primary" @click="handlerEdit" v-lockButton>确定</el-button>
      <el-button class="btn-normal" @click="handleClose"> 取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'bill-edit-time-dialog',
  data() {
    return {
      isShowDialog: false,
      billData: {
        billId: '',
        billCode: '',
        applyName: '',
        applyDept: '',
        applyDate: ''
      }
    }
  },
  methods: {
    show(obj, row) {
      this.isShowDialog = true
      this.init(row)
    },
    init(row) {
      this.billData.billId = row.getRowId()
      this.$callApiParams('getCformInfoById', { billId: this.billData.billId },
        result => {
          if (result.success) {
            this.billData.billId = result.data.id
            this.billData.billCode = result.data.业务编码
            this.billData.applyName = result.data.创建人名称
            this.billData.applyDept = result.data.部门名称
            this.billData.applyDate = result.data.申请日期
            return true
          }
        })
    },
    handleClose() {
      this.isShowDialog = false
    },
    validateDate(newDate) {
      const currDate = new Date()
      const applyDate = new Date(newDate)
      if (currDate < applyDate) {
        this.$message({
          message: '填报时间不能大于当前时间',
          type: 'warning'
        })
        this.billData.applyDate = ''
        return false
      }
      return true
    },
    handlerEdit() {
      if (!this.validateDate()) {
        return
      }
      this.$callApiParams('editApplyTime', this.billData,
        result => {
          if (result.success) {
            this.handleClose()
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
