<template>
  <el-dialog
    append-to-body
    :title='"切换流程到最新版本"'
    :visible.sync="isDialog"
    width="960px"
    :close-on-click-modal='false'>
    <el-form ref="form" label-width="100px" style="margin-top:5px" class="wf-data-setting-form">
      <el-row>
        <el-col :span="10">
          <el-form-item  label-width="41px" label="搜索">
            <el-input v-model="filterText" placeholder="单据编码、单据类型、申请部门、申请人" @keyup.enter.native="handleSearch"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="false">
          <el-form-item label="原节点">
            <el-input v-model="oldNodeName" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="切换到新节点">
            <el-select v-model="newNodeId" placeholder="请选择" filterable>
              <el-option
                v-for="item in nodes"
                :key="item.bizid"
                :label="item.name"
                :value="item.bizid">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <b-list class="wf-data-setting-blist" ref="baFlowList" style="height:500px "/>
    <span slot="footer" class="dialog-footer">
      <el-button class="btn-normal" type="primary"
        :disabled="btDisabled" @click="changeWfVersion">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'wf-data-setting',
  data() {
    return {
      filterText: '',
      isDialog: false,
      metaId: '',
      oldNodeName: '',
      newNodeId: '',
      nodes: [],
      ids: '',
      dataType: '',
      btDisabled: true,
      initParams: {}
    }
  },
  mounted() {
    // his.init(this.metaId)
  },
  methods: {
    init(metaId) {
      this.metaId = metaId
      this.oldNodeName = ''
      this.newNodeId = ''
      this.$nextTick(() => {
        var params = {}
        params.metaId = this.metaId
        this.$callApiParams('getWfNodesLast', params, (result) => {
          this.nodes = result.data
          return true
        })
        this.initParams = {
          params: {
            metaId: this.metaId,
            dataApiKey: 'getDataByWf'
          },
          clickRowCallback: (row, table) => {
            this.oldNodeName = row.currentNode
            this.ids = row.ID
            this.dataType = row.dataType
          },
          rowCheckedCallback: rows => {
            this.btDisabled = this.$isEmpty(rows)
          }
        }
        this.$refs.baFlowList.init(this.initParams)
      })
    },
    handleSearch() {
      this.initParams.params.filterText = this.filterText
      this.initParams.doLoadTable()
    },
    changeWfVersion() {
      var params = {}
      params.metaId = this.metaId
      params.nodeId = this.newNodeId
      params.ids = this.$refs.baFlowList.getTableCheckedIdsStr()
      params.dataType = this.$refs.baFlowList.rowsData[0].dataType
      for (let i = 0; i < this.nodes.length; i++) {
        if (this.newNodeId === this.nodes[i].bizid) {
          params.nodeName = this.nodes[i].name
        }
      }
      this.$callApiParams('changeWfVersion', params, (result) => {
        this.isDialog = false
        return true
      })
    }

  }
}
</script>

<style lang='scss' scoped>
.wf-data-setting-blist {
  ::v-deep .buttons-normal,::v-deep .main-border {
    padding: 0;
  }
}
.wf-data-setting-form::v-deep .el-col-4 .el-form-item__content { margin-left: 10px !important; }
</style>
