<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-15 11:13:35
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-07-15 11:51:55
-->
<template>
  <common-dialog
    append-to-body
    :title='"检查流程"'
    v-model="isDialog"
    size="large"
    :showFooter='false'>
    <template #dialogContent>
    <div class="wfcheck flex-column">
    <wfcanvas
      style="height:300px;border: 1px solid #bbb;"
      ref="wfcanvass"
      :currentNodeId="currentNodeId"
      :auditNodeList="auditNodeList"
      :auditNodeConectionList="auditNodeConectionList"
      :readOnly="true"
      canDrag
      :canvasId="wfCanvasId"/>

    <bBList class="wf-data-setting-blist flex-1" ref="baFlowList"/>
    </div>

    </template>
  </common-dialog>
</template>

<script>
    import BList from '@/views/ba/ba-list.vue'
    import wfcanvas from '@/components/wf/wfcanvas.vue'
    import {getUUID} from "../../utils";

    export default {
        name: "wf-data-check",
        compoments: { BList, wfcanvas },
      data() {
        return {
          isDialog: false,
          currentNodeId:'',
          auditNodeList: [],
          auditNodeConectionList: [],
          wfCanvasId: 'bifrost_workflow_canvas_view',
          meta: { instance: { detailDlgHeight: 500, detailDlgWidth: 500 }},
          metaId: ''
        }
      },
      methods: {
        init(metaData,metaId) {
          this.metaId = metaId
          this.wfCanvasId = 'bifrost_workflow_canvas_view' + getUUID()
          this.initMeta(metaData)
          this.$nextTick(()=>{
            this.$refs.baFlowList.init({
              showPager: false,
              params: {
                metaId: this.metaId,
                dataApiKey: 'getWfReport'
              }
            })
            this.$refs.baFlowList.setButtonNormalNoPaddingTop(true)
          })

        },
        initMeta(metaData) {
          var meta = JSON.parse(JSON.stringify(metaData.meta))
            this.$nextTick(() => {
              this.meta.instance.detailDlgWidth = meta.instance.detailDlgWidth
              this.meta.instance.detailDlgHeight = meta.instance.detailDlgHeight
              this.$nextTick(() => {
                this.$refs.wfcanvass.loadMeta(meta)
              })
            })
        }
      }
    }
</script>

<style lang='scss' scoped>
  .wfcheck {
    ::v-deep .el-dialog ,::v-deep el-dialog__body {
      padding: 0px  !important;
    }
  }
  .wf-data-setting-form::v-deep .el-col-4 .el-form-item__content { margin-left: 10px !important; }
</style>
