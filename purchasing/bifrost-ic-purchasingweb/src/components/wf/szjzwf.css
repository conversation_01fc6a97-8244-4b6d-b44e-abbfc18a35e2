/*
    Default styles for jsPlumb Toolkit/Community Editions.

    Copyright 2020 https://jsplumbtoolkit.com
*/

/**
jtk-connector is assigned to connector SVG elements. overflow:visible here ensures we dont have to take into account path
width and overlay sizes when computing the size of the svg element. In the Community edition this is a change from
2.14.0 onwards.
 */
.jtk-connector {
  overflow: visible;
}

/* --------------------------------------------------------------------------------------------- */
/* --- SURFACE WIDGET -------------------------------------------------------------------------- */
/* --------------------------------------------------------------------------------------------- */

/*
    Assigned to every Node managed by an instance of the Toolkit. They are required to be positioned absolute, to
    enable dragging to work properly.
*/
.jtk-node {
  position: absolute;
}

/*
    Assigned to every Group managed by an instance of the Toolkit. They are required to be positioned absolute, to
    enable dragging to work properly. We set overflow:visible on Group elements too, as a drag outside of the bounds
    is automatically reverted anyway, and without overflow:visible you cannot drag a node to some other element. You can
    also drag a node out of the element's viewport and if you drop it you can never get it back.
*/
.jtk-group {
  position: absolute;
  overflow: visible;
}

/*

    This is the attribute used to mark which part of a Group DOM element should contain the child Nodes. We mark it
    as having `position:relative` so that the absolute positioned Nodes are drawn correctly.
*/
[jtk-group-content] {
  position: relative;
}

/*
    This style was created in response to this Chrome bug:
    http://stackoverflow.com/questions/13758215/artifacts-when-css-scaled-in-chrome

    Basically it's about how sometimes there can be artefacts left on screen when the user drags an element. It seems
    the issue has been fixed in more recent versions of Chrome, but the style is left here in case you come across
    the problem.
*/
.jtk-node.jtk-drag {
  /*-webkit-backface-visibility: hidden;*/
}

/*
    Suppresses the pointer events on an element that was created by Katavorio in response to a drag in which the element
    should first be cloned. Having this clone ignore pointer events means there is less chance that any other
    mouse activity (such as click) on the original element will not be consumed by katavorio.
 */
.katavorio-clone-drag {
  pointer-events: none;
}

/*
    Assigned to an element that is the `Container` in a `render` call.
    Elements that are acting as Surface widgets should have overflow:hidden set to prevent libs from
    scrolling them during drag (we don't want scrollbars; we have an infinite canvas). Position is set to
    `relative` as this is the parent for nodes, which are positioned absolute (and for absolute positioning
    to work, you need to ensure the parent node has `position:relative`). This style also sets some default
    values for the cursor - using a `grab` cursor where supported.
*/
.jtk-surface {
  overflow: hidden !important;
  position: relative;
  cursor: move;
  cursor: -moz-grab;
  cursor: -webkit-grab;

  /*
      For IE10+. As discussed on this page:

      https://msdn.microsoft.com/en-us/library/ie/jj583807(v=vs.85).aspx

      Microsoft have very helpfully implemented default behaviours for a bunch of touch events and
      then consumed the events so you don't have to be bothered by them. They've "done a lot of research"
      about this stuff and put together a really great default experience for everyone in the entire world.
  */
  touch-action: none;

  /*
      Another Chrome issue that appears to have been fixed in later versions
      http://stackoverflow.com/questions/15464055/css-transition-effect-makes-image-blurry-moves-image-1px-in-chrome
  */
  /*
  -webkit-backface-visibility: hidden;
  -webkit-transform: translateZ(0) scale(1.0, 1.0);
  */
}

/*
    Assigned to the surface when it is being panned. The default is to change the cursor (in browsers that support
    a `grabbing` cursor), and to disable text selection.
*/
.jtk-surface-panning {
  cursor: -moz-grabbing;
  cursor: -webkit-grabbing;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/*
    The work area in a surface renderer.
*/
.jtk-surface-canvas {
  overflow: visible !important;
}

/*
    For IE10+. Discussed above in the .jtk-surface styles. This one is specific to elements that are configured
    to be droppable on a Surface via its `registerDroppableNodes` method.
*/
.jtk-surface-droppable-node {
  touch-action: none;
}

/*
    Assigned to a Surface widget when panning is disabled (and therefore the app is relying on scrollbars when the content overflows).
*/
.jtk-surface-nopan {
  overflow: scroll !important;
  cursor: default;
}

/*
Assigned to tile images in a tiled background
*/
.jtk-surface-tile {
  border: none;
  outline: none;
  margin: 0;
  -webkit-transition: opacity .3s ease .15s;
  -moz-transition: opacity .3s ease .15s;
  -o-transition: opacity .3s ease .15s;
  -ms-transition: opacity .3s ease .15s;
  transition: opacity .3s ease .15s;
}

/*
    Assigned to the element used for node select with the mouse ("lasso").
*/
.jtk-lasso {
  border: 2px solid rgb(49, 119, 184);
  background-color: WhiteSmoke;
  opacity: 0.5;
  display: none;
  z-index: 20000;
  position: absolute;
}

/*
    This class is added to the document body on lasso drag start and removed at the end of lasso dragging. Its purpose
    is to switch off text selection on all elements while the user is dragging the lasso.
*/
.jtk-lasso-select-defeat * {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/**
    Added to the lasso mask when it is operating in 'inverted' mode, ie. the excluded parts of the UI are covered, rather
    than the normal mode in which the selected parts of the UI are covered.
*/
.jtk-lasso-mask {
  position: fixed;
  z-index: 20000;
  display: none;
  opacity: 0.5;
  background-color: #07234E;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

/*
    Assigned to some element that has been selected (either via lasso or programmatically).
*/
.jtk-surface-selected-element {
  border: 2px dashed #f76258 !important;
}

/*
    Assigned to all pan buttons in a surface widget.
*/
.jtk-surface-pan {
  background-color: Azure;
  opacity: 0.4;
  text-align: center;
  cursor: pointer;
  z-index: 2;
  -webkit-transition: background-color 0.15s ease-in;
  -moz-transition: background-color 0.15s ease-in;
  -o-transition: background-color 0.15s ease-in;
  transition: background-color 0.15s ease-in;
}

/*
    Specific styles for the top and bottom pan buttons.
    Top/bottom are 100% width and 20px high by default
*/
.jtk-surface-pan-top, .jtk-surface-pan-bottom {
  width: 100%;
  height: 20px;
}

/*
    Hover styles for all pan buttons.
    On hover, change color, background color, font weight and opacity.
*/
.jtk-surface-pan-top:hover, .jtk-surface-pan-bottom:hover, .jtk-surface-pan-left:hover, .jtk-surface-pan-right:hover {
  opacity: 0.6;
  background-color: rgb(49, 119, 184);
  color: white;
  font-weight: bold;
}

/*
    Specific styles for the left and right pan buttons.
    Left/right pan buttons are 100% height and 20px wide
*/
.jtk-surface-pan-left, .jtk-surface-pan-right {
  width: 20px;
  height: 100%;
  line-height: 40;
}


/*
    Assigned to a pan button when the user is pressing it.
*/
.jtk-surface-pan-active, .jtk-surface-pan-active:hover {
  background-color: #f76258;
}

/* --------------------------------------------------------------------------------------------- */
/* --- MINIVIEW WIDGET ------------------------------------------------------------------------- */
/* --------------------------------------------------------------------------------------------- */

/*
    Assigned to an element that is acting as a Miniview.
    As with Surface, Miniview elements should have overflow:hidden set to prevent
    libs from scrolling them during drag. This style also provides a default width/height for a miniview,
    which you may wish to override.
*/
.jtk-miniview {
  overflow: hidden !important;
  width: 125px;
  height: 125px;
  position: relative;
  background-color: #B2C9CD;
  border: 1px solid #E2E6CD;
  border-radius: 4px;
  opacity: 0.8;
}

/*
    Assigned to the element that shows the size of the related viewport in a Miniview widget, and which can be dragged to
    move the surface.
*/
.jtk-miniview-panner {
  border: 5px dotted WhiteSmoke;
  opacity: 0.4;
  background-color: rgb(79, 111, 126);
  cursor: move;
  cursor: -moz-grab;
  cursor: -webkit-grab;
}

/*
    Assigned to the miniview's panner when it is being dragged.
*/
.jtk-miniview-panning {
  cursor: -moz-grabbing;
  cursor: -webkit-grabbing;
}

/*
    Added to all elements displayed in a miniview.
*/
.jtk-miniview-element {
  background-color: rgb(96, 122, 134);
  position: absolute;
}

/*
    Added to Group elements displayed in a miniview
*/
.jtk-miniview-group-element {
  background: transparent;
  border: 2px solid rgb(96, 122, 134);
}

/*
    Assigned to the collapse/expand miniview button
*/
.jtk-miniview-collapse {
  color: whiteSmoke;
  position: absolute;
  font-size: 18px;
  top: -1px;
  right: 3px;
  cursor: pointer;
  font-weight: bold;
}

/*
    The '-' symbol when the miniview is expanded
*/
.jtk-miniview-collapse:before {
  content: "\2012";
}

/*
    Assigned to the miniview element when it is collapsed.
*/
.jtk-miniview-collapsed {
  background-color: #449ea6;
  border-radius: 4px;
  height: 22px;
  margin-right: 0;
  padding: 4px;
  width: 21px;
}

/*
    Hide all children of the miniview (except the expand button) when it is collapsed so you don't see anything
    poking through under the + icon.
*/
.jtk-miniview-collapsed .jtk-miniview-element, .jtk-miniview-collapsed .jtk-miniview-panner {
  visibility: hidden;
}

/*
    The '+' symbol when the miniview is collapsed.
*/
.jtk-miniview-collapsed .jtk-miniview-collapse:before {
  content: "+";
}

/*
    Hover state for the collapse/expand icon.
*/
.jtk-miniview-collapse:hover {
  color: #E4F013;
}

/* ------------------------------------------------------------------------------------------- */
/* --- DIALOGS --------------------------------------------------------------------------------*/
/* ------------------------------------------------------------------------------------------- */

/*
    This is the element that acts as the dialog underlay - the modal "mask". Note the high z-index default
    set here (and note also the overlay style below has a z-index with a value higher by one).
*/
.jtk-dialog-underlay {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  position: fixed;
  z-index: 100000;
  opacity: 0.8;
  background-color: #CCC;
  display: none;
}

/*
    This is the element that acts as the parent for dialog content.
*/
.jtk-dialog-overlay {
  position: fixed;
  z-index: 100001;
  display: none;
  background-color: white;
  font-family: "Open Sans", sans-serif;
  padding: 7px;
  box-shadow: 0 0 5px gray;
  overflow: hidden;
}

.jtk-dialog-overlay-x {
  max-height: 0;
  transition: max-height 0.5s ease-in;
  -moz-transition: max-height 0.5s ease-in;
  -ms-transition: max-height 0.5s ease-in;
  -o-transition: max-height 0.5s ease-in;
  -webkit-transition: max-height 0.5s ease-in;
}

.jtk-dialog-overlay-y {
  max-width: 0;
  transition: max-width 0.5s ease-in;
  -moz-transition: max-width 0.5s ease-in;
  -ms-transition: max-width 0.5s ease-in;
  -o-transition: max-width 0.5s ease-in;
  -webkit-transition: max-width 0.5s ease-in;
}

.jtk-dialog-overlay-top {
  top: 20px;
}

.jtk-dialog-overlay-bottom {
  bottom: 20px;
}

.jtk-dialog-overlay-left {
  left: 20px;
}

.jtk-dialog-overlay-right {
  right: 20px;
}

.jtk-dialog-overlay-x.jtk-dialog-overlay-visible {
  max-height: 1000px;
}

.jtk-dialog-overlay-y.jtk-dialog-overlay-visible {
  max-width: 1000px;
}

/*
    The element containing buttons in a dialog.
*/
.jtk-dialog-buttons {
  text-align: right;
  margin-top: 5px;
}

/*
    An individual button in a dialog.
*/
.jtk-dialog-button {
  border: none;
  cursor: pointer;
  margin-right: 5px;
  min-width: 56px;
  background-color: white;
  outline: 1px solid #ccc;
}

/*
    Hover style for an individual button in a dialog.
*/
.jtk-dialog-button:hover {
  color: white;
  background-color: #234b5e;
}

/*
    The titlebar of a dialog.
*/
.jtk-dialog-title {
  text-align: left;
  font-size: 14px;
  margin-bottom: 9px;
}

.jtk-dialog-content {
  font-size: 12px;
  text-align: left;
  min-width: 250px;
  margin: 0 14px;
}

.jtk-dialog-content ul {
  width: 100%;
  padding-left: 0;
}

.jtk-dialog-content label {
  cursor: pointer;
  font-weight: inherit;
}

.jtk-dialog-overlay input, .jtk-dialog-overlay textarea {
  background-color: #FFF;
  border: 1px solid #CCC;
  color: #333;
  font-size: 14px;
  font-style: normal;
  outline: none;
  padding: 6px 4px;
  margin-right: 6px;
}

.jtk-dialog-overlay input:focus, .jtk-dialog-overlay textarea:focus {
  background-color: #cbeae1;
  border: 1px solid #83b8a8;
  color: #333;
  font-size: 14px;
  font-style: normal;
  outline: none;
}

/* -------------------------------------------------------------------------------------------- */
/* --- DRAWING TOOLS -------------------------------------------------------------------------- */
/* -------------------------------------------------------------------------------------------- */

/*
    Assigned to the element that is drawn around some other element when a drawing operation is taking place.
*/
.jtk-draw-skeleton {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  outline: 2px solid #84acb3;
  opacity: 0.8;
}

/*
    Assigned to every handle (top left, top right, bottom left, bottom right, center) in a draw skeleton.
*/
.jtk-draw-handle {
  position: absolute;
  width: 7px;
  height: 7px;
  background-color: #84acb3;
}

/*
    Assigned to the top left handle in a draw skeleton
*/
.jtk-draw-handle-tl {
  left: 0;
  top: 0;
  cursor: nw-resize;
}

/*
    Assigned to the top right handle in a draw skeleton
*/
.jtk-draw-handle-tr {
  right: 0;
  top: 0;
  cursor: ne-resize;
}

/*
    Assigned to the bottom left handle in a draw skeleton
*/
.jtk-draw-handle-bl {
  left: 0;
  bottom: 0;
  cursor: sw-resize;
}

/*
    Assigned to the bottom right handle in a draw skeleton
*/
.jtk-draw-handle-br {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

/*
    Assigned to the center handle in a draw skeleton (the handle by which the element may be dragged). This is
    not visible by defaut; enable if you need it.
*/
.jtk-draw-drag {
  display: none;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -10px;
  margin-top: -10px;
  width: 20px;
  height: 20px;
  background-color: #84acb3;
  cursor: move;
}

/*
    This class is added to the document body on drag resize start and removed at the end of resizing. Its purpose
    is to switch off text selection on all elements while the user is resizing an element.
*/
.jtk-drag-select-defeat * {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/*@font-face {*/
/*    font-family: 'ico-jsplumb';*/
/*    src: url("ico-jsplumb.eot?-oqkwhs");*/
/*    src: url("ico-jsplumb.eot?#iefix-oqkwhs") format("embedded-opentype"), url("ico-jsplumb.woff?-oqkwhs") format("woff"), url("ico-jsplumb.ttf?-oqkwhs") format("truetype"), url("ico-jsplumb.svg?-oqkwhs#ico-jsplumb") format("svg");*/
/*    font-weight: normal;*/
/*    font-style: normal*/
/*}*/

/*[class^="icon-"], [class*=" icon-"] {*/
/*    font-family: 'ico-jsplumb';*/
/*    speak: none;*/
/*    font-style: normal;*/
/*    font-weight: normal;*/
/*    font-variant: normal;*/
/*    text-transform: none;*/
/*    line-height: 1*/
/*}*/

/*.icon-license:before {*/
/*    content: "\e604"*/
/*}*/

/*.icon-source:before {*/
/*    content: "\e605"*/
/*}*/

/*.icon-support:before {*/
/*    content: "\e606"*/
/*}*/

/*.icon-facebook:before {*/
/*    content: "\e600"*/
/*}*/

/*.icon-twitter:before {*/
/*    content: "\e601"*/
/*}*/

/*.icon-github:before {*/
/*    content: "\e602"*/
/*}*/

/*.icon-linkedin:before {*/
/*    content: "\e603"*/
/*}*/

/*.icon-cross:before {*/
/*    content: "\ea0f"*/
/*}*/

/*.icon-checkmark:before {*/
/*    content: "\ea10"*/
/*}*/

*, *:before, *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility
}

html {
  padding: 0;
  margin: 0
}

body {
  background-color: #FFF;
  color: #434343;
  font-family: "Lato", sans-serif;
  font-size: 14px;
  font-weight: 400;
  height: 100%;
  padding: 0
}

@media only screen and (max-width: 320px) {
  body {
    margin-top: 51px
  }
}

.clear-padding {
  padding: 0
}

.full-width {
  width: 100%
}

.blank-left-padding {
  padding: 0 0 0 83px
}

.full-width-container {
  padding: 40px 0;
  width: 100%
}

@media only screen and (max-width: 320px) {
  .full-width-container {
    padding: 30px 0
  }
}

.padding-top-page {
  padding-top: 92px
}

@media only screen and (max-width: 320px) {
  .padding-top-page {
    padding-top: 10px
  }
}

.center-block {
  display: table
}

.pos-relative {
  position: relative
}

@media only screen and (max-width: 320px) {
  .mobile-center {
    text-align: center
  }
}

.bg-sepia {
  background-color: #f4f5ef
}

.color-white {
  color: #FFF !important
}

.divider-10 {
  padding-top: 10px
}

.divider-20 {
  padding-top: 20px
}

.divider-30 {
  padding-top: 30px
}

.big-title {
  color: #58775d;
  font-size: 56px;
  font-weight: 400;
  margin-bottom: 20px
}

@media only screen and (max-width: 320px) {
  .big-title {
    font-size: 32px;
    margin-bottom: 10px
  }
}

.high-title {
  color: #696b60;
  font-size: 34px;
  font-weight: 400;
  line-height: 1.2em;
  margin-bottom: 20px
}

@media only screen and (max-width: 320px) {
  .high-title {
    font-size: 32px;
    margin-bottom: 10px
  }
}

.prod-title {
  color: #696b60;
  font-size: 32px;
  font-weight: 400;
  line-height: 1.2em;
  margin-bottom: 20px
}

@media only screen and (max-width: 320px) {
  .prod-title {
    font-size: 32px;
    margin-bottom: 10px
  }
}

.title-white {
  color: #FFF;
  font-size: 32px;
  font-weight: 400;
  line-height: 1.2em
}

@media only screen and (max-width: 320px) {
  .title-white {
    font-size: 32px;
    margin-bottom: 10px
  }
}

.vue-bifrostIcApp > .title {
  color: #434343;
  font-size: 25px;
  font-weight: 400;
  margin-bottom: 10px
}

@media only screen and (max-width: 320px) {
  .title {
    font-size: 18px;
    margin-bottom: 6px
  }
}

.big-lead {
  color: #FFF;
  font-size: 25px;
  font-weight: 400;
  margin-bottom: 20px
}

@media only screen and (max-width: 320px) {
  .big-lead {
    font-size: 18px;
    margin-bottom: 10px
  }
}

.lead {
  color: #434343;
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 0
}

@media only screen and (max-width: 320px) {
  .lead {
    font-size: 14px
  }
}

.apidoc-jsplumb-logo {
  display: none
}

.btn-big-download a {
  border-radius: 0;
  color: #FFF;
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  margin: 0;
  padding: 10px 20px;
  text-decoration: none;
  background-color: #096070
}

.btn-big-download a:hover:not(:disabled), .btn-big-download a:active:not(:disabled), .btn-big-download a:focus:not(:disabled) {
  color: #FFF;
  cursor: pointer;
  background-color: #012930
}

.btn-download a {
  border-radius: 0;
  color: #FFF;
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  margin: 0;
  padding: 10px 20px;
  text-decoration: none;
  background-color: #096070
}

.btn-download a:hover:not(:disabled), .btn-download a:active:not(:disabled), .btn-download a:focus:not(:disabled) {
  color: #FFF;
  cursor: pointer;
  background-color: #012930
}

.demo-highlight {
  background: url("/img/back-green.jpg") repeat-x center
}

.logo-jsplumb img {
  padding-bottom: 16px;
  max-width: 100%
}

@media only screen and (max-width: 320px) {
  .logo-jsplumb img {
    max-width: 63%
  }
}

@media only screen and (min-width: 769px) and (max-width: 1023px) {
  .logo-jsplumb img {
    max-width: 63%
  }
}

.browser-window {
  position: relative
}

.browser-window .browser-dropdown-menu {
  position: absolute;
  top: 10px;
  width: 100%
}

.browser-window table {
  width: 100%
}

.browser-window td.corners {
  height: 56px;
  width: 53px
}

.browser-window td.center-top {
  background: url("/img/browser-window-top-center.png") repeat-x;
  width: 100%
}

.browser-window td.center {
  background-color: #FFF
}

.browser-window .jsplumb-demo-container {
  padding: 10px
}

.download-container {
  text-align: right
}

@media only screen and (max-width: 320px) {
  .download-container {
    text-align: center
  }
}

.background-blue {
  background: url("/img/back-blue.jpg") repeat-x center
}

.background-dark-green {
  background: url("/img/back-dark-green.jpg") repeat
}

.btn-drop {
  color: #FFF;
  font-weight: 700;
  padding: 10px 20px 8px 20px;
  background-color: #6dadcc;
  cursor: pointer;
  width: 100%
}

.btn-drop:hover, .btn-drop:focus, .btn-drop:active, .open .dropdown-toggle.btn-drop {
  color: #FFF;
  border: none;
  background-color: #3491be;
  text-decoration: none
}

.btn-dropmenu {
  -webkit-box-shadow: none;
  background-color: #6dadcc;
  border-radius: 0;
  border: none;
  box-shadow: none;
  color: #FFF;
  margin-top: 1px !important;
  padding: 10px 0;
  width: 100%
}

.btn-dropmenu > li > a {
  color: #FFF;
  border: none;
  padding: 10px 20px
}

.btn-dropmenu > li > a:hover {
  background-color: #3c9cca;
  color: #FFF
}

.row #main {
  height: 400px;
  margin-top: 25px
}

.row #dataset {
  display: none
}

.navbar-top {
  background-color: #58775d;
  border-color: none;
  font-size: 14px;
  font-weight: 700
}

@media only screen and (max-width: 320px) {
  .navbar-top {
    background-color: #58775d
  }
}

.navbar-brand {
  height: 56px
}

.nav-wrapper {
  margin-top: 10px
}

.navbar-header a {
  border: none
}

.navbar-nav > li > a {
  border: none;
  color: #FFF;
  padding: 6px 10px;
  text-decoration: none
}

.navbar-nav > li > a.active {
  border: none;
  color: #cdcc73;
  padding: 6px 10px;
  text-decoration: none;
  cursor: default
}

.navbar-nav > li:last-child > a {
  color: #FFF;
  padding: 6px 0 6px 10px;
  text-decoration: none
}

.navbar-nav > li:last-child > a.active {
  border: none;
  color: #cdcc73;
  padding: 6px 10px;
  text-decoration: none;
  cursor: default
}

.nav {
  float: left
}

@media only screen and (max-width: 320px) {
  .nav {
    float: left;
    margin-top: 10px
  }
}

@media only screen and (min-width: 321px) and (max-width: 768px) {
  .nav {
    float: left
  }
}

.nav > li > a:hover, .nav > li > a:focus {
  background-color: transparent;
  color: #cdcc73;
  text-decoration: none
}

.navbar-toggle .icon-bar {
  background-color: #FFF
}

.social-nav {
  color: #FFF;
  float: right;
  font-size: 22px
}

.social-nav .link {
  cursor: pointer;
  border: none;
  color: #FFF;
  margin-left: 10px;
  text-decoration: none
}

.social-nav .link:hover, .social-nav .link:focus, .social-nav .link:active {
  color: #cdcc73;
  border: none;
  text-decoration: none
}

.breadcrumbs {
  height: 20px;
  top: 52px;
  position: fixed;
  z-index: 1030;
  font-size: 11px;
  padding-left: 2%;
  background-color: aliceblue;
  text-transform: uppercase;
  width: 100%;
  padding-top: 3px
}

.breadcrumbs a {
  color: #629f8d;
  text-decoration: none
}

.breadcrumbs a:hover, .breadcrumbs a:focus {
  color: #434343;
  text-decoration: underline
}

.breadcrumbs .crumbs {
  float: left
}

.breadcrumbs .demo-nav {
  display: inline-block;
  float: right;
  margin-right: 2%
}

@media (max-width: 490px) {
  .crumbs {
    display: none
  }

  .breadcrumbs {
    text-align: center
  }

  .demo-nav {
    float: none !important;
    margin-left: auto;
    margin-right: auto !important
  }
}

.blockFooter .copyright {
  font-size: 12px;
  color: #a0b8b7
}

.blockFooter .copyright a {
  border: none;
  color: #FFF;
  text-decoration: none
}

.blockFooter .copyright a:hover {
  color: #cdcc73;
  text-decoration: none
}

ul.navbar-footer {
  padding: 4px 0 0 10px
}

@media only screen and (max-width: 320px) {
  ul.navbar-footer {
    padding: 6px 0 0 0
  }
}

.navbar-footer > li {
  float: left;
  font-size: 14px;
  list-style-type: none
}

.navbar-footer > li > a {
  border: none;
  color: #FFF;
  padding: 6px 6px 0 0;
  text-decoration: none
}

.navbar-footer > li > a:hover {
  color: #cdcc73;
  text-decoration: none
}

.blockFooter .social-nav {
  float: right;
  font-size: 22px
}

@media only screen and (max-width: 320px) {
  .blockFooter .social-nav {
    float: left;
    margin-top: 10px
  }
}

.blockFooter .social-nav .link {
  border: none;
  color: #a0b8b7;
  margin-left: 10px;
  text-decoration: none
}

@media only screen and (max-width: 320px) {
  .blockFooter .social-nav .link {
    margin-left: 0
  }
}

.blockFooter .social-nav .link:hover, .blockFooter .social-nav .link:focus, .blockFooter .social-nav .link:active {
  color: #cdcc73;
  border: none;
  text-decoration: none
}

.clear-footer {
  width: 100%;
  clear: both
}

body.ff .clear-footer {
  margin-bottom: 95px
}

body.ff .footer-wrapper {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0
}

#docs {
  margin-top: 101px;
  margin-bottom: 40px
}

#docs h2, #docs h3 {
  color: #629f8d;
  padding-bottom: 3px
}

#docs pre {
  background: #f4f5ef;
  border-color: #c5c7b8;
  border-radius: 3px;
  font-family: courier, sans-serif;
  font-size: 12px
}

#docs a {
  color: #629f8d;
  text-decoration: none
}

#docs a:hover, #docs a:focus {
  color: #434343;
  text-decoration: underline
}

#docs ul {
  padding-left: 10px
}

#docs table {
  border: 1px solid #a0b8b7
}

#docs th {
  padding: 10px;
  background-color: #a0b8b7
}

#docs td {
  padding: 10px;
  border: 1px solid #a0b8b7
}

#docs .markdown-body {
  font-size: 13px;
  line-height: 1.6;
  background-color: white
}

#docs .markdown-body ul {
  padding-left: 16px
}

#docs .markdown-body li {
  list-style-type: circle
}

#docs .docs-nav {
  padding-bottom: 10px;
  padding-top: 10px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #f4f5ef;
  border: 1px solid #f4f5ef;
  border-radius: 3px
}

#docs .docs-nav > ul > li > a {
  font-weight: bold;
  font-size: 13px
}

#docs .docs-nav > ul > li > ul {
  margin-top: 7px
}

#docs .docs-nav > ul > li {
  color: #629f8d;
  font-size: 13px;
  font-weight: bold
}

#docs .docs-nav > ul > li > ul a {
  font-weight: normal
}

#docs .docs-nav li {
  list-style-type: none;
  font-size: 12px;
  border-top: 1px dotted #ccc;
  padding-top: 6px;
  padding-bottom: 5px
}

.apidoc-jsplumb-logo {
  display: none;
  left: 50%;
  position: absolute;
  top: 10px
}

#api-classes {
  margin: 0;
  padding: 0;
  background-color: #f4f5ef;
  font-size: 13px
}

#api-classes li {
  list-style-type: none;
  margin: 0;
  border-bottom: 1px dotted #ccc;
  padding-top: 5px;
  padding-bottom: 5px
}

#api-classes li a {
  color: #629f8d
}

.yui3-tabview-panel {
  background-color: #f4f5ef !important;
  border: none !important
}

#api-tabview-panel {
  overflow: hidden
}

#api-filter {
  width: 100%
}

.apidoc-main {
  margin-left: 265px !important;
  height: auto;
  width: auto;
  background-color: white;
  border: none
}

.docs-main {
  margin-left: 30px
}

.box.intro {
  background-color: #f4f5ef;
  padding: 14px;
  margin-bottom: 19px;
  border-radius: 3px;
  font-size: 13px
}

.apidoc-summary {
  width: 150px
}

.apidoc-detail {
  margin-left: 160px
}

.apidocs {
  margin-top: 20px !important;
  padding-left: 30px
}

#api-options {
  margin-top: 30px;
  padding-left: 30px
}

.apidoc-intro {
  padding-top: 30px !important
}

#api-list {
  margin-bottom: 50px
}

#api-list h2 {
  display: none
}

.index-item {
  width: 30%;
  float: left;
  display: inline
}

.index-list {
  overflow: hidden
}

pre.license {
  padding: 15px;
  word-break: normal;
  word-wrap: normal;
  white-space: pre-wrap
}

.license-wrapper, .privacy {
  margin-top: 50px;
  text-align: left;
  width: 75%;
  margin-left: auto;
  margin-right: auto;
  padding: 15px
}

.privacy {
  font-size: 80px
}

.community-demo .demo {
  height: 650px;
  position: relative;
  overflow: auto;
  background-color: white;
  border-radius: 3px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #CCC;
  margin-top: 73px;
  width: 90%
}

.product-matrix-container {
  width: 100%;
  font-size: 15px
}

.product-matrix-container .high-title {
  font-size: 28px
}

.product-matrix-container tr:nth-child(odd) td {
  background-color: #FBFBFB;
  border-bottom: 1px solid #CFD0C9
}

.product-matrix-container tr:nth-child(even) td {
  background-color: #E9EBDD;
  border-bottom: 1px solid #CFD0C9
}

.product-matrix-container td {
  border-left: 1px solid #CFD0C9
}

.product-matrix-container th {
  border-bottom: 1px solid #CFD0C9
}

.product-matrix-container th, .product-matrix-container td {
  padding: 10px
}

.product-matrix-container a {
  color: #629f8d;
  text-decoration: none
}

.product-matrix-container table {
  width: 100%;
  border: 1px solid #CFD0C9;
  font-size: 90%
}

.product-matrix-container table table {
  border: none
}

.product-matrix-container table table th, .product-matrix-container table table td, .product-matrix-container table table tr {
  border: none
}

.license-type .icon {
  color: #a0b8b7;
  font-size: 59px;
  margin-left: 15px
}

.license-type.divider {
  border-bottom: 1px solid #CFD0C9;
  padding-bottom: 20px;
  margin-bottom: 10px
}

.license-type .divider-color {
  border-bottom: 1px solid #CFD0C9;
  background-color: #f4f5ef
}

.license-type .price-table .title {
  color: #696b60;
  font-size: 34px;
  font-weight: 400;
  line-height: 1.2em;
  margin-bottom: 8px
}

@media only screen and (max-width: 320px) {
  .license-type .price-table .title {
    font-size: 32px;
    margin-bottom: 4px
  }
}

.license-type .price-table .sub-title {
  color: #696b60;
  font-size: 25px;
  font-weight: 400;
  line-height: 1.2em;
  margin-bottom: 8px
}

@media only screen and (max-width: 320px) {
  .license-type .price-table .sub-title {
    font-size: 25px;
    margin-bottom: 4px
  }
}

.license-type .price-table .total-title {
  color: #696b60;
  font-size: 22px;
  font-weight: 400;
  line-height: 1.2em;
  text-align: right
}

@media only screen and (max-width: 320px) {
  .license-type .price-table .total-title {
    font-size: 22px
  }
}

.license-type .price-table .label {
  color: #434343;
  display: block;
  font-size: 15px;
  font-weight: 400;
  padding: 0;
  text-align: left;
  white-space: normal;
  margin-bottom: 10px;
  line-height: 1.3em
}

.license-type .price-table .price {
  font-size: 15px;
  font-weight: 700;
  padding-left: 10px;
  text-align: right
}

.license-type .price-table .check-price {
  padding-left: 10px;
  width: 22px
}

.license-type .price-table .total-price {
  font-size: 18px;
  font-weight: 700;
  padding-left: 10px;
  padding-right: 20px;
  text-align: right;
  width: 22px
}

.license-type .btn-buy {
  padding: 10px;
  text-align: right
}

.license-type .notes {
  color: #8DA3A2;
  font-style: italic;
  padding-top: 40px;
  padding-left: 20px
}

@media only screen and (max-width: 320px) {
  .license-type .notes {
    padding-top: 0;
    padding-left: 72px
  }
}

@media only screen and (min-width: 321px) and (max-width: 768px) {
  .license-type .notes {
    padding-top: 0;
    padding-left: 72px
  }
}

@media only screen and (min-width: 769px) and (max-width: 1023px) {
  .license-type .notes {
    padding-top: 0;
    padding-left: 72px
  }
}

.license-type button {
  background: none;
  border: none
}

.payment-container {
  width: 80%;
  max-width: 1000px;
  margin-right: auto;
  margin-left: auto;
  margin-top: 50px
}

.item-packager * {
  font-size: 12px
}

.item-packager table {
  margin: auto
}

.package-form {
  margin: auto
}

.payment-form input:focus {
  background-color: #cbeae1
}

.payment-form-inner {
  overflow: hidden;
  -webkit-transition: height 0.5s linear;
  -moz-transition: height 0.5s linear;
  -ms-transition: height 0.5s linear;
  -o-transition: height 0.5s linear;
  transition: height 0.5s linear
}

.jtk-payment-form label {
  display: block
}

.jtk-payment-form label span {
  font-size: 12px;
  color: cadetblue
}

.error {
  color: red;
  font-size: 11px
}

.txtInv {
  width: 450px;
  height: 300px
}

.formMessage {
  min-height: 36px;
  font-size: 12px
}

.formMessage.error {
  color: red
}

.formMessage .wait {
  width: 36px;
  height: 36px;
  float: left;
  margin-right: 10px;
  display: none
}

.invoice-details table {
  border: 1px solid #456;
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #CFD0C9
}

.invoice-details table th, .invoice-details table td {
  padding: 10px;
  text-align: center
}

.invoice-details table td.item {
  text-align: right
}

.invoice-details td {
  border-left: 1px solid #CFD0C9
}

.invoice-details tr:nth-child(even) td {
  background-color: #E9EBDD;
  border-bottom: 1px solid #CFD0C9
}

.colTotal {
  font-weight: bold;
  text-align: right !important
}

.payment-form input {
  font-size: 16px
}

.payment-form button {
  background: none;
  border: none
}

pre {
  outline: 1px solid #ccc;
  padding: 5px;
  margin: 0
}

.string {
  color: green
}

.number {
  color: darkorange
}

.boolean {
  color: blue
}

.null {
  color: magenta
}

.key {
  color: red
}

.blog-content .w {
  position: absolute;
  background-color: white;
  box-shadow: 0px 0px 2px black;
  text-align: center;
  width: 50px;
  height: 50px;
  border-radius: 3px;
  cursor: pointer
}

.blog-content .demo {
  height: 340px;
  margin-top: 15px;
  margin-bottom: 15px
}

.blog-content ._jsPlumb_overlay.label {
  color: black
}

.blog-content .highlight {
  margin-top: 15px;
  margin-bottom: 15px
}

.blog-content h3 {
  margin-top: 25px
}

.blog-content h4, .blog-content h5, .blog-content h6 {
  margin-top: 20px;
  font-weight: bold
}

.blog-content h4 {
  font-size: 18px
}

.contact-form {
  width: 80%;
  max-width: 700px;
  margin-left: auto;
  padding-bottom: 25px;
  margin-right: auto
}

.contact-form button {
  background: none;
  border: none
}

/* ---------------------------------------------------------------------------------------------------- */
/* --- page structure --------------------------------------------------------------------------------- */
/* ---------------------------------------------------------------------------------------------------- */

body {
  background-color: #FFF;
  color: #434343;
  font-family: "Lato", sans-serif !important;
  font-size: 14px;
  font-weight: 400;
  height: 100%;
  padding: 0;
}

.jtk-bootstrap {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.jtk-bootstrap .jtk-page-container {
  display: flex;
  width: 100vw;
  justify-content: center;
  flex: 1;
}

.jtk-bootstrap .jtk-container {
  width: 60%;
  max-width: 800px;
}

.jtk-bootstrap-wide .jtk-container {
  width: 80%;
  max-width: 1187px;
}

.jtk-demo-main {
  position: relative;
}

.jtk-demo-main .description {
  font-size: 13px;
  margin-top: 25px;
  padding: 13px;
  margin-bottom: 22px;
  background-color: #f4f5ef;
}

.jtk-demo-main .description li {
  list-style-type: disc !important;
}

.jtk-demo-canvas {
  border: 1px solid #CCC;
  background-color: white;
  display: flex;
  position: relative;
}

.canvas-wide {
  margin-left: 0;
}

.miniview {
  position: absolute;
  top: 25px;
  right: 25px;
  z-index: 100;
}


.jtk-demo-dataset {
  text-align: left;
  max-height: 600px;
  overflow: auto;
}

.demo-title {
  float: left;
  font-size: 18px;
}

.controls {
  top: 25px;
  color: #FFF;
  margin-right: 10px;
  position: absolute;
  left: 25px;
  z-index: 1;
}

.controls i {
  background-color: #3E7E9C;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 0;
  padding: 4px;
}

li {
  list-style-type: none;
}

/* ------------------------ node palette -------------------- */

.sidebar {
  margin: 0;
  padding: 0;
  padding-top: 7px;
  padding-right: 10px;
  width: 150px;
  float: left;
  text-align: center;
  height: 550px;
  background-color: #84acb3;
}

.sidebar ul li {
  background-color: #234b5e;
  border-radius: 11px;
  color: #f7ebca;
  cursor: move;
  margin-bottom: 10px;
  margin-left: 6px;
  padding: 8px;
  width: 128px;
}

.sidebar ul {
  width: 100%;
  padding: 0;
}

.sidebar ul li:hover {
  background-color: #577a8b;
}

.sidebar i {
  float: left;
}

@media (max-width: 600px) {
  .sidebar {
    float: none;
    height: 55px;
    width: 100%;
    padding-top: 0;
  }

  .sidebar ul li {
    display: inline-block;
    margin-top: 7px;
    width: 67px;
  }

  .jtk-demo-canvas {
    margin-left: 0;
    margin-top: 10px;
    height: 364px;
  }
}

/* ---------------------------------------------------------------------------------------------------- */
/* --- jsPlumb setup ---------------------------------------------------------------------------------- */
/* ---------------------------------------------------------------------------------------------------- */

.jtk-connector {
  z-index: 9;
}

.jtk-endpoint {
  z-index: 12;
  opacity: 0.8;
  cursor: pointer;
}

.jtk-overlay {
  background-color: white;
  color: #434343;
  font-weight: 400;
  padding: 4px;
  z-index: 10;

}

.jtk-overlay.jtk-hover {
  color: #434343;
}

path {
  cursor: pointer;
}

.delete {
  padding: 2px;
  cursor: pointer;
  float: left;
  font-size: 10px;
  line-height: 20px;
}

.add, .edit {
  cursor: pointer;
  float: right;
  font-size: 10px;
  line-height: 20px;
  margin-right: 2px;
  padding: 2px;
}

.edit:hover {
  color: #ff8000;
}

.selected-mode {
  color: #E4F013;
}

.connect {
  width: 10px;
  height: 10px;
  background-color: #f76258;
  position: absolute;
  bottom: 13px;
  right: 5px;
}

/* header styles */

.demo-links {
  position: fixed;
  right: 0;
  top: 57px;
  font-size: 11px;
  background-color: white;
  opacity: 0.8;
  padding-right: 10px;
  padding-left: 5px;
  text-transform: uppercase;
  z-index: 100001;
}

.demo-links div {
  display: inline;
  margin-right: 7px;
  margin-left: 7px;
}

.demo-links i {
  padding: 4px;
}

/*
.navbar-top {
    background-color: #3c565d;
    border:none;
    font-size: 14px;
    font-weight: 700;
}

.navbar-nav > li > a {
    border: none;
    color: #FFF;
    padding: 6px 10px;
    text-decoration: none;
}

.social-nav {
    color: #FFF;
    float: right;
    font-size: 22px;
}

.nav > li > a:hover, .nav > li > a:focus {
    background-color: transparent;
    color: #cdcc73;
    text-decoration: none;
}
*/

.demo {
  /* for IE10+ touch devices */
  touch-action: none;
}

.w {
  padding-top: 15px;
  padding-left: 4px;
  padding-right: 4px;
  position: absolute;
  z-index: 4;
  border: 1px solid #2e6f9a;
  box-shadow: 2px 2px 19px #e0e0e0;
  -o-box-shadow: 2px 2px 19px #e0e0e0;
  -webkit-box-shadow: 2px 2px 19px #e0e0e0;
  -moz-box-shadow: 2px 2px 19px #e0e0e0;
  -moz-border-radius: 8px;
  border-radius: 8px;
  opacity: 0.8;
  cursor: move;
  background-color: white;
  font-size: 13px;
  -webkit-transition: background-color 0.25s ease-in;
  -moz-transition: background-color 0.25s ease-in;
  transition: background-color 0.25s ease-in;
  box-shadow: rgb(99, 97, 97) 0px 0px 4px;
  border-radius: 4px;
}

.w:hover {
  background-color: #5c96bc;
  color: white;

}

.cn {
  padding: 5px 10px;
  position: absolute;
  z-index: 4;
  /*border: 1px solid #2e6f9a;*/
  /*box-shadow: 2px 2px 19px #e0e0e0;*/
  -o-box-shadow: 2px 2px 19px #e0e0e0;
  /*-webkit-box-shadow: 2px 2px 19px #e0e0e0;*/
  -moz-box-shadow: 2px 2px 19px #e0e0e0;
  -moz-border-radius: 8px;
  border-radius: 8px;
  opacity: 0.8;
  cursor: move;
  /*background-color: white;*/
  font-size: 12px;
  line-height: 13px;
  /*-webkit-transition: background-color 0.25s ease-in;*/
  -moz-transition: background-color 0.25s ease-in;
  transition: background-color 0.25s ease-in;
  /*box-shadow: rgb(99, 97, 97) 0px 0px 4px;*/
  border-radius: 4px;
}

.nd {
  padding: 5px 10px;
  position: absolute;
  z-index: 4;
  border: 1px dashed #2e6f9a;
  box-shadow: 2px 2px 19px #e0e0e0;
  -o-box-shadow: 2px 2px 19px #e0e0e0;
  -webkit-box-shadow: 2px 2px 19px #e0e0e0;
  -moz-box-shadow: 2px 2px 19px #e0e0e0;
  -moz-border-radius: 8px;
  border-radius: 8px;
  opacity: 0.8;
  cursor: move;
  /*background-color: white;*/
  font-size: 12px;
  line-height: 13px;
  /*-webkit-transition: background-color 0.25s ease-in;*/
  -moz-transition: background-color 0.25s ease-in;
  transition: background-color 0.25s ease-in;
  /*box-shadow: rgb(99, 97, 97) 0px 0px 4px;*/
  border-radius: 4px;
}

.aLabel {
  -webkit-transition: background-color 0.25s ease-in;
  -moz-transition: background-color 0.25s ease-in;
  transition: background-color 0.25s ease-in;
}

.aLabel.jtk-hover, .jtk-source-hover, .jtk-target-hover {
  background-color: #1e8151;
  color: white;
}

.aLabel {
  background-color: white;
  opacity: 0.8;
  padding: 0.3em;
  border-radius: 0.5em;
  border: 1px solid #346789;
  cursor: pointer;
}

.ep {
  position: absolute;
  bottom: 37%;
  right: 5px;
  width: 1em;
  height: 1em;
  background-color: orange;
  cursor: pointer;
  box-shadow: 0 0 2px black;
  -webkit-transition: -webkit-box-shadow 0.25s ease-in;
  -moz-transition: -moz-box-shadow 0.25s ease-in;
  transition: box-shadow 0.25s ease-in;
  right: -1px;
  left: -1px;
  width: auto;
  top: 10px;
  bottom: -1px;
  height: auto;
  background-color: white;
  box-shadow: none;
  opacity: 0;
  z-index: 1000;
}

.ep:hover {
  box-shadow: 0 0 6px black;
}

.statemachine-demo .jtk-endpoint {
  z-index: 3;
}

#opened {
  left: 10em;
  top: 5em;
}

#phone1 {
  left: 35em;
  top: 12em;
  width: 7em;
}

#inperson {
  left: 12em;
  top: 23em;
}

#phone2 {
  left: 28em;
  top: 24em;
}

#rejected {
  left: 10em;
  top: 35em;
}

.dragHover {
  border: 2px solid orange;
}

path, .jtk-endpoint {
  cursor: pointer;
}

.endpoint-current {
  background-color: #da6100 !important;
  border: 1px solid #bd5400 !important;
  color: white;
}


.endpoint-current-cnn {
  background-color: #da6100 !important;
  border: 1px solid #bd5400 !important;
  color: white;
}


svg[connection-current] path:nth-child(2) {
  stroke: #da6100;
}


svg[connection-current] path:nth-child(3) {
  stroke: #da6100;
  fill: #da6100;
}


.node-editor {
  position: absolute;
  left: 0px;
  top: 9px;
  border: solid 1px #fff;
  width: 100%;
  background: #da6100;
  color: #fff;
  z-index: 100000;
  text-align: center;
  font-size: inherit;
  display: none;
}


.node-connection-editor {
  top: 4px
}


.node-editor-editing {
  display: block;
}
