<template>
  <div>
    <el-cascader
      style="width: 100%"
      :options="options"
      v-model="value1"
      @change="handleChange"
      filterable
      :props="{ value: 'label' }"
      placeholder="省/市/区"
      :disabled="disabled"
      clearable
    ></el-cascader>
  </div>
</template>

<script>
// 引入省市区数据包
import { regionData } from "element-china-area-data";
export default {
  name: "cascader",
  props: ["value",  "disabled"],
  data() {
    return {
      options: regionData,
      selectedOptions: [],
    };
  },
  computed: {
    value1: {
      set(val) {
        this.$emit("input", val);
      },
      get(val) {
        return this.value;
      },
    },
  },
  methods: {
    handleChange(value) {
      this.$emit("provincesValueChang", value);
    },
  },
};
</script>

