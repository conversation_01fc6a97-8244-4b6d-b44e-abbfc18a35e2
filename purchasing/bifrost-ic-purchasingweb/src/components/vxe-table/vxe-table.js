/*
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-15 11:53:43
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-10 09:39:08
 */

// z主要兼容以前element的table方法
import { Table } from 'vxe-table'

const TablePathed = {
  extends: Table,
  data() {
    return {
    }
  },
  methods: {
    clearSelection() {
      const result = this.clearCheckboxRow()
      this.$emit('selection-change', [])
      return result
    },
    toggleRowSelection(row, flag) {
      if (this.$isEmpty(flag)) {
        this.toggleCheckboxRow(row)
      } else {
        this.setCheckboxRow(row, flag)
      }
      if (
        !this.checkboxConfig.trigger || this.checkboxConfig.trigger === 'default'
      ) {
        this.$emit('selection-change', { row })
      }
    }
  }
}

export default {
  install(Vue) {
    Vue.component(Table.name, TablePathed)
  }
}
