// 重写vxetable的样式

.vxe-table {
  font-family: PingFangSC-Medium;
  .vxe-header--row {
    background: #F0F5FF;
    .vxe-header--column {
      font-weight: 600;
      color: #333333;
    }
  }
}
.vxe-table--render-default .vxe-body--row.row--checked, .vxe-table--render-default .vxe-body--row.row--radio {
  background-color: #F4FCFF;
}
.vxe-table--render-default.border--full .vxe-body--column, .vxe-table--render-default.border--full .vxe-footer--column, .vxe-table--render-default.border--full .vxe-header--column {
  background-image: linear-gradient(#DDDDDD,#DDDDDD),linear-gradient(#DDDDDD,#DDDDDD);
}

.vxe-table.vxe-table--render-default.size--mini {
  .vxe-header--row th {
    height: 30px;
  }
  .vxe-cell {
    padding: 4px;
  }
}