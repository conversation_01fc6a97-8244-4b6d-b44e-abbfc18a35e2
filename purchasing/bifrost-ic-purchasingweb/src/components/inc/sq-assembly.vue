<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-18 09:05:00
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-09 17:31:19
-->
<template>
  <div :class="isCformPath?'cformDetailsAssembly':'contractAssembly'">
    <base-attachment ref="baseAttachment" ></base-attachment>
  </div>
</template>

<script>

export default{
  name: '事前申请单',
  data() {
    return {
      isCformPath: window.location.href.includes('/cform'),
      attList: [], // 第一次加载的附件数据
      isError: false, // 是否发生过错误
      isMonthlyPayment: false, // 是否按月付款
      needInitPlanData: true,
      initPlanIndex: 3
    }
  },
  methods: {
    showError(result) {
      this.isError = true
      return 0
    },
    initAssembly(meta, mode, formFormat) {
      this.$refs.baseAttachment.$children[0].btAddText = '上传附件'
      this.$refs.baseAttachment.$children[0].attTypeTableName = 'ELE_SQ_ATT_TYPE'
      this.$refs.baseAttachment.$children[0].initMeta(meta)

      // 附件区块按钮栏顶部隐藏默认的padding
      this.$nextTick(() => {
        this.$refs.baseAttachment.setButtonNormalNoPaddingTop(true)
      })
      // 初始化数据
      this.attList = []
      this.isError = false
    },
    getExtData() {
      const extData = {}
      return extData
    },
    fillAtt(dataVo) {
      this.$fillAtt(this, dataVo)
    }
  }
}
</script>

<style scoped lang="scss">
  .base-attachment {
    padding: 10px 0px 0px;
    height: calc((100% - 20px) / 3 + 10px);
  }
  .contractAssembly {
    height: 100%;
    width: 100%;
    //padding: 0px 0px 0px 10px;
  }
  .cformDetailsAssembly{
    height: 100%;
    width: 100%;
    border: none;
    padding-left: 10px;
    /deep/.is-scrollable{padding: 0;}
  }
  .el-tabs .el-tabs--top {
    height: 100%;
  }
  /deep/.el-tabs__content {
    height: calc(100% - 52px) !important;
  }
  .contractAssembly .el-tabs__content .el-tab-pane {
    height: 100%;
  }
  /deep/ .bottom-table{
    flex: 1;
    overflow: overlay;
  }
  /deep/ .el-table .cell{
    padding: 0px 5px !important;
  }
  /deep/ #ba-fileCategory .el-tabs__content {
    height: calc(100% - 45px) !important;
  }
</style>
