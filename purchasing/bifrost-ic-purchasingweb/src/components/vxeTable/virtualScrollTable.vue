<!--
 * @Author: your name
 * @Date: 2021-05-14 14:30:06
 * @LastEditTime: 2023-10-11 16:42:20
 * @LastEditors: liyongbo <EMAIL>
 * @Description:  当有分组表头时不支持横向虚拟滚动，需将 scroll-x={enabled: false} 关闭
 * @FilePath: \G3-trunk-others\src\components\vxeTable\virtualScrollTable.vue
-->
<template>
  <div :class="{'isDragging':isDragging}"  class="vTable-box">
    <vxe-table
      ref="vTable"
      :scroll-y="{ enabled: scrollY, gt: scrollYGt }"
      :scroll-x="{ enabled: scrollX, gt: scrollXGt }"
      :key="tbaleKey"
      :height="newHeight"
      :row-id="rowId"
      :border="border"
      :maxHeight="maxHeight"
      :autoResize="autoResize"
      :stripe="stripe"
      :resizable="resizable"
      :round="round"
      :size="size"
      :class="className"
      :loading="loading"
      :align="align"
      :header-align="headerAlign"
      :showHeader="showHeader"
      :highlight-hover-row="highlightHoverRow"
      :highlight-current-row="highlightCurrentRow"
      :highlight-current-column="highlightCurrentColumn"
      :highlight-hover-column="highlightHoverColumn"
      :show-overflow="showOverflow"
      :show-header-overflow="showHeaderOverflow"
      :checkbox-config="checkboxConfig"
      :openEdit="openEdit"
      :emptyText="emptyText"
      :show-footer="showFooter"
      :footer-method="footerMethod"
      :cell-style="cellStyle"
      :footer-cell-class-name="footerCellClassName"
      :footer-row-class-name="footerRowClassName"
      :header-cell-class-name="({ column }) => column.property"
      :span-method="spanMethod"
      @cell-click="cellClick"
      @cell-dblclick="dblCellClick2"
      @checkbox-change="checkboxChange"
      @checkbox-all="checkboxAll"
      @radio-change="radioChange"
      :tree-config="treeConfig"
      :sort-config="{
        remote:true,
        sortMethod: customSortMethod,
        trigger: sortTrigger,
        defaultSort: { field: defaultSortFieId, order: defaultSortOrder },
        orders: sortOrders,
      }"
    :filter-config="filterConfig"
      :row-config="{
        height: rowHeight,
        useKey: rowUseKey,
      }"
      @sort-change="sortChangeEvent"
      :sync-resize="syncResize"
      :data="tableData"
      :edit-config="editConfig"
      :edit-rules="validRules"
      :tooltip-config="tooltipConfig"
    >
      <!-- <vxe-table-column v-if="showSeq" type="seq" align='center' width="50"></vxe-table-column>
      <vxe-table-column v-if="showCheckbox" type="checkbox" align='center' width="50"></vxe-table-column> -->
      <template v-for="(item, index) in tableColumn">
        <template v-if="item.children">
          <vxe-table-colgroup
            :key="index"
            :width="item.width"
            :min-width="item.minWidth"
            :align="item.align"
            :header-align="item.center ? item.center : headerAlign"
            :title="item.label"
            :fixed="item.fixed"
          >
            <template v-for="(groupItem, groupIndex) in item.children">
              <template v-if="groupItem.children">
                <vxe-table-colgroup
                  :key="groupIndex"
                  :width="groupItem.width"
                  :min-width="groupItem.minWidth"
                  :align="groupItem.align"
                  :header-align="groupItem.center ? groupItem.center : headerAlign"
                  :title="groupItem.label"
                  :fixed="groupItem.fixed"
                >
                  <vxe-table-column
                    v-for="(thirdItem, thirdIndex) in groupItem.children"
                    :key="thirdIndex"
                    :type="thirdItem.type"
                    :width="thirdItem.width"
                    :min-width="thirdItem.minWidth"
                    :align="thirdItem.align"
                    :header-align="thirdItem.center ? thirdItem.center : headerAlign"
                    :title="thirdItem.label"
                    :fixed="thirdItem.fixed"
                    :formatter="thirdItem.formatter"
                    :sortable="thirdItem.sortable"
                    :sort-by="thirdItem.sortBy"
                    :sort-type="thirdItem.sortType"
                    :field="thirdItem.prop"
                    :visible="thirdItem.isShow"
                    :class-name="thirdItem.columnClassName"
                  >
                    <template #header="{ column, columnIndex }" v-if="item.header">
                      <slot
                        name="headerColumn"
                        :item="item"
                        :column="column"
                        :columnIndex="columnIndex"
                      >
                      </slot>
                    </template>
                    <template #default="{ row, rowIndex }" v-if="thirdItem.slotColumn">
                      <slot
                        name="slotColumn"
                        :item="thirdItem"
                        :row="row"
                        :rowIndex="rowIndex"
                      ></slot>
                    </template>
                  </vxe-table-column>
                </vxe-table-colgroup>
              </template>
              <vxe-table-column
                v-else
                :key="groupIndex"
                :type="groupItem.type"
                :width="groupItem.width"
                :min-width="groupItem.minWidth"
                :align="groupItem.align"
                :header-align="groupItem.center ? groupItem.center : headerAlign"
                :title="groupItem.label"
                :fixed="groupItem.fixed"
                :formatter="groupItem.formatter"
                :sortable="groupItem.sortable"
                :sort-by="groupItem.sortBy"
                :sort-type="groupItem.sortType"
                :field="groupItem.prop"
                :visible="groupItem.isShow"
                :class-name="groupItem.columnClassName"
              >
                <template #header="{ column, columnIndex }" v-if="item.header">
                  <slot
                    name="headerColumn"
                    :item="item"
                    :column="column"
                    :columnIndex="columnIndex"
                  >
                  </slot>
                </template>
                <template #default="{ row, rowIndex }" v-if="groupItem.slotColumn">
                  <slot
                    name="slotColumn"
                    :item="groupItem"
                    :row="row"
                    :rowIndex="rowIndex"
                  ></slot>
                </template>
              </vxe-table-column>
            </template>
          </vxe-table-colgroup>
        </template>
        <vxe-table-column
          v-else
          :key="index"
          :type="item.type"
          :width="item.width"
          :min-width="item.minWidth"
          :align="item.align"
          :header-align="item.center ? item.center : headerAlign"
          :title="item.label"
          :fixed="item.fixed"
          :formatter="item.formatter"
          :sortable="item.sortable"
          :sort-by="item.sortBy"
          :sort-type="item.sortType"
          :field="item.prop"
          :visible="item.isShow"
          :class-name="item.columnClassName"
          :edit-render="item.editRender"
          :tree-node="item.treeNode"
          :filters="item.filters"
          :filter-multiple="item.filterMultiple"
        >
          <template #header="{ column, columnIndex }" v-if="item.header">
            <slot
              name="headerColumn"
              :item="item"
              :column="column"
              :columnIndex="columnIndex"
            >
            </slot>
          </template>

          <template #filter="{ $panel, column, columnIndex }" v-if="item.filterColumn">
            <template v-for="(option, index) in column.filters">
              <slot
                name="filterColumn"
                :panel='$panel'
                :item="item"
                :column="column"
                :option="option"
                :columnIndex="columnIndex"
              >
              </slot>
            </template>
          </template>

          <template #default="{ row, rowIndex }" v-if="item.slotColumn">
            <slot
              name="slotColumn"
              :item="item"
              :row="row"
              :rowIndex="rowIndex"
            ></slot>
          </template>
          <template #default="{ row }" v-else-if="item.prop == 'wfStatusName' && !item.slotColumn">
            <div class="flex-box">
              <span 
                :class="[
                  'status',
                  {'color-#11ddcc': row.wfStatus==10 || row.wfStatus==20 || row.wfStatus==0}, // 10已创建/20已提交
                  {'color-#bbbbbb': row.wfStatus==11}, // 已撤回
                  {'color-#f53b3d': row.wfStatus==12}, // 已退回
                  {'color-#1f7fff': row.wfStatus==21}, // 审核中
                  {'color-#11bb77': row.wfStatus==90 || row.wfStatus==91}, // 90已审核/已办结
                ]"
              >
                &bull;
              </span>
              <span>{{ row.wfStatusName }}</span>
            </div>
          </template>
        </vxe-table-column>
      </template>
    </vxe-table>
   
  </div>

</template>
<script>
import props from "./props.js";
import Sortable from "./Sortable.js";
export default {
  props,
  components: {},
  data () {
    return {
      tbaleKey: new Date().getTime(),
      loading: true,
      isDragging:false,
      newHeight: this.height
    };
  },
  watch: {
    height(newvalue){
      this.newHeight = newvalue
    },
    tableColumn: {
      handler: function (newValue, oldValue) {
        this.tbaleKey = new Date().getTime();
      },
      immediate: true,
      deep: true
    },
     tableData: {
      handler: function (newValue, oldValue) {
        this.$nextTick(()=>{
          this.clearCurrentRow(newValue[0])
        })
      },
      immediate: true,
      deep: true
    }
  },
  created () {
    window.vTable = this;
  },
  mounted () {
    this.handleRowDrop();
  },
  beforeDestroy () {
    // 如果有拖拽插件内置对象，销毁
    if (this.sortableJs) this.sortableJs.destroy();
  },
  methods: {
    handleMouseDown(isDragging){
      // 当鼠标按下的时候，将文字禁止选中
      this.isDragging = isDragging
      
    },
    handleMouseUp(isDragging,differencePx){
      this.isDragging = isDragging
      this.newHeight  = (this.newHeight+differencePx) < 100 ?100 :(this.newHeight+differencePx) 
    },
    /**
     * @description: 重置表单事件
     */
    changeKey () {
      this.tbaleKey = new Date().getTime();
    },
    /**
     * @description: 关闭loading事件
     */
    closeLoading () {
      this.loading = false;
    },
    /**
     * @description: 开启loading事件
     */
    openLoading () {
      this.loading = true;
    },
    /**
     * @description: 暴露出来的清除所有行选中事件
     */
    clearCheckboxRow () {
      this.$refs.vTable.clearCheckboxRow();
    },
    /**
     * @description: 单元格被点击时会触发该事件
     * @param { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }
     */
    cellClick (obj) {
      this.$emit("cellClick", obj);
    },
    /**
     * @description: 单元格被双击时会触发该事件
     * @param: { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }
     */
    dblCellClick2 (obj) {
      this.$emit("dblCellClick", obj);
    },
    /**
     * @description: 当手动勾选并且值发生改变时触发的事件
     * @param { checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }
     */
    checkboxChange (obj) {
      this.$emit("checkboxChange", obj);
    },
    /**
     * @description: 只对 type=checkbox 有效，当手动勾选全选时触发的事件
     * @param：{ records, reserves, indeterminates, checked, $event }
     */
    checkboxAll (obj) {
      this.$emit("checkboxAll", obj);
    },
    /**
     * @description: 只对 type=radio 有效，当手动勾选并且值发生改变时触发的事件
     * @param：{ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }
     */
    radioChange (obj) {
      this.$emit("radioChange", {records:[obj.row]});
    },
    /**
     * @description: 用于 type=radio，手动设置用户的选择
     */
    setRadioRow (row) {
      this.$nextTick(()=>this.$refs.vTable.setRadioRow(row))
    },
    /**
     * @description: 用于 type=radio，手动清空用户的选择
     */
    clearRadioRow () {
      this.$refs.vTable.clearRadioRow()
    },
    /**
     * @description: 当排序条件发生变化时会触发该事件
     * @param { column, property, order, sortBy, sortList, $event }
     */
    sortChangeEvent (obj) {
      this.$emit("sortChangeEvent", obj);
    },
    /**
     * @description: 设置第几行展开
     */
    setTreeExpand (row, flag = true) {
      this.$refs.vTable.setTreeExpand(row, flag)
    },
     /**
     * @description: 设置第几行高亮
     */
    clearCurrentRow () {
      this.$refs.vTable.clearCurrentRow()
    },
    /**
     * @description: 列表行拖拽调整排序功能实现
     */
    handleRowDrop() {
      // 没有配置，则后续代码不执行
      if (!this.rowUseKey) return false;
      this.$nextTick(() => {
        // 初始化插件拖拽行功能
        this.sortableJs = Sortable.create(this.$refs.vTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
          handle: '.drag-btn',
          onEnd: ({ newIndex, oldIndex }) => {
            const currRow = this.tableData.splice(oldIndex, 1)[0]
            this.tableData.splice(newIndex, 0, currRow)
          }
        })
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.vTable-box{
  position: relative;
  .isDragging{
    position: relative;
    cursor:  row-resize!important;
    user-select: none;
    overflow: hidden;
  }
}
::v-deep .vxe-icon--caret {
  &-bottom:before {
    border-width: 0.32em;
  }
  &-top:before {
    border-width: 0.32em;
  }
}
::v-deep .vxe-checkbox--icon:before{
  border:  1px solid #dcdfe6!important;
}
.flex-box{
  display: flex;
  justify-content: center;
  .status{
    // font-size: 32px;
    margin-top: -3px;
    margin-right: 8px;
  }
  .color-\#11ddcc{
    color: #11ddcc;
  }
  .color-\#bbbbbb{
    color: #bbbbbb;
  }
  .color-\#f53b3d{
    color: #f53b3d;
  }
  .color-\#1f7fff{
    color: #1f7fff;
  }
  .color-\#11bb77{
    color: #11bb77;
  }
}
</style>
