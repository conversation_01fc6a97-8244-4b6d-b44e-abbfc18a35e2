<!--
 * @Author: your name
 * @Date: 2021-05-14 14:30:06
 * @LastEditTime: 2023-12-28 18:05:13
 * @LastEditors: lijing<PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \g3-asset-web-micro\src\components\vxeTable\baseTable.vue
-->
<template>
  <div :class="{ isDragging: isDragging }" class="xTable-box">
    <vxe-table
      ref="xTable"
      :data="tableData"
      :key="tbaleKey"
      :height="newHeight"
      :maxHeight="maxHeight"
      :border="border"
      :autoResize="autoResize"
      :stripe="stripe"
      :resizable="resizable"
      :round="round"
      :size="size"
      :class="className"
      :loading="loading"
      :align="align"
      :radio-config="radioConfig"
      :span-method="spanMethod"
      :row-id="rowId"
      :header-align="headerAlign"
      :showHeader="showHeader"
      :highlight-hover-row="highlightHoverRow"
      :highlight-current-row="highlightCurrentRow"
      :highlight-current-column="highlightCurrentColumn"
      :highlight-hover-column="highlightHoverColumn"
      :show-overflow="showOverflow"
      :show-header-overflow="showHeaderOverflow"
      :openEdit="openEdit"
      :emptyText="emptyText"
      :show-footer="showFooter"
      :footer-method="footerMethod"
      :footer-cell-class-name="footerCellClassName"
      :footer-row-class-name="footerRowClassName"
      :header-cell-class-name="({ column }) => column.property"
      @cell-click="cellClick"
      @cell-dblclick="dblCellClick2"
      @checkbox-change="checkboxChange"
      @checkbox-all="checkboxAll"
      @radio-change="radioChange"
      :tree-config="treeConfig"
      :checkbox-config="checkboxConfig"
      :row-config="rowConfig"
      :sort-config="{
        sortMethod: customSortMethod,
        trigger: sortTrigger,
        defaultSort: { field: defaultSortFieId, order: defaultSortOrder },
        orders: sortOrders,
        remote: remote,
      }"
      @sort-change="sortChangeEvent"
      :sync-resize="syncResize"
      :edit-config="editConfig"
      :edit-rules="validRules"
      :tooltip-config="tooltipConfig"
    >
      <template v-for="(item, index) in tableColumn">
        <template v-if="item.children">
          <vxe-table-colgroup
            :key="index"
            :width="item.width"
            :min-width="item.minWidth"
            :align="item.align"
            :header-align="item.center ? item.center : headerAlign"
            :title="item.label"
            :fixed="item.fixed"
          >
            <vxe-table-column
              v-for="(groupItem, groupIndex) in item.children"
              :key="groupIndex"
              :type="groupItem.type"
              :width="groupItem.width"
              :min-width="groupItem.minWidth"
              :align="groupItem.align"
              :header-align="groupItem.center ? groupItem.center : headerAlign"
              :title="groupItem.label"
              :fixed="groupItem.fixed"
              :formatter="groupItem.formatter"
              :sortable="groupItem.sortable"
              :sort-by="groupItem.sortBy"
              :sort-type="groupItem.sortType"
              :field="groupItem.prop"
              :visible="groupItem.isShow"
              :tree-node="groupItem.treeNode"
              :class-name="groupItem.columnClassName"
            >
              <template #header="{ column, columnIndex }" v-if="item.header">
                <slot
                  name="headerColumn"
                  :item="item"
                  :column="column"
                  :columnIndex="columnIndex"
                >
                </slot>
              </template>
              <template
                #default="{ row, rowIndex }"
                v-if="groupItem.slotColumn"
              >
                <slot
                  name="slotColumn"
                  :item="groupItem"
                  :row="row"
                  :rowIndex="rowIndex"
                ></slot>
              </template>
            </vxe-table-column>
          </vxe-table-colgroup>
        </template>
        <vxe-table-column
          v-else
          :key="index"
          :type="item.type"
          :width="item.width"
          :min-width="item.minWidth"
          :align="item.align"
          :header-align="item.center ? item.center : headerAlign"
          :title="item.label"
          :field="item.prop"
          :fixed="item.fixed"
          :formatter="item.formatter"
          :sortable="item.sortable"
          :sort-by="item.sortBy"
          :sort-type="item.sortType"
          :visible="item.isShow"
          :tree-node="item.treeNode"
          :class-name="item.columnClassName"
          :edit-render="item.editRender"
        >
          <template #header="{ column, columnIndex }" v-if="item.header">
            <slot
              name="headerColumn"
              :item="item"
              :column="column"
              :columnIndex="columnIndex"
            >
            </slot>
          </template>
          <template #default="{ row, rowIndex }" v-if="item.slotColumn">
            <slot
              name="slotColumn"
              :item="item"
              :row="row"
              :rowIndex="rowIndex"
            >
            </slot>
          </template>

          <template
            #default="{ row }"
            v-else-if="item.prop == 'wfStatusName' && !item.slotColumn"
          >
            <div class="flex-box">
              <span
                :class="[
                  'status',
                  {
                    'color-#11ddcc':
                      row.wfStatus == 10 ||
                      row.wfStatus == 20 ||
                      row.wfStatus == 0,
                  }, // 10已创建/20已提交
                  { 'color-#bbbbbb': row.wfStatus == 11 }, // 已撤回
                  { 'color-#f53b3d': row.wfStatus == 12 }, // 已退回
                  { 'color-#1f7fff': row.wfStatus == 21 }, // 审核中
                  { 'color-#11bb77': row.wfStatus == 90 || row.wfStatus == 91 }, // 90已审核/已办结
                ]"
              >
                &bull;
              </span>
              <span>{{ row.wfStatusName }}</span>
            </div>
          </template>
        </vxe-table-column>
      </template>
    </vxe-table>
  </div>
</template>
<script>
import props from "./props.js";
export default {
  props,
  components: {  },
  data() {
    return {
      tbaleKey: new Date().getTime(),
      loading: false,
      isDragging: false,
      newHeight: this.height,
    };
  },
  watch: {
    height(newValue) {
      this.newHeight = newValue;
    },
    tableColumn: {
      handler: function (newValue, oldValue) {
        this.tbaleKey = new Date().getTime();
      },
      immediate: true,
      deep: true,
    },
    tableData: {
      handler: function (newValue, oldValue) {
        this.$nextTick(() => {
          this.clearCurrentRow();
        });
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    window.baseTable = this;
  },
  methods: {
    handleMouseDown(isDragging) {
      console.log(this.newHeight);
      // 当鼠标按下的时候，将文字禁止选中
      this.isDragging = isDragging;
    },
    handleMouseUp(isDragging, differencePx) {
      this.isDragging = isDragging;
      this.newHeight =
        this.newHeight + differencePx < 100
          ? 100
          : this.newHeight + differencePx;
    },
    /**
     * @description: 重置表单事件
     */
    changeKey() {
      this.tbaleKey = new Date().getTime();
    },
    /**
     * @description: 关闭loading事件
     */
    closeLoading() {
      this.loading = false;
    },
    /**
     * @description: 开启loading事件
     */
    openLoading() {
      this.loading = true;
    },
    /**
     * @description: 暴露出来的清除所有行选中事件
     */
    clearCheckboxRow() {
      this.$refs.xTable.clearCheckboxRow();
    },
    /**
     * @description: 单元格被点击时会触发该事件
     * @param { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }
     */
    cellClick(obj) {
      this.$emit("cellClick", obj);
    },
    /**
     * @description: 单元格被双击时会触发该事件
     * @param: { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }
     */
    dblCellClick2(obj) {
      this.$emit("dblCellClick", obj);
    },
    /**
     * @description: 当手动勾选并且值发生改变时触发的事件
     * @param { records, checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }
     */
    checkboxChange(obj) {
      this.$emit("checkboxChange", obj);
    },
    /**
     * @description: 只对 type=checkbox 有效，当手动勾选全选时触发的事件
     * @param：{ records, reserves, indeterminates, checked, $event }
     */
    checkboxAll(obj) {
      this.$emit("checkboxAll", obj);
    },
    /**
     * @description: 只对 type=radio 有效，当手动勾选并且值发生改变时触发的事件
     * @param：{ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }
     */
    radioChange(obj) {
      this.$emit("radioChange", { records: [obj.row] });
    },
    /**
     * @description: 用于 type=radio，手动设置用户的选择
     */
    setRadioRow(row) {
      this.$nextTick(() => this.$refs.xTable.setRadioRow(row));
    },
    /**
     * @description: 用于 type=radio，手动清空用户的选择
     */
    clearRadioRow() {
      this.$refs.xTable.clearRadioRow();
    },
    /**
     * @description: 当排序条件发生变化时会触发该事件
     * @param { column, property, order, sortBy, sortList, $event }
     */
    sortChangeEvent(obj) {
      this.$emit("sortChangeEvent", obj);
    },
    /**
     * @description: 设置第几行展开
     */
    setTreeExpand(row, flag = true) {
      this.$refs.xTable.setTreeExpand(row, flag);
    },
    /**
     * @description: 展开所有
     */
    setAllTreeExpand(flag = true) {
      this.$refs.xTable.setAllTreeExpand(flag);
    },
    /**
     * @description: 高亮行
     * @param { column, property, order, sortBy, sortList, $event }
     */
    setCurrentRow(row) {
      this.$refs.xTable.setCurrentRow(row);
    },
    clearCurrentRow(row) {
      this.$refs.xTable.clearCurrentRow();
    },
  },
};
</script>
<style lang="scss" scoped>
.xTable-box {
  position: relative;
  .isDragging {
    position: relative;
    cursor: row-resize !important;
    user-select: none;
    overflow: hidden;
  }
}

::v-deep .vxe-icon--caret {
  &-bottom:before {
    border-width: 0.32em;
  }
  &-top:before {
    border-width: 0.32em;
  }
}
::v-deep .vxe-checkbox--icon:before {
  border: 1px solid #dcdfe6 !important;
}

.flex-box {
  display: flex;
  justify-content: center;
  .status {
    // font-size: 32px;
    margin-top: -3px;
    margin-right: 4px;
  }
  .color-\#11ddcc {
    color: #11ddcc;
  }
  .color-\#bbbbbb {
    color: #bbbbbb;
  }
  .color-\#f53b3d {
    color: #f53b3d;
  }
  .color-\#1f7fff {
    color: #1f7fff;
  }
  .color-\#11bb77 {
    color: #11bb77;
  }
}
</style>