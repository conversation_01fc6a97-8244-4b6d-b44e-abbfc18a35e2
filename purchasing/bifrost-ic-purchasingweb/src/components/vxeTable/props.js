/*
 * @Author: your name
 * @Date: 2021-05-14 16:00:01
 * @LastEditTime: 2023-11-10 19:07:51
 * @LastEditors: x<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \G3-trunk-others\src\components\vxeTable\props.js
 */
const props = {
  /**
   * @description: 表格数据
   */
  tableData: {
    type: Array,
    default: () => [],
  },
  /**
   * @description: 表格栏
   */
  tableColumn: {
    type: Array,
    default: () => [],
  },
  /**
   * @description: 默认显示序列号
   */
  showSeq: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 自动跟随某个属性的变化去重新计算表格
   */
  syncResize: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 默认显示复选框
   */
  showCheckbox: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 表格的class类名
   */
  className: [String, Array],
  /**
   * @description: 纵向虚拟滚动配置（不支持深层结构和展开行）
   */
  scrollY: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 指定大于指定行时自动启动纵向虚拟滚动，如果为 0 则总是启用，如果为 -1 则关闭（注：启用纵向虚拟滚动之后将不能支持动态行高）
   */
  scrollYGt: {
    type: Number,
    default: 10,
  },
  /**
   * @description: 横向虚拟滚动配置（不支持深层结构和展开行）
   */
  scrollX: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 指定大于指定列时自动启动横向虚拟滚动，如果为 0 则总是启用，如果为 -1 则关闭（注：启用横向虚拟滚动之后将不能支持分组表头）
   */
  scrollXGt: {
    type: Number,
    default: 10,
  },
  radioConfig: Object,
  /**
   * @description: 表格的高度；支持铺满父容器或者固定高度，如果设置为 auto，则必须确保存在父节点且不允许存在相邻元素，auto, %, px
   */
  height: [Number, String],
  /**
   * @description: 只对 show-overflow 有效，每一行的高度, Number
   */
  rowHeight: {
    type: [Number],
    default: 40,
  },
  rowConfig: {
    type: Object,
    default: ()=> {
      return {
        height: 40,
        
      }
    }
  },
  /**
   * @description: 是否开启每一行的 VNode 设置 key 属性（非特殊情况下不需要使用）
   */
  rowUseKey: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 表格的最大高度，%, px
   */
  maxHeight: [Number, String],
  /**
   * @description: 自动监听父元素的变化去重新计算表格
   */
  autoResize: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 是否带有边框, full（完整边框）, outer（外边框）, inner（内边框）, none（无边框）
   */
  border: {
    type: [Boolean, String],
    default: true,
  },
     /**
   * @description: 合并单元格
   * @param: ({ columns, data }) => any[][]
   */
     spanMethod: Function,
  /**
   * @description: 所有的列是否允许拖动列宽调整大小, 默认 true
   */
  resizable: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 是否带有斑马纹（在可编辑表格场景下，临时插入的数据不会有斑马纹样式）
   */
  stripe: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 是否为圆角边框
   */
  round: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 表格的尺寸，medium, small, mini
   */
  size: String,
  /**
   * @description: 所有的列对齐方式 left（左对齐）, center（居中对齐）, right（右对齐）
   */
  align: {
    type: String,
    default: "left",
  },
  /**
   * @description: 开启树形懒加载
   */
  rowId: {
    type: String,
    default: "bizid",
  },
  /**
   * @description: 	所有的表头列的对齐方式 left（左对齐）, center（居中对齐）, right（右对齐）
   */
  headerAlign: {
    type: String,
    default: "center",
  },
  /**
   * @description: 是否显示表头
   */
  showHeader: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 是否要高亮当前行
   */
  highlightCurrentRow: {
    type: Boolean,
    default: true,
  },
  /**
   * @description: 鼠标移到行是否要高亮显示
   */
  highlightHoverRow: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 是否要高亮当前列
   */
  highlightCurrentColumn: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 鼠标移到列是否要高亮显示
   */
  highlightHoverColumn: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 设置所有内容过长时显示为省略号 ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
   */
  showOverflow: {
    type: [Boolean, String],
    default: "tooltip",
  },
  showHeaderOverflow: {
    type: [Boolean, String],
    default: "tooltip",
  },
  /**
   * @description: 是否需要编辑
   */
  openEdit: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 空数据显示的内容
   */
  emptyText: String,
  /**
   * @description: 是否显示表尾
   */
  showFooter: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 表尾的数据获取方法，返回一个二维数组
   * @param: ({ columns, data }) => any[][]
   */
  footerMethod: Function,
   /**
   * @description: 表尾的数据获取方法，返回一个二维数组
   * @param: ({ columns, data }) => any[][]
   */
   spanMethod: Function,
  /**
   * @description: 给表尾的行附加 className
   * @param: ({ columns, data }) => any[][]
   * @return string
   */
  footerRowClassName: Function,
  /**
   * @description: 给表尾的单元格附加 className
   * @param: (({ $rowIndex, column, columnIndex, $columnIndex }) => any)
   * @return string
   */
  footerCellClassName: Function,
   /**
   * @description: 给单独行样式
   * @param: (({ $rowIndex, column, columnIndex, $columnIndex }) => any)
   * @return string
   */
   cellStyle: Function,
  /**
   * @description: 给单元格附加样式 any | (({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex }) => any)
   */
  // dblCellClick: Function,
  treeConfig: Object,
  /**
   * @description: 设置父子节点不互相关联
   */
  checkboxConfig: Object,
  /**
   * @description: 校验规则配置
   */
  validRules: Object,
  /**
   * @description: 单元格工具提示
   */
  tooltipConfig: Object,
  /**
   * @description: 指定列 tree-node 属性来开启树表格
   */
  treeNode: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 触发方式（注：当多种功能重叠时，会同时触发）default（点击按钮触发）, cell（点击单元格触发）, row（点击行触发）
   */
  treeTrigger: {
    type: String,
    default: "cell",
  },
  /**
   * @description: 默认展开所有子孙树节点（只会在初始化时被触发一次）
   */
  expandAll: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 默认展开指定树节点（只会在初始化时被触发一次，需要有 row-id）
   */
  expandRowKeys: [String, Array],
  /**
   * @description: 对于同一级的节点，每次只能展开一个
   */
  accordion: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 只对 lazy 启用后有效，标识是否存在子节点，从而控制是否允许被点击
   */
  treeHasChild: {
    type: String,
    default: "hasChild",
  },
  /**
   * @description: 是否使用懒加载（启用后只有指定 hasChild 的节点才允许被点击）
   */
  lazy: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 该方法用于异步加载子节点（必须返回 Promise<any[]> 对象）
   * @param: ({ row }) => Promise<any[]>
   */
  loadMethod: Function,
  /**
   * @description: 是否开启排序
   */
  sortable: {
    type: Boolean,
    default: false,
  },
  /**
   * @description: 	只对 sortable 有效，指定排序的字段（当值 formatter 格式化后，可以设置该字段，使用值进行排序）
   * @param：string | (({ row, column }) => string | number)
   */
  sortBy: String,
  /**
   * @description: 排序的字段类型，比如字符串转数值等 auto, number, string
   * @param {*}
   * @return {*}
   */
  sortType: {
    type: String,
    default: "auto",
  },
  /**
   * @description: 全局排序方法，当触发排序时会调用该函数返回排序后的列表
   * @param {*} ({ data, column, property, order }) => any[]
   * @return {*}
   */
  customSortMethod: Function,
  /**
   * @description: 触发方式（注：当多种功能重叠时，会同时触发）
   * @param: default（点击按钮触发）, cell（点击表头触发）
   */
  sortTrigger: {
    type: String,
    default: "cell",
  },
  /**
   * @description: 默认排序的列名称 string
   */
  defaultSortFieId: String,
  /**
   * @description: 默认排序方式  asc（升序）,desc（降序）, null
   */
  defaultSortOrder: {
    type: String,
    default: "null",
  },
  /**
   * @description: 自定义轮转顺序  asc（升序）,desc（降序）, null
   */
  sortOrders: {
    type: Array,
    default: () => {
      return ["asc", "desc", "null"];
    },
  },
  /**
   * @description: 所有列是否使用服务端排序，如果设置为 true 则不会对数据进行处理
   */
  remote: {
    type: Boolean,
    default: true
  },
  /**
  * @description: 表头筛选的自定义配置项
  * @param {*} { remote, filterMethod, showRotate, iconNone, iconMatch }
  * remote any:boolean 所有列是否使用服务端筛选，如果设置为 true 则不会对数据进行处理
  * filterMethod any:({ options, values, cellValue, row, column }) => boolean 全局筛选方法，当触发筛选时会调用该函数返回是否有效
  * showRotate any:boolean 是否显示列头筛选图标
  * iconNone any:string 默认显示的图标 vxe-icon--xxx
  * iconMatch any:string 匹配选项高亮时显示的图标
  */
  filterConfig: {
    type: Object,
    default: () => {
      return { remote:true };
    }
  },
  /**
   * @description: 可编辑配置项
   */
  editConfig: {
    type: Object,
    default: () => {
      return {};
    },
  },
};

const columnProps = {
  /**
   * @description: 列的类型，seq（序号） checkbox（复选框） radio（单选框） expand（展开行）html（HTML 标签）
   * @type string
   */
  type: "",
  /**
   * @description: 列宽度
   * @type  number | string px, %
   */
  width: "",
  /**
   * @description: 将列固定在左侧或者右侧 left（固定左侧）, right（固定右侧）
   * @type string
   */
  fixed: "left",
  /**
   * @description: 列对齐方式left（左对齐）, center（居中对齐）, right（右对齐）  继承 table.align
   * @type string
   */
  align: "",
  /**
   * @description: 格式化显示内容 ({ cellValue, row, column }) => any | any[] | string
   */
  formatter: () => {},
};

export default props;
