/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-18 15:37:17
 * @LastEditTime: 2024-01-03 14:50:18
 * @LastEditors: liji<PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \G3-trunk-others\src\components\vxeTable\formats.js
 */
import XEUtils from "xe-utils";
import {
  Time,
  formatCurrency,
  formatNumberRgx,
  getDate24Hours,
  useType,
} from "@/utils/index";
/**
 * @description: 时间类型转换字段
 */
export const timeType = [
  "bizEndTime",
  "repairStartTime",
  "repairEndTime",
  "actionEndTime",
  "planCompleteTime",
  "actionStartTime",
  "planStartTime",
  "billDate",
  "approvalDate",
  "getDate",
  "firstMonth",
  "investDate",
  "uploadTime",
  "inverstDate",
  "completionPlanDate",
  "planCommissionDate",
  "completionRealityDate",
  "startBuildingDate",
  "changeDate",
  "voucherDate",
  "voucher",
  "entryDate",
  "exceptGetDate",
  "applyTime",
  "checkDate",
  "auditTime",
  "receiveUnitRecordTime",
  "applyDate",
  "depInitDate",
  "deprInitMonth",
  "validDate",
  "distributeDate",
  "disposeVoucherTime",
  "accountTime",
  "disposeTime",
  "gainDate",
  "handDate",
  "startDate",
  "endDate",
  "supportDate",
  "lastMaintenanceData",
  "feedBackDate",
  "preDate",
  "checkTime",
];
/**
 * @description: 货币类型转换字段
 */
export const currencyType = [
  "actualAssetValue",
  "actualAssetCount",
  "monthAccDep",
  "quotaPrice",
  "quotaMoney",
  "price",
  "other_assets",
  "asset_no_patent",
  "asset_patent",
  "no_asset_sum",
  "fixation_asset",
  "flow_asset",
  "total",
  "change_amount_dispose",
  "total_amount",
  "start_invest_amount",
  "paper_amout",
  "fixation_amount",
  "change_amount_those_year",
  "total_inverst_amount",
  "plan_inverst_amout",
  "other_room_other_area",
  "other_room_free_area",
  "otheRoomLease",
  "other_room_self_area",
  "other_room_total_area",
  "biz_room_other_area",
  "biz_room_free_area",
  "bizRoomLease",
  "biz_room_self_area",
  "biz_room_total_area",
  "office_other_area",
  "office_free_area",
  "officeLendLease",
  "office_self_area",
  "office_total_area",
  "land_area_work",
  "land_area",
  "main_dep_money",
  "land_area_other",
  "land_area_unused",
  "land_area_letloan",
  "land_area_work",
  "subtotal",
  "asset_value",
  "totalYield",
  "financeSupTotal",
  "paperAmout",
  "disposeAmount",
  "startInvestAmount",
  "totalInvestLot",
  "roomArea",
  "bizValue",
  "businessMoney",
  "taxValueTotal",
  "ungainValueTotal",
  "gainValueTotal",
  "needGainValueTotal",
  "retainValueTotal",
  "unhandValueTotal",
  "handValueTotal",
  "needHandValueTotal",
  "leaseArea",
  "amountIndex",
  "actualAmount",
  "actualValue",
  "valueIndex",
  "initValue",
  "deprMoneyTotal",
  "equipnmentValue",
  "beginValue",
  "beginDepValue",
  "beginNetValue",
  "currentAddValue",
  "addDepValue",
  "addNetValue",
  "currentReduceValue",
  "reduceDepValue",
  "reduceNetValue",
  "endValue",
  "endDepValue",
  "endNetValue",
  "adminInitValue",
  "bizInitValue",
  "initAssetVal",
  "finMoney",
  "fundMoney",
  "otherMoney",
  "otherSupTotal",
  "initAssetValSum",
  "deprSupMoney",
  "adminSupTotal",
  "sciSupTotal",
  "finSupTotal",
  "bizSupTotal",
  "finTotalMoney",
  "fundTotalMoney",
  "otherTotalMoney",
  "deprMoney",
  "accDep",
  "adminProTotal",
  "bizProTotal",
  "sciProTotal",
  "finProTotal",
  "fundProTotal",
  "financeProTotal",
  "otherProTotal",
  "countMoney",
  "adminTotal",
  "bizTotal",
  "finTotal",
  "fundTotal",
  "otherTotal",
  "totalDepMoney",
  "totalNetMoney",
  "adminDep",
  "bizDep",
  "finDep",
  "fundDep",
  "otherDep",
  "remainingValue",
  "adminNet",
  "bizNet",
  "finNet",
  "fundNet",
  "otherNet",
  "residualValue",
  "adjMoney",
  "admValue",
  "bisValue",
  "reFinancialFund",
  "reScientificFund",
  "reOtherFund",
  "financialDep",
  "scientificDep",
  "otherDep",
  "netValue",
  "financialNet",
  "scientificNet",
  "init_value",
  "sum(init_value)",
  "mainDepMoney",
  "currValue",
  "netVal",
  "otherMoney",
  "scienceMoney",
  "financeMoney",
  "repairCosts",
  "maintenanceBudget",
  "averageValue",
  "assetValue",
  "inverstAmount",
  "curr_value",
  "depr_month_money",
  "depr_total_money",
  "sum(depr_total_money)",
  "sum(curr_value)",
  "totalAmount",
  "deprTotalMoney",
  "unit_price",
  "estimateValue",
  "planAmountTotal",
  "sumAmountTotal",
  "fixedAmountTotal",
  "currYearAmount",
  "fixationAmount",
  "planInverstAmout",
  "totalInverstAmount",
  "landArea",
  "changeAmount",
  "changeLot",
  "attachOldValue",
  "attachNewValue",
  "average_value",
  "total_money",
  "sum(total_money)",
  "remaining_value",
  "sum(remaining_value)",
  "attachPreValue",
  "max_value",
  "avg_value",
  "min_value",
  "depr_month_money",
  "needGainValue",
  "gainValue",
  "ungainValue",
  "taxValue",
  "retainValue",
  "needHandValue",
  "handValue",
  "unhandValue",
  "area",
  "secondCount",
  "totalValue",
  "attachValues",
  "totalMoney",
  "mofApp",
  "nonMofApp",
  "estiYearProf",
  "estiYearChar",
  "debtAmt",
  "debtReplyAmt",
  "debtRemainAmt",
  "assetCount",
  "numOrArea",
  // 统计报表 - 自定义报表
  "fglxMoney",
  "ffglxMoney",
  "zczjMoney",
  "kyzjOldMoney",
  "zyzjMoney",
  "kyzjMoney",
  "qtzjMoney",
  "accDepVal",
  "syflInitAssetVal",
  "syflyxzInitAssetVal",
  "zysbInitAssetVal",
  "syflxgInitAssetVal",
  "syflxgyxzInitAssetVal",
  "zysbxgInitAssetVal",
  "ffgAccDepVal",
  "fgAccDepVal",
  "kyAccDepVal",
  "kyOldAccDepVal",
  "zyAccDepVal",
  "zcAccDepVal",
  "qtAccDepVal",
  "gdInitAssetVal",
  "gdNetVal",
  "gdNumOrArea",
  "gdTotalAccDep",
  "totalAccDep",
  "wxInitAssetVal",
  "wxNetVal",
  "wxTotalAccDep",
  "wxNumOrArea",
  "gdzcAccDep",
  "gdzcInitAssetVal",
  "gdzcNetVal",
  "gdzcNumOrArea",
  "wxzcAccDep",
  "wxzcInitAssetVal",
  "wxzcNetVal",
  "wxzcNumOrArea",
  "totalInitAssetVal",

  "totalNetVal",
  "fgInitAssetVal",
  "fgAccDep",
  "fgNetVal",
  "devInitAssetVal",
  "devAccDep",
  "devNetVal",
  "jyInitAssetVal",
  "jyAccDep",
  "jyNetVal",
  "wxAccDep",
  "tdInitAssetVal",
  "tdAccDep",
  "tdNetVal",
];
export const ThreeFunds = ["newText", "oldText", "newValue", "oldValue"];
/**
 * @description: 编号转换字段
 */
export const numberRgx = [
  "cardQuantity",
  "asset_count",
  "sum(asset_count)",
  "sum(depr_use_limit)",
  "sum(file_count)",
  "sum(depr_months)",
  "attachValue",
];
/**
 * @description: 时间转换字段
 */
export const timeRgx = ["deprMonth"];
/**
 * @description: 使用状态（字典）
 */
export const fixedAssetStateCode = [
  {
    dictName: "在用",
    dictValue: "1",
  },
  {
    dictName: "出租出借",
    dictValue: "2",
  },
  {
    dictName: "出租出借",
    dictValue: "2",
  },
  {
    dictName: "闲置",
    dictValue: "3",
  },
  {
    dictName: "待处置",
    dictValue: "4",
  },
  {
    dictName: "盘亏",
    dictValue: "5",
  },
  {
    dictName: "逾期在用",
    dictValue: "6",
  },
  // {
  //   dictName: "在用",
  //   dictValue: "21"
  // },
  // {
  //   dictName: "出租",
  //   dictValue: "22"
  // },
  // {
  //   dictName: "出借",
  //   dictValue: "23"
  // },
  // {
  //   dictName: "闲置",
  //   dictValue: "24"
  // },
  // {
  //   dictName: "待报废",
  //   dictValue: "25"
  // },
  // {
  //   dictName: "质押",
  //   dictValue: "26"
  // },
  // {
  //   dictName: "对外投资",
  //   dictValue: "27"
  // },
  // {
  //   dictName: "在建",
  //   dictValue: "28"
  // },
  // {
  //   dictName: "待修",
  //   dictValue: "29"
  // },
  // {
  //   dictName: "维修",
  //   dictValue: "31"
  // },
  // {
  //   dictName: "报废",
  //   dictValue: "41"
  // },
  // {
  //   dictName: "报损",
  //   dictValue: "42"
  // },
  // {
  //   dictName: "调拨",
  //   dictValue: "43"
  // },
  // {
  //   dictName: "出让",
  //   dictValue: "44"
  // },
  // {
  //   dictName: "捐赠",
  //   dictValue: "45"
  // },
  // {
  //   dictName: "置换",
  //   dictValue: "46"
  // },
  // {
  //   dictName: "差错更正",
  //   dictValue: "47"
  // },
  // {
  //   dictName: "其他处置",
  //   dictValue: "91"
  // },
];

export const formats = {
  // 格式化三元类型 :formatter="['formatTernary', '1', '男', '女']"
  formatTernary({ cellValue, column }, key, definiteValue, negativeValue) {
    return String(cellValue)
      ? cellValue == key
        ? definiteValue
        : negativeValue
      : "";
  },
  // 格式化日期，默认 yyyy-MM-dd HH:mm:ss
  formatDate({ cellValue }, format) {
    return XEUtils.toDateString(cellValue, format || "yyyy-MM-dd HH:mm:ss");
  },
  // 四舍五入金额，每隔3位逗号分隔，默认2位数
  formatAmount({ cellValue }, digits = 2) {
    return XEUtils.commafy(XEUtils.toNumber(cellValue), { digits });
  },
  // 格式化银行卡，默认每4位空格隔开
  formatBankcard({ cellValue }) {
    return XEUtils.commafy(XEUtils.toValueString(cellValue), {
      spaceNumber: 4,
      separator: " ",
    });
  },
  // 四舍五入,默认两位数
  formatFixedNumber({ cellValue }, digits = 2) {
    return XEUtils.toFixed(XEUtils.round(cellValue, digits), digits);
  },
  // 向下舍入,默认两位数
  formatCutNumber({ cellValue }, digits = 2) {
    return XEUtils.toFixed(XEUtils.floor(cellValue, digits), digits);
  },
  //财务状况转换
  formatFinancialStatus({ cellValue }) {
    return financialStatusFun(cellValue);
  },
  //时间类型转换  ${year}-${month}-${day}
  formatTime({ cellValue }) {
    return Time(cellValue);
  },
  //货币类型转换
  formatCurrency({ cellValue }) {
    return formatCurrency(cellValue);
  },
  //编号转换
  formatNumberRgx({ cellValue }) {
    return formatNumberRgx(cellValue);
  },
  //时间转换 years - month - day hours : minutes : seconds;
  formatDateHours({ cellValue }) {
    return getDate24Hours(cellValue);
  },
  //价值类型转换
  formatValueType({ cellValue }) {
    return valueType.filter((e) => e.dictValue == cellValue)[0]?.["dictName"];
  },
  //资产状态转换
  formatFixedAssetStateCode({ cellValue }) {
    return fixedAssetStateCode.filter((e) => e.dictValue == cellValue)[0]?.[
      "dictName"
    ];
  },
  //使用方向转换
  formatUseType({ cellValue }) {
    return useType().get(cellValue);
  },
  // 三项资金
  formatThreeFunds({ cellValue, row }) {
    if (
      ["initAssetVal", "mofApp", "nonMofApp", "monthAccDep"].includes(
        row.changeField
      )
    ) {
      return formatCurrency(cellValue);
    } else {
      return cellValue;
    }
  },
};
/**
 * @description: 通用formatter方法
 * @param {*} list  需要格式化的tableColumn
 * @param {*} that  Vue实例
 * 20220623 列表数据格式化：前端只对 数字类型做千分号和保留两位小数转换，如数量，金额，面积
 */
export function breakdownFormat(list, that) {
  for (const item of list) {
    if (currencyType.indexOf(item.prop) > -1) {
      item.formatter = "formatCurrency";
    }
    if (numberRgx.indexOf(item.prop) > -1) {
      item.formatter = "formatNumberRgx";
    }
    if (timeRgx.indexOf(item.prop) > -1) {
      item.formatter = "formatTime";
    }
    if (ThreeFunds.indexOf(item.prop) > -1) {
      item.formatter = "formatThreeFunds";
    }
    // 为第二级列表字段添加format
    if (item?.children?.length > 0) {
      for (const oitem of item.children) {
        if (currencyType.indexOf(oitem.prop) > -1) {
          oitem.formatter = "formatCurrency";
        }
        if (numberRgx.indexOf(oitem.prop) > -1) {
          oitem.formatter = "formatNumberRgx";
        }
        if (timeRgx.indexOf(oitem.prop) > -1) {
          oitem.formatter = "formatTime";
        }
      }
    }
  }
  return list;
}
