<template>
  <el-dialog title="文件预览"
             :visible.sync="dialogVisible"
             destroy-on-close
             append-to-body
             :before-close="handleClose"
             width="80%">
    <div style="height: 80vh" v-if="dialogVisible">
      <el-container style="height: 100%; border: 1px solid #eee">
        <el-aside style="background-color: rgb(238, 241, 246);width: 170px;padding-top: 20px;">
          <el-upload v-if="isShowUpload"
                     action :auto-upload='false' :show-file-list="false"
                     :on-change="uploadFile"
                     :multiple="false">
            <el-button size="small" style="width: 200px" type="primary"><i
              class="el-icon-upload el-icon--right">点击上传</i></el-button>
          </el-upload>

          <div v-show="file" :class="`fileViewContainer ${file.fileClass?file.fileClass:''}`"
               v-for="(file,index) in fileViews"
               :key="`fileViewFile${index}`"
               @click="fileViewContainerClick(file)">
            <div class="fileViewFile" :style="file.css"/>
            <div class="fileViewTitle" :title="file.fileName">{{ file.fileName }}</div>
          </div>
        </el-aside>
        <el-main class="fileView" v-loading="htmlLoading"
                 element-loading-text="正在解析文件，请稍等。"
                 element-loading-spinner="el-icon-loading">
          <h3 align="center" style=" position: absolute;top: 20px;">{{ fileView.fileName }}</h3>
          <div v-html="getFileHtml" v-if="fileView.showType==='html'">
          </div>
          <iframe :src="getViewUrl" width="100%" v-if="fileView.showType==='pdf'" height="99%" scrolling="yes">
          </iframe>
          <div v-for="(img, index) in fileView.urls" :key="index">
            <el-image :src="img" v-if="fileView.showType==='imgs'">
              <div slot="error" style="height: 100%;width: 100%">
                <i class="el-icon-picture-outline" style="font-size: 100px;text-align: center">图片加载失败，请联系管理员。</i>
              </div>
            </el-image>
          </div>
          <el-image :src="getViewUrl" v-if="fileView.showType==='img'">
            <div slot="error" style="height: 100%;width: 100%">
              <i class="el-icon-picture-outline" style="font-size: 100px;text-align: center">图片加载失败，请联系管理员。</i>
            </div>
          </el-image>
          <div v-if="fileView.showType==='ofd' && showOfd">
            <ofd :ofdObj="ofdObj"/>
          </div>
          <div id="fileExtInfo">
            <ul>
              <!-- item是数组里面的元素  index是数组的下标 -->
              <li v-for="(item,index) in fileExtInfos[fileView.fileId]" :key="index">-->{{ item }}</li>
            </ul>
          </div>
          <div>
            <li v-for="(item,index) in this.einvoiceFolderReferenceDesc" :key="index">-->该发票已被{{item.formType}} (<span style="text-decoration: underline;text-decoration-color: dodgerblue;color: dodgerblue;cursor: pointer;" @click="viewCformData(item.cformDataId,item.metaName)">{{item.code}}</span>) 关联,使用金额:{{$formatMoney(item.value)}}</li>
          </div>
        </el-main>

      </el-container>
    </div>
  </el-dialog>
</template>

<script>
import { downloadFile, selectByIds, getFileLIst } from '../../api/file/file'
import _ from 'lodash'

export default {
  name: 'file-view',
  props: {
    isShowUpload: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      htmlLoading: false,
      dialogVisible: false,
      fileViews: [],
      officeIconMap: new Map(),
      fileDownloadByFileIdExtData: {},
      fileView: {
        html: '', fileName: '', fileId: '',
        url: '/subsytem/platform/pdf/web/viewer.html',
        showType: 'html',
        fileClass: ''
      },
      fileExtInfos: { // file额外的信息
        id: ['发票电子签章验证通过/不通过']
      },
      ofdObj: {},
      showOfd: false,
      tempFile: {},
      debounce: undefined,
      einvoiceFolderReferenceDesc: []
    }
  },
  computed: {
    getFileHtml() {
      return this.fileView.html
    },
    getViewUrl() {
      return this.fileView.url
    },
    getViewUrls() {
      return this.fileView.urls
    }
  },
  methods: {
    init() {
      this.dialogVisible = false
      const style = 'align-content: center ;float:left; background-size:100% 100%;width:150px;height:130px;cursor:pointer'
      this.officeIconMap.set('doc', 'background: url(' + require('@/assets/fileView/images/word.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('docx', 'background: url(' + require('@/assets/fileView/images/docx.png') + ') no-repeat 0px 0px;' + style)
      this.officeIconMap.set('xls', 'background: url(' + require('@/assets/fileView/images/xls.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('xlsx', 'background: url(' + require('@/assets/fileView/images/xlsx.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('pptx', 'background: url(' + require('@/assets/fileView/images/pptx.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('ppt', 'background: url(' + require('@/assets/fileView/images/ppt.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('pdf', 'background: url(' + require('@/assets/fileView/images/pdf.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('txt', 'background: url(' + require('@/assets/fileView/images/txt.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('rar', 'background: url(' + require('@/assets/fileView/images/rar.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('zip', 'background: url(' + require('@/assets/fileView/images/zip.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('jpg', 'background: url(' + require('@/assets/fileView/images/jpg.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('jpeg', 'background: url(' + require('@/assets/fileView/images/jpeg.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('png', 'background: url(' + require('@/assets/fileView/images/png.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('gif', 'background: url(' + require('@/assets/fileView/images/gif.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('bmp', 'background: url(' + require('@/assets/fileView/images/bmp.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('ofd', 'background: url(' + require('@/assets/fileView/images/ofd.png') + ' ) no-repeat 0px 0px;' + style)
      this.officeIconMap.set('PDF', 'background: url(' + require('@/assets/fileView/images/pdf.png') + ' ) no-repeat 0px 0px;' + style)
    },
    showDetailDialog(exportPram) {
      var row = exportPram.row
      var item = exportPram.item
      var fileViewObj = exportPram.fileView
      var attId = row.attId
      if (!attId) {
        var dataRef = item.columnEntity.dataRef // 可以设置多个，用冒号隔开，用于参数拓展
        attId = row[dataRef]
      }
      if (row.attType === '1') { // 发票预览时需组装发票额外显示的数据
        const fileExtInfos = {}
        const ext = row.attName.substring(row.attName.lastIndexOf('.') + 1)
        // 签章信息不为空时才组装数据 暂时只支持pdf
        const signatureInfo = row.signatureInfo
        if (this.$isNotEmpty(signatureInfo) && (ext === 'pdf' || ext === 'PDF')) {
          fileExtInfos[attId] = [signatureInfo]
        }
        this.resultAttributes['fileExtInfos'] = fileExtInfos
      }
      this.$fileDownloadByFileId(
        fileViewObj, row.attName, attId, this.resultAttributes)
    },
    cleanInitData() {
      this.fileViews = []
      this.fileView = { html: '', fileName: '', url: '', showType: 'html' }
      this.dialogVisible = true
    },
    open(findParam) {
      this.cleanInitData()
      if (!findParam) {
        this.$message.warning('预览附件参数不能为空，请使用AttachmentInfo作为查询条件{fileIds:[],bizDataId:""}。')
        return
      }
      this.fileExtInfos = findParam.fileExtInfos ? findParam.fileExtInfos : []
      this.dialogVisible = false
      new Promise(re => {
        const ids = findParam.fileIds ? new Set(findParam.fileIds) : new Set()
        this.selectEinvoiceReference(ids)
        if (findParam.bizDataId) {
          getFileLIst(findParam).then(result => {
            const data = result.data
            if (data.success && data.data) {
              data.data.list.forEach(file => ids.add(file.id))
            }
            re(ids)
          })
        } else {
          re(ids)
        }
      }).then(ids => {
        if (ids) {
          ids = Array.from(ids)
          selectByIds(ids).then(result => {
            const data = result.data
            //判断data.data[0].fileExtend是否包含zip
            if(data.data[0].fileExtend.includes("zip")){
              this.$message.warning('您所选择的文件格式不支持在线预览，请下载后查看');
            }else {
              this.dialogVisible = true
            }
            if (data.success && data.data) {
              const fileMap = new Map()
              data.data.forEach((file, index) => {
                const viewData = {
                  css: this.officeIconMap.get(file.fileName.substring(file.fileName.lastIndexOf('.') + 1)),
                  fileName: file.fileName,
                  id: file.id,
                  fileClass: ''
                }
                fileMap.set(file.id, viewData)
              })
              ids.forEach((id, index) => {
                const viewData = fileMap.get(id)
                this.fileViews.push(viewData)
                // 默认打开第一个
                if (index === 0) {
                  viewData.fileClass = 'currentFile'
                  this.fileToView(viewData)
                }
              })
            }
          })
        }
      })
      // this.dialogVisible = true
    },
    fileViewContainerClick(file) {
      this.fileViews.forEach(fl => {
        fl.fileClass = ''
      })
      file.fileClass = 'currentFile'
      this.tempFile = file
      this.debounce()
    },
    handleClick() {
      this.fileToView(this.tempFile)
      this.fileViewByDownload(this.tempFile)
    },
    fileViewByDownload(file) {
      // 确定是通过下载的方式进行预览时，才响应双击事件
      var fileName = file.fileName
      var isWordExcelClick2Download =
        this.fileDownloadByFileIdExtData['WordExcel点击预览触发下载']
      if (this.$isNotEmpty(fileName) &&
        isWordExcelClick2Download === true &&
        this.$isFileUseDownload2Preview(fileName)) {
        this.$fileDownloadByFileId(
          {}, fileName, file.id, this.fileDownloadByFileIdExtData)
      }
    },
    uploadFile(file, fileList) {
      this.fileViews.push({
        css: this.officeIconMap.get(file.name.substring(file.name.lastIndexOf('.') + 1)),
        fileName: file.name,
        blob: file.raw
      })
    },
    fileToViewCall(file) {
      this.htmlLoading = true
      const fd = new FormData()
      const ext = file.fileName.substring(file.fileName.lastIndexOf('.') + 1)
      fd.append('file', file.blob, file.fileName)
      fd.append('fileId', file.id)
      const isBlob = ext === 'ofd' || ext === 'doc' || ext === 'docx'
      const requestParam = {
        url: `${process.env.VUE_APP_API_IC}/bifrost/cloud/api.do?apiKey=fileToView`,
        method: 'post',
        data: fd
      }
      if (isBlob) {
        Object.assign(requestParam, { responseType: 'blob', data: fd })
      } else {
        Object.assign(requestParam, fd)
      }

      this.$callApi('fileToView', fd, result => {
        if (result.success) {
          this.htmlLoading = false
          let blob = []
          if (isBlob) {
            var fileName = file.fileName.replace(/%/g, '%25')
            if (result.data?.type === 'png') {
              this.fileView.urls = []
              this.fileView.showType = 'imgs'
              result.data.list.forEach(res => {
                const base64 = `data:image/png;base64,${res}`
                this.fileView.urls.push(base64)
              })
            } else {
              var bstr = atob(result.data.html)
              var n = bstr.length
              var u8arr = new Uint8Array(n)
              while (n--) {
                u8arr[n] = bstr.charCodeAt(n)
              }
              // 创建Blob对象
              blob = new Blob([u8arr], { type: 'application/pdf' })
              this.fileView.url = '/subsytem/platform/pdf/web/viewer.html?file=' + URL.createObjectURL(blob) + '&_file_name= ' + fileName + '.pdf'
              this.fileView.showType = 'pdf'
            }
          } else {
            this.fileView.showType = 'html'
            this.fileView.html = result.data.html
          }
          const fileType = result.data.fileType || ''
          if (fileType === 'xlsx' || fileType === 'xls') {
            this.$nextTick(this.setExcelTab)
          }
        }
        return true
      }, result => {
        this.htmlLoading = false
      }, { isSave: false })
    },
    setExcelTab() {
      document.querySelectorAll('.tabs').forEach(function(tab) {
        tab.addEventListener('click', function(e) {
          e = e || window.event
          var target = e.target || e.srcElement

          if (target.nodeName.toLowerCase() === 'a') {
            var index = Array.from(target.parentNode.parentNode.children).indexOf(target.parentNode)

            target.parentNode.classList.add('tab-active')
            Array.from(target.parentNode.parentNode.children).forEach(function(sibling) {
              if (sibling !== target.parentNode) {
                sibling.classList.remove('tab-active')
              }
            })

            var tables = document.querySelectorAll('.excelDefaults')
            tables.forEach(function(table, i) {
              if (i === index) {
                table.classList.add('table-active')
              } else {
                table.classList.remove('table-active')
              }
            })
          }
        })
      })
    },
    fileToView(file) {
      downloadFile(file.id).then(res => {
        if (res && res.status) {
          file.blob = res.data
          var fileName = file.fileName.replace(/%/g, '%25')
          const ext = file.fileName.substring(file.fileName.lastIndexOf('.') + 1)
          switch (ext) {
            case 'webp':
            case 'WMF':
            case 'raw':
            case 'ai':
            case 'eps':
            case 'ufo':
            case 'dxf':
            case 'pcd':
            case 'cdr':
            case 'psd':
            case 'svg':
            case 'fpx':
            case 'exif':
            case 'tga':
            case 'pcx':
            case 'gif':
            case 'tiff':
            case 'bmp':
            case 'jpg':
            case 'jpeg':
            case 'png':
              this.fileView.url = window.URL.createObjectURL(file.blob)
              this.fileView.showType = 'img'
              break
            case 'xls':
            case 'xlsx':
              this.fileToViewCall(file)
              break
            case 'doc':
            case 'docx':
              this.fileToViewCall(file)
              break
            case 'ppt':
            case 'pptx':
              this.fileToViewCall(file)
              break
            case 'ofd':
              this.fileToViewCall(file)
              // this.showOfD(file)
              break
            case 'pdf':
            case 'PDF':
              this.fileView.url = '/subsytem/platform/pdf/web/viewer.html?file=' + URL.createObjectURL(file.blob) + '&_file_name=' + fileName + '.pdf'
              this.fileView.showType = 'pdf'
              break
            case 'txt':
              this.fileToString(file.blob)
              break
            default:
          }
          this.fileView.fileName = fileName
          this.fileView.fileId = file.id
          this.fileView.css = this.officeIconMap.get(ext)
        }
      })
    },
    fileToString(blob) {
      const reader = new FileReader()
      reader.readAsText(blob, 'utf-8')
      reader.onload = () => {
        this.fileView.showType = 'html'
        this.fileView.html = reader.result
      }
    },
    showOfD(file) {
      this.fileView.showType = 'ofd'
      this.showOfd = true
      this.ofdObj = {
        file: new File([file.blob], file.fileName, { type: 'application/ofd' }),
        name: file.fileName
      }
    },
    handleClose() {
      this.showOfd = false
      this.dialogVisible = false
    },
    selectEinvoiceReference(ids) {
      this.$callApiParams('selectEinvoiceReference', { 'ids': Array.from(ids).join(',') },
        result => {
          if (result.success && result.data) {
            this.einvoiceFolderReferenceDesc = result.data
          }
          return true
        })
    },
    viewCformData(cformDataId, name) {
      this.$showDetail(cformDataId, undefined, undefined, name)
    }
  },
  created() {
    this.init()
    // 防止重复点击ofd
    this.debounce = _.debounce(this.handleClick, 300)
  }
}
</script>

<style scoped>
.fileViewContainer {
  height: 130px;
  width: 120px;
  margin-left: 25px;
  text-align: center;
  cursor: pointer;
  position: relative;
  border: 1px solid rgb(238, 241, 246);
  border-radius: 3px;
  margin-bottom: 2px;
}

.fileViewContainer:hover {
  border: 1px solid #bbb;
}

.fileViewContainer.currentFile {
  border: 1px solid #aaa;
  box-shadow: 0px 0px 1px 0px rgb(0, 0, 0);
}

.fileViewContainer .fileViewFile {
  position: absolute;
  width: 80px !important;
  height: 80px !important;
  float: none !important;
  left: 20px;
  top: 15px;
}

.fileViewContainer .fileViewTitle {
  position: absolute;
  width: 100%;
  top: 95px;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 20px;
  white-space: nowrap;
}
</style>
