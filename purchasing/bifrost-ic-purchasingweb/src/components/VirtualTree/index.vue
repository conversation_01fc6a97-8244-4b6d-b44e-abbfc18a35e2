<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-23 13:26:48
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-07 14:28:23
-->
<template>
  <div
    class="virtual-tree"
    ref="scroller"
    @scroll="handleScroll"
    :style="{ height: treeBoxHeight + 'px' }"
  >
    <div class="virtual-tree-phantom" :style="{ height: contentHeight }"></div>
    <div
      class="virtual-tree-content"
      :style="{ transform: `translateY(${offset}px)` }"
    >
      <template v-for="(item, index) in visibleData">
        <div
          :key="item.id || Math.random()"
          @click.stop="nodeClick(item)"
          :class="{
            'virtual-tree-node': true,
            'is-current': item.id === currentData.id,
          }"
          :style="{
            paddingLeft: 18 * (item.level - 1) + 'px',
            minHeight: itemHeight + 'px',
          }"
        >
          <i
            :class="[
              item.expand
                ? 'el-icon-caret-bottom'
                : 'el-icon-caret-right',
              !showExpand(item) ? 'is-leaf' : '',
            ]"
            @click.stop="e => toggleExpand(!showExpand(item), item, e)"
          />
          <el-checkbox
            v-if="showCheckbox"
            v-model="item.checked"
            :indeterminate="item.indeterminate"
            :disabled="!!item.disabled"
            @click.native.stop
            @change="check => handleCheckChange(check, item)"
          >
          </el-checkbox>
          <span
            v-if="item.loading"
            class="virtual-tree-node-loading-icon el-icon-loading"
          >
          </span>
          <slot name="content" :item="item" :index="index">
            <span>{{ item.label }}</span>
          </slot>
        </div>
      </template>
      <!-- 远程搜索用 -->
      <slot name="lastBtn"></slot>
    </div>

    <div v-if="visibleData.length === 0" class="no-data">无数据</div>
  </div>
</template>

<script>
  export default {
    name: 'virtualTree',
    props: {
      tree: {
        type: Array,
        required: true,
        default: () => [],
      },
      option: {
        type: Object,
        default: () => ({
          label: 'label',
          value: 'value',
        }),
      },
      //树容器高度
      treeBoxHeight: {
        type: [String, Number],
        default: 250,
      },
      //节点高度
      itemHeight: {
        type: Number,
        default: 35,
      },
      //节点左边距
      paddingLeft: {
        type: Number,
        default: 18,
      },
      //是否开启懒加载
      lazy: {
        type: Boolean,
        default: false,
      },
      //异步插入数据方法
      load: {
        type: Function,
      },
      // //是否显示复选框
      showCheckbox: {
        type: Boolean,
        default: false,
      },
      //默认展开节点
      // 传入类型为父节点的id
      expandKeys: {
        type: Array,
        default: () => [],
      },
      //默认选中节点
      checkedKeys: {
        type: Array,
        default: () => [],
      },
      //开启多选
      multiple: {
        type: Boolean,
        default: false,
      },
      //设置父子节点不关联
      checkStrictly: {
        type: Boolean,
        default: false,
      },
      //只能选中末级，用于非复选框属性
      checkLastLevel: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        contentHeight: '0px',
        offset: 0,
        visibleData: [],
        currentData: {},
      }
    },
    computed: {
      /**
       * @description: 调用flatten方法将多维数组转成扁平数组，并给每个节点添加一些基础属性
       * @param：{ Object } this.tree - 传入的树节点对象
       * @param：{ String } childKey 默认为children
       * @param：{ Number } level node层级 默认1
       * @param：{ Object } parent 节点父级
       * @returns: { Object } 转换后的扁平数组
       */
      flattenTree() {
        let that = this
        const flatten = function(
          list,
          childKey = 'children',
          level = 1,
          parent = null
        ) {
          let arr = []
          list.forEach(item => {
            item.id = item[that.option.value]
            // item.label = item[that.option.label]
            item.label = that.option.code
              ? `${item[that.option.code]}-${item[that.option.label]}`
              : item[that.option.label]
            item.checked = !!item.checked
            item.indeterminate = !!item.indeterminate
            item.disabled = !!item.disabled
            item.level = level
            if (item.visible === undefined) {
              item.visible = true
            }
            if (!parent.visible || !parent.expand) {
              item.visible = false
            }
            item.parent = parent
            arr.push(item)
            if (item[childKey]) {
              arr.push(...flatten(item[childKey], childKey, level + 1, item))
            }
          })
          return arr
        }
        return flatten(this.tree, 'children', 1, {
          level: 0,
          visible: true,
          expand: true,
          children: this.tree,
        })
      },
      /**
       * @description: 默认展示三屏数据
       * @returns: { Number } 展示的数据总数
       */
      visibleCount() {
        const height = this.$refs.scroller?.offsetHeight || 250
        return Math.floor(height / this.itemHeight) * 3
      },
    },
    mounted() {
      this.updateView()
    },
    watch: {
      /**
       * @description: 监听tree变化更新视图
       */
      tree: {
        handler: function(val) {
          this.updateView()
          this.$nextTick(() => {
            this.handleExpand()
          })
        },
        deep: true,
      },
      /**
       * @description: 默认展开节点
       */
      expandKeys: {
        handler: function() {
          this.updateView()
          this.$nextTick(() => {
            this.handleExpand()
          })
        },
        deep: true,
        immediate: true,
      },
      /**
       * @description: 设置默认选中
       */
      checkedKeys: {
        handler: function(val) {
          this.setCurrent(val)
        },
        deep: true,
      },
    },
    methods: {
      /**
       * @description: 默认展开节点事件
       */
      handleExpand() {
        const filterNode = this.flattenTree.filter(c =>
          this.expandKeys.find(k => k === c.id)
        )
        filterNode.forEach(node => {
          this.toggleExpand(false, node, {})
        })
      },
      /**
       * @description: 判断是否需要展开数据
       * @param：{ Object } node - 传入的树节点对象
       * @returns: { Boolean } 为true时展开，为false则隐藏
       */
      showExpand(node) {
        if (this.lazy) {
          return !node.isLeaf
        } else {
          return node.children && node.children.length
        }
      },
      /**
       * @description: 点击节点的回调
       * @param：{ Object } node - 传入的树节点对象
       * @listens 触发父组件node-click事件
       * @listens-> @retuen { Object [] } 返回选中节点的数组
       */
      nodeClick(node) {
        let data = []
        if (this.showCheckbox) {
          if (!this.checkStrictly) {
            this.handleCheckChange(!node.checked, node, true)
          } else {
            node.checked = !node.checked
          }
          data = this.getCheckedNodes()
        } else {
          if (this.checkLastLevel) {
            if (node.children && node.children.length > 0) return
          }
          this.currentData = node
          data = [node]
          this.$emit('nodeClickBlur')
        }
        console.log(data, 'nodeclick')
        this.$emit('node-click', data)
        this.$emit('node-click-blur', data)
      },
      /**
       * @description: 更新视图，包括更新内容区域的高度, 触发滑动事件
       */
      updateView() {
        this.getContentHeight()
        this.handleScroll()
      },
      /**
       * @description: 更新内容区域的高度
       */
      getContentHeight() {
        this.contentHeight =
          (this.flattenTree || []).filter(c => c.visible).length *
            this.itemHeight +
          'px'
      },
      /**
       * @description: 滑动事件
       */
      handleScroll() {
        const height = this.$refs.scroller?.offsetHeight || 250
        const scrollTop = this.$refs.scroller?.scrollTop - height
        window.requestAnimationFrame(() => {
          this.updateVisibleData(scrollTop > 0 ? scrollTop : 0)
        })
      },
      /**
       * @description: 更新可视区域的数据
       */
      updateVisibleData(scrollTop = 0) {
        const start = Math.floor(scrollTop / this.itemHeight)
        const end = start + this.visibleCount
        const allVisibleData = (this.flattenTree || []).filter(c => c.visible)
        this.visibleData = allVisibleData.slice(start, end)
        this.offset = start * this.itemHeight
      },
      /**
       * @description: 下拉展开或合上
       * @param: { Boolean } isE 是否已展开，展开为 true，合上则为 false
       * @param: { Object } node 当前节点对象
       */
      toggleExpand(isE, node) {
        if (isE) return
        if (!this.lazy) {
          const isExpand = node.expand
          if (isExpand) {
            this.collapse(node, true)
          } else {
            if (node.children && node.children.length) {
              this.expand(node, true)
            }
          }
          this.updateView()
        } else {
          const isExpand = node.expand
          if (isExpand) {
            this.collapse(node, true)
            this.updateView()
          } else {
            if (node.children && node.children.length) {
              this.expand(node, true)
              this.updateView()
            } else {
              // 异步加载节点
              node.loading = true
              this.load(node)
                .then(data => {
                  node.loading = false
                  node.children = data
                  if (data.length === 0) {
                    node.isLeaf = true
                  }
                  this.expand(node, true)
                  this.updateView()
                })
                .catch(() => {
                  node.loading = false
                  node.children = []
                  node.isLeaf = true
                  this.expand(node, true)
                  this.updateView()
                })
            }
          }
        }
      },
      /**
       * @description: 展开节点
       * @param: { Object } node 当前节点对象
       */
      expand(node) {
        node.expand = true
        node.visible = true
        this.recursionVisible(node.expand, node.children, true)
      },
      /**
       * @description: 折叠节点
       * @param: { Object } node 当前节点对象
       */
      // 折叠节点
      collapse(node) {
        node.expand = false
        this.recursionVisible(node.expand, node.children, false)
      },
      /**
       * @description: 递归子节点设置visible状态
       * @param: { Boolean } expand 判断节点是否显示，为true时显示，为false则隐藏
       */
      recursionVisible(expand, children, status) {
        children.forEach(node => {
          node.visible = expand ? status : false
          if (node.children) {
            this.recursionVisible(node.expand, node.children, status)
          }
        })
      },
      /**
       * @description: 折叠所有
       * @param: { Number } level 隐藏!=level的全部层级
       */
      collapseAll(level = 1) {
        this.flattenTree.forEach(node => {
          node.expand = false
          if (node.level !== level) {
            node.visible = false
          }
        })
        this.updateView()
      },
      /**
       * @description: 展开所有
       */
      expandAll() {
        this.flattenTree.forEach(node => {
          node.expand = true
          node.visible = true
        })
        this.updateView()
      },
      /**
       * @description: 清空选中
       */
      clearSelected() {
        this.currentData = {}
        if (this.showCheckbox) {
          this.flattenTree.map(e => {
            e.checked = false
            e.indeterminate = false
          })
        }
        this.updateView()
      },
      /**
       * @description: 设置值默认选中
       * @param: { Object | String } current 选中的值
       */
      setCurrent(current, noListens) {
        if (current) {
          let checkNode = []
          if (Object.prototype.toString.call(current) === '[object Array]') {
            checkNode = this.flattenTree.filter(c => current.includes(c.id))
          } else if (
            Object.prototype.toString.call(current) === '[object Object]'
          ) {
            this.currentData = current
            checkNode = [current]
          } else {
            checkNode = this.flattenTree.filter(c => current == c.id)
            this.currentData = checkNode
          }
          if (this.showCheckbox) {
            checkNode.forEach(node => {
              this.handleCheckChange(true, node)
            })
          } else {
            if (!noListens) {
              this.$emit('node-click', checkNode)
            }
          }
          this.updateView()
        }
      },
      /**
       * @description: checkbox选中事件
       * @param: { Boolean } check 是否勾选
       * @param: { Object } node 当前节点
       * @param: { Boolean } noListens 是否关闭监听
       * @listens 触发父组件 check 事件
       * @listens-> @retuen { Object {} } check：选中状态，currentData：当前节点，checked：所有选中数组，halfChecked：获取半选中状态下的节点
       */
      handleCheckChange(check, node, noListens) {
        if (typeof node === 'string') {
          const label = node
          node = this.flattenTree.filter(c => c.label === label)[0]
        }
        node.checked = check
        node.indeterminate = false
        if (!this.checkStrictly) this.handleParentChange(check, node)
        if (!this.checkStrictly) this.handleChildrenChange(check, node)
        this.updateView()
        if (noListens) return
        this.$emit('check', {
          check: check,
          currentData: node,
          checked: this.getCheckedNodes(),
          halfChecked: this.getHalfCheckedNodes(),
        })
      },
      /**
       * @description: 处理父节点选中状态
       * @param: { Booblean } check 选中状态
       * @param: { Object } node 传入的树节点
       */
      handleParentChange(check, node) {
        const recursionCheck = parent => {
          const checkLength = parent.children.filter(node => !!node.checked)
          const allEmpty = parent.children.every(node => !node.checked)
          const allCheck = parent.children.every(node => !!node.checked)
          const otherCheck =
            checkLength.length > 0 &&
            checkLength.length < parent.children.length
          const otherIndeterminate = parent.children.filter(
            node => !!node.indeterminate
          )
          if (allCheck) {
            parent.indeterminate = false
            parent.checked = allCheck
          }
          if (allEmpty) {
            parent.indeterminate = false
            parent.checked = !allEmpty
          }
          if (
            otherCheck ||
            (otherIndeterminate.length > 0 &&
              otherIndeterminate.length < parent.children.length)
          ) {
            parent.checked = false
            parent.indeterminate = true
          }
          if (
            parent.parent &&
            parent.id &&
            parent.level !== 0 &&
            parent.parent.children &&
            parent.parent.children.length
          ) {
            recursionCheck(parent.parent)
          }
        }
        if (
          node.parent &&
          node.id &&
          node.level !== 0 &&
          node.parent.children &&
          node.parent.children.length
        ) {
          recursionCheck(node.parent)
        }
      },
      /**
       * @description: 设置子节点选中状态
       * @param: { Booblean } check 选中状态
       * @param: { Object } node 传入的树节点
       */
      handleChildrenChange(check, node) {
        const recursionCheck = childNodes => {
          childNodes.forEach(node => {
            node.checked = check
            if (node.children && node.children.length) {
              recursionCheck(node.children)
            }
          })
        }
        if (node.children && node.children.length) {
          recursionCheck(node.children)
        }
      },
      /**
       * @description: 获取当前选中的节点
       */
      getCheckedNodes() {
        return this.flattenTree.filter(c => c.checked)
      },
      /**
       * @description: 获取indeterminate(半选中)状态下的节点
       */
      getHalfCheckedNodes() {
        return this.flattenTree.filter(c => c.indeterminate)
      },
      /**
       * @description: 节点过滤方法，默认字符匹配
       * @param: { String } val
       */
      filter(val) {
        if (!val) {
          this.flattenTree.map(e => (e.visible = true))
          this.collapseAll()
        } else {
          this.flattenTree.map(e => (e.visible = e.label.includes(val)))
          this.updateView()
        }
      },
      /**
       * @description: 显示所有节点
       */
      showAllNode() {
        this.flattenTree.map(e => (e.visible = true))
        this.updateView()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .virtual-tree {
    position: relative;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      -webkit-appearance: none;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background-color: #999;
    }
    &-phantom {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      z-index: -1;
    }
    &-content {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      min-height: 100px;
    }
    &-node {
      display: flex;
      cursor: pointer;
      color: #606266;
      box-sizing: border-box;
      font-weight: normal;
      align-items: center;
      font-family: Microsoft YaHei;
      .el-checkbox {
        margin-right: 8px !important;
      }
      &.is-current {
        background-color: #f2f7ff;
        color: #409eff;
      }
      &-loading-icon {
        width: 15px;
        height: 15px;
        line-height: 15px;
        text-align: center;
        margin: 5px;
      }
      i {
        padding: 6px;
        cursor: pointer;
        color: #c0c4cc;
        // font-size: 16px;
        padding: 6px;
        font-size: 20px;
        transition: all 0.2s ease;
        &.is-expand {
          transform: rotateZ(90deg);
        }
        &.is-leaf {
          color: transparent;
          cursor: default;
        }
      }
      &:hover {
        background: #e5f1fe;
      }
    }
    & .no-data {
      text-align: center;
      color: #999;
      // font-size: 14px;
      font-weight: normal;
    }
  }
  ::v-deep {
  .el-tree-node__content > .el-tree-node__expand-icon {
    padding: 6px;
    font-size: 20px;
  }
  // .el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  //   background-color: #fff!important;
  // }
}
</style>
