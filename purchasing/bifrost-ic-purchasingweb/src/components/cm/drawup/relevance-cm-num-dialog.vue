<template>
  <div id="plandialog">
    <el-dialog
      append-to-body
      ref="recordDlg"
      :title="'关联合同数详情'"
      :visible.sync="isdialog"
      width="50%"
      :close-on-click-modal='false'
      @close="handleClose"
      class="common-dlg-form">
      <page>
        <template #pageContent>
          <div class="bottom-table">
            <el-table :data="relevanceCmInfo" style="width: 100%" height="260">
              <el-table-column align="center" prop="cmCode" label="合同编码" width="150">
                <template slot-scope='{$index}'>
                  <cm-link :ba-id="relevanceCmInfo[$index].cmId" :ba-code="relevanceCmInfo[$index].cmCode"/>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="cmName" label="合同名称" show-overflow-tooltip width="300"/>
              <el-table-column align="right" prop="cmAmount" label="合同总价（元）" width="130"/>
              <el-table-column align="center" prop="ourSubject" label="合约方" show-overflow-tooltip/>
            </el-table>
          </div>
        </template>
      </page>
    </el-dialog>

  </div>
</template>

<script>

export default {
  name: 'relevance-cm-num-dialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.plandetails = bool
    }
  },
  data() {
    return {
      isdialog: this.dialog,
      plandetails: this.isDetails,
      relevanceCmInfo: [
        {
          'cmCode': '',
          'cmName': '',
          'cmAmount': '',
          'ourSubject': '',
          'cmId': ''
        }
      ]
    }
  },
  mounted() {
    // this.$setDlgSize(this, 'recordDlg', 800, )
  },
  methods: {
    handleClose() {
      this.isdialog = false
      this.isDetails = false
      this.relevanceCmInfo = []
      this.$emit('update:dialog', false)
    },
    resetForm() {
      if (this.$refs['relevanceCmInfo']) { // 清空form数据
        this.$refs['relevanceCmInfo'].resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  /*/deep/ .el-dialog__body{
    margin-left: -245px;
  }*/
  /deep/ .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }
</style>
<style lang="scss">
  #contractDialog {
    .el-input--prefix .el-input__inner {
      padding-left: 30px !important;
    }
  }
</style>
