<template>
  <div class="plan-list">
    <div class="top-btns" v-if="!mode">
      <el-button size="small" @click="addBtn" :disabled='!havePlan || !showButton || isCmSummaryList' plain
                 icon="el-icon-circle-plus-outline">添加履行计划
      </el-button>
      <el-button size="small" @click="deleteBtn" :disabled='checkedRow.length === 0 || !havePlan || !showButton
      || isCmSummaryList' plain
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table plan-container">
      <el-table border :data="planData" @selection-change="handleSelectionChange" show-summary
                :summary-method="getSummaries" style="width: 100%">
        <el-table-column type="selection" align="center" width="30"/>
        <el-table-column align="left" prop="contracpartyName" label="履行主体" width="240">
          <template #header><span style="color:#f56c6c;">*</span>履行主体</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.contracpartyId" class="contracpartyName" :disabled="row.isUsed || (!disabledArr.includes('履行主体') && mode)"
                       placeholder="选择履行主体" v-if="havePlan && !isCmSummaryList" @change="hideError($index)" style="width: 100%">
              <el-option
                v-for="item in contractList"
                :key="item.contracpartyName"
                :label="item.contracpartyName"
                :value="item.id"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{ row.contracpartyName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="performStage" label="履行阶段" width="95">
          <template #header><span style="color:#f56c6c;">*</span>履行阶段</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.performStage" class="performStage" :disabled="row.isPay || (!disabledArr.includes('履行阶段') && mode)"
                       placeholder="履行阶段" v-if="havePlan && !isCmSummaryList" @change="hideError($index)"
                       @click.native="choosePrformStage($index)">
              <el-option
                v-for="item in performStageList"
                :key="item.eleName"
                :label="item.eleName"
                :value="item.eleName"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{ row.performStage }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划付款金额" prop="planMoney" width="100">
          <template #header><span style="color:#f56c6c;">*</span>计划付款金额</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan && !isCmSummaryList" class="planMoney"
                      v-model="row.planMoney"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"
                      :disabled="row.isUsed || (!disabledArr.includes('计划付款金额') && mode)"
                      placeholder="输入金额"/>
            <span v-else>{{ row.planMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="planPaymentDate" label="日期" width="140">
          <template #header><span style="color:#f56c6c;">*</span>日期</template>
          <template slot-scope="{row, $index}">
            <el-date-picker
              style="width:100%"
              :disabled="row.isUsed || isCmSummaryList || (!disabledArr.includes('日期') && mode)"
              v-model="row.planPaymentDate"
              type="date"
              :editable="false"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              @change="hideError($index)"
              class="planPaymentDate">
            </el-date-picker>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="performCondition" label="履行条件">
          <template #header><span style="color:#f56c6c;">*</span>履行条件</template>
          <template slot-scope='{row}'>
            <el-input v-if="havePlan && !isCmSummaryList" class="planCondition" :disabled="row.isUsed || (!disabledArr.includes('日期') && mode)"
                      v-model="row.performCondition"
                      placeholder="输入履行条件"
                      :maxlength="500"/>
            <span v-else>{{ row.performCondition }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="corrBa" label="关联指标" width="320" v-if="isShowRelevantBa">
          <template #header><span style="color:#f56c6c;">*</span>关联指标</template>
          <template slot-scope='{row, $index}'>
            <el-select clearable v-model="row.corrBa" class="purMode" @click.native="selectCorrBa"
                       :disabled="row.isUsed || (!disabledArr.includes('关联指标') && mode)"
                       placeholder="关联指标" v-if="havePlan && !isCmSummaryList" @change="hideError($index)" style="width: 300px">
              <el-option
                v-for="item in corrBa"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="isOptionDisabled(item, row)"
                style="font-size: 12px">
              </el-option>
            </el-select>
            <span v-else>{{ row.corrBa }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import Plandialog from './plandialog'
import $ from 'jquery'

export default {
  name: 'contract-plan',
  components: { Plandialog },
  props: {
    disabledArr: { type: Array, default: () => [] }, // 审核模式下显示可修改的配置项
    mode: { type: Boolean, default: false } // 是否是审核模式
  },
  data() {
    return {
      checkedRow: [],
      planData: [],
      isDialog: false,
      isChange: false,
      havePlan: true,
      isCmSummaryList: false,
      showButton: true,
      contractList: [], // 合约方信息
      performStageList: [], // 履行阶段
      corrBa: [],
      corrBaData: [],
      isShowZhiBao: false,
      isShowRelevantBa: false,
      formFormat: {},
      isNeedNeverBa: false,
      isPuCreateCm: false // 是否是采购生成合同
    }
  },
  activated() {
    const _this = this
    window.$event.$off('backData', null, 'componentD')
    window.$event.$on('backData', (data) => {
      var count = []
      for (let i = 0; i < data.length; i++) {
        var index = i + 1 + ''
        var countObj = {}
        countObj.value = index
        countObj.label = index
        countObj.disabled = false
        count.push(countObj)
      }
      if (this.isNeedNeverBa) {
        count.push({ value: '无', label: '无', disabled: false })
      }
      this.$set(_this, 'corrBa', count)
      // _this.corrBa = count
    }, 'componentD')
    window.$event.$off('getPlanInfo', null, 'componentC')
    window.$event.$on('getPlanInfo', (baDetail) => {
      const planInfo = this.getPlanInfo()
      // return planInfo
      window.$event.$emit('planInfoReceived', planInfo)
    }, 'componentC')
  },
  beforeDestroy() {
    window.$event.$off('getPlanInfo')
  },
  created() {
    // 删除提示

  },
  computed: {
    isOptionDisabled() {
      return (item, row) => {
        if (row.hasOwnProperty('corrBa') && row.corrBa[0] !== undefined) {
          var changeValue = row.corrBa[0]
          if (changeValue !== '无' && item.label !== '无') {
            return false
          } else {
            return true
          }
        } else if (!this.showButton && item.label === '无') {
          return true
        } else {
          return false
        }
      }
    }
  },
  mounted() {
    this.selectPerformStageList() // 查询履行阶段基础数据
  },
  methods: {
    selectPerformStageList() {
      this.$callApi('selectPerformStageData', {}, result => {
        if (result.success) {
          this.performStageList = result.data
        }
        return true
      })
    },
    init(dataVo, mode, formFormat) {
      this.formFormat = formFormat
      this.isPuCreateCm = dataVo.extData?.hasOwnProperty('采购签合同')
      this.changeTotalPlanMoney() // 初始化时先调用该方法
      formFormat.addShowErrorCallbacks({
        '履行计划错误提示': errorItems => {
          if (this.$isNotEmpty(errorItems)) {
            var planIndexes = []
            var keys = Object.keys(errorItems)
            keys.forEach(key => {
              planIndexes.push(key.replace('planInfo', ''))
            })
            this.showPlanError(planIndexes)
          }
        }
      })
    },
    showPlanError(planIndexes) {
      if (this.$isNotEmpty(planIndexes)) {
        var $planTableTr = $('.plan-container table tr')
        $.each($planTableTr, (index, item) => {
          const planIndex = index - 1 // 目的是为了排除掉第一个tr(列头)

          if (planIndexes.indexOf(planIndex + '-contracpartyName') > -1) {
            $(item).find('.contracpartyName').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-performStage') > -1) {
            $(item).find('.performStage').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planMoney') > -1) {
            $(item).find('.planMoney').find('input').addClass('planError')
          } else if (planIndexes.indexOf(planIndex + '-planPaymentDate') > -1) {
            $(item).find('.planPaymentDate').find('input').addClass('planError')
          }
        })
      }
    },
    hideError(rowIndex) {
      var $rows = $('.plan-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.planError').removeClass('planError')
        }
      })
    },
    formFormatItem(item) {
      item.planMoney = this.$formatMoney(item.planMoney)
      this.changeTotalPlanMoney()
    },
    changeTotalPlanMoney() { // 合同总价动态变化
      let totalPlanMoney = 0
      this.planData.forEach(plan => {
        totalPlanMoney += parseFloat(this.$unFormatMoney(plan.planMoney))
      })
      // 合同总价 跟随履行计划总额改变而改变
      this.formFormat.setValue('合同总价', this.$fixMoney(totalPlanMoney))
      const fundDirection = this.formFormat.getValue('款项方向')
      // 同步变更收付款金额
      if (fundDirection === '收款') {
        this.formFormat.setValue('收款金额', this.$fixMoney(totalPlanMoney))
      } else if (fundDirection === '付款') {
        this.formFormat.setValue('付款金额', this.$fixMoney(totalPlanMoney))
      }
      // 是否需要过滤招投标类型
      let filterBiddingType = false
      this.formFormat.fcRule.forEach(i => {
        if (i.field === '合同业务类别' &&
          this.$isNotEmpty(i.options)) {
          const optionArr = []
          i.options.forEach(option => {
            optionArr.push(option.label)
          })
          if (optionArr.indexOf('施工类') > -1 &&
            optionArr.indexOf('工程货物类') > -1 &&
            optionArr.indexOf('工程服务类') > -1) {
            filterBiddingType = true
          }
        }
      })
      filterBiddingType && this.filterBiddingType(totalPlanMoney)
    },
    filterBiddingType(totalPlanMoney) {
      const bizType = this.formFormat.getValue('合同业务类别')
      const biddingType = this.formFormat.getValue('招投标类型')
      const gt400w = totalPlanMoney > 4000000
      const gt200w = totalPlanMoney > 2000000
      const gt100w = totalPlanMoney > 1000000
      const gt20lt400w = totalPlanMoney >= 200000 && totalPlanMoney <= 4000000
      const gt20lt200w = totalPlanMoney >= 200000 && totalPlanMoney <= 2000000
      const gt20lt100w = totalPlanMoney >= 200000 && totalPlanMoney <= 1000000
      const lt20w = totalPlanMoney < 200000
      // 过滤下拉框的值
      const filterParams = { label: '招投标类型', data: [], biddingType: biddingType }
      const dataObj = {
        gkzb: '公开招标', yqzb: '邀请招标', zjwt: '直接委托'
      }
      if (bizType === '施工类') {
        if (gt400w) {
          filterParams.data = [dataObj.yqzb, dataObj.zjwt]
        } else if (gt20lt400w) {
          filterParams.data = [dataObj.gkzb, dataObj.zjwt]
        } else if (lt20w) {
          filterParams.data = [dataObj.gkzb, dataObj.yqzb]
        }
      } else if (bizType === '工程货物类') {
        if (gt200w) {
          filterParams.data = [dataObj.yqzb, dataObj.zjwt]
        } else if (gt20lt200w) {
          filterParams.data = [dataObj.gkzb, dataObj.zjwt]
        } else if (lt20w) {
          filterParams.data = [dataObj.gkzb, dataObj.yqzb]
        }
      } else if (bizType === '工程服务类' || bizType === '货物类' || bizType === '服务类') {
        if (gt100w) {
          filterParams.data = [dataObj.yqzb, dataObj.zjwt]
        } else if (gt20lt100w) {
          filterParams.data = [dataObj.gkzb, dataObj.zjwt]
        } else if (lt20w) {
          filterParams.data = [dataObj.gkzb, dataObj.yqzb]
        }
      }
      // 判断已选择的招投标类型是否在过滤后的选项内 在的话置空
      if (filterParams.data.indexOf(filterParams.biddingType) > -1) {
        this.formFormat.setValue('招投标类型', '')
      }
      this.formFormat.setFilterParams(filterParams)
    },
    addBtn() {
      const data = {}
      // 解决合同申请一个指标时默认选中
      window.$event.$emit('getbaDetailData')
      window.$event.$on('backData', () => {
        // 当只有一个关联指标时 自动选中
        if (this.corrBa.length === 1) {
          data.corrBa = [this.corrBa[0].value]
        }
      }, 'componentD')
      data.contracpartyName = ''
      data.performStage = '进度款'
      data.planMoney = ''
      this.planData = this.planData.concat([], data)
    },
    deleteBtn() {
      for (var i = 0; i < this.checkedRow.length; i++) {
        if (this.checkedRow[i].usedAmount > 0) {
          this.$message.error('该履行计划已被使用，不能删除')
          return
        }
        var index = this.planData.indexOf(this.checkedRow[i])
        this.planData.splice(index, 1)
      }
      this.changeTotalPlanMoney()
    },
    handleSelectionChange(rows) {
      this.checkedRow = rows
    },
    reload(data) {
      if (!this.isChange) {
        this.planData = this.planData.concat([], data)
        this.getPlanData()
      } else {
        this.checkedRow[0] = Object.assign(this.checkedRow[0], data)
      }
      this.isChange = false
    },
    getPlanData() {
      for (var i = 0; i < this.planData.length; i++) {
        this.planData[i].planMoney = this.$unFormatMoney(this.planData[i].planMoney)
        for (var j = 0; j < this.contractList.length; j++) {
          if (this.planData[i].contracpartyId === this.contractList[j].id) {
            this.planData[i].contracpartyName = this.contractList[j].contracpartyName
            break
          }
        }
      }
      return this.planData
    },
    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (this.$isEmpty(data)) {
        return ''
      }
      const dt = new Date(data)
      row[column.property] = dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate()
      return dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate()
    },
    getPlanInfo() {
      return this.planData
    },
    getbaDetailData() {
      return this.corrBaData
    },
    selectCorrBa(value) {
      window.$event.$emit('getbaDetailData')
    },
    choosePrformStage() {
      if (this.isShowZhiBao) {
        this.selectPerformStageList()
      } else {
        const stageList = []
        this.performStageList.forEach(item => {
          if (item.eleName !== '质保') {
            stageList.push(item)
          }
        })
        this.performStageList = stageList
      }
    },
    getSummaries(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = '合计'
          return
        }
        if (index === 4) {
          sums[index] = '大写'
          return
        }
        const values = data.map(item => Number(this.unFmtMoney(item[column.property])))
        if (!values.every(value => isNaN(value)) && column.label.indexOf('金额') > -1) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                prev = Number(_this.$fixMoney(prev))
              }
              if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                curr = Number(_this.$fixMoney(curr))
              }
              return _this.$formatMoney(Number(prev) + Number(curr))
            } else {
              return prev
            }
          }, 0)
          sums[index] += ''
        } else {
          sums[index] = ''
        }
      })
      if (sums[3]) {
        sums[5] = _this.$digitUppercase(_this.$unFormatMoney(sums[3]))
      } else {
        sums[5] = ''
      }
      return sums
    },
    unFmtMoney(money) {
      if (this.$isNotEmpty(money)) {
        return money.toString().replace(/￥|$|,/g, '')
      }
      return undefined
    }
  }
}
</script>

<style scoped lang="scss">
.plan-list {
  padding: 3px 0px 3px 0px;
  width: 100%;
  height: 100% !important;
  display: flex;
  flex-direction: column;

  .total-amt {
    margin-left: 10px;
    font-size: 14px;
  }

  .top-btns {
    padding-bottom: 10px;
    margin-left: 0px;
    width: 27%;
    display: flex;

    .el-input {
      margin-left: 5px;
    }
  }

  .bottom-table {
    overflow: hidden;
    flex: 1;
  }

  /deep/ .el-table .cell {
    padding: 0px 5px !important;
  }

  /deep/ .el-table .warning-row {
    background-color: rgb(255, 43, 43) !important;
  }

  /deep/ .el-table--border .el-table__cell:first-child .cell {
    padding: 0px 5px !important
  }

  /deep/ .planError {
    border-color: #ff5c00 !important;
  }
}
</style>
<style lang="scss">
.mini-table .plan-list .el-table .el-table-column--selection .cell {
  padding: 0px 0px !important;
}

.mini-table .plan-list .el-table .cell {
  padding: 0px 3px;
}

.mini-table .plan-list .el-table input {
  padding: 0px 3px;
  height: 28px;
}

.mini-table .plan-list .el-table .el-input__suffix {
  right: -4px;
}

.mini-table .plan-list .el-table .el-select .el-input .el-select__caret {
  font-size: 12px;
}

.mini-table .plan-list .el-table .cell .el-date-editor .el-input__prefix {
  display: none;
}

.plan-container input.el-input__inner {
  font-size: 12px;
}

.plan-container .planMoney input.el-input__inner {
  text-align: right;
  padding-right: 5px;
}
</style>
