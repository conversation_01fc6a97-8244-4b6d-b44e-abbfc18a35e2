<template>
  <div id="cm-file-dialog">
    <el-dialog
      append-to-body
      :title="'查看附件'"
      :visible.sync="isDialogFile"
      width="640px"
      :close-on-click-modal='false'
      @close="handleClose">
      <page>
        <template #pageContent>
          <div style="margin-bottom: 20px">
            <el-button class="btn-normal" @click="fileDownload" icon="el-icon-download" :disabled="selected.length==0">下载</el-button>
            <el-button class="btn-normal" @click="deleteFile" icon="el-icon-delete" :disabled="selected.length==0">删除</el-button>
          </div>
           <el-table
              ref="fileTable"
              :data="fileTableData"
              highlight-current-row
              border
              style="height: 300px; "
              @selection-change="handleSelectionChange"
              @row-click="clickRow"
              @select-all="selectAll"
              @select="selectCheck">
                <el-table-column
                  type="selection"
                  width="35">
                </el-table-column>
              <el-table-column prop="" label="附件名称">
                <template slot-scope='{row}'>
                  <div v-show="row.isOne">
                    {{row.attName}}
                  </div>
                  <div v-show="!row.isOne">
                    <a @click="handlePreView(row)">{{row.attName}}</a>
                  </div>
                </template>
              </el-table-column>
                <el-table-column
                  property="createUserName"
                  label="上传人"
                  align="center"
                  width="150">
                </el-table-column>
                <el-table-column
                  property="uploadTime"
                  label="上传时间"
                  align="center"
                  width="150">
                </el-table-column>
          </el-table>
        </template>
      </page>
    </el-dialog>
     <file-view ref="fileView"></file-view>
  </div>
</template>

<script>
import { downloadFile, batchDownload } from '@/api/file/file'
import FileView from '../../../components/fileview/file-view.vue'
export default {
  name: 'cmFileDialog',
  components: { FileView },
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isDialogFile = bool
    },
    isDetails(bool) {
      this.contracdetails = bool
    }
  },
  mounted() {
  },
  data() {
    return {
      isDialogFile: this.isDialogFile,
      contracdetails: this.isDetails,
      fileTable: {
        fileName: '',
        userName: '',
        date: ''
      },
      fileTableData: [],
      batchDownloadName: '批量下载', // 批量下载文件名称
      isDisabled: false,
      selectIndex: [], // table勾选存放用户
      selected: []
    }
  },
  methods: {
    init(data) {
      this.isDialogFile = true
      this.fileTableData = data
    },
    handleSelectionChange(val) {
      this.selected = val
    },
    handleClose() {
      this.isDialogFile = false
    },
    handlePreView(row) {
      const fileType = row.attName.substring(row.attName.lastIndexOf('.'))
      if (fileType === '.pdf') {
        const fileIds = []
        fileIds.push(row.attId)
        this.$refs.fileView.open({ fileIds: fileIds, bizDataId: null })
      } else {
        const attachIds = row.attId
        downloadFile(attachIds).then((res) => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件信息不存在')
          }
        })
      }
    },
    fileDownload() {
      var checkedRows = this.selected
      // 批量下载
      const attachIdArr = Array.from(checkedRows, ({ attId }) => attId)
      const attachIds = attachIdArr.join(',')
      if (checkedRows.length === 1) {
        downloadFile(attachIds).then((res) => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件信息不存在')
          }
        })
      } else {
        batchDownload({ attachIds: attachIds, fileName: this.batchDownloadName }).then(res => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            this.handleFileDownloadRes(res, str1)
          } else {
            this.$message.error('文件不存在！')
          }
        }).catch(err => {
          console.log(err)
          this.$message({
            type: 'error',
            message: '文件不存在！'
          })
        })
      }
    },
    handleFileDownloadRes(res, str1) {
      if (!res.data) {
        this.$message.error('文件信息不存在')
        return
      }
      var filename = str1 || undefined
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // 检测是否在IE浏览器打开
        window.navigator.msSaveOrOpenBlob(new Blob([res.data]), filename)
      } else {
        // 谷歌、火狐浏览器
        let url = ''
        if (
          window.navigator.userAgent.indexOf('Chrome') >= 1 ||
                window.navigator.userAgent.indexOf('Safari') >= 1
        ) {
          url = window.webkitURL.createObjectURL(new Blob([res.data]))
        } else {
          url = window.URL.createObjectURL(new Blob([res.data]))
        }
        const link = document.createElement('a')
        const iconv = require('iconv-lite')
        iconv.skipDecodeWarning = true // 忽略警告
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', filename)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      }
    },
    deleteFile() {
      var checkedRows = this.selected
      var fkGuid = this.selected[0].fkGuid
      // 批量下载
      const attachIdArr = Array.from(checkedRows, ({ attId }) => attId)
      const attachIds = attachIdArr.join(',')
      const source = this.selected[0].source
      this.$callApiParams('deleteFile', { ids: attachIds, source: source }, result => {
        this.$callApiParams('selectFile', { ids: fkGuid, source: source }, result => {
          this.init(result.data)
          return false
        })
        return true
      })
    },
    selectAll(selection) {
      if (selection.length !== 0) {
        for (let i = 0; i < selection.length; i++) {
          this.selectIndex[i] = selection[i].userId
        }
      } else {
        this.selectIndex = []
      }
    },
    selectCheck(selection, row) {
      for (var i = 0; i < this.selectIndex.length + 1; i++) {
        if (this.selectIndex.length < selection.length) {
          this.selectIndex[this.selectIndex.length] = row.userId
          break
        } else if (this.selectIndex.length > selection.length) {
          if (this.selectIndex[i] === row.userId) {
            this.selectIndex.splice(i, 1)
            break
          }
        }
      }
    }
  }
}

</script>

<style scoped>

</style>
