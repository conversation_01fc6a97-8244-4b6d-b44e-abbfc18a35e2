<template>
  <div id="plandialog">
    <el-dialog
      append-to-body
      ref="recordDlg"
      width="640px"
      :title="'合同备案'"
      :visible.sync="isdialog"
      :close-on-click-modal='false'
      @close="handleClose"
      class="common-dlg-form">
      <page>
        <template #pageContent>
          <el-form
            ref="planForm"
            :model="planForm"
            label-width="80px"
            :disabled="plandetails"
            id="contractDialog"
            style="overflow: hidden;">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="备案日期" :required="true">
                  <el-date-picker v-model="planForm.recordDate" value-format="yyyy-MM-dd" type="date"
                                  placeholder="选择日期"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备案人" :required="true">
                  <sup-tree :setting="setting"
                            ref="supTree"
                            :is-get-child="true"
                            :nodes="userData"
                            v-model="planForm.recordUser"
                            @getCheckObjs="checkObj"
                            :checked-values="planForm.recordUser?[planForm.recordUser]:[currentUser.userId]">
                  </sup-tree>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item label="备案说明">
                <el-input type="textarea" :rows="5" maxlength="200"
                          resize="none" show-word-limit v-model="planForm.explain"></el-input>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="上传附件">
                <el-upload
                  ref="upload"
                  v-model="planForm.enclosure"
                  :file-list="fileList"
                  :on-change="handleChange"
                  :action="uploadUrl"
                  :show-file-list="true"
                  :on-success="onSuccess"
                  :on-error="onError"
                  :auto-upload="false"
                  :multiple="true"
                  :on-remove="handleRemove"
                  accept=".doc,.docx,.docm,.xls,.xlsx,.ppt,.pptx,.pdf,.rar,.zip">
                  <el-button slot="trigger" class="btn-normal">选择文件</el-button>
                </el-upload>
              </el-form-item>
            </el-row>
          </el-form>
          <div class="dialog-footer" slot="footer">
            <el-button class="btn-normal" @click="handleClose('planForm')" v-if="!plandetails"> 取消</el-button>
            <el-button
              class="btn-normal"
              type="primary"
              :loading="submitLoading"
              @click="handleSumbit('planForm')"
              v-if="!plandetails"
            >
              确定
            </el-button>
          </div>
        </template>
      </page>
    </el-dialog>

  </div>
</template>

<script>
import { upload } from '@/api/file/file'

export default {
  name: 'cnDialog',
  components: { upload },
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.plandetails = bool
    }
  },
  data() {
    return {
      currentUser: {},
      nodeName: '合同备案',
      isdialog: this.dialog,
      plandetails: this.isDetails,
      planForm: {},
      nowdate: '',
      uploadUrl: '',
      fileList: [],
      recAcctTypeOptions: [],
      filedForm: {
        bizId: '',
        recAcctType: '',
        fileName: '',
        fileComment: ''
      },
      attList: [],
      bizTblName: 'ATTACHMENT',
      setting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'name'
          }
        }
      },
      currentUse: {},
      userData: [],
      submitLoading: false
    }
  },
  methods: {
    handleClose() {
      this.isdialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
      this.clearUpload()
      this.$refs.supTree.clearInputBox()
      this.planForm.recordUser = this.currentUser.userId
    },
    handleSumbit() {
      this.submitLoading = true
      if (this.$refs.supTree.treeObj.getSelectedNodes()[0]) {
        this.planForm.recordUser = this.$refs.supTree.treeObj.getSelectedNodes()[0].name
      } else {
        this.submitLoading = false
        this.$message.error('备案人不能为空')
        return
      }
      const params = {}
      params.ids = this.planForm.ids
      params.explain = this.planForm.explain
      params.recordDate = this.planForm.recordDate
      params.recordUser = this.planForm.recordUser
      this.planForm.enclosure = this.fileList
      params.enclosure = this.planForm.enclosure
      this.$callApiParams('updateCmRecord', params,
        result => {
          this.submitLoading = false
          this.handlefiledSumbit(this.planForm.ids)
          this.handleClose()
          this.$parent.$refs.curdList.$children[0].reloadTable()
        }, () => {
          this.submitLoading = false
        })
    },
    resetForm() {
      if (this.$refs['planForm']) { // 清空form数据
        this.$refs['planForm'].resetFields()
      }
    },
    getNowDateTime() {
      this.planForm.recordDate = this.$nowFormatDate()
    },
    reload() {
      this.$reInit(this)
    },
    onSuccess(res) {
      this.$alert(res.data, '提示', {
        confirmButtonText: '确定',
        callback: action => {
          this.clearUpload()
          console.log('上传成功')
        }
      })
    },
    onError(res) {
      this.$alert('创建失败', '提示', {
        confirmButtonText: '确定',
        callback: action => {
          console.log('上传失败')
        }
      })
    },
    clearUpload() { //  清理上传的文件
      // 清理缓存文件
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    handleChange(file, fileList, scope) {
      // 文件是否已存在上传列表中
      const isExists = this.fileList.some((f) => f.name === file.name)
      if (isExists) {
        this.$message.error('文件：' + file.name + '已存在，请勿重复上传!')
        // 文件展示列表是将新添加的文件放在数组末尾
        fileList.pop()
        return
      }
      const isGt100M = Number(file.size / 1024 / 1024) > 100
      if (isGt100M) {
        const currIdx = fileList.indexOf(file)
        fileList.splice(currIdx, 1)
        this.$message.error('文件大小不能超过100MB，请压缩后重新上传！')
        return
      }
      if (file) {
        this.filedForm.attName = file.name
        this.fileList.push(file)
      }
    },
    handleRemove(file, fileList) {
      const currIdx = this.fileList.indexOf(file)
      this.fileList.splice(currIdx, 1)
    },
    handlefiledSumbit(cmId) {
      if (this.$isNotEmpty(this.fileList)) {
        var params = {}
        params.typeCode = '合同备案' // 类型编码
        params.bizCode = 'BA' // 模块编码
        params.bizCodeName = '备案' // 模块名称
        params.bizTblName = this.bizTblName // 业务表名称
        params.bizId = cmId // 业务记录编码,业务表主键ID
        params.attType = '0' // 附件类别
        params.appendixType = '' // 附件类型
        params.appendixTypeCode = '' // 附件类型编码
        params.source = '合同备案' // 来源
        params.methodName = 'initFileList'
        params.isClearFileList = false // 是否清理附件数据
        this.$uploadAttachment(this, params) // 上传附件
        this.isdialog = false
        this.clearUpload()
      }
    },
    fileUpload(result, file, fileList, uploadRef) {
      // 清理缓存文件
      uploadRef.forEach(upload => {
        upload.clearFiles()
      })
      // 获取上传文件大小
      const isGt100M = Number(file.size / 1024 / 1024) > 100
      if (isGt100M) {
        this.$msgbox({
          title: '',
          message: '文件大小不能超过100MB，请压缩后重新上传！',
          type: 'warning'
        })
        return
      }
    },
    checkObj(nodes) {
      if (this.$isNotEmpty(nodes)) {
        // this.planForm.responsible = nodes[0].name
        this.$refs.supTree.visible = false
      } else {
        // this.planForm.responsible = ''
      }
    },
    getSystemUser() { // 所有系统人员
      this.$callApiParams('选择系统人员',
        {}, result => {
          this.userData = []
          result.data.forEach(re => {
            this.userData.push({
              id: re.id,
              name: re.label,
              parentId: re.parentId,
              code: re.itemKey
            })
          })
          return true
        })
    },
    isHasFile(fileName) {
      const attData = this.attList.filter(item => item.attName === fileName)
      if (!this.$isEmpty(attData)) {
        this.isdialog = false
        return false
      }
      return true
    }
  },
  mounted() {
    this.$getLoginUserInfo().then(userInfo => {
      this.currentUser = userInfo.data
    })
    this.getSystemUser()
    this.getNowDateTime()
    this.$setDlgSize(this, 'recordDlg', 640, 350)
  }
}
</script>

<style lang="scss" scoped>
/*/deep/ .el-dialog__body{
  margin-left: -245px;
}*/
/deep/ .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
  margin-bottom: 10px;
}
</style>
<style lang="scss">
#contractDialog {
  .el-input--prefix .el-input__inner {
    padding-left: 30px !important;
  }
}
</style>
