<template>
  <div class="common-page multipleTabsContracparty">
    <div style="height: 100%">
      <cm-relevant-ba-list ref="cmRelevantBaList" :isBaseaAplication=true></cm-relevant-ba-list>
    </div>
  </div>
</template>

<script>
export default {
  name: 'cm-relevant-ba-tab',
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      formCanvasObj: undefined,
      isMonthlyPayment: false, // 是否按月付款
      needInitPlanData: true,
      initPlanIndex: 3,
      cmMoneyType: '',
      isAdditionalRecording: false // 是否已补录
    }
  },

  methods: {
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.formCanvasObj = formCanvasObj
      this.isAdditionalRecording = dataVo.extData.isAdditionalRecording
      var initBeforeColItems = this.$clone(formCanvasObj.$refs.formFormat.colItems)
      this.initAssembly(dataVo, {}, formCanvasObj.$refs.formFormat, formCanvasObj.assemblyData, initBeforeColItems)
      if (dataVo.extData.hasOwnProperty('签署备案') || dataVo.extData.hasOwnProperty('合同汇总表')) {
        this.$nextTick(() => {
          this.$refs.cmRelevantBaList.isShowBa = false
          this.$refs.cmRelevantBaList.havePlan = false
        })
      }
      if (this.$refs.cmRelevantBaList !== undefined) {
        this.$refs.cmRelevantBaList.selectSq = dataVo.extData['合同选择指标参照需要事前指标']
        this.$refs.cmRelevantBaList.metaId = dataVo.meta.id
        const refSourceType = dataVo.extData['refSourceType']
        if (this.$isNotEmpty(refSourceType) && refSourceType === '合同变更') {
          this.$refs.cmRelevantBaList.isShowBa = false
          this.$refs.cmRelevantBaList.havePlan = false
          this.$refs.cmRelevantBaList.isCmChange = true
          if (this.$isNotEmpty(dataVo.extData.planInfo)) {
            dataVo.extData.planInfo.forEach(plan => {
              if (this.$formatMoney(plan.usedAmount) > 0) {
                var corrBaStr = plan.corrBa
                var corrBaInt = this.$isNotEmpty(corrBaStr) ? corrBaStr.map(Number) : ''
                corrBaInt.forEach(value => {
                  dataVo.extData.baDetail[value - 1].isShow = true
                })
              }
            })
          }
        }
      }
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      var cmRelevantBa = dataVo.extData.合同关联指标
      if (cmRelevantBa) {
        const baDetail = this.$refs.cmRelevantBaList.baDetail
        if (baDetail) {
          for (let i = 0; i < baDetail.length; i++) {
            baDetail[i].baNum = i + 1
            baDetail[i].usableApplyAmount = baDetail[i].applyAmount
          }
          this.amountFormat(baDetail)
          dataVo.extData.baDetail = baDetail
        }
      }
      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
      const cmRelevantBaList = this.$refs.cmRelevantBaList
      dataVo.colItems.forEach(colItem => {
        if (colItem.labelOrigin === '是否为框架协议类') {
          if (colItem.dataValue === '是' && !this.isAdditionalRecording) {
            cmRelevantBaList.isDisabledApplyAmount = true
            cmRelevantBaList.isDisabledInvoice = true
          } else if (colItem.dataValue === '否') {
            cmRelevantBaList.isDisabledApplyAmount = false
            cmRelevantBaList.isDisabledInvoice = false
          }
          return
        } else if (colItem.labelOrigin === '部门ID') {
          cmRelevantBaList.deptCode = colItem.dataValue
        }
      })
      if (cmRelevantBaList.isDisabledInvoice || cmRelevantBaList.isDisabledApplyAmount) {
        cmRelevantBaList.baDetail.forEach(item => {
          item.applyAmount = ''
          item.invoiceType = ''
          item.preTaxAmount = ''
          item.taxRate = ''
          item.taxes = ''
          item.afterTaxAmount = ''
        })
      }
    },
    initAssembly(dataVo, mode, formFormat, assemblyData, initBeforeColItems) {
      this.$refs.cmRelevantBaList && this.$refs.cmRelevantBaList.init(dataVo, mode, formFormat)
    },
    triggerAssemblyEvent(itemName, itemValue, fcModel) {
      if (itemName === '是否为框架协议类') {
        if (itemValue === '是') {
          fcModel.disabled(true, '是否收取质保金')
          fcModel.disabled(true, '合同总价')
          fcModel.disabled(true, '计划付款方式')
          fcModel.setValue('计划付款方式', '')
          fcModel.setValue('是否收取质保金', '否')
          if (!this.isAdditionalRecording) {
            fcModel.setValue('合同总价', '')
          }
          this.$removeStar(fcModel, '合同总价')
          this.$removeStar(fcModel, '计划付款方式')
          const startTime = this.$getItemLableDom(fcModel, '期限开始日期')
          const endTime = this.$getItemLableDom(fcModel, '期限结束日期')
          this.$addAsterisk(startTime, 'remove', 'col-isRequired')
          this.$addAsterisk(endTime, 'remove', 'col-isRequired')
        } else {
          fcModel.disabled(false, '是否收取质保金')
          fcModel.disabled(false, '合同总价')
          fcModel.disabled(false, '计划付款方式')
          this.$addStar(fcModel, '合同总价')
          this.$addStar(fcModel, '计划付款方式')
          this.$addStar(fcModel, '期限开始日期')
          this.$addStar(fcModel, '期限结束日期')
        }
      }
    },
    amountFormat(amounts) {
      amounts && amounts.map(item => {
        if (item.itemAmount) {
          item.itemAmount = this.$fixMoney(item.itemAmount)
        }
        if (item.itemUsableAmount) {
          item.itemUsableAmount = this.$fixMoney(item.itemUsableAmount)
        }
        if (item.baAmount) {
          item.baAmount = this.$fixMoney(item.baAmount)
        }
        if (item.useAmount) {
          item.useAmount = this.$fixMoney(item.useAmount)
        }
        if (item.usableAmount) {
          item.usableAmount = this.$fixMoney(item.usableAmount)
        }
        if (item.applyAmount) {
          item.applyAmount = this.$fixMoney(item.applyAmount)
        }
        if (item.purPrice) {
          item.purPrice = this.$fixMoney(item.purPrice)
        }
        if (item.thoseYearsAmount) {
          item.thoseYearsAmount = this.$fixMoney(item.thoseYearsAmount)
        }
        if (item.comingYearsAmount) {
          item.comingYearsAmount = this.$fixMoney(item.comingYearsAmount)
        }
        if (item.preTaxAmount) {
          item.preTaxAmount = this.$fixMoney(item.preTaxAmount)
        }
        if (item.taxes) {
          item.taxes = this.$fixMoney(item.taxes)
        }
        if (item.afterTaxAmount) {
          item.afterTaxAmount = this.$fixMoney(item.afterTaxAmount)
        }
        if (item.usableApplyAmount) {
          item.usableApplyAmount = this.$fixMoney(item.usableApplyAmount)
        }
      })
    }
  }
}
</script>
<style>
.multipleTabsContracparty .contracparty-list {
  height: 100%;
  padding-top: 0px;
}
</style>
