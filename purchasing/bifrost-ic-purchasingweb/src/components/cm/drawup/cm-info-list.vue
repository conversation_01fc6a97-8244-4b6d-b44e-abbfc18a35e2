<template>
  <el-tabs @tab-click="handleClick">
    <div id="purpolicy-edit">
      <div style="margin-top: 0px">合同基本信息
        <el-button v-if="isShowBtn" size="mini" @click="selectRefCm">选择合同</el-button>
      </div>
      <div class="table-left bottom-table">
        <el-form
          ref="cmInfoData"
          :model="cmInfoData"
          style="border: 1px solid #ccc;padding-top: 20px;"
          label-width="200px">
          <el-row class="row-marginBottom">
            <el-col :span="11">
              <el-form-item label="申请部门" prop="申请部门">
                <el-input v-model="cmInfoData.applyDept" :disabled="true" show-word-limit></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="申请人" prop="申请人">
                <el-input v-model="cmInfoData.applicant" :isUnitShow='true'
                          :disabled="true" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-marginBottom">
            <el-col :span="11">
              <el-form-item label="申请日期" prop="申请日期">
                <el-input v-model="cmInfoData.applyDate" :isUnitShow='true' :disabled="true" show-word-limit></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="合同编码" prop="合同编码">
                <el-input v-model="cmInfoData.cmCode" :disabled="true" show-word-limit>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-marginBottom">
            <el-col :span="11">
              <el-form-item label="合同名称" prop="合同名称">
                <el-input v-model="cmInfoData.cmName" :disabled="true" show-word-limit></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="合同业务类别" prop="合同业务类别">
                <el-input v-model="cmInfoData.cmBusinessType" :disabled="true" size="medium"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="合同总金额" prop="合同总金额">
                <el-input v-model="cmInfoData.cmAmount" :disabled="true" size="medium"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="合同起止时间" prop="合同起止时间">
                <el-date-picker style="width: 580px"
                                v-model="cmInfoData.startEndDate" type="daterange" :disabled="true"
                                value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                                end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-marginBottom">
            <el-col :span="11">
              <el-form-item label="合同签订日期" prop="合同签订日期">
                <el-date-picker v-model="cmInfoData.cmSignDate" :disabled="true" value-format="yyyy-MM-dd" type="date"
                                placeholder="选择日期"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="是否为框架协议类" prop="spePurMethods">
                <el-select v-model="cmInfoData.isFrame" :disabled="true" size="medium" clearable>
                  <el-option key="是" label="是" value="是"></el-option>
                  <el-option key="否" label="否" value="否"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="table-left bottom-table" style="margin-top: 20px;margin-bottom: 10px">合同主体
          <el-button size="mini" v-if="isShowBtn" @click="selectRefContracparty">添加合约方</el-button>
          <el-button size="small" v-if="isShowBtn" @click="deleteContracpartyBtn(true)"
                     :disabled='checkedrows.length === 0' plain icon="el-icon-delete"> 删除
          </el-button>
        </div>
        <div class="bottom-table" style="margin-top: 5px;flex: 1;height:calc(100% - 375px);z-index: 9999 ">
          <el-table class="el-table-right" border height="320" style="width: 100%;height:100%;"
                    :data="cmInfoData.contracpartyInfo" @selection-change="handleSelectionChange">
            <el-table-column type="selection" align="center"></el-table-column>
            <el-table-column align="center" prop="contractParty" show-overflow-tooltip
                             label="合约方"></el-table-column>
            <el-table-column align="center" prop="contracpartyName" show-overflow-tooltip
                             label="合约方名称"></el-table-column>
            <el-table-column align="center" prop="corporation" show-overflow-tooltip
                             label="法人代表"></el-table-column>
            <el-table-column align="center" prop="corporationTel" show-overflow-tooltip
                             label="法人代表联系电话"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-tabs>
</template>

<script>
export default {
  name: 'cm-info-list',
  data() {
    return {
      title: '',
      dialogVisible: false,
      isFoodBtn: true,
      isDisabledRefCm: false,
      userData: [],
      cmInfoData: {
        cmId: '',
        applyDept: '',
        applicant: '',
        applyDate: '',
        cmCode: '',
        cmName: '',
        cmBusinessType: '',
        cmAmount: '',
        startEndDate: [],
        cmSignDate: '',
        isFrame: '',
        contracpartyInfo: []
      },
      cmId: '',
      oldCmId: '', // 修改时，原本所选择的合同ID
      checkedrows: [],
      cmEvaluateId: '',
      isShowBtn: true
    }
  },
  methods: {
    init(dataVo, mode, formFormat) {
      var cmInfoData = dataVo.extData.cmInfoData
      this.cmEvaluateId = dataVo.data.id
      this.cmId = dataVo.extData.cmId
      this.oldCmId = dataVo.extData.cmId
      if (this.$isNotEmpty(cmInfoData)) {
        this.cmInfoData = cmInfoData
      }
    },
    auditInit(bizzId, callback) {
      this.isFoodBtn = false
      this.init(bizzId)
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('update:dialog', false)
      this.clearUpload()
      this.$refs.supTree.clearInputBox()
    },
    handleClick() {

    },
    selectRefCm() {
      const params = {}
      params.合同验收履约评价 = ''
      params.合同验收履约评价表使用合同 = ''
      params.启用用户控制 = ''
      if (this.$isNotEmpty(this.oldCmId)) {
        params.checkIds = this.oldCmId
      }

      this.$refDataCommon('选择合同', selectedData => {
        selectedData.list.forEach(row => {
          this.cmId = this.$getRowId(row)
          this.oldCmId = this.cmId

          var startEndDate = []
          this.cmInfoData.cmId = row.id
          this.cmInfoData.applyDept = row.创建部门名称
          this.cmInfoData.applicant = row.申请人
          this.cmInfoData.applyDate = row.申请日期
          this.cmInfoData.cmCode = row.业务编码
          this.cmInfoData.cmName = row.业务名称
          this.cmInfoData.cmBusinessType = row.合同业务类别
          this.cmInfoData.cmAmount = row.合同总价
          startEndDate.push(row.期限开始日期)
          startEndDate.push(row.期限结束日期)
          this.cmInfoData.startEndDate = startEndDate
          this.cmInfoData.cmSignDate = row.合同拟签订日期
          this.cmInfoData.isFrame = row.是否为框架协议类
          const selectParams = {}
          selectParams.CONTRACT_ID_in = row.id
          if (this.$isNotEmpty(this.cmEvaluateId)) {
            selectParams.IS_EVALUATED_nullOrEmpty_or_IS_EVALUATED_eq_or_CM_EVALUATE_ID_eq = 'isNull,否,' + this.cmEvaluateId
          } else {
            selectParams.IS_EVALUATED_nullOrEmpty_or_IS_EVALUATED_eq = 'isNull,否'
          }

          this.$callApiParams('selectConPageData'
            , selectParams
            , result => {
              if (result.success) {
                this.cmInfoData.contracpartyInfo = result.data.rows
              }
              return true
            })
        })
      }, params)
    },
    selectRefContracparty() {
      if (this.$isEmpty(this.cmId)) {
        this.$message({ message: '请选择合同', type: 'warning' })
        return
      }
      const _this = this
      const params = {}
      params.handleRefCheckedData = (params, colItem, funGetValue) => {
        let idsCheck = []
        if (this.$isNotEmpty(_this.cmInfoData.contracpartyInfo)) {
          idsCheck = _this.cmInfoData.contracpartyInfo.map(item => {
            return item.id
          })
        }
        return idsCheck
      }
      params.CONTRACT_ID_in = this.cmId
      if (this.$isNotEmpty(this.cmEvaluateId)) {
        params.IS_EVALUATED_nullOrEmpty_or_IS_EVALUATED_eq_or_CM_EVALUATE_ID_eq = 'isNull,否,' + this.cmEvaluateId
      } else {
        params.IS_EVALUATED_nullOrEmpty_or_IS_EVALUATED_eq = 'isNull,否'
      }
      params.multiple = true

      const refData = { colType: '弹框', dataRef: '选择合同合约方' }
      this.$refData(undefined, refData,
        () => {
        }, () => {
        },
        selectedData => {
          this.backFill(selectedData)
        },
        selectedData => {
          if (this.$isEmpty(this.cmInfoData.contracpartyInfo)) {
            this.cmInfoData.contracpartyInfo = []
          }
        }, params)
    },
    backFill(selectedData) {
      var _this = this
      if (_this.$isEmpty(_this.cmInfoData.contracpartyInfo)) {
        _this.cmInfoData.contracpartyInfo = []
      }
      _this.cmInfoData.contracpartyInfo = selectedData.list
    },
    handleSelectionChange(rows) {
      this.checkedrows = rows
    },
    deleteContracpartyBtn(btnEvent) {
      for (let i = 0; i <= this.cmInfoData.contracpartyInfo.length - 1; i++) {
        this.checkedrows.forEach(item => {
          const data = this.cmInfoData.contracpartyInfo[i]
          if (this.$isEmpty(item.index)) {
            if (item.id === data.id) {
              this.cmInfoData.contracpartyInfo.splice(i, 1)
            }
          } else {
            if (item.id === data.id && item.index === data.index) {
              this.cmInfoData.contracpartyInfo.splice(i, 1)
            }
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
#purpolicy-edit {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .table-wrap {
    z-index: 9999;
    flex: 1;
    display: flex;
    margin: 0px 15px;
    margin-top: -15px;

    .table-left, .table-right {
      width: 50%;
    }

    .table-left {
      margin-right: 15px;
    }

    .table-right {
      height: 100%;
    }

    .el-table-left {
      .el-table__body-wrapper {
        height: 93%;
      }
    }

    .el-table-right {
      .el-table__body-wrapper {
        height: 86%;
      }
    }

    .el-table-right-bottom {
      .el-table__body-wrapper {
        height: 83%;
      }
    }
  }

  .purpolicy-edit-btn {
    z-index: 9999;
    height: auto;
    text-align: right;
    margin-top: 10px;
  }
}

.el-tabs__nav-wrap::after {
  // height: 0px;
}

.formRegularContainerBa {
  border: none;
  padding: 10px 15px 10px 15px;
}

.formRegularContainer .colitem-money .el-input--small .el-input__inner {
  height: 36px;
  line-height: 36px;
}

.formRegularContainer .formCommonCols .el-form-item--small.el-form-item {
  background: #dbeeff;
}

.formRegularContainer .formCommonCols .colitem-textarea-ba-adjust {
  height: 64px !important;
}

.formRegularContainer .formCommonCols .colitem-textarea-ba-adjust .el-form-item__label {
  line-height: 64px !important;
}

.formCommonCols .colitem-textarea {
  // height: 88px;
}
</style>

