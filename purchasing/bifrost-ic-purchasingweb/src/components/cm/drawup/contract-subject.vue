<template>
    <div class="conSubject">
        <el-form
                ref="subjectForm"
                :model="subjectForm"
                label-width="auto"
                :disabled="subjectdetails"
                :rules="rules"
                style="border: 1px solid #ccc;padding-top: 20px;">
            <el-row :gutter="20" class="row-marginBottom">
                <el-col :span="11">
                    <el-form-item label="我方主体" prop="weSubject">
                        <el-input v-model="subjectForm.weSubject" placeholder="我方主体"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="11">
                    <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
                        <el-input v-model="subjectForm.unifiedSocialCreditCode" placeholder="合约方名称"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" class="row-marginBottom">
                <el-col :span="11">
                    <el-form-item label="我方经办人">
                        <el-input v-model="subjectForm.weHandler" placeholder="我方经办人"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="11">
                    <el-form-item label="经办人联系电话">
                        <el-input v-model="subjectForm.weHandlerTel" placeholder="经办人联系电话"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <contracparty-list ref="contracpartylist" :isBaseaAplication=true></contracparty-list>
    </div>
</template>

<script>
export default {
  name: 'contract-subject',
  data() {
    return {
      rules: {
        weSubject: [
          { required: true, message: '请输入我方主体', trigger: 'blur' }
        ],
        unifiedSocialCreditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
        ]
      },
      subjectForm: {
        contracpartyList: []
      },
      subjectdetails: false
    }
  },
  methods: {
    getSubjectData() {
      var _this = this
      this.$refs['subjectForm'].validate((valid) => {
        if (valid) {
          var contracpartyList = _this.$refs.contracpartylist.extData.contracparty
          _this.subjectForm.contracpartyList = contracpartyList
        } else {
          _this.subjectForm.contracpartyList = []
        }
      })
      return _this.subjectForm
    },
    setData(subjectInfo) {
      this.$refs['subjectForm'].clearValidate()
      this.subjectForm = subjectInfo
      this.$refs.contracpartylist.extData.contracparty = subjectInfo.contracpartyList
    }
  }
}
</script>

<style scoped>

</style>
<style lang="scss" scoped>
    .conSubject{
        height: 100%;
        width: 100%;
        margin-top: 2px
    }
    .conSubject .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
        margin-bottom: 10px !important;
    }
    right-collapsed-container .collapsed-content{
        margin-left: 20px !important;
    }
    .contracparty-list {
      height: calc(100% - 131px) !important;
      padding: 10px 0px 0px 0px;
      width: 100%;
    }
    .row-marginBottom {
      margin-bottom: 10px;
    }
</style>
