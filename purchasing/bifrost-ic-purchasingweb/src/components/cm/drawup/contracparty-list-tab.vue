<template>
  <div class="common-page multipleTabsContracparty">
    <div style="height: 300px">
      <contracparty-list ref="contracpartylist" :isBaseaAplication=true></contracparty-list>
    </div>
    <div style="flex: 1;overflow: hidden">
      <contract-plan :isBaseaAplication=true
                      :isPayeeDataCanRepeat=false ref="contractPlan"></contract-plan>
    </div>

  </div>
</template>

<script>
// import { selectPuCreateCmData } from '../../../views/cm/cmUtils'

export default {
  name: 'contracparty-list-tab',
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      formCanvasObj: undefined,
      isMonthlyPayment: false, // 是否按月付款
      needInitPlanData: true,
      initPlanIndex: 3,
      cmMoneyType: '',
      isZhibao: true, // 是否收取保质金
      isOne: false, // 是否是一次性
      isGetmoney: false,
      isChange: true,
      isAdditionalRecording: true // 判断是否已补录
    }
  },
  methods: {
    async initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      if (dataVo.extData.hasOwnProperty('采购签合同')) { // 采购签合同需查询合约方信息
        this.$refs.contracpartylist.isPuCreateCm = true
        await selectPuCreateCmData(dataVo, this)
      }
      dataVo.extData.planInfo.forEach(plan => {
        var money = this.$formatMoney(plan.planMoney)
        plan.planMoney = money
      })
      var cmRelevantBa = dataVo.extData.合同关联指标
      if (cmRelevantBa) {
        var isNeedNeverBa = dataVo.extData.合同履行计划是否需要无指标
        this.$nextTick(() => {
          this.$refs.contractPlan.isShowRelevantBa = true
          this.$refs.contractPlan.isNeedNeverBa = isNeedNeverBa
        })
      }
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.formCanvasObj = formCanvasObj
      var initBeforeColItems = this.$clone(formCanvasObj.$refs.formFormat.colItems)
      this.initAssembly(dataVo, {}, formCanvasObj.$refs.formFormat, formCanvasObj.assemblyData, initBeforeColItems)
      if (dataVo.extData.hasOwnProperty('签署备案')) {
        this.$nextTick(() => {
          this.$refs.contracpartylist.isShowBtn = false
          this.$refs.contractPlan.havePlan = false
          this.$refs.contractPlan.showButton = false
        })
      }
      if (dataVo.extData.hasOwnProperty('合同汇总表')) {
        this.$refs.contracpartylist.isShowBtn = false
        this.$refs.contractPlan.havePlan = false
        this.$refs.contractPlan.showButton = false
        this.$refs.contractPlan.isCmSummaryList = true
      }
      if (this.$isNotEmpty(baseListFormObj.extData.来源类型) && baseListFormObj.extData.来源类型 === '合同变更') {
        this.isChange = false
      }
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      const planInfo = this.$refs.contractPlan.getPlanData()
      const contracpartyList = this.$refs.contracpartylist.extData.contracparty
      dataVo.extData.planInfo = planInfo
      dataVo.extData.contracpartyInfo = contracpartyList
      return dataVo
    },
    showError(result) { // 后端校验本组件错误
      this.isError = true
      for (const key in result.attributes) {
        if (key !== 'formEditTabErrors') {
          this.delayTimeFun(result.attributes[key])
        }
      }
      return 1
    },
    delayTimeFun(msg) {
      const _this = this
      setTimeout(function() {
        _this.$message.error(msg)
      }, 0)
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
    },
    initAssembly(dataVo, mode, formFormat, assemblyData, initBeforeColItems) {
      dataVo.colItems.forEach(colItems => {
        if (colItems.labelOrigin === '补录金额状态') {
          if (colItems.dataValue === '已补录') {
            this.cmMoneyType = colItems.dataValue
            return
          }
          if (colItems.dataValue === '未补录') {
            this.cmMoneyType = colItems.dataValue
            return
          }
          if (colItems.dataValue === '无需补录') {
            this.cmMoneyType = colItems.dataValue
            return
          }
        }
        if (colItems.labelOrigin === '金额类型') {
          if (colItems.dataValue === '确定金额' || colItems.defaultValue === '确定金额') {
            const contractSum = this.$getItemLableDom(formFormat.fcModel, '合同总价')
            const paymentDirection = this.$getItemLableDom(formFormat.fcModel, '款项方向')
            const planType = this.$getItemLableDom(formFormat.fcModel, '计划付款方式')
            const bidWinningAmount = this.$getItemLableDom(formFormat.fcModel, '中标金额')
            this.$addAsterisk(contractSum, 'add', 'col-isRequired')
            this.$addAsterisk(paymentDirection, 'add', 'col-isRequired')
            this.$addAsterisk(planType, 'add', 'col-isRequired')
            this.$addAsterisk(bidWinningAmount, 'add', 'col-isRequired')
          }
        }
      })
      if (assemblyData) {
        dataVo.extData.contracpartyInfo = assemblyData
      }
      dataVo.cformSettings.forEach(setting => {
        if (setting.optionName === '按月付款') {
          this.isMonthlyPayment = true
        }
      })
      if (dataVo.extData) {
        this.$refs.contracpartylist.dataVo = dataVo
        this.$refs.contracpartylist.extData.contracparty = dataVo.extData.contracpartyInfo
        var change = dataVo.extData.isChange
        if (change === true) {
          this.$alert('合约方信息已变更，请查阅', '提示', {
            confirmButtonText: '确定'
          })
        }
        this.isAdditionalRecording = dataVo.extData.isAdditionalRecording
        this.reloadContracparty(dataVo.extData.contracpartyInfo)
        if (this.$isEmpty(dataVo.data.id) && this.$isEmpty(dataVo.extData.initRefDataVoFromRemoteData)) {
          const planList = []
          if (this.isMonthlyPayment) {
            this.dataId = dataVo.data.id
          }
          this.$refs.contractPlan.planData = planList
        } else {
          this.needInitPlanData = false // 当是修改时，不触发初始化履行计划数据
          if (this.$isEmpty(dataVo.extData.planInfo)) {
            this.initPlanIndex = 4
          } else {
            this.initPlanIndex = 0
          }
          if (this.$isNotEmpty(dataVo.extData.planInfo)) {
            dataVo.extData.planInfo.forEach(plan => {
              if (this.$formatMoney(plan.planMoney) > 0 &&
                this.$formatMoney(plan.planMoney) === this.$formatMoney(plan.usedAmount)) {
                plan.isUsed = true
              } else {
                plan.isUsed = false
              }
              if (plan.usedAmount>0) {
                plan.isPay = true
              } else {
                plan.isPay = false
              }
              const corrBaStr = plan.corrBaStr
              plan.corrBa = corrBaStr
            })
          }
          this.$refs.contractPlan.planData = dataVo.extData.planInfo
        }
        // 采购生产合同回填合约方数据
        if (this.$isNotEmpty(dataVo.extData.suppliers)) {
          this.$refs.contracpartylist.extData.contracparty = dataVo.extData.suppliers[0]
          this.reloadContracparty(dataVo.extData.suppliers[0])
        }
      }
      this.$refs.contractPlan.init(dataVo, mode, formFormat)
    },
    triggerAssemblyEvent(itemName, itemValue, fcModel) {
      if (itemName === '金额类型') {
        if (itemValue === '无金额' && this.cmMoneyType !== '已补录') {
          this.$refs.contractPlan.havePlan = false
          this.$refs.contractPlan.planData = []
        } else {
          this.$refs.contractPlan.havePlan = true
        }
      } else if (itemName === '计划付款方式') {
        const plan = {}
        const planList = []
        if (itemValue === '一次性' || itemValue === '按比例') {
          plan.contracpartyName = ''
          plan.performStage = '进度款'
          plan.planMoney = ''
          planList.push(plan)
          if (itemValue === '一次性') {
            this.$refs.contractPlan.showButton = false
          } else {
            this.$refs.contractPlan.showButton = true
          }
        } else if (itemValue === '分期') {
          if (this.isMonthlyPayment) {
            this.getMonthPlan(planList)
          } else {
            for (var i = 0; i < 2; i++) {
              const plan = {}
              plan.contracpartyName = ''
              plan.performStage = '进度款'
              plan.planMoney = ''
              planList.push(plan)
            }
          }
          this.$refs.contractPlan.showButton = true
        }
        if (this.needInitPlanData && this.initPlanIndex > 3 && this.isChange) { // tab页时要大于3，是为了“修改”进来时不触发
          const contractPrice = fcModel.getValue('合同总价')
          this.fillPlanMoney(planList, contractPrice)
          this.$refs.contractPlan.planData = planList
        }
        this.initPlanIndex = this.initPlanIndex + 1
        this.needInitPlanData = true
      } else if (itemName === '合同总价') {
        if (this.needInitPlanData && this.initPlanIndex > 2 && this.isChange) {
          const planInfo = this.$refs.contractPlan.planData
          this.fillPlanMoney(planInfo, itemValue)
          this.$refs.contractPlan.planData = planInfo
        }
        this.initPlanIndex = this.initPlanIndex + 1
        this.needInitPlanData = true
      } else if (itemName === '合同业务类别') {
        this.$refs.contractPlan.changeTotalPlanMoney()
      }
      // 合同关联表单
      if (itemName === '是否为框架协议类') {
        if (itemValue === '是') {
          if (!this.isAdditionalRecording) {
            this.$refs.contractPlan.planData = []
            this.$refs.contractPlan.showButton = false
          } else {
            this.$refs.contractPlan.showButton = true
          }
        } else {
          this.$refs.contractPlan.showButton = true
        }
      }
      if (itemName === '是否收取质保金') {
        if (itemValue === '是') {
          this.isZhibao = false
        } else {
          this.isZhibao = true
        }
      }
      if (itemName === '计划付款方式') {
        if (itemValue === '一次性') {
          this.isOne = true
        } else {
          this.isOne = false
        }
      }
      if (itemName === '款项方向') {
        if (itemValue === '收款') {
          this.isGetmoney = true
        } else {
          this.isGetmoney = false
        }
      }
      if (this.isGetmoney || this.isOne || this.isZhibao) {
        this.$refs.contractPlan.isShowZhiBao = false
      } else {
        this.$refs.contractPlan.isShowZhiBao = true
      }
    },
    fillPlanContracpartyName() {
      this.$refs.contractPlan.getPlanData()
    },
    reloadContracparty(contracparty) {
      this.$refs.contractPlan.contractList = contracparty
    },
    splicingDate(fisrtDayOfMonth, month) {
      const arr = fisrtDayOfMonth.split('-')
      arr[1] = month < 10 ? '0' + month : month
      return arr[0] + '-' + arr[1] + '-' + arr[2]
    },
    getMonthPlan(planList) {
      const nowDate = this.$nowFormatDate()
      const fisrtDayOfMonth = nowDate.substring(0, nowDate.length - 2) + '01'
      for (var i = 0; i < 12; i++) {
        const plan = {}
        plan.contracpartyName = ''
        plan.performStage = '进度款'
        plan.planMoney = ''
        const monthDate = this.splicingDate(fisrtDayOfMonth, i + 1)
        plan.planPaymentDate = monthDate
        planList.push(plan)
      }
    },
    fillPlanMoney(planList, contractPrice) {
      if (this.$isNotEmpty(contractPrice) && contractPrice !== 0) {
        const avgPrice = Number((contractPrice / planList.length).toFixed(2))
        const difference = Number((contractPrice - (avgPrice * planList.length)).toFixed(2))
        for (var i = 0; i < planList.length; i++) {
          planList[i].planMoney = avgPrice
          if (i === planList.length - 1) {
            planList[i].planMoney = Number((planList[i].planMoney + difference).toFixed(2))
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.multipleTabsContracparty {
  display: flex;
  flex-direction: column;
  height: 100%;
  /deep/.contracparty-list {
    height: 100%;
    padding-top: 0px;
  }
}
</style>
