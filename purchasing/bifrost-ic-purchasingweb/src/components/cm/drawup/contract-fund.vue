<template>
    <div class="conFund">
        <el-form
                ref="fundForm"
                :model="fundForm"
                label-width="150px"
                :disabled="funddetails"
                :rules="rules">
            <el-row>
                <el-form-item>
                    <el-col :span="11">
                        <el-form-item label="金额类型" prop="amtType">
                            <el-select v-model="fundForm.amtType">
                                <el-option
                                  v-for="item in moneyTypeList"
                                  :key="item.eleName"
                                  :label="item.eleName"
                                  :value="item.eleName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="款项方向" prop="fundDirection">
                            <el-select v-model="fundForm.fundDirection">
                                <el-option
                                  v-for="item in payDirectionList"
                                  :key="item.eleName"
                                  :label="item.eleName"
                                  :value="item.eleName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item>
                    <el-col :span="11">
                        <el-form-item label="币种">
                            <el-select v-model="fundForm.currency" filterable>
                                <el-option
                                        v-for="item in currencyTypeList"
                                        :key="item.eleName"
                                        :label="item.eleName"
                                        :value="item.eleName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="合同总价" prop="contractSum">
                            <el-input v-model="fundForm.contractSum" placeholder="合同总价" type="number">
                              <template slot="append">元</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item>
                    <el-col :span="11">
                        <el-form-item label="质保金金额" prop="retentionMoney">
                            <el-input v-model="fundForm.retentionMoney" placeholder="质保金金额" type="number">
                              <template slot="append">元</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="履约金金额" prop="retentionMoneyAmt">
                            <el-input v-model="fundForm.retentionMoneyAmt" placeholder="履约金金额" type="number">
                              <template slot="append">元</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item>
                    <el-col :span="11">
                        <el-form-item label="合同到期提醒日期">
                            <el-date-picker v-model="fundForm.reminderDate" type="date" placeholder="选择日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="结算方式">
                            <el-select v-model="fundForm.paymentType">
                                <el-option
                                  v-for="item in setModeList"
                                  :key="item.eleName"
                                  :label="item.eleName"
                                  :value="item.eleName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item>
                    <el-col :span="11" v-if="fundForm.fundDirection=='收支双向'
                        || fundForm.fundDirection=='收款'  || fundForm.fundDirection==''">
                        <el-form-item label="收款金额" prop="receivableAmt">
                            <el-input v-model="fundForm.receivableAmt" placeholder="收款金额"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" v-if="fundForm.fundDirection=='收支双向'
                        || fundForm.fundDirection=='付款'  || fundForm.fundDirection==''">
                        <el-form-item label="付款金额" prop="paymentAmt">
                            <el-input v-model="fundForm.paymentAmt" placeholder="付款金额"></el-input>
                        </el-form-item>
                    </el-col>
                </el-form-item>
            </el-row>
        </el-form>
    </div>

</template>

<script>
export default {
  name: 'contract-fund',
  data() {
    return {
      rules: {
        amtType: [
          { required: true, message: '请输入金额类型', trigger: 'change' }
        ],
        fundDirection: [
          { required: true, message: '请输入款项方向', trigger: 'change' }
        ],
        contractSum: [
          { required: true, message: '请输入合同总价', trigger: 'blur' }
        ],
        receivableAmt: [
          { required: true, message: '请输入收款金额', trigger: 'blur' }
        ],
        paymentAmt: [
          { required: true, message: '请输入付款金额', trigger: 'blur' }
        ],
        retentionMoney: [
          { required: true, message: '请输入质保金金额', trigger: 'change' }
        ],
        retentionMoneyAmt: [
          { required: true, message: '请输入履约金金额', trigger: 'blur' }
        ]
      },
      fundForm: {},
      moneyTypeList: [],
      payDirectionList: [],
      currencyTypeList: [],
      setModeList: [],
      funddetails: false
    }
  },
  mounted() {
    this.selectFundBasicData() // 查询基础数据
  },
  methods: {
    selectFundBasicData() {
      this.$callApi('selectFundBasicData', {}, result => {
        if (result.success) {
          this.moneyTypeList = result.data.moneyType
          this.payDirectionList = result.data.payDirection
          this.currencyTypeList = result.data.currencyType
          this.setModeList = result.data.setMode
        }
        return true
      })
    },
    getFundData() {
      var _this = this
      this.$refs['fundForm'].validate((valid) => {

      })
      return _this.fundForm
    }
  }
}
</script>

<style scoped>

</style>
<style lang="scss" scoped>
    .conFund{
        height: 100%;
        width: calc(100% + 180px);
        margin-left: -170px;
        margin-top: 10px
    }
    .conFund .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
        margin-bottom: 10px !important;
    }
    right-collapsed-container .collapsed-content{
        margin-left: 20px !important;
    }
    .warp{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .test{
            width:49%;
            text-align: center;
            display: flex;
            height: 30px;
            justify-content: center;
            align-items: center;
            background: red;
            margin: 5px;
        }
    }
</style>
