<template>
  <div id="plandialog">
    <el-dialog
      append-to-body
      :title="'履行计划'"
      :visible.sync="isdialog"
      width="50%"
      :close-on-click-modal='false'
      @close="handleClose">
        <el-form
          ref="planForm"
          :model="planForm"
          label-width="200px"
          :disabled="plandetails"
          :rules="rules">
            <el-row>
                <el-form-item>
                    <el-col :span="11">
                        <el-form-item label="履行主体" prop="contracpartyName">
                          <el-select v-model="planForm.contracpartyName" @change="contracChange" filterable>
                            <el-option
                              v-for="item in contractList"
                              :key="item.contracpartyName"
                              :label="item.contracpartyName"
                              :value="item">
                            </el-option>
                          </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="履行阶段" prop="performStage">
                            <el-select v-model="planForm.performStage" filterable>
                              <el-option
                                v-for="item in performStageList"
                                :key="item.eleName"
                                :label="item.eleName"
                                :value="item.eleName">
                              </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item>
                    <el-col :span="11">
                        <el-form-item label="计划付款日期">
                          <el-date-picker v-model="planForm.planPaymentDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="履行金额" prop="planMoney">
                            <el-input v-model="planForm.planMoney" type="number" min="0" placeholder="请输入履行金额">
                              <template slot="append">元</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item>
                    <el-col :span="22">
                        <el-form-item label="履行条件">
                            <el-input type="textarea" v-model="planForm.performCondition"></el-input>
                        </el-form-item>
                    </el-col>
                </el-form-item>
            </el-row>
        </el-form>
      <template #footer>
        <el-button @click="handleClose('planForm')" v-if="!plandetails"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('planForm')"
          v-if="!plandetails"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'plandialog',
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.plandetails = bool
    }
  },
  mounted() {
    this.selectPerformStageList() // 查询履行阶段基础数据
  },
  data() {
    return {
      isdialog: this.dialog,
      plandetails: this.isDetails,
      planForm: {},
      performStageList: [],
      contractList: [],
      rules: {
        contracpartyName: [
          { required: true, message: '请输入履行主体', trigger: 'blur' }
        ],
        performStage: [
          { required: true, message: '请输入履行阶段', trigger: 'blur' }
        ],
        planMoney: [
          { required: true, message: '请输入履行金额', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    selectPerformStageList() {
      this.$callApi('selectPerformStageData', {}, result => {
        if (result.success) {
          this.performStageList = result.data
        }
        return true
      })
    },
    contracChange(data) {
      this.planForm.contracpartyId = data.id
      this.planForm.contracpartyName = data.contracpartyName
    },
    handleClose() {
      this.dialog = false
      this.isDetails = false
      this.$emit('update:dialog', false)
    },
    handleSumbit(formName) {
      this.$refs[formName].validate((valid) => {
        if (this.planForm.planMoney <= 0) {
          this.$message.error('履行金额不能小于等于0')
          return false
        }
        if (valid) {
          this.handleClose()
          this.$parent.reload(this.planForm)
          return true
        } else {
          return false
        }
      })
    },
    resetForm() {
      if (this.$refs['planForm']) { // 清空form数据
        this.$refs['planForm'].resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  /deep/ .el-dialog__body{
    margin-left: -245px;
  }
  /deep/ .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
      margin-bottom: 10px;
  }
</style>
<style lang="scss">
</style>
