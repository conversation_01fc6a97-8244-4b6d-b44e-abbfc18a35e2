<template>
  <div id="plandialog">
    <el-dialog
      append-to-body
      ref="recordDlg"
      :title="'补录框架协议'"
      :visible.sync="isdialog"
      width="50%"
      :close-on-click-modal='false'
      @close="handleClose"
      class="common-dlg-form">
      <page>
        <template #pageContent>
          <el-form
            ref="planForm"
            :model="planForm"
            label-width="150px"
            :disabled="plandetails"
            id="contractDialog"
            style="overflow: hidden;padding: 10px 30px 20px 0px;">
            <el-row>
              <el-col :span="11">
                <el-form-item label="履行阶段" :required="true">
                  <el-select v-model="planForm.performStage" filterable>
                    <el-option
                      v-for="item in performStageList"
                      :key="item.eleName"
                      :label="item.eleName"
                      :value="item.eleName">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="计划付款金额" :required="true">
                  <InputMoney v-model="planForm.planMoney" isEmtryShowZero/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="11">
                <el-form-item label="计划付款日期" :required="true">
                  <el-date-picker v-model="planForm.planPaymentDate" value-format="yyyy-MM-dd" type="date"
                                  placeholder="选择日期"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="合同开始时间" :required="true">
                  <el-date-picker v-model="planForm.cmStartDate" value-format="yyyy-MM-dd" type="date"
                                  :disabled="isDisabled" placeholder="选择日期"></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item label="合同结束时间">
                  <el-date-picker v-model="planForm.cmEndDate" value-format="yyyy-MM-dd" type="date"
                                  :disabled="isShowEndDate" placeholder="选择日期"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="关联指标" :required="true">
                  <el-select v-model="planForm.baNum" filterable>
                    <el-option
                      v-for="item in corrBaList"
                      :key="item.id"
                      :label="item.label"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-form-item label="履行条件" :required="true">
                  <el-input type="textarea" :rows="5" maxlength="200"
                            resize="none" show-word-limit v-model="planForm.performCondition"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="dialog-footer" slot="footer">
            <el-button @click="handleClose('planForm')" v-if="!plandetails"> 取消</el-button>
            <el-button
              type="primary"
              @click="handleSumbit('planForm')"
              v-if="!plandetails"
            >
              确定
            </el-button>
          </div>
        </template>
      </page>
    </el-dialog>

  </div>
</template>

<script>
import { upload } from '@/api/file/file'

export default {
  name: 'frame-agreement-dialog',
  components: { upload },
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    },
    isDetails(bool) {
      this.plandetails = bool
    }
  },
  data() {
    return {
      currentUser: {},
      nodeName: '合同备案',
      isdialog: this.dialog,
      plandetails: this.isDetails,
      nowdate: '',
      uploadUrl: '',
      fileList: [],
      recAcctTypeOptions: [],
      planForm: {},
      performStageList: [],
      corrBaList: [
        { id: '', label: '' }
      ],
      baNum: [],
      isShowEndDate: false,
      isDisabled: false
    }
  },
  mounted() {
    // this.$setDlgSize(this, 'recordDlg', 800, 350)
    this.getNowDateTime()
  },
  methods: {
    handleClose() {
      this.isdialog = false
      this.isDetails = false
      // this.planForm = {}
      this.$emit('update:dialog', false)
    },
    handleSumbit() {
      const params = {}
      params.ids = this.planForm.ids
      params.performStage = this.planForm.performStage
      params.planMoney = this.planForm.planMoney
      params.planPaymentDate = this.planForm.planPaymentDate
      params.cmStartDate = this.planForm.cmStartDate
      params.cmEndDate = this.planForm.cmEndDate
      params.baNum = this.planForm.baNum ? this.planForm.baNum.value : ''
      params.performCondition = this.planForm.performCondition
      var _this = this
      this.$callApiParams('frameAgreement', params,
        result => {
          _this.handleClose()
          _this.$reloadTable(this, null, null)
        })
    },
    resetForm() {
      if (this.$refs['planForm']) { // 清空form数据
        this.$refs['planForm'].resetFields()
      }
    },
    getNowDateTime() {
      this.planForm.planPaymentDate = this.$nowFormatDate()
    },
    selectPerformStageList(isShowZhiBao) {
      this.$callApi('selectPerformStageData', {}, result => {
        if (result.success) {
          this.performStageList = result.data
          if (!isShowZhiBao) {
            const stageList = []
            this.performStageList.forEach(item => {
              if (item.eleName !== '质保') {
                stageList.push(item)
              }
            })
            this.performStageList = stageList
          }
        }
        return true
      })
    },
    reload() {
      this.$reInit(this)
    }
  }
}
</script>

<style lang="scss" scoped>
/*/deep/ .el-dialog__body{
  margin-left: -245px;
}*/
/deep/ .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
  margin-bottom: 10px;
}
</style>
<style lang="scss">
#contractDialog {
  .el-input--prefix .el-input__inner {
    padding-left: 30px !important;
  }
}
</style>
