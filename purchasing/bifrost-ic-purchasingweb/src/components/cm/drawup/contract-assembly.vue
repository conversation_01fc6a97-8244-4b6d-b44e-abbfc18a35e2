<template>
    <div :class="isCformPath?'cformDetailsAssembly':'contractAssembly'">
      <contracparty-list ref="contracpartylist" :isBaseaAplication=true :style="{height:scrollerHeight}"></contracparty-list>
      <contract-plan ref="contractPlan" :style="{height: contractPlanHeight}"></contract-plan>
      <base-attachment ref="baseAttachment" :style="{height: baseAttachmentHeight}" ></base-attachment>
    </div>
</template>

<script>
import ContractSubject from './contract-subject'
import ContractFund from './contract-fund'
import ContractPlan from './contract-plan'
export default {
  name: '合同',
  components: { ContractPlan, ContractFund, ContractSubject },
  data() {
    return {
      isCformPath: window.location.href.includes('/cform'),
      attList: [], // 第一次加载的附件数据
      isError: false, // 是否发生过错误
      isMonthlyPayment: false, // 是否按月付款
      needInitPlanData: true,
      initPlanIndex: 3,
      scrollerHeight: 'calc((100% - 20px) / 3)',
      contractPlanHeight: 'calc((100% - 20px) / 3 + 10px) !important',
      baseAttachmentHeight: 'calc((100% - 20px) / 3 + 10px)',
      cmMoneyType: ''
    }
  },
  mounted() {
    this.dynamicHeight() // 查询履行阶段基础数据
  },
  methods: {
    showError(result) {
      this.isError = true
      return 0
    },
    initMetaAfter(align, nb) {
    },
    initAssembly(meta, mode, formFormat, assemblyData, initBeforeColItems) {
      initBeforeColItems.forEach(colItems => {
        if (colItems.labelOrigin === '补录金额状态') {
          if (colItems.dataValue === '已补录') {
            this.cmMoneyType = colItems.dataValue
            return
          }
        }
      })
      if (assemblyData) {
        meta.extData.contracpartyInfo = assemblyData
      }
      meta.cformSettings.forEach(setting => {
        if (setting.optionName === '按月付款') {
          this.isMonthlyPayment = true
        }
      })
      if (meta.extData) {
        this.$refs.contracpartylist.extData.contracparty = meta.extData.contracpartyInfo
        this.reloadContracparty(meta.extData.contracpartyInfo)
        var change = meta.extData.isChange
        if (change === true) {
          this.$alert('合约方信息已变更，请查阅', '提示', {
            confirmButtonText: '确定'
          })
        }
        if (this.$isEmpty(meta.data.id) && this.$isEmpty(meta.extData.initRefDataVoFromRemoteData)) {
          const planList = []
          if (this.isMonthlyPayment) {
            this.dataId = meta.data.id
            // this.getMonthPlan(planList) 去掉默认合同制单界面进来显示付款计划
            // this.$refs.contractPlan.isMonthlyPayment = true
          }
          this.$refs.contractPlan.planData = planList
        } else {
          this.needInitPlanData = false // 当是修改时，不触发初始化履行计划数据
          if (this.$isEmpty(meta.extData.planInfo)) {
            this.initPlanIndex = 3
          } else {
            this.initPlanIndex = 0
          }
          this.$refs.contractPlan.planData = meta.extData.planInfo
        }
      }
      this.$refs.contractPlan.init(meta, mode, formFormat)
      this.$refs.baseAttachment.$children[0].btAddText = '上传附件'
      this.$refs.baseAttachment.$children[0].attTypeTableName = 'ELE_HT_ATT_TYPE'
      this.$refs.baseAttachment.$children[0].initMeta(meta)

      // 附件区块按钮栏顶部隐藏默认的padding
      this.$nextTick(() => {
        this.$refs.baseAttachment.setButtonNormalNoPaddingTop(true)
      })
      // 初始化数据
      this.attList = []
      this.isError = false
      // 采购生产合同回填合约方数据
      if (this.$isNotEmpty(meta.extData.suppliers)) {
        this.$refs.contracpartylist.extData.contracparty = meta.extData.suppliers[0]
        this.reloadContracparty(meta.extData.suppliers[0])
      }
    },
    getExtData() {
      const planInfo = this.$refs.contractPlan.getPlanData()
      const extData = {}
      var contracpartyList = this.$refs.contracpartylist.extData.contracparty
      extData.contracpartyInfo = contracpartyList
      extData.planInfo = planInfo
      return extData
    },
    fillAtt(dataVo) {
      this.$fillAtt(this, dataVo)
    },
    triggerAssemblyEvent(itemName, itemValue, fcModel) {
      if (itemName === '金额类型') {
        if (itemValue === '无金额' && this.cmMoneyType !== '已补录') {
          this.$refs.contractPlan.havePlan = false
          this.$refs.contractPlan.planData = []
        } else {
          this.$refs.contractPlan.havePlan = true
        }
      } else if (itemName === '计划付款方式') {
        const plan = {}
        const planList = []
        if (itemValue === '一次性' || itemValue === '按比例') {
          this.dynamicHeight(itemValue, this.isMonthlyPayment)
          plan.contracpartyName = ''
          plan.performStage = '进度款'
          plan.planMoney = ''
          planList.push(plan)
          if (itemValue === '一次性') {
            this.$refs.contractPlan.showButton = false
          } else {
            this.$refs.contractPlan.showButton = true
          }
        } else if (itemValue === '分期') {
          this.dynamicHeight(itemValue, this.isMonthlyPayment)
          this.$refs.contractPlan.showButton = true
          if (this.isMonthlyPayment) {
            this.getMonthPlan(planList)
          } else {
            for (var i = 0; i < 2; i++) {
              const plan = {}
              plan.contracpartyName = ''
              plan.performStage = '进度款'
              plan.planMoney = ''
              planList.push(plan)
            }
          }
          /* const businessType = fcModel.getValue('合同业务类别')
          if (businessType === '工程类') {
            fcModel.disabled(true, '计划付款方式')
          }*/
        }
        if (this.needInitPlanData && this.initPlanIndex > 2) { // 大于2，是为了“修改”进来时不触发
          const contractPrice = fcModel.getValue('合同总价')
          this.fillPlanMoney(planList, contractPrice)
          this.$refs.contractPlan.planData = planList
        }
        this.initPlanIndex = this.initPlanIndex + 1
        this.needInitPlanData = true
      } else if (itemName === '合同总价') {
        if (this.needInitPlanData && this.initPlanIndex > 2) {
          const planInfo = this.$refs.contractPlan.planData
          this.fillPlanMoney(planInfo, itemValue)
          this.$refs.contractPlan.planData = planInfo
        }
        this.initPlanIndex = this.initPlanIndex + 1
        this.needInitPlanData = true
      }
    },
    fillPlanContracpartyName() {
      this.$refs.contractPlan.getPlanData()
    },
    reloadContracparty(contracparty) {
      this.$refs.contractPlan.contractList = contracparty
    },
    splicingDate(fisrtDayOfMonth, month) {
      const arr = fisrtDayOfMonth.split('-')
      arr[1] = month < 10 ? '0' + month : month
      return arr[0] + '-' + arr[1] + '-' + arr[2]
    },
    getMonthPlan(planList) {
      const nowDate = this.$nowFormatDate()
      const fisrtDayOfMonth = nowDate.substring(0, nowDate.length - 2) + '01'
      for (var i = 0; i < 12; i++) {
        const plan = {}
        plan.contracpartyName = ''
        plan.performStage = '进度款'
        plan.planMoney = ''
        const monthDate = this.splicingDate(fisrtDayOfMonth, i + 1)
        plan.planPaymentDate = monthDate
        planList.push(plan)
      }
    },
    fillPlanMoney(planList, contractPrice) {
      if (this.$isNotEmpty(contractPrice) && contractPrice !== 0) {
        const avgPrice = Number((contractPrice / planList.length).toFixed(2))
        const difference = Number((contractPrice - (avgPrice * planList.length)).toFixed(2))
        for (var i = 0; i < planList.length; i++) {
          planList[i].planMoney = avgPrice
          if (i === planList.length - 1) {
            planList[i].planMoney = Number((planList[i].planMoney + difference).toFixed(2))
          }
        }
      }
    },
    dynamicHeight(itemValue, isMonthlyPayment) {
      if (itemValue === '分期' && isMonthlyPayment === true) {
        this.$refs.contractPlan.inputHeight = '20px'
        this.scrollerHeight = 'calc((100% - 20px) /5.25)'
        this.contractPlanHeight = 'calc((100% - 20px) /1.555) !important'
        this.baseAttachmentHeight = 'calc((100% - 20px) /5.15)'
      } else {
        this.scrollerHeight = 'calc((100% - 20px) / 3)'
        this.contractPlanHeight = 'calc((100% - 20px) / 3 + 10px) !important'
        this.baseAttachmentHeight = 'calc((100% - 20px) / 3 + 10px)'
      }
    }
  }
}
</script>

<style scoped lang="scss">
  .contracparty-list {
    // height: calc((100% - 20px) / 3);
    padding: 0px 0px 0px;
  }
  .base-attachment {
    padding: 10px 0px 0px;
    // height: calc((100% - 20px) / 3 + 10px);
  }
  .contractAssembly {
    height: 100%;
    width: 100%;
    padding: 0px 0px 0px 0px;
  }
  .cformDetailsAssembly{
    height: 100%;
    width: 100%;
    border: none;
    padding-left: 10px;
    /deep/.is-scrollable{padding: 0;}
  }
  .el-tabs .el-tabs--top {
    height: 100%;
  }
  /deep/.el-tabs__content {
    height: calc(100% - 52px) !important;
  }
  .contractAssembly .el-tabs__content .el-tab-pane {
    height: 100%;
  }
  /deep/ .bottom-table{
    flex: 1;
    overflow: overlay;
  }
  /deep/ .el-table .cell{
    padding: 0px 5px !important;
  }
  /deep/ .plan-list {
    // height: calc((100% - 20px) / 3 + 10px) !important;
    padding: 10px 0px 0px;
  }
  /deep/ #ba-fileCategory .el-tabs__content {
    height: calc(100% - 45px) !important;
  }
</style>
