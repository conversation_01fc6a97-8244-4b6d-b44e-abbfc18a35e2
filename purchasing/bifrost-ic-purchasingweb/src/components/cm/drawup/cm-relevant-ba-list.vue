<script src="../../../../../../bifrost-platform-pa/project.config.js"></script>
<script src="../../../public-path.js"></script>
<template>
  <div class="plan-list">
    <div class="top-btns" v-show="isShowBa && !isChange && !mode">
      <el-button size="small" @click="addBaBtn" :disabled="!showButton || (disabledArr.length < 1 && mode)" icon="el-icon-circle-plus-outline">添加指标</el-button>
      <el-button size="small" @click="deleteBaBtn" :disabled='checkedBaRow.length === 0 || !havePlan || !showButton' plain
                 icon="el-icon-delete">删除
      </el-button>
    </div>
    <div class="bottom-table plan-container">
      <el-table border ref="cmRelevantBaListTable" :data="baDetail" @selection-change="handleSelectionChange"
                style="width: 100%" show-summary
                :summary-method="getSummaries">
        <el-table-column type="selection" align="center" width="50" v-if="havePlan && !isChange"/>
        <el-table-column label="序号" align="center" width="70">
          <template slot-scope="baNum">
            {{ baNum.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="指标编码">
          <template #header><span style="color:#f56c6c;">*</span>指标编码</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan || isCmChange"
                      :disabled="!disabledArr.includes('指标编码') && mode"
                      v-model="row.baCode"
                      readonly
                      placeholder="请选择指标"
                      @click.native="chooseFuncBtn($index)"/>
            <span v-else>{{ row.baCode }}</span>
            <el-input v-model="row.baSourceId" v-show="false"/>
            <el-input v-model="row.baSourceType" v-show="false"/>
            <el-input v-model="row.parentId" v-show="false"/>
          </template>
        </el-table-column>

        <el-table-column align="center" label="指标名称">
          <template #header><span style="color:#f56c6c;">*</span>指标名称</template>
          <template slot-scope='{row}'>
            <el-input disabled v-model="row.baName"/>
          </template>
        </el-table-column>
        <el-table-column label="部门经济分类科目" align="right" v-if="isShowSub">
          <template #header><span style="color:#f56c6c;">*</span>部门经济分类科目</template>
          <template slot-scope='{row}'>
            <el-input :disabled="!disabledArr.includes('部门经济分类科目') && mode" v-model="row.deptEcoSubject"/>
          </template>
        </el-table-column>
        <el-table-column label="指标金额(元)" prop="baAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>指标金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan || isCmChange"
                      class="money"
                      disabled
                      v-model="row.baAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.baAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已用金额(元)" prop="useAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>已用金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan || isCmChange"
                      class="money"
                      disabled
                      v-model="row.useAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.useAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用金额(元)" prop="usableAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>可用金额(元)</template>
          <template slot-scope='{row, $index}'>
            <el-input v-if="havePlan || isCmChange"
                      class="money"
                      disabled
                      v-model="row.usableAmount"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      @blur="formFormatItem(row)"
                      @input="hideError($index)"/>
            <span v-else>{{ row.usableAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="申请金额(元)" prop="applyAmount" align="right">
          <template #header><span style="color:#f56c6c;">*</span>申请金额(元)</template>
          <template slot-scope='{row}'>
            <el-input class="money"
                      :disabled="(isDisabledApplyAmount && !isChange && !isAdditionalRecording) || isShowInvoice || (!disabledArr.includes('申请金额(元)') && mode)"
                      v-if="havePlan || isCmChange"
                      v-model="row.applyAmount"/>
            <span v-else>{{ row.applyAmount }}</span>
          </template>
        </el-table-column>
        <div v-if="isShowInvoice">
          <el-table-column label="发票类型" prop="invoiceType" align="center">
            <template #header><span style="color:#f56c6c;">*</span>发票类型</template>
            <template slot-scope='{row}'>
              <el-radio-group v-model="row.invoiceType" v-if="havePlan || isCmChange" :disabled="isDisabledInvoice || (!disabledArr.includes('发票类型') && mode)"
                              @change="invoiceTypeChange(row)">
                <el-radio label="专票">专票</el-radio>
                <el-radio label="普票">普票</el-radio>
              </el-radio-group>
              <span v-else>{{ row.invoiceType }}</span>
            </template>
          </el-table-column>
          <el-table-column label="税前金额" prop="preTaxAmount" align="right">
            <template #header><span style="color:#f56c6c;">*</span>税前金额</template>
            <template slot-scope='{row, $index}'>
              <el-input class="money"
                        :disabled="(isDisabledInvoice && !isChange  && !isAdditionalRecording) || (!disabledArr.includes('税前金额') && mode)"
                        v-if="havePlan || isCmChange"
                        v-model="row.preTaxAmount"
                        oninput="value=value.replace(/[^0-9.]/g,'')"
                        @blur="formFormatItem(row)"
                        @input="changeInvoiceInfo(row, $index, 'preTaxAmount')"/>
              <span v-else>{{ row.preTaxAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="税率" prop="taxRate" align="right">
            <template #header><span style="color:#f56c6c;">*</span>税率</template>
            <template slot-scope='{row, $index}'>
              <el-select v-model="row.taxRate"
                        :disabled="(isDisabledInvoice && !isChange  && !isAdditionalRecording) || (!disabledArr.includes('税率') && mode)"
                        v-if="havePlan || isCmChange"
                        @change="changeInvoiceInfo(row, $index, 'taxRate')"
                        style="text-align: center; text-align-last: center;">
                <el-option
                  v-for="item in taxRates"
                  :key="item.eleName"
                  :label="item.eleName"
                  :value="item.eleName">
                </el-option>
              </el-select>
              <span v-else>{{ row.taxRate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="税金" prop="taxes" align="right">
            <template #header><span style="color:#f56c6c;">*</span>税金</template>
            <template slot-scope='{row}'>
              <el-input v-if="havePlan || isCmChange"
                        class="money"
                        :disabled="!disabledArr.includes('税金') && mode"
                        v-model="row.taxes"/>
              <span v-else>{{ row.taxes }}</span>
            </template>
          </el-table-column>
          <el-table-column label="税后金额" prop="afterTaxAmount" align="right">
            <template #header><span style="color:#f56c6c;">*</span>税后金额</template>
            <template slot-scope='{row, $index}'>
              <el-input v-if="havePlan || isCmChange"
                        class="money"
                        :disabled="(isDisabledInvoice && !isChange  && !isAdditionalRecording) || (!disabledArr.includes('税后金额') && mode)"
                        v-model="row.afterTaxAmount"
                        oninput="value=value.replace(/[^0-9.]/g,'')"
                        @blur="formFormatItem(row)"
                        @input="changeInvoiceInfo(row, $index, 'afterTaxAmount')"/>
              <span v-else>{{ row.afterTaxAmount }}</span>
            </template>
          </el-table-column>
        </div>
      </el-table>
    </div>

  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'cm-relevant-ba-list',
  props: {
    disabledArr: { type: Array, default: () => [] }, // 审核模式下显示可修改的配置项
    mode: { type: Boolean, default: false } // 是否是审核模式
  },
  data() {
    return {
      checkedBaRow: [],
      baDetail: [],
      purItemDetail: [],
      havePlan: true,
      showButton: true,
      isShowBa: true,
      corrBa: '',
      planData: [],
      isFrame: false,
      isShowSub: false,
      cmRelevantBa: false,
      deptCode: '',
      metaId: '',
      selectSq: '',
      isDisabledApplyAmount: false,
      isChange: false,
      isShowInvoice: false, // 显示发票相关列
      isDisabledInvoice: false, // 是否禁用发票相关列
      taxRates: [],
      isAdditionalRecording: true, // 判断是否已补录
      isCmChange: false
    }
  },
  activated() {
    this.fetchData()
  },
  beforeDestroy() {
    window.$event.$off('getPlanInfo')
  },
  methods: {
    deleteBaBtn() {
      window.$event.$emit('getPlanInfo')
      var flag = true
      for (var i = 0; i < this.checkedBaRow.length; i++) {
        var baIndex = this.baDetail.indexOf(this.checkedBaRow[i]) // 被删除的索引
        this.planData.forEach(plan => {
          var corrBa = plan.corrBa // 字符串
          var corrBaInt = this.$isNotEmpty(corrBa) ? corrBa : ''
          if (corrBaInt.includes(baIndex + 1)) {
            flag = false
          }
        })
      }
      if (!flag) {
        return this.$message.warning('选择数据中被履行计划关联，不能删除')
      }
      for (var j = 0; j < this.checkedBaRow.length; j++) {
        var index = this.baDetail.indexOf(this.checkedBaRow[j])
        this.planData.map((keys) => {
          if (Number(keys.corrBa) !== index + 1) {
            this.$set(keys, 'corrBa', [])
          }
          // keys.corrBa && keys.corrBa.map((key) => {
          //   if (Number(key) !== index + 1) {
          //     // keys = []
          //     this.$set(keys, 'corrBa', [])
          //
          //   }
          // })
        })
        this.baDetail.splice(index, 1)
      }
    },
    formFormatItem(item) {
      item.itemAmount = this.$formatMoney(item.itemAmount)
      item.itemUsableAmount = this.$formatMoney(item.itemUsableAmount)
      item.baAmount = this.$formatMoney(item.baAmount)
      item.useAmount = this.$formatMoney(item.useAmount)
      item.usableAmount = this.$formatMoney(item.usableAmount)
      item.applyAmount = this.$formatMoney(item.applyAmount)
      item.preTaxAmount = this.$formatMoney(item.preTaxAmount)
      item.taxes = this.$formatMoney(item.taxes)
      item.afterTaxAmount = this.$formatMoney(item.afterTaxAmount)
    },
    init(dataVo, mode, formFormat) {
      var cmRelevantBa = dataVo.extData.合同关联指标
      if (cmRelevantBa) {
        var cmRelevantBaShowInvoiceInfo = dataVo.extData.合同关联指标是否显示发票信息
        this.isShowInvoice = cmRelevantBaShowInvoiceInfo
        this.isAdditionalRecording = dataVo.extData.isAdditionalRecording
        if (this.isShowInvoice) {
          this.taxRates = dataVo.extData.taxRates
        }
        this.$nextTick(() => {
          if (dataVo.extData.baDetail) {
            this.baDetail = dataVo.extData.baDetail
            this.baDetail.forEach(item => {
              this.formFormatItem(item)
            })
          }
        })
      }
    },
    hideError(rowIndex) {
      var $rows = $('.plan-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.planError').removeClass('planError')
        }
      })
    },
    addBaBtn() {
      const data = {}
      data.baCode = ''
      data.baName = ''
      data.deptEcoSubject = ''
      data.itemAmount = ''
      data.itemUsableAmount = ''
      data.baAmount = ''
      data.useAmount = ''
      data.applyAmount = ''
      data.invoiceType = this.isDisabledInvoice ? '' : '专票'
      data.preTaxAmount = ''
      data.taxRate = ''
      data.taxes = ''
      data.afterTaxAmount = ''
      this.baDetail = this.baDetail.concat([], data)
      // window.$event.$emit("getRelevantBa",this.baDetail)
    },
    invoiceTypeChange(row) {
      if (this.isShowInvoice) {
        row.applyAmount = row.preTaxAmount
      } else {
        row.applyAmount = row.afterTaxAmount
      }
    },
    changeInvoiceInfo(row, rowIndex, taxCalcStd) {
      this.hideError(rowIndex)
      if (taxCalcStd !== 'taxRate') { // 标识先填写哪个金额 以哪个金额为不变金额计算
        if (this.$isEmpty(row.taxCalcStd)) {
          if (this.$isNotEmpty(row[taxCalcStd])) {
            row.taxCalcStd = taxCalcStd
          }
        }
      }
      let taxRate = row.taxRate
      if (this.$isEmpty(taxRate)) { // 税率为空是 不计算相关金额
        return
      }
      let afterTaxAmount = row.afterTaxAmount
      let preTaxAmount = row.preTaxAmount
      if (taxCalcStd === 'taxRate') { // 税率变化时 定位不变金额
        taxCalcStd = row.taxCalcStd
      }
      // 税率转小数
      taxRate = Number(row.taxRate.replace(/%|\//g, '')) / 100
      if (taxCalcStd === 'afterTaxAmount') { // 以税后金额为准 计算税金和税前金额
        if (this.$isNotEmpty(afterTaxAmount)) {
          afterTaxAmount = parseFloat(this.$unFormatMoney(afterTaxAmount))
          // 税金=申请金额（不含税）*税率
          let taxes = parseFloat(afterTaxAmount * taxRate).toFixed(2)
          // 申请金额（含税）=申请金额（不含税）+税金
          row.preTaxAmount = this.$formatMoney(afterTaxAmount + Number(taxes))
          row.taxes = this.$formatMoney(taxes)
        } else {
          row.preTaxAmount = 0
          row.taxes = 0
        }
      } else if (taxCalcStd === 'preTaxAmount') { // 以税前金额为准 计算税金和税后金额
        // 申请金额（含税）=申请金额（不含税）+税金
        if (this.$isNotEmpty(preTaxAmount)) {
          preTaxAmount = parseFloat(this.$unFormatMoney(preTaxAmount))
          // 申请金额（不含税）=申请金额（含税）÷(1+税率)
          afterTaxAmount = parseFloat(preTaxAmount / (taxRate + 1)).toFixed(2)
          // 税金=申请金额（含税）- 申请金额（不含税）
          row.taxes = (preTaxAmount - afterTaxAmount).toFixed(2)
          row.afterTaxAmount = this.$formatMoney(afterTaxAmount)
        } else {
          row.taxes = 0
          row.afterTaxAmount = 0
        }
      }
      // 计算后赋值申请金额
      if (this.isShowInvoice) {
        row.applyAmount = row.preTaxAmount
      } else {
        row.applyAmount = row.afterTaxAmount
      }
    },
    handleSelectionChange(rows) {
      this.checkedBaRow = rows
    },
    reload(data) {
      if (!this.isChange) {
        this.baDetail = this.baDetail.concat([], data)
      } else {
        this.checkedBaRow[0] = Object.assign(this.checkedBaRow[0], data)
      }
      this.isChange = false
    },
    chooseFuncBtn(index) {
      if (this.baDetail[index].isShow) {
        return
      }
      if (this.selectSq === '是') {
        this.$refDataBaAdvance(
          { '部门ID': this.deptCode, '关闭可用金额控制': '', 'metaId': this.metaId,'删除表单类别条件':'' },
          selectedData => {
            if (selectedData[0].formType === '事前申请单') {
              if (selectedData[0].事前未使用金额 === 0) {
                return this.$message({
                  message: '事前未使用金额为0,请重新选择事前单',
                  type: 'warning'
                })
              } else {
                var flag = false
                if (selectedData[0].bas.length>1){
                  for (let i = 0; i < selectedData[0].bas.length-1; i++) {
                    this.addBaBtn();
                  }
                }
                selectedData[0].bas.forEach(item => {
                  this.baDetail[index].baId = item.ID
                  this.baDetail[index].baCode = item.业务编码
                  this.baDetail[index].baName = item.业务名称
                  this.baDetail[index].deptEcoSubject = item.经济分类
                  this.baDetail[index].baAmount = this.$formatMoney(item.指标金额)
                  this.baDetail[index].useAmount = this.$formatMoney(item.指标已使用.toFixed(2))
                  this.baDetail[index].usableAmount = this.$formatMoney(item.指标未使用.toFixed(2))
                  this.baDetail[index].deptEcoSubjectId = item.经济分类ID
                  this.baDetail[index].accountSub = item.会计科目
                  this.baDetail[index].pmId = item.预算项目ID
                  this.baDetail[index].pmName = item.预算项目
                  this.baDetail[index].baSourceId = selectedData[0].ID
                  this.baDetail[index].baSourceType = '事前申请单'
                  this.baDetail[index].parentId = item.bizid //事前指标id
                  index++
                })
                if (flag) {
                  return this.$message.warning('指标：' + selectedData[0].业务编码 + '已存在，不能新增')
                }
              }
            }else {
              if (selectedData[0].可用金额 === 0) {
                return this.$message({
                  message: '可用金额为0,请重新选预算项目',
                  type: 'warning'
                })
              } else {
                var flag = false
                this.baDetail.forEach(item => {
                  if (selectedData[0].ID === item.baId) {
                    flag = true
                  }
                })
                if (flag) {
                  return this.$message.warning('指标：' + selectedData[0].业务编码 + '已存在，不能新增')
                }
                this.$nextTick(() => {
                  this.baDetail[index].baId = selectedData[0].ID
                  this.baDetail[index].baCode = selectedData[0].业务编码
                  this.baDetail[index].baName = selectedData[0].业务名称
                  this.baDetail[index].deptEcoSubject = selectedData[0].经济分类
                  this.baDetail[index].baAmount = this.$formatMoney(selectedData[0].指标总金额)
                  if (this.$isNotEmpty(selectedData[0].下达金额)) {
                    this.baDetail[index].baAmount = this.$formatMoney(selectedData[0].下达金额)
                  }
                  this.baDetail[index].useAmount = this.$formatMoney(selectedData[0].指标已使用.toFixed(2))
                  this.baDetail[index].usableAmount = this.$formatMoney(selectedData[0].可用金额.toFixed(2))
                  this.baDetail[index].deptEcoSubjectId = selectedData[0].经济分类ID
                  this.baDetail[index].accountSub = selectedData[0].会计科目
                  this.baDetail[index].pmId = selectedData[0].预算项目ID
                  this.baDetail[index].pmName = selectedData[0].预算项目
                })
              }
            }
          })
      } else {
        this.$refDataBa(
          { '部门ID': this.deptCode, '关闭可用金额控制': '' },
          selectedData => {
            if (selectedData[0].可用金额 === 0) {
              return this.$message({
                message: '可用金额为0,请重新选预算项目',
                type: 'warning'
              })
            } else {
              var flag = false
              this.baDetail.forEach(item => {
                if (selectedData[0].ID === item.baId) {
                  flag = true
                }
              })
              if (flag) {
                return this.$message.warning('指标：' + selectedData[0].业务编码 + '已存在，不能新增')
              }
              this.$nextTick(() => {
                this.baDetail[index].baId = selectedData[0].ID
                this.baDetail[index].baCode = selectedData[0].业务编码
                this.baDetail[index].baName = selectedData[0].业务名称
                this.baDetail[index].deptEcoSubject = selectedData[0].经济分类
                this.baDetail[index].baAmount = this.$formatMoney(selectedData[0].指标总金额)
                if (this.$isNotEmpty(selectedData[0].下达金额)) {
                  this.baDetail[index].baAmount = this.$formatMoney(selectedData[0].下达金额)
                }
                this.baDetail[index].useAmount = this.$formatMoney(selectedData[0].指标已使用.toFixed(2))
                this.baDetail[index].usableAmount = this.$formatMoney(selectedData[0].可用金额.toFixed(2))
                this.baDetail[index].deptEcoSubjectId = selectedData[0].经济分类ID
                this.baDetail[index].accountSub = selectedData[0].会计科目
                this.baDetail[index].pmId = selectedData[0].预算项目ID
                this.baDetail[index].pmName = selectedData[0].预算项目
              })
            }
          })
      }
    },
    selectCorrBa() {
      var baDetailLength = this.baDetail.length
      var count = []
      for (let i = 0; i < baDetailLength; i++) {
        count.push(i + 1)
      }
      this.corrBa = count
    },
    getSummaries(param) {
      const _this = this
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 3) {
          sums[index] = '合计'
          return
        }
        if (column.label === '指标金额(元)' || column.label === '已用金额(元)' || column.label === '可用金额(元)'
          || column.label === '申请金额(元)' || column.label === '税前金额'
          || column.label === '税金' || column.label === '税后金额') {
          const values = data.map(item => {
            const money = _this.$unFormatMoney(item[column.property])
            return Number(money)
          })
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                if (typeof prev !== 'number' && prev.indexOf(',') !== -1) {
                  prev = Number(_this.$fixMoney(prev))
                }
                if (typeof curr !== 'number' && curr.indexOf(',') !== -1) {
                  curr = Number(_this.$fixMoney(curr))
                }
                return _this.$formatMoney(Number(prev) + Number(curr))
              } else {
                return prev
              }
            }, 0)
            sums[index] += ''
          } else {
            sums[index] = ''
          }
        }
      })

      return sums
    },
    getbaDetailData() {
      return this.baDetail
    },
    fetchData() {
      // this.baDetail = this.baDetail
      this.$nextTick(() => {
        this.baDetail = this.baDetail
        window.$event.$off('getbaDetailData', null, 'componentA')
        window.$event.$on('getbaDetailData', () => {
          this.$nextTick(() => {
            const baDetailData = this.getbaDetailData()
            if (this.$isEmpty(baDetailData)) return
            const filteredArr = baDetailData.filter((element) => {
              return element !== undefined
            }, 'componentA')
            window.$event.$emit('backData', filteredArr)
          })
        })
        window.$event.$off('planInfoReceived', null, 'componentB')
        window.$event.$on('planInfoReceived', (planInfo) => {
          this.planData = planInfo
        }, 'componentB')
      })
      // this.reload()
    }
  }
}
</script>

<style scoped lang="scss">
.plan-list {
  width: 100%;
  height: 100% !important;
  display: flex;
  flex-direction: column;

  .total-amt {
    margin-left: 10px;
    font-size: 14px;
  }

  .top-btns {
    padding-bottom: 10px;
    margin-left: 0px;
    width: 27%;
    display: flex;

    .el-input {
      margin-left: 5px;
    }
  }

  .bottom-table {
    flex: 1;
  }

  .el-table /deep/.el-table__cell {
    padding: 4px 0px !important;
  }
  .el-table /deep/.el-table__footer-wrapper .el-table__cell {
    padding: 10px 0px !important;
  }

  /deep/ .el-table .warning-row {
    background-color: rgb(255, 43, 43) !important;
  }

  /deep/ .el-table--border .el-table__cell:first-child .cell {
    padding: 0px 5px !important
  }

  /deep/ .planError {
    border-color: #ff5c00 !important;
  }
}
</style>
<style lang="scss">
.mini-table .plan-list .el-table .el-table-column--selection .cell {
  padding: 0px 0px !important;
}

.mini-table .plan-list .el-table .cell {
  padding: 0px 3px;
}

.mini-table .plan-list .el-table input {
  padding: 0px 3px;
  height: 28px;
}

.mini-table .plan-list .el-table .el-input__suffix {
  right: -4px;
}

.mini-table .plan-list .el-table .el-select .el-input .el-select__caret {
  font-size: 12px;
}

.mini-table .plan-list .el-table .cell .el-date-editor .el-input__prefix {
  display: none;
}

.plan-container input.el-input__inner {
  font-size: 12px;
}

.plan-container .money input.el-input__inner {
  text-align: right;
  padding-right: 5px;
}
</style>
