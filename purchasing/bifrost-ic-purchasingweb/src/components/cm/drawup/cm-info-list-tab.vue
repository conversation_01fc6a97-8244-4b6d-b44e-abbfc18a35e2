<template>
  <div class="common-page multipleTabsContracparty">
    <div style="height: 300px">
      <cm-info-list ref="cmInfoList" :isBaseaAplication=true></cm-info-list>
    </div>
  </div>
</template>

<script>
export default {
  name: 'cm-info-list-tab',
  data() {
    return {
      baseListFormObj: undefined, // base-list-form对象
      formCanvasMeta: undefined,
      formCanvasObj: undefined
    }
  },

  methods: {
    initDataVo(baseListFormObj, dataVo, formCanvasObj) { // 主表单初始之后，本组件进行初始化
      this.baseListFormObj = baseListFormObj
      this.formCanvasMeta = baseListFormObj.formCanvasMeta
      this.initAssembly(dataVo, {}, formCanvasObj.$refs.formFormat)
    },
    fillDataVoBeforeSave(dataVo) { // 执行保存时，本组件填充数据
      const cmId = this.$refs.cmInfoList.cmId
      if (this.$isNotEmpty(cmId)) {
        dataVo.extData.cmId = cmId
      }
      dataVo.extData.contracpartyInfo = this.$refs.cmInfoList.cmInfoData.contracpartyInfo
      return dataVo
    },
    showAfter(dataVo) { // 切换到当前页签时调用这个方法
    },
    initAssembly(dataVo, mode, formFormat, assemblyData, initBeforeColItems) {
      this.$refs.cmInfoList && this.$refs.cmInfoList.init(dataVo, mode, formFormat)
    },
    triggerAssemblyEvent(itemName, itemValue, fcModel) {

    }
  }
}
</script>
<style>
  .multipleTabsContracparty .contracparty-list { height: 100%; padding-top: 0px; }
</style>
