<template>
  <div style="width: 100%;height:100%">
    <div style="margin:6px 0">履行计划列表</div>
        <el-table
          ref="refTable"
          class="mini-table"
          :data="tableData"
          @selection-change="rowChecked"
          @row-dblclick="rowDblclick"
          border

          style="width: 100%;height:90% !important">
          <el-table-column type="selection" width="35"/>
          <el-table-column
            v-for="(item, index) in columns"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
            :align="item.align"
            :sortable="item.sortable"
            header-align="center"
            show-overflow-tooltip
            :key='index'>
            <template slot-scope="scope">
              <slot name="tableRowSlot" :slotScope="scope">
                {{formatColData(scope.row, scope.column)}}
              </slot>
            </template>
          </el-table-column>
        </el-table>
  </div>
</template>

<script>
export default {
  name: 'contract-expand-list',
  data() {
    return {
      columns: [],
      tableData: [],
      columnTypes: {},
      multiple: false,
      activeTab: '',
      checkedRow: undefined,
      checkedRows: []
    }
  },
  methods: {
    rowDblclick(row) {
      this.$event(this, 'refTargetDbClick', { tab: this.activeTab, list: [row] })
    },
    init(parmas, activeTab, ckeckedId) {
      this.activeTab = activeTab
      const ps = this.$clone(parmas)
      var dataMap = {}
      ps.exParams = `${parmas.exParams}-扩展`
      this.$callApiParams(`${ps.apiKey}-扩展`, ps,
        result => {
          this.tableData = result && result.data
          this.tableData.forEach(row => { dataMap[row.ID] = row })
          this.$nextTick(() => {
            ckeckedId && this.$refs.refTable.toggleRowSelection(dataMap[ckeckedId], true)
          })
          this.columnTypes = {}
          if (result.attributes && result.attributes.columns) {
            this.columns = result.attributes.columns
            result.attributes.columns.forEach(col => {
              this.columnTypes[col.prop] = col.colType
            })
          }
          return true
        }, result => {
        })
    },
    rowChecked(rows) {
      if (!this.multiple) { // 单选处理
        const table = this.$refs.refTable
        var checkedRows = this.$getTableSelection(table)
        this.checkedRows = checkedRows
        if (this.checkedRow && checkedRows.length > 1) {
          var newCheckedRow = (checkedRows[0].ID === this.checkedRow.ID)
            ? checkedRows[1] : checkedRows[0]
          table.clearSelection()
          table.toggleRowSelection(newCheckedRow, true)
        } else {
          this.checkedRow = checkedRows[0]
          if (checkedRows.length > 1) {
            table.clearSelection()
            table.toggleRowSelection(this.checkedRow, true)
          }
        }
      }
    },
    formatColData(row, column) {
      const colValue = row[column.property] // 列原始值
      if (this.columnTypes[column.property] === '金额') {
        return this.$formatMoney(colValue)
      }
      return colValue
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
