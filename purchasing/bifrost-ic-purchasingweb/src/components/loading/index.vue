<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-27 18:27:58
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-09 17:32:12
-->
<template>
  <div v-if="show" class="loading-container" :style="{'top': hasTop ? '40px' : ''}">
    <i class="el-icon-loading"></i>
  </div>
</template>

<script>
export default {
  name: 'loading',
  props: {
    hasTop: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false
    }
  }
}
</script>

<style lang="scss">
  .loading-container {
    position: absolute;
    top: 2px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    background: #fff;
    font-size: 24px;
    color: #000;
    z-index: 999999;
    .el-icon-loading {
      position: absolute;
      top: 50%;
      left: 50%;
    }
  }
</style>

