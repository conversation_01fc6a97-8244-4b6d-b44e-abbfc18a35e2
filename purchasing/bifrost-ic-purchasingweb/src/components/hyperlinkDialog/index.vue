<!--
 * @Description: 超链接详情弹窗
 * @version:
 * @Author: zhangshaowu <EMAIL>
 * @Date: 2024-05-13 14:19:56
 * @LastEditors: zhangshaowu <EMAIL>
 * @LastEditTime: 2024-06-05 16:53:15
-->
<template>
    <el-dialog
    append-to-body
    :title="title"
    :visible.sync="isdialog"
    width="1200px"
    :close-on-click-modal='false'
    @close="handleClose">
        <div class="dialogWarp">
            <block-view v-if="isBlockView" ref="blockView"/>
            <form-canvas v-else ref="formCanvas"/>
        </div>
    </el-dialog>
</template>

<script>
export default({
  name: 'hyperlinkDialog',
  data() {
    return {
      title: '合同详情',
      isdialog: false,
      isBlockView: false
    }
  },
  methods: {
    init(data, index) {
      const flag = index || 0
      const param = data?.prop?.colItem || {
        ...data,
        hyperlinkId: data.baId
      }
      const hyperlinkId = param.hyperlinkId.split(',')
      var params = {
        isApply: false,
        isEdit: false,
        isAuditMode: true,
        mode: '详情',
        dataId: hyperlinkId[flag],
        viewId: param.viewId
      }
      const callBack = (data = {}) => {
        this.title = data?.meta?.name || data?.containers[0]?.blocks[0]?.data?.meta?.name || '合同详情' // 此处合同相关详情取值不规范 所以给一个默认值
        this.isdialog = true
        this.$nextTick(() => {
          if (data.containers) {
            this.isBlockView = true
            const _this = this
            setTimeout(() => {
              _this.$refs.blockView.init(undefined, params)
            }, 100)
          } else {
            this.isBlockView = false
            const _this = this
            setTimeout(() => {
              _this.$refs.formCanvas.initByDataVo(data, '详情',
                undefined, undefined,
                undefined, undefined)
            }, 100)
          }
        })
      }
      this.$callApiParams('selectCformVo', params,
        (result) => {
          callBack(result.data)
          return true
        })
    },
    handleClose() {
      this.isdialog = false
    }
  }
})
</script>

<style lang='scss' scoped>
.dialogWarp{
    height: 500px;
}
</style>
