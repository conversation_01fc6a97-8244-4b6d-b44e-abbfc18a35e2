<template>
  <div class="fullscreen-container">
    <!-- 功能区域 -->
    <div class="px-5 mb-4 mt-2 flex-shrink-0">
      <div class="bg-white rounded-xl shadow-lg p-4 animate-fade-in border border-gray-100">
        <div class="flex flex-col md:flex-row justify-between items-stretch gap-4" style="min-height: 300px;">
          <!-- 左侧：功能按钮组 -->
          <div class="w-full md:w-5/5 h-full flex flex-col">
            <h3 class="text-base font-semibold text-gray-700 mb-3 flex items-center gap-2 justify-between line-1">
              <div class="flex items-center gap-2">
                <div class="bg-indigo-100 p-1.5 rounded-lg text-indigo-600">
                  <i class="fas fa-th-large"></i>
                </div>
                常用功能
              </div>
              <div class="flex">
                <el-button type="text" @click="showFunctionSelector">
                  <i class="fas fa-plus"></i>
                  <span>添加功能</span>
                </el-button>
                <el-button type="text" @click="toggleEditMode">
                  <i :class="isEditMode ? 'fas fa-check' : 'fas fa-cog'"></i>
                  <span>{{ isEditMode ? '完成' : '编辑' }}</span>
                </el-button>
              </div>
            </h3>

            <div class="functions-wrapper flex-1 bg-gradient-to-br from-gray-50 to-white p-2 rounded-xl border border-gray-100"
                 :class="{ 'edit-mode': isEditMode }">
              <div class="functions-grid" ref="functionContainer">
                <!-- 默认功能 -->
                <div v-for="func in defaultFunctions"
                     :key="func.code"
                     class="function-item default-function">
                  <el-button class="action-btn"
                           :class="func.colorClass"
                           @click="handleFunctionClick(func)">
                    <i :class="func.icon"></i>
                    <span class="font-medium">{{ func.text }}</span>
                  </el-button>
                </div>

                <!-- 自定义功能 -->
                <div v-for="func in customFunctions"
                     :key="func.code"
                     class="function-item custom-function"
                     draggable="true"
                     @dragstart="handleDragStart($event, func)"
                     @dragend="handleDragEnd"
                     @dragover.prevent
                     @dragenter.prevent="handleDragEnter($event, func)"
                     @dragleave.prevent="handleDragLeave"
                     @drop.prevent="handleDrop($event, func)">
                  <el-button class="action-btn"
                           :class="func.colorClass"
                           @click="handleFunctionClick(func)">
                    <i :class="func.icon"></i>
                    <span class="font-medium">{{ func.text }}</span>
                  </el-button>
                  <el-button v-if="isEditMode"
                           class="remove-btn"
                           type="text"
                           @click="removeFunction(func)">
                    <i class="fas fa-times"></i>
                  </el-button>
                </div>

                <!-- 全部功能按钮 -->
                <div class="function-item" @click="showAllFunctions">
                  <div class="add-function-btn">
                    <i class="fas fa-ellipsis"></i>
                    <span>全部功能</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 项目统计 -->
            <h3 class="text-base font-semibold text-gray-700 mt-3 mb-3 flex items-center gap-2 justify-between line-1">
              <div class="flex items-center gap-2">
                <div class="bg-indigo-100 p-1.5 rounded-lg text-indigo-600">
                  <i class="fas fa-th-large"></i>
                </div>
                我的项目统计（与我有关的项目）
              </div>
            </h3>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="hover" class="stat-card">
                  <div class="flex justify-between items-center">
                    <div class="stat-label">总项目数</div>
                    <div class="stat-value">{{ statistics.total }}</div>
                    <div class="stat-icon bg-blue-100 text-blue-600">
                      <i class="fas fa-list"></i>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover" class="stat-card">
                  <div class="flex justify-between items-center">
                    <div class="stat-label">采购中项目</div>
                    <div class="stat-value">{{ statistics.inProgress }}</div>
                    <div class="stat-icon bg-blue-100 text-blue-600">
                      <i class="fas fa-list-check"></i>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover" class="stat-card">
                  <div class="flex justify-between items-center">
                    <div class="stat-label">已完成项目</div>
                    <div class="stat-value">{{ statistics.completed }}</div>
                    <div class="stat-icon bg-blue-100 text-blue-600">
                      <i class="fas fa-circle-check"></i>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <!-- 右侧：待办事项 -->
          <div class="w-full md:w-2/5 p-4 bg-gradient-to-br from-gray-50 to-white rounded-xl border border-gray-100 h-full flex flex-col shadow-sm">
            <h3 class="text-lg font-semibold text-gray-700 mb-3 flex items-center gap-2 relative">
              <div class="bg-blue-100 p-1.5 rounded-lg text-blue-600">
                <i class="fas fa-tasks"></i>
              </div>
              <span class="relative">
                通知与我的待办
                <span class="absolute -top-2 -right-6 text-xs bg-red-500 text-white px-1.5 py-0.5 rounded-full shadow-sm flex items-center justify-center min-w-[20px] h-5">
                  {{ todoList.length }}
                </span>
              </span>
            </h3>

            <div class="flex-1 overflow-auto pr-1">
              <div v-for="todo in todoList"
                   :key="todo.id"
                   class="todo-item flex items-center hover:shadow-md">
                <div :class="['w-2 h-2 rounded-full shrink-0', todo.statusClass]"></div>
                <div class="text-gray-700 flex-1">{{ todo.content }}</div>
                <el-button type="text" @click="handleTodoClick(todo)">
                  <i class="fas fa-arrow-right"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="project-table-container">
      <el-card shadow="hover" class="h-full">
        <div class="flex flex-col">
          <div class="flex justify-between items-center p-3 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
            <h2 class="text-lg font-bold text-gray-800 flex items-center gap-2">
              <div class="bg-blue-100 p-1 rounded-lg text-blue-600">
                <i class="fas fa-clipboard-list"></i>
              </div>
              我的项目（与我有关的项目）
            </h2>
            <div class="flex items-center">
              <el-input
                v-model="searchQuery"
                placeholder="请输入项目名称或项目编号"
                prefix-icon="el-icon-search"
                clearable
                @clear="handleSearch"
                @keyup.enter.native="handleSearch">
              </el-input>
              <el-button type="primary" @click="handleSearch">
                <i class="fas fa-search"></i>
                查询
              </el-button>
            </div>
          </div>

          <el-table
            :data="filteredProjects"
            style="width: 100%"
            @row-click="handleRowClick">
            <el-table-column
              prop="id"
              label="序号"
              width="70"
              align="center">
            </el-table-column>
            <el-table-column
              prop="name"
              label="项目名称"
              width="150">
              <template slot-scope="scope">
                <div class="flex items-center">
                  <div>
                    <div class="font-medium">{{ scope.row.name }}</div>
                    <div class="text-xs text-gray-500">{{ scope.row.type }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="code"
              label="项目编号"
              width="150">
            </el-table-column>
            <el-table-column
              label="招标阶段">
              <template slot-scope="scope">
                <div class="flex flex-wrap">
                  <el-tag
                    v-for="(status, index) in scope.row.statuses"
                    :key="index"
                    :type="getStatusType(status.type)"
                    class="mr-1 mb-1">
                    <i :class="status.icon"></i>
                    {{ status.text }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[5, 10, 15, 20]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next"
              :total="total">
            </el-pagination>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 功能选择器弹窗 -->
    <el-dialog
      title="添加常用功能"
      :visible.sync="functionSelectorVisible"
      width="50%"
      :before-close="handleFunctionSelectorClose">
      <div class="grid grid-cols-3 gap-4">
        <el-button
          v-for="option in availableFunctions"
          :key="option.code"
          :disabled="isFunctionSelected(option.code)"
          :class="{'selected': isFunctionSelected(option.code)}"
          @click="toggleFunctionSelection(option)">
          <i :class="option.icon"></i>
          <span>{{ option.text }}</span>
        </el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="functionSelectorVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAddFunctions">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 全部功能弹窗 -->
    <el-dialog
      title="全部功能"
      :visible.sync="allFunctionsVisible"
      width="70%"
      :before-close="handleAllFunctionsClose">
      <div class="grid grid-cols-4 gap-4">
        <el-button
          v-for="func in allFunctions"
          :key="func.code"
          :class="func.colorClass"
          @click="handleAllFunctionClick(func)">
          <i :class="func.icon"></i>
          <span>{{ func.text }}</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ProjectManagement',
  data() {
    return {
      isEditMode: false,
      searchQuery: '',
      currentPage: 1,
      pageSize: 5,
      total: 0,
      draggedItem: null,
      functionSelectorVisible: false,
      allFunctionsVisible: false,
      selectedFunctions: [],

      // 默认功能
      defaultFunctions: [
        { code: 'purchase', text: '我要采购', icon: 'fas fa-shopping-cart', colorClass: 'bg-gradient-to-br from-blue-600 to-blue-600' },
        { code: 'change', text: '我要变更', icon: 'fas fa-exchange-alt', colorClass: 'bg-gradient-to-br from-green-600 to-green-600' },
        { code: 'search', text: '我要查询', icon: 'fas fa-search', colorClass: 'bg-gradient-to-br from-purple-600 to-purple-600' }
      ],

      // 自定义功能
      customFunctions: [],

      // 待办事项
      todoList: [
        { id: 1, content: '汇文楼H6负一楼全空间精密测量实验室装修（2023-ZS-001）需要您修改信息后重新提交审核', status: 'warning', statusClass: 'bg-yellow-500' },
        { id: 2, content: '期刊/论文出版服务（2023-XX-002）需要您确认采购文件', status: 'info', statusClass: 'bg-blue-500' },
        { id: 3, content: '政府办公软件升级（2023-XX-002）采购项目待提交审核', status: 'success', statusClass: 'bg-green-500' }
      ],

      // 项目列表
      projectList: [
        { id: 1, name: '汇文楼H6负一楼全空间精密测量实验室装修', code: '2023-ZS-001', status: 'warning', statusClass: 'bg-yellow-500' },
        { id: 2, name: '期刊/论文出版服务', code: '2023-XX-002', status: 'info', statusClass: 'bg-blue-500' },
        { id: 3, name: '政府办公软件升级', code: '2023-XX-002', status: 'success', statusClass: 'bg-green-500' }
      ],

      // 统计数据
      statistics: {
        total: 24,
        inProgress: 18,
        completed: 5
      },

      // 可用功能列表
      availableFunctions: [
        { code: 'history', text: '变更记录', icon: 'fas fa-history', colorClass: 'bg-gradient-to-br from-orange-500 to-orange-600' },
        { code: 'report', text: '报表中心', icon: 'fas fa-file-alt', colorClass: 'bg-gradient-to-br from-red-500 to-red-600' },
        { code: 'notice', text: '通知公告', icon: 'fas fa-bell', colorClass: 'bg-gradient-to-br from-yellow-500 to-yellow-600' },
        // ... 其他功能选项
      ]
    }
  },
  computed: {
    filteredProjects() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.projectList.slice(start, end)
    }
  },
  methods: {
    toggleEditMode() {
      this.isEditMode = !this.isEditMode
    },
    showFunctionSelector() {
      this.functionSelectorVisible = true
    },
    showAllFunctions() {
      this.allFunctionsVisible = true
    },
    handleFunctionSelectorClose() {
      this.functionSelectorVisible = false
      this.selectedFunctions = []
    },
    handleAllFunctionsClose() {
      this.allFunctionsVisible = false
    },
    isFunctionSelected(code) {
      return this.defaultFunctions.some(f => f.code === code) ||
             this.customFunctions.some(f => f.code === code)
    },
    toggleFunctionSelection(func) {
      const index = this.selectedFunctions.findIndex(f => f.code === func.code)
      if (index === -1) {
        this.selectedFunctions.push(func)
      } else {
        this.selectedFunctions.splice(index, 1)
      }
    },
    confirmAddFunctions() {
      this.customFunctions.push(...this.selectedFunctions)
      this.functionSelectorVisible = false
      this.selectedFunctions = []
      this.saveUserConfig()
    },
    removeFunction(func) {
      const index = this.customFunctions.findIndex(f => f.code === func.code)
      if (index !== -1) {
        this.customFunctions.splice(index, 1)
        this.saveUserConfig()
      }
    },
    handleDragStart(event, func) {
      if (!this.isEditMode) return
      this.draggedItem = func
      event.dataTransfer.effectAllowed = 'move'
    },
    handleDragEnd() {
      this.draggedItem = null
    },
    handleDragEnter(event, func) {
      if (!this.isEditMode) return
      event.target.closest('.function-item').classList.add('drag-over')
    },
    handleDragLeave(event) {
      event.target.closest('.function-item').classList.remove('drag-over')
    },
    handleDrop(event, func) {
      if (!this.isEditMode) return
      const fromIndex = this.customFunctions.indexOf(this.draggedItem)
      const toIndex = this.customFunctions.indexOf(func)
      if (fromIndex !== -1 && toIndex !== -1) {
        this.customFunctions.splice(fromIndex, 1)
        this.customFunctions.splice(toIndex, 0, this.draggedItem)
        this.saveUserConfig()
      }
      event.target.closest('.function-item').classList.remove('drag-over')
    },
    handleSearch() {
      // 实现搜索逻辑
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    handleCurrentChange(val) {
      this.currentPage = val
    },
    handleRowClick(row) {
      // 实现行点击逻辑
    },
    handleTodoClick(todo) {
      // 实现待办点击逻辑
    },
    handleFunctionClick(func) {
      // 实现功能点击逻辑
    },
    getStatusType(type) {
      const types = {
        success: 'success',
        primary: 'primary',
        warning: 'warning',
        info: 'info'
      }
      return types[type] || 'info'
    },
    saveUserConfig() {
      localStorage.setItem('userFunctionConfig', JSON.stringify({
        customFunctions: this.customFunctions
      }))
    },
    loadUserConfig() {
      const savedConfig = localStorage.getItem('userFunctionConfig')
      if (savedConfig) {
        const config = JSON.parse(savedConfig)
        this.customFunctions = config.customFunctions || []
      }
    }
  },
  created() {
    this.loadUserConfig()
  }
}
</script>

<style scoped>
.fullscreen-container {
  width: 100%;
  max-width: 100%;
  padding: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(180deg, #f9fafb 0%, #f3f4f6 100%);
}

.functions-wrapper {
  height: auto;
  max-height: 280px;
  overflow-y: auto !important;
  position: relative;
  background: transparent !important;
  padding-right: 4px;
  overflow-x: hidden !important;
  width: 100%;
  max-width: 100%;
}

.functions-wrapper.edit-mode .custom-function {
  cursor: grab;
  position: relative;
}

.functions-wrapper.edit-mode .custom-function:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed rgba(59, 130, 246, 0.4);
  border-radius: 12px;
  pointer-events: none;
}

.function-item {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 80px;
  transition: transform 0.2s, box-shadow 0.2s;
  will-change: transform, box-shadow;
}

.function-item.dragging {
  opacity: 0.8;
  transform: scale(1.05);
  z-index: 20;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  cursor: grabbing;
}

.function-item.drag-over {
  transform: translateY(5px);
  position: relative;
}

.function-item.drag-over::after {
  content: '';
  position: absolute;
  top: -8px;
  left: 0;
  right: 0;
  height: 4px;
  background: #3b82f6;
  border-radius: 2px;
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: #ef4444;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 10;
}

.functions-wrapper.edit-mode .custom-function .remove-btn {
  opacity: 1;
  visibility: visible;
}

.todo-item {
  padding: 0px 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
  margin-bottom: 0px;
}

.todo-item:hover {
  background-color: #f8fafc;
  border-left-color: #2979ff;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateX(3px);
}

.stat-card {
  padding: 15px 20px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 0px;
  transition: all 0.3s;
  border: 1px solid rgba(0, 0, 0, 0.03);
  height: 83px;
}

.stat-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-label {
  font-size: 16px;
  color: #4b5563;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 20px;
}

.project-table-container {
  flex: 1 1 auto;
  overflow: hidden;
  padding: 0 16px 16px;
  height: auto;
  min-height: 200px;
  max-height: calc(100vh - 426px);
  display: flex;
  flex-direction: column;
}

.pagination-container {
  flex-shrink: 0;
  border-top: 1px solid #e5e7eb;
  background: linear-gradient(90deg, #fff, #f9fafb);
  padding: 6px 16px;
  position: sticky;
  bottom: 0;
  background-color: white;
  z-index: 10;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.function-item.custom-function {
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes scaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.8);
    opacity: 0;
  }
}

.function-item.removing {
  animation: scaleOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}
</style>
