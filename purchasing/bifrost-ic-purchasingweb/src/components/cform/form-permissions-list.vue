<template>
  <div style="height: 100%">
    <el-row>
      <el-col :span="12">
        <span>表单列表</span>
      </el-col>
      <el-col :span="12">
        <span style="margin-left: 1px">用户列表</span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <div style="float: left;border: #DDDDDD solid 1px;height: 497px;padding: 5px; width: 310px;">
          <sup-tree :setting="setting"
                    ref="supTree"
                    :nodes="treeData"
                    :is-popover="false"
                    :edit-enable="true"
                    :autoDeleteChildren="false">

          </sup-tree>
        </div>
      </el-col>
      <el-col :span="12">
        <div style="float: right;border: #DDDDDD solid 1px;height: 497px;padding: 5px; width: 330px;">
          <sup-tree :setting="userSetting"
                    ref="userSupTree"
                    :nodes="userData"
                    :is-popover="false"
                    :edit-enable="true">

          </sup-tree>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'formPermissionsList',
  data() {
    return {
      formId: '',
      formType: '',
      userDatas: [],
      treeData: [],
      dataType: 'CformMetaEntity',
      setting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'label'
          }
        },
        view: {
          selectedMulti: false, // 按住ctrl是否可以多选
          showIcon: true,
          showLine: true,
          fontCss: function(treeId, treeNode) {
            return (treeNode.searchNode) ? { 'color': '#A60000', 'font-weight': 'bold' } : ''
          }
        },
        callback: {
          onClick: this.formClick,
          onCheck: this.formCheckChange
        }
      },
      userData: [],
      deptData: [],
      userSetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'userCode',
            pIdKey: 'parentCode'
          },
          key: {
            name: 'userName',
            id: 'bizId'
          }
        },
        view: {
          showIcon: true,
          showLine: true
        },
        callback: {
          onCheck: this.userCheckChange
        }
      },
      nodeId: '',
      nodeName: '',
      metaId: '',
      metaVersionId: '',
      WfUserDeptVo: {},
      userDept: [],
      currUserCode: '', // 当前选中用户编码
      currUserName: '', // 当前选中用户名称
      formPermissions: [],
      formPermissionsVo: {}
    }
  },
  mounted() {
    this.selectClassifyList()
    this.getAllUser()
  },
  methods: {
    // 用户选择发生改变事件
    userCheckChange(event, treeId, treeNode) {
      if (treeNode.checked) {
        if (Array.isArray(treeNode.children) && treeNode.children.length) {
          treeNode.children.forEach(item => {
            this.userDatas.push({ id: item.id, userCode: item.userCode })
          })
        } else {
          this.userDatas.push({ id: treeNode.id, userCode: treeNode.userCode })
        }
        if (this.$isNotEmpty(this.formId)) {
          var formUserEntity = {}
          formUserEntity.formId = this.formId
          formUserEntity.formType = this.formType
          formUserEntity.userId = treeNode.id
          formUserEntity.userCode = treeNode.userCode
          this.formPermissions.push(formUserEntity)

          this.formPermissions = this.formPermissions.filter(
            (obj, index) =>
              this.formPermissions.findIndex(
                (item) => item.formId === obj.formId && item.userId === obj.userId && item.userCode === obj.userCode
              ) === index
          )
        }
        this.deleteFormPermissions(this.formPermissions)
      } else {
        var deleteIndex
        var deleteId
        var userId
        if (Array.isArray(treeNode.children) && treeNode.children.length) {
          this.userDatas = this.userDatas.filter(item => {
            const obj = treeNode.children.find(child => item.id === child.id)
            if (!obj) return item
          })

          for (let i = 0; i < this.formPermissions.length; i++) {
            for (let j = 0; j < treeNode.children.length; j++) {
              if (this.formPermissions[i].userId === treeNode.children[j].id &&
                this.formPermissions[i].userCode === treeNode.children[j].userCode &&
                this.formPermissions[i].formId === this.formId) {
                deleteIndex = i
                deleteId = this.formPermissions[i].formId
                userId = this.formPermissions[i].userId
              }
            }
            this.deleteFormPermissions(this.formPermissions, deleteIndex, deleteId, userId)
          }
        } else {
          this.userDatas = this.userDatas.filter(item => treeNode.id !== item.id)

          for (let i = 0; i < this.formPermissions.length; i++) {
            if (this.formPermissions[i].userId === treeNode.id &&
              this.formPermissions[i].userCode === treeNode.userCode &&
              this.formPermissions[i].formId === this.formId) {
              deleteIndex = i
              deleteId = this.formPermissions[i].formId
              userId = this.formPermissions[i].userId
            }
          }
          this.deleteFormPermissions(this.formPermissions, deleteIndex, deleteId, userId)
        }
      }
    },
    deleteFormPermissions(formPermissions, deleteIndex, deleteId, userId) {
      const keyObs = {}
      formPermissions.forEach(item => {
        if (!this.$isNotEmpty(keyObs[item.formId])) {
          keyObs[item.formId] = []
        }
        keyObs[item.formId].push(item)
      })

      if (this.$isNotEmpty(deleteIndex)) {
        const keys = Object.keys(keyObs)
        keys.forEach((key) => {
          if (deleteId === key) {
            keyObs[key] = keyObs[key].filter(item => item.userId !== userId)
          }
        })

        let newArr = []
        Object.values(keyObs).forEach(item => {
          newArr = [...newArr, ...item]
        })
        this.formPermissions = newArr
      }
      this.formPermissionsVo = keyObs
    },
    // 表单点击发生改变事件
    formClick(event, treeId, treeNode) {
      this.formId = treeNode.itemKey
      this.$callApiParams('getFormTypeName',
        { formId: this.formId }, result => {
          this.formType = result.data
          this.getFormUser(this.formId)
          return true
        })
    },
    selectUser(formId, formPermissions, addUserCode) {
      const userCode = []
      for (let i = 0; i < formPermissions.length; i++) {
        if (formPermissions[i].formId === formId) {
          userCode.push(formPermissions[i].userCode)
        }
      }
      this.$refs.userSupTree.treeObj.checkAllNodes(false)
      var newUserCode = userCode.concat(addUserCode)
      if (this.$isNotEmpty(newUserCode)) {
        for (var i = 0; i < newUserCode.length; i++) {
          const node = this.$refs.userSupTree.treeObj.getNodeByParam('userCode', newUserCode[i], null)
          this.$refs.userSupTree.treeObj.checkNode(node, true)
          var formUserEntity = {}
          formUserEntity.formId = formId
          formUserEntity.formType = this.formType
          formUserEntity.userId = node.id
          formUserEntity.userCode = node.userCode
          this.formPermissions.push(formUserEntity)

          this.formPermissions = this.formPermissions.filter(
            (obj, index) =>
              this.formPermissions.findIndex(
                (item) => item.formId === obj.formId && item.userId === obj.userId && item.userCode === obj.userCode
              ) === index
          )
        }
      }
    },
    getFormUser(formId) {
      this.$callApiParams('getFormUser',
        { formId: formId }, result => {
          if (result.success) {
            const userCode = result.data
            if (this.$isNotEmpty(userCode)) {
              this.$refs.userSupTree.treeObj.checkAllNodes(false)
              for (var i = 0; i < userCode.length; i++) {
                const node = this.$refs.userSupTree.treeObj.getNodeByParam('userCode', userCode[i], null)
                this.$refs.userSupTree.treeObj.checkNode(node, true)
              }
            }
            this.selectUser(this.formId, this.formPermissions, userCode)
          }
          return true
        })
    },
    isTempateNode(treeNode) {
      return (treeNode &&
        treeNode.exData &&
        treeNode.exData.isTemplate)
    },
    selectClassifyList() {
      this.$callApiParams('selectClassifyList',
        { dataType: this.dataType }, result => {
          this.treeData = result.data
          this.treeData.forEach(node => {
            if (this.isTempateNode(node)) {
              // 数据模板节点显示为树的叶子节点
              delete node.children
            }
          })
          return true
        })
    },
    getAllUser() {
      this.$callApiParams('getAllUser', {},
        result => {
          this.userData = result.data
          return true
        })
    }
  }
}
</script>

<style scoped>

</style>
