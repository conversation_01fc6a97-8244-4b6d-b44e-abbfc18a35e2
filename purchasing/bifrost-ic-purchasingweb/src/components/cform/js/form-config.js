
/** ******** 规则表单要素联动设置***********/
export const formConfig = {
  '合同': {
    // '款项方向': {
    //   'itemSetting': [
    //     { 'itemValue': '付款', 'disabled': { '付款金额': false, '收款金额': true }},
    //     { 'itemValue': '收款', 'disabled': { '付款金额': true, '收款金额': false }},
    //     { 'itemValue': '收支双向', 'disabled': { '付款金额': false, '收款金额': false }},
    //     { 'itemValue': '', 'disabled': { '付款金额': true, '收款金额': true }}
    //   ],
    //   'triggerAssemblyEvent': false,
    //   'triggerExtendEvent': true
    // },
    '期限类型': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '金额类型': {
      'itemSetting': [
        { 'itemValue': '无金额', 'disabled': { '合同总价': false }},
        { 'itemValue': '不确定金额（限总价）', 'disabled': { '合同总价': false }},
        { 'itemValue': '确定金额', 'disabled': { '合同总价': false }}
      ],
      'triggerAssemblyEvent': true,
      'triggerExtendEvent': true
    },
    '招标类型': {
      'triggerExtendEvent': true
    },
    '期限开始日期': {
      'triggerExtendEvent': true
    },
    '期限结束日期': {
      'triggerExtendEvent': true
    },
    '计划付款方式': {
      'triggerAssemblyEvent': true
    },
    '合同总价': {
      'triggerAssemblyEvent': true
    },
    '合同业务类别': {
      'triggerAssemblyEvent': true,
      'triggerExtendEvent': true
    }
  },
  '预算编报': {
    '是否三年规划': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '实施期限': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '项目结束时间': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    }
  },
  '部门指标': {
    '是否政府采购': {
      'triggerExtendEvent': true
    },
    '是否末级指标': {
      'triggerExtendEvent': true
    }
  },
  '财政指标': {
    '是否末级': {
      'triggerExtendEvent': true
    }
  },
  '预算项目': {
    '是否购买服务': {
      'triggerExtendEvent': true
    },
    '是否资产配置': {
      'triggerExtendEvent': true
    },
    '是否政府采购': {
      'triggerExtendEvent': true
    }
  },
  '采购申请单': {
    '用途说明': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '申购用途': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '领取方式': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '领取说明': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '预算单价': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '采购数量': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    },
    '预算总价': {
      'triggerAssemblyEvent': false,
      'triggerExtendEvent': true
    }
  }
}
