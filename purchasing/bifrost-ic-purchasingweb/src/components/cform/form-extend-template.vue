<template>
    <span style="display: none"></span>
</template>
<script>
export default {
  name: 'formExtend-表单模板',
  methods: {
    setModeCellLineStr(colItem, value, ct) {
      // 表单类别为表单模板的文本换行
      if (colItem.labelOrigin === '合同合约方') {
        if (this.$isNotEmpty(value) && value.indexOf(',')) {
          var amounts = value.split(',')
          ct.t = 'inlineStr'
          ct.s = [{ 'v': amounts.join('\r\n') }]
        }
      }
    },
    setlegalOpinion(colItem, value, ct) {
      if (colItem.labelOrigin === '法律意见书修改意见' || colItem.labelOrigin.search('法律意见书修改意见') !== -1) {
        if (this.$isEmpty(value)) return
        var law = value.replace(/\s/g, '\r\n')
        // var lawFormat = law.map(am => {
        //   return this.$formatMoney(am)
        // })
        ct.t = 'inlineStr'
        ct.s = [{ 'v': law }]
      }
    }
  }
}
</script>
