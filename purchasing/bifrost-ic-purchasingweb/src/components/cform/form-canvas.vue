<template>
  <div
    :id="formCanvasId"
    class="formCanvas"
    :style="{ height: formCanvasHeight }"
  >
    <div class="formCommon">
      <div
        class="formMain"
        id="formMain"
        ref="formMain"
        v-loading="printLoading"
        element-loading-text="正在解析打印数据"
        element-loading-spinner="el-icon-loading"
      >
        <component
          ref="formFormat"
          :is="formatType"
          :class="classFormatMode()"
          :isPreviewDetail="isPreviewDetail"
          :hasExtDataAssembly="hasExtAssembly"
          :hideSilder="hideSilder"
          :nodes="nodes"
          :filterNodes="filterNodes"
          :getNodeIdFromItemKey="getNodeIdFromItemKey"
          @sliderChange="sliderChangeHeader"
          @setEditingColItem="setEditingColItem"
          @clearRFormElementList="handleElement"
          @colSetcheckedSelectClick="setRFormTableHeaderLable"
        >
          <template #content>
            <component ref="extDataAssembly" :is="extDataAssembly" />
          </template>
        </component>
      </div>

      <div
        :class="
          isFreedomForm && isDetailMode
            ? 'formFreeSettingExtra'
            : 'formCommonSettingExtra'
        "
        v-if="(isPreviewDetail || isDesignMode) && !isFreedomForm"
      >
        <div class="formCommonSettingExtraSlider" v-if="isPreviewDetail">
          <span class="formCommonSettingExtraSliderLabel">详情弹框宽度</span>
          <el-slider
            v-model="meta.instance.detailDlgWidth"
            :min="800"
            :max="1900"
            @change="sliderChange"
          />
        </div>
        <div class="formCommonSettingExtraSlider" v-if="isPreviewDetail">
          <span class="formCommonSettingExtraSliderLabel">详情弹框高度</span>
          <el-slider
            v-model="meta.instance.detailDlgHeight"
            :min="500"
            :max="960"
            @change="sliderChange"
          />
        </div>
      </div>
      <div class="form_settings" v-show="isDesignMode && !hideColSetting">
        <div class="retract-block" style="cursor:pointer;"  @click="switchExpandCol">
          <i class="el-icon-arrow-left" v-if="!colSettingVisible" style="font-size:13px;cursor:pointer;"></i>
          <i class="el-icon-arrow-right" v-else style="font-size:13px;cursor:pointer;"></i>
        </div>
        <div class="formCommmonSettingContainer" v-show="colSettingVisible">
          <el-tabs v-model="activeTabName" type="card" :class="{ 'base-tab-hide': !isBlockForm }">
            <div class="colSettingmask" v-show="showMask" title="其他tab关联表单不可编辑"></div>
            <el-tab-pane label="表单组件" name="comp" :disabled="tabPaneDisabled">
              <div class="formCommonSetting">
                <col-setting
                  ref="colSetting"
                  v-show="!elementAreaStatus"
                  :mode="mode"
                  :isFree="false"
                  :blockParentCheckData="blockParentCheckData"
                  @saveMeta="saveMeta"
                  @colItemsChange="colItemsChange"
                  @editingColItemChanged="editingColItemChanged"
                />
                <element-area
                  ref="elementArea"
                  v-show="elementAreaStatus"
                  :isBlockForm="isBlockForm"
                  @compItemClick="compItemClick"/>
              </div>
            </el-tab-pane>
            <el-tab-pane label="组件管理" name="manage" :disabled="tabPaneDisabled">
              <div class="formCommonSetting">
                <element-config
                  ref="elementConfig"
                  :disabled="elementConfigDisabled"
                  :rformIndex="2"/>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <component ref="formExtend" :is="formExtendName" />
    </div>
  </div>
</template>

<script>
import FormRegular from './form-regular'
import FormFree from './form-free'
import * as jquery from 'jquery'
import { CFORM_BLOCK_TYPE } from '@/constant'
import { isEqual } from 'lodash'

let $ = jquery
if (window.jQuery) {
  $ = window.jQuery
} else {
  window.jQuery = $
}
export default {
  name: 'form-canvas',
  components: { FormFree, FormRegular },
  props: {
    isPreviewDetail: { type: Boolean, default: false },
    hideColSetting: { type: Boolean, default: false },
    hideSilder: { type: Boolean, default: false },
    isBlockPreview: { type: Boolean, default: false },
    nodes: { type: Array, default: () => [] },
    filterNodes: { type: Function, default: function() {} },
    getNodeIdFromItemKey: { type: Function, default: function() {} }
  },
  provide() {
    return {
      mode: this.mode,
      outFormCanvasIns: this,
      hideColSetting: this.hideColSetting,
      hideElementArea: this.hideElementArea,
      rFormTableHeaderLabel: () => this.rFormTableHeaderLabel,
      handleElement: () => this.handleElement(),
      rFormSetElementList: (allElement, selectedElement) => this.rFormSetElementList(allElement, selectedElement),
      setEditingColItem: (label) => this.setEditingColItem(label),
      clearCurrentSelectedRForm: () => this.clearCurrentSelectedRForm(),
      changeRFormTableHeaderLabel: (label) => this.changeRFormTableHeaderLabel(label),
      updateRform: (index, updateParams) => this.updateRform(index, updateParams),
      setSelectedComp: (bizid) => this.setSelectedComp(bizid),
      resetElementConfig: () => this.resetElementConfig(),
      handleCurrentRformBlock: (block, config, initParams) => this.handleCurrentRformBlock(block, config, initParams),
      changeElementConfigDisabled: (disabled) => this.changeElementConfigDisabled(disabled),
      clearRformConfig: (disabled) => this.clearRformConfig(disabled),
      blockParentCheckDataFun: (data) => this.blockParentCheckDataFun(data),
      getBlockActiveTabIndex: () => this.getBlockActiveTabIndex(),
      changeShowMask: (show) => this.changeShowMask(show)
    }
  },
  inject: {
    toggleDisabled: { default: undefined },
    switchColItemsSettingBar: { default: undefined },
    showAuditDetailCallback: { default: undefined },
    outFormCanvasIns: { default: undefined },
    getCurrentNode: { default: undefined }
  },
  data() {
    return {
      formCanvasHeight: '100%',
      formCanvasId: 'formCanvas' + new Date().getTime() + '',
      mode: '制单', // "设计", "制单", "详情"
      meta: this.newMeta(),
      formType: '表单模板',
      colSettingVisible: true, // 是否显示右侧要素设置工具栏
      dataVo: {}, // 这个对象是制单或详情时的服务端对象CformDataVo
      needProcess: true,
      isFreedomForm: false,
      isBlockForm: false,
      isOpeningCanvas: false, // 是否是设计时单击左树打开新节点
      extDataAssembly: '',
      hasExtAssembly: false,
      printLoading: false,
      cformSettings: {},
      formExtendName: '',
      isPrintDetails: null,
      assemblyData: [], // 右侧拓展初始化数据
      numberFormulaCols: [], // 表单中设置了公式的数值要素
      calculateFormulaValuesFunc: undefined,
      elementAreaStatus: false, // 是否显示要素区
      rFormTableHeaderLabel: '', // 当前选中的rForm表头
      activeTabName: 'comp',
      elementConfigDisabled: true,
      currentRformBlock: null, // 当前正在配置的区块
      blockParentCheckData: {}, // 区块父级名称检测数据
      showMask: false, // 是否显示disabled遮罩
      tabPaneDisabled: false
    }
  },
  computed: {
    elementAreaText() {
      return `切换${this.elementAreaStatus ? '要素' : '组件'}区`
    },
    formatType() {
      let type
      if (this.isFreedomForm) {
        type = 'form-free'
      } else {
        if (this.isBlockForm && this.isDesignMode) {
          type = 'block-view'
        } else {
          type = 'form-regular'
        }
      }
      return type
    },
    isDesignMode() {
      // 判断当前是否是设计模式
      return this.mode === '设计'
    },
    isFillFormMode() {
      // 判断当前是否是制单模式
      return this.mode === '制单'
    },
    isDetailMode() {
      // 判断当前是否是详情
      return this.mode === '详情' || this.isAuditMode
    },
    isAuditMode() {
      // 判断当前是否是审核
      return this.mode === '审核'
    }
  },
  created() {
    var this_ = this
    this_.$onEvent(this_, {
      setColItemXY(colItem) {
        // 获取单元格位置
        this_.$refs.formFormat.setColItemXY(colItem)
      },
      removeColItem(colItem) {
        this_.$refs.formFormat.removeColItem(colItem)
      },
      colSetcheckedSelectClick(selection, row) {
        this_.$refs.formFormat.colSetcheckedSelectClick(selection, row)
      }
    })
  },
  methods: {
    changeShowMask(show) {
      this.showMask = show
      this.tabPaneDisabled = show
    },
    blockParentCheckDataFun(data) {
      this.blockParentCheckData = data
    },
    switchExpandCol() {
      if (this.switchColItemsSettingBar) {
        this.switchColItemsSettingBar()
      }
    },
    classFormatMode() {
      if (this.isAuditMode) {
        return 'formCanvasDetail'
      } else if (this.isPrintDetails) {
        return 'printDetailsBlock'
      } else {
        return ''
      }
    },
    // 制单或查看表单详情时，这个方法从远程获取表单数据。
    // 其中versionId和dataId不能同时为空，mode的值为制单或详情
    // initFormExData:初始化表单时有外部传入的额外数据
    initByVersionDataId(
      versionId,
      dataId,
      mode,
      callback,
      isPrintDetails,
      isEditing,
      canvasExTabsFun,
      initFormExData,
      queryParams,
      assemblyData
    ) {
      this.assemblyData = assemblyData
      const isPrint = this.$isNotEmpty(isPrintDetails) ? isPrintDetails : false
      queryParams = queryParams || {}

      var params = {
        versionId: versionId,
        dataId: dataId,
        isEditing: isEditing,
        isPrint: isPrint,
        mode: mode
      }
      // params请求参数
      params = Object.assign(params, queryParams)
      const apiKey = queryParams.printType ? 'getCformTemplateToPrint' : 'selectCformVo'
      this.$callApiParams(apiKey, params, (result) => {
        // 当form-free接口返回数据后 顶部tabdisabled变为false
        if (this.showAuditDetailCallback) {
          this.showAuditDetailCallback(false)
        }
        if (isPrintDetails && result.data.containers) {
          const blocks = result.data.containers[0].blocks
          result.data = result.data.containers[0].blocks[0].data
          result.data.blocks = blocks
        } else if (queryParams.printType) {
          result.data = this.setBaBlocks(result.data, params)
        }

        if (result.attributes?.auditScreenMetaId) {
          initFormExData.jumpToSaveFormData = {
            attributes: result.attributes,
            data: result.data
          }
        }

        return this.initByDataVo(
          result.data,
          mode,
          callback,
          isPrintDetails,
          canvasExTabsFun,
          initFormExData)
      }, () => {
        params.failCallback?.()
        this.$closeDialog()
        if (queryParams?.buttonType === '电子签章') {
          return true
        }
      })
    },
    setBaBlocks(result, params) {
      const data = result.cformData
      // data.data.id = params.dataId
      data.printType = params.printType
      const pageData = {
        columns: [],
        rows: result.detailRowData
      }
      const keys = Object.keys(result.detailColumnRelation)
      keys.forEach(label => {
        const column = {
          label,
          prop: result.detailColumnRelation[label]
        }
        pageData.columns.push(column)
      })
      data.blocks = [{ pageData, cformModuleName: '', handlerKey: CFORM_BLOCK_TYPE.HANDLERKEY }]
      return data
    },
    initByDataVo(
      dataVo,
      mode,
      callback,
      isPrintDetails,
      canvasExTabsFun,
      initFormExData
    ) {
      // dataVo 后端返回的数据
      this.isPrintDetails = isPrintDetails

      // 处理制单时公式计算。由于要素改变后触发事件机制与网络请求结合的方式不可靠
      // 所以目前使用周期执行方法的方式进行轮询更新公式要素的方式
      this.numberFormulaCols = []
      const colItemList = dataVo.containers ? dataVo.containers[0].dataVo.colItems : dataVo.colItems // 兼容区块表单与自由表单规则表单的数据格式
      colItemList.forEach(col => {
        if (col.isNumberFormula) {
          this.numberFormulaCols.push(col)
        }
      })

      this.stopCalculateFormulaValues()
      if (this.$isNotEmpty(this.numberFormulaCols)) {
        // setTimeout(this.startCalculateFormulaValues, 2000)
        this.$requestAnimationFrame(() => {
          this.startCalculateFormulaValues()
        }, 1000)
      }

      let exTabs = []
      if (typeof canvasExTabsFun === 'function') {
        exTabs = canvasExTabsFun(dataVo)
      }
      if (initFormExData && initFormExData.callbackBeforeFormLoaded) {
        const data = dataVo.containers ? dataVo.containers[0].dataVo : dataVo // 兼容区块表单与自由表单规则表单的数据格式
        initFormExData.callbackBeforeFormLoaded(data)
      }

      this.$setProperty(this, 'formFormat', 'formCanvasExTabs', exTabs)
      this.dataVo = dataVo.containers ? dataVo.containers[0].dataVo : dataVo // 兼容区块表单与自由表单规则表单的数据格式

      if (this.$isNotEmpty(this.getCurrentNode?.())) {
        this.dataVo.extData.currentNode = this.getCurrentNode()
      }

      var meta = {
        main: this.dataVo.meta,
        instance: this.dataVo.version,
        extData: this.dataVo.extData,
        colItems: this.dataVo.colItems,
        cformSettings: this.dataVo.cformSettings,
        attTypeSettings: this.dataVo.attTypeSettings,
        attList: this.dataVo.attList,
        data: this.dataVo.data,
        blocks: this.dataVo.blocks || [],
        printType: this.dataVo.printType
      }
      const id = dataVo.data ? dataVo.data.id : (dataVo.containers ? dataVo.containers[0]?.dataVo?.data?.id : dataVo?.main?.id) // 兼容区块表单与自由表单规则表单的数据格式
      this.initMeta(
        mode, meta, this.$isEmpty(id), isPrintDetails, initFormExData)
      if (callback) {
        this.$nextTick(() => {
          callback(meta, this.dataVo)
        })
      }
      return true
    },
    initMeta(mode, meta, isInsertForm, isPrintDetails, initFormExData) {
      // 初始化表单数据
      if (!meta) {
        this.$message.error('表单对象为空')
      } else {
        // 重置区块数据 start
        this.changeElementConfigDisabled?.(true)
        // 清空组件管理配置项
        this.clearRformConfig()
        if (this.outFormCanvasIns?.$refs.formFormat?.tabs?.length <= 1 ||
            this.outFormCanvasIns?.$refs.formFormat?.activeTabIndex === 0) {
          // 切换tab时不用重置右侧的表单组件管理
          this.hideElementArea()
        }
        // 清空选中当前操作的是哪个表格 及 清空表头选中
        this.changeRFormTableHeaderLabel()
        this.changeCurrentBlockId?.()
        this.clearCurrentSelectedRForm()
        this.handleElement()
        // 重置区块数据 end
        this.mode = mode
        this.meta = meta
        this.resetElementConfig()
        if (this.$isEmpty(meta.main)) {
          this.formType = meta.formType
          // 是否为自由表单
          this.isFreedomForm = meta.isFreedomForm
          if (!this.outFormCanvasIns) {
            // 是否为区块表单
            this.isBlockForm = meta.isBlockForm
          }
        } else {
          this.formType = meta.main.formType
          // 是否为自由表单
          this.isFreedomForm = meta.main.isFreedomForm
          if (!this.outFormCanvasIns) {
            // 是否为区块表单
            this.isBlockForm = meta.main.isBlockForm
          }
        }

        // isFreedomForm的改变会导致$refs的对象重新加载，需要等加载完成
        this.$nextTick(() => {
          if (this.$refs.colSetting) {
            this.$refs.colSetting.isFree = this.isFreedomForm
          }
          // 判断是否为制单模式
          const editButtonVisible = this.isFillFormMode
          if (this.$isNotEmpty(this.$refs.formFormat.$refs.formEditDetail)) {
            this.$setProperty(
              this,
              'formEditDetail',
              'showButtons',
              editButtonVisible
            )
          }
          this.initMetaFormFormat(
            mode,
            meta,
            isInsertForm,
            isPrintDetails,
            initFormExData
          )
          this.$emit('metaInit', meta)
          this.$event(this, 'initMetaById', meta, mode)
        })
      }
      if (window.luckysheet) {
        this.$nextTick(() => {
          window.luckysheet.exitEditMode()
        })
      }
      this.$refs.colSetting.$refs.tableColsAll.clearSelection()
      this.$refs.colSetting.$refs.tableColItems.clearSelection()
    },
    // 判断当前表单是否对某个选项已勾选
    // 可通过“更多设置”中的“表单设置”，勾选相关选项
    hasFormOption(optionName) {
      if (this.$isNotEmpty(this.cformSettings)) {
        for (let i = 0; i < this.cformSettings.length > 0; i++) {
          if (this.cformSettings[i].optionName === optionName) {
            return true
          }
        }
      }
      return false
    },
    // isInsertForm指定的是是否新增表单实例模式
    initMetaFormFormat(
      mode,
      meta,
      isInsertForm,
      isPrintDetails,
      initFormExData
    ) {
      this.$nextTick(() => {
        this.$refs.formFormat.mode = mode
        this.$refs.formFormat.meta = meta
        this.$refs.formFormat.title = meta.main.name
        this.$refs.formFormat.headerPaddingTop = meta.instance.headerPaddingTop
        this.$refs.formFormat.headerHeight = meta.instance.headerHeight
        this.$refs.formFormat.cformSettings = meta.cformSettings
        this.$refs.formFormat.dataVo = this.dataVo
        this.cformSettings = meta.cformSettings

        // 需要将selectCformVo填写在extData的数据传递到formFormat
        // 提供后续处理使用，比如handleIncLastUseOption
        this.$refs.formFormat.exData = {}
        if (this.dataVo && this.dataVo.extData) {
          this.$refs.formFormat.exData = Object.assign({}, this.dataVo.extData)
          // 后端传回规则表单是否使用缩小样式
          if (this.dataVo.extData.useFormRegularCompact) {
            initFormExData = initFormExData || {}
            initFormExData.useFormRegularCompact =
              this.dataVo.extData.useFormRegularCompact
          }
        }

        // 初始化表单动态功能扩展，这机制是：
        // 由具体业务模块根据表单类型扩展VUE组件，表单画布对这个组件进行加载，
        // 这个加载的组件被传递到表单组件，最终由表单组件来使用这个扩展VUE组件内的方法。
        // 场景：form-extend-inc-bx.vue扩展报销单的功能，比如申请金额大写联动
        const name = `formExtend-${this.formType}`
        if (this.$isNotEmpty(window.$viewNames[name])) {
          // 存在扩展组件才加载,如果是非区块表单
          if (!this.isBlockForm) {
            this.formExtendName = name
          }
          let formExtend
          this.$nextTick(() => {
            if (typeof this.$refs.formFormat.initFormExtend === 'function') {
              // 获取formExtend
              formExtend = this.$refs.formExtend
              if (formExtend && formExtend.getFinalExtendObj) {
                const finalExtendObj = formExtend.getFinalExtendObj()
                if (finalExtendObj) {
                  formExtend = finalExtendObj
                }
              }
              this.$refs.formFormat.initFormExtend(formExtend)
            }
            this.$refs.formFormat.exData['formExtend'] = formExtend
          })
        }

        // 到此可确定是自由表单还是规则表单
        if (this.isFreedomForm) {
          $('.formCanvasRuntimeHasAssembly').addClass(
            'formCanvasRuntimeHasAssemblyFree'
          )
          $('.formCanvasRuntimeHasAssemblyFree').addClass(
            'formCanvasRuntimeHasAssembly'
          )
        }

        var $formCanvasRuntime = $('#' + this.formCanvasId).parent()
        $formCanvasRuntime.removeClass('formCanvasRuntimeRegularEdit')
        $formCanvasRuntime.removeClass('formCanvasRuntimeRegularDesign')
        // 判断是否为设计模式
        if (this.isDesignMode) {
          var isNewMeta = this.$isEmpty(meta.main.id)
          this.$nextTick(() => {
            // 退出预览时，可能不会引发colItemsChange事件，此处手动调用
            if (meta.isResumePreview) {
              this.colItemsChange(meta.colItems)
              meta.isResumePreview = undefined
            }

            // 加载一个新表单时，重新初始化要素数据
            this.isOpeningCanvas = true
            this.$refs.colSetting.init(isNewMeta)
            this.$refs.colSetting.setColItems(meta)

            if (!this.isFreedomForm) {
              $formCanvasRuntime.addClass('formCanvasRuntimeRegularDesign')
            }
            if (this.isBlockForm) {
              // 处理行表单和组件区选中联动 不是新的表单才联动 且block长度大于1  证明有行表单
              this.$refs.elementArea.handleSelectedComp('clearAll')
              if (!isNewMeta) {
                if (this.meta?.blockView?.containers?.[0].blocks?.length > 1) {
                  this.meta.blockView.containers[0].blocks.forEach(block => {
                    if (block.handlerKey !== '表单') {
                      this.setSelectedComp(block.cformModuleId)
                    }
                  })
                }
              }
              const params = {
                isEdit: true,
                mode: '设计',
                meta: this.meta,
                isPreviewDetail: initFormExData?.isPreviewDetail,
                isDesignMode: this.isDesignMode,
                isBlockPreview: this.isBlockPreview,
                initFormExData
              }
              if (this.$isNotEmpty(meta.blockView)) {
                params.blockView = meta.blockView
              }
              this.$refs.formFormat.init(undefined, params, isNewMeta)
            }
          })
          // 是否为详情模式
        } else if (this.isDetailMode) {
          this.$refs.formFormat.createForm(
            meta.colItems,
            isInsertForm,
            isPrintDetails,
            initFormExData
          )
        } else if (this.isFillFormMode) {
          this.$refs.formFormat.createForm(
            meta.colItems,
            isInsertForm,
            undefined,
            initFormExData
          )
          if (!this.isFreedomForm) {
            $formCanvasRuntime.addClass('formCanvasRuntimeRegularEdit')
          }

          this.$nextTick(() => {
            if (this.$refs.formFormat.dataVo &&
              this.$refs.formFormat.dataVo.extData &&
              this.$refs.formFormat.dataVo.extData[`制单样式`] !== undefined &&
              this.$refs.formFormat.$refs.rightCollapsedContainer) {
              if (this.$refs.formFormat.dataVo.extData[`制单样式`] === '分tab') {
                this.$refs.formFormat.isRightCollapsed = false
              }
            }
            if (initFormExData.showEditButtons !== undefined) {
              if (this.$isNotEmpty(this.$refs.formFormat.$refs.formEditDetail)) {
                this.$refs.formFormat.$refs.formEditDetail.showButtons =
                  initFormExData.showEditButtons
              }
            } else {
              if (!this.isDesignMode && this.$isNotEmpty(this.$refs.formFormat.$refs.formEditDetail)) {
                this.$refs.formFormat.$refs.formEditDetail.showButtons = true
              }
            }
          })
        }
        this.$emit('changeBtnStatus', false)
      })
    },
    stopCalculateFormulaValues() {
      if (this.calculateFormulaValuesFunc !== undefined) {
        clearInterval(this.calculateFormulaValuesFunc)
        this.calculateFormulaValuesFunc = undefined
      }
    },
    startCalculateFormulaValues() {
      if (this.isFillFormMode &&
        this.calculateFormulaValuesFunc === undefined &&
        this.$isNotEmpty(this.numberFormulaCols)) {
        // this.calculateFormulaValuesFunc =
        //   setInterval(this.calculateFormulaValues, 1500)
        this.calculateFormulaValuesFunc = this.calculateFormulaValues()
      }
    },
    calculateFormulaValues() { // 制单时从后端获取数值公式的计算值
      if (this.isFillFormMode &&
          this.$isNotEmpty(this.numberFormulaCols)) {
        var dataVo = this.getDataToSave()

        this.$callApi('calculateFormulaColValues',
          dataVo, result => {
            var formulaColValues = result.attributes.formulaColValues
            if (this.$isNotEmpty(formulaColValues) &&
              this.$refs.formFormat.setValue) {
              var labels = Object.keys(formulaColValues)
              labels.forEach(lb => {
                var valueFloat = parseFloat(formulaColValues[lb])
                if (valueFloat > 0) {
                  this.$refs.formFormat.setValue(lb, valueFloat)
                } else {
                  // 公式计算值是0，清空要素可能已存在的数据
                  this.$refs.formFormat.setValue(lb, '')
                }
              })

              if (this.$refs.formFormat.formExtend.afterCalculateFormulaValues) {
                this.$refs.formFormat.formExtend.afterCalculateFormulaValues(
                  formulaColValues, this.$refs.formFormat)
              }
            }

            // this.stopCalculateFormulaValues()
            return true
          })
      }
    },
    colItemsChange(colItems) {
      this.$refs.formFormat.colItemsChange(colItems, this.isOpeningCanvas)
      this.isOpeningCanvas = false
    },
    editingColItemChanged(isEditing, colItem, isRform = false) {
      // this.hideElementArea()
      // 判断是否是区块行表单 是区块行表单不执行勾选Cform的label
      if (!isRform) {
        this.$refs.formFormat.editingColItemChanged(isEditing, colItem)
      }
    },
    setEditingColItem(label) {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.setEditingColItem(label)
        return
      }
      this.$refs.colSetting.setEditingColItem(label)
    },
    isAuditCanEdit(itemLabel) { // 是否是可修改的要素
      var auditCanEditLabels = []
      if (this.isAuditMode &&
        this.dataVo &&
        this.dataVo.extData &&
        this.dataVo.extData['审核编辑数据'] &&
        this.$isNotEmpty(this.dataVo.extData['审核编辑数据']['可修改的要素'])) {
        auditCanEditLabels = this.dataVo.extData['审核编辑数据']['可修改的要素']
      }
      return (auditCanEditLabels.indexOf(itemLabel) > -1)
    },
    sliderChangeHeader(headerPaddingTop, headerHeight) {
      this.meta.instance.headerPaddingTop = headerPaddingTop
      this.meta.instance.headerHeight = headerHeight
      this.sliderChange()
    },
    sliderChange() {
      // 滑块拖动松开鼠标时，表单已经保存过时，执行自动保存
      if (this.isDesignMode || this.isFillFormMode) {
        this.$refs.colSetting.saveMeta(false, (result) => {
          return true
        })
      } else if (this.isDetailMode) {
        this.$emit('sliderChange', this.meta)
      }
    },
    fillFormData(colItems, isDataToSave, updateAfter) {
      // 填充表单填写的数据
      this.$refs.formFormat.fillFormData(colItems, isDataToSave, updateAfter)
    },
    getFormExtend() { // 获取表单扩展组件
      if (this.$refs.formFormat.exData) {
        return this.$refs.formFormat.exData['formExtend']
      }
      return undefined
    },
    getDataToSave(updateAfter) {
      // 制单保存时获取表单数据
      // 自由表单时，isFillNewForm标识在this.$refs.formFormat
      // 中有误需要在保存之前纠正
      var isFillNewForm = this.$isEmpty(this.dataVo.data.id)
      this.$refs.formFormat.isFillNewForm = isFillNewForm

      this.fillFormData(this.dataVo.colItems, true, updateAfter)
      var extData = {}
      if (this.$refs.extDataAssembly) {
        extData = this.$refs.extDataAssembly.getExtData()
        this.$refs.extDataAssembly.fillAtt(this.dataVo) // 填充附件信息
      }

      if (extData) {
        this.dataVo.extData = Object.assign(this.dataVo.extData, extData)
      }
      if (this.$refs.formFormat.formExtraItemData) {
        // 表单额外要素
        if (this.$refs.formFormat.formExtraItemData.length > 0) {
          this.dataVo.extData.formExtraItemData =
            this.$refs.formFormat.formExtraItemData
        }
      }
      if (this.$refs.formFormat.fillDataBeforeSave) {
        // 保存前额外填充数据
        this.$refs.formFormat.fillDataBeforeSave(this.dataVo)
      }

      // 统一处理表单公示
      if (this.$refs.formFormat.exData &&
        this.$isNotEmpty(this.$refs.formFormat.exData['公示临时ID'])) {
        this.dataVo.extData['公示临时ID'] =
          this.$refs.formFormat.exData['公示临时ID']
      }

      // 保存之前扩展处理dataVo
      if (
        this.$refs.formFormat.formExtend &&
        this.$refs.formFormat.formExtend.warpDataVoBeforeSave
      ) {
        this.$refs.formFormat.formExtend.warpDataVoBeforeSave(
          this.$refs.formFormat,
          this.dataVo
        )
      }
      return this.dataVo
    },
    saveMeta(callback, callbackFailed, exArg = {}) {
      // 设计时保存表单元数据
      this.$emit('saveMeta', callback, callbackFailed, undefined, exArg)
    },
    switchFormFormat(type) {
      // 新增时，可以切换表单格式
      this.$refs.colSetting.selectColsData()
      this.hideElementArea()
      this.handleElement()
      this.$refs.elementArea.handleSelectedComp('clearAll')
      if (type === 'block') {
        this.isBlockForm = true
        this.isFreedomForm = false
        this.$refs.elementArea.getRFormComp()
      } else {
        this.isBlockForm = false
        this.isFreedomForm = type
      }
    },
    changeColItemsJSON(cols) {
      if (this.$refs.colSetting) {
        this.$refs.colSetting.changeColItemsJSON(cols)
      }
    },
    getMetaToSave() {
      this.meta.instance.formatJson = this.$refs.formFormat.getFormatJson()
      this.meta.colItems = this.$refs.colSetting.colItemsJSON
      if (this.$isNotEmpty(this.$refs.colSetting.deletingColItemNames)) {
        this.meta.exData = this.meta.exData || {}
        this.meta.exData['deletingColItemNames'] = this.$clone(
          this.$refs.colSetting.deletingColItemNames
        )
        this.$refs.colSetting.deletingColItemNames = [] // 使用完需要马上清空
      }
      if (this.isBlockForm) {
        this.meta.blockData = this.$refs.formFormat.getAllData()
      }
      return this.meta
    },
    newMeta() {
      const name = this.isBlockForm ? '区块表单' : this.mode === '设计' ? '新建表单' : ''
      // 设计时新建表单数据对象
      const result = {
        main: {
          id: '',
          name,
          formType: '',
          isFreedomForm: this.isFreedomForm,
          isBlockForm: this.isBlockForm
        },
        colItems: [],
        instance: {
          formatJson: '',
          headerHeight: 80,
          headerPaddingTop: 20,
          detailDlgWidth: 936,
          detailDlgHeight: 800
        }
      }
      if (this.isBlockForm) {
        result.blockData = {}
      }
      return result
    },
    nameChange(name) {
      // 同步表单名称
      this.$call(this.$refs.formFormat, 'formCanvas', 'nameChange', name)
      if (!this.isFreedomForm) {
        this.$refs.formFormat.title = name
      }
    },
    showError(result, errorNum, errorHitContainer) {
      errorNum = errorNum || 0
      if (
        this.$isNotEmpty(this.$refs.extDataAssembly) &&
        this.$isNotEmpty(this.$refs.extDataAssembly.showError) &&
        typeof this.$refs.extDataAssembly.showError === 'function'
      ) {
        errorNum = this.$refs.extDataAssembly.showError(result)
      }
      var hitCount = this.$refs.formFormat.showError(result, errorNum)

      var hitCountInt = parseInt(hitCount)
      const attributes = result.attributes
      if (hitCountInt > 0) {
        var msg = `检查发现${hitCount}个错误<br/>`
        Object.entries(attributes).forEach((attribute) => {
          const errorKey = attribute[0]
          if (errorKey.indexOf('ID') === -1 &&
            errorKey.indexOf('id') === -1 &&
            errorKey.indexOf('Id') === -1 &&
            typeof attribute[1] === 'string') {
            // 当不包含ID时，则拼接
            msg += `${attribute[0]}` + '：' + `${attribute[1]}<br/>`
          }
        })
        this.$message.error(msg)
      }

      if (errorHitContainer) {
        errorHitContainer['hitCountInt'] = hitCountInt
      }
      return hitCountInt > 0
    },
    initExtDataAssembly(meta, mode, initBeforeColItems) {
      // initBeforeColItems:初始化表单前的要素集合
      if (
        this.$refs.formFormat.isRightCollapsed &&
        this.$refs.extDataAssembly &&
        this.$refs.extDataAssembly.initAssembly
      ) {
        this.$refs.extDataAssembly.initAssembly(
          meta,
          mode,
          this.$refs.formFormat,
          this.assemblyData,
          initBeforeColItems
        )
      }
    },
    syncBaToPayees(bas, amountSum) {
      if (this.$refs.formFormat.isRightCollapsed) {
        if (this.$refs.extDataAssembly) {
          this.$refs.extDataAssembly.syncBaToPayees(bas, amountSum)
        }
      } else {
        var formEditTabsComponents =
          this.$parent.$parent.$parent.formEditTabsComponents
        for (var tabName in formEditTabsComponents) {
          var comp = formEditTabsComponents[tabName]
          if (comp && comp.syncBaToPayees) {
            comp.syncBaToPayees(bas, amountSum)
          }
        }
      }
    },
    setExtDataAssembly(formType) {
      // 遍历判断是否存在组件
      const requireComponent = require.context('@/components', true, /\.vue$/)
      // eslint-disable-next-line no-unused-vars
      var hasAssembly = false
      var assemblyName = null
      requireComponent.keys().forEach((fileName) => {
        // 获取组件配置
        const componentConfig = requireComponent(fileName)
        const componentName = componentConfig['default'].name
        // 如有两个单据共同使用一个组件,例如[报销单+借款单]去获取组件配置
        if (componentName.indexOf(formType) > -1) {
          hasAssembly = true
          assemblyName = componentName
        }
      })
      this.$refs.formFormat.hasExtAssembly = hasAssembly
      this.hasExtAssembly = hasAssembly
      this.extDataAssembly = assemblyName != null ? assemblyName : formType
      var assembleClass = 'formCanvasRuntimeHasAssembly'
      if (hasAssembly) { // 制单UI有右边内容时，需要定制不同的UI
        $('.formCanvas').parent().addClass(assembleClass)
        // $('.formCanvas').closest('.common-page').css('cssText', 'padding:0px !important;')
      } else {
        $('.formCanvas').parent().removeClass(assembleClass)
      }
    },
    showBtDraft(isShowBtDraft) {
      this.$refs.formFormat.showBtDraft(isShowBtDraft)
    },
    setBtDraftLoading(isLoading) {
      this.$refs.formFormat.setBtDraftLoading(isLoading)
    },
    toggleElementArea() {
      if (this.activeTabName !== 'comp') {
        this.activeTabName = 'comp'
      }
      this.elementAreaStatus = !this.elementAreaStatus
      this.$emit('changeToggleText', this.elementAreaText)
    },
    compItemClick(data) {
      this.$refs.formFormat?.handleCompItemClick(data)
    },
    updateRform(index, updateParams) {
      this.$call(this, 'tabComponents', 'updateRform', index, updateParams)
    },
    hideElementArea() {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.hideElementArea()
        return
      } else if (this.elementAreaStatus) {
        this.elementAreaStatus = false
        this.$emit('changeToggleText', this.elementAreaText)
      }
    },
    rFormSetElementList(allElement, selectedElement) {
      this.$refs.colSetting.rFormSetElementList(allElement, selectedElement)
    },
    handleElement() {
      // 不点击表头需要清空要素
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.handleElement()
        return
      } else {
        this.$refs.colSetting.clearRFormElementList()
        this.$refs.colSetting.$refs.tableColItems.clearSelection()
      }
    },
    setRFormTableHeaderLable(selection, row) {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.setRFormTableHeaderLable(selection, row)
        return
      } else {
        if (selection.length === 1) {
          this.changeRFormTableHeaderLabel(selection[0].label)
        }
      }
    },
    clearCurrentSelectedRForm() {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.clearCurrentSelectedRForm()
        return
      } else {
        this.currentRformBlock = null
        this.$call(this, 'tabComponents', 'clearCurrentRFormTable')
      }
    },
    changeRFormTableHeaderLabel(label) {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.changeRFormTableHeaderLabel(label)
        return
      } else {
        // 如果label有值则是选中表头 不传label则是不选中表头
        this.rFormTableHeaderLabel = label || ''
      }
    },
    getBlockActiveTabIndex() {
      if (this.outFormCanvasIns) {
        return this.outFormCanvasIns.getBlockActiveTabIndex()
      } else {
        if (this.isBlockForm) {
          return this.$refs.formFormat.activeTabIndex || 0
        }
      }
    },
    setSelectedComp(bizid) {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.setSelectedComp(bizid)
        return
      } else {
        // 如果组件删除 但是已存在的区块表单中有该组件 则不联动
        const comps = this.$refs.elementArea.comps[0].data
        if (this.$isNotEmpty(comps)) {
          const bizidArr = comps.map(comp => comp.bizid)
          if (bizidArr.indexOf(bizid) === -1) {
            return
          }
        }
        const activeTabIndex = this.getBlockActiveTabIndex()
        const selectedComp = this.$refs.elementArea.selectedComp[activeTabIndex]
        if (Array.isArray(selectedComp)) {
          if (selectedComp.indexOf(bizid) === -1) {
            this.$refs.elementArea.handleSelectedComp('push', bizid, activeTabIndex)
          } else {
            if (this.$isNotEmpty(selectedComp)) {
              this.$refs.elementArea.handleSelectedComp('splice', selectedComp.indexOf(bizid), activeTabIndex)
            }
          }
        } else {
          this.$refs.elementArea.handleSelectedComp('create', bizid, activeTabIndex)
        }
      }
    },
    /**
     * 处理当前区块的配置
     * @param {Object} block 当前区块数据
     * @param {Object} config 当前区块配置
     * @param {Object} extraParams 额外参数 { buttons: 当前baseList按钮, currRformParams: 当前区块rform的params }
     */
    handleCurrentRformBlock(block = null, config = null, extraParams = null) {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.handleCurrentRformBlock(block, config, extraParams)
        return
      } else {
        let isSameConfig = false
        // 当传入的block不为空 且当前区块为空时赋值
        if (this.$isNotEmpty(block)) {
          this.currentRformBlock = block
          // 设置组件管理默认的name
          this.$refs.elementConfig.setDefaultName(block?.name)
        }
        if (this.$isNotEmpty(config)) {
          // 判断两个config是否一致
          isSameConfig = isEqual(this.currentRformBlock.config, config)
          Object.assign(this.currentRformBlock.config, config)
          this.$refs.elementConfig.setForm(this.currentRformBlock.config)
        } else {
          this.$refs.elementConfig.resetForm()
        }
        if (this.$isNotEmpty(extraParams)) {
          this.$refs.elementConfig.getBlockInitParams(extraParams?.currRformParams)
          if (extraParams?.buttons) {
            this.$refs.elementConfig.setButtonSelection(extraParams.buttons)
          }
        }
        // 设置当前区块配置
        this.$call(this, 'tabComponents', 'setRformConfig', this.currentRformBlock, isSameConfig)
      }
    },
    resetElementConfig() {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.resetElementConfig()
        return
      } else {
        if (this.activeTabName !== 'comp') {
          this.activeTabName = 'comp'
        }
      }
    },
    /**
     * 修改配置disabled
     * @param {Boolean} disabled 组件管理是否可以配置
     */
    changeElementConfigDisabled(disabled) {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.changeElementConfigDisabled(disabled)
        return
      } else {
        // 如果点击了配置按钮 则展示配置页面 否则展示表单组件
        if (disabled) {
          this.activeTabName = 'comp'
          // 清空默认name
          this.$refs.elementConfig.setDefaultName()
          this.$refs.elementConfig.resetForm()
        } else {
          this.activeTabName = 'manage'
        }
        this.elementConfigDisabled = disabled
      }
    },
    clearRformConfig() {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.clearRformConfig()
        return
      } else {
        // 调用block-container的clearRformConfig方法
        this.$call(this, 'tabComponents', 'clearRformConfig')
      }
    },
    addColItemModifiedCallbacks(callbacks) {
      this.$refs.formFormat.addColItemModifiedCallbacks(callbacks)
    },
    resetBlockTabsConfig() {
      if (this.outFormCanvasIns) {
        this.outFormCanvasIns.resetBlockTabsConfig()
        return
      } else {
        this.$refs.formFormat.resetBlockTabsConfig()
      }
    }
  }
}
</script>
<style lang="scss">
.formCanvasDetail .formMain {
  border: none;
}
.formCanvasDetail .formCommonTitle {
  font-size: 26px;
}
.formCanvasDetail .formCommonHeader {
  padding-top: 0px !important;
  height: 100px !important;
}
.formCanvasDetail .el-main {
  padding-top: 0px;
}
.formCanvasDetail .el-footer {
  display: none;
}
// .formCanvasDetail .formCommonCols .col-isRequired .el-form-item__label:before {
//   display: none !important;
// }
.formCanvasDetail .formRegularContainer .el-input__inner,
.formCanvasDetail .formRegularContainer .el-textarea__inner,
.formCanvasDetail .formRegularContainer .el-input__prefix {
  border: none !important;
  cursor: default !important;
}
.formCanvasDetail .el-input__inner,
.el-radio__inner,
.el-checkbox__label {
  cursor: default !important;
}
/*.formCanvasDetail .customInput .el-input__prefix{display: block !important;}*/
/*.formCanvasDetail .customDom .customSelect .el-input__suffix-inner{display: block !important;}*/
.formCanvasDetail .el-input__prefix,
.formCanvasDetail .el-input__suffix-inner,
.formCanvasDetail .el-date-editor--daterange .el-input__icon {
  display: none !important;
}
.formCanvasDetail .el-date-editor--daterange.el-range-editor.el-input__inner {
  justify-content: flex-start;
  .el-range-input {
    flex: unset;
    background: transparent;
  }
}
.formCanvasDetail .el-radio,
.formCanvasDetail .el-radio__input,
.formCanvasDetail .el-radio__original,
.formCanvasDetail .el-radio__inner,
.formCanvasDetail .el-input-number__increase,
.formCanvasDetail .el-checkbox__input,
.formCanvasDetail .el-checkbox__inner,
.formCanvasDetail .el-checkbox {
  cursor: default !important;
}
.formCanvasDetail .formCommonCols .el-input--prefix .el-input__inner {
  padding-left: 5px;
}
.formCanvasDetail .formCommonCols .el-radio__inner:hover,
.formCanvasDetail .formCommonCols .el-checkbox__inner {
  border: 1px solid #aaa;
}
.formCanvasDetail .formCommonCols .el-checkbox-group label:not(.is-checked),
.formCanvasDetail .formCommonCols .el-radio-group label:not(.is-checked) {
  display: none !important;
}
.printDetailsBlock .formCommonCols .el-checkbox-group label:not(.is-checked),
.printDetailsBlock .formCommonCols .el-radio-group label:not(.is-checked) {
  display: flex !important;
}
.printDetailsBlock .formCommonCols .el-radio-group label {
  margin-right: 10px !important;
}
  .formCanvasDetail .formMain {border: none;}
  .formCanvasDetail .formCommonTitle {font-size: 26px;}
  .formCanvasDetail .formCommonHeader {
    padding-top: 0px !important;
    height: 100px !important;}
  .formCanvasDetail .el-main {padding-top: 0px;}
  .formCanvasDetail .el-footer {display: none;}
  // .formCanvasDetail .formCommonCols .col-isRequired .el-form-item__label:before {
  //   display: none !important;}
  .formCanvasDetail .formRegularContainer .el-input__inner,
  .formCanvasDetail .formRegularContainer .el-textarea__inner,
  .formCanvasDetail .formRegularContainer .el-input__prefix{
    border: none !important;cursor: default !important;}
  .formCanvasDetail .el-input__inner,.el-radio__inner,.el-checkbox__label{cursor: default !important;}
  /*.formCanvasDetail .customInput .el-input__prefix{display: block !important;}*/
  /*.formCanvasDetail .customDom .customSelect .el-input__suffix-inner{display: block !important;}*/
  .formCanvasDetail .el-input__prefix,
  .formCanvasDetail .el-input__suffix-inner{display: none !important;}
  .formCanvasDetail .el-input__prefix.auditCanEdit,
  .formCanvasDetail .el-input__suffix-inner.auditCanEdit,
  .formCanvasDetail .auditCanEdit .el-input__prefix,
  .formCanvasDetail .auditCanEdit .el-input__suffix-inner{display: unset !important;}

  .formCanvasDetail .el-radio,
  .formCanvasDetail .el-radio__input,
  .formCanvasDetail .el-radio__original,
  .formCanvasDetail .el-radio__inner,
  .formCanvasDetail .el-input-number__increase,
  .formCanvasDetail .el-checkbox__input,
  .formCanvasDetail .el-checkbox__inner,
  .formCanvasDetail .el-checkbox{
    cursor: default !important;}
  .formCanvasDetail .formCommonCols .el-input--prefix .el-input__inner {
    padding-left: 5px;}
  .formCanvasDetail .formCommonCols .el-input--prefix .el-input__inner.auditCanEdit,
  .formCanvasDetail .formCommonCols .auditCanEdit .el-input__inner{
    padding-left: 25px;}
  .formCanvasDetail .formCommonCols .el-radio__inner:hover,
  .formCanvasDetail .formCommonCols .el-checkbox__inner {border: 1px solid #aaa;}
  .formCanvasDetail .formCommonCols .el-checkbox-group label:not(.is-checked),
  .formCanvasDetail .formCommonCols .el-radio-group label:not(.is-checked) {display: none !important;}
  .formCanvasDetail .formCommonCols .el-checkbox-group.auditCanEdit label:not(.is-checked),
  .formCanvasDetail .formCommonCols .el-radio-group.auditCanEdit label:not(.is-checked) {display: unset !important;}

  .formCanvasDetail .formCommonCols.formFreeView .el-checkbox-group.auditCanEdit label:not(.is-checked),
  .formCanvasDetail .formCommonCols.formFreeView .el-radio-group.auditCanEdit label:not(.is-checked) {display: flex !important;}

  .printDetailsBlock .formCommonCols .el-checkbox-group label:not(.is-checked),
  .printDetailsBlock .formCommonCols .el-radio-group label:not(.is-checked) {display: flex !important;}
  .printDetailsBlock .formCommonCols .el-radio-group label {margin-right: 10px !important;}
</style>
<style lang="scss">
.formCanvas {
  width: 100%;
  height: 100%;
  position: relative;
}
.formCommonSettingExtra {
  position: absolute;
  top: 15px;
  right: 30px;
}
.formFreeSettingExtra {
  position: absolute;
  top: 10px;
  right: 20px;
  background: #fff;
  z-index: 2999;
  padding: 10px 8px 0px 8px;
  border: 1px solid #666;
  border-radius: 5px;
}
.formCommonSettingExtraSlider {
  width: 200px;
  height: 30px;
}
.formCommonSettingExtraSlider .formCommonSettingExtraSliderLabel {
  font-size: 14px;
  color: #8492a6;
  line-height: 20px;
}
.formCommonSettingExtraSlider .formCommonSettingExtraSliderLabel + .el-slider {
  float: right;
  width: 50%;
  margin-top: 3px;
}
.formCommonSettingExtraSlider .el-slider__button {
  width: 14px;
  height: 14px;
}
.formCommonSettingExtraSlider .el-slider__runway {
  margin: 5px 0px;
}
.formCommon {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
}
.formMain {
  flex: 1;
  position: relative;
  overflow: hidden;
}
.form_settings {
  position: relative;
  display: flex;
  min-width: 10px;
  .retract-block {
    display: flex;
    align-items: center;
    position: absolute;
    width: 10px;
  }
}
.formCommmonSettingContainer {
  position: relative;
  flex: 1;
  height: 100%;
  margin-left: 10px;
  .colSettingmask {
    position: absolute;
    inset: 0;
    cursor: not-allowed;
    z-index: 9999;
    background-color: rgba($color: #000000, $alpha: 0.1)
  }
  .el-tabs__header {
    margin: 0;
  }
  .el-tabs--card > .el-tabs__header.is-top .el-tabs__item {
    background-color: #f0f5ff;
    &.is-active {
      border-color: #DDDDDD;
      background-color: #fff;
      border-bottom: 1px solid #fff;
      color: #333333;
    }
  }
}
.formCommonSetting {
  flex: 1;
  height: 100%;
  border: 1px solid #DDDDDD;
  width: 470px;
  padding: 10px 0 10px 10px;
}
.formCommonHeader {
  width: 100%;
  height: 105px;
  text-align: center;
  padding-top: 10px;
  position: relative;
}
.formCommonTitle {
  height: 100px;
  min-width: 50px;
  white-space: nowrap;
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  text-decoration: underline;
  letter-spacing: 3px;
  padding: 0px 5px;
  border-color: rgb(152, 191, 255);
}
.formCommonTitle span {
  color: rgb(24, 119, 225);
  display: inline-block;
  border-bottom: 1px solid rgb(24, 119, 225);
  padding-bottom: 2px;
}
.formCommonTitle span span {
  padding: 7px 25px;
  border-bottom: 1px solid rgb(24, 119, 225);
  display: inline-block;
  font-family: Helvetica Neue, Helvetica, PingFang SC, \5FAE\8F6F\96C5\9ED1,
    Tahoma, Arial, sans-serif;
  font-weight: bold;
}
.formCommonCols {
  padding: 0px;
}
.formCommonColsNoBorderBottom {
  border-bottom: none;
}
.formCommonCols .el-form-item__label {
  font-size: 14px;
  line-height: 36px !important;
}
.formCommonCols .el-input__inner {
  padding: 0px 5px;
  font-size: 14px;
  height: 36px;
  line-height: 36px;
  border: none;
}
.formCommonCols .el-radio__inner {
  height: 16px;
  width: 16px;
}
.formCommonCols .el-radio__label {
  font-size: 14px;
  padding-left: 5px;
}
.formCommonCols .el-checkbox {
  margin-right: 10px;
  line-height: 38px !important;
}
.formCommonCols .el-checkbox__label {
  padding-left: 2px;
  font-size: 14px;
}
.formCommonCols .el-checkbox__inner {
  height: 16px;
  width: 16px;
}
.formCommonCols .el-checkbox__inner::after {
  left: 5px;
  top: 2px;
}
.formCommonCols .el-form-item--mini.el-form-item,
.formCommonCols .el-form-item--small.el-form-item {
  margin: 0px 0px;
  margin: 0px -1px -1px 0px;
  border: 1px solid rgb(152, 191, 255);
}
.formCommonCols .form-create {
  background: rgb(219, 238, 255);
  border-right: 1px solid #98bfff;
}
.formCommonCols .el-form-item__label {
  padding: 0px 10px 0px 0px;
}
.formCommonCols .el-form-item__content {
  height: 38px;
  padding: 1px;
  border-left: 1px solid rgb(152, 191, 255);
  background: #fff;
  position: relative;
}
.formCommonCols .col-isRequired .el-form-item__label:before {
  content: "*";
  // font-size: 20px;
  color: rgba(255, 0, 0, 1);
  // line-height: 12px;
  // font-family: monospace;
}
.formCommonCols .el-form-item__label .el-icon-warning:before {
  color: rgba(255, 0, 0, .5);
}
.formCommonCols .el-select input,
.formCommonCols .colitem-button input {
  border: none !important;
}
.formCommonCols .colitem-textarea {
  display: flex;
  // height: 88px;
}
.formCommonCols .colitem-textarea .el-form-item__label {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  // height: 100%;
  // line-height: 88px !important;
}
.formCommonCols .colitem-textarea .el-form-item__content {
  height: 100%;
  flex: 1;
  margin-left: 0px!important;
  // line-height: 88px !important;
}
.formCommonCols .colitem-textarea .el-textarea {
  vertical-align: inherit;
}
.formCommonCols .el-radio-group {
  padding-left: 5px;
  width: 100%;
  height: 36px;
  line-height: 46px;
}
.formCommonCols .el-checkbox-group {
  padding-left: 5px;
  overflow: hidden;
  height: 36px;
}
.formCommonCols .el-col .el-error .el-radio-group,
.formCommonCols .el-col .el-error .el-checkbox-group {
  border: 1px solid #ff5c00 !important;
  border-radius: 3px;
}

.formCommonCols .el-select .el-input__suffix {
  top: 3px;
  right: 7px;
}
.formCommonCols .el-input-number {
  width: 100%;
}
.formCommonCols .el-input-number__increase,
.formCommonCols .el-input-number__decrease {
  display: none;
  line-height: 34px;
  height: 34px;
  font-size: 14px;
}
.formCommonCols .el-input-number input {
  padding-left: 5px;
  text-align: left;
}

.formCommonCols .colitem-percent .el-input-number__increase .el-icon-plus {
  display: none;
}
.formCommonCols .colitem-percent .el-input-number__increase {
  display: inherit;
}
.formCommonCols .colitem-percent .el-input-number__increase:after {
  content: "%";
}

.formCommonCols .colitem-money .el-input-number__increase .el-icon-plus {
  display: none;
}
.formCommonCols .colitem-money .el-input-number__increase {
  display: inherit;
}
.formCommonCols .colitem-money .el-input-number__increase:after {
  content: "元";
}

.formCommonCols .colitem-money .el-input::after {
  position: absolute;
  top: 1px;
  right: 1px;
  content: "元";
  display: inline-block;
  width: 30px;
  height: calc(100% - 2px);
  vertical-align: middle;
  text-align: center;
  background: #f5f7fa;
  border-left: 1px solid #DCDFE6;
}
.formCommonCols .colitem-money .el-input__suffix .el-input__suffix-inner {
  display: none;
}

.formCommonCols .colitem-button .el-input--suffix .el-input__inner {
  padding-right: 5px;
  cursor: pointer;
}
.formCommonCols .colitem-button .is-disabled .el-input__inner {
  cursor: not-allowed;
}
.cursorNotAllowed .el-form-item__content .el-input--suffix .el-input__inner {
  cursor: not-allowed;
}
.formCommonCols .colitem-button .el-icon-search:before,
.formCommonCols .el-icon-date:before {
  cursor: pointer;
  font-size: 16px;
}
.formCommonCols .el-input--prefix .el-input__inner {
  padding-left: 30px;
}
.formCommonCols textarea {
  /*border: 1px solid #DCDFE6 !important;*/
  border: none !important;
  font-size: 14px;
}
.formCommonCols .el-col .col-err-message {
  display: none;
}
.formCommonCols .el-col .el-error .el-input__inner,
.formCommonCols .el-col .el-error .el-textarea__inner,
#wf-audit-content .wf-audit-detail .common-page .form-create .el-error .el-input__inner,
#wf-audit-content .wf-audit-detail .common-page .form-create .el-error .el-textarea__inner {
    border: 1px solid #ff5c00 !important;
}

.formCommonCols .el-col .el-error.col-err-message,
.formCommonCols .el-col .el-error .col-err-message {
  position: absolute;
  display: block;
  top: 3px;
  right: 4px;
  color: #ff5c00;
  font-size: 12px;
  line-height: 12px;
  height: 12px;
}
.formCommonCols .el-col .colitem-money .el-form-item__content .col-err-message,
.formCommonCols
  .el-col
  .colitem-percent
  .el-form-item__content
  .col-err-message {
  right: 45px !important;
}
.formCommonCols
  .el-col
  .colitem-select
  .el-form-item__content
  .col-err-message {
  right: 35px !important;
}

.isMultipleEditTabs .formCanvasRuntimeHasAssembly .el-main {
  padding: 0px 0px 0px 0px !important;
  overflow: hidden;
}
</style>
