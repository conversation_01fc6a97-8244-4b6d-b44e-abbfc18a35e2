<template>
  <div class="flex-column setting-contaniner">
    <div class="colSetting mini-table">
      <div class="colSettingAll">
        <div class="colSettingToolbar">
          <el-input placeholder="查找" @blur="colSearchBlur" @focus="colSearchFocus" @keyup.enter.native="queryAllList" v-model="colsAllSearchText" class="colSettingFilterInput"/>
          <el-select v-model="currentTag"
            @change="queryAllList"
            class="colSettingSelect" clearable placeholder="分类">
            <el-option
                    v-for="(item, index) in tagList"
                    :key="index"
                    :label="item"
                    :value="item"/>
          </el-select>
          <div v-show="isShowColhandleBtn">
              <el-row class="colSettingToolbarButtonContainer">
                <el-button v-show="!isRFormElementList"
                           icon="el-icon-circle-plus-outline"
                           title="新增要素"
                           @click="btAddClick"
                           circle/>
                <el-button v-show="!isRFormElementList"
                           icon="el-icon-delete"
                           title="删除要素"
                           @click="btDeleteClick"
                           circle
                           :loading="isDeleting || isMoveLoading"
                           :disabled="btDeleteDisabledColsAll"/>
                <el-button class="colArrowsBtn" icon="el-icon-right" title="移入选择列表"
                  @click="btSelectClick" circle :loading="isSelecting || isMoveLoading"
                  :disabled="btSelectDisabledColsAll"/>
            </el-row>
          </div>
        </div>
        <div style="height: calc(100% - 33px);">
          <vxe-table
            id="all-colItems-table"
            ref="tableColsAll"
            :data="showAllList"
            size="mini"
            border
            auto-resize
            height="100%"
            show-overflow="tooltip"
            show-header-overflow="tooltip"
            :row-config="{isHover: true, useKey:true, keyField:'id',height: 30}"
            :checkbox-config="{reserve: true}"
            :scroll-y="{enabled: true, gt: 20}"
            @checkbox-change="rowCheckedColsAllDisplay"
            @cell-dblclick="rowDblclickColsAll"
            @checkbox-all="selectAllColsAll"
            style="width: 220px;height: 100%;">
            <vxe-column type="checkbox" width="24"/>
            <vxe-column title="所有要素" field="labelOrigin"/>
            <vxe-column title="分类" width="36" field="tag"/>
            <vxe-column title="类型" field="colType" width="67"/>
          </vxe-table>
        </div>
      </div>
      <div class="colSettingSelected">
        <div class="colSettingSelectedAll">
          <div class="colSettingToolbar">
            <el-input placeholder="查找" v-model="colItemsSearchText" @keyup.enter.native="queryColItems" @blur="colSearchBlur" @focus="colSearchFocus" class="colSettingFilterInput"/>
            <el-row class="colSettingToolbarButtonContainer">
              <el-button class="colArrowsBtn" icon="el-icon-back" title="移出选择列表" @click="btUnSelectClick"
                        circle :loading="isDeleting || isMoveLoading" :disabled="btSelectDisabledColItems"
                        />
            </el-row>
          </div>

          <div style="height: calc(100% - 33px);">
            <vxe-table
              id="show-colItems-table"
              ref="tableColItems"
              :data="showColItems"
              size="mini"
              border
              auto-resize
              height="100%"
              show-overflow="tooltip"
              show-header-overflow="tooltip"
              :row-config="{isHover: true, useKey:true, keyField:'id',height: 30}"
              :scroll-y="{enabled: true, gt: 20}"
              :checkbox-config="{reserve: true}"
              @checkbox-change="checkedSelectClick"
              @selection-change="rowCheckedColItemsDisplay"
              @cell-dblclick="rowDblclickColItems"
              @checkbox-all="selectAllColItems"
              style="width: 220px;">
              <vxe-column type="checkbox" width="24"/>
              <vxe-column title="已选要素" field="label"/>
              <vxe-column title="序号" width="40" field="order" class="colItem-order"/>
              <vxe-column title="类型" width="67" field="colType"/>
            </vxe-table>
          </div>
        </div>

      </div>
    </div>
    <div class="colSettingSelectedSetting">
      <div class="colSettingSelectedSettingBlock">
        <div class="colSettingSelectedSettingEdit">
          <el-form label-width="73px" size="mini" >
            <el-form-item>
              <template #label>
                  <span style="color:#f56c6c;margin-left: -5px;">*</span>
                  <span :title="colItem.labelOrigin">要素名称</span>
              </template>
              <el-input ref="colItemName" v-model="colItem.label" :disabled="isDisabledLabel && colItem.colType!=='审核'"/>
            </el-form-item>
            <el-form-item label="数据类型">
              <el-select v-model="colItem.colType"
                        @change="colTypeChanged"
                        :disabled="isDisabledColType">
                <el-option
                      v-for="item in colTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"/>
              </el-select>
            </el-form-item>
            <el-form-item :title="colItem.dataRef" v-show="isDataRef">
                <template #label>
                  <span style="color:#f56c6c;margin-left: -5px;"
                        v-show="colItem.colType==='下拉框' || colItem.colType==='弹框' || colItem.colType==='公示'">*</span>{{refText}}
                </template>
              <el-input v-model="colItem.dataRef"
                        :placeholder="colItem.colType==='单选'||colItem.colType==='多选'?'选项+逗号隔开如A,B':''"
                        :disabled="isDisabledRef"
                        v-if="!isDataRefDlg && !isDataRefDrop && !isDataRefPublicInfo"/>
              <el-select v-model="colItem.dataRef" v-if="isDataRefPublicInfo" :disabled="isParentHeader">
                <el-option
                        v-for="item in publicInfos"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
              </el-select>
              <el-select v-model="colItem.dataRef" :disabled="isParentHeader"
                         filterable default-first-option v-if="isDataRefDlg">
                <el-option
                  v-for="item in dataRefs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"/>
              </el-select>
              <el-select v-model="colItem.dataRef" :disabled="isParentHeader"
                         filterable allow-create default-first-option v-if="isDataRefDrop">
                <el-option
                        v-for="(item, index) in dataRefDrop"
                        :key="index"
                        :label="item"
                        :value="item"/>
              </el-select>
            </el-form-item>
            <el-form-item :title="colItem.dataRef" v-show="colItem.colType==='审核' && !isRFormElementList">
                <template #label>
                  <span style="color:#f56c6c;margin-left: -5px;" v-show="colItem.colType==='审核'">*</span>关联节点
              </template>
              <!--<el-select v-model="colItem.dataRef"
                         @change="wfColItemChanged"
                         @visible-change="refreshWfNodeNames">
                <el-option
                  v-for="item in wfNodeNames"
                  :key="item" :label="item" :value="item"/>
              </el-select>-->
              <el-select v-model="dataRefOfNodeNames"
                         multiple
                         collapse-tags
                         :disabled="isParentHeader"
                        @change="wfColItemChanged"
                        @visible-change="refreshWfNodeNames">
                <el-option
                        v-for="(item, index) in wfNodeNames"
                        :key="index" :label="item" :value="item"/>
              </el-select>
            </el-form-item>
            <el-form-item label="计算公式" :title="tipDefaultValue" v-show="isNumber">
              <textarea class="defaultTextarea" :rows="2"
                        v-model="colItem.dataRef" :disabled="isDisabledFormula"/>
            </el-form-item>
            <el-form-item label="行高" v-show="!isRFormElementList && !isFree">
              <el-select v-model="colItem.textAreaRows" :disabled="isParentHeader || !isComparison(colItem) || isDisabledColWidth">
                <el-option label="3" :value="3"/>
                <el-option label="4" :value="4"/>
                <el-option label="5" :value="5"/>
              </el-select>
            </el-form-item>
            <el-form-item label="自适应高" v-show="!isRFormElementList && isFree">
              <div style="display: flex; justify-content: space-around;">
                <el-radio v-model="colItem.isAdaptiveRowHeight" :disabled="isParentHeader || !isFree" :label="true">是</el-radio>
                <el-radio v-model="colItem.isAdaptiveRowHeight" :disabled="isParentHeader || !isFree" :label="false">否</el-radio>
              </div>
            </el-form-item>
            <el-form-item v-show="!isFree" label="列宽" :title="tipColWidth">
              <el-select v-show="!isRFormElementList" v-model="colItem.width" style="flex: 1;" :disabled="isDisabledColWidth"
                        :title="tipColWidth" placeholder="">
                <el-option label="1" value="1"/>
                <el-option label="2" value="2"/>
              </el-select>
              <el-input v-show="isRFormElementList" v-model="colItem.width" />
            </el-form-item>
            <el-form-item label="父级名称" v-show="isRFormElementList">
              <el-select v-model="colItem.parentName" style="flex: 1;" :disabled="parentNameDisabled" clearable @change="parentNameChange">
                <el-option v-for="col in filterParentName" :key="col.id" :label="col.label" :value="col.label"/>
              </el-select>
            </el-form-item>
            <el-form-item label="填写提示" :row=1 title="填写提示用于填写单据时,默认的提示语句">
            <textarea class="errToastTextarea" :rows="3"
                      v-model="colItem.fillRemindMessage"
                      :disabled="isParentHeader || !isColItemEditing" />
            </el-form-item>
            <el-form-item :label="valueRangeText">
              <div style="display: flex;" v-if="valueRangeText==='数值范围'">
                <el-input oninput="value=value.replace(/[^-0-9.]/gi,'')" v-model="colItem.min" :disabled="isDisabledMinMax"/>
                <span style="padding: 0px 2px;">至</span>
                <el-input oninput="value=value.replace(/[^-0-9.]/gi,'')" v-model="colItem.max" :disabled="isDisabledMinMax"/>
              </div>
              <div style="display: flex;" v-if="valueRangeText==='字符个数'">
                <el-input oninput="value=value.replace(/\D|^0/g,'')" v-model="colItem.min" :disabled="isDisabledMinMax"/>
                <span style="padding: 0px 2px;">至</span>
                <el-input oninput="value=value.replace(/\D|^0/g,'')" v-model="colItem.max" :disabled="isDisabledMinMax"/>
              </div>
            </el-form-item>
            <el-form-item  label="显示签章" v-if="isSignature()">
              <div style="display: flex;justify-content: space-around;">
                <el-radio  v-model="colItem.showSignature" :label="true" :disabled="isParentHeader"
                          @input="isShowSignature(colItem,true)">是</el-radio>
                <el-radio v-model="colItem.showSignature" :label="false" :disabled="isParentHeader"
                          @input="isShowSignature(colItem,false)">否</el-radio>
              </div>
            </el-form-item>
            <el-form-item label="错误提示" :title="tipErrorMessage" v-show="!isRFormElementList">
              <textarea class="errToastTextarea" :rows="2"
                        v-model="colItem.validateMessage" :disabled="isParentHeader || !isColItemEditing || isAuditColItem"/>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="colSettingSelectedSettingBlock">
        <el-form label-width="73px" size="mini" class="colSettingSelectedSettingEdit" >
          <el-form-item v-show="colItem.colType==='审核' && !isRFormElementList">
            <template #label>
                <span style="color:#f56c6c;margin-left: -5px;" v-show="colItem.colType==='审核'">*</span>审核数据
            </template>
            <el-select v-model="colItem.defaultValue" :disabled="isParentHeader || !isColItemEditing">
              <el-option
                      v-for="(item, index) in wfDataTypes"
                      :key="index" :label="item" :value="item"/>
            </el-select>
          </el-form-item>
          <el-form-item label="修改设置">
            <el-select v-model="colItem.modifyType" :disabled="isDisabledModifyType">
              <el-option
                      v-for="item in modifyTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="是否展示">
            <div style="display: flex; justify-content: space-around;" v-if="!isRFormElementList">
              <el-radio v-model="colItem.isEnabled"  @change="isisEnabledChange(colItem)" label="是" :disabled="!isColItemEditing">是</el-radio>
              <el-radio v-model="colItem.isEnabled"  @change="isisEnabledChange(colItem)" label="否" :disabled="!isColItemEditing">否</el-radio>
            </div>
            <div style="display: flex; justify-content: space-around;" v-else>
              <el-radio v-model="colItem.isShow"  @change="isisEnabledChange(colItem)" label="是" :disabled="!isColItemEditing">是</el-radio>
              <el-radio v-model="colItem.isShow"  @change="isisEnabledChange(colItem)" label="否" :disabled="!isColItemEditing">否</el-radio>
            </div>
          </el-form-item>
          <el-form-item label="是否必填">
            <div style="display: flex; justify-content: space-around;">
              <el-radio v-model="colItem.isRequired" :label="true" :disabled="isDisabledRequired"
                        @change="isRequiredChange(colItem)">是</el-radio>
              <el-radio v-model="colItem.isRequired" :label="false" :disabled="isDisabledRequired"
                        @change="isRequiredChange(colItem)">否</el-radio>
            </div>
          </el-form-item>
          <el-form-item label="参照多选" v-show="!isRFormElementList && !isFree">
            <div style="display: flex; justify-content: space-around;">
              <el-radio v-model="colItem.isMultipleRef" :label="true" :disabled="isNoRefDisabled"
              >是</el-radio>
              <el-radio v-model="colItem.isMultipleRef" :label="false" :disabled="isNoRefDisabled"
              >否</el-radio>
            </div>
          </el-form-item>
          <el-form-item label="序号">
            <el-input style="flex: 1;" oninput="value=value.replace(/[^-0-9]/g,'')"
                      v-model="colItem.order"
                      :disabled="isParentHeader || !isColItemEditing || isFree"/>
          </el-form-item>
          <el-form-item label="小数位数" v-show="!isRFormElementList">
            <div class="colItemDefaultValueColWith" style="display: flex;">
              <el-input style="flex: 1;width: 65px;"  oninput="value=value.replace(/[^0-9]+/g, '')" v-model="colItem.digits" :disabled="isParentHeader || isDisabledDigit || isSignature()"/>
            </div>
          </el-form-item>
          <el-form-item label="动态提示">
              <el-button type="primary" style="width: 65px;" @click="addBxRemind" :disabled="isParentHeader || !isColItemEditing" >添加</el-button>
          </el-form-item>
          <el-form-item label="提示字段" v-show="!isRFormElementList&&!isBlockForm">
          <el-input ref="remindTitle" v-model="colItem.remindTitle" :disabled="isParentHeader || !isColItemEditing" />
          </el-form-item>
          <el-form-item label="默认值" :title="tipDefaultValue" v-show="colItem.colType!=='审核'">
            <!-- <el-input v-model="colItem.defaultValue"
              :disabled="isDisabledDefaultValue" v-if="colItem.labelOrigin !== '业务编码'"/> -->
              <!-- <el-input
                type="textarea"
                v-model="colItem.defaultValue"
                v-if="colItem.labelOrigin !== '业务编码'"
                :disabled="isDisabledDefaultValue"
                :autosize="{ minRows: 1, maxRows: 1}"
              >
              </el-input> -->
            <textarea class="defaultTextarea" :rows="2"
                      v-model="colItem.defaultValue" :disabled="isDisabledDefaultValue" v-if="colItem.labelOrigin !== '业务编码'"/>
            <el-select v-model="colItem.defaultValue" :disabled="isParentHeader"
                       clearable v-if="colItem.labelOrigin === '业务编码'">
              <el-option
                      v-for="item in uniqueCodeList"
                      :key="item.ucode"
                      :label="item.uname"
                      :value="item.ucode"/>
            </el-select>
          </el-form-item>
          <el-form-item label="汇总字段" title="用于列表查询统计" v-show="isRFormElementList">
            <el-select v-model="colItem.collectItem" clearable filterable :disabled="isParentHeader">
              <el-option
                v-for="item in getShowItems"
                :key="item.labelOrigin"
                :label="item.labelOrigin"
                :value="item.labelOrigin"/>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
       <el-button @click="btSaveCol"
            :type="isColItemEditing? 'primary' : 'plain'"
            size="small"
            class="colSettingSelectedSetting_submit"
            :disabled="!isColItemEditing || colEditDisabled"
            :loading="isColSaving">{{btColItemEditText}}</el-button>
      <inc-remind  ref="BxRemind"></inc-remind>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'col-setting',
  props: {
    mode: { type: String }, // "设计", "制单", "详情"
    blockParentCheckData: { type: Object, default: () => {} }
  },
  inject: {
    outFormCanvasIns: { default: undefined },
    clearCurrentSelectedRForm: { default: undefined },
    changeRFormTableHeaderLabel: { default: undefined },
    updateRform: { default: undefined },
    resetElementConfig: { default: undefined },
    hideElementArea: { default: undefined },
    handleElement: { default: undefined },
    changeElementConfigDisabled: { default: undefined },
    clearRformConfig: { default: undefined },
    changeSaveDisabled: { default: undefined }
  },
  data() {
    return {
      isFree: false, // 是否是自由格式
      tipDefaultValue: '新增填报数据时，该要素默认带出的初始值',
      tipColWidth: '表示要素水平跨度是1列或是2列，应用在规则表单的文本(255)和文本(不限)',
      tipErrorMessage: '填报数据时，如果要素值不满足校验，将提示此信息',
      showSignature: false,
      isNew: false, // 当前是否是新增表单
      btDeleteDisabledColsAll: true, // 要素定义列表删除按钮是否禁用
      btSelectDisabledColsAll: true, // 要素定义列表选择按钮是否禁用
      btSelectDisabledColItems: true, // 要素设置列表选择按钮是否禁用
      searchKeyColsAll: '', // 可选要素表格的查询关键字
      searchKeyColItems: '', // 已选要素表格的查询关键字
      colsAllSearchText: '', // 要素定义列表的搜索关键字
      colItemsSearchText: '', // 要素设置列表的搜索关键字
      currentTag: '', // 当前标签分类
      tagList: [], // 标签列表
      colTypes: [], // 要素数据类型列表
      modifyTypes: [], // 修改设置类型列表
      dataRefs: [], // 参照数据列表
      dataRefDrop: [], // 下拉参照
      publicInfos: [], // 公示列表
      uniqueCodeList: [], // 唯一编码列表
      colsAll: {}, // 所有要素, key=要素ID，value=要素对象
      colsAllList: [], // 可选要素表格显示的要素列表
      colsAllListJSON: [],
      colItems: [], // 要素设置列表
      colItemsJSON: [],
      deletingColItemNames: [], // 表单保存时手动删除的列集合
      isEditingCol: false, // 是否是在编辑要素定义
      isEditingColItem: false, // 是否是在编辑要素设置
      isMoveLoading: false,
      isDeleting: false, // 正在执行删除
      isSelecting: false, // 正在执行选定要素
      isColSaving: false, // 正在执行要素保存
      isColItemEditing: false, // 是否正在编辑要素
      colEditDisabled: false,
      isNumber: false, // 是否是数值
      isFloat: false, // 是否是浮点数
      isText: false, // 是否文本
      isRequiredEnabled: true, // 是否必填可用
      isColWidthEnabled: false,
      shouldRef: false, // 是否要设置参照
      btColItemEditText: '编辑要素', // 编辑按钮文本
      colItem: this.newCol(), // 当前编辑的要素
      isDataRefDlg: true, // 弹框参照
      isDataRefDrop: false, // 下拉参照列表
      isDataRefPublicInfo: false, // 公示
      wfMetaIds: [],
      wfDataTypes: ['审核意见', '审核人', '审核日期', '审核签章'], // 流程要素的数据类型枚举
      wfNodeNames: [], // 流程节点名称列表
      isShowColhandleBtn: true,
      cloneColItemRef: '',
      oldCheckedRowsColItem: {}, // 记录原来勾选的已选要素,用于移入选择列表时新要素替换旧的要素,
      formCanvasId: null,
      selectTargetRow: {},
      isRFormElementList: false, // 要素是否展示的是表头的要素
      isBlockForm: false, // 是否是区块表单
      rFormAllList: [], // rForm所有要素
      rFormColItems: [], // rForm已选要素
      rFormAllListSearch: [], // rForm所有要素搜索结果
      rFormColItemsSearch: [], // rForm已选要素搜索结果
      showColMap: {},
      dataRefOfNodeNames: [] // 要素关联节点的集合（后存入colItem.dataRef）
    }
  },
  computed: {
    isParentHeader() {
      return this.isRFormElementList && this.$isNotEmpty(this.blockParentCheckData?.level) && this.blockParentCheckData?.level !== 1
    },
    filterParentName() {
      if (Object.keys(this.colItem).length > 0) {
        return this.rFormColItems.filter(col => col.label !== this.colItem.label && !this.blockParentCheckData?.filterParentName?.includes(col.prop) && col.isEnabled === '是')
      } else {
        return this.rFormColItems
      }
    },
    parentNameDisabled() {
      return this.colItem.length === 1 && this.isRFormElementList
    },
    isDataRef() {
      return this.colItem.colType !== '审核' && !this.isNumber
    },
    showAllList() {
      return this.getColsWithoutRefID(this.isRFormElementList ? this.rFormAllList : this.colsAllList)
    },
    showColItems() {
      return this.getColsWithoutRefID(this.isRFormElementList ? this.rFormColItems : this.colItems)
    },
    getShowItems() {
      return this.colItems.filter(item => {
        return !this.showColMap[item.label]
      })
    },
    refText() {
      return (this.colItem.colType === '公示') ? '公示类别' : '参照数据'
    },
    valueRangeText() {
      return this.isColNumber() ? '数值范围' : '字符个数'
    },
    isDisabledLabel() { // 编辑要素设置时，是否可以修改名称
      // 非编辑状态不能修改，自由表单编辑要素设置时不能修改
      return (!this.isColItemEditing ||
              (this.isFree && !this.isEditingCol))
    },
    isDisabledColType() { // 编辑要素设置时，不能修改数据类型
      return this.isParentHeader || (!this.isColItemEditing || this.isEditingColItem)
    },
    isDisabledFormula() {
      return (this.isParentHeader || (!this.isColItemEditing && !this.isEditingCol) || !this.isNumber)
    },
    isDisabledDefaultValue() {
      return (this.isParentHeader || !this.isColItemEditing || (this.colItem.colType === '公示'))
    },
    isDisabledModifyType() {
      return (this.isParentHeader || !this.isColItemEditing ||
        this.colItem.colType === '审核' ||
        this.colItem.colType === '公示')
    },
    isAuditColItem() { // 是否是审核要素
      return (!this.isColItemEditing || (this.colItem.colType === '审核'))
    },
    isDisabledRef() { // 可用性：参照类型
      return this.isParentHeader || (!this.isColItemEditing ||
            (this.colItem.colType !== '单选' &&
            this.colItem.colType !== '多选' &&
            this.colItem.colType !== '下拉框' &&
            this.colItem.colType !== '弹框'))
    },
    isDisabledRequired() { // 可用性：是否必填
      return (!this.isColItemEditing ||
              this.colItem.colType === '单选' ||
              this.colItem.colType === '二维码' ||
              this.colItem.colType === '条形码' ||
              this.colItem.colType === '隐藏框' ||
              this.colItem.colType === '审核')
    },
    isNoRefDisabled() {
      const noDisAbled = ['弹框', '下拉框']
      return (this.isParentHeader || !(noDisAbled.includes(this.colItem.colType)))
    },
    isDisabledMinMax() { // 可用性：最大最小值
      return (this.isParentHeader || !this.isColItemEditing ||
              (!this.isColNumber() && !this.isColText()) ||
              this.colItem.colType === '公示')
    },
    isDisabledDigit() { // 可用性：小数位数
      return (!this.isColItemEditing ||
        (this.colItem.colType !== '小数' &&
          this.colItem.colType !== '百分比'))
    },
    isDisabledColWidth() { // 可用性：列宽
      return !this.isColItemEditing || this.isFree
    },

    colItemsActual() { // 实际已选要素，不会经过查询过滤
      // 此处存在一个问题：在已选列表存在关键字查询的情况下，
      // 此时如果列表的要素序号有变化，不能实时反映到表单界面
      // 需要手动执行保存要素设置后，制单界面才会渲染变化
      return this.colItems
    }
  },
  created() {
    window.$event.$on('fcRuleChange', this.fcRuleChange)
  },
  beforeDestroy() {
    window.$event.$off('fcRuleChange', this.fcRuleChange)
  },
  watch: {
    colItem: {
      deep: true,
      handler: (nVal) => {
        Object.keys(nVal).forEach(item => {
          if (item === 'isEnabled' && nVal[item] === '') nVal[item] = '是'
        })
      }
    }
  },
  methods: {
    // 父级表头改变
    parentNameChange(parentName) {
      const changeDisabled = disabled => {
        this.colEditDisabled = disabled
        this.changeSaveDisabled?.(disabled)
      }
      changeDisabled(true)
      const params = {
        selectColProp: this.colItem.prop,
        dataType: this.colItem.dataType,
        level: this.blockParentCheckData?.level,
        parentName
      }
      this.$callApiParams('setColumnParentNameValidate', params,
        () => {
          changeDisabled(false)
          return true
        },
        () => {
          changeDisabled(true)
        })
    },
    changeColItemsJSON(cols) {
      this.colItemsJSON.forEach(item => {
        cols.forEach(itemCol => {
          if (item.id === itemCol.id) {
            item.colXPosition = itemCol.colXPosition
            item.colYPosition = itemCol.colYPosition
            item.colPagePosition = itemCol.colPagePosition
            item.signatureType = itemCol.signatureType
          }
        })
      })
    },
    fcRuleChange(showColMap) {
      this.showColMap = showColMap || {}
    },
    isComparison(colItem, comparison = 'fieldComponent') {
      var type = 'input'

      if (colItem.colType === '单选') {
        type = 'radio'
      } else if (colItem.colType === '多选') {
        type = 'checkbox'
      } else if (colItem.colType === '下拉框') {
        // 如果是详情模式，直接使用input，不使用select
        // 因为select将有响应事件，暂时不能解绑事件

      } else if (colItem.colType === '日期') {
        type = 'DatePicker'
      } else if (colItem.colType === '整数' ||
        colItem.colType === '小数' ||
        colItem.colType === '金额' ||
        colItem.colType === '百分比') {
        type = 'InputNumber'
      } else if (colItem.colType === '弹框ID' || colItem.colType === '隐藏框') {
        type = 'hidden'
      } else if (colItem.colType === '弹框') {
        type = 'input'
      } else {
        if (parseInt(colItem.width) === 2) { // 跨行文本处理，比如详情之类的要素
          type = 'fieldComponent'
        }
      }

      if (colItem.colType === '文本(30)' || colItem.colType === '文本(100)') {
        type = 'input'
      }

      if (colItem.isEnabled === '否') {
        type = 'hidden'
      }
      return type === comparison
    },
    isShowSignature(colItem, show) {
      this.$set(colItem, 'showSignature', show)
      this.$forceUpdate()
      this.outFormCanvasIns.$refs.formFormat.isShowSignature(show, this.colItem)
    },
    colSearchBlur() {
      this.isShowColhandleBtn = true
      $('.colSettingToolbar').css('width', 'auto')
      $('.colSettingToolbarButtonContainer').css('width', '105px')
      $('.colSettingFilterInput').css('width', '70px')
      $('.colSettingFilterInput').css('flex', 'initial')
    },
    colSearchFocus() {
      this.isShowColhandleBtn = false
      $('.colSettingToolbarButtonContainer').css('width', '70px')
      $('.colSettingToolbar').css('width', '220px')
      $('.colSettingFilterInput').css('flex', '1')
    },
    checkedSelectClick(data = {}) {
      if (this.$isEmpty(data.row)) {
        return
      }
      const selection = this.$getTableSelection(this.$refs.tableColItems)
      if (this.isFree) {
        if (selection.length > 1) {
          const del_row = selection.shift()
          this.$refs.tableColItems.toggleRowSelection(del_row, false)
        }
      }
      this.$event(this, 'colSetcheckedSelectClick', selection, data.row)
      // 修复vxe规则表单选中后不触发selection-change问题
      if (selection[0] && !this.isFree) {
        this.$refs.tableColItems.toggleRowSelection(selection[0] || '', true)
      } else if (!selection[0]) {
        this.$refs.tableColItems.clearSelection()
      }
    },
    init(isNewFrom) {
      if (this.formCanvasId === this.outFormCanvasIns.meta.main.id) return
      this.clearRFormElementList()
      this.isNew = isNewFrom
      this.currentTag = ''
      this.colsAllSearchText = ''
      this.colItemsSearchText = ''
      this.selectColsData()
      this.colItem = {}
      this.dataRefOfNodeNames = []
      this.formCanvasId = this.outFormCanvasIns.meta.main.id
    },
    getColsWithoutRefID(items) { // 要素列表不显示“弹框ID”类别
      return items.filter(item => {
        return (item.colType !== '弹框ID')
      })
    },
    rowDblclickColsAll({ row, $event: event }) {
      if (this.isRFormElementList) {
        const labels = row.label
        const updateParams = {
          labels,
          type: '移入'
        }
        this.findSelectedTableInRorms(updateParams)
        this.$refs.tableColsAll.clearSelection()
      } else {
        // 限制要素绑定双击复选框
        if (event.target._prevClass.includes('checkbox') || this.isMoveLoading) return
        this.isMoveLoading = true
        // 自由表单时，双击要素定义列表时，如果当前有正在编辑的要素设置
        // 则相当于使用当前双击的要素定义替换正在编辑的要素设置，即左右对调
        if (this.isFree) {
          var params = {
            isNotPass: false
          }
          this.$event(this, 'addColItem', params)
          if (params.isNotPass) {
            this.isMoveLoading = false
            return
          }
        }
        this.doInListWrap([row.id])
      }
    },
    rowDblclickColItems({ row }) {
      if (this.isRFormElementList) {
        const labels = row.label
        const updateParams = {
          labels,
          type: '移出'
        }
        this.findSelectedTableInRorms(updateParams)
      } else {
        if (this.isMoveLoading) {
          return
        }
        this.isMoveLoading = true
        this.$event(this, 'removeColItem', row)
        this.doOutList([row.id])
      }
    },
    wfColItemChanged(newNodeName) { // 审核要素的名称为空时，自动设置为关联节点的名称
      if (this.$isEmpty(this.colItem.label)) {
        this.colItem.label = newNodeName
      }
      if (this.$isEmpty(this.colItem.defaultValue)) {
        // this.$set(this.colItem, 'defaultValue', '审核意见')
        this.colItem.defaultValue = '审核意见'
      }
      this.colItem.dataRef = this.dataRefOfNodeNames.join(',')// 将关联节点集合转成 字符串
    },
    refreshWfNodeNames(visible) {
      if (visible && this.$isNotEmpty(this.wfMetaIds)) {
        this.$callApiParams('getAllWfNodesByWfMetaId',
          { 'wfMetaIds': this.wfMetaIds.join(',') },
          result => {
            var wfNodes = result.data
            if (this.$isNotEmpty(wfNodes)) {
              this.wfNodeNames = []
              wfNodes.forEach(node => this.wfNodeNames.push(node.name))
            }
            return true
          })
      }
    },
    canDoIn() { // 是否可以执行移入已选要素
      var ids = this.$getTableCheckedIds(this.$refs.tableColsAll)
      if (this.isFree) { // 如果是自由表单，只能单个要素逐一绑定
        return (ids.length === 1)
      }
      return (ids.length > 0)
    },
    canDoOut() { // 是否可以执行移出已选要素
      var ids = this.$getTableCheckedIds(this.$refs.tableColItems)
      return (ids.length > 0)
    },
    btSelectClick() {
      if (this.isRFormElementList) {
        const checkedRows = this.$getTableSelection(this.$refs.tableColsAll)
        const labels = checkedRows.map(row => row.label)
        const updateParams = {
          labels,
          type: '移入'
        }
        this.findSelectedTableInRorms(updateParams)
        this.$refs.tableColsAll.clearSelection()
      } else {
        this.doInListWrap(this.$getTableCheckedIds(this.$refs.tableColsAll))
      }
    },
    btUnSelectClick() { // 从要素设置移除，从sourceId找到原始的要素定义添加到定义列表
      var checkedRows = this.$getTableSelection(this.$refs.tableColItems)
      if (this.isRFormElementList) {
        const labels = checkedRows.map(row => row.label)
        const updateParams = {
          labels,
          type: '移出'
        }
        this.findSelectedTableInRorms(updateParams)
      } else {
        checkedRows.forEach(row => {
          this.$event(this, 'removeColItem', row)
        })
        this.doOutList(this.$getTableCheckedIds(this.$refs.tableColItems))
      }
    },

    RowOutList(ids, notRefresh) {
      this.deletingColItemNames = []
      this.colItems = this.colItems.filter(item => ids.indexOf(item.id) === -1)
      this.colItemsJSON = this.colItemsJSON.filter(item => ids.indexOf(item.id) === -1)
    },
    doInListWrap(ids) {
      // 自由表单
      const oldCheckColItemLabel = this.$isNotEmpty(this.oldCheckedRowsColItem) && this.oldCheckedRowsColItem.label
      const oldCheckColItemId = this.$isNotEmpty(this.oldCheckedRowsColItem) && this.oldCheckedRowsColItem.id
      // 清空选择
      this.$refs.tableColsAll.clearSelection()
      // 自由表单时的移入操作，如果当前有编辑的要素设置，则先移除再添加
      if (this.isFree) {
        var editingColItem = this.getEditingColItemLabel() || oldCheckColItemLabel
        const doInCol = this.colsAll[ids[0]]
        const isDoOut = doInCol && doInCol.colType !== '隐藏框'
        if (editingColItem && this.$isNotEmpty(editingColItem) && isDoOut) {
          const id = this.colItem.id || oldCheckColItemId
          this.doOutList([id], true)
        }
      }
      // ids  rowId
      this.doInList(ids)
    },
    doOutList(ids, notRefresh) { // 移出
      this.deletingColItemNames = []
      this.colItems = this.colItems.filter(item => {
        if (ids.indexOf(item.id) > -1) {
          this.deletingColItemNames.push(item.labelOrigin)
          return
        }
        return item
      })
      this.colItemsJSON = this.colItemsJSON.filter(item => ids.indexOf(item.id) === -1)
      this.selectColsData()
      if (notRefresh !== true) {
        this.refreshAfterInOut()
      }
      this.$nextTick(() => {
        this.$emit('colItemsChange', this.colItemsJSON)
      })
    },
    doInList(ids, notRefresh) { // 移入
      let theAddingColLabel
      var obj
      ids.forEach((id, iIndex) => {
        // 新增要素设置时，拷贝要素定义，处理表单ID关联
        obj = this.$clone(this.colsAll[id])
        // obj.sourceId = obj.id // 关联原始要素定义
        theAddingColLabel = obj.label
        this.$event(this, 'setColItemXY', obj)
        obj.isAutoAdd = false // 前端添加的要素设置肯定不是自动添加
        this.colItems.push(obj)
        this.colItemsJSON.push(obj)
        // 从要素定义列表里删除
        this.colsAllList = this.colsAllList.filter(col => col.id !== id)
        this.colsAllListJSON = this.colsAllListJSON.filter(col => col.id !== id)
        if (this.isNew && iIndex === 0) {
          this.setEditingColItem(obj.label)
        }
      })
      if (notRefresh !== true) {
        // 如果保存失败，则需要将之前界面上添加的要素删除
        var failedCallback = (params) => {
          const oldColItemLabel = this.getEditingColItemLabel()
          if (this.isFree && params && params.formFormat) {
            params.formFormat.removeColItem(obj)
          }
          // 保存失败，规则表单去除的数据消失问题
          ids.forEach(id => {
            this.colsAllList.push(this.colsAll[id])
            this.colsAllListJSON.push(this.colsAll[id])
          })
          return oldColItemLabel || ''
        }
        this.refreshAfterInOut(theAddingColLabel, failedCallback)
      }
      this.$nextTick(() => {
        this.$emit('colItemsChange', this.colItemsJSON)
        // this.setEditingColItem(theAddingColLabel)
      })
      return theAddingColLabel
    },
    refreshAfterInOut(theAddingColLabel, failedCallback) {
      this.$nextTick(() => {
        // 操作穿梭时，对已存在表单自动执行保存操作，并且不需要弹出保存提示
        // 如果当前正在执行新增表单的操作，穿梭操作不会触发保存动作
        this.saveMeta(false,
          result => {
            // 以下处理实现从要素定义列表新增一个要素设置后，新增的要素处于勾选状态
            // 请查询关键字theAddingColLabel了解程序流程
            if (result && this.$isNotEmpty(theAddingColLabel)) {
              result.theAddingColLabel = theAddingColLabel
            }
            this.$sortColumnList(this.colsAllList)
            return true
          },
          (result, params) => {
            let oldColItemLabel = ''
            if (failedCallback) {
              oldColItemLabel = failedCallback(params)
            }
            // 穿梭保存失败时，params中存储有versionRelate对象，
            // 可以模拟点击树节点重新打开一个当前表单，实现恢复穿梭之前的数据
            var versionRelate = params['versionRelate']
            if (versionRelate) {
              versionRelate.reloadCurrentNode(oldColItemLabel)
            }
          })
      })
    },
    queryAllList() {
      if (this.isRFormElementList) {
        // 规则表单不支持条形码和二维码
        let items = this.getColsWithoutRefID(this.rFormAllListSearch)
        if (this.$isNotEmpty(this.currentTag) ||
          this.$isNotEmpty(this.colsAllSearchText)) {
          items = items.filter(item => {
            return this.matchFilter(
              item.labelOrigin, item.tag,
              this.colsAllSearchText, this.currentTag)
          })
        }
        this.rFormAllList = items
      } else {
        // 规则表单不支持条形码和二维码
        let items = this.getColsWithoutRefID(this.colsAllListJSON)
        if (this.$isNotEmpty(this.currentTag) ||
          this.$isNotEmpty(this.colsAllSearchText)) {
          items = items.filter(item => {
            return this.matchFilter(
              item.labelOrigin, item.tag,
              this.colsAllSearchText, this.currentTag)
          })
        }
        this.colsAllList = items
      }
    },
    queryColItems() { // 已选要素表格实际显示的数据(可能经过了查询过滤)
      if (this.isRFormElementList) {
        let items = this.getColsWithoutRefID(this.rFormColItemsSearch)
        if (this.$isNotEmpty(this.colItemsSearchText)) {
          items = items.filter(item => {
            return this.matchFilter(
              item.label, '',
              this.colItemsSearchText, '')
          })
        }
        this.rFormColItems = items
      } else {
        let items = this.getColsWithoutRefID(this.colItemsJSON)
        if (this.$isNotEmpty(this.colItemsSearchText)) {
          items = items.filter(item => {
            return this.matchFilter(
              item.label, '',
              this.colItemsSearchText, '')
          })
        }
        this.colItems = items
      }
    },
    matchFilter(label, tag, searchText, tagType) {
      var matchSearch = true
      if (this.$isNotEmpty(searchText)) {
        matchSearch = String(label.toLowerCase())
          .match(searchText.toLowerCase())
      }

      var matchTag = true
      if (this.$isNotEmpty(tagType)) {
        matchTag = String(tag).match(tagType)
      }
      return (matchSearch && matchTag)
    },
    selectColsData(callback, failCallBack) { // 查找所有的要素定义
      this.$callApiParams('selectColsData', {},
        result => {
          this.colsAll = {}
          this.colsAllList = []
          this.colsAllListJSON = []
          this.colTypes = result.data.colTypes
          this.modifyTypes = result.data.modifyTypes
          this.tagList = result.data.tagList
          this.dataRefs = result.data.dataRef
          this.dataRefDrop = result.data.dataRefDrop
          this.uniqueCodeList = result.data.uniqueCodeList

          this.publicInfos = []
          if (result.data.publicInfos) {
            result.data.publicInfos.forEach(info => {
              var dlgTokens = this.$resolvePublicInfosDlgTokens(info)
              this.publicInfos.push({ label: dlgTokens[0], value: info })
            })
          }

          var colsList = result.data.colsAll
          colsList.forEach(item => {
            // 自由格式时，可用使用所有类型的要素
            // 规则格式时，不能使用条形码和二维码
            if ((this.isFree ||
              (item.colType !== '条形码' && item.colType !== '二维码'))) {
              this.colsAll[item.id] = item
              this.colsAllList.push(item)
              this.colsAllListJSON.push(item)
            }
          })
          this.makeSureColItemsNotInColsAll()

          if (callback) {
            callback(result)
          }
          return true
        }, () => {
          failCallBack && failCallBack()
        })
    },
    setColItems(meta) { // 设计时新打开表单要更新要素设置列表
      this.$refs.tableColsAll.clearSelection()
      this.colItems = meta.colItems
      this.isBlockForm = meta.main.isBlockForm
      this.colItemsJSON = this.$clone(this.colItems)
      this.queryColItems()
      this.wfMetaIds = meta.wfMetaIds
      this.makeSureColItemsNotInColsAll()
      this.$nextTick(() => {
        if (this.$isNotEmpty(meta.theAddingColLabel)) {
          this.setEditingColItem(meta.theAddingColLabel)
          meta.theAddingColLabel = undefined
        } else if (this.$isNotEmpty(meta.oldColItemLabel)) {
          this.setEditingColItem(meta.oldColItemLabel)
          meta.oldColItemLabel = undefined
        }
        this.$nextTick(() => {
          this.isMoveLoading = false
        })
      })
    },
    makeSureColItemsNotInColsAll() {
      // 已经在要素设置中使用的要素定义ID，确保不在要素定义列表中显示
      var colIdsUsed = []
      this.colItemsJSON.forEach(item => {
        if (item.colType !== '弹框ID') { // "弹框ID"要素不会在要素列表中显示，所以不用理会
          // colIdsUsed.push(item.sourceId)
          colIdsUsed.push(item.labelOrigin)
        }
      })
      if (colIdsUsed.length > 0) {
        this.colsAllList = this.colsAllList.filter(item => colIdsUsed.indexOf(item.labelOrigin) === -1)
        this.colsAllListJSON = this.colsAllListJSON.filter(item => colIdsUsed.indexOf(item.labelOrigin) === -1)
      }
      this.queryAllList()
      this.$nextTick(() => {
        this.$emit('colItemsChange', this.colItemsJSON)
      })
    },
    colTypeChanged() {
      if (this.colItem.colType === '单选') {
        this.colItem.isRequired = true
      }
      // if ((this.colItem.colType === '文本(30)')) { // 短文本列宽固定为1
      //   this.colItem.width = '1'
      // }
      this.isDataRefDlg = (this.colItem.colType === '弹框')
      this.isDataRefDrop = (this.colItem.colType === '下拉框')
      this.isDataRefPublicInfo = (this.colItem.colType === '公示')
      this.isNumber = this.isColNumber()
      const allColSelectItems = this.$getTableSelection(this.$refs.tableColsAll)
      if (this.colItem.id) { // 修改时
        if (allColSelectItems.length === 1) {
          if (this.isDataRefDlg && this.cloneColItemRef.colType === '弹框') {
            this.colItem.dataRef = this.cloneColItemRef.dataRef
          } else if (this.isDataRefDrop && this.cloneColItemRef.colType === '下拉框') {
            this.colItem.dataRef = this.cloneColItemRef.dataRef
          } else if (this.colItem.colType === '单选' ||
            this.colItem.colType === '多选' ||
            this.colItem.colType === '公示') {
            this.colItem.dataRef = this.cloneColItemRef.dataRef
          } else {
            this.colItem.dataRef = ''
            this.dataRefOfNodeNames = []
          }
        }
      } else { // 新增时
        if (this.colItem.dataRef &&
          (!this.isDataRefDlg || !this.isDataRefDrop || !this.isDataRefPublicInfo)) {
          this.colItem.dataRef = ''
          this.dataRefOfNodeNames = []
        }

        if (this.colItem.colType === '金额') {
          this.colItem.digits = 2
        }

        if (this.colItem.colType === '百分比') {
          this.colItem.digits = 4
        }
      }

      this.syncRequiredMin()
    },
    isisEnabledChange(colItem) {
      const show = this.isRFormElementList ? colItem.isShow : colItem.isEnabled
      if (show === '否') {
        colItem.isRequired = false
      }
    },
    isRequiredChange(colItem) {
      this.syncRequiredMin()
      const show = this.isRFormElementList ? colItem.isShow : colItem.isEnabled
      if (show === '否') {
        this.$message.error('请先展示该要素')
        colItem.isRequired = false
        return
      }
    },
    isColNumber() { // 要素类型是否是数值
      return (this.colItem.colType === '整数' ||
              this.colItem.colType === '小数' ||
              this.colItem.colType === '百分比' ||
              this.colItem.colType === '金额')
    },

    isSignature() {
      return this.colItem.defaultValue === '审核签章'
    },
    isColFloat() { // 要素是否是浮点数
      return (this.colItem.colType === '小数' ||
              this.colItem.colType === '百分比' ||
              this.colItem.colType === '金额')
    },
    isColText() { // 要素是否是显示文本
      return (this.colItem.colType && this.colItem.colType.startsWith('文本') ||
              (this.colItem.colType === '审核' && this.colItem.defaultValue === '审核意见'))
    },
    syncRequiredMin() { // 同步必填与最小值的关系：如果是非数字，必填是最小值默认是1
      if (!this.isColNumber() &&
          this.colItem.min !== undefined) { // 非数字，且填写了最小值
        // 必填时，最小值不能小于1
        if (this.colItem.isRequired && parseInt(this.colItem.min) <= 0) {
          this.colItem.min = 1
        }

        // 非必填，最小值填写了大于0，最小值输入框不可用
        // 则调整最小值为0
        if (!this.colItem.isRequired &&
            parseInt(this.colItem.min) > 0 &&
            this.isDisabledMinMax) {
          this.colItem.min = 0
        }
      }
    },
    newCol() {
      return {
        id: '',
        label: '',
        colType: '',
        modifyType: '读写',
        ref: '',
        isRequired: false,
        min: undefined,
        max: undefined,
        digits: 0,
        order: 0,
        defaultValue: '',
        width: 1,
        validateMessage: '',
        textAreaRows: 3,
        isMultipleRef: false,
        isAdaptiveRowHeight: false
      }
    },
    rowCheckedColsAllDisplay(data = {}) { // 要素定义列表行选择发生变化时的处理
      const checkedRow = data && data.row
      if (!checkedRow) {
        return
      }
      var checkedRows = this.$getTableSelection(this.$refs.tableColsAll)
      var checkedRowsColItem = this.$getTableSelection(this.$refs.tableColItems)
      const type = checkedRows.length && checkedRows[0].colType
      this.isDataRefDlg = (type === '弹框')
      this.isDataRefDrop = (type === '下拉框')
      this.isDataRefPublicInfo = (type === '公示')

      var checkRowsMoreThanOne = (checkedRows.length >= 1)
      this.btDeleteDisabledColsAll = !checkRowsMoreThanOne
      this.btSelectDisabledColsAll = !this.canDoIn()
      if (checkedRows.length === 1) {
        if (this.isFree && checkedRowsColItem.length) {
          this.oldCheckedRowsColItem = this.$clone(checkedRowsColItem[0])
        }
        this.$refs.tableColItems.clearSelection()
        this.colItem = this.$clone(this.isRFormElementList ? checkedRow : this.colsAll[checkedRows[0].id])
        this.dataRefOfNodeNames =
          this.$isNotEmpty(this.colItem.dataRef) ? this.colItem.dataRef.split(',') : []
        this.cloneColItemRef = checkedRow
        if (this.colItem.tag === '扩展') { // 分类为扩展时可改
          this.isEditingColItem = false
          this.isEditingCol = true
          this.beginEdit('修改要素')
        } else {
          this.isColItemEditing = false
        }
      } else {
        // 自由表单时只能勾选一个要素定义，如果之前就是勾选了一个要素定义处于编辑
        // 则此时表明勾选了另一个要素定义，则响应是切换成编辑另一个要素定义
        this.isColItemEditing = false
        this.isEditingCol = false
        this.colItem = {}
        if (this.isFree && this.isEditingCol) {
          if (checkedRows.length) {
            var newCheckedRow = (checkedRows[0].id === this.colItem.id)
              ? checkedRows[1] : checkedRows[0]
            this.$refs.tableColsAll.clearSelection()
            this.$refs.tableColsAll.toggleRowSelection(newCheckedRow, true)
          } else {
            this.isColItemEditing = true
          }
        }
      }
    },
    rowCheckedColItemsDisplay() { // 要素设置列表行选择发生变化时的处理
      const checkedRows = this.$getTableSelection(this.$refs.tableColItems)
      if (checkedRows.length) {
        const type = checkedRows[0].colType
        this.isDataRefDlg = (type === '弹框')
        this.isDataRefDrop = (type === '下拉框')
      }
      this.btSelectDisabledColItems = !this.canDoOut()
      if (checkedRows.length === 1) {
        if (this.isRFormElementList) {
          // 联动选中表头
          this.changeRFormTableHeaderLabel(checkedRows[0].label)
        }
        this.$refs.tableColsAll.clearSelection()
        var checkedRow = checkedRows[0]
        // if (checkedRow.labelOrigin === '业务编码' && checkedRow.defaultValue) {
        //   var defaultValue = checkedRow.defaultValue
        //   if (defaultValue.startsWith('#{') || defaultValue.startsWith('${')) {
        //     defaultValue = defaultValue.substring(2, defaultValue.length - 1) // 去掉首尾 #{、${、 }
        //   }
        //   checkedRow.defaultValue = defaultValue
        // }

        // Object.keys(checkedRow).forEach(item => {
        //   this.$set(this.colItem, item, checkedRow[item])
        // })
        // 深拷贝数据 每次只能修改一个要素 切换到其他要素后恢复到之前的数据
        this.colItem = this.$clone(checkedRow)
        this.dataRefOfNodeNames =
          this.$isNotEmpty(this.colItem.dataRef) ? this.colItem.dataRef.split(',') : []

        this.isEditingCol = false
        this.isEditingColItem = true
        this.beginEdit('修改要素')
        this.colTypeChanged()
        this.$emit('editingColItemChanged', true, this.colItem, this.isRFormElementList)
      } else {
        // 区块表单 当要素是某一个区块行表单的数据时 选择1个以上的要素要把选中边框清除
        if (this.isRFormElementList) {
          if (checkedRows.length > 1 || checkedRows.length === 0) {
            this.changeRFormTableHeaderLabel()
          }
        }
        this.isColItemEditing = false
        this.isEditingColItem = false
        this.colItem = {}
        // if (this.isFree) {
        //   this.btSelectDisabledColItems = this.canDoOut()
        // }
        this.$emit('editingColItemChanged', false)
        if (this.isFree && this.isEditingCol) {
          if (checkedRows.length) {
            var newCheckedRow = (checkedRows[0].id === this.colItem.id)
              ? checkedRows[1] : checkedRows[0]
            this.$refs.tableColsAll.clearSelection()
            this.$refs.tableColsAll.toggleRowSelection(newCheckedRow, true)
          } else {
            this.isColItemEditing = true
          }
        }
      }
    },
    getEditingColItemLabel() { // 获取当前编辑的要素设置的label
      if (this.isEditingColItem) {
        return this.colItem.label
      }
    },
    getEditingColItem() { // 获取当前编辑的要素设置
      if (this.isEditingColItem) {
        return this.colItem
      }
    },
    setEditingColItem(colLabel) { // 设置当前编辑的要素设置
      var targetRow
      if (this.$isNotEmpty(colLabel)) {
        if (this.isRFormElementList) {
          for (let i = 0; i < this.rFormColItems.length; i++) {
            if (this.rFormColItems[i].label === colLabel) {
              targetRow = this.rFormColItems[i]
              break
            }
          }
        } else {
          for (let i = 0; i < this.colItems.length; i++) {
            if (this.colItems[i].label === colLabel) {
              targetRow = this.colItems[i]
              break
            }
          }
        }
      }
      if (targetRow && targetRow.colType === '隐藏框') {
        window.luckysheet.setRangeShow('A1', { show: false })
      }
      if (targetRow) {
        this.selectTargetRow = targetRow
        this.$refs.tableColItems.clearSelection()
        this.$refs.tableColItems.loadData(this.showColItems).then(() => { this.$refs.tableColItems.scrollToRow(targetRow) })
        this.$refs.tableColItems.toggleRowSelection(targetRow, true)
      } else {
        this.selectTargetRow = {}
        // 没有对应的要素设置，则取消勾选
        var currentColItem = this.getEditingColItem()
        if (this.$isNotEmpty(currentColItem)) {
          this.$refs.tableColItems.clearSelection()
        }
      }
    },
    btAddClick() {
      this.$refs.tableColsAll.clearSelection()
      this.$refs.tableColItems.clearSelection()
      this.isEditingCol = true
      this.isColItemEditing = false
      this.isEditingColItem = false
      this.colItem = this.newCol()
      this.dataRefOfNodeNames = []
      this.colItem.colType = '文本(30)'
      this.colItem.tag = '扩展'
      this.beginEdit('新增要素')
      this.cloneColItemRef = ''
    },
    beginEdit(btEditText) {
      this.isColItemEditing = true
      this.btColItemEditText = btEditText
      this.$nextTick(() => {
        this.$refs.colItemName.focus()
        this.colTypeChanged()
      })
    },
    btDeleteClick() {
      var ids = this.$getTableCheckedIdsStr(this.$refs.tableColsAll)
      const selections = this.$getTableSelection(this.$refs.tableColsAll)
      var message = (selections.length === 1)
        ? `确定要删除要素定义 [${selections[0].label}] 吗？`
        : `确定要删除 ${selections.length} 个要素定义吗？`
      this.$callApiParamsConfirm(message,
        null, 'deleteCformCol', { ids: ids },
        result => {
          this.selectColsData()
          this.btDeleteDisabledColsAll = !this.btDeleteDisabledColsAll
          selections.splice(0, selections.length)
        })
    },
    saveMeta(alterWhenIsNew, callbackSuccess, callbackFailed, exData = {}) {
      if (this.isNew && !exData.isImportingExcel) {
        if (alterWhenIsNew) {
          this.$message.info('请先保存当前表单')
          this.isColSaving = false
        }
        const callback = () => {
          this.isMoveLoading = false
        }
        this.selectColsData(callback, callback)
      } else { // 修改已存在表单的要素时，使用表单整体的保存方法
        const exArg = {}
        if (exData.isImportingExcel) {
          exArg.actionKey = 'saveImport'
        }
        this.$emit('saveMeta',
          result => {
            this.isColSaving = false
            if (callbackSuccess) {
              return callbackSuccess(result)
            }
          }, (result, params) => {
            this.isColSaving = false
            if (callbackFailed) {
              return callbackFailed(result, params)
            }
          }, exArg)
      }
    },
    btSaveCol() {
      this.isColSaving = true
      if (this.isEditingCol) { // 修改要素定义，使用独立的保存API保存
        this.colItem.labelOrigin = this.colItem.label
        if (this.$isEmpty(this.colItem.dataRef) &&
          this.$isNotEmpty(this.dataRefOfNodeNames)) {
          this.colItem.dataRef = this.dataRefOfNodeNames.join(',')
        }

        this.$callApi('saveCformCol', this.colItem,
          result => {
            var id = result.attributes['id']
            this.isColSaving = false
            this.selectColsData(re => {
              var row = this.colsAll[id]
              if (this.isEditingCol) { // 当前是保存要素定义
                this.$nextTick(() => {
                  this.$refs.tableColsAll.toggleRowSelection(row)
                  this.$refs.tableColsAll.loadData(this.showAllList).then(() => { this.$refs.tableColsAll.scrollToRow(row) })
                })
              }
            })
          }, result => { this.isColSaving = false })
      } else if (this.isEditingColItem) {
        this.colItem.labelAlias = this.colItem.label
        // 解决深拷贝选中要素后数据不响应问题
        if (this.isRFormElementList) {
          this.rFormColItems.forEach(item => {
            if (item.id === this.colItem.id) {
              Object.assign(item, this.colItem)
            }
          })
        } else {
          this.colItems.forEach(item => {
            if (item.id === this.colItem.id) {
              Object.assign(item, this.colItem)
            }
          })
        }
        this.saveMeta(true)
      }
    },
    addBxRemind() {
      const metaId = this.$sessionStorage.get('treeId')
      const coltype = this.colItem.colType
      const isBlockForm = this.isBlockForm
      const exparam = { targetRow: this.selectTargetRow, metaId: metaId,
        coltype: coltype, isBlockForm: isBlockForm }
      // 获取到要素名称和表单Id打开增加提示语句编辑框
      this.$refs.BxRemind.selectBxRemindList(exparam)
    },
    // 区块表单点击表头 重新设置所有要素 已选要素
    rFormSetElementList(allElement, selectedElement) {
      this.$refs.tableColItems.clearSelection()
      this.rFormAllList = allElement
      this.rFormAllListSearch = allElement
      this.rFormColItems = selectedElement
      this.rFormColItemsSearch = selectedElement
      this.isRFormElementList = true
      this.resetElementConfig()
    },
    clearRFormElementList() {
      if (this.isRFormElementList) {
        this.rFormAllList = []
        this.rFormColItems = []
        this.rFormAllListSearch = []
        this.rFormColItemsSearch = []
        this.isRFormElementList = false
      }
    },
    findSelectedTableInRorms(updateParams) {
      const allRforms = document.querySelectorAll('.blockViewBlockRform')
      const eleToArr = Array.from(allRforms)
      for (let i = 0; i < eleToArr.length; i++) {
        if (eleToArr[i].querySelector('.currentSelectedTable')) {
          const index = eleToArr[i]?.parentElement?.dataset?.index
          this.updateRform(index, updateParams)
          break
        }
      }
    },
    selectAllColsAll() {
      if (this.$isNotEmpty(this.$getTableSelection(this.$refs.tableColsAll))) {
        this.$refs.tableColItems.clearSelection()
      }
    },
    selectAllColItems() {
      if (this.$isNotEmpty(this.$getTableSelection(this.$refs.tableColItems))) {
        this.$refs.tableColsAll.clearSelection()
      }
    },
    resetColSetting() {
      this.changeElementConfigDisabled?.(true)
      // 清空组件管理配置项
      this.clearRformConfig?.()
      this.hideElementArea()
      // 清空选中当前操作的是哪个表格 及 清空表头选中
      this.changeRFormTableHeaderLabel()
      this.clearCurrentSelectedRForm()
      this.handleElement()
    }
  }
}
</script>
<style lang="scss" scoped>
  .colSetting {
    overflow: hidden;
    flex: 1;
    position: relative;
    display: flex;
    width:100%;
    margin-bottom: 10px;
    // height:100%;
  }
  .colSettingAll {position: relative;width:220px;flex: 1;}
  .colSettingSelected {flex: 1;display: flex;flex-direction: column;}
  .colSettingSelectedAll{flex:1; height: 40%;}
  .colSettingSelectedSetting {
    position: relative;
    display: flex;
    height: 438px;
    // width: 220px;
    margin-right: 10px;
    border: 1px solid #DDDDDD;
    &_submit {
      position: absolute!important;
      bottom: 10px;
      left: 50%;
      transform: translate(-50%,0);
    }
  }
  .colSettingSelectedSettingBlock{
    flex: 1;
    height: 100%;
    padding: 10px;
    position: relative;
  }
  .defaultTextarea{font-size: 12px;border: 1px solid #bbbbbb;padding: 5px;margin-bottom:-8px;width:100%}
   .defaultTextarea:focus, /deep/input:focus {
    outline: none !important;
    border-color: #719ECE;
    box-shadow: 0 0 5px #719ECE;
  }
  .errToastTextarea{font-size: 12px;border: 1px solid #bbbbbb;padding: 5px;width:100%}
  .errToastTextarea:focus, /deep/input:focus {
    outline: none !important;
    border-color: #719ECE;
    box-shadow: 0 0 5px #719ECE;
  }
  #show-colItems-table,
  #all-colItems-table {
    /deep/ .el-table__body-wrapper{
      overflow-x: hidden;
    }
  }
  .flex-column {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .setting-contaniner .colSettingSelectedSettingEdit{
    /deep/.el-form-item--mini.el-form-item,/deep/ .el-form-item--small.el-form-item {
      margin-bottom: 10px;
    }
  }
</style>
<style lang="scss">
  .colSettingSelectedSettingEdit .el-form-item__label {
    text-align: left;
  }
  .colItemDefaultValueColWith .el-input__suffix {
    right: 0px;
  }
</style>
