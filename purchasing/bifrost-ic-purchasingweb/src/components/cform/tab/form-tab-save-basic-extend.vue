<template>
  <div :id="formTabSaveBasicExtendId" style="width: 100%">
    <div style="display: flex;">
      <div v-show="isShowConifcide" style="padding-bottom: 5px">{{extendTitle}}</div>
      <component
        ref="formTabSaveBasicExtendFunc"
        :is="formTabSaveBasicExtendFuncId"
        :keyId="formTabSaveBasicExtendFuncKeyId"/>
    </div>
    <form-tab-save-列表编辑 ref="formTabSaveForm" v-show="isShowConifcide" isBasicExtend/>
  </div>
</template>

<script>
import ElementArea from '../element-area'
export default {
  name: 'form-tab-save-basic-extend',
  components: { ElementArea },
  data() {
    return {
      currentYear: '',
      params: undefined,
      formTabSaveBasicExtendId: 'formTabSaveBasicExtendId' + new Date().getTime(),
      extendTitle: '',
      template: undefined,
      formTabSaveBasicExtendFuncId: '',
      formTabSaveBasicExtendFuncKeyId:
        'formTabSaveBasicExtendFuncKey' + new Date().getTime(),
      isShowConifcide: true
    }
  },
  methods: {
    init(params) {
      this.params = params
      this.extendTitle = params.template.name
      this.template = params.template
      this.isShowConifcide = params.dataVo?.extData?.isShowConifcide
      var doShowTabContentIfTemplate = () => {
        this.$refs.formTabSaveForm.showTabContentIfTemplate(params)
      }
      // 存在模板的方法组件，则使用组件的init方法实现根据模板类型进行定制处理
      // 比如1级项目预算模板设置“更换预算类型”按钮，以及一些金额计算控制
      var funcExtendName = `form-tab-save-basic-extend-func-${params.template.templateType}`
      if (this.$isNotEmpty(window.$viewNames[funcExtendName])) {
        this.formTabSaveBasicExtendFuncId = funcExtendName
        this.$nextTick(() => {
          // 确保先执行模板扩展组件的init方法
          if (this.$refs.formTabSaveBasicExtendFunc.init) {
            this.$refs.formTabSaveBasicExtendFunc.init(this, params)
            this.$refs.formTabSaveBasicExtendFunc.secondInit(this, params)
          }
          this.$nextTick(() => { doShowTabContentIfTemplate() })
        })
      } else {
        doShowTabContentIfTemplate()
      }
    },
    fillDataVoBeforeSave(dataVo) {
      this.$refs.formTabSaveForm.fillDataVoBeforeSave(dataVo)
    },
    updateRowsData(updatayear) {
      this.$refs.formTabSaveForm.updateRowsData(updatayear)
    },
    addRow(bt) {
      this.$refs.formTabSaveForm.addRow(bt)
    },
    // 可以添加行数据
    addRowsData(row) {
      this.$refs.formTabSaveForm.addRowsData(row)
    },
    // 获取表格数据
    getRowsData() {
      return this.$refs.formTabSaveForm.getRowsData()
    },
    deleteTableDataByField(id) {
      this.$refs.formTabSaveForm.deleteTableDataByField(id)
    },
    clearRow() {
      this.$refs.formTabSaveForm.clearRow()
    },
    showError(result) {
      this.$refs.formTabSaveForm.showError(result)
    },
    resetTemplate(template, bizzId) { // 重新装填模板数据
      this.extendTitle = template.name
      var resetTemplateNew = (template) => {
        // 更换模板时 重新执行模板扩展组件的init方法
        this.params.template = template
        this.params.template.name = template.name
        this.init(this.params)
      }
      this.$refs.formTabSaveForm.resetTemplate(template, bizzId, resetTemplateNew)
    }
  }
}
</script>
<style lang="scss" scoped>
.project-form {
  /deep/.el-form-item {
    margin-bottom: 10px;
  }
}
</style>

