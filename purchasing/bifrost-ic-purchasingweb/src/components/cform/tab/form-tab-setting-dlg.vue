<template>
    <el-dialog ref="formTabSaveDlg"
        append-to-body :title="title"
        :visible.sync="formTabSaveDlgVisible"
        :close-on-click-modal='false'
        @close="onDlgClose">
        <div class="formTabSaveDlg">
            <b-page ref="basePageFormTabSaveDlg">
            <template #mainContent>
              <el-table ref="formTabSaveDlgTable" border :data="tabVo.tabs"
                  @selection-change="handleSelectionChange"
                  style="width: 100%;height: 100%;">
                <el-table-column type="selection" align="center" width="25"/>
                <el-table-column label="页签标题">
                  <template slot-scope='{row}'>
                    <el-input style="width: 100%" v-model="row.tabName"/>
                  </template>
                </el-table-column>
                <el-table-column :label="tabEnabledText" width="80">
                  <template slot-scope='{row}'>
                    <div style="flex: 1;width: 100%;display: flex;">
                      <el-radio v-model="row.isEnabled" :label="true" :disabled="!row.isEnabledRadioEnabled"
                                @input="exColRadioChangeds(true, row.tabType, row.tabName, tabVo)">是</el-radio>
                      <el-radio v-model="row.isEnabled" :label="false" :disabled="!row.isEnabledRadioEnabled"
                                @input="exColRadioChangeds(true, row.tabType, row.tabName, tabVo)">否</el-radio>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column v-if="exColDataEnabled !== undefined"
                   :label="exColDataEnabled.label" :width="exColDataEnabled.width"
                   :align="exColDataEnabled.align"
                   header-align="center" show-overflow-tooltip>
                  <template slot-scope='{row}'>
                    <div style="flex: 1;width: 100%;display: flex;">
                      <el-radio v-model="row[exColDataEnabled.prop]" :label="true"
                                @input="exColRadioChanged(true, row.tabType, row.tabName, tabVo)">是</el-radio>
                      <el-radio v-model="row[exColDataEnabled.prop]" :label="false"
                                @input="exColRadioChanged(false, row.tabType, row.tabName, tabVo)">否</el-radio>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="页签类型" width="150">
                  <template slot-scope='{row}'>
                    <el-select v-model="row.tabType"
                         @change="tabTypeChanged(row)"
                         placeholder="请选择"
                         v-if="row.tabType !== '基本信息'">
                      <el-option
                        v-for="(item, index) in tabTypes"
                        :key="index"
                        :label="item"
                        :value="item"/>
                    </el-select>
                    <span v-else style="font-size: 14px;">{{row.tabType}}</span>
                  </template>
                </el-table-column>

                <el-table-column label="其他" :width="180" show-overflow-tooltip>
                  <template slot-scope='{row}'>
                    <el-select v-model="row['refData1']"
                      :placeholder="exColDataOther.placeholder"
                      @visible-change="colOtherSelectClick"
                      clearable
                      v-if="row.tabType !== '基本信息' && exColDataOther !== undefined && row.isShowExColDataOtherContent === true">
                      <el-option
                        v-for="(dropItem, index) in exColDataOther.selectDrop"
                        :label="dropItem.label"
                        :value="dropItem.value"
                        :key="index"/>
                    </el-select>

                    <el-select v-model="row['refData1']"
                         placeholder="请设置扩展数据"
                         v-if="row.tabType === '基本信息'" clearable>
                        <el-option
                          v-for="(dropItem, index) in tabBasicExtendData"
                          :label="dropItem.label"
                          :value="dropItem.value"
                          :key="index"/>
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </b-page>
            <div slot="footer" class="footer formTabSaveDlgFooter">
                <el-button class="btn-normal" ref="btCancel" @click="formTabSaveDlgVisible = false">取消</el-button>
                <el-button class="btn-normal" ref="btOK" type="primary" @click="ok">{{btOkText}}</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script>
export default {
  name: 'form-tab-setting-dlg',
  data() {
    return {
      title: '页签设置',
      btOkTextRaw: '保存',
      btOkText: '',
      formTabSaveDlgVisible: false,
      tabEnabledText: '启用',
      callbackOK: undefined,
      exHandleNewTab: undefined,
      formId: '',
      exColDataEnabled: undefined, // 启用功能的扩展列，比如预算启用
      exColDataOther: undefined, // 其他功能的扩展列，比如预算启用
      handleTabTypeChanged: undefined,
      exColDataOtherSelectClick: undefined,
      handleExColRadioChanged: undefined,
      isEnabledColRadioChanged: undefined,
      tabTypes: [],
      tabVo: { tabs: [] },
      exParamsFormTabSave: undefined,
      tabBasicExtendData: undefined
    }
  },
  methods: {
    show(tabVo, width, height, callbackOK, exParams) {
      if (this.$isEmpty(tabVo)) {
        this.$message.error('tabVo不能为空')
        return
      }

      this.tabVo = this.$clone(tabVo)
      this.tabVo.tabs.forEach(tab => {
        if (tab.tabType === '基本信息') {
          // 基本信息必须开启“项目启用”
          tab.isEnabled = true
          tab.isEnabledRadioEnabled = false
        }

        if (this.exColDataOther != null &&
          this.$isNotEmpty(tab[this.exColDataOther.prop])) {
          tab[this.exColDataOther.prop] = tab[this.exColDataOther.prop] + ''
        }
      })

      this.btOkText = this.btOkTextRaw
      this.callbackOK = callbackOK
      if (exParams) {
        this.exHandleNewTab = exParams.exHandleNewTab
        this.exColDataOtherSelectClick = exParams.exColDataOtherSelectClick
        this.handleExColRadioChanged = exParams.handleExColRadioChanged
        this.isEnabledColRadioChanged = exParams.isEnabledColRadioChanged
        this.exParamsFormTabSave = exParams.exParamsFormTabSave
        if (exParams.exHandleSaveDlgData) {
          // 额外设置当前弹框数据，比如增加预算启用列等
          exParams.exHandleSaveDlgData(this)
        }
      }

      this.formTabSaveDlgVisible = true
      this.$setDlgSize(this, 'formTabSaveDlg', width, height)
      this.$nextTick(() => {
        this.setLoading(false)

        this.$refs.basePageFormTabSaveDlg.init({
          buttons: [
            { text: '新增', icon: 'el-icon-plus', enabledType: '0', click: (bt) => { this.addTab() } },
            { text: '删除', icon: 'el-icon-delete', enabledType: '1+', click: (bt) => { this.removeTab() } },
            { text: '上移', icon: 'el-icon-top', enabledType: '1', click: bt => { this.rowUpOrDown(true) } },
            { text: '下移', icon: 'el-icon-bottom', enabledType: '1', click: bt => { this.rowUpOrDown(false) } }
          ]
        })
        this.$refs.basePageFormTabSaveDlg.setButtonNormalNoPaddingTop(true)

        // 解决类似问题：初始化时页签类别是预算基本信息，
        // 则其他页签同步显示选择预算表单的下拉列表
        this.tabVo.tabs.forEach(tab => this.tabTypeChanged(tab))

        // 解决类似问题：预算基本信息页签中，其他列的值是下拉列表，
        // 需要从远程获取列表，需要在点击select后才触发，所以这里
        // 收到触发一次使得自动填充，否则会出现下拉框中显示的是表
        // 单的id而不是表单的名称
        this.colOtherSelectClick(true)
        this.colOtherSelectClickBasicExtendData()
      })
    },
    tabTypeChanged(row) {
      if (this.handleTabTypeChanged) {
        this.handleTabTypeChanged(row)
      }
    },
    exColRadioChanged(value, tabType, tabName, tabVo) {
      if (this.handleExColRadioChanged) {
        this.handleExColRadioChanged(value, tabType, tabName, tabVo)
      }
    },
    exColRadioChangeds(value, tabType, tabName, tabVo) {
      if (this.isEnabledColRadioChanged) {
        this.isEnabledColRadioChanged(value, tabType, tabName, tabVo)
      }
    },
    colOtherSelectClickBasicExtendData() {
      // 单击基本信息页签的其他列时，加载数据模板列表
      if (this.tabBasicExtendData === undefined) {
        this.tabBasicExtendData = []
        this.$callApiParams('selectRowFormTemplates',
          {}, result => {
            var rowFormTemplates = result.data
            rowFormTemplates.forEach(template => {
              this.tabBasicExtendData.push({ label: template.name, value: template.id })
            })

            // 加载数据模板列表重新触发双向数据绑定，这样基本信息的
            // 其他列才能显示的是绑定的数据模板名称，而不是模板id
            this.$nextTick(() => {
              this.tabVo.tabs.forEach(tab => {
                if (tab.tabType === '基本信息') {
                  var refData1Before = tab.refData1
                  tab.refData1 = ''
                  this.$nextTick(() => { tab.refData1 = refData1Before })
                }
              })
            })
            return true
          })
      }
    },
    rowUpOrDown(isUp) {
      var checkedRowIds = this.$getTableCheckedIds(this.$refs.formTabSaveDlgTable)
      var bt = {
        getRowId: () => checkedRowIds[0],
        params: { baseListObj: this.$refs.basePageFormTabSaveDlg }
      }
      this.$rowUpOrDown(bt, isUp, this.tabVo.tabs)
      this.$nextTick(() => this.handleSelectionChange())
    },
    addTab() {
      var tab = {
        id: new Date().getTime(),
        tabName: '',
        isEnabled: true,
        isEnabledRadioEnabled: true,
        formId: this.formId,
        tabType: '列表编辑' }
      if (this.exHandleNewTab) {
        this.exHandleNewTab(tab)
      }
      this.tabVo.tabs.push(tab)
    },
    removeTab() {
      var checkedRowIds = this.$getTableCheckedIds(this.$refs.formTabSaveDlgTable)
      var tabsLeft = []
      var containsBasicTab = false
      this.tabVo.tabs.forEach(tab => {
        if (checkedRowIds.indexOf(tab.id) < 0) {
          if (!containsBasicTab && tab.tabType === '基本信息') {
            containsBasicTab = true
          }
          tabsLeft.push(tab)
        }
      })

      if (!containsBasicTab) {
        this.$message.error('基本信息页签不能删除')
        return
      }
      this.tabVo.tabs = tabsLeft
    },
    handleSelectionChange() {
      var checkedRows = this.$getTableSelection(this.$refs.formTabSaveDlgTable)
      var btDeleteDisabled = this.$isEmpty(checkedRows)
      this.$refs.basePageFormTabSaveDlg.setBtProperty(
        '删除', 'disabled', btDeleteDisabled)
      this.refreshUpDownBtDisabled()
    },
    colOtherSelectClick(isVisible) {
      if (isVisible && this.exColDataOtherSelectClick) {
        this.exColDataOtherSelectClick(this)
      }
    },
    refreshUpDownBtDisabled() {
      // 上移和下移先都设置不可用
      var checkedRows = this.$getTableSelection(this.$refs.formTabSaveDlgTable)
      this.$refs.basePageFormTabSaveDlg.setBtProperty('上移', 'disabled', true)
      this.$refs.basePageFormTabSaveDlg.setBtProperty('下移', 'disabled', true)
      this.$rowCheckedCallback4RowUpOrDown(
        checkedRows, this.$refs.basePageFormTabSaveDlg, this.tabVo.tabs)
    },
    onDlgClose() {
    },
    ok() {
      this.setLoading(true)

      // 保存tabVo之前先清空每个tab中临时变量的rowFormTemplate
      // 否则会因为getVo的bean校验机制导致报校验错误
      this.tabVo.tabs.forEach(tab => {
        tab.rowFormTemplate = null
      })

      this.$nextTick(() => {
        this.$callApi('saveCformTabs',
          this.tabVo, result => {
            this.setLoading(false)
            this.closeDlg()
            if (this.callbackOK) {
              this.callbackOK(this.exParamsFormTabSave)
            }
          }, result => { this.setLoading(false) })
      })
    },
    setLoading(isLoading) {
      this.$refs.btOK.loading = isLoading
      this.$refs.btCancel.disabled = isLoading
      this.btOkText = isLoading ? '执行中...' : this.btOkTextRaw
    },
    closeDlg() {
      this.formTabSaveDlgVisible = false
      this.setLoading(false)
    }
  }
}
</script>

<style lang="scss">
.formTabSaveDlg { height: calc(100% - 62px) }
.formTabSaveDlgFooter { text-align: right; padding-top: 32px; }
</style>
