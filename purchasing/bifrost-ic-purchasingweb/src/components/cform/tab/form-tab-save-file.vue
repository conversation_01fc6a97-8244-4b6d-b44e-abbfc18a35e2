<template>
  <div class="formTabSaveFile" style="height: 100%">
    <attach-tab ref="attachAudiExtend"
        :isBlueTable="true" v-if="!isDetailOrAudit"/>
    <attach-audit-extend ref="attachAudiExtendDetail" v-if="isDetailOrAudit"/>
  </div>
</template>

<script>

export default {
  name: 'form-tab-save-文件依据',
  data() {
    return {
      isDetailOrAudit: false,
      nodeName: ''
    }
  },
  methods: {
    showTabContent(params) {
      if (params.dataVo) {
        this.isDetailOrAudit = params.isDetailOrAudit
        this.$nextTick(() => {
          if (this.isDetailOrAudit) {
            params.dataVo.extData.setFileTabLabel = (fileCount) => {
              if (params.fileTabName && params.setTabLabel) {
                params.setTabLabel(params.fileTabName,
                  `${params.fileTabName}(${fileCount})`)
              }
            }

            this.$refs.attachAudiExtendDetail.isBlueTable = true
            if (params.isAudit) {
              this.$refs.attachAudiExtendDetail.init(
                params.dataVo, this.nodeName, true)
            } else {
              this.$refs.attachAudiExtendDetail.initFormDetail(params.dataVo)
            }
          } else {
            this.$refs.attachAudiExtend.isBlueTable = true
            this.$refs.attachAudiExtend.initAssembly(params.dataVo)
          }
        })
      }
    },
    fillDataVoBeforeSave(dataVo) {
      if (!this.isDetailOrAudit) {
        this.$refs.attachAudiExtend.fillDataVoBeforeSave(dataVo)
      }
    },
    setNodeNameWhenAudit(nodeName) {
      this.nodeName = nodeName
      if (this.attachAudiExtendDetail) {
        this.$refs.attachAudiExtendDetail.nodeName = nodeName
      }
    }
  }
}
</script>

<style lang="scss">

</style>
