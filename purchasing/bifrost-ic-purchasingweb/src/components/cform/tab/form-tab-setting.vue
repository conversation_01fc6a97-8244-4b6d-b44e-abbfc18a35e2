<template>
  <div style="height: 100%">
    <page ref="page">
      <template #pageContent>
        <LayoutTem isDbCol :isPageShow="false" :isFilterShow="false">
          <template #dbColLeft>
            <div style="height: 100%;padding: 12px;">
              <b-page ref="basePageLeftTree">
                <template #mainContent>
                  <sup-tree :setting="setting"
                            ref="supTree"
                            :nodes="treeData"
                            :is-popover="false"
                            :edit-enable="true"/>
                </template>
              </b-page>
            </div>
          </template>
          <template #main>
            <div class="cformTabMainContent">
              <b-page ref="basePageCformTabMainContent">
                <template #mainContent>
                    <form-tab-save ref="formTabSave" @tab-click="tabClick"/>
                </template>
              </b-page>
            </div>
          </template>
        </LayoutTem>
      </template>
    </page>

    <form-tab-setting-dlg ref="tabSaveDlg"/>
    <row-form-setting ref="rowFormSetting"/>
    <cform-rule-dialog ref="cformRuleDialog"/>
  </div>
</template>

<script>

import FormTabSettingDlg from './form-tab-setting-dlg'
import FormTabSave from './form-tab-save'
import RowFormSetting from '../../tablecolumn/row-form-setting'
import CformRuleDialog from '../rule/cform-rule-dialog'
export default {
  name: 'form-tab-setting',
  components: { RowFormSetting, FormTabSave, FormTabSettingDlg, CformRuleDialog },
  data() {
    return {
      isDialog: false,
      isDetails: false,
      tabType: '',
      treeData: [],
      activedTab: '',
      currentFormId: '',
      currentTabName: '',
      firstTabName: '',
      currentTabIndex: '',
      tabVo: { tabs: [] }, // 设计包含的所有tab数据
      exHandleSaveDlgData: undefined,
      exHandleNewTab: undefined,
      exHandleAfterTabRefresh: undefined,
      exButtons: [], // 主内容区域额外的按钮
      exParamsFormTabSave: undefined,
      exColDataOtherSelectClick: undefined,
      handleExColRadioChanged: undefined,
      isEnabledColRadioChanged: undefined,
      setting: {
        callback: {
          onClick: this.treeNodeClick
        }
      }
    }
  },
  methods: {
    init(exParams) {
      this.exParamsFormTabSave = exParams
      this.$refs.basePageLeftTree.init({
        buttons: [
          { text: '设置表单页签', enabledType: '1',
            click: (bt) => { this.showTabSaveDlg() } },
          { text: '前往表单设计器', enabledType: '0', click: (bt) => {} }
        ]
      })

      var buttons = [
        { text: '设置页签内容', icon: 'el-icon-tickets', enabledType: '0', click: (bt) => {
          this.showFormTabRowEditSetting()
        } },
        { text: '权限控制', icon: 'el-icon-lock', enabledType: '0', click: (bt) => {
          this.setCformAuthRight()
        } }
      ]

      this.exButtons.forEach(bt => { buttons.push(bt) })
      this.$refs.basePageCformTabMainContent.init({ buttons: buttons })

      this.$nextTick(() => {
        this.$refs.page.commonPageClassEx =
          this.$refs.page.commonPageClassEx + ' column-top-hide'
        this.$refs.basePageLeftTree.setButtonNormalNoPaddingTop(true)
        this.$refs.basePageCformTabMainContent.setButtonNormalNoPaddingTop(true)
        this.$refs.formTabSave.isEdit = true

        this.initTreeData()
      })
    },
    setMainContentBtProperty(btText, property, value) {
      this.$refs.basePageCformTabMainContent.setBtProperty(btText, property, value)
    },
    setCformAuthRight() {
      var tab = this.$refs.formTabSave.getTab
      var tabName = tab.tabName
      var tabId = tab.id
      var versionId = this.$refs.formTabSave.tabInitParams.versionId
      this.$refs.cformRuleDialog.showFormRuleDialog(this.currentFormId,
        tabName, tabId, versionId)
    },
    initTreeData() {
      if (this.$isNotEmpty(this.tabType)) {
        this.$callApiParams('selectCformTabTree',
          { tabType: this.tabType }, result => {
            this.treeData = result.data
            this.$nextTick(() => {
              // 点击第一个叶子节点
              for (let i = 0; i < this.treeData.length; i++) {
                if (this.$isNotEmpty(this.treeData[i].itemKey)) {
                  // 模拟点击节点加载数据
                  this.treeNodeClick(null, null, this.treeData[i])
                  this.$nextTick(() => {
                    // 高亮选中这个树节点
                    this.$refs.supTree.selectAndCheckNode(this.treeData[i].id)
                  })
                  break
                }
              }
            })
            return true
          })
      }
    },
    showTabSaveDlg() {
      var exParams = {
        exHandleSaveDlgData: this.exHandleSaveDlgData,
        exHandleNewTab: this.exHandleNewTab,
        exColDataOtherSelectClick: this.exColDataOtherSelectClick,
        handleExColRadioChanged: this.handleExColRadioChanged,
        isEnabledColRadioChanged: this.isEnabledColRadioChanged,
        exParamsFormTabSave: this.exParamsFormTabSave
      }
      this.$refs.tabSaveDlg.show(this.tabVo, 800, 600, this.refreshTabs, exParams)
    },
    treeNodeClick(event, treeId, treeNode) {
      var rows = this.$isNotEmpty(treeNode.itemKey) ? ['1'] : []
      this.$refs.basePageLeftTree.rowChecked(rows, false)
      if (rows.length > 0 && this.currentFormId !== treeNode.itemKey) {
        this.currentFormId = treeNode.itemKey
        this.$refs.tabSaveDlg.formId = this.currentFormId
        this.refreshTabs(this.exParamsFormTabSave)
      }
    },
    refreshTabs(exParams) {
      if (exParams && !exParams.dontResetLocalParams) {
        this.exParamsFormTabSave = exParams
      }

      var setTabSaveBtLoading = (isLoading) => {
        this.$refs.basePageLeftTree.setBtProperty('设置表单页签', 'loading', isLoading)
      }
      var cbBeforeApi = () => { setTabSaveBtLoading(true) }
      var cbFailed = () => { setTabSaveBtLoading(false) }
      var cbSuccess = (result, tabVo, formTabSave) => {
        if (this.exHandleAfterTabRefresh) {
          this.exHandleAfterTabRefresh(result, tabVo, formTabSave)
          this.tabVo = tabVo
        }
        setTabSaveBtLoading(false)
      }

      this.$refs.formTabSave.refreshTabs(
        this.currentFormId, cbBeforeApi, cbSuccess, cbFailed, exParams)
    },
    tabClick(tab, tabCompnent) {
      // 设置按钮“设置页签内容”是否可用
      this.currentTabName = tab.name
      this.currentTabIndex = tab.index
      if (tab.index === 0) {
        this.firstTabName = tab.name
      }
      var isBtSetTabContentEnabled =
        tabCompnent ? tabCompnent.isBtSetTabContentEnabled : false
      this.setMainContentBtProperty(
        '设置页签内容', 'disabled', !isBtSetTabContentEnabled)
    },
    showFormTabRowEditSetting() {
      this.$refs.rowFormSetting.show({
        formId: this.currentFormId,
        tabName: this.currentTabName,
        refreshAfterSaved: () => {
          var exParams = this.$cloneDeep(this.exParamsFormTabSave)
          exParams.dontResetLocalParams = '占位符'
          exParams.showThisTabAfterRefresh = {
            name: this.currentTabName,
            index: this.currentTabIndex
          }
          this.refreshTabs(exParams)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cformTabMainContent {
  width: 100%;
  height: 100%;
  border: 1px solid #DDDDDD;
  padding: 10px;
}
</style>
