<template>
  <div :class="`level3RichTextBlock ${errorClass}`">
    <el-input
      type="textarea"
      resize="none"
      :disabled="disabled"
      :readonly="readonly"
      placeholder="请输入内容"
      v-model="richText"
      :maxlength="maxlength"
      style="height: calc(100% - 10px);"
      show-word-limit>
    </el-input>
    <span class="errorSpan">{{errorMessage}}</span>
  </div>
</template>

<script>
export default {
  name: 'form-tab-save-三级明细富文本',
  data() {
    return {
      maxlength: 5000,
      readonly: false,
      tabData: undefined, // 如果当前组件被放在页签中，这个字段存储对应的tab
      parentRowId: undefined,
      richText: '',
      richData: {},
      parentBaseListObj: undefined,
      errorMap: {}, // key = rowId, value = error
      errorMessage: ''
    }
  },
  watch: {
    parentRowId(val, oldVal) {
      this.$nextTick(() => {
        this.recordValues(oldVal)
        this.resetValues(val)
        this.showError(undefined, val)
      })
    }
  },
  computed: {
    disabled() {
      return this.$isEmpty(this.parentRowId)
    },
    errorClass() {
      return this.$isEmpty(this.errorMessage) ? '' : 'level3RichTextBlockHasError'
    }
  },
  methods: {
    showTabContent(params) {
      if (this.tabData) {
        if (this.tabData.columnEntity &&
            this.tabData.columnEntity.max !== undefined) {
          this.maxlength = parseInt(this.tabData.columnEntity.max)
        }

        this.readonly = !this.tabData.params.isEdit
        const subProCalculteDisabled = params.dataVo.extData.subProCalculteDisabled
        if (subProCalculteDisabled) {
          if (!this.readonly) {
            this.readonly = true
          }
        }
      }
      this.switchRowIndex2RowId(params)
    },
    clearData() {
      this.richText = ''
      this.richData = {}
    },
    recordValues(rowId) {
      if (rowId !== undefined) {
        this.richData[rowId] = this.richText
      }
    },
    resetValues(rowId) {
      rowId = rowId || this.parentRowId
      if (this.richData[rowId] !== undefined) {
        this.richText = this.richData[rowId]
      } else {
        this.richText = ''
      }
    },
    switchRowIndex2RowId(params) {
      var dataVo = params.dataVo
      var rowsData = params.rowsDataInit
      var dataKey = this.tabData.params.formIdTabName + '_三级明细富文本'
      if (dataVo &&
        this.$isNotEmpty(dataVo.extData[dataKey]) &&
        this.$isNotEmpty(rowsData)) {
        var dataL2 = dataVo.extData[dataKey]
        var dataL3 = dataL2[this.tabData.tabName] || {}
        // this.richData = {}
        for (let i = 0; i < rowsData.length; i++) {
          var indexStr = i + ''
          var rowId = this.$getRowId(rowsData[i])
          if (this.$isNotEmpty(dataL3[indexStr])) {
            this.richData[rowId] = dataL3[indexStr]
          }
        }
      }
    },
    fillDataVoBeforeSave(dataVo, funcAddPageData, parentBaseListObj) {
      this.errorMap = {}
      this.parentBaseListObj = parentBaseListObj
      this.recordValues(this.parentRowId)

      if (this.$isNotEmpty(this.richData)) {
        var dataL3 = {}
        var rowsData = parentBaseListObj.rowsData
        for (let i = 0; i < rowsData.length; i++) {
          var indexStr = i + ''
          var rowId = this.$getRowId(rowsData[i])
          if (this.$isNotEmpty(this.richData[rowId])) {
            dataL3[indexStr] = this.richData[rowId]
          }
        }

        var l2Key = this.tabData.params.formIdTabName + '_三级明细富文本'
        if (dataVo.extData[l2Key] === undefined) {
          dataVo.extData[l2Key] = {}
        }
        var dataL2 = dataVo.extData[l2Key]
        dataL2[this.tabData.tabName] = dataL3
      }
    },
    showError(result, newParentRowId) {
      // 先将错误数据解析到errorMap，后续可以持续使用
      if (result !== undefined) {
        this.errorMap = {}

        var rowsData = this.parentBaseListObj.rowsData
        var indexToRowIdMap = {}
        for (let i = 0; i < rowsData.length; i++) {
          var rowId = this.$getRowId(rowsData[i])
          indexToRowIdMap[i + ''] = rowId
        }

        // 类似：formId_2级页签_3级页签 _2级当前行索引:0:0
        var errorKeyPrefix = this.tabData.params.formIdTabName +
          '_' + this.tabData.tabName + '_'
        var keys = Object.keys(result.attributes)
        keys.forEach(key => {
          if (key.indexOf(errorKeyPrefix) > -1) {
            var tokens = key.split(':')
            var keyPart1 = tokens[0]
            var indexStr = keyPart1.replace(errorKeyPrefix, '')
            var rowId = indexToRowIdMap[indexStr]
            if (this.$isNotEmpty(rowId)) {
              this.errorMap[rowId] = result.attributes[key]
            }
          }
        })
      }

      newParentRowId = newParentRowId || this.parentRowId
      this.errorMessage = this.errorMap[newParentRowId]
    }
  }
}
</script>
<style lang="scss">
  .level3RichTextBlock { height: 100%; position: relative; }
  .level3RichTextBlock .errorSpan {
    color: #F56C6C;position: absolute;top: 3px; right: 4px;
    font-size: 12px;height: 12px;line-height: 12px; }
  .level3RichTextBlock textarea { height: 100%; }
  .level3RichTextBlockHasError .el-textarea .el-textarea__inner { border: 1px solid #F56C6C !important; }
</style>
