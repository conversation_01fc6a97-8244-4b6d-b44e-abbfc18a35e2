<template>
  <div :id="formTabSaveFormId" style="height: 100%;display: flex;flex-direction: column;">
    <form-canvas ref="formCanvas"/>
    <div style="flex: 1;overflow: auto;">
      <form-tan-save-basic-year ref="formTanSaveBasicYear"></form-tan-save-basic-year>
      <slot name="extendContent">
        <keep-alive>
          <component
            ref="formTabSaveFormExtend"
            :is="formTabSaveFormExtendId"
            :keyId="formTabSaveFormExtendKeyId"/>
        </keep-alive>
      </slot>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'form-tab-save-form',
  data() {
    return {
      formTabSaveFormId: 'formTabSaveForm' + new Date().getTime(),
      formCanvasMode: undefined,
      formCanvasMeta: undefined,
      formTabSaveFormExtendId: '',
      formTabSaveFormExtendKeyId: ''
    }
  },
  methods: {
    initBasicInfo(params) {
      if (params.formCanvasMode === '详情') {
        this.$refs.formTanSaveBasicYear.disabled = true
      } else {
        this.$refs.formTanSaveBasicYear.disabled = false
      }
      this.currentYear = (Number(params.dataVo.extData.currentYear) + 1).toString()
      // 2024-6-13 暂时写死在这里
      if (params.dataVo.extData.accordingListMap) {
        this.$refs.formTanSaveBasicYear.accordingListMap = params.dataVo.extData.accordingListMap
        this.$refs.formTanSaveBasicYear.bugdetYear = this.currentYear
      }
    },
    showTabContent(params) {
      if (this.formCanvasMode) { // 只初始化一次
        return
      }
      this.setExtendUsingTemplateIf(params)

      params = params || {}
      this.formTabSaveFormExtendId = params.formTabSaveFormExtendId
      this.formCanvasMode = this.$isNotEmpty(params.formCanvasMode)
        ? params.formCanvasMode : '制单'

      params.getDataToSaveInTab = () => {
        // 获取表单填单数据
        var dataVo = this.$refs.formCanvas.getDataToSave()
        dataVo.extData.accordingListMap = this.$refs.formTanSaveBasicYear.accordingListMap

        // 如果存在扩展组件，填充扩展组件的填单数据
        if (this.$refs.formTabSaveFormExtend &&
          this.$refs.formTabSaveFormExtend.fillDataVoBeforeSave) {
          this.$refs.formTabSaveFormExtend.fillDataVoBeforeSave(dataVo)
        }
        return dataVo
      }
      params.getFormExtendInTab = () => {
        return this.$refs.formCanvas.getFormExtend()
      }
      params.getFormTabSaveFormExtend = () => {
        return this.$refs.formTabSaveFormExtend
      }
      var versionId = params.theVersionId
      var dataId = params.theDataId
      if (this.$isEmpty(versionId) && this.$isEmpty(dataId)) {
        this.$message.error('versionId和dataId不能同时为空')
        return
      }

      var callbackInitCanvas = (meta, dataVo) => {
        this.formCanvasMeta = meta
        if (this.$isNotEmpty(dataVo.extData.rowFormTemplate)) {
          params.template = dataVo.extData.rowFormTemplate
        }
      }
      var initFormExData = {}
      initFormExData.showHeader = false
      initFormExData.showEditButtons = false
      initFormExData.showHeaderFillMode = false
      initFormExData.useFormRegularCompact = true
      initFormExData.setStatusText = params.setStatusText
      initFormExData.formTabSaveFormExtend = (cformTabChangedObj) => {
        return this.$refs.formTabSaveFormExtend
      }
      initFormExData.formTabSave = params.formTabSave
      const initMetaBefore = initFormExData.initMetaBefore
      initFormExData.initMetaBefore = (dataVo) => {
        initMetaBefore?.(dataVo)
        params.initFormBefore?.(dataVo, this.formCanvasMeta)
      }
      initFormExData.callbackAfterFormLoaded = (dataVo) => { // 表单UI加载之后调用
        if (params.callbackAfterMainFormLoaded) {
          // 用于主表单初始化记录数据
          params.callbackAfterMainFormLoaded(dataVo, this.formCanvasMeta)

          // 在表单扩展组件里设置控制状态栏文本的方法
          var formExtend = this.$refs.formCanvas.getFormExtend()
          if (formExtend) {
            formExtend.setStatusText = params.setStatusText
            formExtend.getFormTabSaveForm = () => {
              return this
            }
            formExtend.formTabSaveFormExtend = (cformTabChangedObj) => {
              return this.$refs.formTabSaveFormExtend
            }
            formExtend.formTabSave = params.formTabSave
          }

          this.$nextTick(() => {
            var regularFormActualHeight = $('#' + this.formTabSaveFormId)
              .find('.formCommonCols.formRegularColbottomBorber')
              .height()
            if (parseInt(regularFormActualHeight) > 0) {
              $('#' + this.formTabSaveFormId)
                .find('.formCanvas')
                .height(parseInt(regularFormActualHeight) + 10)
            }

            if (this.formCanvasMode !== '制单') {
              $('#' + this.formTabSaveFormId +
                ' .formRegularDetailOrAuditMode .formCanvasTab .el-tabs__content')
                .css('cssText', 'height:100% !important')
            }

            // 表单加载后执行扩展组件的init方法
            if (this.$refs.formTabSaveFormExtend &&
                this.$refs.formTabSaveFormExtend.init) {
              this.$refs.formTabSaveFormExtend.init(params)
            }
          })
        }
        // 2024-6-13 预算新需求
        this.initBasicInfo(params)
      }

      // 由业务场景传递templateKey，传递给selectCfromVo，
      // 最终匹配到具体的模板扩展处理器，进行特别模板数据的
      // 的处理。目前的应用场景是：由form-tab-save-func-extend-bgpm
      // 传递templateKey=预算模板，使得查询表单数据时，如果新增1级项目预算填报模板时，
      // 对填充上一年度填写的当前年度的部门项目预算金额
      var queryParams = {}
      queryParams.templateKey = params.templateKey
      queryParams.formType = params.formType
      queryParams.isApply = params.isApply
      queryParams.stateItem = params.stateItem
      if (this.$isNotEmpty(params.exQueryParams)) {
        Object.assign(queryParams, params.exQueryParams)
      }
      this.$refs.formCanvas.initByVersionDataId(
        versionId,
        dataId,
        this.formCanvasMode,
        callbackInitCanvas,
        undefined,
        params.isEdit,
        undefined,
        initFormExData,
        queryParams
      )
    },
    showError(result) {
      var errorHitContainer = {}
      this.$refs.formCanvas.showError(result, undefined, errorHitContainer)
      if (this.$refs.formTabSaveFormExtend) {
        this.$refs.formTabSaveFormExtend.showError(result)
      }
      var errorCount = errorHitContainer['hitCountInt']
      return errorCount
    },
    setExtendUsingTemplateIf(params) {
      // 如果表单页签绑定了模板，这里设置页签扩展区域使用扩展组件
      if (params.tabEntity && params.tabEntity.rowFormTemplate) {
        params.template = params.tabEntity.rowFormTemplate
        params.formTabSaveFormExtendId = 'form-tab-save-basic-extend'
        params.formTabSaveFormExtendKeyId = 'template' + params.tabEntity.rowFormTemplate.id
        this.formTabSaveFormExtendId = ''
      }
    }
  }
}
</script>

<style lang="scss">
</style>
