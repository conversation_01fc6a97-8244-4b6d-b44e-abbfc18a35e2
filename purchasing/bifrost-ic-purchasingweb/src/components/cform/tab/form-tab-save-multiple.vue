<template>
  <div :class="`level3MultipleBlock ${errorClass} ${readonlyClass}`">
    <div v-for="(item, index) in allData" :key="index"
         :class="`level3MultipleItemBlock level3MultipleItemBlock${index}`"
         :level3MultipleItem="index">
      <el-transfer v-model="item.value"
              :data="item.items" :titles="item.titles"
              @change="bindItemDblClick"/>
      <span class="errorSpan">{{item.errorMessage}}</span>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'form-tab-save-三级明细多选项',
  data() {
    return {
      multipleData: {},
      readonly: false,
      tabData: undefined, // 如果当前组件被放在页签中，这个字段存储对应的tab
      parentRowId: undefined,
      parentBaseListObj: undefined,
      errorMap: {}, // key = rowId, value = error
      finalCols: undefined, // 最终多选项
      finalColsRef: undefined,
      allData: undefined,
      allDropList: undefined,
      subProLabelDisabled: false,
      hotClassfityDisabled: false,
      hotClassfityRequired: false,
      subProLabelRequired: false
    }
  },
  watch: {
    parentRowId(val, oldVal) {
      this.$nextTick(() => {
        this.recordValues(oldVal)
        this.resetValues(val)
        this.showError(undefined, val)
      })
    }
  },
  computed: {
    disabled() {
      return this.$isEmpty(this.parentRowId)
    },
    readonlyClass() {
      return this.readonly ? 'level3MultipleBlockReadonly' : ''
    },
    errorClass() {
      return this.$isEmpty(this.errorMessage) ? '' : 'level3MultipleBlockHasError'
    }
  },
  methods: {
    showTabContent(params) {
      this.finalCols = []
      this.finalColsRef = []
      this.allData = []

      if (this.tabData) {
        if (this.tabData.tableHeader &&
          this.tabData.tableHeader.exData !== undefined) {
          this.finalCols = this.tabData.tableHeader.exData['最终多选项']
        }
        this.readonly = !this.tabData.params.isEdit
        this.subProLabelDisabled = params.dataVo.extData.subProLabelDisabled
        this.hotClassfityDisabled = params.dataVo.extData.hotClassfityDisabled
        this.hotClassfityRequired = params.dataVo.extData.hotClassfityRequired
        this.subProLabelRequired = params.dataVo.extData.subProLabelRequired
      }
      this.switchRowIndex2RowId(params)
      this.finalCols.forEach(col => {
        this.finalColsRef.push(col.columnEntity.dataRef)
      })

      // 从服务器加载下拉列表，然后填充到界面
      this.selectListItems(() => {
        this.finalCols.forEach(col => {
          var dataRef = col.columnEntity.dataRef
          this.addOneMultiple(dataRef, col.prop, [])
        })
        this.bindItemDblClick()
        this.resetValues()
      })
    },
    hideIfReadonly() {
      if (this.readonly) {
        $('.level3MultipleBlock').find('.el-transfer__buttons').hide()
        $('.level3MultipleBlock').find('.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span').hide()
        var $itemBlock = $('.level3MultipleItemBlock')
        for (let i = 0; i < $itemBlock.length; i++) {
          var $panel = $($itemBlock[i]).find('.el-transfer-panel')
          $($panel[0]).hide()
        }
      }
    },
    recordValues(rowId) {
      if (rowId) {
        this.allData.forEach(data => {
          if (this.multipleData[data.prop] === undefined) {
            this.multipleData[data.prop] = {}
          }
          this.multipleData[data.prop][rowId] = data.value
        })
      }
    },
    resetValues(newParentRowId) {
      newParentRowId = newParentRowId || this.parentRowId
      this.allData.forEach(data => {
        var prop = data.prop
        var items = data.items
        items.forEach(it => {
          it.disabled = (newParentRowId === undefined)
          if (this.readonly) {
            it.disabled = true
          } else {
            if (prop === '热点分类') {
              if (this.hotClassfityDisabled) {
                it.disabled = true
              }
            }
            if (prop === '子项目标签') {
              if (this.subProLabelDisabled) {
                it.disabled = true
              }
            }
          }
        })
        data.items = []
        this.$nextTick(() => {
          data.items = items
          this.bindItemDblClick()
        })

        if (this.multipleData[data.prop] && newParentRowId) {
          var dataL3 = this.multipleData[data.prop]
          if (dataL3[newParentRowId] === undefined) {
            dataL3[newParentRowId] = []
          }
          data.value = dataL3[newParentRowId]
        }
      })

      this.$nextTick(() => {
        this.hideIfReadonly()

        // 删除穿梭框固定高度，置空“无数据”文本
        var $level3MultipleBlock = $('.level3MultipleBlock')
        $level3MultipleBlock.find('.el-transfer-panel__empty').text('')
        $level3MultipleBlock.find('.el-transfer').css('height', '100%')
        $level3MultipleBlock.find('.el-transfer-panel').css('height', '100%')
        $level3MultipleBlock.find('.el-transfer-panel__body').css('height', 'calc(100% - 38px)')
        $level3MultipleBlock.find('.el-transfer-panel__list').css('height', 'calc(100% - 0px)')
        $level3MultipleBlock.find('.el-transfer-panel__list').css('max-height', '200px')
      })
    },
    bindItemDblClick() {
      if (this.readonly) {
        return
      }

      this.$nextTick(() => {
        var this_ = this
        $('.el-transfer-panel__item').unbind()
        $('.el-transfer-panel__item').dblclick(function() {
          var label = $(this).find('.el-checkbox__label span').text()
          var $level3MultipleItemBlock = $(this).closest('.level3MultipleItemBlock')
          var itemIndex = $level3MultipleItemBlock.attr('level3MultipleItem')
          var itemValues = this_.allData[parseInt(itemIndex)].value
          var valueIndex = itemValues.indexOf(label)
          if (valueIndex > -1) {
            itemValues.splice(valueIndex, 1)
          } else {
            itemValues.push(label)
          }

          this_.$nextTick(() => { this_.bindItemDblClick() })
        })
      })
    },
    selectListItems(callbackWhenCompleted) {
      this.allDropList = {}
      if (this.$isNotEmpty(this.finalColsRef)) {
        this.finalColsRef.forEach(dataRef => {
          this.$callApiParams('refLabelValuesDynamic',
            { 'dataRef': dataRef },
            result => {
              this.allDropList[dataRef] = result.data
              var keys = Object.keys(this.allDropList)
              if (keys.length === this.finalColsRef.length &&
                  callbackWhenCompleted) {
                callbackWhenCompleted()
              }
              return true
            })
        })
      }
    },
    switchRowIndex2RowId(params) {
      var dataVo = params.dataVo
      var rowsData = params.rowsDataInit
      var dataKey = this.tabData.params.formIdTabName + '_三级明细多选项'
      if (dataVo &&
        this.$isNotEmpty(dataVo.extData[dataKey]) &&
        this.$isNotEmpty(rowsData)) {
        var dataL2 = dataVo.extData[dataKey]
        this.finalCols.forEach(col => {
          var prop = col.prop
          var dataL3 = dataL2[prop] || []
          var propDataL3 = {}
          for (let i = 0; i < rowsData.length; i++) {
            var indexStr = i + ''
            var rowId = this.$getRowId(rowsData[i])
            if (this.$isNotEmpty(dataL3[indexStr])) {
              propDataL3[rowId] = dataL3[indexStr]
              if (this.multipleData[prop] === undefined) {
                this.multipleData[prop] = propDataL3
              }
            }
          }
        })
      }
    },
    fillDataVoBeforeSave(dataVo, funcAddPageData, parentBaseListObj) {
      this.errorMap = {}
      this.parentBaseListObj = parentBaseListObj
      this.recordValues(this.parentRowId)

      if (this.$isNotEmpty(this.multipleData)) {
        var l2Key = this.tabData.params.formIdTabName + '_三级明细多选项'
        if (dataVo.extData[l2Key] === undefined) {
          dataVo.extData[l2Key] = {}
        }
        var dataL2 = dataVo.extData[l2Key]

        var rowsData = this.parentBaseListObj.rowsData
        var keys = Object.keys(this.multipleData)
        keys.forEach(key => { // 对于每一个多选项类别进行rowId替换rowIndex
          dataL2[key] = {}
          var dataL3 = this.multipleData[key]
          for (let i = 0; i < rowsData.length; i++) {
            var indexStr = i + ''
            var rowId = this.$getRowId(rowsData[i])
            if (this.$isNotEmpty(dataL3[rowId])) {
              dataL2[key][indexStr] = dataL3[rowId]
            }
          }
        })
      }
    },
    addOneMultiple(dataRef, level3Prop, value) {
      value = value || []
      var itemsRaw = this.allDropList[dataRef] || []
      var items = []
      itemsRaw.forEach(it => {
        var oneItem = {}
        oneItem.key = it.label
        oneItem.label = it.label
        items.push(oneItem)
      })

      var multipleItem = {
        prop: level3Prop,
        titles: ['选项列表', level3Prop],
        errorMessage: '',
        value: value,
        items: items }
      this.allData.push(multipleItem)
    },
    showError(result, newParentRowId) {
      // 先将错误数据解析到errorMap，后续可以持续使用
      if (result !== undefined) {
        this.errorMap = {}

        var rowsData = this.parentBaseListObj.rowsData
        var indexToRowIdMap = {}
        for (let i = 0; i < rowsData.length; i++) {
          var rowId = this.$getRowId(rowsData[i])
          indexToRowIdMap[i + ''] = rowId
        }

        // 类似：formId_2级页签_3级页签 _2级当前行索引:0:最终多选项prop
        var errorKeyPrefix = this.tabData.params.formIdTabName +
          '_' + this.tabData.tabName + '_'
        var keys = Object.keys(result.attributes)
        keys.forEach(key => {
          if (key.indexOf(errorKeyPrefix) > -1) {
            var tokens = key.split(':')
            var keyPart1 = tokens[0]
            var indexStr = keyPart1.replace(errorKeyPrefix, '')
            var rowId = indexToRowIdMap[indexStr]
            if (this.$isNotEmpty(rowId)) {
              var finalProp = tokens[2]
              if (this.$isNotEmpty(finalProp)) {
                if (this.errorMap[finalProp] === undefined) {
                  this.errorMap[finalProp] = {}
                }
                this.errorMap[finalProp][rowId] = result.attributes[key]
              }
            }
          }
        })
      }

      newParentRowId = newParentRowId || this.parentRowId
      if (newParentRowId) {
        var index = 0
        this.allData.forEach(data => {
          var $panels = $('.level3MultipleItemBlock' + index).find('.el-transfer-panel')
          $($panels[1]).removeClass('level3MultipleBlockHasError')

          var errorMsg = ''
          if (this.errorMap[data.prop] &&
            this.$isNotEmpty(this.errorMap[data.prop][newParentRowId])) {
            errorMsg = this.errorMap[data.prop][newParentRowId]
            $($panels[1]).addClass('level3MultipleBlockHasError') // 加红框
          }
          index++
          data.errorMessage = errorMsg
        })
      }
    }
  }
}
</script>
<style lang="scss">
  .level3MultipleBlock {
    height: calc(100% - 3px); position: relative; display: flex;
    flex-direction: row; border: 1px solid #98bfff; padding: 10px; }
  .level3MultipleBlock .errorSpan {
    color: #F56C6C;position: absolute;bottom: 5px; right: 4px;
    font-size: 12px;height: 12px;line-height: 12px; }
  .level3MultipleItemBlock { height: 100%; position: relative;  }
  .level3MultipleItemBlock:not(:last-child) { margin-right: 10px; }
  .level3MultipleItemBlock .el-transfer-panel { border: 1px solid #98bfff; }
  .level3MultipleItemBlock .el-transfer-panel .el-transfer-panel__header { border-bottom: 1px solid #98bfff; }
  .level3MultipleItemBlock .el-transfer__buttons { padding: 0px 5px; }
  .level3MultipleItemBlock .el-button--small { padding: 3px 5px;border-radius: 2px; }
  .level3MultipleItemBlock .el-transfer-panel .el-transfer-panel__header { background: #dbeeff !important; }
  .level3MultipleItemBlock .el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label { font-size: 14px; }

  .level3MultipleItemBlock .el-transfer-panel.level3MultipleBlockHasError { border: 1px solid #F56C6C !important; }
  .level3MultipleBlock.level3MultipleBlockReadonly .el-checkbox__input.is-disabled+span.el-checkbox__label {color: #606266 !important;}
</style>

