<template>
  <div class="cformTabSave formCanvasRuntime" id = "cformTabSave">
    <div :style="`overflow: hidden;display: flex;flex-direction:column;height: 100%;width: 100%`">
      <slot name="headExtendContent" />
      <div style="overflow: hidden;flex: 1;">
        <el-tabs v-model="activedTab" class="cformTabSave-tabs" :class="{'no-tabitem': !tabs.length}" ref="tabs" :type="tabsType" @tab-click="tabClick">
          <el-tab-pane
            v-for="(tab, index) in tabs"
            :key="index"
            :label="`${isShowButtons ? '' : `${(index+1)}.`}${tab.tabLabel}`"
            :disabled="disableTab(tab)"
            :name="tab.tabName">
              <component :is="`form-tab-save-${tab.tabType}`" ref="tabContents"/>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div style="width:100%;margin-top:10px"
           v-show="!isDetailOrAudit">
        <div style="float: left;">
          <div style="height: 10px; width: 100px;"/>
          <div :style="`font-size: 14px;margin-left: 10px;height: 20px; line-height: 20px;${statusTextStyle}`">{{statusText}}</div>
        </div>
        <div style="float: right;">
          <el-button size="small" plain icon="el-icon-d-arrow-left"
                     @click="doBtEditBack" :disabled="isBtDisabled">返回</el-button>
          <el-button size="small" plain icon="el-icon-arrow-left"
                     @click="tabBackNext(--tabIndex)" v-if="isBackBut && !isShowButtons">上一步</el-button>
          <el-button size="small" plain icon="el-icon-arrow-right"
                     @click="tabBackNext(++tabIndex)" v-if="isNextBut && !isShowButtons">下一步</el-button>
          <el-button ref="btDraft" plain size="small" icon="el-icon-tickets" :loading="draftLoading"
                     @click="doBtEditDraft" :disabled="isBtDisabled" v-show="isDraftBut && !isShowButtons">存草稿</el-button>
          <el-button size="small" plain type="primary" icon="el-icon-edit"
                     @click="doBtEditSave" :disabled="isBtDisabled" v-show="isSaveBut || isShowButtons">保存</el-button>
        </div>
      </div>
      <div :style="`font-size: 14px;line-height: 14px;margin-top:10px;${statusTextStyle}`"
        v-show="isDetailOrAudit">{{statusText}}</div>
    </div>
    <component ref="formTabSaveExtend" :is="extendHandler" :disabledTabs="disabledTabs"/>
  </div>
</template>

<script>
import $ from 'jquery'
import { getTabsType } from '@/utils'
export default {
  name: 'form-tab-save',
  props: {
    disabledTabs: {
      type: Array,
      default: () => []
    },
    isShowButtons: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEdit: true, // 是否处于编辑状态
      tabs: [],
      activedTab: '',
      statusText: '',
      statusTextStyle: '',
      tabInitParams: {},
      btEditBack: undefined,
      btEditDraft: undefined,
      btEditSave: undefined,
      isDetailOrAudit: false, // 当前是否是详情弹框
      extendHandler: '',
      mainFormTabIndex: undefined,
      metaIdTab: undefined,
      attachTabName: '', // 附件tab名称
      tabIndex: 0, // tab页的下标
      isBackBut: false, // 上一页按钮是否显示
      isNextBut: true, // 下一页按钮是否显示
      isSaveBut: false, // 保存按钮是否显示
      isDraftBut: true, // 存草稿按钮是否显示
      isUpdateBut: false, // 是否修改数据 若是则保存按钮一直显示
      tabsType: '',
      draftLoading: false
    }
  },
  computed: {
    contentHeight() {
      return this.isDetailOrAudit ? ' calc(100% - 5px)' : '100%'
    },
    isBtDisabled() {
      return (this.btEditSave === undefined)
    },
    getTab() {
      const currentTab = this.tabs.filter(tab => tab.tabName === this.activedTab)[0]
      return currentTab
    }
  },
  mounted() {
    this.tabsType = getTabsType(this.$refs.tabs.$el)
  },
  methods: {
    init(dlg, params) { // 详情弹框时，初始化调用这个方法
      var exInitData = params.exInitData || params.exInitDataCformTab
      var formCanvasMode = exInitData?.isAudit ? '审核' : '详情'
      var exParamsFormTabSave = {
        detailDlg: dlg,
        formCanvasMode: formCanvasMode,
        isNoEditButtons: true,
        formType: params.formType,
        isApply: params.isApply }
      Object.assign(exParamsFormTabSave, exInitData)
      this.isDetailOrAudit = true
      this.isEdit = false

      var cbBeforeApi = () => { }
      var cbFailed = () => { }
      var cbSuccess = (result, tabVo, formTabSave) => {

      }

      this.refreshTabs(
        exInitData.metaIdTab, cbBeforeApi, cbSuccess, cbFailed,
        exParamsFormTabSave)
    },
    refreshTabs(formId, cbBeforeApi, cbSuccess, cbFailed, exParams) {
      exParams = exParams || {}
      if (exParams.extendKey) {
        this.extendHandler = `form-tab-save-func-extend-${exParams.extendKey}`
      }

      this.$nextTick(() => {
        if (this.$refs.formTabSaveExtend &&
              this.$refs.formTabSaveExtend.exHandleParams) {
          this.$refs.formTabSaveExtend.exHandleParams(this, exParams)
        }

        // metaIdTab记录CformTab机制关联的表单
        // 在项目口径里，这个是项目的metaID
        this.metaIdTab = formId
        this.tabs = []
        this.tabInitParams = Object.assign({}, exParams)
        this.tabInitParams.metaIdTab = this.metaIdTab
        this.tabInitParams.isDetailOrAudit = this.isDetailOrAudit
        this.tabInitParams.setTabLabel = this.setTabLabel
        this.tabInitParams.formTabSaveObj = this

        // 获取当前所有编辑数据，返回CformDataVo对象
        // 这个对象可以直接用于保存动作，这个方法必须在初始化之后才能使用
        this.tabInitParams.getDataVoDataAll = () => {
          var dataVo = this.tabInitParams.getDataToSave()
          for (let i = 0; i < this.$refs.tabContents.length; i++) {
            if (this.$refs.tabContents[i].fillDataVoBeforeSave) {
              this.$refs.tabContents[i].fillDataVoBeforeSave(dataVo)
            }
          }
          return dataVo
        }

        this.tabInitParams.setStatusText = (text, style) => {
          this.statusText = text
          this.statusTextStyle = style
        }

        this.btEditBack = exParams.btEditBack
        this.btEditDraft = exParams.btEditDraft
        this.btEditSave = exParams.btEditSave
        exParams.formId = formId

        if (cbBeforeApi) {
          cbBeforeApi()
        }

        this.$callApiParams('selectCformTabs',
          { formId: formId }, result => {
            return this.doInit(
              result.data, cbBeforeApi,
              cbSuccess, cbFailed, exParams, result)
          }, result => {
            if (cbFailed) {
              cbFailed()
            }
          })
      })
    },
    doInit(tabVo, cbBeforeApi, cbSuccess, cbFailed, exParams, result) {
      // 初始化默认是0 按钮初始化
      this.tabIndex = 0
      this.isBackBut = false
      this.isNextBut = true
      this.isSaveBut = false
      // tab页的下标
      var tabIndex = 1
      tabVo.tabs.forEach(tab => {
        tab.tabLabel = tab.tabName
        if (tab.isEnabled) {
          var tabClone = this.$clone(tab)
          // tabClone.tabLabel = tabIndex + '.' + tabClone.tabLabel
          this.tabs.push(tabClone)
          tabIndex = tabIndex + 1
        }
      })
      if (this.tabs.length === 1) {
        // 只有一个tab不显示上一页下一页
        this.isBackBut = false
        this.isNextBut = false
        this.isSaveBut = true
        this.tabs[0].tabLabel = this.tabs[0].tabLabel.substr(2, this.tabs[0].tabLabel.length)
      }

      if (exParams.reConstructTabsIf) {
        exParams.reConstructTabsIf(tabVo, this)
      }

      if (cbSuccess) {
        cbSuccess(result, tabVo, this)
      }

      if (this.tabs.length > 0) {
        var fileTabIndex
        for (var i = 0; i < this.tabs.length; i++) {
          var tab = this.tabs[i]
          if (tab.tabType === '文件依据') {
            this.tabInitParams.fileTabName = i + '.' + tab.tabName
            this.attachTabName = tab.tabName
            fileTabIndex = i
          }
        }

        this.$nextTick(() => {
          // 主要解决预算启用单独的编制格式时，使用的是预算
          // 基本信息的表单，而不是基本信息的表单
          var getFormTabIndex = exParams.getFormTabIndex
          var getFormTabIndexBasic = () => {
            for (var i = 0; i < this.tabs.length; i++) {
              if (this.tabs[i].tabType === '基本信息') {
                return i
              }
            }
          }

          // 外界设置了getFormTabIndex，则使用该方法确定主表单索引
          // 否则使用基本信息对应的页签索引
          var formTabIndex
          var extraData = {}
          var dataId = exParams.dataId
          var formIdNew
          if (getFormTabIndex) {
            getFormTabIndex(this.tabs, getFormTabIndexBasic, extraData, exParams)
            formTabIndex = extraData.formTabIndex
            if (this.$isNotEmpty(extraData.formId)) {
              // 外部指定使用其他表单
              formIdNew = extraData.formId
            }
          } else {
            formTabIndex = getFormTabIndexBasic()
          }
          if (this.$isNotEmpty(dataId) || exParams.exQueryParams) {
            // 修改时不显示存草稿
            this.isDraftBut = false
          }

          if (formTabIndex === undefined) {
            this.$message.error('找不到表单tab的索引')
          } else {
            // 先初始化表单tab
            var showTabContentMainForm = (theVersionId, dataId) => {
              var formTab = this.tabs[formTabIndex]
              var showThisTabAfterRefresh = this.tabInitParams.showThisTabAfterRefresh
              if (!showThisTabAfterRefresh) { // 指定显示页签时，不默认先显示表单
                this.activedTab = formTab.tabName
              }

              // versionId和dataId用来记录主表单的版本号和单据id
              // theVersionId和theDataId是用来进行本次表单页签初始化使用
              this.tabInitParams.versionId = theVersionId
              this.tabInitParams.dataId = dataId
              this.tabInitParams.theVersionId = theVersionId
              this.tabInitParams.theDataId = dataId

              this.tabInitParams.formCanvasMode = exParams.formCanvasMode
              this.tabInitParams.formId = formIdNew
              if (this.$isEmpty(this.tabInitParams.formId)) {
                this.tabInitParams.formId = exParams.formId
              }

              this.tabInitParams.initFormBefore = (dataVo) => {
                exParams.initFormBefore?.(dataVo)
              }
              this.tabInitParams.callbackAfterMainFormLoaded = (dataVo, formCanvasMeta) => {
                this.tabInitParams.dataVo = dataVo
                this.tabInitParams.formCanvasMeta = formCanvasMeta

                if (exParams.detailDlg) {
                  const width = dataVo.version.detailDlgWidth
                  const height = dataVo.version.detailDlgHeight
                  this.$setDlgSize(exParams.detailDlg, 'globalDialog', width, height)
                }

                this.$nextTick(() => {
                  // 如果是制单场景，需要把其他页签都进行初始化，这样才能不切换页签直接点保存
                  var isFillingForm = (exParams.btEditSave !== undefined)
                  if (isFillingForm) {
                    for (let i = 0; i < this.$refs.tabContents.length; i++) {
                      if (i !== formTabIndex) {
                        this.showTabContent(i)
                      }
                    }
                  }

                  // 主表单初始化之后，自动初始化文件依据tab，或者指定的tab
                  if (showThisTabAfterRefresh) {
                    this.activedTab = showThisTabAfterRefresh.name
                    if (!isFillingForm) {
                      this.showTabContent(showThisTabAfterRefresh.index)
                    }
                  } else if (fileTabIndex) {
                    if (exParams.isShowAuditFileTabWhenInit) { // 审核时优先显示附件tab
                      this.activedTab = this.tabInitParams.fileTabName
                    }
                    if (!isFillingForm) {
                      this.showTabContent(fileTabIndex)
                    }
                  }
                  exParams.callAfterFormLoaded && exParams.callAfterFormLoaded(dataVo)

                  if (dataVo.extData.是否显示存草稿) {
                    this.isDraftBut = true
                  }
                  if (dataVo.extData.是否显示保存) {
                    this.isSaveBut = true
                    this.isUpdateBut = true
                  } else {
                    this.isUpdateBut = false
                  }
                })
                // if (this.tabInitParams.getFormExtend) {
                //   var formExtend = this.tabInitParams.getFormExtend()
                //   formExtend.formTabSave = this
                // }
              }
              this.showTabContent(formTabIndex)

              // 以下两个方法是为了方便在不同的页签中获取到主表单的最新数
              // 据和表单扩展对象，主表单只会初始化一次，而
              // 主表单tab总是第一个初始化，所以通过在页签里设置
              // getDataToSaveInTab，然后传递到外面的方式没有问题
              this.tabInitParams.getDataToSave = this.tabInitParams.getDataToSaveInTab
              this.tabInitParams.getFormExtend = this.tabInitParams.getFormExtendInTab
            }

            if (this.$isNotEmpty(dataId)) {
              // 这种情况是运行时修改表单
              showTabContentMainForm('', dataId)
            } else if (this.$isNotEmpty(exParams.versionId)) {
              // 这种情况是运行时新增，申请列表界面本身已经有versionId
              showTabContentMainForm(exParams.versionId, '')
            } else if (this.$isEmpty(formIdNew)) {
              // 这种情况使用CformTab机制中设置使用当前页签关联的表单
              showTabContentMainForm(result.attributes.metaVersionId, dataId)
            } else {
              // 这种情况getFormTabIndex方法中指定了使用其他表单
              this.$callApiParams('selectCformVersionIdByFormId',
                { formId: formIdNew }, result => {
                  var versionId = result.data
                  showTabContentMainForm(versionId, dataId)
                  return true
                })
            }
          }
        })
      }
      return true
    },
    tabClick(tab) {
      this.showTabContent(parseInt(tab.index))
      // tab页切换
      this.tabIndex = parseInt(tab.index)
      this.tabBackNext(this.tabIndex)
      this.$nextTick(() => {
        // 2023.8.14 解决预算明细申报 高度异常问题
        Array.from(this.$refs.tabContents).forEach(item => {
          if (item.$el.getAttribute('id') && item.$el.getAttribute('id').includes('formTabSaveForm')) {
            var regularFormActualHeight = $('#' + item.$el.getAttribute('id'))
              .find('.formCommonCols.formRegularColbottomBorber')
              .height()
            if (parseInt(regularFormActualHeight) > 0) {
              $('#' + item.$el.getAttribute('id'))
                .find('.formCanvas')
                .height(parseInt(regularFormActualHeight) + 10)
            }
          }
        })
      })
    },
    showTabContent(tabIndex) {
      var tabCompnent = this.$refs.tabContents[tabIndex]
      if (tabCompnent) {
        var tab = {
          index: tabIndex,
          name: this.tabs[tabIndex].tabName
        }

        // 类似预算域表单扩展组件的场景，会在扩展组件里修改了原来的metaIdTab
        // 所以需要在切换页签的时候，将原来的metaIdTab设置回去
        if (this.metaIdTab) {
          this.tabInitParams.metaIdTab = this.metaIdTab
        }
        this.tabInitParams.tabIndex = tabIndex
        this.tabInitParams.tabName = tab.name
        this.tabInitParams.isEdit = this.isEdit
        this.tabInitParams.tabEntity = this.tabs[tabIndex]
        this.tabInitParams.formTabSave = this
        tabCompnent.showTabContent(this.tabInitParams)
        this.$nextTick(() => {
          this.$emit('tab-click', tab, tabCompnent)
        })
        return tabCompnent
      }
    },
    showError(result) {
      var isShow = true
      if (result.attributes.hasOwnProperty('其它')) {
        this.tabClick({ 'index': 0 })
        return false
      }
      for (var i = 0; i < this.$refs.tabContents.length; i++) {
        var tabCompnent = this.$refs.tabContents[i]
        if (tabCompnent && tabCompnent.showError) {
          var bool = tabCompnent.showError(result)
          if (bool > 0) {
            isShow = false
            this.tabClick({ 'index': i })
            break
          }
        }
      }
      if (isShow) { // 当不是行表单的时候要另外抛出错误信息
        this.$message.error(result.msg)
        return false
      }
    },
    doBtEditBack() {
      if (this.btEditBack) {
        this.btEditBack()
      }
    },
    setBtDraftLoading(isLoading) {
      this.draftLoading = isLoading
    },
    doBtEditDraft(e) {
      if (this.btEditDraft) {
        var dataVo = this.tabInitParams.getDataVoDataAll()
        this.btEditDraft(e, dataVo)
      }
    },
    doBtEditSave(e) {
      if (this.btEditSave) {
        var dataVo = this.tabInitParams.getDataVoDataAll()
        this.btEditSave(e, dataVo)
      }
    },
    setTabLabel(tabName, tabLabel) {
      tabLabel = tabLabel || tabName
      for (var i = 0; i < this.tabs.length; i++) {
        if (this.tabs[i].tabName === tabName) {
          this.tabs[i].tabLabel = tabLabel
        }
      }
    },
    setNodeNameWhenAudit(nodeName) {
      for (var i = 0; i < this.tabs.length; i++) {
        if (this.tabs[i].tabName === this.attachTabName) {
          if (this.$refs.tabContents[i].setNodeNameWhenAudit) {
            this.$refs.tabContents[i].setNodeNameWhenAudit(nodeName)
          }
        }
      }
    },
    tabBackNext(tabIndex) {
      this.showTabContent(tabIndex)
      // if (this.tabs.length > 1) {
      //   for (var i = 0; i < this.tabs.length; i++) {
      //     this.activedTab = this.tabs[tabIndex].tabName
      //     break
      //   }
      // }

      if (tabIndex >= 0 && tabIndex < this.tabs.length) {
        this.activedTab = this.tabs[tabIndex].tabName
      }

      if (tabIndex > 0) {
        this.isBackBut = true
      } else {
        this.isBackBut = false
      }
      if (tabIndex + 1 === this.tabs.length) {
        this.isSaveBut = true
        this.isNextBut = false
      } else {
        this.isSaveBut = false
        this.isNextBut = true
      }
    },
    disableTab(tab) {
      return this.disabledTabs.includes(tab.tabName)
    }
  }
}
</script>
<style lang="scss" scoped>
  .cformTabSave {
    width: 100%;
    height: 100%;
    &-tabs {
      display: flex;
      flex-direction: column;
      /deep/.el-tabs__content {
        flex: 1;
      }
    }
  }

</style>
<style lang="scss">
  .cformTabSave {width: 100%;height: 100%;}
  // #cformTabSave {height: 93%;}
  .cformTabSave .el-tabs__header { margin-bottom: 20px; }
  #bifrost-ic-app #cformTabSave /deep/ .el-tabs__content {height: 96% !important;}
</style>
