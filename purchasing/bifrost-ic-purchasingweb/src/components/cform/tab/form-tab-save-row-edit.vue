<template>
  <div class="formTabRowFormRoot">
    <div class="formTabRowFormContent" v-if="!isNoData">
      <row-form-content ref="formTabRowFormContent" :isBasicExtend="isBasicExtend"/>
    </div>
    <div class="formTabRowFormContent formTabRowFormContentEmpty" v-if="isNoData"/>
  </div>
</template>

<script>
// tab常量
const { BUDGET } = {
  BUDGET: '预算信息'
}
import RowFormContent from '../../tablecolumn/row-form-content'
export default {
  name: 'form-tab-save-列表编辑',
  components: { RowFormContent },
  props: {
    isBasicExtend: {
      default: false,
      type: Boolean
    }
  },
  data() {
    return {
      tabData: undefined, // 如果当前组件被放在页签中，这个字段存储对应的tab
      params: undefined,
      isBtSetTabContentEnabled: true, // 这个属性在外界使用
      isNoData: undefined,
      rowFormIdKey: undefined,
      rowFormExKey: undefined,
      formIdTabNameKey: '',
      getFormExtend: undefined,
      isNoEditButtons: undefined
    }
  },
  methods: {
    showTabContent(params) {
      if (this.isNoData !== undefined) { // 只初始化一次
        return
      }
      var reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
      let editInstanceKey = params.metaIdTab
      if (!reg.test(params.metaIdTab)) {
        params.dataVo.extData.表单动态模板ID = params.metaIdTab
        // 预算信息
        if (!this.isBasicExtend && params.tabName === BUDGET) {
          // 预算信息默认隐藏重置，新增
          const formName = params.dataVo.meta?.name || ''
          const isBasicPay = formName.includes('基本支出')
          if (isBasicPay) {
            params.hideCurdButton = ['新增']
          } else {
            params.hiddenButtons = ['重置']
          }
          params.buttons = [{
            text: '重置',
            enabledType: '0',
            click: (bt) => {
              const formExtend = params.getFormExtend?.() || {}
              if (formExtend) {
                // 行编辑表单的数据在triggerExtendEvent函数的第四个参数
                formExtend.resetData?.()
              }
            }
          }]
          editInstanceKey = BUDGET
        }
      }
      var isNoDataValue = true
      var pageData
      var isEdit = params.isEdit || false
      this.getFormExtend = params.getFormExtend
      this.rowFormIdKey = params.metaIdTab
      this.rowFormExKey = params.tabName
      // 给拓展组件挂载实例
      const formExtend = params.getFormExtend?.() || {}
      if (!formExtend.editInstanceMap) {
        formExtend.editInstanceMap = {}
      }
      formExtend.editInstanceMap[editInstanceKey] = this
      if (params.dataVo &&
        params.dataVo.extData &&
        params.dataVo.extData.tabRowFormPageDataMap &&
        params.tabName !== undefined &&
        params.formId !== undefined) {
        this.formIdTabNameKey =
          this.$makeRowFormKey(this.rowFormIdKey, this.rowFormExKey)
        pageData = params.dataVo.extData.tabRowFormPageDataMap[this.formIdTabNameKey]
        if (pageData && this.$isNotEmpty(pageData.columns)) {
          isNoDataValue = false
        }
      }
      if (params.formTabSave) {
        params.tableExStyleClass = 'blue-table' // 表单多tab模式时统一使用蓝色列表
      }

      this.params = params

      this.isNoData = isNoDataValue
      if (!this.isNoData) {
        this.$nextTick(() => {
          // 行编辑表单有单元格值发生变化时，如果主表单有扩展组件
          // 并且扩展组件里有triggerExtendEvent函数，则会调用这个函数
          var rowCellChanged = (scope, colItem, baseListObj) => {
            var rowCellChangedData = {
              scope: scope,
              colItem: colItem,
              baseListObj: baseListObj,
              allRows: baseListObj.rowsData,
              formId: params.formId,
              tabName: params.tabName,
              rowIndex: scope.$index,
              cellName: colItem.prop,
              isBasicExtend: this.isBasicExtend,
              changedValue: scope.row[colItem.prop],
              getDataToSave: () => { return params.getDataToSave() },
              getDataVoDataAll: () => { return params.getDataVoDataAll() },
              getCellValue: (name) => {
                return scope.row[name]
              }
            }

            // 触发表单扩展组件的triggerExtendEvent
            if (this.getFormExtend) {
              var formExtend = this.getFormExtend()
              if (formExtend && formExtend.triggerExtendEvent) {
                // 行编辑表单的数据在triggerExtendEvent函数的第四个参数
                formExtend.triggerExtendEvent(
                  undefined, undefined, undefined, rowCellChangedData)
              }
            }

            // 触发额外组件在params中设置的响应函数
            this.$doRowFormCellChanged(
              params, this.rowFormIdKey, this.rowFormExKey, rowCellChangedData)

            // 调用外界处理单元格变化的函数
            if (params && params.rowCellChanged) {
              params.rowCellChanged(scope, colItem, this)
            }
          }

          // 行编辑表单有弹框单元格值发生变化时，如果主表单有扩展组件
          // 并且扩展组件里有triggerExtendEvent函数，则会调用这个函数
          var rowCellClickCallback = (scope, colItem, selectedData, isRowCellMap) => {
            if (this.getFormExtend) {
              var formExtend = this.getFormExtend()
              if (formExtend && formExtend.rowCellClickCallback) {
                formExtend.rowCellClickCallback(scope, colItem, selectedData, isRowCellMap)
              }
            }
          }

          // 行表单选择之前事件
          var rowCellClickBefore = (colItem, params) => {
            if (this.getFormExtend) {
              var formExtend = this.getFormExtend()
              if (formExtend && formExtend.rowCellClickBefore) {
                formExtend.rowCellClickBefore(colItem, params)
              }
            }
          }

          // 三级明细汇总统计项目总额
          var aotuSummaryPmAmount = (rowsData) => {
            if (this.getFormExtend) {
              var formExtend = this.getFormExtend()
              if (formExtend && formExtend.aotuSummaryPmAmount) {
                formExtend.aotuSummaryPmAmount(rowsData)
              }
            }
          }

          // 行编辑表单有单选单元格值发生变化时，如果主表单有扩展组件
          var rowRadioCallback = (scope, colItem, columnsData, trueMap, rowsData) => {
            if (this.getFormExtend) {
              var formExtend = this.getFormExtend()
              if (formExtend && formExtend.rowRadioCallback) {
                formExtend.rowRadioCallback(scope, colItem, columnsData, trueMap, rowsData)
              }
            }
          }

          var deleteRowEx = (baseListObj, rowIdsDeleted, deleteData) => {
            var deleteRowData = {
              baseListObj: baseListObj,
              formId: params.formId,
              tabName: params.tabName,
              deleteData: deleteData,
              getDataToSave: () => { return params.getDataToSave() },
              getDataVoDataAll: () => { return params.getDataVoDataAll() }
            }
            if (this.getFormExtend) {
              var formExtend = this.getFormExtend()
              if (formExtend && formExtend.triggerExtendEvent) {
                formExtend.rowDeleteExtend(
                  undefined, undefined,
                  undefined, deleteRowData)
              }
            }

            // 额外的行删除时的关联函数处理
            this.$doRowFormDeleteRow(
              params, this.rowFormIdKey, this.rowFormExKey, deleteRowData)

            if (params && params.deleteRowEx) {
              params.deleteRowEx(baseListObj, rowIdsDeleted, deleteData)
            }
          }
          var afterRowCalculation = (scope) => {
            params.afterRowCalculation?.(scope)
            if (this.getFormExtend) {
              var formExtend = this.getFormExtend()
              if (formExtend && formExtend.afterRowCalculation) {
                formExtend.afterRowCalculation(scope)
              }
            }
          }

          var isNoEditButtonsFinal = this.isNoEditButtons
          if (isNoEditButtonsFinal === undefined) {
            isNoEditButtonsFinal = params.isNoEditButtons
          }

          // 这里params中也有tabName，但是由于params是全局的，所以
          // 程序运行到这里的时候，可能tabName.tabName != this.rowFormExKey
          var paramsContent = Object.assign({}, params)
          Object.assign(paramsContent, {
            isNoEditButtons: isNoEditButtonsFinal,
            pageData: pageData,
            rowCellChanged: rowCellChanged,
            rowCellClickCallback: rowCellClickCallback,
            rowCellClickBefore: rowCellClickBefore,
            aotuSummaryPmAmount: aotuSummaryPmAmount,
            rowRadioCallback: rowRadioCallback,
            deleteRowEx: deleteRowEx,
            tabName: this.rowFormExKey,
            afterRowCalculation
          })
          this.$refs.formTabRowFormContent.treeNodeLabel = this.formIdTabNameKey
          this.$refs.formTabRowFormContent.isEdit = isEdit
          // 解决多级表头不可编辑问题
          // if (!paramsContent.hasOwnProperty('editableAny')) {
          //   paramsContent.editableAny = true
          // }
          this.$refs.formTabRowFormContent.init(paramsContent)
        })
      }
    },
    showTabContentIfTemplate(params) { // 与数据模板绑定时初始化行表单控件
      if (params.template === undefined) {
        return
      }

      params.tabName = ''
      params.metaIdTab = params.template.id
      params.tableExStyleClass = 'blue-table' // 有扩展模板的场景统一使用蓝色表格
      this.isNoEditButtons = (params.template.formatType === '固定行数')
      if (params.editableAny) {
        this.isNoEditButtons = false
      }

      this.showTabContent(params)
    },
    resetTemplate(template, bizzId, resetTemplateNew) { // 重新装填模板数据
      bizzId = bizzId || template.id
      this.$callApiParams(
        'selectRowFromPageData',
        {
          isEdit: true,
          bizzId: bizzId,
          treeNodeLabel: template.id,
          treeNodeLabelUseAsDataType: '占位符' },
        result => {
          var pageData = result.data
          this.isNoData = undefined
          this.params.template = template
          this.params.dataVo.extData.tabRowFormPageDataMap[template.id] = pageData
          this.showTabContentIfTemplate(this.params)
          resetTemplateNew(template)
          return true
        })
    },
    fillDataVoBeforeSave(dataVo) {
      if (this.$refs.formTabRowFormContent &&
        this.$refs.formTabRowFormContent.fillDataVoBeforeSave) {
        this.$refs.formTabRowFormContent.fillDataVoBeforeSave(dataVo)
      }
    },
    fillDataVoBeforeSaveByPageData(dataVo, pageData, treeNodeLabel) {
      if (this.$refs.formTabRowFormContent &&
        this.$refs.formTabRowFormContent.fillDataVoBeforeSaveByPageData) {
        this.$refs.formTabRowFormContent.fillDataVoBeforeSaveByPageData(
          dataVo, pageData, treeNodeLabel)
      }
    },
    setButtonBarVisible(visible, isNoPaddingTop) { // 动态设置按钮栏是否显示
      this.$refs.formTabRowFormContent.setButtonBarVisible(visible, isNoPaddingTop)
    },
    updateRowsData(updatayear) {
      this.$refs.formTabRowFormContent.updateRowsData(updatayear)
    },
    addRow(bt) {
      this.$refs.formTabRowFormContent.addRow(bt)
    },
    // 可以添加行数据
    addRowsData(row) {
      this.$refs.formTabRowFormContent.addRowsData(row)
    },
    // 获取表格数据
    getRowsData() {
      return this.$refs.formTabRowFormContent.getRowsData()
    },
    reRenderBtns(hiddenButtons) {
      this.$refs.formTabRowFormContent.reRenderBtns(hiddenButtons)
    },
    parentAddRow(row) {
      this.$refs.formTabRowFormContent?.callbackAddRow?.(row)
    },
    // 预算信息的初始化行
    initBudget(rows) {
      this.$refs.formTabRowFormContent.initBudget(rows)
    },
    clearTabPageData() {
      this.$refs.formTabRowFormContent.clearTabPageData()
    },
    // 子项目测算是否有挑选功能
    submeaGetEstMode() {
      this.$refs.formTabRowFormContent.submeaGetEstMode?.()
    },
    // 根据id删除行
    deleteTableDataByField(id) {
      this.$refs.formTabRowFormContent.deleteTableDataByField(id)
    },
    getHistoryOptions() {
      if (this.$refs.formTabRowFormContent !== undefined) {
        return this.$refs.formTabRowFormContent.getHistoryOptions()
      }
    },
    clearRow() {
      this.$refs.formTabRowFormContent.clearRow()
    },
    clearError() {
      this.$refs.formTabRowFormContent?.clearError()
    },
    showError(result) {
      if (this.$refs.formTabRowFormContent !== undefined) {
        return this.$refs.formTabRowFormContent.showError(result)
      }
      return 0
    },
    fillTable(pageData) { // 填充数据
      if (this.$refs.formTabRowFormContent) {
        this.$refs.formTabRowFormContent.fillTable(pageData)
      }
    },
    disableAllBts() {
      if (this.$refs.formTabRowFormContent) {
        this.$refs.formTabRowFormContent.disableAllBts()
      }
    },
    disableAllBtsResume() {
      if (this.$refs.formTabRowFormContent) {
        this.$refs.formTabRowFormContent.disableAllBtsResume()
      }
    }
  }
}
</script>

<style lang="scss">
  .formTabRowFormRoot { height: 100%;width: 100%; }
  .formTabRowFormRoot .formTabRowFormContent {height: calc(100% - 2px);width: 100%; }
  .formTabRowFormRoot .formTabRowFormContentEmpty { border: 1px solid #DDDDDD; background: #e9e9e9;}
</style>
