<template>
    <el-form ref="targetForm" :model="accordingData" class="project-form" label-width="120px">
      <el-form-item label="年度" prop="bugdetYear">
        <div class="flex-row">
          <el-select v-model="bugdetYear" filterable style="width: 200px;margin-right: 10px" @change="yearChange">
            <el-option
              v-for="item in bugdetYears"
              :key="item.value"
              :label="item.label"
              :value="item.label">
            </el-option>
          </el-select>
          <div v-if="!disabled">
            <el-button type="primary" class="btn-normal" @click="copy()">复制</el-button>
            <el-button type="primary" class="btn-normal" @click="paste()" :disabled ="buttonDis">粘贴</el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="yearTarget">
        <template #label>
          <span style="content: '*';color: red" v-if="!disabled">*</span>政策依据
        </template>
        <el-input type="textarea"
                  maxlength="300"
                  :disabled="disabled"
                  v-model="accordingData.policybasis"
                  show-word-limit/>
      </el-form-item>
      <el-form-item  prop="longTarget" style="margin-bottom: 0px !important;">
        <template #label>
          <span style="content: '*';color: red" v-if="!disabled">*</span>测算依据
        </template>
        <el-input type="textarea"
                  maxlength="2000"
                  :disabled="disabled"
                  v-model="accordingData.calculation"
                  show-word-limit/>
      </el-form-item>
    </el-form>
</template>

<script>
export default {
  name: 'form-tan-save-basic-year',
  data() {
    return {
      bugdetYears: [
      ],
      accordingData: {
        policybasis: '',
        calculation: ''
      },
      accordingListMap: {},
      bugdetYear: '',
      copyYear: '',
      copyData: {},
      disabled: false, // 详情禁用
      buttonDis: true // 粘贴按钮禁用
    }
  },
  methods: {
    copy() {
      var bugdetYear = this.bugdetYear
      if (this.$isNotEmpty(bugdetYear)) {
        this.copyData = this.$clone(this.accordingData)
        this.copyYear = bugdetYear
        this.$message({ message: '已复制【' + bugdetYear + '】年度的数据', type: 'success' })
        this.buttonDis = false
      }
    },
    paste() {
      var bugdetYear = this.bugdetYear
      if (this.$isNotEmpty(bugdetYear)) {
        this.accordingData = this.$clone(this.copyData)
        this.accordingListMap[bugdetYear] = this.accordingData
        this.$message({ message: '已粘贴【' + this.copyYear + '】年度的数据至【' + this.bugdetYear + '】年度', type: 'success' })
      }
    },
    yearChange(year) {
      if (!this.accordingListMap[year]) {
        this.accordingListMap[year] = {
          policybasis: '',
          calculation: ''
        }
      }
      this.accordingData = this.accordingListMap[year]
    }
  }
}
</script>

<style lang="scss" scoped>
.project-form {
  /deep/.el-form-item {
    margin-bottom: 10px;
  }
}
</style>
