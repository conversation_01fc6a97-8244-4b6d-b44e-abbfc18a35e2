<template>
  <page :style="printStyleFormat()">
    <template #pageContent>
      <form-canvas ref="formCanvasDetail"
                   v-show="!showPdf"
                   class="formCanvasDetail"
                   :style="{ width: isPrintDetails ? '85%' : '100%',height:formCanvasHeight+'px' }"
                   :key="keyId"
                   mode="详情"
                   :isPreviewDetail="isPreviewDetail"
                   @sliderChange="sliderChange"/>
      <iframe-pdf ref="iframePdf"
                  v-show="showPdf"
                  :style="{ width: isPrintDetails ? '85%' : '100%', maxWidth: '1100px' }"/>
    </template>
  </page>
</template>
<script>
// import printJS from 'print-js'
import $ from 'jquery'
import domtoimage from '../../utils/domToImage'
// import JSPDF from 'jspdf'
// import html2Canvas from 'html2canvas'
import { exportExcel } from '../../components/cform/js/exportSheet'
import IframePdf from './iframe-pdf'

export default {
  name: 'formDetail-详情',
  components: { IframePdf },
  provide() {
    return {
      resetDlgSize: this.resetDlgSize
    }
  },
  data() {
    return {
      showPdf: false,
      keyId: '',
      meta: {},
      dlgRef: {},
      formCanvasHeight: '100%',
      versionRelateRef: {},
      isPreviewDetail: false,
      params: {},
      printLoading: null,
      canvasHeight: '1000px',
      freeWidth: '800px',
      isPrintDetails: false,
      isSavePdf: false, // 是否保存pdf
      onPrintDlgClose: undefined, // 存在一些场景单独打开打印，关闭打印弹出时需要回调函数
      cfromm: null
    }
  },
  methods: {
    resetDlgSize(insertObj) {
      if (this.dlgRef && this.$isNotEmpty(insertObj)) {
        this.initDlgSize(this.dlgRef, insertObj, 'isDetails')
      }
    },
    printStyleFormat() {
      if (this.isPrintDetails) {
        return {
          width: this.freeWidth,
          'justify-content': 'center',
          display: 'none'
          // height: this.canvasHeight
        }
      } else {
        return ''
      }
    },
    isDesignFreeCform() {
      return !!window.location.href.includes('/cform')
    },
    showPrint(params, formType, exportType, isType, buttonType) {
      if (this.$isEmpty(params)) return
      $('#formPrintContainerGlobal').addClass('showformPrintContainerGlobal')
      this.isSavePdf = params.isSavePdf
      this.cfromm = params.cfromm
      this.onPrintDlgClose = undefined
      if (!this.isSavePdf) {
        if (buttonType === '打印' || buttonType === '打印预览') {
          this.printLoading = this.printLoadingFun()
        } else {
          this.printLoading = this.exportLoadingFun()
        }
        // this.printLoading = this.$isEmpty(exportType) && !isType ? this.printLoadingFun() : this.exportLoadingFun()
      }
      this.$nextTick(() => {
        if (this.isDesignFreeCform()) {
          if (this.$isNotEmpty(params.toSaveRefs)) {
            this.versionRelateRef = params.toSaveRefs
          }
          if (this.$isNotEmpty(params.meta.main)) {
            this.initMeta('详情', params.meta, false)
            const cellData = window.luckysheet && window.luckysheet.getAllSheets().length && window.luckysheet.getAllSheets()[0].celldata
            const rowKey = cellData.filter(item => item.v.ff !== '新宋体（ST Song）').splice(-1)[0].r
            this.formCanvasHeight = window.luckysheet.getAllSheets()[0].visibledatarow[rowKey]
            setTimeout(() => this.toPrint(params.meta, exportType, buttonType), 5000)
          }
        } else {
          if (typeof params === 'string') {
            this.initPrintByDataId(params, true, formType, exportType, params, isType, buttonType)
          } else if (this.$isNotEmpty(params.printId)) {
            if (this.$isNotEmpty(params.onPrintDlgClose)) {
              this.onPrintDlgClose = params.onPrintDlgClose
            }
            this.initPrintByDataId(params.printId, true, formType, exportType, params, isType, buttonType)
          }
        }
      })
    },
    onDlgClose() {
      // 关闭弹框时自由表单需要重新加载
      const metaId = this.$sessionStorage.get('treeId')
      if (this.isDesignFreeCform()) {
        this.$nextTick(() => {
          this.$isNotEmpty(this.versionRelateRef) && this.versionRelateRef.openCanvas(metaId, { needToReloadFreeJson: true })
        })
      }
      // 关闭弹窗 解绑事件
      if (this.$refs.formCanvasDetail && this.$refs.formCanvasDetail.$refs.formFormat.removeEventListeners) {
        this.$refs.formCanvasDetail.$refs.formFormat.removeEventListeners()
      }
    },
    printLoadingFun() {
      return this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.6)'
      })
    },
    exportLoadingFun() {
      return this.$loading({
        lock: true,
        text: '导出中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.6)'
      })
    },
    initPrintByDataId(dataId, isPrintDetails, formType, exportType, exParams, isType, buttonType) { // 运行制单打印时详情
      this.isPrintDetails = isPrintDetails
      this.isPreviewDetail = false
      // 解决保存和打印表单时会出现滚动条
      $('#formPrintContainerGlobal').removeClass('showformPrintContainerGlobal')
      $('#formPrintContainerGlobal').addClass('hideformPrintContainerGlobal')
      $('#formPrintContainerGlobal').removeClass('regularformPrintContainerGlobal')

      if (!this.isSavePdf) {
        if (buttonType === '打印') {
          this.printLoading = this.printLoadingFun()
        } else {
          this.printLoading = this.exportLoadingFun()
        }
        // this.printLoading = isType ? this.exportLoadingFun() : this.printLoadingFun()
      }
      this.keyId = dataId
      var params = typeof exParams === 'object' ? exParams || {} : {}
      if (this.$isNotEmpty(formType)) {
        params.formType = formType
      }
      params.buttonType = buttonType
      this.$nextTick(() => {
        params.failCallback = () => {
          this.printLoading?.close?.()
        }
        this.$refs.formCanvasDetail.initByVersionDataId(
          '', dataId, '详情',
          meta => {
            // 规则表单为选择打印模板
            if (this.$isNotEmpty(meta.main.isFreedomForm) && !meta.main.isFreedomForm) {
              if (this.isSavePdf) { // 走保存pdf时 没有打印模版 则跳过
                this.onPrintDlgClose?.()
                return
              } else {
                this.onPrintDlgClose?.()
                this.printLoading && this.printLoading.close()
                return this.$message({
                  message: '未选择打印模板',
                  type: 'warning'
                })
              }
            }
            if (isType) {
              $('#formPrintContainerGlobal').addClass('showformPrintContainerGlobal')
              $('#formPrintContainerGlobal').removeClass('formCanvasRuntimeHasAssembly formCanvasRuntimeHasAssemblyFree')
            }

            this.$nextTick(() => {
              setTimeout(() => {
                const freeWidth = $('#luckysheet-sheettable_0').width() + 100
                const canvasHeight = $('#luckysheet-sheettable_0').height() + 10
                this.canvasHeight = canvasHeight + 'px'
                this.freeWidth = freeWidth + 'px'
              }, 0)
            })
            // setTimeout(() => { isType ? this.showWPSExport(meta, exportType) : this.toPrint(meta, exportType) }, 5000)
            this.$requestAnimationFrame(() => {
              isType ? this.showWPSExport(meta, exportType, buttonType) : this.toPrint(meta, exportType, buttonType)
            }, 800)
          }, isPrintDetails, undefined,
          undefined, undefined, params)
      })
    },
    toPrint(meta, exportType, buttonType) {
      const that = this
      $('.luckysheet').addClass('hidePrintSheetBorder')
      $('#luckysheet-scrollbar-x').css({ display: 'none' })
      $('#luckysheet-scrollbar-y').css({ display: 'none' })
      $('#luckysheet-icon-morebtn-div').css({ display: 'none' }) // 表单出来的时候会占位出现滚动条，因此隐藏它
      window.dispatchEvent(new Event('focus'))
      const isFree = meta && meta.main && meta.main.isFreedomForm
      // const freeWidth = $('#luckysheet-sheettable_0').width() + 100
      // const freeHeight = this.formCanvasHeight
      // let regularscrollHeight = 200
      if (!isFree) {
        // regularscrollHeight = $('#formPrintContainerGlobal #formFreeView .el-tabs__content')[0].clientHeight
        $('#formPrintContainerGlobal').addClass('regularformPrintContainerGlobal')
      }
      // const regularHeight = regularscrollHeight < 400 ? (regularscrollHeight + 120) : regularscrollHeight
      // const width = isFree ? freeWidth : 'auto'
      // const height = isFree ? freeHeight : regularHeight
      $('.luckysheet').addClass('hideLuckysheetBorder')
      $('#formPrintContainerGlobal').removeClass('hideformPrintContainerGlobal')
      setTimeout(function() {
        // domtoimage.toPng($('#formPrintContainerGlobal')[0], {
        //   width: width, // 高度宽度自行设定
        //   height: height,
        //   quality: 1,
        //   scale: 1.25 // 一个像素转为几个像素
        // }).then((dataUrl) => {
        // const url = dataUrl
        if (!that.isSavePdf && buttonType !== '打印' && buttonType !== '打印预览') {
          that.printLoading && that.printLoading.close()
        }
        if (exportType !== 'PDF') {
          const sheets = window.luckysheet.getAllSheets()
          const filename = meta && meta.main && meta.main.name || sheets[0] && sheets[0].name
          const ids = { bizId: meta.main.id, versionId: meta.instance.id }
          exportExcel(sheets, filename, exportType, '', buttonType, ids, that.cfromm, that.printLoading)
          that.onDlgClose()
          // 解决表单设计时会出现滚动条
          $('#formPrintContainerGlobal').removeClass('showformPrintContainerGlobal')
          $('#formPrintContainerGlobal').addClass('hideformPrintContainerGlobal')
          $('#formPrintContainerGlobal').removeClass('regularformPrintContainerGlobal')
          // printJS({
          //   printable: url,
          //   type: 'image',
          //   imageStyle: `border:none;margin-top:0px; margin-left:-200px`,
          //   documentTitle: '',
          //   header: '',
          //   style: '@page{size:auto;margin: 0cm 1cm 0cm 1cm;}',
          //   onPrintDialogClose() {
          //     clearInterval(focuser)
          //     if (that.onPrintDlgClose) {
          //       that.onPrintDlgClose()
          //       return
          //     }
          // that.$nextTick(() => {
          //   $('#formPrintContainerGlobal').removeClass('showformPrintContainerGlobal')
          //   $('#formPrintContainerGlobal').addClass('hideformPrintContainerGlobal')
          //   $('#formPrintContainerGlobal').removeClass('regularformPrintContainerGlobal')
          // })
          //     if (that.isDesignFreeCform()) {
          //       that.onDlgClose()
          //     }
          //     $('.luckysheet').removeClass('hideLuckysheetBorder')
          //   }
          // })
        } else {
          if (that.isSavePdf) { // 保存下载
            var base64String = ''
            const sheets = window.luckysheet.getAllSheets()
            const filename = meta && meta.main && meta.main.name || sheets[0] && sheets[0].name
            const ids = { bizId: meta.main.id, dataId: meta.data.id }
            const buttonType = '保存PDF'
            exportExcel(sheets, filename, exportType, async(base64, pdfBlob) => {
              // 拿到后端传过来的base64转成pdf文件上传到服务器
              base64String = await base64
              setTimeout(() => {
                // 上传到服务器
                const file = that.$convertBase64ToFile(base64String, meta.main.name)
                const formData = new FormData()
                formData.append('file', file)
                formData.append('path', '')// 文件存储路径
                formData.append('subId', '')// 业务系统编码
                formData.append('typeCode', '')// 类型编码
                formData.append('bizCode', '')// 模块编码
                formData.append('bizCodeName', '')// 模块名称
                formData.append('bizTblName', 'ATTACHMENT')// 业务表名称
                formData.append('bizId', meta.data.id)// 业务记录编码,业务表主键ID
                formData.append('isEsSearch', 'true')// 是否全文检索
                formData.append('fileComment', '')// 附件描述
                formData.append('attType', 'appPDF')// 附件类别
                formData.append('appendixType', '')// 附件类型
                formData.append('appendixTypeCode', '')// 附件类型编码
                formData.append('source', 'appPDF')// 来源
                that.$callApi('uploadAttachment', formData, result => {
                  if (result.success) {
                    if (that.$isNotEmpty(result.data)) {
                      console.warn(result.data, '保存pdf')
                    }
                  }
                  return true
                })
                that.isSavePdf = false
              }, 1000)
            }, buttonType, ids)
          }
          // 处理导出PDF
          // $('.luckysheet').addClass('hidePrintSheetBorder')
          // $('#luckysheet-scrollbar-x').css({ display: 'none' })
          // $('#luckysheet-scrollbar-y').css({ display: 'none' })
          // $('#luckysheet-icon-morebtn-div').css({ display: 'none' }) // 表单出来的时候会占位出现滚动条，因此隐藏它
          // html2Canvas($('#formPrintContainerGlobal')[0]).then((canvas) => {
          //   var contentWidth = canvas.width
          //   var contentHeight = canvas.height
          //   // 一页pdf显示html页面生成的canvas高度;
          //   var pageHeight = contentWidth / 592.28 * 841.89
          //   // 未生成pdf的html页面高度
          //   var leftHeight = contentHeight
          //   // pdf页面偏移
          //   var position = 0
          //   // html页面生成的canvas在pdf中图片的宽高（a4纸的尺寸[595.28,841.89]）
          //   var imgWidth = 705.28
          //   var imgHeight = 705.28 / contentWidth * contentHeight
          //   // var pageData = canvas.toDataURL('image/jpeg', 1.0)
          //   var pdf = new JSPDF('', 'pt', 'a4')// 默认纵向打印
          //   // 有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
          //   // 当内容未超过pdf一页显示的范围，无需分页
          //   if (leftHeight < pageHeight) {
          //     pdf.addImage(dataUrl, 'JPEG', -53, 0, imgWidth, imgHeight)
          //   } else {
          //     while (leftHeight > 0) {
          //       pdf.addImage(dataUrl, 'JPEG', 0, position, imgWidth, imgHeight)
          //       leftHeight -= pageHeight
          //       position -= 841.89
          //       // 避免添加空白页
          //       if (leftHeight > 0) {
          //         pdf.addPage()
          //       }
          //     }
          //   }
          //   if (that.isSavePdf) { // 保存下载
          //     // const base64 = pdf.output('datauristring')
          //     var base64 = ''
          //     const sheets = window.luckysheet.getAllSheets()
          //     const filename = meta && meta.main && meta.main.name || sheets[0] && sheets[0].name
          //     const bizId = meta.main.id
          //     const buttonType = '保存PDF'
          //     exportExcel(sheets, filename, exportType, res => {
          //       base64 = res
          //       const file = that.$convertBase64ToFile(base64, meta.main.name)
          //       // 上传到服务器
          //       const formData = new FormData()
          //       formData.append('file', file)
          //       formData.append('path', '')// 文件存储路径
          //       formData.append('subId', '')// 业务系统编码
          //       formData.append('typeCode', '')// 类型编码
          //       formData.append('bizCode', '')// 模块编码
          //       formData.append('bizCodeName', '')// 模块名称
          //       formData.append('bizTblName', 'ATTACHMENT')// 业务表名称
          //       formData.append('bizId', meta.data.id)// 业务记录编码,业务表主键ID
          //       formData.append('isEsSearch', 'true')// 是否全文检索
          //       formData.append('fileComment', '')// 附件描述
          //       formData.append('attType', 'appPDF')// 附件类别
          //       formData.append('appendixType', '')// 附件类型
          //       formData.append('appendixTypeCode', '')// 附件类型编码
          //       formData.append('source', 'appPDF')// 来源
          //       that.$callApi('uploadAttachment', formData, result => {
          //         if (result.success) {
          //           if (that.$isNotEmpty(result.data)) {
          //             console.warn(result.data, '保存pdf')
          //           }
          //         }
          //         return true
          //       })
          //       that.isSavePdf = false
          //     }, buttonType,bizId)
          //
          //   } else {
          //     pdf.save(meta.main.name + '.pdf')
          //   }
          //
          //   // that.$nextTick(() => {
          //   $('#formPrintContainerGlobal').removeClass('showformPrintContainerGlobal')
          //   $('#formPrintContainerGlobal').addClass('hideformPrintContainerGlobal')
          //   $('#formPrintContainerGlobal').removeClass('regularformPrintContainerGlobal')
          //   // })
          //   if (that.isDesignFreeCform()) {
          //     that.onDlgClose()
          //   }
          //   $('.luckysheet').removeClass('hideLuckysheetBorder')
          //   if (that.onPrintDlgClose) {
          //     that.onPrintDlgClose()
          //     return
          //   }
          // })
        }
        // })
      }, 100)
    },
    pdfUrl(dataVo) {
      function dataURLtoBlob(dataurl) {
        var bstr = atob(dataurl)
        var n = bstr.length
        var u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        return new Blob([u8arr], { type: 'application/pdf' })
      }
      if (dataVo.extData.hasOwnProperty('pdfBase64') && this.$isNotEmpty(dataVo.extData.pdfBase64)) {
        this.showPdf = true
        const url = dataVo.extData.pdfBase64
        const _this = this
        const pdfBlob = dataURLtoBlob(url)
        const fileReader = new FileReader()
        fileReader.onloadend = () => {
          const arrayBuffer = fileReader.result
          const data = new Uint8Array(arrayBuffer)
          if (this.$isNotEmpty(data)) {
            // 渲染 PDF
            _this.$refs.iframePdf.renderPDF(data)
          }
        }
        fileReader.readAsArrayBuffer(pdfBlob)
      }
    },
    initByDataId(dataId, selectCformVoParams) { // 制单时详情
      this.isPreviewDetail = false // 隐藏宽高调整器
      this.keyId = dataId
      this.$nextTick(() => {
        // 这个处理实现在动态tab机制中支持动态设置tab的可见性
        var initFormExData
        var dynamicTabObj
        const failCallback = () => {
          this.printLoading?.close?.()
        }
        if (selectCformVoParams && selectCformVoParams['dynamic-tab']) {
          initFormExData = {}
          dynamicTabObj = selectCformVoParams['dynamic-tab']
          initFormExData['baseListFormObj'] = dynamicTabObj
          selectCformVoParams['dynamic-tab'] = undefined
          initFormExData.useFormRegularCompact =
            selectCformVoParams.useFormRegularCompact
        }

        this.$refs.formCanvasDetail.initByVersionDataId(
          '', dataId, '详情',
          (meta, dataVo) => {
            this.$nextTick(() => {
              this.pdfUrl(dataVo)
              this.params.dataVo = dataVo

              dataVo.extData.isDetailDlg = selectCformVoParams.isDetailDlg
              dataVo.extData.formType = selectCformVoParams.formType
              if (dynamicTabObj) {
                // 当前是全局弹框动态tab模式加载表单详情
                // 这时将设置页签标题和可见性的函数设置到dataVo中
                // 提供给详情弹框上其他页签内的组件使用
                dataVo.extData.tabNames = dynamicTabObj.tabNames
                dataVo.extData.setEditTabVisible = dynamicTabObj.setEditTabVisible
                dataVo.extData.setEditTabLabel = dynamicTabObj.setEditTabLabel
                if (dataVo.extData.detailTabData) {
                  if (this.$isNotEmpty(dataVo.extData.detailTabData.usedAmount)) {
                    dataVo.extData.setEditTabLabel('formDetail-详情',
                      `详情(当前节点：${dataVo.extData.detailTabData.currentNode}
                    ，申请金额：${dataVo.extData.detailTabData.applyAmount})
                    ，已支付金额：${dataVo.extData.detailTabData.usedAmount}`)
                  } else {
                    dataVo.extData.setEditTabLabel('formDetail-详情',
                      `详情(当前节点：${dataVo.extData.detailTabData.currentNode}
                    ，申请金额：${dataVo.extData.detailTabData.applyAmount}`)
                  }
                }
                this.$setDetailDlgAndAuditTabVisible(dataVo)

                // 详情加载完毕，尝试初始化附件，使得附件页签标题上有附件个数
                // 需要确保只自动初始化一次
                if (!dataVo.extData['fileTabHasBeenInited']) {
                  this.$nextTick(() => {
                    dataVo.extData['fileTabHasBeenInited'] = true
                    dynamicTabObj.tabClickByLabel('附件')
                  })
                }
              }
              this.initDlgSize(this.dlgRef, meta, 'isDetails')
            })
          }, undefined, undefined,
          undefined, initFormExData, selectCformVoParams, undefined, failCallback)
      })
    },
    initMeta(mode, meta, isInsertForm) { // 预览详情
      this.isPreviewDetail = true // 显示宽高调整器
      meta.needToReloadFreeJson = true
      this.meta = meta
      this.keyId = new Date().getTime()
      this.$nextTick(() => {
        // 第一次预览加载后，当前组件已加载一次自由表单，
        // 如果关闭预览再打开预览，此时form-free的方法loadJson会因为
        // 存储的标识数据表明已加载而不会再执行，导致第一次预览详情后
        // 后续的预览不能加载，这里置空loadedMode强制loadJson执行
        // this.$setProperty(this,
        //   'formFormat', 'loadedMode', '')
        this.$refs.formCanvasDetail.initMeta(mode, meta, isInsertForm)
      })
    },
    initDlgSize(dlg, meta, isDetails) {
      this.$nextTick(() => {
        const formType = this.$refs.formCanvasDetail.formatType
        // 自由表单自适应
        if (formType !== 'form-regular' && isDetails === 'isDetails') {
          // 处理详情页面弹窗宽高
          new Promise((resolve) => {
            const width = meta.instance.detailDlgWidth
            const height = meta.instance.detailDlgHeight
            this.$setDlgSize(dlg, 'globalDialog', width, height)
            resolve()
          }).then(() => {
            this.$nextTick(() => {
              // 通过设置任务在延迟0毫秒后执行,事件本身并没有延迟,但是能改变任务执行的先后顺序,改变它所调用的函数的优先级,使之异步执行。
              setTimeout(() => {
                const offsetWidth = $('#luckysheet-sheettable_0').width() - 10
                const offsetHeight = $('#luckysheet-sheettable_0').height() + 100
                this.$setDlgSize(dlg, 'globalDialog', offsetWidth, offsetHeight)
                $('#luckysheet-scrollbar-x').addClass('luckysheet-hidden-xhide')
                this.$forceUpdate()
              }, 0)
            })
          })
        } else {
          const width = meta.instance.detailDlgWidth + 40
          const height = meta.instance.detailDlgHeight + 40
          this.$setDlgSize(dlg, 'globalDialog', width, height)
        }
      })
    },
    sliderChange(meta) {
      this.initDlgSize(this.dlgRef, meta)
      if (this.$isNotEmpty(meta.main.id)) { // 修改时才调自动保存，自动保存不要提示保存成功
        this.$isNotEmpty(this.versionRelateRef) && this.versionRelateRef.btSave(() => {
          return true
        }, undefined, true)
      }
    },
    init(dlg, params) { // 详情弹框时的初始化
      this.showPdf = false
      this.dataVo = params.dataVo
      if (this.dataVo) {
        this.pdfUrl(this.dataVo)
      }
      this.dlgRef = dlg
      var billId = params.billId
      if (this.$isNotEmpty(billId)) {
        this.initByDataId(billId, params)
      }

      params.meta && this.$isNotEmpty(params.meta) && this.initMeta('详情', params.meta, false)
      params.meta && this.initDlgSize(dlg, params.meta, 'isDetails')

      if (params.toSaveRefs) {
        this.versionRelateRef = params.toSaveRefs
      }
      this.params = params
    },
    // TODO：导出WPS
    async showWPSExport(meta, exportType, buttonType) {
      await new Promise(async(resolve) => {
        const luckysheet = document.querySelector('.luckysheet')
        luckysheet.classList.add('hidePrintSheetBorder', 'hideLuckysheetBorder')
        const scrollbarX = document.querySelector('#luckysheet-scrollbar-x')
        const scrollbarY = document.querySelector('#luckysheet-scrollbar-y')
        scrollbarX.style.display = 'none'
        scrollbarY.style.display = 'none'
        const iconMoreBtnDiv = document.querySelector('#luckysheet-icon-morebtn-div')
        iconMoreBtnDiv.style.display = 'none'

        // 回显复选框和单选框
        const root = this.$refs.formCanvasDetail.$refs.formFormat.$refs.freeForm
        const customRadio = Array.from(root.querySelectorAll('.customRadio')) || []
        const customCheckbox = Array.from(root.querySelectorAll('.customCheckbox')) || []
        const customDom = [...customRadio, ...customCheckbox]
        for (const tar of customDom) {
          let { width, height, top, left } = tar.parentNode.style
          tar.style.background = 'transparent'
          width = parseFloat(width.split('px')[0]) - 6
          height = parseFloat(height.split('px')[0])
          top = parseFloat(top.split('px')[0])
          left = parseFloat(left.split('px')[0]) + 12

          const customDomUrl = await domtoimage.toPng(tar, {
            width: width, // 高度宽度自行设定
            height: height,
            quality: 1,
            scale: 2 // 一个像素转为几个像素
          })
          await new Promise((res) => {
            window.luckysheet.insertImageDIY(customDomUrl, {
              width,
              height,
              top,
              left,
              success: (id) => {
                res(id)
              }
            })
          })
        }
        const sheets = window.luckysheet.getAllSheets()
        const filename = meta && meta.main && meta.main.name || sheets[0] && sheets[0].name
        const ids = { bizId: meta.main.id, dataId: meta.data.id, versionId: meta.instance?.id }
        this.$nextTick(() => {
          setTimeout(function() {
            exportExcel(sheets, filename, exportType, resolve, buttonType, ids)
          }, 800)
        })
      })
      if (this.onPrintDlgClose) {
        this.onPrintDlgClose()
      }
      this.printLoading && this.printLoading.close()
      const formPrintContainerGlobal = document.querySelector('#formPrintContainerGlobal')
      formPrintContainerGlobal.classList.remove('showformPrintContainerGlobal')
      formPrintContainerGlobal.classList.add('hideformPrintContainerGlobal')
      formPrintContainerGlobal.classList.remove('regularformPrintContainerGlobal')
    }
  }
}
</script>

<style lang="scss">
</style>
