<template>
  <div>
    <div class="pdf-container">
      <canvas ref="canvas"></canvas>
      <div
        ref="overlay"
        class="overlay"
        :style="{ pointerEvents: dragImage ? 'auto' : 'auto' }"
        @dragover.prevent
      ></div>
      <div class="drag-container">
        <div
          class="drag-content"
          :class="{ 'dragging': dragImage }"
          :style="{ right: dragImageX + 'px', bottom: dragImageY + 'px' }"
          draggable="true"
          @dragstart="handleDragStart($event, item)"
          @dragend="handleDragEnd($event, item)"
        >
          <div class="drag-rect" ref="dragRect"></div>
          <div class="drag-image">
            <img v-if="isCircleStamp" :src="require('@/assets/' + officialSeal)"  />
            <img v-else :src="require('@/assets/' + personalSeal)"  :style="{ transform: 'scale(1)' }" />
          </div>
        </div>
      </div>
    </div>
    <div class="pagination">
      <button @click="prevPage" :disabled="currentPage === 1">上一页</button>
      <span>{{ currentPage }} / {{ numPages }}</span>
      <button @click="nextPage" :disabled="currentPage === numPages">下一页</button>

      <label for="toggleSwitch" style="margin-left: 240px">切换图片类型:</label>
      <button id="toggleSwitch" @click="toggleStampType" style="margin-left: 10px">
        {{ isCircleStamp ? '公'+'\u3000'+'章' : '个人章' }}
      </button>

    </div>
  </div>
</template>

<script>
import pdfjs from 'pdfjs-dist/build/pdf'
import 'pdfjs-dist/build/pdf.worker.entry'

export default {
  name: 'showPdf',
  data() {
    return {
      officialSeal: '公章.gif',
      personalSeal: '个人章.gif',
      pdfInstance: null,
      currentPage: 1,
      numPages: 0,
      scale: 1,
      currentPageCoordinates: null,
      dragImage: false,
      dragImageX: 0,
      dragImageY: 0,
      startX: 0,
      startY: 0,
      isCircleStamp: false,
      pageX: '',
      pageY: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.renderPDF()
    })
  },
  methods: {
    toggleStampType() {
      this.isCircleStamp = !this.isCircleStamp
      this.$emit('toggle-stamp-type', this.isCircleStamp)
      if (this.isCircleStamp) {
        // 切换到公章时修改样式
        this.$refs.dragRect.style.width = '80px'
        this.$refs.dragRect.style.height = '80px'
      } else {
        // 切换到个人章时恢复原样式
        this.$refs.dragRect.style.width = '80px'
        this.$refs.dragRect.style.height = '50px'
      }
    },
    renderPDF(data) {
      const canvas = this.$refs.canvas
      const overlay = this.$refs.overlay

      // 设置 pdf.js 的 worker 路径
      pdfjs.GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.js')

      // 创建 Uint8Array 对象
      const pdfData = new Uint8Array(data)

      // 获取 PDF 文档
      pdfjs.getDocument(pdfData).promise.then((pdf) => {
        this.pdfInstance = pdf
        this.numPages = pdf.numPages

        pdf.getPage(1).then((page) => {
          const viewport = page.getViewport({ scale: 1.1 })
          const a4Width = 595 // A4纸的宽度
          const a4Height = 842 // A4纸的高度
          // 计算缩放比例
          const scaleX = a4Width / viewport.width
          const scaleY = a4Height / viewport.height
          this.scale = Math.min(scaleX, scaleY)
          // 渲染当前页
          this.renderPage(this.currentPage, canvas, overlay)
        })
      })
    },
    renderPage(pageNumber, canvas, overlay) {
      this.pdfInstance.getPage(pageNumber).then((page) => {
        const viewport = page.getViewport({ scale: this.scale })

        const context = canvas.getContext('2d')
        canvas.height = viewport.height
        canvas.width = viewport.width

        page.render({
          canvasContext: context,
          viewport
        })

        overlay.style.width = `${viewport.width}px`
        overlay.style.height = `${viewport.height}px`

        const pageWidth = viewport.width
        const pageHeight = viewport.height
        this.currentPageCoordinates = {
          pageWidth,
          pageHeight
        }
      })
    },
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--
        this.renderPage(this.currentPage, this.$refs.canvas, this.$refs.overlay)
      }
    },
    nextPage() {
      if (this.currentPage < this.numPages) {
        this.currentPage++
        this.renderPage(this.currentPage, this.$refs.canvas, this.$refs.overlay)
      }
    },
    updateCurrentPage(pageNumber, signatureType) {
      if (signatureType === '公章') {
        this.isCircleStamp = true
      } else {
        this.isCircleStamp = false
      }
      this.currentPage = pageNumber
      this.renderPage(this.currentPage, this.$refs.canvas, this.$refs.overlay)
    },
    handleDragStart(event, item) {
      this.startX = event.clientX
      this.startY = event.clientY
      event.dataTransfer.setData('text/plain', '')
      this.dragImage = true
    },
    handleDragEnd() {
      this.dragImageX = parseFloat(this.dragImageX)
      this.dragImageY = parseFloat(this.dragImageY)
      const { height: overlayHeight, width: overlayWidth } = this.$refs.overlay.getBoundingClientRect()
      const { width: tarWidth, height: tarHeight } = this.$refs.dragRect.getBoundingClientRect()
      const x = event.clientX
      const y = event.clientY

      let imageX = this.dragImageX + (this.startX - x)
      let imageY = this.dragImageY + (this.startY - y)
      if (imageX < 0) {
        imageX = 0
      } else if (imageX > overlayWidth - tarWidth) {
        imageX = overlayWidth - tarWidth
      }
      if (imageY < 0) {
        imageY = 0
      } else if (imageY > overlayHeight - tarHeight) {
        imageY = overlayHeight - tarHeight
      }

      this.dragImageX = imageX
      this.dragImageY = imageY
      this.startX = x
      this.startY = y

      this.$emit('getPdfPosition', {
        pageX: Math.round(parseFloat(imageX).toFixed(2)),
        pageY: Math.round(parseFloat(imageY).toFixed(2)),
        currentPage: this.currentPage,
        signatureType: this.isCircleStamp ? '公章' : '个人章'
      })
      this.dragImage = false
    }
  }
}
</script>

<style  scoped>

  .pdf-container {
    position: relative;
    border: 1px solid #bbb;
    display: inline-block;
    margin: 10px;
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    background-color: transparent;
    pointer-events: none;
  }

  .drag-container {
    position: relative;
  }

  .drag-content {
    position: absolute;
    cursor: move;
    right: 0;
    bottom: 0;
    transition: transform 0.2s ease;
    transform: translateZ(0);
  }
  .drag-rect {
    width: 80px;
    height: 50px;
    border: 2px solid #F56C6C; /* Rectangle border style */
  }
  .drag-image {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .drag-image img {
    display: block;
    max-width: 100%;
    max-height: 100%;
  }

  .pagination {
    margin-left: 10px;
  }

  button:disabled {
    opacity: 0.5;
  }
</style>
