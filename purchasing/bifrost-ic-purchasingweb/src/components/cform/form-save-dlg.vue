<template>
    <el-dialog ref="formSaveDlg"
       :title='title'
       append-to-body
       class="common-dlg-form"
       :close-on-click-modal="false"
       :visible.sync="dlgVisible">
        <page>
            <template #pageContent>
                <b-page ref="basePageFormSave">
                    <template #mainContent>
                        <div :id="divId" class="formCanvasRuntime" style="height: 100%">
                            <form-canvas ref="formCanvas"/>
                        </div>
                    </template>
                    <template #toolbarExtend>
                        <component ref="compToolbarExtend" :is="compToolbarExtendId"/>
                    </template>
                </b-page>
            </template>
        </page>
    </el-dialog>
</template>
<script>
import $ from 'jquery'
import BPage from '../page/base-page'
export default {
  name: 'form-save-dlg',
  components: { BPage },
  data() {
    return {
      divId: 'formSaveDlg' + new Date().getTime() + '',
      title: '保存表单',
      dlgVisible: false,
      versionId: undefined,
      dataId: undefined,
      printId: undefined, // 一些场景printId!=dataId，比如合同法律意见书
      exParams: undefined,
      compToolbarExtendId: '' // 按钮后的扩展功能
    }
  },
  methods: {
    show(versionId, dataId, dlgWidth, dlgHeight, exParams, buttons) {
      this.versionId = versionId
      this.dataId = dataId

      this.dlgVisible = true
      this.$setDlgSize(this, 'formSaveDlg', dlgWidth, dlgHeight)
      this.initBtEditEvents()

      this.$nextTick(() => {
        exParams = exParams || {}
        this.exParams = exParams

        buttons = buttons || []
        var keys = Object.keys(this.exParams)
        if (keys.indexOf('noPrint') === -1) { // 没有设置noPrint，即有打印
          var click = bt => {
            if (this.$isNotEmpty(this.printId)) {
              this.$showExportWps(this.getParams4PrintOrPdf(), '表单模板', '', '打印')
            }
          }

          // 打印按钮初始都是不可用，返回表单数据之后再决定是否可用
          var printButton = {
            text: '打印', icon: 'el-icon-printer', enabledType: '1', click: click }
          buttons = [printButton].concat(buttons)

          // 导出PDF star
          const ExportPDFClick = bt => {
            if (this.$isNotEmpty(this.printId)) {
              this.$showExportWps(this.getParams4PrintOrPdf(), '表单模板', '', '导出')
            }
          }

          const PDFButton = {
            text: '导出PDF', icon: 'el-icon-download', enabledType: '1', click: ExportPDFClick }
          buttons = [PDFButton].concat(buttons)
        // 导出PDF end
        }

        if (this.$isNotEmpty(this.exParams.compToolbarExtendId)) {
          this.compToolbarExtendId = this.exParams.compToolbarExtendId
        }
        this.$refs.basePageFormSave.init({ buttons: buttons })
        if (this.$isEmpty(buttons)) {
          this.$refs.basePageFormSave.setButtonBarVisible(false, true)
        } else {
          var $formCanvasParent = $('#' + this.divId)
          $formCanvasParent.closest('.el-dialog__body').css('padding-top', '0px')
        }

        this.initFromContent()
        this.$nextTick(() => {
          // $('#' + this.divId).closest('.el-dialog__body').css('padding-bottom', '0px')
          setTimeout(() => {
            $('#' + this.divId).find('footer.el-footer').css('padding-right', '0px')
          }, 300)
          this.$requestAnimationFrame(() => {
            // 刷新表单视图
            this.$requestAnimationFrame(() => {
              const sheetDom = $('#formFreeDetail').find('.luckysheet').length === 0 ? $('#formFreeFill').find('.luckysheet') : $('#formFreeDetail').find('.luckysheet')
              if (sheetDom.length !== 1) {
                sheetDom.last().remove()
              }
              this.$refs.formCanvas.$refs.formFormat.resizeClient()
            }, 300)
          }, 1000)
        })
      })
    },
    getParams4PrintOrPdf() {
      var params4PrintOrPdf = Object.assign({}, this.exParams)
      return Object.assign(
        params4PrintOrPdf, {
          printId: this.printId,
          onPrintDlgClose: this.initFromContent })
    },
    initFromContent() {
      this.$nextTick(() => {
        var isReadonly = this.$isNotEmpty(this.exParams.isDisablefooter)

        var callback = (meta, dataVo) => {
          this.printId = dataVo.data.id // 记录id用于打印
          var printDisabled = this.$isEmpty(this.printId)
          this.$refs.basePageFormSave.setBtProperty('打印', 'disabled', printDisabled)
          this.$refs.basePageFormSave.setBtProperty('导出PDF', 'disabled', printDisabled)

          // 是否隐藏底部footer
          if (isReadonly) {
            this.$refs.formCanvas.$refs.formFormat.$refs.formEditDetail.showButtons = false
            this.$requestAnimationFrame(() => {
              $('.common-dlg-form #form-edit-detail-el-footer').hide()
            }, 100)
          } else {
            this.$refs.formCanvas.$refs.formFormat.$refs.formEditDetail.showButtons = true
            $('.common-dlg-form #form-edit-detail-el-footer').show()
          }

          if (this.$refs.compToolbarExtend &&
            this.$refs.compToolbarExtend.init) {
            this.$refs.compToolbarExtend.init(this, dataVo)
          }
        }

        var mode = isReadonly ? '详情' : '制单'
        var isEditing = !isReadonly
        this.$refs.formCanvas.initByVersionDataId(
          this.versionId, this.dataId, mode,
          callback, undefined,
          isEditing, undefined,
          { overDialogZindex: 3000 }, this.exParams)
      })
    },
    initBtEditEvents() {
      var this_ = this
      this.$onEvent(this, {
        btEditBack() {
          this_.dlgVisible = false
        },
        btEditSave(dataVo) {
          var callbackSuccess = (result) => {
            if (this_.exParams.callbackAfterSaved) {
              this_.exParams.callbackAfterSaved(this_, result)
            }
            this_.dlgVisible = false
          }

          var callbackFailed = result => {
            // formCanvas处理了错误之后，就不再弹出通用的错误提示
            return this_.$refs.formCanvas.showError(result)
          }
          this_.$callApiConfirm(
            '确定执行保存吗?', undefined,
            'WFSAVE&dataType=CformDataEntity', dataVo,
            callbackSuccess, callbackFailed)
        }
      })
    }
  }
}
</script>
