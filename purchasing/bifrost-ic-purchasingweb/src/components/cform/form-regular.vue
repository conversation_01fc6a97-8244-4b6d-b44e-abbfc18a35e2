<template>
    <div :id="formRegularId" class="formRegularContainer">
      <form-edit-detail :isLoading="loading" ref="formEditDetail">
        <template #formMainContent>
            <div style="width:100%;height:100%;" class="free-container">
                <div style="flex:1" id="formFreeView"
                     v-loading="loading"
                     element-loading-spinner="el-icon-loading"
                     element-loading-text="加载中...">
                    <el-tabs class="formCanvasTab hideFormCanvasTab"
                        style="width: 100%;height: 100%;"
                         v-model="formCanvasActiveTab"
                         @tab-click="formCanvasTabClick">
                        <el-tab-pane :key="formCanvasFirstTab"
                                     :label="formCanvasFirstTab" :name="formCanvasFirstTab">
                            <div class="formCommonHeader" :style="headerStyle" v-show="showHeader">
                                <div class="formCommonTitle">
                                    <span><span>{{title}}</span></span>
                                </div>
                                <div class="formRegularListLabel" >
                                  <div v-for="(item) in formExtraItemData" :key="item.id" class="listItem"><span class="listItemLabel">{{item.label}}：</span><span class="listItemValue">{{item.value}}</span></div>
                                </div>
                            </div>
                            <!-- 介于表单标题和表单的动态组件 -->
                            <formCustomContent ref="formCustomContent" :meta="meta" :mode="mode"/>
                            <!-- :class="{formRegularNotYPadding: formRegularNotYPadding}"  -->
                            <div class="formCommonCols formRegularColbottomBorber" ref="formCommonCols" @click="blockCformAreaClick">
                                <form-create v-model="fcModel" :rule="fcRule" :option="fcOption">
                                    <template slot="type-field-component" slot-scope="scope">
                                        <textarea class="form-create-textarea el-textarea__inner"
                                                  v-model="scope.model.value"
                                                  :readonly="formatTextareaReadonly(scope)"
                                                  :disabled="formatTextareaDisabled(scope)"
                                                  @input="textareaInput(scope)"
                                                  @change="textareaChange(scope)"
                                                  @blur="textareaBlur(scope)"
                                                  id="regularCheckBox" :rows="formatTextareaRow(scope)" />
                                    </template>
                                    <template slot="type-hyperlink" slot-scope="scope">
                                      <a href="javascript:void(0);" @click="checkDetails(scope,index)" v-for="(item,index) in scope.model.value" :key="index">
                                        {{item}}
                                      </a>
                                    </template>
                                </form-create>
                            </div>
                            <div class="formCommonSettingExtra formRegularSettingExtra" v-if="(isDesignMode || isPreviewEdit) && !hideSilder">
                                <div class="formCommonSettingExtraSlider">
                                    <span class="formCommonSettingExtraSliderLabel">标题顶部间隔</span>
                                    <el-slider v-model="headerPaddingTop" @change="sliderDebounce" :min="0" :max="100" />
                                </div>
                                <div class="formCommonSettingExtraSlider">
                                    <span class="formCommonSettingExtraSliderLabel">标题底部间隔</span>
                                    <el-slider v-model="headerHeight" @change="sliderDebounce" :min="80" :max="300" />
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane :key="tab.name" :label="tab.title"
                            :name="tab.name" v-for="tab in formCanvasExTabs">
                            <keep-alive>
                                <component ref="formCanvasTabComponents" :is="tab.name"/>
                            </keep-alive>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <div class="mini-table">
                    <right-collapsed-container
                            ref="rightCollapsedContainer"
                            v-if="isRightCollapsed && isFillFormMode && hasExtAssembly"
                            :isRightCollapsed='isAuditMode?true:false'>
                        <template #content>
                            <slot name="content">
                            </slot>
                        </template>
                    </right-collapsed-container>
                </div>
            </div>
        </template>
      </form-edit-detail>
      <hyperlinkDialog ref="hyperlinkDialog"></hyperlinkDialog>
    </div>
</template>

<script>
import $ from 'jquery'
import ColSetting from './col-setting'
import FormEditDetail from '../list/form-edit-detail'
import { formConfig } from './js/form-config.js'
export default {
  name: 'form-regular',
  components: { FormEditDetail, ColSetting },
  inject: {
    hideElementArea: { default: undefined },
    handleElement: { default: undefined },
    hideColSetting: { default: false },
    clearCurrentSelectedRForm: { default: false },
    changeRFormTableHeaderLabel: { default: undefined },
    changeBlockLoading: { default: undefined },
    changeElementConfigDisabled: { default: undefined },
    clearRformConfig: { default: undefined },
    changeCurrentBlockId: { default: undefined },
    initBlockView: { default: undefined }
  },
  props: {
    isPreviewDetail: { type: Boolean, default: false },
    hasExtDataAssembly: { type: Boolean, default: false },
    hideSilder: { type: Boolean, default: false }
  },
  watch: {
    fcRule: {
      handler() {
        const showColMap = {}
        this.fcRule.map(item => {
          if (item.type !== 'hidden') {
            showColMap[item.field] = { ...item.colItem }
          }
        })
        window.$event.$emit('fcRuleChange', showColMap)
      }
    }
  },
  data() {
    return {
      loading: true,
      formRegularId: 'formRegular' + new Date().getTime() + '',
      title: '', // 表单标题
      isClosingRefDlg: false,
      isPreviewEdit: false, // 是否正在预览
      headerPaddingTop: 0,
      headerHeight: 0,
      mode: '', // "设计", "制单", "详情"
      fcModel: {},
      fcRule: [],
      fcRulePropColMap: {},
      fcOption: { submitBtn: false, resetBtn: false },
      colItems: [],
      hasExtAssembly: this.hasExtDataAssembly,
      meta: {},
      cformSettings: [], // “更多设置”中的表单设置数据
      formExtend: undefined,
      exData: {}, // 用于存储与扩展相关的数据
      allColItemsMap: {},
      colItemsMap: {},
      isFormCreateTextarea: false, // Textarea禁用
      sliderTimer: null,
      formCanvasFirstTab: '',
      formCanvasActiveTab: '',
      formCanvasExTabs: [], // 画布额外的Tab数据：[{name:'', title:''}]
      formCanvasTabIndexs: {},
      dataVo: undefined,
      formExtraItemData: [],
      showHeader: true,
      isLoadedFree: false,
      isRightCollapsed: true,
      filterParams: {},
      blockParams: {},
      // 初始化时,所有要素为日期的(编辑时日期范围不生效问题)
      datePickerColItems: [],
      errorTip: {}
      // formRegularNotYPadding: false
    }
  },
  computed: {
    headerStyle() {
      return `height:${this.headerHeight}px;padding-top:${this.headerPaddingTop}px;`
    },
    isDesignMode() { // 判断当前是否是设计模式
      return (this.mode === '设计')
    },
    isDetailMode() { // 判断当前是否是详情
      return (this.mode === '详情' || this.isAuditMode)
    },
    isAuditMode() { // 判断当前是否是审核
      return (this.mode === '审核')
    },
    isFillFormMode() { // 判断当前是否是制单模式
      return (this.mode === '制单')
    }
  },
  methods: {
    removeBrackets(str) {
      return str[0]
    },
    getFcRulePropColMap(label) {
      return this.fcRulePropColMap[label]
    },
    // 超链接查看详情
    checkDetails(item, index) {
      this.$refs.hyperlinkDialog.init(item, index)
    },
    showBtDraft(isShowBtDraft) {
      this.$refs.formEditDetail.hideBtDraft = !isShowBtDraft
    },
    setBtDraftLoading(isLoading) {
      this.$refs.formEditDetail.setBtDraftLoading(isLoading)
    },
    formCanvasTabClick(tab, result, errorNum, isRefresh = false) { // 画布存在多个tab时，tab点击的响应处理
      var tabName = tab.name
      var tabObj = this.getFormCanvasTabComponent(tabName)
      if (this.$isNotEmpty(tabObj) && tabObj.init) {
        this.$nextTick(() => !isRefresh && tabObj.init(this.dataVo))
        tabObj.showError(result, errorNum)
      }
    },
    activitedTabClick(result, errorNum, isRefresh) {
      if (this.$isNotEmpty(result.data) && this.$isNotEmpty(this.formCanvasExTabs)) {
        this.formCanvasActiveTab = this.formCanvasExTabs[0].name
      } else {
        this.formCanvasActiveTab = this.formCanvasFirstTab
      }
      this.formCanvasTabClick({ name: this.formCanvasActiveTab }, result, errorNum, isRefresh)
    },
    getFormCanvasTabComponent(tabName) { // 获取表单额外Tab的对象
      var index = this.formCanvasTabIndexs[tabName]
      if (this.$isNotEmpty(index)) {
        if (parseInt(index) === 0) {
          return this
        } else {
          return this.$refs.formCanvasTabComponents[index - 1]
        }
      }
    },
    sliderDebounce() { // 避免多次请求减少资源开销
      if (this.sliderTimer) {
        clearTimeout(this.sliderTimer)
      }
      this.sliderTimer = setTimeout(() => {
        this.sliderChange()
      }, 2000)
    },
    formatTextareaReadonly(scope) {
      const { prop } = scope
      return prop.props.readonly
    },
    formatTextareaDisabled(scope) {
      const { prop } = scope
      return prop.props.disabled
    },
    formatTextareaRow(scope) {
      const { prop } = scope
      let rowNum = 1
      if (prop.col) {
        if (prop.col.span === 24) {
          rowNum = prop.props.rows ? prop.props.rows : 3
        } else {
          rowNum = 1
        }
      }
      return rowNum
    },
    textareaChange(scope) {
      // textarea要素需要单独触发值变化回调函数
      this.runColItemModifiedCallbacksBy(scope.prop.field)
    },
    textareaInput(scope) {
      scope.rule.value = scope.model.value
    },
    setColItemXY(colItem) { // 规则表单XY都是0，仅用于区分是否是自动要素
      colItem.rowIndex = 0
      colItem.columnIndex = 0
    },
    selectchange(inject, value, label) {
      var itemValue = value
      var itemConfig = inject.inject
      this.selectcSetUp(itemConfig, itemValue)

      // 触发全局要素值变化的回调函数
      this.runColItemModifiedCallbacks(inject, itemValue)
      if (inject.self.colItem && inject.self.colItem.colType === '金额') {
        inject.self.value = this.$formatMoney(value)
      }
      if (label && this.fcRulePropColMap[label]) { // 规则提示处理
        this.verifyDate(this.fcRulePropColMap[label])
        const options = this.fcRulePropColMap[label].options || []
        for (let i = 0; i < options.length; i++) {
          const item = options[i]
          const optionRemind = item.remindInfo
          if (optionRemind && item.value === value) {
            const remindInfo = {}
            remindInfo[optionRemind.formFieldName] = optionRemind.remindMessage
            this.setPostil(remindInfo)
            return
          }
        }
      }
    },
    // 编辑时日期范围不生效问题
    formLoadedVerifyDate() {
      this.datePickerColItems?.forEach(col => this.verifyDate(this.fcRulePropColMap[col.label]))
    },
    verifyDate(rule = {}) { // 校验开始时间和结束时间可选范围
      if (!rule.field) {
        return
      }
      const colItem = this.colItemsMap[rule.field]
      const cType = rule.type
      if (this.$isEmpty(colItem) || colItem.colType !== '日期' || !(cType === 'DatePicker')) {
        return
      }
      const keysMap = {
        '开始': '结束',
        '结束': '开始'
      }
      const startMap = {
        '开始': true
      }
      const objKeys = Object.keys(keysMap)

      for (let i = 0; i < objKeys.length; i++) {
        const isIncludes = colItem.label.includes(objKeys[i])
        if (!isIncludes) {
          continue
        }
        const keywords = colItem.label.split(objKeys[i])
        const firstKey = keywords[0] || ''
        const endKey = keywords[1] || ''
        const relatKey = firstKey + keysMap[objKeys[i]] + endKey
        const relatColItem = this.fcRulePropColMap[relatKey]
        const isStart = startMap[objKeys[i]]
        let dateValue = ''
        if (rule.value) {
          dateValue = new Date(rule.value).getTime() || ''
          dateValue = isStart ? (dateValue - 86400000) : dateValue
        }
        if (!relatColItem) {
          continue
        }
        if (!relatColItem.props) {
          this.$set(relatColItem, 'props', {})
        }
        if (!relatColItem.props.pickerOptions) {
          this.$set(relatColItem.props, 'pickerOptions', {})
        }

        const disabledDateFn = (time) => {
          if (isStart) {
            return time.getTime() < dateValue
          }
          return time.getTime() > dateValue
        }
        const disabledDate = dateValue ? disabledDateFn : ''
        this.$set(relatColItem.props.pickerOptions, 'disabledDate', disabledDate)
      }
    },
    selectcSetUp(itemConfig, itemValue) {
      var itemSetting = itemConfig.itemSetting
      if (this.$isNotEmpty(itemSetting)) {
        for (var i = 0; i < itemSetting.length; i++) {
          if (itemSetting[i].itemValue === itemValue) {
            for (var key in itemSetting[i].disabled) {
              this.fcModel.disabled(itemSetting[i].disabled[key], key)
              if (itemSetting[i].disabled[key]) {
                this.fcModel.setValue(key, null)
              }
            }
            break
          }
        }
      }
      // 触发事件发送到页面右边扩展组件，组件名如：“合同”
      // if (this.$isNotEmpty(itemConfig.triggerAssemblyEvent) && itemConfig.triggerAssemblyEvent) {
      this.$nextTick(() => {
        if (!this.isDesignMode && this.formExtend && this.formExtend?._data?.formFormat?._data?.dataVo?.extData?.hasOwnProperty('签署备案')) {
          return
        }
        if (this.meta.extData) {
          if (!(this.meta.extData[`制单样式`] === '分tab')) {
            this.$parent.$refs.extDataAssembly.triggerAssemblyEvent(itemConfig.itemName, itemValue, this.fcModel)
          } else {
            var formEditTabsComponents = this.$parent && this.$parent.$parent &&
            this.$parent.$parent.$parent &&
            this.$parent.$parent.$parent.$parent
              ? this.$parent.$parent.$parent.$parent.formEditTabsComponents : []
            if (this.$isNotEmpty(formEditTabsComponents)) {
              for (var tabName in formEditTabsComponents) {
                var comp = formEditTabsComponents[tabName]
                if (comp && comp.triggerAssemblyEvent) {
                  comp.triggerAssemblyEvent(itemConfig.itemName, itemValue, this.fcModel)
                }
              }
            }
          }
        }
      })
      // }
      // 触发事件发送到表单扩展组件，组件名如：“formExtend-合同”
      // if (this.$isNotEmpty(itemConfig.triggerExtendEvent) && itemConfig.triggerExtendEvent) {
      if (this.formExtend) {
        if (this.formExtend.triggerExtendEvent) {
          this.$nextTick(() => {
            this.formExtend.triggerExtendEvent(itemConfig.itemName, itemValue, this.fcModel)
          })
        }
      }
      this.blockParams?.triggerExtendEvent?.(itemConfig.itemName, itemValue, this.fcModel)
      // }
    },
    removeColItem(colItem) {
    },
    sliderChange() {
      this.$emit('sliderChange', this.headerPaddingTop, this.headerHeight)
    },
    getFormatJson() {
      return ''
    },
    fillFormData(colItems, isDataToSave, updateAfter) { // 获取表单当前填写的数据
      var hasFormData = false
      var colItemsMap = {}
      colItems.forEach(item => {
        colItemsMap[item.label] = item
      })
      // 填数据(自定义内容的数据填充到dataVo当中)
      this.$refs.formCustomContent.fillFormData(this.dataVo)
      this.$nextTick(() => {
        this.fcModel.submit((formData, fApi) => {
          Object.entries(formData).map(item => {
            var value = item[1]
            const colItem = colItemsMap[item[0]]
            if (value instanceof Array) {
              value = value.join(',')
            }
            if (colItem) {
              colItem.dataValue = value
              if (colItem.colType === '金额') {
                colItem.dataValue = this.$fixMoney(value)
              }
              if (value === undefined) {
                colItem.dataValue = ''
              }
            }
            hasFormData = true
          })
          updateAfter && updateAfter() // 用于区块表单数据不同步问题
        })
      })
      return hasFormData
    },
    colItemsChange(colItems, isOpeningCanvas) { // 要素设置变化事件
      // 这个方法只在规则格式设计时使用
      // 每次刷新设计画布，先暂存当前填写的要素值，
      // 然后将这次值填入到将要新建的画布内，实现刷新后要素值依然存在
      var isNew = false
      if (isOpeningCanvas) {
        isNew = true
      } else {
        var hasFormData = this.fillFormData(colItems)
        isNew = !hasFormData
      }
      this.createForm(colItems, isNew)
    },
    addColItemModifiedCallbacks(callbacks) {
      this.$addFormActionCallbacks(
        this, 'colItemModifiedCallbacks', callbacks)
    },
    textareaBlur(scope) {
      const label = scope.prop?.field
      const value = scope.prop?.value
      if (label && this.colItemsMap[label]) {
        this.verifyRequired(this.colItemsMap[label], value)
      }
    },
    verifyRequired(colItem, itemValue, selectedData = []) {
      const label = colItem.label
      const isRequired = colItem.isRequired || false
      if (isRequired && !itemValue && !selectedData.length) {
        this.$set(this.errorTip, label, `${label}: 此项不能为空`)
        this.showErrorTip()
      } else {
        this.removeError(label)
      }
    },
    removeError(name) {
      var $colItems = $(this.$refs.formCommonCols).find(`.el-error.err-message-${name}`)
      $colItems.removeClass('el-error')
    },
    inputBlur(inject) {
      const itemConfig = inject.inject || {}
      const itemName = itemConfig.itemName
      if (itemName && this.colItemsMap[itemName]) {
        this.verifyRequired(this.colItemsMap[itemName], inject.self?.value)
      }
    },
    runColItemModifiedCallbacks(inject, itemValue, selectedData) {
      var itemConfig = inject.inject || {}
      var itemName = itemConfig.itemName
      if (itemName === undefined) {
        this.$message.error('回调要素变化函数时，不能获取到要素名称')
        return
      }
      const colItem = this.colItemsMap[itemName]
      if (colItem) {
        const excludedChangeVerify = {
          'input': true,
          'fieldComponent': true
        }
        const type = this.fcRulePropColMap[itemName]?.type
        if (type && (!excludedChangeVerify[type] || colItem.colType === '弹框')) { // input、textarea在blur时处理
          this.verifyRequired(colItem, itemValue, selectedData)
        }
        var colItemCopy = this.$cloneDeep(colItem)
        colItemCopy.dataValue = itemValue

        var callbacks = this.$getFormActionCallbacks(
          this, 'colItemModifiedCallbacks')
        callbacks.forEach(cb => cb(colItemCopy, selectedData))
      }
    },
    runColItemModifiedCallbacksBy(label, selectedData = []) {
      // selectedData 弹框选择时会传递当前行选择的数据， 注意其他情况为空
      var itemConfig = { itemName: label }
      var inject = { inject: itemConfig }
      this.runColItemModifiedCallbacks(inject, this.getValue(label), selectedData)
    },
    initFormExtend(formExtendObj) {
      this.filterParams = {}
      this.formExtend = formExtendObj
    },
    clearRefData(item, isClear = true) {
      if (!isClear) return
      this.fcModel.setValue(item.label, null)
      this.fcModel.setValue(item.labelOrigin, null)
      this.fcModel.setValue(item.labelOrigin + 'ID', null)
      item.dataValue = ''
      this.clearAfterReloadFRule(item)
      var clearAfterFun = (params) => {
        this.clearAfterReloadFRule(params)
      }

      // if (this.formExtend) {
      //   if (this.formExtend.clearRefDataExr && !this.isAuditMode) {
      //     this.formExtend.clearRefDataExr(item, this.fcModel, this.colItemsMap, clearAfterFun)
      //   }
      // }
      if (!this.isAuditMode) {
        this.formExtend?.clearRefDataExr?.(item, this.fcModel, this.colItemsMap, clearAfterFun)
        this.blockParams?.clearRefDataExr?.(item, this.fcModel, this.colItemsMap, clearAfterFun)
      }
    },
    // 合同变更 合同续签 兼容区块合同与项目口径管理 参照弹窗
    defaultReference(colItems, initFormExData) {
      var params = {}
      if (initFormExData?.baseListFormObj?.refFormParams === undefined) {
        return
      }
      if (initFormExData.baseListFormObj.refFormParams['是否弹出默认参照']) {
        const showDefaultReference = initFormExData.baseListFormObj.dataVo.cformSettings.filter(item => item.optionName === '默认参照设置')
        const cformSettings = showDefaultReference[0] || {}
        // 弹出默认参照
        if (cformSettings.extraData) {
          colItems.forEach(item => {
            if (item.label === cformSettings.extraData) {
              const refData = Object.assign(cformSettings, item)
              if (this.$isNotEmpty(this.meta.main)) {
                params['formId'] = this.meta.main.id
              }
              params = {
                ...params,
                ...initFormExData?.baseListFormObj?.refFormParams,
                colItemId: item.id,
                formType: this.meta.main.formType,
                doNotFillRelateColItem: this.isAuditMode, // 审核时修改参照要素，不要修改关联要素
                isRelatedRefIncludeDept: false,
                formExtendExData: {},
                updateParamsCallback: (obj) => {
                  obj.checkedData.push(item.realValueRefID.split(' ')[0])
                },
                multiple: !!item.isMultipleRef,
                onDlgClose: (params) => {
                  // 如果表单参照不进行参照，则直接退出制单界面返回到列表
                  if (params.closeWhetherToReturn === true && params.isOK !== true) {
                    this.$event(this, 'btEditBack', false)
                  }
                }
              }
              if (this.formExtend) {
                if (this.formExtend.isRelatedRefIncludeDept) {
                  params.isRelatedRefIncludeDept =
                    this.formExtend.isRelatedRefIncludeDept(item)
                }
                params.formExtendExData = this.formExtend.exData
              }
              return this.$refData(colItems, refData, () => { }, this.setValue, () => {
              }, (value) => {
                if (initFormExData.baseListFormObj.blockView.containers.length > 1) { // 判断是否是多tab页
                  this.initBlockView?.({
                    viewId: initFormExData.baseListFormObj.blockView.bizid,
                    versionId: initFormExData.baseListFormObj.dataVo.version.bizid,
                    refId: value.list[0].id,
                    formType: value.list[0].formType,
                    dataId: value.list[0].id,
                    isRef: true,
                    refFormSelectedData: value
                  })
                } else {
                  this.initBlockView?.({
                    viewId: value.list[0].viewId,
                    versionId: value.list[0].metaVersionId,
                    billId: value.list[0].id,
                    formType: value.list[0].formType,
                    dataId: value.list[0].id,
                    isRef: true,
                    refFormSelectedData: value
                  })
                }
              }, params)
            }
          })
        }
      }
    },
    // 生成表单，isNew标识是否新增表单实例，不是表单
    createForm(colItems, isNew, isPrintDetails, initFormExData) {
      this.sourceType = initFormExData?.jumpToSaveFormData?.sourceType || initFormExData?.blockParams?.jumpToSaveFormData?.sourceType || ''
      // 是否弹出默认参照
      if (this.mode === '制单' && isNew) {
        this.defaultReference(colItems, initFormExData)
      }
      // 区块表单 没有要素则隐藏Y轴的padding
      // if (this.hideColSetting) {
      //   this.formRegularNotYPadding = !colItems.length
      // }
      // colItems.forEach(item => {
      //   if (item.label === '测试隐藏框') {
      //     this.$message.success(item.dataValue)
      //   }
      // })

      this.isLoadedFree = false
      initFormExData = initFormExData || {}
      // 区块扩展相关
      this.blockParams = initFormExData.blockParams || {}
      if (initFormExData.showHeader !== undefined) {
        this.showHeader = initFormExData.showHeader
      }

      if (initFormExData.showEditButtons === undefined) {
        this.showHeader = true
      }

      // 搬到form-canvas中
      // if (initFormExData.showEditButtons !== undefined) {
      //   this.$refs.formEditDetail.showButtons = initFormExData.showEditButtons
      // } else {
      //   this.showHeader = true
      //   if (!this.isDesignMode) {
      //     this.$refs.formEditDetail.showButtons = true
      //   }
      // }

      // formRegularCompact
      var $formRegularRootContainer = $('#' + this.formRegularId)
      var $formFreeView = $formRegularRootContainer.find('#formFreeView')
      $formFreeView.removeClass('noHeader')
      if (!this.showHeader) {
        $formFreeView.addClass('noHeader')
      }

      // 是否使用缩小的规则表单界面
      if (initFormExData.useFormRegularCompact === true) {
        $formRegularRootContainer.addClass('formRegularCompact')
      } else {
        $formRegularRootContainer.removeClass('formRegularCompact')
      }

      this.colItems = colItems
      var fcRuleItems = []
      this.colItemsMap = {}
      this.datePickerColItems = []
      var maxLabelLength = 0

      this.$nextTick(() => {
        if (this.formExtend) {
          if (this.formExtend.initMetaBefore) {
            this.formExtend.initMetaBefore(this.dataVo)
          }
        }
      })

      initFormExData.initMetaBefore?.(this.dataVo)

      var initBeforeColItems = this.$clone(colItems)

      // 运行时可通过设置CformDataVo中extData的“隐藏的要素”来特别指定要素不显示
      let hiddenItemsDynamic = []
      if (this.$isNotEmpty(this.meta.extData) &&
        this.$isNotEmpty(this.meta.extData.隐藏的要素)) {
        hiddenItemsDynamic = this.meta.extData.隐藏的要素
      }

      // 运行时可通过设置CformDataVo中extData的“必填的要素”来特别指定要素有必填标识
      let requiredItemsDynamic = []
      if (this.$isNotEmpty(this.meta.extData) &&
        this.$isNotEmpty(this.meta.extData.必填的要素)) {
        requiredItemsDynamic = this.meta.extData.必填的要素
      }

      this.$nextTick(() => {
        colItems.forEach(item => {
          // 业务场景特别指定要隐藏的要素
          if (hiddenItemsDynamic.indexOf(item.labelOrigin) > -1 ||
            hiddenItemsDynamic.indexOf(item.labelAlias) > -1) {
            return
          }

          if (requiredItemsDynamic.indexOf(item.labelOrigin) > -1 ||
            requiredItemsDynamic.indexOf(item.labelAlias) > -1) {
            item.isRequired = true
          }

          var readonly = (this.isDetailMode && !this.$parent.isAuditCanEdit(item.label))
          this.allColItemsMap[item.labelOrigin] = item
          if (item.label !== item.labelOrigin) { // 别名也索引到要素对象
            this.allColItemsMap[item.label] = item
          }
          if (item.colType === '隐藏框') { // 隐藏框不显示
            if (isNew) { // 新增时将默认值填入
              item.dataValue = item.defaultValue
            }
            return
          }

          if (item.isAutoAdd && item.colType !== '弹框ID') {
            if (this.$isEmpty(item.dataValue) && isNew && item.isAutoAdd) { // 新增时，必要要素填入默认值
              item.dataValue = item.defaultValue
            }
            return
          }

          const initRefDataVoFromRemoteData = this.$isEmpty(this.meta.extData)
            ? '' : this.meta.extData.initRefDataVoFromRemoteData
          // 要素值在新增时取要素设置默认值，修改时取对象的dataValue字段
          var value = item.dataValue
          if (isNew &&
            this.$isNotEmpty(item.defaultValue) &&
            this.$isEmpty(initRefDataVoFromRemoteData)) { // 新增时取要素默认值
            value = item.defaultValue
          }

          // 多选或弹框时，值可能是由逗号分隔的多个值
          const selectMultiple = item.colType === '下拉框' && item.isMultipleRef
          const ARR_COLTYPE = ['多选', '日期范围', '弹框']
          if (ARR_COLTYPE.includes(item.colType) || selectMultiple) {
            // 由于收支模板管理功能需要，使用模板新增时id是空的，弹窗多选要素在新增时不显示带出的数据
            // if (isNew && item.colType === '弹框' && item.isMultipleRef === true) {
            //   value = ''
            // }
            value = value.replace('，', ',')
            if (value.indexOf(',') > -1) {
              value = value.split(',')
              if (item.colType === '日期范围') { // 排序
                value = value.sort(function(a, b) {
                  return new Date(a) - new Date(b)
                })
              }
            } else if (value) {
              value = [value]
            }
          }
          if (item.colType === '金额') {
            value = this.$formatMoney(value)
          }
          var typesData = this.getItemTypes(item, isNew)
          var placeholder = ''
          const isRef = initFormExData.baseListFormObj?.isRef || ''
          if (item.labelOrigin === '业务编码' &&
            (isNew || this.isDesignMode || this.isPreviewEdit || isRef || item.dataValue === '')) { // 特别处理业务编码的显示
            placeholder = '保存后自动生成'
            value = ''
          }

          var itemData = {
            title: item.label,
            field: item.label,
            value: value,
            options: item.labelValues,
            col: { span: 12 },
            props: {
              readonly: readonly,
              clearable: true,
              number: false,
              size: 'default',
              placeholder: placeholder
            },
            colItem: item
          }
          itemData.type = typesData.type
          itemData.emit = typesData.emit

          // 如果没有通过配置设置change事件，在此统一补上。这样就能实现
          // 所有的要素有变化时，都会触发全局的值变化监听回调
          if (this.$isEmpty(itemData.emit)) {
            var itemConfig = { itemName: item.label }
            itemData.emit = [{ name: 'change', inject: itemConfig }]
            this.fcModel.on(item.label + '-change', this.runColItemModifiedCallbacks)
          }
          if (itemData.type === 'input' && item.colType !== '弹框') { // 弹框在change触发
            itemData.emit = itemData.emit || []
            itemData.emit.push({ name: 'blur', inject: itemConfig })
            this.fcModel.on(item.label + '-blur', this.inputBlur)
          }

          if (item.colType === '弹框' && this.$isNotEmpty(value)) {
            itemData.children = this.clearIconFun(colItems, item)
          } else {
            itemData.children = typesData.children
          }

          Object.assign(itemData.props, typesData.props)
          Object.assign(itemData, typesData.parentExtend)

          if (this.meta.extData && this.meta.extData.hiddenItems) {
            var hiddenItems = this.meta.extData.hiddenItems
            if (hiddenItems.indexOf(item.labelOrigin) < 0) {
              fcRuleItems.push(itemData)
            }
          } else {
            fcRuleItems.push(itemData)
          }

          if (maxLabelLength < item.label.length) {
            maxLabelLength = item.label.length
          }
          this.colItemsMap[item.labelOrigin] = item
          if (item.label !== item.labelOrigin) { // 别名也索引到要素对象
            this.colItemsMap[item.label] = item
          }
        })

        // 自动适配最长标题，确保不会出现换行
        var labelWidth = '100px'
        if (maxLabelLength > 4 && maxLabelLength <= 6) {
          labelWidth = '125px'
        } else if (maxLabelLength > 6 && maxLabelLength <= 8) {
          labelWidth = '150px'
        } else if (maxLabelLength > 8 && maxLabelLength <= 10) {
          labelWidth = '175px'
        } else if (maxLabelLength > 10 && maxLabelLength <= 12) {
          labelWidth = '210px'
        } else if (maxLabelLength > 12 && maxLabelLength <= 14) {
          labelWidth = '250px'
        } else if (maxLabelLength > 14) {
          labelWidth = '275px'
        }

        this.fcRule = []
        this.fcRulePropColMap = {}
        const extData = this.meta.extData || {}
        const remindInfo = extData.remindInfo || {}
        fcRuleItems.forEach(item => {
          item.col.labelWidth = labelWidth
          if (item.colItem && remindInfo[item.colItem.labelOrigin]) {
            item.info = remindInfo[item.colItem.labelOrigin]
          }
          this.fcRule.push(item)
          this.fcRulePropColMap[item.field] = item
        })
        // 如果还没有任何要素设置，此时隐藏规则表单画布底部蓝线
        var $formCommonCols = $('.formCommonCols')
        $formCommonCols.removeClass('formCommonColsNoBorderBottom')
        if (this.fcRule.length === 0) {
          $('.formCommonCols').addClass('formCommonColsNoBorderBottom')
        }

        this.$nextTick(() => {
          this.setFormReadonly()
          if (this.isDesignMode) { // 设计模式时，设置点击标题栏与右侧属性框联动
            // 绑定标题点击事件
            var $labels = $('.formCommonCols .el-form-item__label')
            $labels.addClass('colItemLabel')
            $.each($labels, (i, v) => {
              var label = $(v).find('span').text()
              $(v).click(() => {
                $labels.removeClass('currentEditingItem')
                $(v).addClass('currentEditingItem')
                this.$emit('clearRFormElementList')
                this.$emit('setEditingColItem', label)
              })
            })

            // 立即检查当前是否存在编辑 (这里暂时注释，重构时无处理，需要处理)
            // var colItem = this.$refs.colSetting.getEditingColItem()
            // if (colItem) {
            //   this.editingColItemChanged(true, colItem)
            // }
          } else if (this.isDetailMode) {
            if (!this.isAuditMode && this.$parent.formatType !== 'form-free') {
              return $('.formRegularContainer').addClass('formRegularContainerNoBorder')
            }
          }
        })
        this.$emit('colItemsChange', colItems)

        this.fcModel.nextTick(() => {
          this.$nextTick(() => {
            if (this.formExtend) {
              var searchRefFun = (item) => {
                this.searchRefFun(item)
              }
              if (this.formExtend.initGetRegularInfo) {
                this.formExtend.initGetRegularInfo(
                  this, this.fcModel, this.fcRule,
                  this.colItemsMap, searchRefFun,
                  initFormExData.baseListFormObj)
              }
            }

            if (this.formExtend) {
              if (this.formExtend.initMetaAfter) {
                this.formExtend.initMetaAfter(
                  this.fcModel, this.colItems, this, initFormExData, this.meta)
              }
            }

            initFormExData.initMetaAfter?.(this.fcModel, this.colItems, this, initFormExData, this.meta)

            if (this.formExtend) {
              if (this.formExtend.hideDisableProjectLevel) {
                this.formExtend.hideDisableProjectLevel(
                  this.fcRule, this.fcModel, this.colItems, this)
              }
            }

            initFormExData.hideDisableProjectLevel?.(this.fcRule, this.fcModel, this.colItems, this)
            this.formLoadedVerifyDate()
            // 表单渲染完成再调用的方法
            initFormExData?.callbackAfterFormLoaded?.(this.dataVo)
            this.setMessageClass()
          })
        })
      })

      // 制单时初始化右侧扩展内容
      if (this.isFillFormMode || this.isAuditMode) {
        this.$nextTick(() => {
          this.$parent.initExtDataAssembly(this.meta, this.mode, initBeforeColItems)
        })
      }
      if (this.meta.extData && this.meta.extData.formExtraItemData) {
        this.formExtraItemData = this.meta.extData.formExtraItemData
      } else {
        // if (window.location.href.includes('/pur-cm')) {
        //   this.formExtraItemData = this.formExtraItemData
        // } else {
        //   this.formExtraItemData = []
        // }

        if (!window.location.href.includes('/pur-cm')) {
          this.formExtraItemData = []
        }
      }
      // 有额外的TAB时，显示tab标题
      this.formCanvasFirstTab = this.meta?.main?.name
      if (this.$isNotEmpty(this.formCanvasExTabs)) {
        $('.formCanvasTab').removeClass('hideFormCanvasTab')

        this.formCanvasTabIndexs[this.formCanvasFirstTab] = 0
        for (var i = 1; i <= this.formCanvasExTabs.length; i++) {
          var tabName = this.formCanvasExTabs[i - 1].name
          this.formCanvasTabIndexs[tabName] = i
        }
      }

      // 显示画布中的第一个TAB的内容
      this.$nextTick(() => {
        this.formCanvasActiveTab = this.formCanvasFirstTab
        if (this.loading) {
          this.loading = false
          this.changeBlockLoading && this.changeBlockLoading(false)
        }
      })
      this.$isNotEmpty(this.formCanvasExTabs) && this.$refs.formCanvasTabComponents[0].init(this.dataVo)
    },
    editingColItemChanged(isEditing, colItem) { // 属性框编辑的要素变动时，联动选中标题
      var $labels = $('.formCommonCols .el-form-item__label')
      $labels.removeClass('currentEditingItem')
      if (isEditing) {
        var label = colItem.label
        $.each($labels, (i, v) => {
          var lb = $(v).find('span').text()
          if (lb === label || lb === '*' + label) {
            $(v).addClass('currentEditingItem')
          }
        })
      }
    },
    colSetcheckedSelectClick(selection, row) {
      this.$emit('colSetcheckedSelectClick', selection, row)
    },
    clearAfterReloadFRule(item) {
      this.fcRule.forEach(i => {
        if (i.field === item.label) {
          i.children = this.searchIconFun(item)
        }
      })
    },
    clearIconFun(colItems, item) {
      if (this.isDetailMode && !this.isAuditMode) return
      const iconArr = [
        {
          type: 'i',
          class: 'el-icon-circle-close el-input__icon',
          slot: 'prefix',
          on: {
            click: () => {
              const props = this.fcRulePropColMap[item.label]?.props
              if (props && props.disabled) {
                return
              }
              this.clearRefData(item)
              const itemConfig = { itemName: item.label }
              const inject = { inject: itemConfig }
              this.runColItemModifiedCallbacks(inject, this.getValue(item.label))
            }
          }
        }
      ]
      return iconArr
    },
    searchIconFun(item, isdisabled = false) {
      const iconArr = [{
        type: 'i',
        class: 'el-icon-search el-input__icon',
        slot: 'prefix',
        on: {
          click: () => {
            if (isdisabled) return
            const props = this.fcRulePropColMap[item.label]?.props
            if (props && props.disabled) {
              return
            }
            this.searchRefFun(item)
          }
        }
      }]
      return iconArr
    },
    refAfterInitWhenFillForm(exParams) { // 制单初始后，先弹出表单参照
      var item = {
        dataRef: exParams.dataRef,
        label: '', labelOrigin: '', colType: '弹框', remark: '表单参照'
      }
      this.searchRefFun(item, exParams)
    },
    searchRefFun(item, exParams) {
      // 详情模式不响应按照事件，当前元素审核可编辑时，需要响应参照
      if (!this.isDetailMode || this.$parent.isAuditCanEdit(item.label)) {
        var params = {}
        params.formType = this.meta.main.formType
        params.multiple = false // 目前规则表单的参照只支持单选
        if (item.colType === '弹框' && item.isMultipleRef === true) {
          params.multiple = true
        }
        params.doNotFillRelateColItem = this.isAuditMode // 审核时修改参照要素，不要修改关联要素

        // 参照填充关联要素时，是否包括部门和部门ID要素
        params.isRelatedRefIncludeDept = false
        params.formExtendExData = {}
        if (this.formExtend) {
          if (this.formExtend.isRelatedRefIncludeDept) {
            params.isRelatedRefIncludeDept =
              this.formExtend.isRelatedRefIncludeDept(item)
          }
          params.formExtendExData = this.formExtend.exData
        }

        if (this.formExtend && this.formExtend.exHandleRefParams) {
          this.formExtend.exHandleRefParams(params, this, this.colItems, item)
        }
        this.blockParams?.exHandleRefParams?.(params, this, this.colItems, item)

        if (this.formExtend && this.formExtend.ignoreRefItem) {
          params.ignoreRefItem = this.formExtend.ignoreRefItem
        }

        if (this.formExtend && this.formExtend.isRefNoCode) { // 参照结果是否不含编码
          params.isRefNoCode = (labelOrigin, dataRef) => {
            return this.formExtend.isRefNoCode(labelOrigin, this, dataRef)
          }
        }
        if (this.blockParams?.isRefNoCode) {
          params.isRefNoCode = (labelOrigin, dataRef) => {
            return this.blockParams.isRefNoCode(labelOrigin, dataRef)
          }
        }

        if (this.blockParams?.callbackBeforeRefComplete) {
          params.closeDlgByOutside = true
          params.callbackBeforeRefComplete =
            (selectedData, params, callbackCloseRefDlg, setBtnUnLoad) => {
              this.blockParams.callbackBeforeRefComplete(
                this, item, selectedData, params, callbackCloseRefDlg, setBtnUnLoad)
            }
        }

        exParams = exParams || {}
        params = Object.assign(params, exParams)
        if (this.$isNotEmpty(this.formExtend ? this.formExtend.refDataBefore : '')) {
          var isShow = this.formExtend.refDataBefore(item, this.fcModel)
          if (!isShow) {
            return
          }
        }
        params['colItemId'] = item.id
        if (this.$isNotEmpty(this.meta.main)) {
          params['formId'] = this.meta.main.id
        }
        this.$refData(
          this.colItems, item, this.fcModel.getValue,
          this.fcModel.setValue, selectedData => {
            this.fcRule.forEach(i => {
              if (i.field === item.label) {
                i.children = this.clearIconFun(this.colItems, item)
              }
            })

            this.isClosingRefDlg = true
            if (this.formExtend) {
              var clearAfterFun = (params) => {
                this.clearAfterReloadFRule(params)
              }
              if (this.formExtend.clearRefDataExr && !this.isAuditMode) {
                const clickBeforeRefValue = this.fcModel.getValue(item.label) || []
                this.formExtend.changeClearRefDataExr(
                  item, clickBeforeRefValue, selectedData,
                  this.fcModel, this.colItemsMap, clearAfterFun)
              }
            }
          }, selectedData => {
            this.$nextTick(() => {
              if (this.formExtend && this.formExtend.refDataAfter) {
                this.formExtend.refDataAfter(item, selectedData, this.fcModel)
              }
              this.blockParams?.refDataAfter?.(item, selectedData, this.fcModel)
              const list = selectedData.list
              if (this.$isNotEmpty(list) && this.$isNotEmpty(list[0].remindInfo)) {
                const remindInfo = {}
                remindInfo[list[0].remindInfo.formFieldName] = list[0].remindInfo.remindMessage
                this.setPostil(remindInfo)
              }
              this.isClosingRefDlg = false

              // 弹框要素需要单独触发值变化回调函数
              this.runColItemModifiedCallbacksBy(item.label, selectedData)
            })
          }, params)
      }
    },
    setPostil(remindInfo = {}) {
      if (!this.isFillFormMode) { // 只有在制单的时候显示
        return
      }
      const remindKeys = Object.keys(remindInfo)
      remindKeys.forEach((item, itemIndex) => {
        const colItem = this.fcRulePropColMap[item]
        if (this.$isNotEmpty(colItem)) {
          colItem.info = remindInfo[item]
        }
      })
    },
    clearRefLabelValues(label) { // 清除下拉的·option
      const colItem = this.fcRulePropColMap[label] || {}
      if (!colItem) return
      const itemsKey = colItem.labelValues ? 'labelValues' : 'options'
      colItem[itemsKey] = []
    },
    setColRequired(label, isRequired = false) {
      const colItem = this.fcRulePropColMap[label] || {}

      if (this.$isEmpty(colItem)) {
        return
      }
      let className = colItem.className
      if (!isRequired) {
        className = className.replace(/col-isRequired/g, '')
      } else {
        className += ' col-isRequired'
      }
      colItem.className = className
    },
    setDisabled(label, isDisabled) {
      this.fcModel.disabled(isDisabled, label)
      if (this.fcRulePropColMap[label]) {
        this.$set(this.fcRulePropColMap[label]?.props, 'readonly', isDisabled)
      }
    },
    setColValue(label, value) {
      const colItem = this.allColItemsMap[label]
      if (this.$isNotEmpty(colItem)) {
        colItem.dataValue = value
      }
    },
    setValue(label, value, noWarn) {
      let colItem = this.fcRulePropColMap[label]?.colItem || {}
      // labelOrigin找不到时找label
      if (this.$isEmpty(colItem) && this.allColItemsMap[label]) {
        colItem = this.allColItemsMap[label]
        label = colItem.label
      }
      if (this.$isNotEmpty(colItem) && colItem.colType === '金额') {
        value = this.$formatMoney(value)
      }
      // 传进来的label可能是labelOrigin
      if (this.$isEmpty(colItem) && this.allColItemsMap[label]) {
        label = this.allColItemsMap[label].label
      }
      this.fcModel.setValue(label, value)
    },
    getValue(label, isHideItem) {
      const value = this.fcModel.getValue(label, isHideItem)
      if (isHideItem && value === undefined) {
        return this.allColItemsMap[label]?.dataValue
      }
      return value
    },
    getItemTypes(item, isNew) { // 获取要素的input类型
      var type = 'input'
      var props = {}
      var parentExtend = {}
      var children = []

      var emit = []

      if (item.colType === '单选') {
        type = 'radio'
        parentExtend = { className: 'colitem-radio' }
        emit = this.getConfigCollocation(item, emit)
      } else if (item.colType === '多选') {
        type = 'checkbox'
        parentExtend = { className: 'colitem-checkbox' }
      } else if (item.colType === '下拉框') {
        // 如果是详情模式，直接使用input，不使用select
        // 因为select将有响应事件，暂时不能解绑事件
        var this_ = this
        if (!this.isDetailMode || this.$parent.isAuditCanEdit(item.label)) {
          type = 'select'
          props = { filterable: true, multiple: item.isMultipleRef, collapseTags: item.isMultipleRef }
          parentExtend = {
            className: 'colitem-select', nativeOn: {
              click: (event) => {
                for (var i = 0; i < this_.fcRule.length; i++) {
                  var obj = this_.fcRule[i]
                  if (obj.field === item.label) {
                    // 下拉携带#号后面的参数
                    // eg: 被收款方#{合同ID} 合同ID需要加入到查询参数中
                    const exParams = {}
                    if (this.$isNotEmpty(item.dataRef) && item.dataRef?.indexOf('#') > -1) {
                      const filter = item.dataRef.split('#')[1]
                      const REG = /\{|\}/ // 正则表达式匹配左花括号或右花括号
                      let handlefilter
                      // 证明是要素 携带花括号 把花括号去除
                      if (REG.test(filter)) {
                        handlefilter = filter.replace(/\{|\}/g, '')
                        exParams[handlefilter] = this.getValue(handlefilter, true).split(this.$getRefIdValueSplitor())[0]
                      }
                    }
                    const params = {
                      dataRef: item.dataRef,
                      colItemId: item.id,
                      'cformMeta': this.dataVo.meta
                    }
                    Object.assign(params, exParams)
                    if (this.$isNotEmpty(this.meta.main)) {
                      params['formId'] = this.meta.main.id
                    }
                    this_.$refLabelValues(params, obj, this.filterParams)
                    break
                  }
                }
              }
            }
          }
          emit = this.getConfigCollocation(item, emit)
        }
      } else if (item.colType === '日期' || item.colType === '日期范围') {
        type = 'DatePicker'
        props = { type: 'date', format: 'yyyy-MM-dd' }
        parentExtend = { className: 'colitem-datetime' }
        emit = this.getConfigCollocation(item, emit)
        if (item.colType === '日期范围') {
          props.type = 'daterange'
        } else {
          this.datePickerColItems.push(item)
        }
      } else if (item.colType === '整数' ||
        item.colType === '小数' ||
        item.colType === '金额' ||
        item.colType === '百分比') {
        type = 'InputNumber'
        props = { precision: item.digits }
        if (item.colType === '金额') { // TODO: 金额需要实现千分位效果，使用焦点事件
          type = 'input'
          parentExtend = { className: 'colitem-money' }
          props = { precision: 2 }
          // 金额限制只能保留两位小数
          emit = this.getConfigCollocation(item, emit)
        }
        if (item.colType === '百分比') {
          parentExtend = { className: 'colitem-percent' }
          props = { precision: 2 }
        }
        if (item.colType === '整数') {
          parentExtend = { className: 'colitem-integer' }
          emit = this.getConfigCollocation(item, emit)
        }
        if (item.colType === '小数') {
          parentExtend = { className: 'colitem-float' }
        }
      } else if (item.colType === '弹框ID') {
        type = 'hidden'
      } else if (item.colType === '弹框') {
        type = 'input'
        if (item.hyperlink) {
          type = 'hyperlink'
        }
        props = { type: 'text', readonly: true }
        children = this.searchIconFun(item)
        parentExtend = {
          className: 'colitem-button', on: {
            focus: () => {
              this.searchRefFun(item)
            }
          }
        }
      } else {
        props = { type: 'text' }
        if (parseInt(item.width) === 2) { // 跨行文本处理，比如详情之类的要素
          parentExtend = { col: { span: 24 }, className: 'colitem-textarea' }
          // props = { type: 'textarea', rows: 3, resize: 'none' }
          props = { rows: item.textAreaRows, resize: 'none' }
          type = 'fieldComponent'
          // Textarea禁用
          if (this.isDetailMode) this.isFormCreateTextarea = true
        } else {
          parentExtend = { className: 'colitem-text' }
        }
      }
      if (parseInt(item.width) === 2) {
        parentExtend['col'] = { span: 24 }
      }
      if (item.colType === '文本(30)' || item.colType === '文本(100)') {
        type = 'input'
        props['type'] = 'text'
        parentExtend['className'] = 'colitem-text'
      }

      if (item.isRequired) {
        var clazz = parentExtend.className ? parentExtend.className : ''
        parentExtend.className = 'col-isRequired ' + clazz
      }
      if ((item.modifyType === '只读') ||
        (item.modifyType === '保存后只读' && !isNew)) {
        if (!this.isDetailMode) { // 详情时只读的控制由详情机制统一处理
          props.readonly = true
          props.disabled = true
          // children = this.searchIconFun(item, true)
          // 除详情外 只读状态无需显示放大镜以及清除按钮
          children = []
        }
      }
      if (item.isEnabled === '否' && !this.isDesignMode) {
        type = 'hidden'
      }
      return {
        type: type,
        props: props,
        children,
        parentExtend: parentExtend,
        emit: emit
      }
    },
    getConfigCollocation(item, emit) {
      var formType = this.meta?.main?.formType
      var itemConfig = {}
      if (formType in formConfig) {
        if (item.label in formConfig[formType]) {
          itemConfig = formConfig[formType][item.label]
        }
      }
      itemConfig.itemName = item.label
      const emitData = [{ name: 'change', inject: itemConfig }] // inject是change事件的参数
      this.fcModel.on(item.label + '-change', (inject, value) => this.selectchange(inject, value, item.label))
      if (this.$isNotEmpty(item.dataValue)) {
        setTimeout(() => {
          this.selectcSetUp(itemConfig, item.dataValue)
        }, 1000)
      }
      return emitData
    },
    fireRelateAfterRefForm() { // 表单参照之后触发要素联动处理
      // if (this.$isNotEmpty(formConfig)) {
      //   this.$nextTick(() => {
      //     var formType = this.meta.main.formType
      //     var formTypeConfig = formConfig[formType]
      //     if (this.$isNotEmpty(formTypeConfig)) {
      //       this.colItems.forEach(item => {
      //         var itemConfig = formTypeConfig[item.label]
      //         if (this.$isNotEmpty(item.dataValue) && this.$isNotEmpty(itemConfig)) {
      //           setTimeout(() => {
      //             this.selectcSetUp(itemConfig, item.dataValue)
      //           }, 1000)
      //         }
      //       })
      //     }
      //   })
      // }
    },
    addShowErrorCallbacks(callbacks) {
      this.$addFormActionCallbacks(
        this, 'showErrorCallbacks', callbacks)
    },
    // 给错误提示添加class类名
    setMessageClass() {
      const that = this
      const $colItems = $(this.$refs.formCommonCols).find('.el-col .el-form-item')
      $.each($colItems, function(i, v) {
        const $item = $(this)
        const label = $item.find('label.el-form-item__label span').text()
        if (that.colItemsMap[label]) {
          $item.addClass(`err-message-${label}`)
        }
      })
    },
    // 展示单个的
    showErrorTip() {
      const keys = Object.keys(this.errorTip)
      keys.forEach((label) => {
        const $item = $(this.$refs.formCommonCols).find(`.err-message-${label}`)
        const $itemContent = $item.find('.el-form-item__content')
        var $errorSpan = $itemContent.find('span.col-err-message')
        if ($errorSpan.length === 0) {
          $errorSpan = $('<span class="col-err-message"/>')
          $itemContent.append($errorSpan)
        }
        $errorSpan.text(this.errorTip[label])
        $item.addClass('el-error')
      })
    },
    showError(result, errorNum) { // 显示后端错误，然后成功找到匹配错误要素的个数
      errorNum = errorNum || 0
      this.activitedTabClick(result, errorNum, this.isLoadedFree)
      if (result.data) {
        this.isLoadedFree = true
      } else {
        this.isLoadedFree = false
      }
      var this_ = this
      var hitCount = errorNum
      var $colItems = $(this.$refs.formCommonCols).find('.el-col .el-form-item')
      var errorItems = {}
      this.errorTip = {}
      if (result && !result.success && result.attributes) { // 后端的错误信息放在R.attributes
        errorItems = result.attributes
      }
      $.each($colItems, function(i, v) {
        var $item = $(this)
        $item.removeClass('el-error')

        var label = $item.find('label.el-form-item__label span').text()
        if (this_.$isNotEmpty(errorItems[label])) { // 命中错误要素
          $item.addClass(`err-message-${label}`)
          hitCount++
          var $itemContent = $item.find('.el-form-item__content')
          var $errorSpan = $itemContent.find('span.col-err-message')
          if ($errorSpan.length === 0) {
            $errorSpan = $('<span class="col-err-message"/>')
            $itemContent.append($errorSpan)
          }
          $errorSpan.text(`${label}: ${errorItems[label]}`)
          $item.addClass('el-error')
        }
      })

      // 扩展的错误提示，比如收款人组件的错误标红
      var callbacks = this.$getFormActionCallbacks(
        this, 'showErrorCallbacks')
      callbacks.forEach(cb => cb(errorItems))
      return hitCount
    },
    setFormReadonly() { // 详情和审核时设置只读
      var $formRegular = $('#' + this.formRegularId)
      if (this.isDetailMode || this.isAuditMode) {
        $formRegular.addClass('formRegularDetailOrAuditMode')

        var this_ = this
        var $colItems = $formRegular.find('.formCommonCols .el-col .el-form-item')
        $.each($colItems, function(i, v) {
          var $item = $(this)
          var $input = $item.find('input,textarea')

          var $labelSpan = $item.find('> label > span')
          var itemLabel = $labelSpan.text().trim()
          if (this_.$parent.isAuditCanEdit(itemLabel)) {
            $input.removeAttr('readonly')
            $input.removeAttr('disabled')
            $input.addClass('auditCanEdit')

            $item.find('.el-checkbox-group').addClass('auditCanEdit')
            $item.find('.el-radio-group').addClass('auditCanEdit')
            $item.find('.el-radio__inner').addClass('auditCanEdit')
            $item.find('.el-checkbox__inner').addClass('auditCanEdit')
            $item.find('.el-input__prefix').addClass('auditCanEdit')
            $item.find('.el-input__suffix-inner').addClass('auditCanEdit')

            // 弹框审核可编辑时，不使用手动编辑，而是使用参照
            if ($item.hasClass('colitem-button')) {
              $input.attr('readonly', 'readonly')
              $input.css('cssText', 'cursor:pointer !important')
            }

            return true // 实际上是continue
          }

          $input.removeClass('auditCanEdit')
          $input.attr('readonly', 'readonly')
          $input.attr('disabled', 'disabled')
          $item.find('.el-radio__input.is-checked+.el-radio__label').css('color', '#606266')
          // $item.find('.el-radio__input.is-checked .el-radio__inner').hide()

          // 金额和百分比有特殊的事件绑定，需要单独处理
          if ($item.hasClass('colitem-money') || $item.hasClass('colitem-percent')) {
            var $inputMock = $('<input type="text"/>')
            $inputMock.attr('readonly', 'readonly')
            $inputMock.attr('disabled', 'disabled')
            $inputMock.val($input.val())
            $inputMock.addClass('mock-money-input')
            $inputMock.addClass('el-input__inner')
            $input.parent().append($inputMock)
            $input.hide()
          }
        })
      } else {
        $formRegular.removeClass('formRegularDetailOrAuditMode')
      }
    },
    setFilterParams(filterParams) {
      this.filterParams = filterParams
    },
    blockCformAreaClick(e) {
      this.changeElementConfigDisabled?.(true)
      // 清空组件管理配置项
      this.clearRformConfig?.()
      this.hideElementArea()
      // 清空选中当前操作的是哪个表格 及 清空表头选中
      this.changeRFormTableHeaderLabel()
      this.changeCurrentBlockId?.()
      this.clearCurrentSelectedRForm()
      if (this.$isNotEmpty(e)) {
        let clickElement = e.target
        while (clickElement?.className?.indexOf('formCommonCols') === -1) {
          if (clickElement?.className?.indexOf('colItemLabel') !== -1) {
            return
          }
          clickElement = clickElement.parentNode
        }
      }
      this.handleElement()
    },
    changeLoading(loading) {
      this.loading = loading
    }
  },
  beforeRouteLeave(to, from, next) {
    this.formExtraItemData = []
    next()
  }
}
</script>
<style>
/*定义滚动条样式*/
::-webkit-scrollbar{
  width:6px;
  height:6px;
  background-color:rgba(240,240,240,1);
}
/*定义滚动条轨道内阴影+圆角*/

::-webkit-scrollbar-track{
  box-shadow: inset000pxrgba(240,240,240,.5);
  border-radius:10px;
  background-color:rgba(240,240,240,.5);
}
/*定义滑块内阴影+圆角*/
::-webkit-scrollbar-thumb{
  /* border-radius:10px; */
  box-shadow: inset000pxrgba(240,240,240,.5);
  background-color:#DADADA;

}
.formCommon .formMain .formCommonCols .el-icon-search:before {
    font-size: 20px;
}
.formCommon .formMain .formCommonCols .is-disabled .el-icon-search:before {
  cursor: not-allowed;
}
.formCommon .formMain .formCommonCols .colitem-button:hover .el-icon-search {
    color: #006bcc;
}
.formRegularColbottomBorber{ border-bottom: 1px solid #98bfff;}
.formCommonCols .colItemLabel {
    cursor: pointer;
    border: 1px solid #dbeeff;
}
.formCommonCols .colItemLabel:hover {
    border: 1px solid #6eb7fb;
}
.formCommonCols .currentEditingItem {
    border: 1px solid #006bcc !important;
}
.formRegularContainer {
    height: 100%;
    border: 1px solid  #DDDDDD;
}
.formCanvasRuntime .formRegularContainer { border: none; }
.formCanvasRuntimeHasAssembly { padding: 0px !important; }
.formCanvasRuntimeHasAssembly .el-main { padding: 0px 0px 10px 0px !important; }

.formRegularContainerNoBorder {border: none;}
.formRegularContainer .el-input__prefix {display: flex;align-items: center;top: 0px !important; }
.formRegularContainer .el-input__prefix  .el-icon-circle-close {height: auto;line-height: normal; }
.formRegularContainer .colitem-button:hover .el-icon-circle-close {color: #006bcc; }
.formRegularContainer .el-input__prefix  .el-icon-circle-close:before {font-size: 18px;cursor: pointer; }
.formRegularContainer #formFreeView {
    border-bottom: none;
    position: relative;}
.formCanvasRuntimeHasAssembly #formFreeView { padding: 10px;border: 1px solid #bbb;overflow: scroll; }
.formCanvasRuntimeHasAssembly #formFreeView.noHeader { padding: 5px;overflow: auto; }
.formCanvasRuntimeHasAssemblyFree #formFreeView {
    padding: 10px 1px 0px 10px;margin-bottom: -7px;border: none; }
.formRegularContainer #formFreeView .formRegularSettingExtra{right: 10px;}
.form-create-textarea{padding: 5px !important;resize:none !important}
.formRegularListLabel{position: absolute;bottom: 0;margin-bottom: 0px;text-align: left;}
.formRegularListLabel .listItem{margin-bottom: 0px;}
.formRegularListLabel .listItemLabel{font-weight: 600;}
.formRegularListLabel .listItemValue{color: #666;}
.formRegularContainer .formCommonCols .el-radio-group {padding-left: 7px;}

.formRegularCompact .el-input-number .el-input-number__increase,
.formRegularCompact .el-input-number .el-input-number__increase,
.formRegularCompact .el-input-number.is-disabled .el-input-number__increase,
.formRegularCompact .el-input-number.is-disabled .el-input-number__increase,
.el-input-number.is-disabled .el-input-number__decrease,
.el-input-number.is-disabled .el-input-number__increase,
.el-input-number.is-disabled:hover .el-input-number__decrease:hover,
.el-input-number.is-disabled:hover .el-input-number__increase:hover{
  border-color: #cdcdcd !important;
  color: #9f9f9f !important;
  top: -1px !important;
}
.formRegularCompact .el-input-number { line-height: 28px !important; }

.formCanvasDetail .formRegularCompact .formCommonHeader { height: 65px !important; padding-top: 6px; margin-top: -7px; }
.formRegularCompact .formCommonTitle { font-size: 26px;height: 60px;  }
.formRegularCompact .formCommonTitle span span { padding: 2px 24px; }
.formCanvasDetail .formRegularCompact .el-main { padding: 0px; }
.formRegularContainer.formRegularCompact { padding: 0px; }

.formRegularCompact .formCommonCols .el-form-item__label { font-size: 14px;line-height: 30px !important;}
.formRegularCompact .formCommonCols .el-form-item__content { height: 30px;line-height: 30px;padding: 0px; }
.formRegularCompact .formCommonCols .el-input__inner { height: 28px;font-size: 14px; }

.formRegularCompact .formCommonCols .el-input__inner.auditCanEdit,
.formCommonCols .el-input__inner.auditCanEdit,
.formRegularCompact .formCommonCols .el-textarea__inner.auditCanEdit,
.formCommonCols .el-textarea__inner.auditCanEdit,
.customDom .customSelect.auditCanEdit .el-input--small .el-input__inner,
.customDom .customRadio.auditCanEdit .el-radio__label,
.customDom .customInput .el-input__inner.auditCanEdit { color: #2fbefc !important; }

.formRegularCompact .el-input__icon { width: 22px;line-height: 32px; }
.formRegularCompact .formCommonCols .el-input-number__increase,
.formRegularCompact .formCommonCols .el-input-number__decrease {
  line-height: 30px;height: 30px;font-size: 14px; top: -1px; }
.formRegularCompact .formCommonCols .el-radio__label,
.formRegularCompact .formCommonCols  .el-checkbox__label{ font-size: 14px;}
.formRegularCompact .formCommonCols .el-radio-group,
.formRegularCompact .formCommonCols .el-checkbox-group { height: 30px;line-height: 29px; }
.formRegularCompact .formCommonCols .el-checkbox { margin-right: 5px;line-height: 30px !important;}
.formRegularCompact .formCommonCols textarea { font-size: 14px;height: 48px !important;line-height:14px;padding: 2px !important; }
.formRegularCompact .formCommonCols .colitem-textarea { height: 50px !important; }
.formRegularCompact .formCommonCols .colitem-textarea .el-form-item__label { line-height: 50px !important; }

/* .formCanvasRuntime.formCanvasRuntimeRegularEdit { padding: 0px 20px 20px 20px !important; } */
.formRegularContainer .el-main { padding: 0px !important; }
.formRegularContainer .el-footer { padding: 0px !important; }
.formCanvasRuntimeRegularDesign .el-main { padding: 10px !important; }
.formCanvasRuntime.formCanvasRuntimeHasAssembly.formCanvasRuntimeRegularEdit { padding: 10px !important; }
.blockViewBlock .formCanvasTab { position: relative; }
</style>
<style lang="scss" scoped>
// 日期范围
.formCommonCols /deep/.el-date-editor--daterange.el-input__inner {
  overflow: hidden;
  width: 100%!important;
  padding: 3px 0;
  .el-range__icon {
    margin-left: 0px;
  }
}
// 日期范围制单详情时
.formRegularCompact /deep/.el-date-editor--daterange.el-input__inner {
  .el-range-separator, .el-input__icon {
    line-height: 22px;
  }
  .el-range__close-icon {
    line-height: 25px;
  }
}
// 日期范围disabled时
.formCommonCols /deep/.el-date-editor--daterange.el-range-editor.is-disabled {
  background-color: #efefef;
  .el-range-input {
    background-color: #efefef;
    color: #606266;
  }
}

</style>
