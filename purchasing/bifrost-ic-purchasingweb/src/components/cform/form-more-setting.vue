<template>
  <el-dialog ref="moreSettingDlg" append-to-body class="moreSettingDlg" :visible.sync="moreSettingVisible" :close-on-click-modal="false">
    <page>
      <template #pageContent>
        <el-tabs v-model="moreSettingTab" @tab-click="moreSettingTabClick">
          <el-tab-pane label="表格列设置" :name="firstLabelName">
            <div class="tableListSetting mini-table">
              <div class="tableListSettingColAll">
                <div class="colSettingToolbar">
                  <el-select v-model="tableListSettingCurrentFormType"
                             style="margin-right: 5px;width: 100px;"
                             @change="currentFormTypeChanged"
                             class="colSettingSelect" placeholder="表单类别">
                    <el-option
                      v-for="item in cformTypeData"
                      :key="item.id"
                      :label="item.value"
                      :value="item.value"/>
                  </el-select>
                  <el-input placeholder="查找" prefix-icon="el-icon-search"
                            v-model="tableListSettingFormColsSearchText" class="colSettingFilterInput"
                            style="margin-right: 0px;"/>
                  <el-row class="colSettingToolbarButtonContainer" style="width: 65px;">
                    <el-button icon="el-icon-right" title="移入表格列"
                               @click="btSelectClick" circle :loading="isSelecting"
                               :disabled="btSelectDisabledFormCols"/>
                  </el-row>
                </div>
                <el-table
                  ref="tableListSettingFormCols"
                  :data="tableListSettingFormCols"
                  border
                  @selection-change="tableListSettingFormColsChange"
                  @row-dblclick="tableListSettingFormColsDblclick"
                  style="width: 290px;"
                  height="495"
                  max-height="495">
                  <el-table-column type="selection" width="25"></el-table-column>
                  <el-table-column label="表单要素" prop="label"></el-table-column>
                  <el-table-column label="序号" width="40" prop="order"></el-table-column>
                  <el-table-column label="类型" width="70" prop="colType"></el-table-column>
                </el-table>
              </div>
              <div class="tableListSettingColSelected">
                <div class="colSettingToolbar">
                  <el-input placeholder="查找" prefix-icon="el-icon-search"
                            v-model="tableListSettingColAllSearchText" class="colSettingFilterInput"
                            style="margin-right: 0px;"/>
                  <el-row class="colSettingToolbarButtonContainer">
                    <el-button icon="el-icon-circle-plus-outline" title="新增列" @click="btAddClick" circle/>
                    <el-button icon="el-icon-delete" title="删除列" @click="btDeleteClick"
                               circle :loading="isDeleting" :disabled="btDeleteDisabled"/>
                  </el-row>
                </div>
                <el-table
                  ref="tableListSettingColAll"
                  :data="tableListSettingColAllShow"
                  border
                  @selection-change="tableListSettingColAllChange"
                  @row-dblclick="tableListSettingColAllDblclick"
                  style="width: 290px;"
                  height="495"
                  max-height="495">
                  <el-table-column type="selection" width="25"></el-table-column>
                  <el-table-column label="表格列" prop="label"></el-table-column>
                  <el-table-column label="序号" width="40" prop="order"></el-table-column>
                  <el-table-column label="类型" width="70" prop="colType"></el-table-column>
                </el-table>
              </div>
              <div class="tableListSettingColEdit colSetting">
                <div class="tableListSettingColEditButton" style="margin-top:33px">
                  <!-- <el-button @click="btTableListSettingSaveCol"
                     :type="isTableListSettingColEditing? 'primary' : 'plain'"
                     size="mini"
                     :disabled="!isTableListSettingColEditing"
                     :loading="isTableListSettingColSaving">{{btTableListSettingColText}}</el-button> -->
                </div>
                <div style="overflow: auto" class="tableListSettingColEditForm colSettingSelectedSettingEdit">
                  <el-form label-width="70px" size="mini">
                    <el-form-item>
                      <template #label>
                        <span style="color:#f56c6c;margin-left: -5px;" >*</span>表格列名
                      </template>
                      <el-input v-model="colItem.label" :disabled="!isTableListSettingColEditing"/>
                    </el-form-item>
                    <el-form-item label="要素名称">
                      <el-input v-model="colItem.prop" :disabled="!isTableListSettingColEditing"/>
                    </el-form-item>
                    <el-form-item label="数据类型">
                      <el-select v-model="colItem.colType" :disabled="!isTableListSettingColEditing" @change="clearBindPram">
                        <el-option
                          v-for="(item, index) in commonData.colTypes"
                          :key="index"
                          :label="item"
                          :value="item"/>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="绑定操作">
                      <el-select v-model="colItem.reserveData" :disabled="!isTableListSettingColEditing">
                        <el-option
                          v-for="(item, index) in commonData.reserveDatas"
                          :key="index"
                          :label="item"
                          :value="item"/>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="请求参数">
                      <el-input v-model="colItem.dataRef"  :disabled="!isTableListSettingColEditing"/>
                    </el-form-item>
                    <el-form-item label="列序号">
                      <el-input v-model="colItem.order" @input="numericTypes" maxlength="4" :disabled="!isTableListSettingColEditing"/>
                    </el-form-item>
                    <el-form-item label="列宽度">
                      <el-input v-model="colItem.width" @input="formatColWidthToNum" :disabled="!isTableListSettingColEditing"/>
                    </el-form-item>
                    <el-form-item label="对齐方式">
                      <el-select v-model="colItem.align" :disabled="!isTableListSettingColEditing">
                        <el-option key="left" value="left" label="靠左对齐"/>
                        <el-option key="right" value="right" label="靠右对齐"/>
                        <el-option key="center" value="center" label="居中对齐"/>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="父级名称">
                      <el-input v-model="colItem.parentName" :disabled="!isTableListSettingColEditing"/>
                    </el-form-item>
                    <el-form-item label="是否排序">
                      <div style="display: flex;">
                        <div style="flex: 2;width: 65px;display: flex;">
                          <el-radio v-model="colItem.sortable" :label="true"
                                    :disabled="!isTableListSettingColEditing">是</el-radio>
                          <el-radio v-model="colItem.sortable" :label="false"
                                    :disabled="!isTableListSettingColEditing">否</el-radio>
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="是否固定">
                      <div style="display: flex;">
                        <div style="flex: 2;width: 65px;display: flex;">
                          <el-radio v-model="colItem.fixable" :label="true"
                                    :disabled="!isTableListSettingColEditing">是</el-radio>
                          <el-radio v-model="colItem.fixable" :label="false"
                                    :disabled="!isTableListSettingColEditing">否</el-radio>
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="是否导出">
                      <div style="display: flex;">
                        <div style="flex: 2;width: 65px;display: flex;">
                          <el-radio v-model="colItem.exportable" :label="true"
                                    :disabled="!isTableListSettingColEditing">是</el-radio>
                          <el-radio v-model="colItem.exportable" :label="false"
                                    :disabled="!isTableListSettingColEditing">否</el-radio>
                        </div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="表单设置" name="表单设置_960:695">
            <div id="cformName">
              表单名称：{{formName}}
            </div>
            <div id='cformOption'>
              <el-checkbox-group v-model="checkedCSList" style = "column-count: 3" @change="handleCheckedCSChange">
                <el-checkbox v-for="cformOption in cformOptionList"
                             :label="cformOption.tname"
                             :key="cformOption.id">
                  {{cformOption.tname}}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <div v-show="isShowLowerPart" style="flex: 1;border: 1px solid #DDDDDD; margin-top: 7px; overflow-y: auto;">
              <div id='recAcctTypeOption' v-show="isShowRecAcctTypeOption">
                <span>必须上传附件</span>
                <el-select v-model="selectRecAcctTypeOptions" multiple @change="selectRecAcctTypeChange" style="width:100%">
                  <el-option
                    v-for="item in recAcctTypeOptions"
                    :key="item.eleName"
                    :label="item.eleName"
                    :value="item.eleCode">
                  </el-option>
                </el-select>
              </div>

              <div v-show="isShowDefaultReference" class="option-canvas">
                <span>默认参照设置</span>
                <el-autocomplete v-model="templateName"
                style="display:block"
                  :fetch-suggestions="querySearch"
                  placeholder="请输入内容"
                  clearable
                  @clear="empty"
                  @select="handleSelect">
                  <template slot-scope="{ item }">
                    <div class="name">{{ item.label }}</div>
                  </template>
                </el-autocomplete>
              </div>

              <div v-show="isShowPrintTemplate" class="option-canvas">
                <span>打印模板</span>
                <sup-tree :setting="templatesetting"
                          ref="supTree"
                          :is-get-child="true"
                          :nodes="treeParentNodes"
                          :checked-values="templateTreeNodeId">
                </sup-tree>
              </div>
              <div v-show="isShowMainBillSetting" class="option-canvas">
                <span>设置汇总表</span>
                <sup-tree :setting="setting"
                          ref="supTreeMainBill"
                          :is-get-child="true"
                          :nodes="treeParentNodes"
                          :checked-values="mainBillMetaIds">
                </sup-tree>
              </div>
              <div v-show="isShowSqForm" class="option-canvas">
                <span>事前单配置</span>
                <sup-tree :setting="setting"
                          ref="supTreeSqForm"
                          :is-get-child="true"
                          :nodes="treeParentNodes"
                          :checked-values="sqFormTreeNodeId?[sqFormTreeNodeId]:[]">
                </sup-tree>
              </div>
              <div v-show="isShowSignatureSetting" class="option-canvas">
                <el-form :model="signatureSetting" inline class="signatureSettingForm" label-width="100px">
                  <el-form-item label="签章宽度">
                    <el-input   v-model="signatureSetting.width" oninput="value = value.replace(/\D/g, '')" @blur="signatureSetting.width = $event.target.value"></el-input>
                  </el-form-item>
                  <el-form-item label="签章高度 ">
                    <el-input v-model="signatureSetting.height" oninput="value = value.replace(/\D/g, '')" @blur="signatureSetting.height = $event.target.value"></el-input>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="表单权限配置" name="权限配置_744:685">
            <div>
              <formPermissionsList ref="permissions"/>
            </div>
          </el-tab-pane>
          <el-tab-pane v-if="isFreedomForm" label="图片设置" name="图片设置_450:655">
            <div style="height: 100%">
              <ImgFile id="ImgFile" style="height: 100%" isSelectType ref="ImgFile" :bizId='metaId'/>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div slot="footer" class="dialog-footer">
          <el-button class="btn-normal" @click="closeDlg">取消</el-button>
          <el-button class="btn-normal" @click="btTableListSettingSaveCol"
                     v-lockButton
                     v-show="moreSettingTab===firstLabelName"
                     :type="isTableListSettingColEditing? 'primary' : 'plain'"
                     :disabled="!isTableListSettingColEditing"
          >保存</el-button>
          <el-button class="btn-normal" type="primary" v-lockButton @click="hideDialog">确定</el-button>
        </div>
      </template>
    </page>
  </el-dialog>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'form-more-setting',
  props: {
    isFreedomForm: { type: Boolean, default: false }
  },
  data() {
    return {
      hasBa: false,
      firstLabelName: '表格列设置_940:695',
      commonData: this.newCommonData(),
      cformColsMap: {}, // 以id为键值索引表单要素
      btDeleteDisabled: true, // 删除列按钮是否可用
      btSelectDisabledFormCols: true, // 表单要素列表移入表格列按钮是否可用
      tableListSettingColAll: [], // 表格列列表
      tableListSettingFormColsSearchText: '', // 表单要素搜索文本
      tableListSettingColAllSearchText: '', // 表格列列表搜索文本
      cformTypeData: [], // 表单类别数据
      moreSettingTab: '', // 更多设置当前页签
      moreSettingVisible: false, // 更多设置弹框可见性
      colSettingCount: 0, // 要素设置的个数
      colSettingVisible: false, // 是否显示右侧要素设置工具栏
      tableListSettingCurrentFormType: '', // 表格列设置的当前表单类别
      isTableListSettingColEditing: false, // 是否正在编辑表格列设置
      isTableListSettingColSaving: false, // 是否表格列设置正在保存
      isDeleting: false, // 是否正在删除列
      isSelecting: false, // 是否正在移入表格列
      btTableListSettingColText: '新增表格列', // 表格列设置保存按钮文本
      colItem: {}, // 当前编辑的列
      order: 1000, // 最大排序值
      isShowRecAcctTypeOption: false, // 是否显示必须上传附件
      isShowLowerPart: false,
      isShowPrintTemplate: false, // 是否显示打印模板
      isShowDefaultReference: false, // 是否显示默认参照设置
      isShowSqForm: false, // 是否显示事前单配置
      isShowMainBillSetting: false, // 是否显示设置主表列表
      isShowSignatureSetting: false,
      signatureSetting: {
        width: '',
        height: ''
      },
      mainBillMetaIds: [],
      defaultReferenceList: [], // 设置默认参照选项
      cformOptionList: [], // 表单选项数据
      recAcctTypeOptions: [], // 附件类型下拉数据
      selectRecAcctTypeOptions: [], // 已选择的附件类型
      cformSettings: [], // 表单设置
      attTypeSettings: [], // 附件设置
      metaId: '',
      checkedCSList: [], // 选中的表单选项value数据
      checkedCformOptionList: [], // 选中的表单选项数据
      cformSettingList: [], // 表单设置数据
      attTypeSettingList: [], // 附件类型设置数据
      templateName: '', // 选中的默认参照选项
      templateList: [], // 选中的默认参照选项
      formName: '', // 表单名称
      treeParentNodes: [],
      setting: {
        check: {
          enable: false
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'label'
          }
        }
      },
      templateTreeNodeId: '',
      printTemplateId: '',
      sqFormTreeNodeId: '',
      sqFormId: '',
      templatesetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'label'
          }
        }
      }
    }
  },
  computed: {
    tableListSettingFormCols() {
      // 表单要素列表不显示已经在表格列中使用的要素
      var usedLabel = {}
      this.tableListSettingColAll.forEach(item => {
        if (this.$isNotEmpty(item.labelOrigin)) {
          usedLabel[item.labelOrigin] = item
        }
      })
      var itemsUsing = this.commonData.cformCols.filter(item => {
        return this.$isEmpty(usedLabel[item.labelOrigin])
      })

      var items = []
      if (this.$isNotEmpty(this.tableListSettingFormColsSearchText)) {
        // 匹配搜索
        items = itemsUsing.filter(item => {
          return String(item.label.toLowerCase()).match(
            this.tableListSettingFormColsSearchText.toLowerCase())
        })
      } else {
        items = itemsUsing
      }
      return this.$sortColumnList(items)
    },
    tableListSettingColAllShow() { // 表格列定义实际显示的数据
      var items = []
      if (this.$isNotEmpty(this.tableListSettingColAllSearchText)) {
        items = this.tableListSettingColAll.filter(item => {
          return String(item.labelOrigin.toLowerCase())
            .match(this.tableListSettingColAllSearchText.toLowerCase())
        })
      } else {
        items = this.tableListSettingColAll
      }
      const orderTem = Math.max.apply(Math, items.map(item => { return item.order })) + 1
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.order = this.$isEmpty(items) ? 0 : orderTem
      return this.$sortColumnList(items)
    }
  },
  mounted() {
    this.selectCformOptionList() // 查询表单选项设置列表
    this.initQuotaClassifyTreeData()
  },
  methods: {
    initQuotaClassifyTreeData() {
      this.$callApiParams('selectClassifyList',
        { dataType: 'CformMetaEntity' }, result => {
          this.treeParentNodes = result.data
          return true
        })
    },
    selectRecAcctTypeChange(value) { // 选择附件类别改变数据
      // 改变一个选项状态时
      var selectAttTypeSettingList = this.recAcctTypeOptions.filter(item => {
        if (value.indexOf(item.eleCode) > -1) {
          return item
        }
      })
      // 过滤已有的附件类型设置
      const oldAttTypeSettings = this.attTypeSettings.filter(item => {
        if (value.indexOf(item.attTypeCode) > -1) {
          return item
        }
      })
      // 已有表单设置optionName数组
      var oldOptionNameArr = []
      oldAttTypeSettings.forEach(setting => {
        oldOptionNameArr.push(setting.attTypeCode)
      })
      // 过滤选择数组中不包含旧表单设置的数据
      const newSelectAttTypeOption = selectAttTypeSettingList.filter(item => oldOptionNameArr.indexOf(item.eleCode) === -1)
      var newAttTypeSettings = []
      newSelectAttTypeOption.forEach(item => {
        var attTypeSetting = { // 初始化表单设置数据
          metaId: this.metaId,
          attTypeId: item.eleId,
          attTypeCode: item.eleCode,
          attTypeName: item.eleName
        }
        newAttTypeSettings.push(attTypeSetting)
      })
      this.attTypeSettingList = [].concat(oldAttTypeSettings, newAttTypeSettings)
    },
    closeDlg() {
      this.moreSettingVisible = false
      Object.keys(this.signatureSetting).forEach(item => {
        this.signatureSetting[item] = ''
      })
    },
    selectCformSettingList() { // 表单设置选项
      this.mainBillMetaIds = []
      if (this.$isNotEmpty(this.$refs.supTreeMainBill)) {
        this.$refs.supTreeMainBill.getNodeChecked(false)
        this.$refs.supTreeMainBill.treeObj.cancelSelectedNode()
      }

      this.$callApiParams('selectCformSettingList', { META_ID_eq: this.metaId },
        result => {
          if (result.success) {
            this.isShowRecAcctTypeOption = false
            this.isShowPrintTemplate = false
            this.isShowDefaultReference = false
            this.isShowSqForm = false
            this.isShowMainBillSetting = false
            this.isShowSignatureSetting = false
            this.cformSettings = result.data
            this.cformSettingList = result.data
            this.checkedCSList = []

            if (this.cformSettings.length > 0) {
              this.cformSettings.forEach(setting => {
                this.checkedCSList.push(setting.optionName)
                if (setting.optionName === '必须上传附件') {
                  this.isShowRecAcctTypeOption = true
                }
                if (setting.optionName === '打印模板') {
                  this.isShowPrintTemplate = true
                }
                if (setting.optionName === '默认参照设置') {
                  this.isShowDefaultReference = true
                  this.selecctRefDefaultTemplateSettingList() // 查询默认参照设置
                }
                if (setting.optionName === '事前单配置') {
                  this.isShowSqForm = true
                }
                if (setting.optionName === '设置签章') {
                  this.isShowSignatureSetting = true
                  if (setting.extraData) {
                    const widthAndHeight = setting.extraData.split(';')
                    widthAndHeight.forEach(item => {
                      Object.keys(this.signatureSetting).forEach(ss => {
                        const itemKey = item.split(':')
                        if (ss === itemKey[0]) {
                          if (itemKey[1] === '') {
                            this.signatureSetting[ss] = ''
                          } else {
                            this.signatureSetting[ss] = itemKey[1]
                          }
                        }
                      })
                    })
                  }
                }
                if (setting.optionName === '设置汇总表') {
                  this.isShowMainBillSetting = true
                  var mainBillMetaId = setting.extraData
                  if (this.$isNotEmpty(mainBillMetaId)) {
                    this.treeParentNodes.forEach(node => {
                      if (node.itemKey === mainBillMetaId) {
                        this.mainBillMetaIds = [node.id]
                        var mainBillTreeNode = this.$refs.supTreeMainBill.treeObj.getNodeByParam(
                          'id', node.id, null)
                        this.$refs.supTreeMainBill.treeObj.selectNode(mainBillTreeNode)
                        this.$refs.supTreeMainBill.getNodeChecked(true)
                      }
                    })
                  }
                }
              })
            }
            this.getIsShowLowerPart()
          }
          return true
        })
    },
    getIsShowLowerPart() {
      const mainTable = document.querySelector('#cformOption') // 获取主表dom元素
      if (!this.isShowRecAcctTypeOption && !this.isShowPrintTemplate && !this.isShowSqForm &&
        !this.isShowMainBillSetting && !this.isShowSignatureSetting && !this.isShowDefaultReference) {
        //  如果没有下半部分没有可以显示的内容则隐藏下半部分
        this.isShowLowerPart = false
        mainTable.style = 'height: 505px;max-height:none'
      } else {
        this.isShowLowerPart = true
        mainTable.style = 'height: 100%;'
      }
    },
    selectAttachmentTypeSettingList() { // 附件类型选项
      this.$callApiParams('selectAttachmentTypeSettingList', { META_ID_eq: this.metaId },
        result => {
          if (result.success) {
            this.attTypeSettings = result.data
            this.selectRecAcctTypeOptions = []
            this.attTypeSettings.forEach(item => {
              this.selectRecAcctTypeOptions.push(item.attTypeCode)
            })
          }
          return true
        })
    },
    selecctPrintTemplateSettingList() {
      this.templateTreeNodeId = []
      this.printTemplateId = []
      if (this.$isNotEmpty(this.$refs.supTree)) {
        this.$refs.supTree.treeObj.cancelSelectedNode()
        this.$refs.supTree.treeObj.checkAllNodes(false)
        this.$refs.supTree.getNodeChecked(false)
      }
      this.$callApiParams('selecctPrintTemplateSettingList', { META_ID_eq: this.metaId },
        result => {
          if (result.success) {
            if (result.data.length > 0) {
              result.data.forEach(setting => {
                this.templateTreeNodeId.push(setting.templateTreeNodeId)
                this.printTemplateId.push(setting.templateId)
                if (this.$isNotEmpty(this.$refs.supTree)) {
                  var node = this.$refs.supTree.treeObj.getNodeByParam('id', setting.templateTreeNodeId, null)
                  this.$refs.supTree.treeObj.checkNode(node, true, false)
                  this.$refs.supTree.treeObj.selectNode(node)
                  this.$refs.supTree.getNodeChecked(true)
                }
              })
            }
          }
          return true
        })
    },
    // 查询默认弹窗是否设置
    selecctRefDefaultTemplateSettingList() {
      this.$callApiParams('selecctRefDefaultTemplateSettingList', { META_ID_eq: this.metaId }, result => {
        if (result.success) {
          if (result.data.length > 0) {
            this.templateName = result.data[0].templateName
            this.templateList.push(result.data[0])
          }
        }
        return true
      })
    },
    // 查询默认弹窗选项
    selectRefTemplateSettingData() {
      this.$callApiParams('selectRefTemplateSettingData', { metaId: this.metaId }, result => {
        if (result.success) {
          if (result.data.length > 0) {
            this.defaultReferenceList = result.data
          }
        }
        return true
      })
    },
    // 参照弹窗设置
    querySearch(queryString, cb) {
      var restaurants = this.defaultReferenceList
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    // 参照弹窗设置
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.label.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    empty() {
      this.templateList = []
    },
    // 参照弹窗设置
    handleSelect(item) {
      this.templateList = []
      this.templateName = item.label
      this.templateList.push(item)
    },
    selectSqFormSettingList() {
      this.sqFormTreeNodeId = ''
      this.sqFormId = ''
      if (this.$isNotEmpty(this.$refs.supTreeSqForm)) {
        this.$refs.supTreeSqForm.getNodeChecked(false)
        this.$refs.supTreeSqForm.treeObj.cancelSelectedNode()
      }
      this.$callApiParams('selectSqFormSettingList', { META_ID_eq: this.metaId },
        result => {
          if (result.success) {
            if (result.data.length > 0) {
              this.sqFormTreeNodeId = result.data[0].sqFormTreeNodeId
              this.sqFormId = result.data[0].sqFormId
              if (this.$isNotEmpty(this.$refs.supTreeSqForm)) {
                var node = this.$refs.supTreeSqForm.treeObj.getNodeByParam('id', this.sqFormTreeNodeId, null)
                this.$refs.supTreeSqForm.treeObj.selectNode(node)
                this.$refs.supTreeSqForm.getNodeChecked(true)
              }
            }
          }
          return true
        })
    },
    isCformSetting() { // 当前标签是否是“表单设置”
      return this.moreSettingTab.startsWith('表单设置')
    },
    isImgSetting() {
      // 当前标签是否是
      return this.moreSettingTab.startsWith('图片设置')
    },
    isCformPermissionsSetting() {
      // 当前标签是否是"表单权限配置"
      return this.moreSettingTab.startsWith('权限配置')
    },
    initSetting(meta) {
      this.metaId = meta.main.id
      this.hasBa = this.labelOriginColItemMap(meta.colItems) // 获取表单数据是多指标还是单指标
      this.selectCformSettingList() // 查询表单设置列表
      this.selectAttachmentTypeSettingList() // 查询附件类型选项
      this.selecctPrintTemplateSettingList() // 查询打印配置选项
      this.selectRefTemplateSettingData() // 查询默认参照选项
      this.selectSqFormSettingList() // 查询事前单配置选项
      this.formName = this.$parent.formName
      this.selectAttTypeList(meta.exData.attTypeTableName) // 查询附件类型列表

      // 初始化imgFlie组件 更新bizId
      if (this.$isNotEmpty(this.$refs.ImgFile)) {
        this.$refs.ImgFile.init(meta.main.id)
      }
    },
    labelOriginColItemMap(colItem) {
      var itemMap = []
      colItem.forEach(item => {
        itemMap.push(item.labelOrigin)
      })
      return this.$isFormContainsRepeatrepeatValues(itemMap, '指标')
    },
    handleCloseOption() {
      this.isShowRecAcctTypeOption = false
      this.isShowPrintTemplate = false
      this.isShowDefaultReference = false
      this.isShowMainBillSetting = false
      this.isShowSqForm = false
      this.optionDialogVisible = false
      this.isShowSignatureSetting = false
    },
    handleCheckedCSChange(value) { // 多选改变事件
      // 改变一个选项状态时
      this.checkedCformOptionList = this.cformOptionList.filter(item => {
        if (value.indexOf(item.tname) > -1) {
          return item
        }
      })
      // 过滤已有的表单设置
      const oldCformSettings = this.cformSettings.filter(item => {
        if (value.indexOf(item.optionName) > -1) {
          return item
        }
      })
      // 已有表单设置optionName数组
      var oldOptionNameArr = []
      oldCformSettings.forEach(setting => {
        oldOptionNameArr.push(setting.optionName)
      })
      // 过滤选择数组中不包含旧表单设置的数据
      const newCheckedCformOption = this.checkedCformOptionList.filter(item => oldOptionNameArr.indexOf(item.tname) === -1)
      var newCformSettings = []
      newCheckedCformOption.forEach(item => {
        var cformSetting = {// 初始化表单设置数据
          metaId: this.metaId,
          optionId: item.id,
          optionName: item.tname,
          extraData: item.extraData
        }
        newCformSettings.push(cformSetting)
      })
      this.cformSettingList = [].concat(oldCformSettings, newCformSettings)
      const showData = this.cformSettingList.filter(item => item.optionName === '必须上传附件')
      const showTemplateData = this.cformSettingList.filter(item => item.optionName === '打印模板')
      const showDefaultReference = this.cformSettingList.filter(item => item.optionName === '默认参照设置')
      const showMainBillSetting = this.cformSettingList.filter(item => item.optionName === '设置汇总表')
      const sqFormData = this.cformSettingList.filter(item => item.optionName === '事前单配置')
      const showSignatureSetting = this.cformSettingList.filter(item => item.optionName === '设置签章')

      if (this.$isNotEmpty(showData)) {
        this.isShowRecAcctTypeOption = true
      } else {
        this.isShowRecAcctTypeOption = false
      }

      if (this.$isNotEmpty(showTemplateData)) {
        this.isShowPrintTemplate = true
      } else {
        this.isShowPrintTemplate = false
      }

      if (this.$isNotEmpty(showDefaultReference)) {
        this.isShowDefaultReference = true
      } else {
        this.isShowDefaultReference = false
      }

      if (this.$isNotEmpty(showMainBillSetting)) {
        this.showMainBillSetting = true
      } else {
        this.showMainBillSetting = false
      }

      if (this.$isNotEmpty(sqFormData)) {
        this.isShowSqForm = true
      } else {
        this.isShowSqForm = false
      }
      if (this.$isNotEmpty(showSignatureSetting)) {
        this.isShowSignatureSetting = true
      } else {
        this.isShowSignatureSetting = false
      }
      this.isShowMainBillSetting = this.$isNotEmpty(showMainBillSetting)
      this.getIsShowLowerPart()
    },
    selectAttTypeList(attTypeTableName) { // 查询附件类型列表
      if (this.$isNotEmpty(attTypeTableName)) {
        this.$callApi('getAccSetEle&tableName=' + attTypeTableName, {}, result => {
          if (result.success) {
            this.recAcctTypeOptions = result.data
          }
          return true
        })
      }
    },
    selectCformOptionList() { // 查询表单选项设置列表
      this.$callApiParams('selectDictResultList', { tparentCode: '10012' },
        result => {
          if (result.success) {
            this.cformOptionList = result.data
          }
          return true
        })
    },
    saveCformSettingMiscVo() {
      // 设置汇总表ID
      const showMainBillSetting = this.cformSettingList.filter(
        item => item.optionName === '设置汇总表')
      if (this.$isNotEmpty(showMainBillSetting)) {
        var selectedNodes = this.$refs.supTreeMainBill.treeObj.getSelectedNodes()
        if (this.$isNotEmpty(selectedNodes) &&
            this.$isNotEmpty(selectedNodes[0]) &&
            this.$isNotEmpty(selectedNodes[0].id)) {
          showMainBillSetting[0].extraData = selectedNodes[0].itemKey
        }
      }

      // this.$callApi('saveCformSettingMiscVo',
      //   { metaId: this.metaId, cformSettingList: this.cformSettingList },
      //   result => {
      //     if (result.success) {
      //       this.$message.success('保存表单设置成功')
      //     }
      //     return true
      //   })
    },
    async saveShowSignatureSetting() {
      // 设置汇总表ID
      let signatureSetting = ''
      const showSignatureSetting = this.cformSettingList.filter(
        item => item.optionName === '设置签章')
      if (this.$isNotEmpty(showSignatureSetting)) {
        Object.keys(this.signatureSetting).forEach((item, index) => {
          if (index <Object.keys(this.signatureSetting).length - 1) {
            signatureSetting += `${item}:${this.signatureSetting[item]};`
          } else {
            signatureSetting += `${item}:${this.signatureSetting[item]}`
          }
        })
        showSignatureSetting[0].extraData = signatureSetting
      }
      await new Promise(re => {
        var values = []
        this.cformSettingList.forEach(item => {
          values.push(item.optionName)
        })
        if (this.hasBa && values.indexOf('多指标参照') !== -1) {
          this.$message.error('表单中已绑定多个指标，不能勾选多指标参照！')
        } else {
          this.$callApi('saveCformSettingMiscVo',
            { metaId: this.metaId, cformSettingList: this.cformSettingList },
            result => {
              if (result.success) {
                Object.keys(this.signatureSetting).forEach(item => {
                  this.signatureSetting[item] = ''
                })
                this.$findRefInner(this.$parent, 'formFormat').cformSettings = result.data.cformSettingList
                this.$message.success('保存表单设置成功')
              }
              re()
              return true
            })
        }
      })
    },
    saveAttTypeSettingListVo() {
      // if (this.$isNotEmpty(this.attTypeSettingList)) {
      this.$callApi('saveAttTypeSettingListVo', { attTypeSettingList: this.attTypeSettingList, metaId: this.metaId },
        result => {
          if (result.success) {
            this.$message.success('保存表单附件设置成功')
          }
          return true
        })
      // }
    },
    // 保存默认参照弹窗设置
    saveRefTemplate() {
      const list = []
      if (this.isShowDefaultReference) { // 配置了默认参照设置
        if (this.templateList.length > 0) {
          list.push({
            metaId: this.metaId,
            templateId: this.templateList[0].itemKey ? this.templateList[0].itemKey : '',
            templateTreeNodeId: this.templateList[0].id
          })
          this.$callApi('saveRefDefaultTemplateSettingVo', { refDefaultTemplateSettingEntityList: list },
            result => {
              return true
            })
        }
      } else {
        this.$callApiParams('deleteRefDefaultTemplateSetting', { metaId: this.metaId },
          result => {
            return true
          })
      }
      return true
    },
    savePrintTemplateSetting() {
      const printSettingList = []
      if (this.isShowPrintTemplate) { // 配置“打印模板”选项
        if (this.$refs.supTree.objValues.length > 0) { // objValue是只获取子级的节点集合（配置isGetChild=true）
          this.templateTreeNodeId = []
          this.printTemplateId = []
          this.$refs.supTree.objValues.forEach(node => {
            const tempalteData = {}
            tempalteData.metaId = this.metaId
            tempalteData.templateId = node.itemKey
            tempalteData.templateTreeNodeId = node.id
            printSettingList.push(tempalteData)
            this.templateTreeNodeId.push(node.id)
            this.printTemplateId.push(node.itemKey)
          })
        } else {
          // 树形下拉没有被选中，则为空
          this.templateTreeNodeId = []
          this.printTemplateId = []
        }
        if (this.$isEmpty(this.templateTreeNodeId)) {
          this.$message.error('打印模板不能为空')
          return false
        }
        this.$callApi('savePrintSettingListVo', { printSettingList: printSettingList },
          result => {
            return true
          })
      } else {
        // 如果没有选中”打印模板“选项
        this.templateTreeNodeId = []
        this.printTemplateId = []
        // 删除后台数据
        this.$callApiParams('deletePrintTemplateSetting', { metaId: this.metaId },
          result => {
            return true
          })
      }
      return true
    },
    saveSqFormSetting() {
      if (this.isShowSqForm) {
        if (this.$refs.supTreeSqForm.treeObj.getSelectedNodes()[0]) {
          this.sqFormTreeNodeId = this.$refs.supTreeSqForm.treeObj.getSelectedNodes()[0].id
          this.sqFormId = this.$refs.supTreeSqForm.treeObj.getSelectedNodes()[0].itemKey
        } else {
          // 树形下拉没有被选中，则为空
          this.sqFormTreeNodeId = ''
          this.sqFormId = ''
        }
        if (this.$isEmpty(this.sqFormTreeNodeId)) {
          this.$message.error('事前配置不能为空')
          return false
        }
      } else {
        // 如果没有选中“事前单配置”选项
        this.sqFormTreeNodeId = ''
        this.sqFormId = ''
      }
      const sqFormData = {}
      sqFormData.metaId = this.metaId
      sqFormData.sqFormId = this.sqFormId
      sqFormData.sqFormTreeNodeId = this.sqFormTreeNodeId

      this.$callApi('saveSqFormSetting', sqFormData,
        result => {
          return true
        })
      return true
    },
    async hideDialog() { // 点击“确定”按钮
      if (this.isTabTableColListSetting() &&
          this.isTableListSettingColEditing) { // 当前页签是“表格列设置”，并且正在编辑表格列
        this.btTableListSettingSaveCol(true,
          result => { // 此时先保存再关闭窗体
            this.moreSettingVisible = false
          })
      } else if (this.isCformSetting()) { // 当前页签是“表单设置”
        const savePrintSuccess = this.savePrintTemplateSetting()
        const saveRefSuccess = this.saveRefTemplate()
        if (!savePrintSuccess || !saveRefSuccess) {
          return
        }
        this.saveCformSettingMiscVo()
        this.saveAttTypeSettingListVo()
        this.saveSqFormSetting()
        await this.saveShowSignatureSetting()

        this.isShowRecAcctTypeOption = false
        this.isShowPrintTemplate = false
        this.isShowDefaultReference = false
        this.isShowMainBillSetting = false
        this.isShowSqForm = false
        this.moreSettingVisible = false
        this.isShowSignatureSetting = false
        this.$findRefInner(this.$parent, 'formFormat').refreshSignature && this.$findRefInner(this.$parent, 'formFormat').refreshSignature()
      } else if (this.isImgSetting()) {
        const imgFileObj = this.$refs.ImgFile.imgFileObj || ''
        const dataInfoObj = this.$refs.ImgFile.dataInfoObj || ''
        // 点击确定插入图片做保存
        if (this.$isNotEmpty(imgFileObj) && this.$isNotEmpty(dataInfoObj)) {
          for (let i = 0; i < imgFileObj.length; i++) {
            this.$refs.ImgFile.illustrate(imgFileObj[i], dataInfoObj[i], '', () => {
              // 调用保存
              setTimeout(() => {
                this.$parent.btSave()
                this.$refs.ImgFile.imgFileObj = []
                this.$refs.ImgFile.dataInfoObj = []
              }, 500)
              // Promise.resolve().then(() => {
              //   this.$parent.btSave()
              // })
            })
          }
        } else {
          this.$parent.btSave()
        }
        this.moreSettingVisible = false
      } else if (this.isCformPermissionsSetting()) {
        var formPermissionsVo = {}
        formPermissionsVo.formPermissionsVo = this.$refs.permissions.formPermissionsVo
        this.$callApi('saveUserFormPermissions',
          formPermissionsVo, result => {
            if (result.success) {
              this.$message.success('保存表单权限设置成功')
            }
            return true
          })
        this.$refs.permissions.formPermissions = []
        this.moreSettingVisible = false
      } else { // 直接关闭窗体
        this.moreSettingVisible = false
      }
    },
    newCommonData() {
      return {
        cformCols: [], // 表单要素列表
        colTypes: [], // 表格列类型
        reserveDatas: [], // 绑定操作
        dataRef: []// 绑定操作
      }
    },
    showMoreSetting(cformTypeData, currentFormType) { // 显示更多设置弹框
      this.cformTypeData = cformTypeData
      this.moreSettingVisible = true

      var isTabInit = this.$isNotEmpty(this.moreSettingTab)
      this.$nextTick(() => {
        this.tableListSettingCurrentFormType = currentFormType
        if (!isTabInit) {
          var elTabs = $('.moreSettingDlg .el-tabs__item:first')
          var firstTabName = elTabs.attr('id').replace('tab-', '')
          this.moreSettingTab = firstTabName
          this.moreSettingTabClick({ name: this.moreSettingTab })
        }
        this.selectTableColumnList()
        this.selectCformTableListColumnCommonData()
      })
    },
    isTabTableColListSetting() { // 当前标签是否是“表格列设置”
      return this.moreSettingTab.startsWith('表格列设置')
    },
    currentFormTypeChanged() { // 表单列表变化时，刷新表格列列表
      this.selectTableColumnList()
      this.selectCformTableListColumnCommonData()
    },
    moreSettingTabClick(tab, event) {
      // 更多设置标签名称的规则：xxx_width:height
      var widthHeightPart = tab.name.split('_')[1]
      var widthHeight = widthHeightPart.split(':')
      if (widthHeight.length === 2) { // 用页签name设置更多设置弹框大小
        this.$setDlgSize(this, 'moreSettingDlg',
          parseInt(widthHeight[0]), parseInt(widthHeight[1]))
      }
    },
    getCurrentDataType() {
      return '表格列:' + this.tableListSettingCurrentFormType
    },
    btAddClick() {
      this.isTableListSettingColEditing = true
      this.colItem = {
        dataType: this.getCurrentDataType(),
        label: '新增表格列',
        prop: '',
        order: this.order,
        align: 'left',
        colType: '文本',
        sortable: false,
        fixable: false,
        exportable: true
      }
    },
    btTableListSettingSaveCol(returnValue, callback) {
      // 如果labelOrigin等于空，表明是新增表格列，此时设置labelOrigin=label
      if (this.$isEmpty(this.colItem.labelOrigin)) {
        this.colItem.labelOrigin = this.colItem.label
      } else {
        // 否则表明是修改，设置labelAlias=label，这样能保证新增之后labelOrigin保持不变
        this.colItem.labelAlias = this.colItem.label
        this.colItem.labelOrigin = this.colItem.label
      }
      this.isTableListSettingColEditing && this.saveCols([this.colItem], returnValue, callback)
    },
    saveColsByFormCol(ids) {
      var cols = []
      ids.forEach(id => {
        var item = this.$clone(this.cformColsMap[id])
        // item.sourceId = item.id
        item.id = ''
        item.bizid = ''
        item.reserveData = ''
        item.lockVersion = 0
        item.width = 80
        item.dataType = this.getCurrentDataType()
        item.sortable = false
        item.exportable = true
        item.fixable = false

        if (item.colType === '金额' || item.colType === '百分比') {
          item.align = 'right'
        } else if (item.colType === '整数' || item.colType === '小数') {
          item.colType = '数值'
          item.align = 'right'
        } else {
          item.colType = '文本'
          item.align = 'left'
        }
        cols.push(item)
      })
      this.saveCols(cols, true)
    },
    saveCols(cols, returnValue, callback) {
      this.isTableListSettingColSaving = true
      this.$saveTableColumn({ cols: cols }, resut => {
        this.selectTableColumnList(rt => {
          this.isTableListSettingColEditing = false
          if (typeof (callback) === 'function') {
            callback(rt)
          }
        })
        return returnValue
      }, result => { this.isTableListSettingColSaving = false })
    },
    selectCformTableListColumnCommonData() {
      this.commonData = this.newCommonData()
      this.cformColsMap = {}
      this.$callApiParams('selectCformTableListColumnCommonData',
        { 'FORM_TYPE_eq': this.tableListSettingCurrentFormType },
        result => {
          this.commonData = result.attributes
          this.commonData.cformCols.forEach(item => {
            this.cformColsMap[item.id] = item
          })
          return true
        })
    },
    selectTableColumnList(callback) { // 刷新表格列列表
      if (this.isTabTableColListSetting()) {
        this.tableListSettingColAllChange([]) // 清除原先编辑数据
        this.$selectTableColumnList(this.getCurrentDataType(),
          result => {
            this.tableListSettingColAll = result.data
            this.isTableListSettingColSaving = false
            if (callback) {
              callback(result)
            }
          }, result => { this.isTableListSettingColSaving = false })
      }
    },
    tableListSettingFormColsDblclick(row, column, event) {
      // 双击表单要素列表行，则根据该要素新增一个表格列
      this.saveColsByFormCol([row.id])
    },
    btSelectClick() { // 批量根据表单要素新添表格列
      var ids = this.$getTableCheckedIds(this.$refs.tableListSettingFormCols)
      this.saveColsByFormCol(ids)
    },
    tableListSettingColAllDblclick(row, column, event) { // 双击表格列表行，则删除该表格列
      var $table = { selection: [{ id: row.id }] } // 构造模拟的table
      this.deleteColItems($table)
    },
    btDeleteClick() { // 删除表格列
      this.deleteColItems(this.$refs.tableListSettingColAll)
    },
    deleteColItems($table) { // 删除表格列
      this.isDeleting = true
      this.$deleteTableColumn(
        $table,
        result => {
          this.selectTableColumnList(re => {
            this.isDeleting = false
            this.tableListSettingColAllChange([]) // 清除原先编辑数据
          })
        }
      )
    },
    tableListSettingFormColsChange(checkedRows) {
      this.btSelectDisabledFormCols = (checkedRows.length === 0)
    },
    tableListSettingColAllChange(checkedRows) {
      this.btDeleteDisabled = true
      this.isTableListSettingColEditing = false
      this.btTableListSettingColText = '新增表格列'
      this.colItem = {}

      if (checkedRows.length > 0) {
        this.btDeleteDisabled = false
        if (checkedRows.length === 1) {
          this.colItem = checkedRows[0]
          this.isTableListSettingColEditing = true
          this.btTableListSettingColText = '修改表格列'
        }
      }
    },
    // 处理input只能输入数字
    numericTypes(val) {
      this.colItem.order = val.replace(/[^\d]/g, '')
    },
    formatColWidthToNum(val) {
      this.colItem.width = val.replace(/^\D*([1-9]\d*\.?\d{0,2})?.*$/, '$1')
    },
    getImgFileTableVal() {
      // 获取图片组件表格数据
      // return this.$refs.ImgFile.getVal() || ''
      if (this.$isNotEmpty(this.$refs.ImgFile)) {
        return this.$refs.ImgFile.getVal()
      }
    },
    /**
     * 如果是非超链接，那么就设置绑定操作和请求参数为空
     * @param value
     */
    clearBindPram(value) {
      if (value !== '超链接') {
        this.$set(this.colItem, 'reserveData', '')
        this.$set(this.colItem, 'dataRef', '')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .moreSettingDlg .el-tabs {
    display: flex;
    flex-direction: column;
    height: calc(100% - 52px);
  }
  .moreSettingDlg /deep/.el-tabs__content {
    flex: 1;
  }
  .moreSettingDlg .el-tab-pane {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .moreSettingDlg .el-tabs__header { margin: 0 0 15px !important; }
  .tableListSetting { display: flex; }
  .tableListSettingColAll, .tableListSettingColSelected { margin-right: 10px; }
  .tableListSettingColEditButton { margin-bottom: 5px; }
  .tableListSettingColEditForm {
    border: 1px solid #DDDDDD;
    height: 495px;
    width: 259px;
    padding: 10px;
    position: relative;
    overflow-y: scroll;
  }
  .common-page /deep/ .tableListSetting .el-icon-search { display: none; }
  .common-page /deep/ .tableListSetting .colSettingFilterInput { width: 100px; }
  /deep/ .el-checkbox__label {
    width: 118px;
    //overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  #cformName{
    padding: 5px;
  }
  #cformOption{
    border: 1px solid #DDDDDD;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
  }
  #cformOption label{
    padding: 2px 10px;
  }
  #recAcctTypeOption{
    /*border: 1px solid #bbb;*/
    margin-top: 7px;
    padding: 5px;
  }
  .option-canvas{
    /*border: 1px solid #bbb;*/
    margin-top: 7px;
    padding: 5px;
  }
  .signatureSettingForm{
    margin-top: 20px;
    display: flex;
    .el-form-item{
      display: flex;
    }
  }
</style>
<style>
  /* 隐藏单击图片右边功能区域 */
  #luckysheet-modal-dialog-slider-imageCtrl{
    display: none !important;
  }
</style>
