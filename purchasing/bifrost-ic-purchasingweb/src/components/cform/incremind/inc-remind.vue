<template>
    <el-dialog ref="baFrozenDlg" append-to-body
               title="报销提醒设置" width="45%"
               :visible.sync="remindDialogVisible"
               :close-on-click-modal='false'
               :before-close="closeDetail">
      <el-form ref="form" v-show="!isBlockForm" :model="remindMainEntity" :rules="rules">
        <el-row class="row-marginBottom">
          <el-form-item label="对应表单字段:" prop="formFieldName">
            <el-col :span="20">
              <el-input v-model="remindMainEntity.formFieldName" maxlength="20" placeholder="请输入"></el-input>
            </el-col>
          </el-form-item>
        </el-row>
      </el-form>
      <div class="top-btns">
          <el-button
            type="primary"
            size="mini"
            @click="addBtn"
            icon="el-icon-circle-plus-outline">新增</el-button
          >
          <el-button
            size="mini"
            @click="deleteBtn"
            plain
            icon="el-icon-delete"
          >删除</el-button
          >
        </div>
        <page>
          <template #pageContent>
        <div class="bottom-table plan-container">
          <el-table
            border
            row-key="id"
            :data="remindData"
            @selection-change="handleSelectionChange"
            style=" height: 293px; font-size: 14px"
          >
            <el-table-column type="selection" align="center" width="30" />
            <el-table-column
              align="center"
              prop="standardType"
              label="类型"
              width="180"
            >
              <template slot="header">
                <span style="color: #f00">*</span>类型
              </template>
              <template slot-scope="{ row }">
                <el-select clearable class="planCondition" v-model="row.standardType" placeholder="输入类型信息"  filterable>
                  <el-option  v-for="item in stndardList" v-show="havePlan"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="remindMessage"
              label="提醒信息"
              width="400"
            >
              <template  slot="header">
                <span style="color: #f00">*</span>提醒信息
              </template>
              <template slot-scope="{ row }">
                <el-input
                  v-if="havePlan"
                  class="planCondition"
                  v-model="row.remindMessage"
                  placeholder="输入提醒信息"
                />
                <span v-else>{{ row.remindMessage }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="unfold"
              label="默认展开"
            >
              <template  slot="header">
                <span style="color: #f00">*</span>默认展开
              </template>
              <template slot-scope="{ row }">
                <el-radio-group v-model="row.unfold" size="medium">
                  <el-radio label="1">是</el-radio>
                  <el-radio label="2">否</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>
          </el-table>
          <file-view ref="fileView"></file-view>
        </div>
        </template>
        </page>
      <div style="height: 150px;overflow: auto;margin-top: 10px;">
        <uploadFile :files="files" :bizTblName="bizTblName" :bizDataId="attTempId" :recAcctName="recAcctName" @initFileList = "initFilesList"></uploadFile>
      </div>
      <div class="purpolicy-edit-btn"  style="text-align: right;margin-top:10px;" >
        <el-button size="medium"  @click="btEditBack">返回</el-button>
        <el-button size="medium"  type="primary" @click="btEditSave(remindData)">确 定</el-button>
      </div>
    </el-dialog>
</template>
<script>
import $ from 'jquery'
export default {
  name: 'inc-remind',
  data() {
    return {
      checkedRow: [],
      remindData: [],
      remindMainEntity: {},
      havePlan: true,
      remindDialogVisible: false,
      attTempId: '', // 临时Id
      factorVal: '', // 参照名称
      formId: '', // 单据Id
      itemId: '', // 要素ID
      formFieldName: '', // 对应表单字段,
      stndardList: [],
      files: [],
      bizTblName: '',
      isBlockForm: false,
      blockFormField: '',
      recAcctName: '送货单',
      rules: {
        formFieldName: [{ required: true, message: '请输入对应表单字段!', trigger: 'blur' }]
      }

    }
  },
  mounted() {
    this.$callApiParams('getAttTempId', {}, (result) => {
      this.attTempId = result.data.attTempId
      return true
    })
  },
  methods: {
    selectBxRemindList(exparam) {
      this.factorVal = exparam.targetRow.dataRef // 参照
      this.itemId = exparam.targetRow.sourceId // 要素ID
      this.blockFormField = exparam.targetRow.labelOrigin // 要素名称唯一
      this.formId = exparam.metaId// 表单ID
      this.isBlockForm = exparam.isBlockForm
      if (this.$isEmpty(this.factorVal)) {
        this.$message.warning('当前只支持配置存在参照的数据!')
        return false
      }
      this.remindDialogVisible = true
      this.$callApiParams('selectListRemindData', {
        favtorVal: this.factorVal,
        formId: this.formId,
        colType: exparam.coltype,
        itemId: this.itemId,
        isBlockForm: this.isBlockForm
      }, (result) => {
        if (result.success) {
          const reData = result.data
          if (this.$isNotEmpty(reData)) {
            this.remindMainEntity = reData.remindMainEntity
            this.remindData = reData.remindMainEntity.remindEntityList
            this.formFieldName = reData.remindMainEntity.formFieldName
            this.stndardList = reData.factorList
            this.files = reData.attList
          } else {
            this.remindData = []
          }
        }
        return true
      })
    },
    initFilesList(list) {
      this.files = list
    },
    btEditSave() {
      var remindList = {}
      this.remindMainEntity.remindEntityList = this.remindData
      if (this.isBlockForm) {
        this.remindMainEntity.formFieldName = this.blockFormField
      }
      this.remindMainEntity.formId = this.formId
      this.remindMainEntity.factorVal = this.factorVal
      this.remindMainEntity.itemId = this.itemId
      remindList.attTempId = this.attTempId
      remindList.attList = this.files
      remindList.remindMainEntity = this.remindMainEntity
      remindList.isBlockForm = this.isBlockForm
      this.$callApi('saveRemindInfo', remindList, result => {
        this.remindDialogVisible = false
        this.remindData = []
        this.files = []
      })
    },
    btEditBack() {
      this.remindData = []
      this.remindDialogVisible = false
      this.files = []
    },
    beforeUpload(v) {
      if ((v.size / 1024 / 1024) > 5) { return this.$message.error('文件不小不能大于5MB！') }
    },
    openView(row) {
      this.$refs.fileView.open({ fileIds: [row.attId] })
    },
    hideError(rowIndex) {
      var $rows = $('.plan-container .el-table__row')
      $.each($rows, (index, item) => {
        if (index === rowIndex) {
          $(item).find('.planError').removeClass('planError')
        }
      })
    },
    formFormatItem(item) {
      item.planMoney = this.$formatMoney(item.planMoney)
    },
    addBtn() {
      const data = {}
      data.standardType = ''
      data.remindMessage = ''
      data.unfold = '1'
      this.remindData = this.remindData.concat([], data)
    },
    deleteBtn() {
      for (var i = 0; i < this.checkedRow.length; i++) {
        var index = this.remindData.indexOf(this.checkedRow[i])
        this.remindData.splice(index, 1)
      }
    },
    handleSelectionChange(rows) {
      this.checkedRow = rows
    },
    closeDetail() {
      this.remindDialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
  .plan-list {
    width: 100%;
    height: 100% !important;
    display: flex;
    flex-direction: column;
    .plan-container {
      .el-upload {
        .el-button {
          border: none !important;
        }
      }
      .el-tag{
        width: 140px;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        position: relative;
        /deep/  .el-tag__close{
          position: absolute;
          top: 0;
          right: 0;
        }
      }
    }

    .total-amt {
      margin-left: 10px;
      font-size: 14px;
    }
    .top-btns {
      padding-bottom: 10px;
      margin-left: 0px;
      width: 27%;
      display: flex;
      .el-input {
        margin-left: 5px;
      }
    }
    .bottom-table {
      flex: 1;
    }
    /deep/ .el-table .cell {
      padding: 0px 5px !important;
    }
    /deep/ .el-table .warning-row {
      background-color: rgb(255, 43, 43) !important;
    }
    /deep/ .el-table--border .el-table__cell:first-child .cell {
      padding: 0px 5px !important;
    }
    /deep/ .planError {
      border-color: #ff5c00 !important;
    }
  }
</style>
<style lang="scss">
  .mini-table .plan-list .el-table .el-table-column--selection .cell {
    padding: 0px 0px !important;
  }
  .mini-table .plan-list .el-table .cell {
    padding: 0px 3px;
  }
  .mini-table .plan-list .el-table input {
    padding: 0px 3px;
    height: 28px;
  }
  .mini-table .plan-list .el-table .el-input__suffix {
    right: -4px;
  }
  .mini-table .plan-list .el-table .el-select .el-input .el-select__caret {
    font-size: 12px;
  }
  .mini-table .plan-list .el-table .cell .el-date-editor .el-input__prefix {
    display: none;
  }
  .plan-container input.el-input__inner {
    font-size: 12px;
  }
  .plan-container .planMoney input.el-input__inner {
    text-align: right;
    padding-right: 5px;
  }
</style>
