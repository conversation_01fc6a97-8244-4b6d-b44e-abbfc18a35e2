<template>
  <div id="cformAuthDialog">
    <el-dialog
      append-to-body
      :title="`选择${operation}`"
      :visible.sync="dlgVisible"
      width="40%"
      destroy-on-close
      :close-on-click-modal='false'
      @close="handleFormDialogClose">
      <page>
        <template #pageContent>
          <div :style="{ height : '550px'}">
            <sup-tree :setting="setting"
                      ref="supTree"
                      :nodes="treeData"
                      :checkedValues="treeCheckedValues"
                      :is-popover="false"
                      :edit-enable="true"></sup-tree>
          </div>
        </template>
      </page>
      <template #footer>
        <el-button type="primary" @click="handleFormTreeSelect">确定</el-button>
        <el-button @click="handleFormDialogClose">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'cform-auth-dialog',
  props: {},
  watch: {},
  data() {
    return {
      formId: '',
      ruleId: '',
      operation: '',
      activeName: '',
      dlgVisible: false,
      treeData: [],
      treeCheckedValues: [],
      setting: {
        check: { enable: true },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: { name: 'authNames' }
        }
      }
    }
  },
  mounted() {

  },
  methods: {
    handleFormTreeSelect() {
      var nodes = this.$refs.supTree.getCheckedNodes()
      var assValueDataRefIds = []
      nodes.forEach(function(item, index) {
        const id = item.id
        assValueDataRefIds.push(id)
      })
      this.treeData = []
      this.$parent.handleResetRuleAuth(this.activeName,
        nodes, assValueDataRefIds)
      this.dlgVisible = false
    },
    handleFormDialogOpen(formId, ruleId, activeName, checkIds) {
      this.formId = formId
      this.ruleId = ruleId
      this.activeName = activeName
      this.treeCheckedValues = checkIds
      if (this.activeName === 'agenAuthTab') {
        this.operation = '单位权限'
      } else if (this.activeName === 'roleAuthTab') {
        this.operation = '角色权限'
      } else if (this.activeName === 'userAuthTab') {
        this.operation = '用户权限'
      }
      var parameters = {
        formId: this.formId, ruleId: this.ruleId,
        authType: this.activeName
      }
      this.$callApiParams('getCformRuleAuths', parameters,
        result => {
          var retData = result.data.userAuths
          this.treeData = []
          retData.forEach(re => {
            this.treeData.push({
              id: re.id,
              bizid: re.bizid,
              authId: re.authId,
              authName: re.authName,
              authType: re.authType,
              authNames: re.authCode + ' ' + re.authName,
              parentId: re.parentId,
              lockVersion: re.lockVersion,
              authCode: re.authCode
            })
          })
          return true
        })
      this.dlgVisible = true
    },
    handleFormDialogClose() {
      this.treeData = []
      this.treeCheckedValues = []
      this.dlgVisible = false
    }
  }
}
</script>
<style lang="scss">
  .row-marginBottom {
    margin-bottom: 10px;
  }
</style>
