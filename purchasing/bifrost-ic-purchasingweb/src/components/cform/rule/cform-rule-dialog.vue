<template>
  <div id="cformRuleDialog">
    <el-dialog
      append-to-body
      :title="`权限控制`"
      :visible.sync="dlgVisible"
      destroy-on-close
      width="80%"
      :close-on-click-modal='false'
      @close="handleFormDialogClose">
      <div>
        <page>
          <template #pageContent>
            <el-row :gutter="24">
              <el-col :span="6">
                <el-card body-style="padding: 5px;" style="height:auto; padding: 3px 3px;">
                  <div slot="header" class="clearfix">
                    <el-button type="primary" @click="handleFormRuleCreate">新增规则</el-button>
                    <el-button type="" @click="handleFormRuleDelete">删除规则</el-button>
                  </div>
                  <div style="">
                    <div class="">
                      <el-form ref="queryForm" size="mini" :model="queryForm" @submit.native.prevent style="border:0px">
                        <el-form-item label-position="left" label="" prop="ruleName">
                          <el-input placeholder="请输入规则名称"
                                    class="colSettingFilterInput" @keyup.native.enter="handleFormRuleQuery"
                                    style="width:100%;" v-model="queryForm.ruleName" prop="ruleName">
                            <i slot="suffix" class="el-icon-search" @click="handleFormRuleQuery"></i>
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </div>
                    <el-table ref="cformRuleTable" height="600" style="overflow-x:hidden;width:100%"
                              :data="cformRuleList"
                              :show-header=false
                              :highlight-current-row=true
                              :highlight-selection-row=true
                              @row-click="handleCurrentChange">
                      <el-table-column label="" :header-align="left" prop="ruleNames"></el-table-column>
                    </el-table>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="18">
                <div :style="{ height : '650px'}">
                  <el-card shadow="always" body-style="padding: 5px;" style="height:auto; padding: 3px 3px;border:0px">
                    <div slot="header" class="header-icon">
                      <i></i><b>基本信息</b>
                    </div>
                    <div>
                      <el-form ref="ruleForm" size="mini" class="cus-form" :model="ruleForm">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label-width="110px" label="规则名称" prop="ruleName">
                              <el-input placeholder="请输入" v-model="ruleForm.ruleName" maxlength="64"></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label-width="110px" label="是否启用" prop="ruleState">
                              <el-radio-group v-model="ruleForm.ruleState">
                                <el-radio label="启用">启用</el-radio>
                                <el-radio label="停用">停用</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row class="" v-if="!isFormRule">
                          <el-col :span="12">
                            <el-form-item label-width="110px" label="附件类型" prop="achTypeId">
                              <el-select v-model="ruleForm.achTypeId" filterable clearable>
                                <el-option
                                  v-for="item in cformRuleAchTypes"
                                  :key="item.eleId"
                                  :label="item.eleName"
                                  :value="item.eleId">
                                </el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row class="">
                          <el-col :span="24">
                            <el-form-item label-width="110px" label="规则说明" prop="ruleDesc">
                              <el-input type="textarea" rows="1" placeholder="请输入"
                                        v-model="ruleForm.ruleDesc" maxlength="128"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </div>
                  </el-card>
                  <div>&nbsp;</div>
                  <el-card shadow="always" body-style="padding: 5px;" style="height:auto; padding: 3px 3px;border:0px">
                    <div slot="header" class="header-icon" shadow="always">
                      <i></i><b>条件信息</b>
                    </div>
                    <div>
                      <el-form ref="metaForm" size="mini" :model="metaForm">
                        <el-row class="">
                          <el-col :span="12">
                            <el-form-item label-width="110px" label="状态项" prop="stateItem">
                              <el-select v-model="metaForm.stateItem" filterable clearable>
                                <el-option
                                  v-for="(state, index) in cformRuleStates"
                                  :key="index"
                                  :label="state"
                                  :value="state">
                                </el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label-width="110px" label="要素项" prop="cformMetaName">
                              <el-select v-model="metaForm.cformMetaName" filterable clearable
                                         @change="handleCformMetaNameChange">
                                <el-option
                                  v-for="column in handleCformRuleColumns"
                                  :key="column.bizId"
                                  :label="column.prop"
                                  :value="column.bizId">
                                </el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row class="" v-if="isFormRule">
                          <el-col :span="6">
                            <el-form-item label-width="110px" label="是否隐藏" prop="hideEnable">
                              <el-radio-group v-model="metaForm.hideEnable" @input="handleHideEnableClick">
                                <el-radio label="是">是</el-radio>
                                <el-radio label="否">否</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item label-width="110px" label="是否编辑" prop="editEnable">
                              <el-radio-group v-model="metaForm.editEnable" @input="handleEditEnableClick">
                                <el-radio label="是">是</el-radio>
                                <el-radio label="否">否</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item label-width="110px" label="是否必填" prop="mustInput">
                              <el-radio-group v-model="metaForm.mustInput">
                                <el-radio label="是">是</el-radio>
                                <el-radio label="否">否</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row class="" v-if="isFormRule">
                          <el-col :span="6">
                            <el-form-item label-width="110px" label="页面搜索" prop="searchEnable">
                              <el-radio-group v-model="metaForm.searchEnable">
                                <el-radio label="是">是</el-radio>
                                <el-radio label="否">否</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item label-width="110px" label="列表展示" prop="gridShow">
                              <el-radio-group v-model="metaForm.gridShow">
                                <el-radio label="是">是</el-radio>
                                <el-radio label="否">否</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row class="" v-if="!isFormRule">
                          <el-col :span="5">
                            <el-form-item label-width="110px" label="关联要素值"
                                          prop="associateSymbol">
                              <el-select v-model="metaForm.associateSymbol" filterable clearable>
                                <el-option
                                  v-for="associate in cformRuleAssociates"
                                  :key="associate.symbol"
                                  :label="associate.name"
                                  :value="associate.symbol">
                                </el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col class="line" :span="1"></el-col>
                          <el-col :span="19">
                            <el-form-item label-position="left" label-width="" label="" prop="associateValue">
                              <el-input :placeholder="assValuePlaceHolder" :readonly="assValueReadOnly"
                                        v-model="metaForm.associateValue" @click.native="showDataRefDialog"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row class="">
                          <el-col :span="8" style="margin-left: 5px">
                            <el-button type="primary" @click="handleFormRuleMetaAppend">新增</el-button>
                            <el-button type="" @click="handleFormRuleMetaDelete">删除</el-button>
                          </el-col>
                        </el-row>
                        <el-row class="" v-if="isFormRule">
                          <div style="padding: 3px;height: 200px">
                            <el-table ref="cformRuleMetaTable" border
                                      :data="cformRuleMetaList"
                                      :highlight-current-row=true
                                      :highlight-selection-row=true
                                      @selection-change="handelCformRuleMetaSelected"
                                      @row-click="handelCformRuleMetaOnClick">
                              <el-table-column type="selection" width="25"></el-table-column>
                              <el-table-column label="要素项" prop="cformMetaName"></el-table-column>
                              <el-table-column label="状态项" prop="stateItem"></el-table-column>
                              <el-table-column label="事件" prop="cformMetaEvent"></el-table-column>
                            </el-table>
                          </div>
                        </el-row>
                        <el-row class="" v-if="!isFormRule">
                          <div style="padding: 3px;height: 200px">
                            <el-table ref="attachRuleMetaTable" border
                                      :data="cformRuleMetaList"
                                      :highlight-current-row=true
                                      :highlight-selection-row=true
                                      @selection-change="handelAttachRuleMetaSelected"
                                      @row-click="handelAttachRuleMetaOnClick">
                              <el-table-column type="selection" width="25"></el-table-column>
                              <el-table-column label="要素项" prop="cformMetaName"></el-table-column>
                              <el-table-column label="公式" prop="associateSymbolName"></el-table-column>
                              <el-table-column label="要素值" prop="associateValue"></el-table-column>
                              <el-table-column label="状态项" prop="stateItem"></el-table-column>
                            </el-table>
                          </div>
                        </el-row>
                      </el-form>
                    </div>
                  </el-card>
                  <div>&nbsp;</div>
                  <el-card shadow="always" body-style="padding: 5px;" style="height:auto; padding: 3px 3px;">
                    <div slot="header" class="header-icon">
                      <i></i><b>权限信息</b>
                    </div>
                    <div>
                      <el-form
                        ref="typeForm" size="mini">
                        <el-row class="">
                          <el-col :span="10" style="margin-left: 5px">
                            <el-tabs v-model="activeName" @tab-click="handleFormTabClick">
                              <el-tab-pane label="单位权限" name="agenAuthTab" style="height:100px">
                                <ul>
                                  <li v-for="(agenAuth, index) in cformRuleAgenAuths" :key="index">
                                    {{agenAuth.authCode}} {{agenAuth.authName}}
                                  </li>
                                </ul>
                              </el-tab-pane>
                              <el-tab-pane label="角色权限" name="roleAuthTab" style="height:150px">
                                <ul>
                                  <li v-for="(roleAuth, index) in cformRuleRoleAuths" :key="index">
                                    {{roleAuth.authCode}} {{roleAuth.authName}}
                                  </li>
                                </ul>
                              </el-tab-pane>
                              <el-tab-pane label="用户权限" name="userAuthTab" style="height:150px">
                                <ul>
                                  <li v-for="(userAuth, index) in cformRuleUserAuths" :key="index">
                                    {{userAuth.authCode}} {{userAuth.authName}}
                                  </li>
                                </ul>
                              </el-tab-pane>
                            </el-tabs>
                          </el-col>
                          <el-col :span="6">
                            <i class="el-icon-s-tools" style="font-size:30px;color: grey"
                               @click="handleLoadRuleAuth"></i>
                          </el-col>
                        </el-row>
                      </el-form>
                    </div>
                  </el-card>
                </div>
              </el-col>
            </el-row>
          </template>
        </page>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleCformRuleModify">保存</el-button>
        <el-button @click="handleFormDialogClose()">关闭</el-button>
      </template>
    </el-dialog>
    <cform-auth-dialog ref="cformAuthDialog"/>
    <cform-dataref-dialog ref="cformDataRefDialog"/>
  </div>
</template>
<script>
export default {
  name: 'cform-rule-dialog',
  props: {},
  watch: {},
  data() {
    return {
      tabId: '',
      formId: '',
      versionId: '',
      tabName: '',
      curRuleId: '',
      clickTabName: '',
      isFormRule: true,
      assValueDataRef: '',
      assValueDataRefIds: [],
      dlgVisible: false,
      currentDataType: '',
      currentMetaId: '',
      assValueReadOnly: false,
      activeName: 'agenAuthTab',
      assValuePlaceHolder: '请输入',
      queryForm: {
        ruleName: ''
      },
      ruleForm: {
        id: '',
        bizid: '',
        ruleName: '',
        ruleNames: '',
        formId: '',
        versionId: '',
        formTabId: '',
        ruleState: '启用',
        stateItem: '',
        ruleType: '',
        setAuth: '',
        validMode: '',
        achTypeId: '',
        lockVersion: '',
        achTypeCode: '',
        achTypeName: '',
        ruleDesc: ''
      },
      metaForm: {
        id: '',
        tempId: '',
        bizid: '',
        ruleId: '',
        tabId: '',
        formId: '',
        stateItem: '',
        versionId: '',
        cformMetaId: '',
        templateId: '',
        cformMetaName: '',
        cformMetaField: '',
        metaOrigin: '',
        hideEnable: '否',
        editEnable: '是',
        mustInput: '否',
        gridShow: '是',
        searchEnable: '否',
        associateSymbol: '',
        associateSymbolName: '',
        associateValue: '',
        cformMetaEvent: ''
      },
      // 关联操作符
      cformRuleAssociates: [],
      // 规则记录
      cformRuleList: [],
      // 表单字段
      cformRuleColumns: [],
      // 状态项
      cformRuleStates: [],
      // 附件类型
      cformRuleAchTypes: [],
      // 规则明细
      cformRuleMetaList: [],
      // 单位权限
      cformRuleAgenAuths: [],
      // 角色权限
      cformRuleRoleAuths: [],
      // 用户权限
      cformRuleUserAuths: [],
      cformRuleMetaSelected: [],
      attachRuleMetaSelected: []
    }
  },
  mounted() {

  },
  computed: {
    handleCformRuleColumns() {
      return this.cformRuleColumns.filter(col => col.realCol)
    }
  },
  methods: {
    handleEditEnableClick(value) {
      if (value === '是') {
        this.metaForm.hideEnable = '否'
      } else {
        this.metaForm.mustInput = '否'
      }
    },
    handleHideEnableClick(value) {
      if (value === '是') {
        this.metaForm.editEnable = '否'
        this.metaForm.mustInput = '否'
        this.metaForm.searchEnable = '否'
        this.metaForm.gridShow = '否'
      } else {
        this.metaForm.editEnable = '是'
        this.metaForm.mustInput = '否'
        this.metaForm.searchEnable = '否'
        this.metaForm.gridShow = '是'
      }
    },
    handleCformRuleModify() {
      var isFormRule = this.isFormRule
      if (this.ruleForm.ruleName === '') {
        this.$message({ message: '规则名称不能为空', type: 'warning' })
        return
      }
      if (this.ruleForm.ruleState === '') {
        this.$message({ message: '规则状态不能为空', type: 'warning' })
        return
      }
      if (!isFormRule) {
        if (this.ruleForm.achTypeId === '') {
          this.$message({ message: '附件类型不能为空', type: 'warning' })
          return
        }
      }
      if (this.cformRuleMetaList.length === 0) {
        this.$message({ message: '条件信息不能为空', type: 'warning' })
        return
      }

      var ruleType = ''
      if (isFormRule) {
        ruleType = '表单要素'
      } else {
        ruleType = '附件上传'
      }
      this.ruleForm.ruleType = ruleType
      this.ruleForm.formTabId = this.tabId
      this.ruleForm.versionId = this.versionId
      this.ruleForm.stateItem = this.metaForm.stateItem

      if (!isFormRule) {
        var achTypeCode = ''
        var achTypeName = ''
        var achTypeId = this.ruleForm.achTypeId
        this.cformRuleAchTypes.forEach(function(item, index) {
          const eleId = item.eleId
          if (eleId === achTypeId) {
            achTypeCode = item.eleCode
            achTypeName = item.eleName
          }
        })
        this.ruleForm.achTypeCode = achTypeCode
        this.ruleForm.achTypeName = achTypeName
      } else {
        this.ruleForm.achTypeCode = ''
        this.ruleForm.achTypeName = ''
      }
      var parameters = {
        tabId: this.tabId,
        formId: this.formId,
        achTypeId: this.achTypeId,
        cformRule: this.ruleForm,
        ruleMetas: this.cformRuleMetaList,
        agenAuths: this.cformRuleAgenAuths,
        roleAuths: this.cformRuleRoleAuths,
        userAuths: this.cformRuleUserAuths
      }
      this.$callApi('saveCformRule',
        parameters, result => {
          var data = result.data
          var formRule = data.cformRule
          this.ruleForm = data.cformRule
          var isModify = data.modify
          var finalCformRuleList = []
          if (!isModify) {
            finalCformRuleList.push(formRule)
            this.cformRuleList.forEach(function(item, index) {
              finalCformRuleList.push(item)
            })
            this.cformRuleList = finalCformRuleList
          } else {
            this.cformRuleList.forEach(function(item, index) {
              var bizid = item.bizid
              if (bizid === formRule.bizid) {
                finalCformRuleList.push(formRule)
              } else {
                finalCformRuleList.push(item)
              }
            })
            this.cformRuleList = finalCformRuleList
          }
          this.$refs.cformRuleTable.setCurrentRow(formRule)
          this.handleCurrentChange(formRule)
          if (isModify) {
            this.$message({ message: '修改成功', type: 'success' })
          } else {
            this.$message({ message: '保存成功', type: 'success' })
          }
          return true
        })
    },
    handleCformMetaNameChange(value) {
      var dataType = ''
      var dataRef = ''
      var currentMetaId = ''
      var associates = []
      this.cformRuleColumns.forEach(function(item, index) {
        const bizId = item.bizId
        if (bizId === value) {
          dataType = item.dataType
          dataRef = item.dataRef
          currentMetaId = item.bizId
        }
      })
      if (dataType === '') {
        this.$message({
          message: '无法获取当前选择要素的数据类型', type: 'warning'
        })
        return
      }
      this.currentDataType = dataType
      this.currentMetaId = currentMetaId
      this.metaForm.associateValue = ''
      this.metaForm.associateSymbol = ''
      this.metaForm.tempId = currentMetaId
      this.assValueDataRefIds = []
      this.cformRuleAssociates = []
      if (dataType.startsWith('文本')) {
        associates.push({ 'symbol': 'in', 'name': '包含' })
        this.cformRuleAssociates = associates
        this.assValueReadOnly = false
        this.assValuePlaceHolder = '请输入'
        this.metaForm.associateSymbol = 'in'
      } else if (dataType.startsWith('下拉框') ||
          dataType.startsWith('多选') ||
          dataType.startsWith('单选') ||
          dataType.startsWith('弹框')) {
        associates.push({ 'symbol': 'in', 'name': '包含' })
        this.cformRuleAssociates = associates
        this.assValueReadOnly = true
        this.assValuePlaceHolder = '请选择'
        this.assValueDataRef = dataRef
        this.metaForm.associateSymbol = 'in'
      } else if (dataType.startsWith('数值') ||
          dataType.startsWith('整数') ||
          dataType.startsWith('金额') ||
          dataType.startsWith('日期')) {
        associates.push(
          { 'symbol': '=', 'name': '等于' },
          { 'symbol': '>', 'name': '大于' },
          { 'symbol': '>=', 'name': '大于等于' },
          { 'symbol': '<', 'name': '小于' },
          { 'symbol': '<=', 'name': '小于等于' })
        this.cformRuleAssociates = associates
        this.assValueReadOnly = false
        this.assValuePlaceHolder = '请输入'
        this.metaForm.associateSymbol = '='
      }
    },
    showDataRefDialog() {
      if (this.assValueReadOnly && this.assValueDataRef !== '') {
        var dataRef = this.assValueDataRef
        var dataType = this.currentDataType
        var assValueDataRefIds = this.assValueDataRefIds
        this.$refs.cformDataRefDialog
          .handleFormDialogOpen(dataRef, dataType, assValueDataRefIds)
      }
    },
    handleRestDataRefValue(value, assValueDataRefIds) {
      this.metaForm.associateValue = value
      this.assValueDataRefIds = assValueDataRefIds
    },
    showFormRuleDialog(formId, tabName, tabId, versionId) {
      this.formId = formId
      this.tabName = tabName
      this.tabId = tabId
      this.versionId = versionId
      this.handleMainPageLoad()
    },
    handleFormRuleQuery() {
      var parameters = {
        tabId: this.tabId,
        versionId: this.versionId,
        ruleName: this.queryForm.ruleName
      }
      this.$callApiParams('queryCformRule',
        parameters, result => {
          this.handleFormDialogClean()
          this.cformRuleList = result.data
          return true
        })
    },
    // tab点击事件
    handleFormTabClick(tab) {
      this.clickTabName = tab.name
    },
    handleGenerateId() {
      const date = new Date()
      return 'tmp' + date.getTime() + Math.random().toString(16).substring(2)
    },
    handleFormRuleMetaAppend() {
      var formMeta = this.metaForm
      var stateItem = formMeta.stateItem
      var formMetaName = formMeta.cformMetaName
      var associateSymbol = formMeta.associateSymbol
      var associateValue = formMeta.associateValue
      var isFormRule = this.isFormRule
      if (stateItem === '') {
        this.$message({ message: '状态项不能为空', type: 'warning' })
        return
      }
      if (formMetaName === '') {
        this.$message({ message: '要素项不能为空', type: 'warning' })
        return
      }
      if (!isFormRule) {
        if (associateSymbol === '') {
          this.$message({ message: '关联操作符不能为空', type: 'warning' })
          return
        }
        if (associateValue === '') {
          this.$message({ message: '关联要素值不能为空', type: 'warning' })
          return
        }
      } else {
        associateSymbol = ''
      }
      var ruleColumnVo = {}
      var ruleColumnId = this.currentMetaId
      this.cformRuleColumns.forEach(function(item, index) {
        const bizId = item.bizId
        if (bizId === ruleColumnId) {
          ruleColumnVo = item
        }
      })
      var id = this.handleGenerateId()
      var tabId = ruleColumnVo.tabId
      var formId = ruleColumnVo.formId
      var versionId = ruleColumnVo.versionId
      var rowEditId = ruleColumnVo.rowEditId
      var templateId = ruleColumnVo.templateId
      var cformMetaName = ruleColumnVo.prop
      var metaOrigin = ruleColumnVo.colOrigin
      var cformMetaId = ruleColumnVo.formColId
      var lockVersion = ruleColumnVo.lockVersion
      var associateSymbolName = ''
      var cformMetaEvent = ''
      var _formId = ''
      if (metaOrigin === '自定义表单') {
        _formId = formId
      } else if (metaOrigin === '列表编辑') {
        _formId = rowEditId
      }
      if (isFormRule) {
        var hideEnable = formMeta.hideEnable
        var editEnable = formMeta.editEnable
        var mustInput = formMeta.mustInput
        var gridShow = formMeta.gridShow
        var searchEnable = formMeta.searchEnable
        if (hideEnable === '是') {
          cformMetaEvent = '隐藏'
        } else {
          cformMetaEvent = '不隐藏'
        }
        if (editEnable === '是') {
          cformMetaEvent = cformMetaEvent + ' ' + '可编辑'
        } else {
          cformMetaEvent = cformMetaEvent + ' ' + '不可编辑'
        }
        if (mustInput === '是') {
          cformMetaEvent = cformMetaEvent + ' ' + '必填'
        } else {
          cformMetaEvent = cformMetaEvent + ' ' + '非必填'
        }
        if (gridShow === '是') {
          cformMetaEvent = cformMetaEvent + ' ' + '列表展示'
        } else {
          cformMetaEvent = cformMetaEvent + ' ' + '列表不展示'
        }
        if (searchEnable === '是') {
          cformMetaEvent = cformMetaEvent + ' ' + '可搜索'
        } else {
          cformMetaEvent = cformMetaEvent + ' ' + '不可搜索'
        }
      }

      if (!isFormRule) {
        if (associateSymbol === '=') {
          associateSymbolName = '等于'
        } else if (associateSymbol === '>') {
          associateSymbolName = '大于'
        } else if (associateSymbol === '>=') {
          associateSymbolName = '大于等于'
        } else if (associateSymbol === '<') {
          associateSymbolName = '小于'
        } else if (associateSymbol === '<=') {
          associateSymbolName = '小于等于'
        } else if (associateSymbol === 'in') {
          associateSymbolName = '包含'
        } else {
          associateSymbolName = ''
        }
      }
      var formMetaColumn = {
        id: id,
        bizid: id,
        tabId: tabId,
        formId: _formId,
        versionId: versionId,
        cformMetaId: cformMetaId,
        cformMetaEvent: cformMetaEvent,
        stateItem: stateItem,
        templateId: templateId,
        cformMetaName: cformMetaName,
        metaOrigin: metaOrigin,
        hideEnable: formMeta.hideEnable,
        editEnable: formMeta.editEnable,
        mustInput: formMeta.mustInput,
        gridShow: formMeta.gridShow,
        lockVersion: lockVersion,
        searchEnable: formMeta.searchEnable,
        associateSymbol: associateSymbol,
        associateSymbolName: associateSymbolName,
        associateValue: associateValue
      }
      this.cformRuleMetaList.push(formMetaColumn)
    },
    handleFormRuleMetaDelete() {
      var selectRows = []
      if (this.isFormRule) {
        selectRows = this.cformRuleMetaSelected
      } else {
        selectRows = this.attachRuleMetaSelected
      }
      if (!selectRows || selectRows.length === 0) {
        this.$message({ message: '请选择待删除的记录', type: 'warning' })
        return
      }
      var cformRuleMetaLists = this.cformRuleMetaList
      if (selectRows.length === cformRuleMetaLists.length) {
        this.cformRuleMetaList = []
      } else {
        var leftRuleMetaList = []
        const deleteMap = {}
        selectRows.forEach(function(item, index) {
          var id = item.id
          deleteMap[id] = item
        })
        cformRuleMetaLists.forEach(function(citem, index) {
          var _id = citem.id
          if (!deleteMap[_id]) {
            leftRuleMetaList.push(citem)
          }
        })
        this.cformRuleMetaList = []
        this.cformRuleMetaList = leftRuleMetaList
      }
    },

    handelCformRuleMetaOnClick(rows) {
      this.$refs.cformRuleMetaTable.clearSelection()
      this.$refs.cformRuleMetaTable.toggleRowSelection(rows)
    },
    handelAttachRuleMetaOnClick(rows) {
      this.$refs.attachRuleMetaTable.clearSelection()
      this.$refs.attachRuleMetaTable.toggleRowSelection(rows)
    },

    handelCformRuleMetaSelected(rows) {
      this.cformRuleMetaSelected = rows
    },
    handelAttachRuleMetaSelected(rows) {
      this.attachRuleMetaSelected = rows
    },
    handleResetRuleAuth(activeName, auths, checkIds) {
      if (activeName === 'agenAuthTab') {
        this.cformRuleAgenAuths = auths
      } else if (activeName === 'roleAuthTab') {
        this.cformRuleRoleAuths = auths
      } else if (activeName === 'userAuthTab') {
        this.cformRuleUserAuths = auths
      }
      this.assValueDataRefIds = checkIds
    },
    // 获取权限数据项
    handleLoadRuleAuth() {
      var assValueDataRefIds = this.assValueDataRefIds
      var activeName = this.activeName
      var auths = []
      if (activeName === 'agenAuthTab') {
        auths = this.cformRuleAgenAuths
      } else if (activeName === 'roleAuthTab') {
        auths = this.cformRuleRoleAuths
      } else if (activeName === 'userAuthTab') {
        auths = this.cformRuleUserAuths
      }
      auths.forEach(function(item, index) {
        const id = item.id
        assValueDataRefIds.push(id)
      })
      this.$refs.cformAuthDialog
        .handleFormDialogOpen(
          this.formId, this.curRuleId,
          this.activeName, assValueDataRefIds)
    },
    handleFormRuleCreate() {
      this.handleFormDialogClean()
    },
    // 删除规则记录
    handleFormRuleDelete() {
      this.$confirm('确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var parameters = {
          ids: this.curRuleId,
          formId: this.formId,
          tabId: this.tabId,
          versionId: this.versionId
        }
        this.$callApiParams('deleteCformRule',
          parameters, result => {
            var msg = result.msg
            this.handleFormDialogClean()
            this.cformRuleList = result.data
            this.$message({ message: msg, type: 'success' })
            return true
          })
      }).catch(() => {

      })
    },
    // 获取主页数据
    handleMainPageLoad() {
      this.$callApiParams('getMainPage',
        {
          tabId: this.tabId,
          formId: this.formId,
          versionId: this.versionId
        },
        result => {
          const retData = result.data
          this.isFormRule = retData.formRule
          this.cformRuleList = retData.cformRules
          this.cformRuleColumns = retData.ruleColumns
          this.cformRuleAchTypes = retData.attachTypes
          this.cformRuleStates = retData.stateItems
          this.dlgVisible = true
          return true
        })
    },
    // 单击事件处理
    handleCurrentChange(row) {
      const ruleId = row.bizid
      this.curRuleId = ruleId
      this.assValueDataRefIds = []
      this.$callApiParams('getCformRule',
        { ruleId: ruleId, formId: this.formId },
        result => {
          const retData = result.data
          this.ruleForm = retData.cformRule
          this.cformRuleMetaList = retData.ruleMetas
          this.cformRuleAgenAuths = retData.agenAuths
          this.cformRuleRoleAuths = retData.roleAuths
          this.cformRuleUserAuths = retData.userAuths
          return true
        })
    },
    handleFormDialogClean() {
      this.ruleForm.lockVersion = ''
      this.ruleForm.id = ''
      this.ruleForm.bizid = ''
      this.assValueReadOnly = false
      this.$refs.ruleForm.resetFields()
      this.$refs.metaForm.resetFields()
      this.cformRuleMetaList = []
      this.cformRuleAgenAuths = []
      this.cformRuleRoleAuths = []
      this.cformRuleUserAuths = []
      this.assValueDataRefIds = []
    },
    handleFormDialogClose() {
      this.ruleForm.lockVersion = ''
      this.ruleForm.id = ''
      this.ruleForm.bizid = ''
      this.assValueReadOnly = false
      this.cformRuleMetaList = []
      this.cformRuleAgenAuths = []
      this.cformRuleRoleAuths = []
      this.cformRuleUserAuths = []
      this.assValueDataRefIds = []
      this.$refs.queryForm.resetFields()
      this.$refs.ruleForm.resetFields()
      this.$refs.metaForm.resetFields()
      this.dlgVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
  .row-marginBottom {
    margin-bottom: 10px;
  }

  .cus-form {
    border: 0px;
  }

  .el-dialog-div {
    height: 60vh;
    overflow-x: hidden;
  }

  .header-icon {
    display: flex;
    align-items: center;
    height: 25px;

    i {
      width: 3px;
      height: 100%;
      display: inline-block;
      background: #409EFF;
      border-radius: 6px;
      margin-right: 10px;
    }
  }
</style>
