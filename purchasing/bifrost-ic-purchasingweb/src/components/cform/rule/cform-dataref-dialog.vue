<template>
  <div id="cformDataRefDialog">
    <el-dialog
      append-to-body
      :title="`${operation}`"
      :visible.sync="dlgVisible"
      width="40%"
      destroy-on-close
      :close-on-click-modal='false'
      @close="handleFormDialogClose">
      <page>
        <template #pageContent>
          <div :style="{ height : '550px'}">
            <sup-tree :setting="setting"
                      ref="supTree"
                      :checkedValues="treeCheckedValues"
                      :nodes="treeData"
                      :is-popover="false"
                      :edit-enable="true"></sup-tree>
          </div>
        </template>
      </page>
      <template #footer>
        <el-button type="primary" @click="handleFormTreeSelect">确定</el-button>
        <el-button @click="handleFormDialogClose">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'cform-dataref-dialog',
  props: {},
  watch: {},
  data() {
    return {
      operation: '选择数据',
      dlgVisible: false,
      treeData: [],
      setting: {
        check: { enable: true },
        data: {
          simpleData: {
            enable: true, idKey: 'id', pIdKey: 'parentId'
          },
          key: { name: 'name' }
        }
      },
      treeCheckedValues: []
    }
  },
  mounted() {

  },
  methods: {
    handleGenerateId() {
      const date = new Date()
      return date.getTime() + Math.random().toString(16).substring(2)
    },
    handleFormDialogOpen(dataRef, dataType, ids) {
      this.treeCheckedValues = ids
      if (dataType === '下拉框') {
        const parameters = { 'dataRef': dataRef }
        this.$callApiParams('refLabelValuesDynamic',
          parameters, result => {
            var retData = result.data
            retData.forEach(re => {
              // var id = this.handleGenerateId()
              this.treeData.push({
                id: re.value, code: '', parentId: '', name: re.value
              })
            })
            return true
          })
      } else if (dataType === '多选') {
        const parameters = { 'dataRef': dataRef }
        this.$callApiParams('refLabelValuesDynamic',
          parameters, result => {
            var retData = result.data
            retData.forEach(re => {
              // var id = this.handleGenerateId()
              this.treeData.push({
                id: re.value, code: '', parentId: '', name: re.value
              })
            })
            return true
          })
      } else if (dataType === '弹框') {
        const splits = dataRef.split('#')
        this.$callApiParams(splits[0],
          { exParams: splits[1] }, result => {
            var retData = result.data
            retData.forEach(re => {
              this.treeData.push({
                id: re.id, code: re.itemKey,
                parentId: re.parentId, name: re.label
              })
            })
            return true
          })
      } else if (dataType === '单选') {
        const splits = dataRef.split(',')
        splits.forEach(re => {
          // var id = this.handleGenerateId()
          this.treeData.push({
            id: re, code: '', parentId: '', name: re
          })
        })
      }
      this.dlgVisible = true
    },
    handleFormTreeSelect() {
      var nodes = this.$refs.supTree.getCheckedNodes()
      var dataValue = ''
      var assValueDataRefIds = []
      nodes.forEach(function(item, index) {
        const id = item.id
        const code = item.code
        const name = item.name
        var value = ''
        if (code != null && code !== '') {
          value = code + ' ' + name
        } else {
          value = name
        }
        if (dataValue === '') {
          dataValue = value
        } else {
          dataValue = dataValue + ',' + value
        }
        assValueDataRefIds.push(id)
      })
      this.treeData = []
      this.$parent.handleRestDataRefValue(dataValue, assValueDataRefIds)
      this.dlgVisible = false
    },
    handleFormDialogClose() {
      this.treeData = []
      this.dlgVisible = false
    }
  }
}
</script>
<style lang="scss">
  .row-marginBottom {
    margin-bottom: 10px;
  }
</style>
