<template>
  <div>
    <el-dialog ref="formTypeSettingDlg"
               title='表单类别管理'
               append-to-body
               class="formTypeSettingDlg"
               @closed="formTypeSettingDlgClosed"
               :visible.sync="formTypeSettingDlgVisible">
      <page>
        <template #pageContent>
          <b-curd ref="formTypeList"/>
        </template>
      </page>
    </el-dialog>

    <el-dialog
      append-to-body
      width="640px"
      :title='`${btEditType}类别`'
      :close-on-click-modal="false"
      :visible.sync="formTypeEditDlgVisible">
      <page>
        <template #pageContent>
          <el-form label-width="80px" size="mini" :model="cformType">
            <el-form-item label="类别名称" :required="true">
              <el-input v-model="cformType.name"/>
            </el-form-item>
            <el-form-item label="数据对象">
              <el-select v-model="cformType.cformClass">
                <el-option
                  v-for="(item, index) in cformClasses"
                  :key="index" :label="item" :value="item"/>
              </el-select>
            </el-form-item>
            <el-form-item label="序号">
              <el-input v-model="cformType.typeOrder" @input="numericTypes" maxlength="9"/>
            </el-form-item>
            <el-form-item label="是否唯一">
              <div style="display: flex;">
                <div style="flex: 2;width: 65px;display: flex;">
                  <el-radio-group v-model="cformType.isUnique">
                    <el-radio label='是'>是</el-radio>
                    <el-radio label='否'>否</el-radio>
                  </el-radio-group>
                </div>
                <span style="flex: 1;padding-right: 10px;text-align: right;">需要流程</span>
                <el-radio-group v-model="cformType.needProcess" style="flex: 1;">
                  <el-radio label='是'>是</el-radio>
                  <el-radio label='否'>否</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item label="启用缺票">
              <div style="display: flex;">
                <div style="flex: 2;width: 65px;display: flex;">
                  <el-radio-group v-model="cformType.isBillMissing">
                    <el-radio label='是'>是</el-radio>
                    <el-radio label='否'>否</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="必含要素">
              <el-input type="textarea" :rows="5" resize="none"
                        v-model="cformType.requiredCols"/>
            </el-form-item>
            <el-form-item label="固定列要素">
              <el-input
                resize="none"
                type="textarea"
                :rows="5"
                v-model="cformType.posColNames"
                placeholder="请输入内容"/>
            </el-form-item>
          </el-form>
          <div slot="footer" style="text-align: right; padding-bottom: 20px;">
            <el-button @click="formTypeEditDlgVisible = false">取消</el-button>
            <el-button type="primary" v-lockButton @click="saveCformType">{{btEditType}}</el-button>
          </div>
        </template>
      </page>
    </el-dialog>
  </div>
</template>

<script>
import BCurd from '../page/base-curd'

export default {
  name: 'form-type-setting',
  components: { BCurd },
  data() {
    return {
      cformClasses: [],
      formTypeSettingDlgVisible: false,
      formTypeEditDlgVisible: false,
      btEditType: '新增',
      cformType: this.newCformType()
    }
  },
  methods: {
    init() {
      this.$refs.formTypeList.init({
        showPager: false,
        hideCurdButton: ['详情'],
        params: {
          dataApiKey: 'selectFormTypeDataList',
          deleteApiKey: 'deleteCformType'
        },
        btAddClick: { click: () => this.showEdit(this.newCformType()) },
        btModifyClick: { click: row => this.showEdit(row) }
      })
      this.$callApiParams('selectCformClasses', {},
        result => {
          this.cformClasses = result.data
          return true
        })
    },
    show() {
      this.formTypeSettingDlgVisible = true
      this.$setDlgSize(this, 'formTypeSettingDlg', 1600, 900)
      this.$nextTick(() => {
        this.init()
        this.$refs.formTypeList.setButtonNormalNoPaddingTop(true)
      })
    },
    newCformType() {
      return {
        id: '',
        name: '',
        cformClass: 'Cform',
        typeOrder: 0,
        isUnique: '否',
        needProcess: '是',
        isSystem: '否',
        requiredCols: '业务编码,业务名称,部门:弹框:基础数据#部门,部门名称',
        posColNames: '',
        isBillMissing: '否'
      }
    },
    showEdit(typeData) {
      this.formTypeEditDlgVisible = true
      this.cformType = typeData
      this.btEditType = this.$isEmpty(typeData.id) ? '新增' : '修改'
    },
    saveCformType() {
      this.$callApi('saveCformType', this.cformType,
        result => {
          this.init()
          this.formTypeEditDlgVisible = false
        })
    },
    formTypeSettingDlgClosed() {
      this.$emit('formTypeSettingDlgClosed')
    },
    // 处理input只能输入数字
    numericTypes(val) {
      this.cformType.typeOrder = val.replace(/[^\d]/g, '')
    }
  }
}
</script>

<style lang="scss">
  .formTypeSettingDlg .el-dialog__body {
    padding-top: 0px;
    padding-right: 20px;
  }

  .el-tag + .el-tag {
    margin-left: 10px;
  }

  .button-new-tag {
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }

  .input-new-tag {
    width: 100%;
    vertical-align: bottom;
  }
</style>
