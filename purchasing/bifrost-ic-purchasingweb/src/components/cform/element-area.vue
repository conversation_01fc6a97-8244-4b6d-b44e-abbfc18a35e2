<template>
  <div class='elementArea'>
    <div class='areaItem' v-for='comp in comps' :key='comp.id'>
      <p class='name'>{{ comp.name }}</p>
      <div class='compItemContainer'>
        <div
          class='compItem'
          :class='{selected: selectedClassName(c.bizid, blockActiveTabIndex)}'
          v-for='(c, index) in comp.data' :key='index'
          @click='handleCompItemClick(c)'>
          {{ c.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'element-area',
  props: {
    isBlockForm: { type: Boolean, default: false }
  },
  inject: {
    outFormCanvasIns: { default: undefined },
    setSelectedComp: { default: undefined },
    getBlockActiveTabIndex: { default: undefined }
  },
  data() {
    return {
      comps: [
        {
          id: 1,
          name: '基础组件',
          data: []
        }
        // {
        //   id: 2,
        //   name: '高级组件',
        //   data: []
        // }
      ],
      fileData: {
        id: '-1',
        bizid: '-1',
        columns: [],
        columnsAll: [],
        createDateStr: '',
        createTimeStr: '',
        dataType: '附件',
        deleteTimeStr: '',
        handlerKey: '附件',
        isCform: false,
        name: '附件信息',
        isDelete: 2,
        pageData: {
          columns: [],
          page: {
            current: 1,
            hitCount: false,
            orders: [],
            pages: 0,
            records: [],
            searchCount: true,
            size: 10,
            total: 0
          },
          rows: []
        },
        targetClassName: '',
        updateTimeStr: ''
      },
      invoiceData: {
        id: '-2',
        bizid: '-2',
        columns: [],
        columnsAll: [],
        createDateStr: '',
        createTimeStr: '',
        dataType: '电子发票',
        deleteTimeStr: '',
        handlerKey: '电子发票',
        isCform: false,
        name: '发票信息',
        isDelete: 2,
        pageData: {
          columns: [],
          page: {
            current: 1,
            hitCount: false,
            orders: [],
            pages: 0,
            records: [],
            searchCount: true,
            size: 10,
            total: 0
          },
          rows: []
        },
        targetClassName: '',
        updateTimeStr: ''
      },
      selectedComp: {}
    }
  },
  computed: {
    blockActiveTabIndex() {
      return this.getBlockActiveTabIndex?.() || 0
    }
  },
  methods: {
    selectedClassName(bizid, key) {
      return this.selectedComp[key]?.includes(bizid) || false
    },
    getRFormComp() {
      this.$callApiParams('selectModuleList', {}, (result) => {
        if (this.$isNotEmpty(result.data)) {
          result.data.forEach(item => {
            item.columns.reverse()
            item.pageData.columns.reverse()
          })
          this.comps[0].data = result.data
          this.comps[0].data.push(this.fileData)
          this.comps[0].data.push(this.invoiceData)
        }
        return true
      })
    },
    handleCompItemClick(comp) {
      this.setSelectedComp(comp.bizid)
      comp.isCform = false
      comp.cformModuleId = comp.bizid
      comp.cformModuleName = comp.name
      this.$emit('compItemClick', comp)
    },
    /**
     * 处理组件联动选中
     * @param {string} type 处理数据的方式 clear clearAll push splice equal
     * @param {array | object | number} data 需要处理的数据
     * @param {number} index 第几个tab 默认第0个
     */
    handleSelectedComp(type, data, index = 0) {
      const initSelectedComp = (index) => {
        if (!Array.isArray(this.selectedComp[index])) {
          // this.selectedComp[index] = []
          this.$set(this.selectedComp, index, [])
        }
      }
      switch (type) {
        case 'create':
          initSelectedComp(index)
          this.selectedComp[index].push(data)
          break
        case 'clear':
          this.$set(this.selectedComp, index, [])
          break
        case 'clearAll':
          this.selectedComp = {}
          break
        case 'push':
          this.selectedComp[index].push(data)
          break
        case 'splice':
          this.selectedComp[index].splice(data, 1)
          break
      }
    }
  }
}
</script>
<style lang='scss' scoped>
.elementArea {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-right: 10px;
  .areaItem {
    flex: 1;
    width: 100%;
    padding: 0 10px 20px;
    &:not(:first-child) {
      padding: 14px 10px 20px;
    }
    &:not(:last-child) {
      border-bottom: 1px solid #DDDDDD;
    }
    .name {
      font-weight: 700;
    }
    .compItemContainer {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      .compItem {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 5px;
        background: #f0f5ff;
        text-align: center;
        &:hover {
          outline: 1px solid #6eb7fb;
          cursor: pointer;
        }
        &.selected {
          outline: 1px solid #6eb7fb;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
