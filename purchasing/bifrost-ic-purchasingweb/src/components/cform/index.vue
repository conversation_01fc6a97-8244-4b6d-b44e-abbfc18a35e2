<template>
  <div>
    <versionRelate
            ref="versionRelate"
            bizName="表单"
            dataType='CformMetaEntity'
            :wrapSwitch="wrapSwitch"
            @btNewGuide="btNewGuide"
            @beforeOpenCanvas="beforeOpenCanvas"
            @openNew="openNew"
            @afterMetaQuery="afterMetaQuery"
            @getMetaToSave="getMetaToSave"
            @afterMetaSaved="afterMetaSaved"
            @nodeDeleted="nodeDeleted"
            @canvasOpenedChange="canvasOpenedChange"
            @changeStyle="changeStyle"
            @nodeClick="nodeClick"
            @setBlockFormId="setBlockFormId">
      <template #btSave>
        <el-button plain icon="el-icon-aliiconfuzhi" size="small"
          @click="btSave" :loading="isSaving" :disabled="!canvasOpened || allButtonDisabled || saveDisabled"> 保存</el-button>
        <el-button plain icon="el-icon-copy-document" size="small" @click="btCopy"
          :disabled="!canvasOpened || allButtonDisabled || isNewAdd"> 复制</el-button>
        <el-button plain icon="el-icon-copy-document" size="small" @click="btCopyToOtherBookSet"
                   :disabled="!canvasOpened || allButtonDisabled || isNewAdd || isBlockForm"> 复制到其他账套</el-button>
        <span class="toolbar-button">
          <el-dropdown @command="preview" :disabled="!canvasOpened || allButtonDisabled || isNewAdd">
            <el-button icon="el-icon-aliiconbukejian" plain size="small" :disabled="!canvasOpened || allButtonDisabled || isNewAdd">
              预览<i class="el-icon-arrow-down el-icon--right"/>
            </el-button>
            <el-dropdown-menu slot="dropdown" :disabled="!canvasOpened">
                <el-dropdown-item command="EDIT">预览制单</el-dropdown-item>
                <el-dropdown-item command="DETAIL">预览详情</el-dropdown-item>
                <el-dropdown-item command="PRINTF" v-show="canvasOpened && isFreedomForm && !isNew">打印预览</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
      </template>
      <template #moreButtons>
        <el-button plain icon="el-icon-connection" size="small"
          :title="bindsWf.length?`已绑定 ${bindsWf.length} 个流程`:'未绑定任何流程'" :disabled="!canvasOpened || allButtonDisabled" @click="bindWf"> 流程绑定({{bindsWf.length}})</el-button>
        <span class="toolbar-button">
          <el-dropdown @command="dropdownImportClick">
            <el-button icon="el-icon-aliiconshangchuan" plain size="small" class="mleft-6">
              导入<i class="el-icon-arrow-down el-icon--right"/>
            </el-button>
            <el-dropdown-menu slot="dropdown">
                <el-upload ref="fileDomExcel" class="upload-import" action
                            v-show="false"
                            accept=".xlsx"
                          :auto-upload='false' :show-file-list="false" :limit="1"
                          :on-change="importSheet">
                </el-upload>
                  <el-dropdown-item :disabled="!canvasOpened || allButtonDisabled ||!isFreedomForm"
                                    command="fileDomExcel">导入 Excel</el-dropdown-item>
                  <el-dropdown-item command="fileDomJson" >导入 Dat</el-dropdown-item>
              <el-upload v-show="false" ref="fileDomJson" :on-change="importJson" class="upload-import"
                         action :auto-upload='false' :show-file-list="false" :limit="1">
                </el-upload>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
        <span class="toolbar-button">
          <el-dropdown @command="exportSheet">
            <el-button icon="el-icon-aliiconxiazai" plain size="small">
              导出<i class="el-icon-arrow-down el-icon--right"/>
            </el-button>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="EXCEL" :disabled="!isFreedomForm">导出 Excel</el-dropdown-item>
                <el-dropdown-item command="JSON" :disabled="isNewAdd">导出 Dat</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
        <el-button class="mleft-6" plain @click="handlePrint" icon="el-icon-printer" size="small" v-show="isFreedomForm"
          :disabled="!canvasOpened || isNew || allButtonDisabled"> 打印</el-button>
        <!-- <el-button icon="el-icon-coin" size="small"
                   :type="colItemsSettingButtonType"
                   :title="`已绑定 ${colSettingCount} 个要素`"
                   :disabled="!canvasOpened || allButtonDisabled"
                   @click="switchColItemsSettingBar"> 要素绑定</el-button> -->
        <el-button
          v-if="showWrapButton"
          id="showWrap"
          :class="showWrapClass"
          @click="toggle"
        ></el-button>
        <div class="form-top-wrap" ref="formTopWrap">
          <el-dropdown
            :disabled="!canvasOpened || allButtonDisabled || !isNew"
            @command="switchFormFormat">
            <el-button plain size="small" :disabled="!canvasOpened || allButtonDisabled || !isNew">
              格式<i class="el-icon-arrow-down el-icon--right"/>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :disabled="!canvasOpened || freedomDisabled || allButtonDisabled" :command="true">
                自由格式
              </el-dropdown-item>
              <el-dropdown-item :disabled="!canvasOpened || regularDisabled || allButtonDisabled" :command="false">
                规则格式
              </el-dropdown-item>
              <el-dropdown-item :disabled="!canvasOpened || blockDisabled || allButtonDisabled" command="block">
                区块格式
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button plain icon="el-icon-setting" size="small" title="更多设置" @click="showMoreSetting"
                     :disabled="!canvasOpened || !isCformTypeDataReady || isNew || allButtonDisabled"> 更多设置</el-button>
          <el-button plain icon="el-icon-setting" size="small" title="签章设置" @click="showSignatureSettings"
                     :disabled="!canvasOpened || freedomDisabled || allButtonDisabled"> 签章设置</el-button>
          <el-button plain icon="el-icon-connection" size="small"
                     :disabled="!canvasOpened || freedomDisabled || allButtonDisabled" @click="bindTemplate"> 非表单打印</el-button>
          <el-button
            plain
            icon="el-icon-set-up"
            size="small"
            :title="toggleText"
            @click="toggleElementArea"
            :disabled="toggleBtnDisabled">
            {{ toggleText }}
          </el-button>

          <el-input v-model="formName" size="small" clearable
                    class="cform-name" :disabled="!canvasOpened || allButtonDisabled" @change="nameChange">
            <template slot="prepend"><span style="color:#f56c6c">*</span>表单名称</template>
          </el-input>
          <el-autocomplete
                  ref="cformTypeAutoComplete"
                  size="small"
                  :disabled="!canvasOpened"
                  popper-class="cform-type"
                  :class="canvasOpened?'cform-type':'cform-type cformAutocompleteDisabled'"
                  v-model="formType"
                  :fetch-suggestions="selectFormTypeDataDrop"
                  :readonly="true">
            <template slot="prepend"><span style="color:#f56c6c">*</span>表单类别</template>
            <i class="el-icon-caret-bottom el-input__icon" slot="suffix" @click="showCformTypeDropdown"/>
            <template slot-scope="{ item }">
              <div class="name">{{ item.value }}</div>
              <span class="addr">{{ item.info }}</span>
            </template>
          </el-autocomplete>
        </div>
      </template>

      <template #mainCanvas='{nodes, filterNodes, getNodeIdFromItemKey}'>
        <div class="flex-column">
          <form-canvas ref="formCanvas"
              class="flex-1"
              :nodes="nodes"
              :filterNodes="filterNodes"
              :getNodeIdFromItemKey="getNodeIdFromItemKey"
              :hideColSetting="hideColSetting"
              :isBlockPreview="isBlockPreview"
              @colItemsChange="colItemsChange"
              @metaInit="metaInit"
              @saveMeta="btSave"
              @previewResume="previewResume"
              @changeToggleText="changeToggleText"/>
          <div style="margin-top: 5px;text-align: right;" v-show="isBlockPreview">
            <el-button icon="el-icon-d-arrow-left" @click="blockPreviewCancel">退出预览</el-button>
          </div>
        </div>
      </template>
    </versionRelate>
    <form-more-setting ref="formMoreSetting" :isFreedomForm="isFreedomForm"/>
    <form-signature-position-dlg ref="formSignaturePositionDlg" @save="signatures"/>
    <form-type-setting ref="formTypeSetting" @formTypeSettingDlgClosed="formTypeSettingDlgClosed"/>

    <el-dialog width="450px" append-to-body title="绑定流程" :visible.sync="bindDlgVisible" center>
      <div style="max-height: 400px">
        <checkboxtree
                ref="checkboxtree"
                selectApiKey="selectWfTreeToCformBind"
                nodeKey="label"
                :checkedKeys="bindWfitems"
                :propsVaule="{children: 'children',label:'label'}"/>
      </div>
      <div slot="footer" class="dialog-footer" style="margin-top: 20px">
        <el-button class="btn-normal" @click="cancelBindWf">取 消</el-button>
        <el-button class="btn-normal" type="primary" @click="confirmBindWf">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog width="1440px" append-to-body title="打印预览" :visible.sync="printfDlgVisible" @close="previewPdfClose">
      <preview-printf :direction='previewDirection' :urls="previewPdfUrls" @cancel="printfDlgVisible = false"></preview-printf>
    </el-dialog>
    <el-dialog width="600px" append-to-body title="非表单打印"
               :visible.sync="bindTemplateVisible" center
    >
      <div style="height: 400px;overflow: auto;">
        <checkboxtree
          v-if="bindTemplateVisible"
          ref="templateTree"
          selectApiKey="getTemplateBindTree"
          nodeKey="label"
          :params="{ metaName: this.formName }"
          :checkedKeys="bindTemplateData"
          :propsVaule="{children: 'children',label:'label'}"
          @getResult="checkboxTreeRes"
          @checkChange="checkChangeTemplate"/>
      </div>
      <div style="padding-top: 10px">
        <div>条件</div>
        <el-input
          rows="4"
          type="textarea"
          placeholder="请输入条件"
          v-model="expression"
          :disabled="isExpression"
          maxlength="500"
          show-word-limit>
        </el-input>
      </div>
      <div slot="footer" class="dialog-footer" style="margin-top: 20px">
        <el-button class="btn-normal" @click="cancelBindTemplate">取 消</el-button>
        <el-button class="btn-normal" type="primary" @click="confirmBindTemplate">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import $ from 'jquery'
import request from '@/utils/request'
import FormCanvas from './form-canvas'
import FormMoreSetting from './form-more-setting'
import FormTypeSetting from './form-type-setting'

export default {
  name: 'cformm', // 多加一个m两个避免与luckysheet重新渲染问题冲突
  components: { FormTypeSetting, FormMoreSetting, FormCanvas },
  provide() {
    return {
      switchColItemsSettingBar: this.switchColItemsSettingBar,
      getMeta: this.getMeta,
      formCanvas: this.$refs.formCanvas,
      toggleDisabled: () => this.toggleDisabled,
      clearBlocks: () => this.clearBlocks(),
      changeLoading: (loading) => this.changeLoading(loading),
      commonSetColItems: (meta) => this.commonSetColItems(meta),
      changeSaveDisabled: (disabled) => this.changeSaveDisabled(disabled),
      changeToggleDisabled: (disabled, activeTabIndex) => this.changeToggleDisabled(disabled, activeTabIndex),
      getFormId: (itemKey, isFree) => this.getFormId(itemKey, isFree)
    }
  },
  data() {
    return {
      mode: '',
      canvasOpened: true, // 是否已经打开设计界面
      isFreedomForm: false, // 是否是自由格式
      isBlockForm: false, // 是否是区块格式
      colSettingVisible: false, // 是否显示要素设置工具栏
      meta: '',
      colItems: '', // 表单要素
      formName: '', // 表单名称
      formType: '', // 表单类别
      isSaving: false, // 保存加载中标识
      saveDisabled: false, // 保存按钮disabled
      isNew: false, // 当前是否是新增表单
      // switchFormFormatTip: '', // 表单创建之后不能切换格式提示
      freedomDisabled: false,
      regularDisabled: false,
      blockDisabled: false,
      isCformTypeDataReady: false, // 表单类别数据是否已经从远程读取
      cformTypeData: [],
      colSettingCount: null,
      allButtonDisabled: false, // 禁用所有按钮，比如预览时
      bindDlgVisible: false,
      index: 0,
      bindTemplateVisible: false, // 非表单打印按钮控制
      bindTemplateData: [], // 回显选中
      templateDataList: [], // 保存绑定非表单打印
      expression: '',
      isExpression: true, // 非表单打印条件控制
      bindWfitems: [],
      bindsWf: [],
      isCopy: false,
      metaCopyBeforePreview: {},
      mainId: '',
      isNewAdd: false,
      pictureForm: [], // 存放图片数据,
      wrap: false,
      showWrapButton: false,
      wrapSwitch: false,
      printfDlgVisible: false,
      previewPdfUrls: [],
      previewDirection: 1,
      toggleText: '切换组件区',
      toggleDisabled: true,
      blockFormId: '', // 区块表单独有id (和versionrelate的actualKey一致)
      isMounted: false,
      // 是否隐藏右侧要素
      hideColSetting: false,
      // 设计时预览模式移动到行表单不会有提示拖动
      isBlockPreview: false
    }
  },
  activated() {
    this.$refs.versionRelate.calculate()
    const metaId = this.$sessionStorage.get('treeId')
    this.getModuleList()
    this.$nextTick(() => {
      if (!metaId) {
        return
      }
      if (this.$isNotEmpty(this.blockFormId)) {
        this.$refs.versionRelate.openCanvas(this.blockFormId, { needToReloadFreeJson: true }, true)
        const _this = this
        setTimeout(() => {
          // 重置区块col-setting数据
          _this.$refs.formCanvas?.$refs?.colSetting?.resetColSetting?.()
        }, 0)
      } else {
        this.$refs.versionRelate.openCanvas(metaId, { needToReloadFreeJson: true })
      }
    })
  },
  deactivated() {
    this.$sessionStorage.remove('treeId')
    window.luckysheet.exitEditMode()
  },
  mounted() {
    this.$sessionStorage.remove('treeId')
    this.getModuleList()
    this.updateCformTypeData()
    var this_ = this
    this.$onEvent(this_, {
      btEditBack() { // 设计模式时，返回按钮是结束预览
        this_.$nextTick(() => {
          this_.$refs.versionRelate.pageHideLeftRightResume()
          this_.$setProperty(this_, 'formEditDetail',
            'showButtons', false)
          this_.$setProperty(this, 'formFormat',
            'isPreviewEdit', false)
          this_.$setProperty(this, 'formFormat',
            'hideBtSave', false)

          $('.column-top').show()
          $('.formCanvas').parent().removeClass('formCanvasRuntime')
          $('.formCanvas').parent().removeClass('previewFormCanvas')
          $('.formCanvas').parent().removeClass('formCanvasRuntimeHasAssembly')
          $('.formCanvas').parent().removeClass('formCanvasRuntimeHasAssemblyFree')
          $('.formCanvas').closest('.main-border').css('padding-top', '10px')
          if (this_.isDesignMode()) {
            $('.formCanvas').closest('.common-page').css('padding', '10px')
          }

          // 重新加载设计数据
          // var meta = this_.$refs.formCanvas.getMetaToSave()
          var meta = this_.metaCopyBeforePreview
          if (this_.isFreedomForm) {
            meta.isResumePreview = true
            meta.needToReloadFreeJson = true
          }
          this_.initMeta('设计', meta, false)
          this_.$nextTick(() => {
            this_.setColSettingVisible(true)
            this_.setAllButtonDisabled(false)
          })
        })
      },
      onViewDetailClose() {
        this_.$nextTick(() => {
          var meta = this_.metaCopyBeforePreview
          meta.needToReloadFreeJson = true
          meta.main.isFreedomForm && this_.isDesignMode() && this_.initMeta('设计', meta, false)
        })
      }
    }

    )
    // 浏览器兼容
    this.$nextTick(() => {
      this.$browserCompatibility('cformIndex')
    })
  },
  computed: {
    colItemsSettingButtonType() {
      if (!this.canvasOpened) {
        return ''
      }
      return this.colSettingVisible ? 'primary' : ''
    },
    freeButtonType() { // 自由格式按钮的type
      if (!this.canvasOpened) {
        return ''
      }
      return this.isFreedomForm ? 'primary' : ''
    },
    regularButtonType() { // 规则格式按钮的type
      if (!this.canvasOpened) {
        return ''
      }
      return this.$isEmpty(this.freeButtonType) ? 'primary' : ''
    },
    showWrapClass() {
      return `iconfont ${this.wrapSwitch ? 'el-icon-aliiconxiangshangshuangjiantou' : 'el-icon-aliiconxiangxiashuangjiantou'}`
    },
    toggleBtnDisabled() {
      return !this.canvasOpened || this.toggleDisabled || this.allButtonDisabled
    }
  },
  methods: {
    getFormId(itemKey, isFree) {
      this.$refs.versionRelate?.blockJumpToForm?.(itemKey, isFree)
    },
    changeToggleDisabled(isForm, activeTabIndex) {
      // 第一个tab不用禁用 其他tab禁用
      this.toggleDisabled = Boolean(activeTabIndex)
      const isCompArea = this.toggleText === '切换组件区'
      // 1.第一个tab 且 不是切换组件区
      // 2.其他tab 且 (isForm为false(关联区块) 或者 是切换组件区)
      // 需要重新调用点击切换的方法恢复到要素区
      const condition = (!activeTabIndex && !isCompArea) || (activeTabIndex && (!isForm || !isCompArea))
      if (condition) {
        this.toggleElementArea()
      }
    },
    changeSaveDisabled(disabled) {
      this.saveDisabled = disabled
    },
    getModuleList() {
      if (this.$isNotEmpty(this.$refs.formCanvas) &&
          this.$isNotEmpty(this.$refs.formCanvas.$refs.elementArea)) {
        this.$refs.formCanvas.$refs.elementArea.getRFormComp()
      }
    },
    showSignatureSettings() {
      var formMetaId = this.colItems[0].dataType
      this.$callApiParams('selectCformColumn',
        { dataType: formMetaId }, (result) => {
          if (result.success) {
            var columnsWf = result.data
            const sheets = window.luckysheet.getAllSheets()
            const filename = this.meta && this.meta.main && this.meta.main.name || sheets[0] && sheets[0].name
            this.$refs.formSignaturePositionDlg.show(columnsWf, sheets, filename)
          }
          return true
        })
    },
    signatures({ success, fail, cols }) {
      this.$refs.formCanvas.changeColItemsJSON(cols)
      this.btSave(success, fail)
    },
    checkboxTreeRes(res) {
      this.expression = res.attributes.expression
      var templateList = res.attributes.templateList
      this.bindTemplateData = []
      if (this.$isNotEmpty(templateList)) {
        this.bindTemplateData = templateList.map((item) => item.bindName)
      }
      if (this.$isNotEmpty(this.bindTemplateData)) {
        this.$refs.templateTree.setCheckedKeys(this.bindTemplateData)
        this.isExpression = false
      } else {
        this.isExpression = true
      }
    },
    checkChangeTemplate(res) {
      if (this.$isNotEmpty(res)) {
        if (res.length>0) {
          this.isExpression = false
        }
      } else {
        this.isExpression = true
        this.expression = ''
      }
    },
    toggle() {
      this.wrapSwitch ? this.$refs.formTopWrap.classList.add('hiddenRow') : this.$refs.formTopWrap.classList.remove('hiddenRow')
      this.wrapSwitch = !this.wrapSwitch
    },
    changeStyle(needWrap, switchWrap) {
      // needWrap 是否需要换行
      // switchWrap 按钮样式是上箭头还是下箭头
      needWrap ? this.$refs.formTopWrap.classList.add('setMarginTop') : this.$refs.formTopWrap.classList.remove('setMarginTop')
      switchWrap ? this.$refs.formTopWrap.classList.remove('hiddenRow') : this.$refs.formTopWrap.classList.add('hiddenRow')
      this.showWrapButton = needWrap
      // 如果按钮是开启的 如果按钮不是开启的且不用换行 则不隐藏formTopWrap
      // 如果按钮不是开启的 则隐藏formTopWrap
      if (switchWrap) {
        this.$refs.formTopWrap.classList.remove('hiddenRow')
      } else {
        if (!needWrap) {
          this.$refs.formTopWrap.classList.remove('hiddenRow')
          return
        }
        this.$refs.formTopWrap.classList.add('hiddenRow')
      }
    },
    btNewGuide() {
      this.$sessionStorage.remove('treeId')
      this.isNewAdd = true
    },
    setAllButtonDisabled(value) {
      this.allButtonDisabled = value
      this.$refs.versionRelate?.setAllButtonDisabled(value)
    },
    isDesignMode() { // 判断当前是否是设计模式
      return (this.mode === '设计')
    },
    isFillFormMode() { // 判断当前是否是制单模式
      return (this.mode === '制单')
    },
    isDetailMode() { // 判断当前是否是详情
      return (this.mode === '详情')
    },
    getMeta() {
      return this.$refs.formCanvas.getMetaToSave()
    },
    handlePrint() {
      // this.$showPrint(
      //   {
      //     meta: this.getMeta(),
      //     toSaveRefs: this.$refs.versionRelate
      //   })
      var row = this.getMeta().main
      this.$showExportWps({
        meta: this.getMeta(),
        toSaveRefs: this.$refs.versionRelate
      }, row.formType, '', '打印')
    },
    setColSettingVisible(value) {
      if (this.$isNotEmpty(value)) {
        this.colSettingVisible = value
      }
      const visible = (this.canvasOpened && this.colSettingVisible)
      this.$refs.formCanvas.colSettingVisible = visible
    },
    sliderChangeHeaderOK() { // 预览时调整标题高度间隔后，重新加载预览数据
      this.preview('EDIT')
    },
    blockPreviewCancel() {
      this.isBlockPreview = false
      this.hideColSetting = false
      this.$refs.versionRelate.pageHideLeftRightResume()
      $('.column-top').show()
      this.$setProperty(this, 'formFormat', 'isPreviewEdit', false)
      const meta = this.metaCopyBeforePreview
      this.$nextTick(() => this.initMeta('设计', meta, false))
    },
    blockPreview(type, meta) {
      let isPreviewDetail = false // 表单是否可以编辑（设计时）
      if (type === 'EDIT') {
        $('.column-top').first().hide()
        // this.$call(this, 'formEditDetail', 'setPreviewText')
      } else if (type === 'DETAIL') {
        isPreviewDetail = true
        $('.column-top').hide()
      } else {
        return
      }
      this.isBlockPreview = true
      this.hideColSetting = true
      // 隐藏左右内容区
      this.$refs.versionRelate.pageHideLeftRight()
      this.$setProperty(this, 'formFormat', 'isPreviewEdit', true)
      this.initMeta('设计', meta, false, undefined, { isPreviewDetail }) // 设计时和制单时一样, 如果init制单时参数缺失报错
    },
    preview(type) { // 预览
      var meta = this.getMeta()
      this.metaCopyBeforePreview = this.$clone(meta)
      if (this.isBlockForm) { // 区块预览
        this.blockPreview(type, meta)
        return
      }
      this.$refs.formCanvas.fillFormData(meta.colItems)
      this.$refs.formCanvas.setExtDataAssembly(this.formType)
      if (type === 'EDIT') {
        this.$refs.versionRelate.pageHideLeftRight()
        this.setColSettingVisible(false)
        this.initMeta('制单', meta, true)
        this.setAllButtonDisabled(true)
        this.$call(this, 'formEditDetail', 'setPreviewText')
        this.$setProperty(this, 'formFormat',
          'isPreviewEdit', true)

        $('.column-top').hide() // 预览制单时隐藏顶部按钮工具条
        $('.formCanvas').parent().addClass('formCanvasRuntime') // 预览制单时规则表单去掉外部边框
        $('.formCanvas').parent().addClass('previewFormCanvas')
        $('.formCanvas').closest('.main-border').css('padding-top', '0px')
      } else if (type === 'DETAIL') {
        // this.$refs.formViewDetail.initMeta(this.metaCopyBeforePreview, false)
        meta.needToReloadFreeJson = true
        this.$formDetailDlg({
          formType: this.formType,
          meta,
          toSaveRefs: this.$refs.versionRelate
        })
      } else if (type === 'PRINTF') {
        // this.printfDlgVisible = true
        this.$showExportWps({
          meta,
          toSaveRefs: this.$refs.versionRelate,
          cfromm: this
        }, meta.main.formType, '', '打印预览')
      }
    },
    showPrePrintf(urls, direction) {
      this.previewPdfUrls = urls
      if (direction || direction === 0) {
        this.previewDirection = direction
      } else {
        this.previewDirection = 1
      }
      this.printfDlgVisible = true
    },
    previewPdfClose() {
      if (this.previewPdfUrls.length) {
        this.previewPdfUrls.forEach((url) => window.URL.revokeObjectURL(url))
      }
    },
    initMeta(mode, meta, isInsertForm, isPrintDetails, initFormExData) { // 初始化表单数据
      this.mode = mode
      this.$refs.formCanvas.initMeta(mode, meta, isInsertForm, isPrintDetails, initFormExData)
    },
    previewResume() { // 结束预览
      this.$refs.versionRelate.pageHideLeftRightResume()
    },
    switchFormFormat(type) { // 自由/规则格式切换，目前控制只有新增才能切换
      if (this.isNew) {
        const isBlock = (type === 'block')
        if (type !== true) {
          window.luckysheet.destroy()
        }
        this.$refs.formCanvas.switchFormFormat(type)
        this.isFreedomForm = type
        this.blockDisabled = !isBlock
        this.toggleDisabled = !isBlock
        this.openNew()
      }
      this.setColSettingVisible()
    },
    switchColItemsSettingBar() {
      this.colSettingVisible = !this.colSettingVisible
      this.setColSettingVisible()
    },
    getMetaToSave(metaContainer) {
      metaContainer.meta = this.getMeta()
      if (metaContainer.meta.main.formType === '表单模板') {
        metaContainer.meta.main.templateType = '表单模板'
      }

      if (!this.isCopy) {
        metaContainer.meta.main.name = this.formName
        metaContainer.meta.main.formType = this.formType
        for (var i = 0; i < this.bindsWf.length; i++) {
          this.bindsWf[i].bindName = this.formName
        }
        metaContainer.meta.binds = this.bindsWf
      }
      this.isCopy = false
    },
    btSave(callback, callbackFailed, isSliderCauseSave, exArg = {}) {
      this.$refs.versionRelate.btSave(
        () => {
          // this.isSaving = true
          if (isSliderCauseSave) {
            return true
          }
          if (this.isFreedomForm) {
            this.$refs.formCanvas.$refs.formFormat.bindSignature()
            return true
          }
        },
        result => {
          this.isSaving = false
          if (typeof callback === 'function') {
            return callback(result)
          }
          // 处理规则表单保存 luckysheet报错
          if (!this.isFreedomForm) return
          // 获取文件上传table
        },
        // 保存失败时，用失败回调，传递versionRelate给调用方使用
        (result, params) => {
          this.isSaving = false
          if (result) {
            result.msg = '操作时发生错误<br/>相关信息：' + result.msg
          }
          if (callbackFailed) {
            if (params) {
              params['versionRelate'] = this.$refs.versionRelate
              params['formFormat'] = this.$refs.formCanvas.$refs.formFormat
            }
            callbackFailed(result, params)
          }
        }, () => {
          const exdata = {}
          if (!this.isFreedomForm) return exdata // 区分规则表单还是自由表单，目前规则表单没有图片章功能
          exdata.pictureForm = this.imgSave() || []
          return exdata
        }, () => {
          // 插入图片单独执行插入图片
          return this.insertImage()
        }, exArg)
    },
    syncFreedomRegularDisabled(meta) { // 表单已保存之后，设置自由规则按钮的可用性
      if (meta && this.$isNotEmpty(meta.main.id)) { // 表单保存之后不能改变格式类型
        this.freedomDisabled = !meta.main.isFreedomForm
        this.regularDisabled = meta.main.isFreedomForm
        this.blockDisabled = !meta.main.isBlockForm
        this.toggleDisabled = !meta.main.isBlockForm
      } else {
        this.freedomDisabled = false
        this.regularDisabled = false
        this.blockDisabled = false
        this.toggleDisabled = !this.isBlockForm
      }
    },
    canvasOpenedChange(canvasOpened) {
      this.canvasOpened = canvasOpened
    },
    metaInit(meta) {
      this.mainId = meta.main.id
      this.isNew = this.$isEmpty(meta.main.id)
      // this.switchFormFormatTip = this.isNew ? '' : '表单创建之后不能再切换格式'
      this.formName = meta.main.name
      this.formType = meta.main.formType
      this.isFreedomForm = meta.main.isFreedomForm
      this.isBlockForm = meta.main.isBlockForm
      this.pictureForm = meta.exData && meta.exData.pictureForm || []
      if (!this.isFreedomForm) {
        this.colSettingVisible = true
      } else if (this.isFreedomForm && meta.colItems.length) {
        this.colSettingVisible = true
      }
      this.colSettingCount = meta.colItems.length
      this.colItems = meta.colItems
      this.meta = meta
      this.setColSettingVisible()
      this.syncFreedomRegularDisabled(meta)
    },
    afterMetaQuery(result) {
      this.mode = this.$isEmpty(this.mode) ? '设计' : this.mode
      const isInsertForm = !this.isFillFormMode()
      this.initMeta(this.mode, result.data, isInsertForm)
      const binds = result.data.binds
      const bindItems = []
      const bindWf = []
      for (let i = 0; i < binds.length; i++) {
        bindItems.push(binds[i].metaName)
        const bind = {}
        bind.bindName = binds[i].bindName
        bind.metaName = binds[i].metaName
        bind.metaId = binds[i].metaId
        bindWf.push(bind)
      }
      this.bindWfitems = bindItems
      this.bindsWf = bindWf
      if (!this.isFillFormMode()) { // 当前预览制单时，不重新加载
        this.$setProperty(this, 'formEditDetail',
          'showButtons', false)
      }
      this.isNewAdd = false
      const mainId = result.data.main.id
      this.$sessionStorage.set('treeId', mainId)
    },
    nameChange() { // 标题改变同步到规则格式设计器
      this.$refs.formCanvas.nameChange(this.formName)
    },
    selectFormTypeDataDrop(queryString, cb) {
      var data = this.cformTypeData
      var results = queryString ? data.filter((type) => true) : data
      const filterRegularResult = results.filter(item => item.value !== '报销单' && item.value !== '事前申请单' && item.value !== '借款单')

      const filterResult = this.isFreedomForm ? results : this.isBlockForm ? results : filterRegularResult
      cb(filterResult)// 调用 callback 返回建议列表的数据
    },
    showCformTypeDropdown() { // 显示表单类别下拉
      this.$refs.cformTypeAutoComplete.focus()
    },
    formTypeSettingDlgClosed() { // 表单类别管理弹框关闭，刷新表单类别下拉数据
      this.updateCformTypeData()
    },
    updateCformTypeData() {
      this.$callApiParams('selectFormTypeDataDrop', {},
        result => {
          this.cformTypeData = result.data
          this.isCformTypeDataReady = true

          $('.cform-type .el-input__inner').unbind('hover')
          $('.cform-type .el-input__inner').hover(() => {
            this.showCformTypeDropdown()
          })

          var $cformTypeTitle = $('.cform-type .el-input-group__prepend')
          $cformTypeTitle.unbind('click')
          $cformTypeTitle.click(() => {
            this.canvasOpened && this.$refs.formTypeSetting.show()
          })

          return true
        })
    },
    nodeDeleted(formId) {
      this.updateCformTypeData()
    },
    afterMetaSaved(result) {
      this.updateCformTypeData()
    },
    colItemsChange(colItems) {
      this.colSettingCount = colItems.length
    },
    beforeOpenCanvas(id, exData) {
      this.syncFreedomRegularDisabled()
    },
    openNew(exData) { // 新增表单
      this.$refs.formCanvas.mode = '设计'
      const meta = this.$refs.formCanvas.newMeta()
      this.isNewAdd = true
      if (meta.main.isFreedomForm) {
        meta.needToReloadFreeJson = true
      }
      this.initMeta('设计', meta)
      this.isFreedomForm = meta.main.isFreedomForm
      this.colSettingCount = 0
      this.bindsWf = []
      this.bindWfitems = []
      this.colSettingVisible = true
      this.$refs.versionRelate.canvasOpened = true
      this.$nextTick(() => { this.setColSettingVisible() })
    },
    showMoreSetting() { // 更多设置弹框
      this.$refs.formMoreSetting.showMoreSetting(
        this.cformTypeData, this.formType)
      this.$refs.formMoreSetting.initSetting(this.$refs.formCanvas.meta)
    },
    dropdownImportClick(uploadRef) { // 新增时不提示直接可以导入否则就提示confirm
      if (uploadRef !== 'fileDomJson') { // 如果导入的json，那么就是新增导入
        const meta = this.getMeta()
        const isAddMeta = this.$isEmpty(meta.main.id)
        if (!isAddMeta) {
          this.$confirm('此操作会丢失原有的数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$refs[uploadRef].$refs['upload-inner'].handleClick()
          }).catch(() => {
          })
          return
        }
        if (!this.formType || !this.formName) {
          this.$message.warning('表单类别和表单名称不能为空！')
          return
        }
        const hasName = this.$call(this, 'supTree', 'getNode', this.formName)
        if (hasName) {
          this.$message.warning('当前表单名称已存在！')
          return
        }
      }
      this.$refs[uploadRef].$refs['upload-inner'].handleClick()
    },
    importSheet(file) { // 导入
      this.$call(this, 'formFormat',
        'importSheet', file, this.$refs.fileDomExcel)
    },
    importJson(file) {
      this.$refs.formCanvas.$refs.formFormat.changeLoading(true)
      this.$refs.fileDomJson.clearFiles()
      const fd = new FormData()
      fd.append('file', file.raw, file.name)
      request({
        url: `${process.env.VUE_APP_API_IC}/bifrost/cloud/api.do?apiKey=
        versionActionCformMetaEntity&actionKey=importJson`,
        method: 'post',
        data: fd
      }).then(res => {
        const result = res.data
        if (result && result.success) {
          const callBack = () => {
            this.$refs.versionRelate.nodeClick(result.data, {})
            setTimeout(() => {
              const node = result.data
              const itemKey = node.itemKey
              this.$refs.versionRelate?.$refs?.classifytree.setCurrentItem(node.id, itemKey)
            }, 300)
          }
          this.$refs.versionRelate?.$refs?.classifytree.init(callBack)
          this.$message.success('导入成功')
        } else {
          this.$message.warning(result.msg)
          this.$refs.formCanvas.$refs.formFormat.changeLoading(false)
        }
      })
    },
    exportSheet(format) { // 导出
      if (format === 'JSON') {
        const meta = this.getMeta()
        var id = ''
        if (meta) {
          id = meta.main.id
          if (this.$isEmpty(id)) {
            this.$message.warning('无法获取单据信息!')
          }
          this.$fileDownloadBykey('versionActionCformMetaEntity', {
            actionKey: 'exportJson',
            ids: id,
            isBlockForm: this.isBlockForm
          })
        }
      }
      if (format === 'EXCEL') {
        this.$call(this, 'formFormat', 'exportSheet', format)
      }
    },
    bindWf() {
      this.bindDlgVisible = true
      if (this.index === 0) {
        setTimeout(() => {
          this.$refs.checkboxtree.setCheckedKeys(this.bindWfitems)
        }, 500)
        this.index = 1
      } else {
        this.$refs.checkboxtree.setCheckedKeys(this.bindWfitems)
      }
    },
    bindTemplate() {
      this.bindTemplateVisible = true
    },
    cancelBindWf() {
      this.bindDlgVisible = false
    },
    cancelBindTemplate() {
      this.bindTemplateVisible = false
    },
    confirmBindTemplate() {
      var bindResultTemplate = this.$refs.templateTree.getCheckedNodes()
      this.templateDataList = []
      bindResultTemplate.forEach(item => {
        var bind = {}
        bind.metaId = this.mainId
        bind.metaName = this.formName
        bind.bindName = item.label
        bind.expression = this.expression
        this.templateDataList.push(bind)
      })
      var templateParams = {
        cformTemplateList: this.templateDataList
      }
      this.$callApi('saveTemplateBind',
        templateParams, result => {
          this.bindTemplateVisible = false
          return true
        }, undefined, {
          getExParamsCallApiSave: () => {
            return `&metaName=` + this.formName
          }
        })
    },
    confirmBindWf() {
      var copyBindWfitems = this.bindWfitems
      var copyBindsWf = this.bindsWf
      this.bindDlgVisible = false
      var result = this.$refs.checkboxtree.getCheckedNodesData()
      this.bindWfitems = result
      var bindResult = this.$refs.checkboxtree.getCheckedNodes()
      this.bindsWf = []
      bindResult.forEach(item => {
        var bind = {}
        bind.metaId = item.itemKey
        bind.metaName = item.label
        this.bindsWf.push(bind)
      })
      var rollBackBind = true // 回滚原来的绑定流程
      if (!this.isNew) { // 修改时才调自动保存，自动保存不要提示保存成功
        this.btSave(() => {
          rollBackBind = false
          return true
        }, undefined, true)
      } else {
        rollBackBind = false
      }
      if (rollBackBind) {
        this.bindWfitems = copyBindWfitems
        this.bindsWf = copyBindsWf
      }
    },
    // 复制按钮事件
    btCopy() {
      this.isCopy = true
      this.$refs.versionRelate.btCopy()
    },
    btCopyToOtherBookSet() {
      const refData = { colType: '弹框', dataRef: '选择用户账套' }
      this.$refData(undefined,
        refData,
        () => {},
        () => {},
        () => {},
        selectedData => {
          if (selectedData) {
            this.copyProcess(selectedData)
          }
        },
        { IS_ENABLED_eq: '是',
          multiple: true,
          isPrompt: false,
          filterSelf: true, // filterSelf是否需要过滤当前账套
          checkSelf: false // checkSelf是否默认选中当前账套
        })
    },
    copyProcess(selectedData) {
      const versionRelate = this.$refs.versionRelate
      let node
      if (versionRelate) {
        node = versionRelate.$refs.classifytree.getCurrentNode()
      }
      if (node) {
        this.isCopy = true
        const selectedBookSetIds = []
        if (selectedData.hasOwnProperty('list')) {
          selectedData.list.forEach(data => {
            selectedBookSetIds.push(data.id)
          })
        }
        const versionRelate = this.$refs.versionRelate
        selectedBookSetIds.forEach(selectedBookSetId => {
          const id = this.isBlockForm ? node.actualKey : node.itemKey
          versionRelate.openCanvasByCallApiCopy(id, { 'copyToBookSetId': selectedBookSetId },
            result => {
              versionRelate.btSave(undefined, () => {
                this.$message.success('操作成功')
                versionRelate.openCanvasByCallApi(node.itemKey, 'QUERY')
              }, () => {
                result.success = false
                versionRelate.openCanvasByCallApi(node.itemKey, 'QUERY')
              }, () => { return { 'copyToBookSetId': selectedBookSetId } }, undefined)
            })
        })
      }
    },
    // 处理图片上传组件数据保存
    imgSave() {
      let imgConfiguration = []
      const imgFileTableVal = this.$refs.formMoreSetting.getImgFileTableVal()
      // 获取table数据
      const LsImageOption = window.luckysheet.getImageOption()
      // 获取luckysheet数据

      if (this.$isNotEmpty(imgFileTableVal)) {
        for (let i = 0; i < imgFileTableVal.temFileData.length; i++) {
          for (let j = 0; j < Object.keys(LsImageOption).length; j++) {
            if (imgFileTableVal.temFileData[i].imgSrc === LsImageOption[Object.keys(LsImageOption)[j]].src ||
            imgFileTableVal.temFileData[i].imgString === LsImageOption[Object.keys(LsImageOption)[j]].src) {
              const imageOption = {
                imgWidth: LsImageOption[Object.keys(LsImageOption)[j]].default.width,
                imgHeight: LsImageOption[Object.keys(LsImageOption)[j]].default.height,
                imgLeft: LsImageOption[Object.keys(LsImageOption)[j]].default.left,
                imgTop: LsImageOption[Object.keys(LsImageOption)[j]].default.top,
                imgSrc: imgFileTableVal.temFileData[i].typeCode === '图片文件' ? imgFileTableVal.temFileData[i].imgSrc : '',
                formId: imgFileTableVal.bizId, // 表单实例ID
                luckySheetImgId: Object.keys(LsImageOption)[j] // luckysheet生成图片ID
              }
              delete imgFileTableVal.temFileData[i].id
              delete imgFileTableVal.temFileData[i].uid
              imgConfiguration.push({ ...imgFileTableVal.temFileData[i], ...imageOption })
              break
            }
          }
        }
      } else {
        if (this.$isNotEmpty(LsImageOption)) {
          for (let i = 0; i < Object.keys(LsImageOption).length; i++) {
            const imageOption = {
              imgWidth: LsImageOption[Object.keys(LsImageOption)[i]].default.width,
              imgHeight: LsImageOption[Object.keys(LsImageOption)[i]].default.height,
              imgLeft: LsImageOption[Object.keys(LsImageOption)[i]].default.left,
              imgTop: LsImageOption[Object.keys(LsImageOption)[i]].default.top,
              imgSrc: LsImageOption[Object.keys(LsImageOption)[i]].src,
              luckySheetImgId: Object.keys(LsImageOption)[i], // luckysheet生成图片ID
              formId: this.$sessionStorage.get('treeId')
            }
            imgConfiguration.push(imageOption)
          }
        }

        for (let i = 0; i < this.pictureForm.length; i++) {
          for (let j = 0; j < imgConfiguration.length; j++) {
            // 如果imgSrc为空时 动态   如果imgString为空时 图文
            if (this.$isNotEmpty(this.pictureForm[i].imgSrc)) {
              if (this.pictureForm[i].imgSrc === imgConfiguration[j].imgSrc) {
                this.pictureForm[i].imgWidth = imgConfiguration[j].imgWidth
                this.pictureForm[i].imgHeight = imgConfiguration[j].imgHeight
                this.pictureForm[i].imgLeft = imgConfiguration[j].imgLeft
                this.pictureForm[i].imgTop = imgConfiguration[j].imgTop
                this.pictureForm[i].luckySheetImgId = imgConfiguration[j].luckySheetImgId
                break
              }
            } else {
              if (this.pictureForm[i].imgString === imgConfiguration[j].imgSrc) {
                this.pictureForm[i].imgWidth = imgConfiguration[j].imgWidth
                this.pictureForm[i].imgHeight = imgConfiguration[j].imgHeight
                this.pictureForm[i].imgLeft = imgConfiguration[j].imgLeft
                this.pictureForm[i].imgTop = imgConfiguration[j].imgTop
                this.pictureForm[i].luckySheetImgId = imgConfiguration[j].luckySheetImgId
                break
              }
            }
          }
        }
        imgConfiguration = this.pictureForm
      }
      return imgConfiguration
    },
    // 插入图片
    insertImage() {
      this.$nextTick(() => {
        if (this.$refs.formCanvas.$refs.formFormat.insertImage) {
          return this.$refs.formCanvas.$refs.formFormat.insertImage()
        }
      })
    },
    // 区块表单切换要素区域
    toggleElementArea() {
      this.$refs.formCanvas.toggleElementArea()
    },
    changeToggleText(text) {
      this.toggleText = text
    },
    nodeClick(data, exData) {
      if (this.isBlockForm) {
        // 有actualKey是区块表单
        this.$refs.formCanvas?.hideElementArea()
        this.$refs.formCanvas?.resetBlockTabsConfig()
      }
    },
    changeLoading(loading) {
      if (this.$refs.formCanvas?.$refs.formFormat?.changeLoading) {
        this.clearBlocks()
        this.$refs.formCanvas.$refs.formFormat.changeLoading(loading)
      }
    },
    clearBlocks() {
      // 在新增 删除 切换 保存后 要清空block
      const blockTabs = this.$refs.formCanvas?.$refs.formFormat?.$refs.tabComponents
      if (this.$isNotEmpty(blockTabs)) {
        for (let i = 0; i < blockTabs.length; i++) {
          // const blockTab = blockTabs[i]
          // blockTab.clearBlocks()
          this.$call(this, 'tabComponents', 'clearBlocks')
        }
      }
    },
    setBlockFormId(actualKey) {
      this.blockFormId = actualKey
    },
    commonSetColItems(meta) {
      this.$refs.formCanvas.$refs.colSetting.setColItems(meta)
    }
  }
}
</script>
<style lang="scss">
  $btnGap: 6px;
  .previewFormCanvas .common-page .column-bottom { height:calc(100% - 9px) !important; }
  .previewFormCanvas .collapsed-content .top-btns { cursor: not-allowed !important;width: 50%; }
  .previewFormCanvas .collapsed-content .el-button {pointer-events: none;border-color: #EBEEF5;color: #C0C4CC }
  .vue-bifrostIcApp .common-page .cform-name .el-input__inner {
    // width: 150px;
    padding: 0px 5px;
  }
  // .vue-bifrostIcApp .common-page .cform-type {padding-left: $btnGap;margin-right: 5px;}
  .vue-bifrostIcApp .common-page .cform-type .el-input__inner {
    width: 85px;
    padding: 0px 5px;
    line-height: normal;
    cursor: pointer;
  }
  .vue-bifrostIcApp .common-page .cformAutocompleteDisabled .el-input__inner {
    cursor: not-allowed
  }
  .vue-bifrostIcApp .common-page .cform-type .el-icon-caret-bottom {
    cursor: pointer;
    width: 20px;
  }

  .cform-type {width: 260px !important;}
  .cform-type li {line-height: 18px !important;padding: 3px 5px 3px 15px !important;}
  .cform-type li .name {text-overflow: ellipsis;overflow: hidden;}
  .cform-type li .addr {font-size: 12px;color: #b4b4b4;}
  .cform-type li .highlighted .addr {color: #ddd;}
  .cform-type .el-autocomplete-suggestion__wrap {max-height: 330px !important;}
  .cform-type .el-input-group__prepend {cursor: pointer;}
  .cformAutocompleteDisabled .el-input-group__prepend {cursor: not-allowed;}
  .moreSettingDlg .el-dialog__header { display: none; }
  .moreSettingDlg .dialog-footer { text-align: right; margin-top: 20px;}
  .moreSettingDlg .el-dialog__body { height: 100% !important; }
  // .moreSettingDlg .el-tabs { height: calc(100% - 40px) !important; }
  .wfMoreSettingDlg .el-dialog__header { display: none; }
  .wfMoreSettingDlg .dialog-footer { text-align: right; }
  .wfMoreSettingDlg .el-dialog__body { height: 100% !important; }
  .wfMoreSettingDlg .el-tabs { height: calc(100% - 52px) !important; }
  .colSettingToolbar {
    display: flex;
    margin-bottom: 5px;
  }
  .colSettingToolbar button {
    padding: 2px 5px !important;
    border: none !important;
    margin-left: 2px;
  }
  .colSettingToolbarButtonContainer {
    width: 105px;
    // padding-top: 3px;
  }
  .colSettingToolbar .colSettingFilterInput {
    width: 70px;
    margin-right: 5px;
  }
  #separator .el-tag { width: 100px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position: relative;}
  #separator .el-tag .el-tag__close{position: absolute;top: 0;right: 0;}
  .colSettingToolbar .colSettingSelect {
    width: 70px;
  }
  .colSettingToolbar .colSettingSelect .el-input__inner {
    height: 28px;
    line-height: 28px;
    padding: 0px 15px 0px 10px;
  }
  .colSettingToolbar .colSettingSelect .el-input--small .el-input__icon {
    line-height: 28px;
  }
  .colSettingToolbar .colSettingSelect .el-input__suffix {right: -1px;}
  .colSettingToolbar .el-select .el-input .el-select__caret {
    font-size: 12px;
  }
  .colSettingToolbar .colSettingFilterInput .el-input__inner {
    line-height: 28px !important;
    height: 28px !important;
    padding: 2px 5px !important;
  }
  /* 样式影响（打开平台的之后影响了更多弹框的图标样式） */
  .moreSettingDlg .colSettingToolbarButtonContainer .el-button {
    height: auto;
  }
</style>
<style lang="scss" scoped>
  // $btnGap: 6px;
  .vue-bifrostIcApp .common-page /deep/ .main-border {
    position: relative;
    height: 100%;
    display: flex;
  }
  .vue-bifrostIcApp .common-page .version-relate-buttons .upload-import {
    margin: 0px 5px 0px 5px;
  }
  .sheet-view {
    margin-right: 10px;
    flex: 1;
  }
  .form-top-wrap {
    display: flex;
    // column-gap: 6px;
    // margin-left: $btnGap;
    // width: 27%;
    // .el-input,
    // .el-dropdown + .el-button{
    //   margin-left: $btnGap;
    // }
  }
  // .copy-btn { margin-right: 12px; }
  // .mleft-6 {
  //   margin-left: $btnGap;
  // }
  // .mright-6 {
  //   margin-right: $btnGap;
  // }
  .hiddenRow {
    display: none;
  }
  .setMarginTop {
    margin-top: 20px;
  }
  .version-relate-buttons .toolbar-button,
  .version-relate-buttons > .el-button,
  .form-top-wrap .el-dropdown,
  .form-top-wrap > .el-button,
  .form-top-wrap .cform-name,
  .form-top-wrap .cform-type {
    margin-right: 6px;
  }
</style>
