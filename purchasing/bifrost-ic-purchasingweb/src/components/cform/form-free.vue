<template>
  <form-edit-detail ref="formEditDetail" :class="(isFillFormMode() || isAuditMode()) ? 'free-fill-audit':''">
    <template #formMainContent>
      <div
        :id="`freeForm${meta.main.id}`"
        ref="freeForm"
        style="width:calc(100% - 10px);height:100%"
        class="free-container"
        v-if="!isPrintDetails"
        v-loading="loading"
        element-loading-spinner="el-icon-loading"
        element-loading-text="加载中...">
        <div style="flex:1" id="formFreeView" class="formCommonCols formFreeView">
            <div :id="options.container" style="width:100%;height:100%" class="formFree"/>
            <component ref="formCanvasTopButtonExtend" :is="formCanvasTopButtonExtendId"/>
            <div
                 v-for="(colItem, index) in specialColItems"
                 v-show="colItem.isRender"
                 :class="`customDom el-col ${colItem.className}`"
                 :key="colItem.id+index"
                 :style="colItem.style">
                 <el-date-picker
                  v-model="colItem.checkBoxValues"
                  v-if="colItem.colType==='日期范围'&& colItem.isRender"
                  :disabled="isDisabled(colItem)"
                  type="daterange"
                  class="customDom-daterange"
                  range-separator="-"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始"
                  end-placeholder="结束">
                </el-date-picker>
                <el-radio-group
                        :id="`colItem${colItem.id}`"
                        :class="`customRadio ${colItem.classNameError} ${colItem.auditCanEdit}`"
                        v-model="colItem.dataValue"
                        v-else-if="colItem.colType==='单选'&& colItem.isRender"
                        :disabled="isDisabled(colItem)"
                        @change="specialColItemModified(colItem)"
                        @click.native="preventReloadForm">
                    <el-radio :label="radio.label"
                          v-for="(radio,radioIndex) in colItem.labelValues"
                          :key="radio.label+radioIndex"/>
                </el-radio-group>

                <el-checkbox-group
                    :style="formatCheckboxStyle(colItem)"
                    :id="`colItem${colItem.id}`"
                    :class="`customCheckbox ${colItem.classNameError} ${colItem.auditCanEdit}`"
                    v-model="colItem.checkBoxValues"
                    v-else-if="colItem.colType==='多选' && colItem.isRender"
                    :disabled="isDisabled(colItem)"
                    @change="specialColItemModified(colItem)"
                    @click.native="preventReloadForm">
                    <el-checkbox
                        :label="checkbox.label"
                        :key="checkbox.label+checkboxIndex"
                        v-for="(checkbox,checkboxIndex) in colItem.labelValues">
                    </el-checkbox>
                </el-checkbox-group>

                <el-select
                    :id="`colItem${colItem.id}`"
                    :class="`customSelect ${colItem.classNameError} ${colItem.auditCanEdit} ${colItem.modifyType.includes('只读')?'hide-icon':''}`"
                    :placeholder="colItem.modifyType.includes('只读')?'':'请选择'"
                    clearable
                    filterable
                    v-model="colItem.dataValue"
                    v-else-if="colItem.colType==='下拉框'&& colItem.isRender"
                    :disabled="isDisabled(colItem)"
                    @click.native="refLabelValues(colItem)"
                    @change="specialColItemModified(colItem)"
                    @visible-change="preventReloadForm">
                  <div slot="empty" style="text-align: center; color: #b2b2b2">正在加载</div>
                    <el-option
                        v-for="(select,selectIndex) in colItem.labelValues"
                        :key="select.label+selectIndex"
                        :label="select.label"
                        :value="select.value">
                    </el-option>
                </el-select>

                <el-input :id="`colItem${colItem.id}`"
                        type="text"
                        v-else-if="colItem.colType==='公示' && colItem.isRender"
                        @click.native="refPublicInfos(colItem)"
                        readonly="readonly"
                        prefix-icon="el-icon-edit"
                        :class="`customInput customInputPublicInfo ${colItem.classNameError} ${colItem.auditCanEdit}`"
                        :title="colItem.dataValue"
                        v-model="colItem.dataValue"/>

                <!--这里不使用el-input是为了实现多个值时换行处理-->
              <el-tooltip  v-else-if="colItem.colType==='弹框'&& colItem.isRender" :disabled="colItem.tooltipDisabled"  placement="top" >
                <div class="el-input el-input--small el-input--prefix customInput"
                     :title="colItem.dataValue">
                    <div type="text" readonly="readonly"  @click="refData(colItem)" autocomplete="off"
                         :id="`colItem${colItem.id}`"
                         :class="`el-input__inner div-as-input ${colItem.classNameError} ${colItem.auditCanEdit}`">
                        {{colItem.dataValue}}
                    </div>
                    <span :class="`el-input__prefix ${colItem.auditCanEdit}`" :style="`${(!colItem.dataValue || !colItem.canClearRef) ? '' : `top:calc(50% - calc(${colItem.style.height} / 2))`}`" v-if="!(colItem.modifyType ||'').includes('只读')">
                        <i v-if="!colItem.dataValue || !colItem.canClearRef"  @click="refData(colItem)" class="el-input__icon el-icon-search"/>
                        <i v-else @click="clearRefData(colItem)" class="el-input__icon el-icon-circle-close"/>
                    </span>
                </div>
                <template #content>
                  <div class="special-tooltip">
                    <div class="tooltip-title">{{colItem.label}}</div>
                    <div class="tooltip-content">
                      <div class="tooltip-content-list" v-for="tooltip in colItem.checkBoxValues" :key="tooltip.id">
                        {{tooltip}}
                      </div>
                    </div>
                  </div>
                </template>
              </el-tooltip>
            </div>

        </div>

        <div class="formFreeViewRightContent mini-table">
          <right-collapsed-container
                  ref="rightCollapsedContainer"
                  v-if="isRightCollapsed && isFillFormMode() && hasExtAssembly"
                  :isRightCollapsed='isAuditMode() ? true : false'>
            <template #content>
              <slot name="content"/>
            </template>
          </right-collapsed-container>
        </div>
      </div>
      <div
        :id="`freeForm${meta.main.id}`"
        ref="freeForm"
        style="width:100%;height:100%"
        class="free-container printDetailsWrap"
        v-if="isPrintDetails"
        v-loading="loading"
        element-loading-spinner="el-icon-loading"
        element-loading-text="加载中...">
        <div style="flex:1" id="formFreeView" class="formCommonCols">
            <div :id="options.container" style="width:100%;height:100%" class="formFree"/>
            <component ref="formCanvasTopButtonExtend" :is="formCanvasTopButtonExtendId"/>
            <div :class="`customDom el-col ${colItem.className} customDomPrintDetails `"
                 v-for="(colItem, index) in specialColItems"
                 :key="colItem.id+index"
                 :style="colItem.style">
                 <el-radio-group
                        :style="formatCheckboxStyle(colItem)"
                        :id="`colItem${colItem.id}`"
                        :class="`customRadio ${colItem.classNameError} printRadio`"
                        v-model="colItem.dataValue"
                        v-if="colItem.colType==='单选'&& colItem.isRender &&isShowPrintCheckbox()"
                        @change="specialColItemModified(colItem)"
                        @click.native="preventReloadForm">
                        <el-radio :label="radio.label"
                        v-for="(radio,radioIndex) in colItem.labelValues"
                        :key="radio.label+radioIndex"/>
                </el-radio-group>
                <el-checkbox-group
                    :id="`colItem`"
                    :style="formatCheckboxStyle(colItem)"
                    :class="`customCheckbox ${colItem.classNameError} printCheckbox`"
                    v-model="colItem.checkBoxValues"
                    v-if="colItem.colType==='多选' && colItem.isRender &&isShowPrintCheckbox()"
                    @change="specialColItemModified(colItem)"
                    @click.native="preventReloadForm">
                    <el-checkbox
                        :label="checkbox.label"
                        :key="checkbox.label+checkboxIndex"
                        v-for="(checkbox,checkboxIndex) in colItem.labelValues">
                    </el-checkbox>
                </el-checkbox-group>

            </div>
        </div>
      </div>
    </template>
  </form-edit-detail>
</template>

<script>
import $ from 'jquery'
import ColSetting from './col-setting'
import { exportExcel } from './js/exportSheet'
import { formatData } from './js/formatData'
import LuckyExcel from 'luckyexcel'
import FormRegular from './form-regular'
import { downloadFile } from '@/api/file/file'
import { CFORM_BLOCK_TYPE } from '@/constant'
var elementResizeDetectorMaker = require('element-resize-detector')
export default {
  name: 'form-free',
  components: { FormRegular, ColSetting },
  props: {
    isPreviewDetail: { type: Boolean, default: false },
    hasExtDataAssembly: { type: Boolean, default: false },
    defaultSheetData: {
      type: Array,
      default: () => [
        {
          name: 'Sheet',
          color: '',
          status: '1',
          order: '0',
          data: [],
          config: {},
          index: 0,
          showGridLines: 1,
          row: 22,
          column: 11,
          imgBase64: ''
        }
      ]
    },
    hideSilder: { type: Boolean, default: false }
  },
  inject: {
    resetDlgSize: { default: () => {} }
  },
  watch: {
    loading: function(n, o) {
      // console.log('loading', n, o)
    }
  },
  data() {
    return {
      mergeInfo: {},
      borderInfo: [],
      isBlankColItemDataVlue: true,
      mode: '', // "设计", "制单", "详情"
      meta: { main: { id: '' }, cformSettings: [] }, // 表单数据对象
      hasExtAssembly: this.hasExtDataAssembly,
      signatureImage: {}, // 存储签章图片ID
      isMultipleEditTabs: false, // 是否是多tab制单模式
      formCanvasTopButtonExtendId: '',
      defaultToolBarConfig: {
        undoRedo: false, // 撤销重做
        paintFormat: false, // 格式刷
        numberDecrease: false, // '减少小数位数'
        numberIncrease: false, // '增加小数位数
        sortAndFilter: false, // '排序和筛选'
        image: false, // '插入图片'
        link: false, // '插入链接'
        chart: false, // '图表'
        protection: false, // '工作表保护'
        frozenMode: false, // '冻结方式'
        conditionalFormat: false, // '条件格式'
        print: false, // '打印'
        function: false, // '公式'
        splitColumn: false, // '分列'
        screenshot: false, // '截图',
        postil: true, // '批注'
        dataVerification: false, // '数据验证'
        findAndReplace: false // '查找替换'
      },
      options: {
        container: 'formFree', // LuckySheet为容器id
        lang: 'zh',
        defaultFontSize: 11,
        showtoolbar: true,
        sheetFormulaBar: false,
        showsheetbar: false,
        // showstatisticBar: false,
        showtoolbarConfig: this.defaultToolBarConfig,
        cellRightClickConfig: {
          copy: false, // 复制
          copyAs: false, // 复制为
          paste: false, // 粘贴
          insertRow: true, // 插入行
          insertColumn: true, // 插入列
          deleteRow: true, // 删除选中行
          deleteColumn: true, // 删除选中列
          deleteCell: false, // 删除单元格
          hideRow: false, // 隐藏选中行和显示选中行
          hideColumn: false, // 隐藏选中列和显示选中列
          rowHeight: false, // 行高
          columnWidth: false, // 列宽
          clear: false, // 清除内容
          matrix: false, // 矩阵操作选区
          sort: false, // 排序选区
          filter: false, // 筛选选区
          chart: false, // 图表生成
          image: false, // 插入图片
          link: false, // 插入链接
          data: false, // 数据验证
          cellFormat: false // 设置单元格格式
        },
        data: this.defaultSheetData,
        defaultRowHeight: 28,
        defaultColWidth: 76,
        enableAddRow: false,
        enableAddBackTop: false,
        hook: {
          cellEditBefore: this.cellEditBefore,
          rangeSelect: this.rangeSelect,
          cellMousedown: this.cellMousedown,
          cellRenderAfter: this.cellRenderAfter,
          workbookCreateAfter: this.workbookCreateAfter,
          updated: this.cellUpdated,
          scroll: this.cellScroll,
          sheetMouseup: this.sheetMouseup,
          cellAllRenderBefore: this.showSignature
        }
      },
      isPreviewEdit: false, // 是否正在预览
      cellPosition: {},
      xyColItemMap: {},
      labelColItemMap: {},
      labelOriginColItemMap: {},
      specialColItemMap: {}, // 特殊要素map
      specialColItems: [], // 特殊要素
      isClosingRefDlg: false, // 正在关闭操作弹框
      // isManuallySetValue: false,
      createdAfter: false, // 标识是否已经创建实例，否则会执行api方法在前导致报错
      isFillNewForm: false, // 绑定要素时，需要指明是否是制单新增，来决定单元格是否取默认值
      isImportingExcel: false,
      rightCollapsed: false,
      customUIArr: ['单选', '多选', '下拉框', '弹框', '弹框ID', '公示', '日期范围'],
      isPrintDetails: false,
      // 表单拓展组件
      formExtend: undefined,
      cformSettings: [], // “更多设置”中的表单设置数据
      exData: {}, // 用于存储与扩展相关的数据
      sheetClientWidth: 0,
      sheetclientHeight: 0,
      formFreeClientWidth: 0,
      formFreeClientHeight: 0,
      cacheXyArr: [],
      cacheXyRender: [],
      dataVo: undefined,
      overDialogZindex: null,
      isRightCollapsed: true,
      time: '',
      isEditing: false, // 用于判断是否手动触发编辑
      isActivated: false,
      loading: true,
      autoHeightCellMap: {},
      exCreateFromData: {}
    }
  },
  activated() {
    this.isActivated = true
    this.needToReloadFreeJson = true
    this.loadJson()
    this.addEventListeners()
  },
  deactivated() {
    this.needToReloadFreeJson = false
    // window.luckysheet.exitEditMode()
    $('#luckysheet-input-box').css('z-index', '-1')
    this.removeEventListeners()
  },
  mounted() {
    this.resizeClient()
    // 将监听操作提成多个函数 方便解绑
    this.addEventListeners()
  },
  beforeDestroy() {
    this.removeEventListeners()
  },
  methods: {
    addEventListeners() {
      this.removeEventListeners()
      document.addEventListener('mousedown', this.mousedownEvent)
      window.addEventListener('keydown', this.keydownEvent)
      window.addEventListener('wheel', this.wheelEvent)
    },
    removeEventListeners() {
      document.removeEventListener('mousedown', this.mousedownEvent)
      window.removeEventListener('keydown', this.keydownEvent)
      window.removeEventListener('wheel', this.wheelEvent)
    },
    mousedownEvent(event) {
      if (event.toElement.hasAttribute('class') && event.toElement.className.includes('el-tabs__item is-top')) {
        $('#luckysheet-input-box').css('z-index', '-1')
        window.luckysheet.exitEditMode()
      }
    },
    keydownEvent(event) {
      // 监听用户放大页面操作导致表单不见
      if (event.ctrlKey && (event.key === '+' || event.key === '=' ||
       event.key === '-' || event.key === '_')) {
        // 执行放大页面的操作
        this.resizeClient()
      }
    },
    wheelEvent(event) {
      if (event.ctrlKey) {
        // 判断鼠标滚轮方向
        if (event.deltaY > 0 || event.deltaY < 0) {
          // 刷新视图
          this.resizeClient()
        }
      }
    },
    showBtDraft(isShowBtDraft) {
      this.$refs.formEditDetail.hideBtDraft = !isShowBtDraft
    },
    setBtDraftLoading(isLoading) {
      this.$refs.formEditDetail.setBtDraftLoading(isLoading)
    },
    formatCheckboxStyle(colItem) {
      const height = colItem.style && (colItem.style.height || '').replace('px', '') * 1
      const style = {}
      style['flex-direction'] = 'row'
      style['height'] = 'auto'

      if (this.isShowPrintCheckbox()) {
        style['flex-direction'] = 'column'
        style['height'] = '98%'
        style['justify-content'] = 'center'
      }
      if (height <= 46) {
        style['height'] = '80%'
      }
      return style
    },
    isShowPrintCheckbox() {
      if (this.meta.cformSettings.length) {
        return this.meta.cformSettings.some(item => item.optionName.includes('显示打印选项'))
      } else {
        return false
      }
    },
    uniFormat(arr) {
      return Array.from(new Set(arr))
    },
    findColumnlenIndex(arr) {
      const reverseArr = arr.reverse()
      const reversFirstNum = reverseArr[0]
      const arrLength = arr.length
      const notReversFirstNum = reverseArr.filter((item, index) => {
        if (item !== reversFirstNum) {
          return item
        }
      })
      var reversedNotFirstNum = notReversFirstNum[0]
      var revarseisFirstNumArr = []
      reverseArr.some((i, index) => {
        if (i === reversedNotFirstNum) {
          revarseisFirstNumArr.push(index)
        }
      })
      const isFirstTotal = revarseisFirstNumArr.length ? revarseisFirstNumArr[0] : 0
      var resultIndex = isFirstTotal === 0 || isFirstTotal === 1 ? arrLength : (arrLength - isFirstTotal)
      return resultIndex
    },
    formatColumn(obj, subIndex) {
      const subStr = Object.entries(obj).splice(0, subIndex)
      const subStrObj = {}
      subStr.map(arr => {
        const key = arr[0]
        const value = arr[1]
        subStrObj[key] = value
      })
      return subStrObj
    },
    formatObjToFilterColumnlen(obj) {
      const valArr = Object.values(obj)
      return this.formatColumn(obj, this.findColumnlenIndex(valArr))
    },
    formatCellData(sheetData = []) {
      // 记录竖直方向自下而上循环拿到第一个有值索引并截取有值部分
      const cacheColumnHaveDataIndexNums = []
      sheetData.reverse().forEach((item, index, arr) => {
        item.forEach(i => {
          if (this.isDetailMode() || this.isAuditMode()) { // 处理制单/审核详情和打印时保证都是黑色字体不影响详情和打印字体引起模糊不清
            if (i) {
              if (i.fc) {
                i.fc = '#000000'
              }
              if (i.ct && i.ct.s) {
                i.ct.s.forEach(sItem => {
                  sItem.fc = '#000000'
                })
              }
              cacheColumnHaveDataIndexNums.push(index)
            }
          } else {
            if (i) {
              cacheColumnHaveDataIndexNums.push(index)
            }
          }
        })
      })

      sheetData.splice(0, cacheColumnHaveDataIndexNums[0])

      // 记录水平方向自右向左内测二维数组轮询拿到第一个有值索引并截取有值部分
      const cacheRowHaveDataIndexNums = []
      const rowData = sheetData.reverse()
      rowData.forEach((item) => {
        item.reverse().forEach((i, rowIndex) => {
          if (this.isDetailMode() || this.isAuditMode()) {
            if (i) {
              if (i.fc) {
                i.fc = '#000000'
              }
              if (i.ct && i.ct.s) {
                i.ct.s.forEach(sItem => {
                  sItem.fc = '#000000'
                })
              }
              cacheRowHaveDataIndexNums.push(rowIndex)
            }
          } else {
            if (i) {
              cacheRowHaveDataIndexNums.push(rowIndex)
            }
          }
        })
      })
      rowData.forEach(item => item.reverse())
      var spliceLength = rowData[0].length - Math.min.apply(null, cacheRowHaveDataIndexNums)
      const formatResult = rowData.map(endItem => {
        endItem = endItem.splice(0, spliceLength)
        return endItem
      })
      return formatResult
    },
    hideCellError(colItem) {
      if (this.isSpecialColItem(colItem.colType)) {
        return false
      } else {
        this.hideError(colItem)
      }
    },
    showTooltip(item, content) {
      item.tooltipDisabled = false
      item.tooltipContent = content + item.id
      this.specialColItemModified(item)
    },
    cellScroll(scrollPosition) { // 滚动事件动态刷新位置
      this.cacheXyRender = []
      $('#luckysheet-input-box').css('z-index', '-1')
    },
    getColItemStyle(colItem, positionData) {
      if (positionData) {
        var scrollTop = positionData.scrollPosition ? positionData.scrollPosition.scrollTop : 0
        var offsetTop = this.hasExtAssembly ? 10 : 0
        var offsetLeft = this.hasExtAssembly ? 10 : 0
        offsetTop = this.isMultipleEditTabs ? 0 : offsetTop
        offsetLeft = this.isMultipleEditTabs ? 0 : offsetLeft

        const position = positionData.position
        if (position) {
          var widthOffset = (colItem.colType === '弹框') ? -3 : 0
          var leftOffset = (colItem.colType === '弹框') ? 2 : 1
          leftOffset = (colItem.colType === '下拉框') ? -2 : leftOffset
          leftOffset = (colItem.colType === '单选') ? -3 : leftOffset
          leftOffset = (colItem.colType === '多选') ? -2 : leftOffset
          leftOffset = (colItem.colType === '日期范围') ? -2 : leftOffset
          leftOffset = (colItem.colType === '公示') ? 0 : leftOffset
          return {
            width: `${position.end_c - position.start_c + 1 + widthOffset}px`,
            height: `${position.end_r - position.start_r - 3}px`,
            top: `${position.start_r + offsetTop - scrollTop + 2}px`,
            left: `${position.start_c + offsetLeft + leftOffset}px`,
            zIndex: '1999'
          }
        }
      }
      return {}
    },
    isSpecialColItem(colType) { // 判断是否是特性UI
      return this.customUIArr.includes(colType)
    },
    verifyDate(colItem) { // 校验开始时间和借结束时间
      const { rowIndex: row, columnIndex: col } = colItem
      const keysMap = {
        '开始': '结束',
        '结束': '开始'
      }
      const startMap = {
        '开始': true
      }
      const sheet = window.luckysheet.getAllSheets()[0].data
      const ct = sheet[row][col].ct
      const objKeys = Object.keys(keysMap)

      if (!this.isEditing || colItem.colType !== '日期' || !(ct && ct.t === 'd')) {
        this.isEditing = false
        return
      }
      for (let i = 0; i < objKeys.length; i++) {
        const isIncludes = colItem.label.includes(objKeys[i])
        if (!isIncludes) {
          continue
        }
        const keywords = colItem.label.split(objKeys[i])
        const firstKey = keywords[0] || ''
        const endKey = keywords[1] || ''
        const relatKey = firstKey + keysMap[objKeys[i]] + endKey
        const relatColItem = this.labelColItemMap[relatKey]
        const isStart = startMap[objKeys[i]]
        let offsetDate = 0

        if (!relatColItem || !relatColItem.dataValue) {
          continue
        }

        if (isStart) {
          offsetDate = this.$offsetDate(colItem.dataValue, relatColItem.dataValue)
        } else {
          offsetDate = this.$offsetDate(relatColItem.dataValue, colItem.dataValue)
        }

        if (offsetDate < 0) {
          let message = ''
          if (isStart) {
            message = `开始时间不能大于结束时间`
          } else {
            message = `结束时间不能小于开始时间`
          }

          this.$message.error(message)
          this.setValue(colItem.label, '')
        }
      }
      this.isEditing = false
    },
    cellUpdated(info) { // 表单更新钩子函数
      if (this.loading) {
        this.loading = false
      }
      const { type } = info
      if (type === 'addRC') { // 增加行列操作
        this.xyColItemMap = {}
        const colItems = Object.values(this.labelOriginColItemMap)
        colItems.forEach(colItem => {
          if (!colItem.isAutoAdd) {
            const xyIndex = window.luckysheet.find(`#{${colItem.label}}`)
            if (xyIndex.length) {
              const [{ row, column }] = xyIndex
              colItem.rowIndex = row
              colItem.columnIndex = column
              const key = this.wrapXY(row, column)
              this.xyColItemMap[key] = colItem
            }
          }
        })
      } else if (type === 'delRC') { // 删除行列操作
        this.xyColItemMap = {}
        var colItems = Object.values(this.labelOriginColItemMap)
        const delArr = []
        colItems.forEach((item, index, arr) => {
          const isNotLabelArr = window.luckysheet.find(`#{${item.label}}`)
          if (!isNotLabelArr.length) { // 移除找不到绑定过的要素
            if (!item.isAutoAdd) {
              // this.$call(this.$parent, 'colSetting', 'doOutList', item.id, true)
              isNotLabelArr.forEach(i => {
                const { row, column } = i
                var key = this.wrapXY(row, column)
                delete this.xyColItemMap[key]
              })
              delArr.push(item.id)
            }
          } else {
            // arr.map(oldItem => {
            // const xyIndex = window.luckysheet.find(`#{${item.labelOrigin}}`)
            // if (xyIndex && xyIndex.length > 0) {
            const [{ row, column }] = isNotLabelArr
            item.rowIndex = row
            item.columnIndex = column
            const key = this.wrapXY(row, column)
            this.xyColItemMap[key] = item
            // }
            // })
          }
        })
        this.$call(this.$parent, 'colSetting', 'RowOutList', delArr)
      } else if (type === 'datachange' &&
        (this.isFillFormMode() || this.isAuditMode())) {
        // if (this.isManuallySetValue) {
        //   this.isManuallySetValue = false
        //   return
        // }

        if (info.dataRange && info.dataRange.length > 0) {
          var rowIndex = info.dataRange[0].row_focus
          var columnIndex = info.dataRange[0].column_focus
          var key = this.wrapXY(rowIndex, columnIndex)
          var colItem = this.xyColItemMap[key]
          if (this.$isNotEmpty(colItem)) {
            if (this.isAuditMode() && !this.$parent.isAuditCanEdit(colItem.label)) {
              return
            }
            var value = this.getValue(colItem.label)
            colItem.dataValue = value
            this.colItemModified(colItem)
            this.verifyDate(colItem)
          }
        }
        this.fixSelectedFocusPositionError()
        this.$parent.startCalculateFormulaValues()
      }
    },

    doCommonColItemModify(colItem, value) {
      if (this.$isNotEmpty(colItem)) {
        colItem.dataValue = value
        this.setCellValue(colItem, value, undefined, true)
      }
    },

    // 执行要素值变化的动作，这个动作会引发要素变化的扩展回调处理
    // 这个方法主要是为了提供给外部使用，比如报账单参照多指标设定
    // 指标申请金额之后，需要同步更新单据的申请金额，并联动更新收款人组件
    doColItemModify(colItem, value, notSetValue = false) {
      if (this.$isNotEmpty(colItem)) {
        colItem.dataValue = value
        this.setCellValue(colItem, value, undefined, notSetValue)
        this.$nextTick(() => { this.colItemModified(colItem) })
      }
    },
    colItemModified(colItem) { // 填单模式，要素值变化会调用本方法
      if (this.$isNotEmpty(colItem)) {
        this.hideError(colItem)
        // 只有在制单模式（含制单预览）时，才会触发单元格变化的扩展处理
        var callbacks = this.$getFormActionCallbacks(this, 'colItemModifiedCallbacks')
        if (this.$isNotEmpty(callbacks) &&
            (this.isFillFormMode() || this.$parent.isAuditCanEdit(colItem.label))) {
          var colItems = Object.values(this.labelColItemMap)
          callbacks.forEach(cb => cb(colItem, this, colItems, this.meta))
        }
        if (this.formExtend) {
          if (this.formExtend.verifyAmountIsMatchRefAmount) {
            this.formExtend.verifyAmountIsMatchRefAmount(colItem, this)
          }
        }
      }
    },
    specialColItemModified(colItem) { // 特殊要素值变化
      this.colItemModified(colItem)
      this.preventReloadForm()
      this.updataColItemRemind(colItem)
    },
    addColItemModifiedCallbacks(callbacks) {
      this.$addFormActionCallbacks(
        this, 'colItemModifiedCallbacks', callbacks)
    },
    setColItemXY(colItem) { // 获取单元格位置
      colItem.rowIndex = this.cellPosition.row_focus
      colItem.columnIndex = this.cellPosition.column_focus
      if (this.isDesignMode() && colItem.colType === '隐藏框') {
        // this.setCellValue(colItem, '')
        colItem.rowIndex = -1
        colItem.columnIndex = -1
      }
    },
    removeColItem(colItem) {
      colItem.columnIndex>-1 && colItem.rowIndex>-1 && window.luckysheet.clearCell(colItem.rowIndex, colItem.columnIndex)
    },
    clickCollapsed() {
      this.rightCollapsed = !this.rightCollapsed
    },
    exportSheet(format) { // 导出
      if (format === 'EXCEL') {
        if (this.meta.main && this.$isEmpty(this.fileName)) {
          this.fileName = this.meta.main.name
        }
        const fileName = this.fileName || window.luckysheet.getAllSheets()[0].name
        exportExcel(window.luckysheet.getAllSheets(), fileName)
      }
    },
    hideErrorAll() {
      if (this.isDesignMode()) {
        return
      }
      this.options.data[0].config.borderInfo = this.$clone(this.borderInfo)
    },
    hideError(colItem) {

    },
    addShowErrorCallbacks(callbacks) {
      this.$addFormActionCallbacks(
        this, 'showErrorCallbacks', callbacks)
    },
    showError(result, errorNum) { // 显示校验错误
      var hitCount = errorNum || 0
      var errorItems = {}
      this.hideErrorAll()
      $('.customDom.colItemError').unbind()

      if (result && !result.success && result.attributes) { // 后端的错误信息放在R.attributes
        errorItems = result.attributes
      }

      var labels = Object.keys(errorItems)
      labels.forEach(label => {
        var colItem = this.labelOriginColItemMap[label]
        if (this.$isEmpty(colItem)) return
        hitCount++
        if (this.$isNotEmpty(colItem) && colItem.rowIndex !== -1 && colItem.columnIndex !== -1) {
          const merge = this.mergeInfo[`${colItem.rowIndex}_${colItem.columnIndex}`]
          if (!merge) {
            window.luckysheet.setCellFormat(colItem.rowIndex, colItem.columnIndex, 'bd', { borderType: 'border-all', style: '7', color: '#ff0000' })
          } else {
            for (let i = 0; i < merge.cs; i++) {
              window.luckysheet.setCellFormat(colItem.rowIndex, colItem.columnIndex + i, 'bd', { borderType: 'border-all', style: '7', color: '#ff0000' })
            }
            for (let i = 0; i < merge.rs; i++) {
              window.luckysheet.setCellFormat(colItem.rowIndex + i, colItem.columnIndex, 'bd', { borderType: 'border-all', style: '7', color: '#ff0000' })
            }
          }
        }
      })

      // 扩展的错误提示，比如收款人组件的错误标红
      var callbacks = this.$getFormActionCallbacks(
        this, 'showErrorCallbacks')
      callbacks.forEach(cb => cb(errorItems))
      return hitCount
    },
    // 窗口变化同步刷新sheet
    sheetRefresh() {
      const arr = window.luckysheet.getAllSheets()
      // 屏幕变化 隐藏要素
      this.specialColItems.forEach(item => this.$set(item, 'isRender', false))
      arr.forEach((element) => {
        //
        if (element.status === 1 || element.status === '1') {
          window.luckysheet.updataSheet(element)
        }
      })
      // 图片相关处理
      // this.formDisposeImg()
      // this.insertImage()
    },
    // 刷新视图，随容器宽高改变而刷新
    resizeClient() {
      const that = this
      var erd = elementResizeDetectorMaker()
      erd.listenTo($('#formFreeView'), function(element) {
        that.$nextTick(() => {
          that.sheetRefresh()
        })
      })
    },
    editingColItemChanged(selection, row) { // 属性框编辑的要素变动时，联动选中标题
    },
    colSetcheckedSelectClick(selection, row) { // 处理已选要素联动选中对应的label
      const sheetShowVal = `#{${row.label}}`
      const sheetLabelInfo = window.luckysheet && window.luckysheet.find(sheetShowVal)
      const rangeShowVal = {}
      if (this.$isNotEmpty(selection) && this.$isNotEmpty(sheetLabelInfo)) {
        if (sheetLabelInfo.length) {
          const row = sheetLabelInfo[0].row
          const column = sheetLabelInfo[0].column
          rangeShowVal['row'] = [row, row]
          rangeShowVal['column'] = [column, column]
        }
        if (this.$isNotEmpty(rangeShowVal)) {
          window.luckysheet.setRangeShow(rangeShowVal)
        }
      } else {
        window.luckysheet.setRangeShow('A1', { show: false })
        if (selection[0]) {
          this.$parent.$refs.colSetting.$refs.tableColItems.toggleRowSelection(selection[0] || '', true)
        }
      }
    },
    isDesignMode() { // 判断当前是否是设计模式
      return (this.mode === '设计')
    },
    isFillFormMode() { // 判断当前是否是制单模式
      return (this.mode === '制单')
    },
    isDetailMode() { // 判断当前是否是详情
      return (this.mode === '详情' || this.isAuditMode())
    },
    isAuditMode() { // 判断当前是否是审核
      return (this.mode === '审核')
    },
    initFormExtend(formExtendObj) {
      this.formExtend = formExtendObj
      if (this.formExtend) {
        // 是否存在扩展
        if (this.formExtend.initFormExtend) {
          this.formExtend.initFormExtend(this.meta.main.formType, this)
        }

        // 只有制单时才在画布顶部额外显示按钮
        this.formCanvasTopButtonExtendId = ''
        if (!this.isPrintDetails &&
            !this.isPreviewEdit &&
            this.formExtend.getFormCanvasTopButtonExtendId) {
          if (this.isFillFormMode() || this.isDetailMode()) {
            this.formCanvasTopButtonExtendId =
              this.formExtend.getFormCanvasTopButtonExtendId()
            this.$nextTick(() => {
              this.$refs.formCanvasTopButtonExtend.formFormat = this
              this.syncTopButtonExtend()
            })
          }
        }
      }
    },
    syncTopButtonExtend() {
      if (this.formExtend && this.$isNotEmpty(this.formCanvasTopButtonExtendId)) {
        this.$refs.formCanvasTopButtonExtend.syncTopButtonExtend()
      }
    },
    colItemsChange(colItems, isOpeningCanvas) { // 要素设置变化事件
      // 如果正在关闭操作弹框，可能会触发当前事件，此时不用重新加载，
      // 由参照弹框机制自行处理要素变化
      if (this.isClosingRefDlg) {
        this.isClosingRefDlg = false // 设置为true不加载时，只能使用一次
        return
      }

      // 这个方法只在规则格式设计时使用
      // 每次刷新设计画布，先暂存当前填写的要素值，
      // 然后将这次值填入到将要新建的画布内，实现刷新后要素值依然存在
      // var hasFormData = this.fillFormData(colItems)
      // var isNew = !hasFormData
      // this.createForm(colItems, isNew)
      this.createForm(colItems, isOpeningCanvas)
    },
    // 生成表单，isFillNewForm标识是否新增表单实例，不是表单
    createForm(colItems, isFillNewForm,
      isPrintDetails, exData = { overDialogZindex: null }) {
      // 是否弹出默认参照
      if (this.mode === '制单' && isFillNewForm) {
        this.defaultReference(colItems, exData)
      }
      this.exCreateFromData = exData
      this.overDialogZindex = exData.overDialogZindex
      this.isMultipleEditTabs = false
      if (exData.isMultipleEditTabs !== undefined) {
        this.isMultipleEditTabs = exData.isMultipleEditTabs
      }

      new Promise(re => {
        this.isPrintDetails = isPrintDetails
        // 制单,审核时初始化收款人
        if (this.isFillFormMode() || this.isAuditMode()) {
          this.$nextTick(() => {
            if (this.$parent.initExtDataAssembly) {
              this.$parent.initExtDataAssembly(this.meta, this.mode)
            }
          })
        }
        this.loadJson()
        this.$nextTick(re)
      }).then(re => {
        this.loadColItems(colItems, isFillNewForm)
        this.hideErrorAll()
        // 表单初始化后，提供给扩展操作的机会
        if (this.formExtend && this.formExtend.afterInit) {
          this.$nextTick(() => {
            this.formExtend.afterInit(this)
            if (exData && exData.callbackAfterFormLoaded) {
              exData.callbackAfterFormLoaded(this.dataVo)
            }
            this.loading = false
          })
        }
      })
    },
    // 合同变更 合同续签 兼容区块合同与项目口径管理 参照弹窗
    defaultReference(colItems, initFormExData) {
      var params = {}
      if (initFormExData?.baseListFormObj?.refFormParams === undefined) {
        return
      }
      if (initFormExData.baseListFormObj.refFormParams['是否弹出默认参照']) {
        const showDefaultReference = initFormExData.baseListFormObj.dataVo.cformSettings.filter(item => item.optionName === '默认参照设置')
        const cformSettings = showDefaultReference[0] || {}
        // 弹出默认参照
        if (cformSettings.extraData) {
          colItems.forEach(item => {
            if (item.label === cformSettings.extraData) {
              const refData = Object.assign(cformSettings, item)
              if (this.$isNotEmpty(this.meta.main)) {
                params['formId'] = this.meta.main.id
              }
              params = {
                ...params,
                ...initFormExData?.baseListFormObj?.refFormParams,
                colItemId: item.id,
                formType: this.meta.main.formType,
                doNotFillRelateColItem: this.isAuditMode, // 审核时修改参照要素，不要修改关联要素
                isRelatedRefIncludeDept: false,
                formExtendExData: {},
                updateParamsCallback: (obj) => {
                  obj.checkedData.push(item.realValueRefID.split(' ')[0])
                },
                multiple: !!item.isMultipleRef,
                onDlgClose: (params) => {
                  // 如果表单参照不进行参照，则直接退出制单界面返回到列表
                  if (params.closeWhetherToReturn === true && params.isOK !== true) {
                    this.$event(this, 'btEditBack', false)
                  }
                }
              }
              this.$nextTick(() => {
                if (this.formExtend) {
                  if (this.formExtend.isRelatedRefIncludeDept) {
                    params.isRelatedRefIncludeDept =
                      this.formExtend.isRelatedRefIncludeDept(item)
                  }
                  params.formExtendExData = this.formExtend.exData
                }
              })
              return this.$refData(colItems, refData, () => { }, this.setValue, () => {
              }, (value) => {
                this.initBlockView?.({
                  viewId: value.list[0].viewId,
                  versionId: value.list[0].metaVersionId,
                  billId: value.list[0].id,
                  formType: value.list[0].formType,
                  dataId: value.list[0].id,
                  isRef: true,
                  refFormSelectedData: value
                })
              }, params)
            }
          })
        }
      }
    },
    formatSaveCellCustomWidth(data) {
      if (Object.prototype.toString.call(data) === '[object Object]') {
        const obj = {}
        const value = Object.values(data)[0]
        const length = Object.keys(data).length
        obj[length] = value
        const res = [obj]
        return res
      } else {
        return {}
      }
    },
    formarGetCellCustomWidth(data) {
      if (Object.prototype.toString.call(data) === '[object Array]') {
        const length = Object.keys(data[0])[0] * 1
        const value = Object.values(data[0])[0]
        return new Array(length).fill(value).reduce((res, item, index) => { res[index] = item; return res }, {})
      } else {
        return data
      }
    },
    getFormatJson() {
      // // 删除表单实例里图片数据
      // TODO： 保存前删除实例图片数据
      // 避免视图无图片 删除提示The worksheet has no pictures to delete
      if (this.$isNotEmpty(window.luckysheet.getImageOption())) {
        window.luckysheet.deleteImage()
      }

      const data = this.$clone(window.luckysheet.getAllSheets()) // 表格数据
      const sheetData = JSON.parse(JSON.stringify(data[0].data))
      const columnlen = this.$isNotEmpty(data[0].config) && this.$isNotEmpty(data[0].config.columnlen) && JSON.parse(JSON.stringify(data[0].config.columnlen))
      data[0].data = this.formatCellData(sheetData)
      data[0].config.columnlen = this.formatObjToFilterColumnlen(columnlen)
      if (this.$isNotEmpty(data[0].config) && this.$isNotEmpty(data[0].config.customWidth)) {
        data[0].config.customWidth = this.formatSaveCellCustomWidth(data[0].config.customWidth)
      }
      return formatData().compressToEncodedURIComponent(JSON.stringify(data)) // 压缩数据
    },
    fillFormData(colItems) { // 获取表单当前填写的数据
      var hasFormData = false
      colItems.forEach(item => {
        // 只需要填充绑定到单元格的要素即可
        if (this.$isNotEmpty(item.rowIndex) &&
            this.$isNotEmpty(item.columnIndex) &&
            item.rowIndex >= 0 &&
            item.columnIndex >= 0) {
          var value = this.getValue(item.label)
          if (value instanceof Array) {
            value = value.join(',')
          }

          // 新增表单，业务编码设置了编码规则，并且是只读，
          // 此时保存前端不能传值，否则后端会校验不通过
          if (this.isNewFormBizCode(item)) {
            value = ''
          }

          // 针对报账单据处理多指标模式一中，类似处理cell值为指标金额外换行的情形
          if (!this.isDesignMode() &&
            this.formExtend &&
            this.formExtend.specialGetCellValue) {
            value = this.formExtend.specialGetCellValue(item, value)
          }
          if (item.colType === '金额' && value === 0) {
            value = '0.00'
          }
          item.dataValue = value || ''
          hasFormData = true
        }
      })
      return hasFormData
    },
    wrapXY(rowIndex, columnIndex) {
      return rowIndex + '_' + columnIndex
    },
    loadColItems(colItems, isFillNewForm) {
      // 组织要素哈希表，方便后续查询
      this.xyColItemMap = {}
      this.labelColItemMap = {}
      this.labelOriginColItemMap = {}
      this.specialColItemMap = {}
      this.specialColItems = []
      this.isFillNewForm = this.isFillFormMode() && isFillNewForm
      for (let i = 0; i < colItems.length; i++) {
        var item = colItems[i]
        item.style = ''
        var key = this.wrapXY(item.rowIndex, item.columnIndex)
        item.tooltipDisabled = true
        this.xyColItemMap[key] = item
        this.labelColItemMap[item.label] = item
        this.cacheXyArr.push(key)
        this.labelOriginColItemMap[item.labelOrigin] = item

        var isAuditCanEdit = this.$parent.isAuditCanEdit(item.label)
        item.auditCanEdit = isAuditCanEdit ? 'auditCanEdit' : ''

        if (this.isFillFormMode() ||
          this.isPrintDetails ||
          ((this.isDetailMode() || this.isAuditMode()) && item.colType === '公示') ||
          isAuditCanEdit) {
          if (this.isSpecialColItem(item.colType) && item.isEnabled !== '否') { // 制单时处理特殊要素
            this.specialColItemMap[key] = item
            this.specialColItems.push(item)

            if (this.isFillNewForm) { // 新填单，处理默认值
              if (this.$isNotEmpty(item.defaultValue)) {
                if (item.colType === '多选') {
                  item.checkBoxValues = item.defaultValue.split(',')
                } else if (item.colType === '日期范围') {
                  item.checkBoxValues = item.defaultValue.split(',').sort(function(a, b) {
                    return new Date(a) - new Date(b)
                  })
                } else { // 制单时不处理审核要素
                  item.dataValue = item.defaultValue
                }
              }
            }
          }
        }

        // 制单时需要对多对多模式的参照，设置不能从表单界面清除参照数据
        if (this.isFillFormMode() &&
          this.formExtend &&
          this.formExtend.exHandleRefParams &&
          this.$isNotEmpty(this.specialColItems)) {
          this.$nextTick(() => {
            this.specialColItems.forEach(item => {
              if (item.colType === '弹框') {
                var ps = {}
                this.formExtend.exHandleRefParams(ps, this, colItems, item)
                item.canClearRef = this.$isEmpty(ps['多对多模式']) || ps.canClearRef
              }
            })
          })
        }
      }

      if (this.createdAfter) { // 表单已经创建后，如果要素有变动，需要在此进行绑定
        colItems.forEach((item, index) => {
          const isRefresh = index >= colItems.length - 1
          this.bindColItem(item, isRefresh)
        })
      }
      this.formatRefItemMultipleValues()
    },
    formatRefItemMultipleValues() { // 将弹框多选值更换成换行模式
      this.$nextTick(() => {
        this.specialColItems.forEach(item => {
          if (item.colType === '弹框') {
            this.setValue(item.labelOrigin, item.dataValue)
          }
        })
      })
    },
    loadJson(excelData) {
      // 更新sheet
      // 设计模式时，只有手动点击树节点打开和导入excel时，才会加载格式
      if (this.isDesignMode()) {
        if (this.meta.needToReloadFreeJson !== true && this.$isEmpty(excelData)) {
          return
        }
      }
      if (this.$isNotEmpty(excelData)) { // 导入时的处理
        this.options.data = excelData
        this.isImportingExcel = true
      } else {
        var json = this.meta.instance.formatJson
        json = formatData().decompressFromEncodedURIComponent(json) // 解压
        this.options.data = JSON.parse(json)

        if (this.$isEmpty(json)) {
          if (this.meta.colItems.length) {
            this.options.data = window.luckysheet.getAllSheets()
          } else {
            this.options.data = this.defaultSheetData
            this.options.data[0].data = []
          }
        }
      }
      if (this.isDesignMode()) { // 设计时显示工具栏和对准线
        this.options.container = 'formFree'
        this.options.showtoolbar = true
        this.options.rowHeaderWidth = null
        this.options.showtoolbarConfig = this.defaultToolBarConfig
        this.options.columnHeaderHeight = null
        this.options.cellRightClickConfig.insertRow = true
        this.options.cellRightClickConfig.insertColumn = true
        this.options.cellRightClickConfig.deleteRow = true
        this.options.cellRightClickConfig.deleteColumn = true
      } else if (this.isFillFormMode() || this.isDetailMode()) {
        if (this.options.container !== 'formFree') {
          // 配置容器名称
          this.options.container = 'formFreeFill'
        }
        // 隐藏工具栏
        this.options.showtoolbar = false
        delete this.options.showtoolbarConfig

        this.options.rowHeaderWidth = 1
        this.options.columnHeaderHeight = 0
        // 取消删除、插入行和列操作
        this.options.cellRightClickConfig.insertRow = false
        this.options.cellRightClickConfig.insertColumn = false
        this.options.cellRightClickConfig.deleteRow = false
        this.options.cellRightClickConfig.deleteColumn = false
        if (this.isAuditMode()) {
          this.options.container = 'formFreeAuditDetail'
        } else if (this.isDetailMode()) {
          this.options.container = this.isPrintDetails ? 'formDetailPrint' : 'formFreeDetail'
        } else if (this.isFillFormMode()) {
          this.options.container = 'formFreeFill'
        }
      }
      if (!this.isDesignMode()) {
        this.options.data[0].data = this.formatCellData(this.options.data[0].data)
      } else {
        this.options.data[0].data = []
      }
      if (this.options.data[0].config && this.options.data[0].config.customWidth) {
        this.options.data[0].config.customWidth = this.formarGetCellCustomWidth(this.options.data[0].config.customWidth)
      }
      if (this.options.data[0].scrollTop) {
        this.options.data[0].scrollTop = 0
      }
      window.luckysheet.destroy()
      this.createdAfter = false
      this.$nextTick(() => {
        // 保存单元格边框和合并信息
        this.borderInfo = this.$clone((this.options.data[0].config?.borderInfo) || [])
        this.mergeInfo = this.options.data[0].config?.merge || {}
        this.autoHeightCellMap = {}
        window.luckysheet.create(this.options)
        if (this.isActivated) {
          this.isActivated = false
          window.luckysheet.create(this.options)
        }
        $('#luckysheet-wa-editor >div:first-child').css('cssText', 'flex:none !important')
        $('#luckysheet-pivot-btn-title,#toolbar-separator-pivot-table,#toolbar-separator-text-rotate-mode,#luckysheet-zoom-content').css('display', 'none')
        this.formFreeClientWidth = this.isDetailMode() ? $('#formFreeDetail').width() : $('#formFree').width()
        this.formFreeClientHeight = this.isDetailMode() ? $('#formFreeDetail').height() : $('#formFree').height()
        $('#luckysheet-selection-copy').hide()
        if (this.mode === '审核') {
          this.formFreeClientWidth = $('#formFreeAuditDetail').width()
          this.formFreeClientHeight = $('#formFreeAuditDetail').height()
        }
        this.sheetClientWidth = $('#luckysheet-sheettable_0').width() - 108 // 108为luckysheet的canvas偏移量
        this.sheetclientHeight = $('#luckysheet-sheettable_0').height()
      })
      if (this.isDetailMode() || this.isFillFormMode()) {
        this.$nextTick(() => {
          window.luckysheet.hideGridLines() // 隐藏网格线
          // // 隐藏选中样式
          $('#luckysheet-cell-selected').addClass('luckysheet-cell-selected-immutable')
          $('#luckysheet-cell-selected-focus').css('background', 'none')
          $('#luckysheet-cell-main').css('background', '#ffffff')
          $('#luckysheet-row-count-show,#luckysheet-column-count-show').addClass('hide-luckysheet-row-column-count')
          $('#luckysheet-rows-h-selected,#luckysheet-cols-h-selected').addClass('luckysheet-rows-h-selected-flag')
          setTimeout(() => {
            this.formFreeClientWidth = this.isDetailMode() ? $('#formFreeDetail').width() : $('#formFree').width()
            this.formFreeClientHeight = this.isDetailMode() ? $('#formFreeDetail').height() : $('#formFree').height()
            this.sheetClientWidth <= this.formFreeClientWidth ? $('#luckysheet-scrollbar-x').addClass('luckysheet-hidden-xhide') : $('#luckysheet-scrollbar-x').addClass('luckysheet-hidden-xscroll')
            // this.sheetclientHeight <= this.formFreeClientHeight ? $('#luckysheet-scrollbar-y').addClass('luckysheet-hidden-yhide') : $('#luckysheet-scrollbar-y').addClass('luckysheet-hidden-yscroll')
            // $('#luckysheet-scrollbar-y').addClass('luckysheet-hidden-yscroll')
            // $('#luckysheet-scrollbar-x').addClass('luckysheet-hidden-xscroll') 可能存在别的场景存在这种隐藏方式
          }, 0)
        })
      }
      if (this.mode === '审核' || this.isFillFormMode()) {
        this.$nextTick(() => {
          $('.luckysheet-grid-container').css({ 'border-right': '1px solid #bbb', 'border-bottom': '1px solid #bbb' })
          $('.luckysheet-grid-container').css({ 'border-left': '1px solid #eee', 'border-top': '1px solid #eee' })
          $('#luckysheet-row-count-show,#luckysheet-column-count-show').addClass('hide-luckysheet-row-column-count')
          this.sheetClientWidth <= this.formFreeClientWidth ? $('#luckysheet-scrollbar-x').addClass('luckysheet-hidden-xhide') : $('#luckysheet-scrollbar-x').addClass('luckysheet-hidden-xscroll')
          this.sheetclientHeight <= this.formFreeClientHeight ? $('#luckysheet-scrollbar-y').addClass('luckysheet-hidden-yhide') : $('#luckysheet-scrollbar-y').addClass('luckysheet-hidden-yscroll')
        })
      }
    },
    workbookCreateAfter(json) {
      Object.keys(this.xyColItemMap).forEach((key, index) => {
        const item = this.xyColItemMap[key]
        const isRefresh = index >= Object.keys(this.xyColItemMap).length - 1
        if (item.defaultValue === '审核签章') {
          item.showSignature = false
        }
        this.bindColItem(item, isRefresh)
        // this.bindSignature(item)
      })
      this.meta.needToReloadFreeJson = undefined
      this.createdAfter = true
      this.isFillNewForm = false
      if (this.isImportingExcel) {
        this.isImportingExcel = false
        this.$nextTick(() => {
          this.$parent.$refs.colSetting.saveMeta(false, result => {
            return true
          }, () => {}, { isImportingExcel: true })
        })
      }
      window.luckysheet.setRangeShow('A1', { show: false })
      // 图片相关处理
      // this.formDisposeImg()
      // 优化luckysheet 内部 150 问题
      setTimeout(() => {
        this.$nextTick(() => {
          $('#luckysheet-scrollbar-x').scrollLeft(0)
        })
      }, 150)
      this.bindSignature()
      this.insertImage()
      this.insertDynamic()
      this.setPostil(this.exData.remindInfo)
      this.emitComposeCellPosition()
      this.loading = false
    },
    updataColItemRemind(colItem) {
      if (!this.isFillFormMode() || !colItem) { // 只有在制单的时候显示
        return
      }
      const labelValues = colItem.labelValues || []
      let select = {}
      for (let i = 0; i < labelValues.length; i++) {
        const option = labelValues[i]
        if (option.value === colItem.dataValue) {
          select = option
          break
        }
      }
      if (this.$isEmpty(select.remindInfo)) {
        return
      }

      const remindInfo = {}
      remindInfo[select.remindInfo.formFieldName] = select.remindInfo.remindMessage
      this.setPostil(remindInfo)
    },
    setPostil(remindInfo = {}) {
      if (!this.isFillFormMode()) { // 只有在制单的时候显示
        return
      }
      const findConfig = {
        isWholeWord: true,
        isCaseSensitive: true
      }

      const remindKeys = Object.keys(remindInfo)
      remindKeys.forEach((item, itemIndex) => {
        const remindCells = window.luckysheet.find(item, findConfig)
        const postilConfig = {
          ps: {
            left: null,
            top: null,
            width: null,
            height: null,
            value: remindInfo[item],
            isshow: false
          }
        }
        remindCells.forEach((cell, cellIndex) => {
          const isRefresh = itemIndex === (remindKeys.length - 1) && cellIndex === (remindCells.length - 1)
          window.luckysheet.setCellValue(
            cell.row, cell.column, postilConfig, { isRefresh }
          )
        })
      })
    },
    refreshSignature() {
      const delList = []
      Object.keys(this.signatureImage).forEach(item => {
        delList.push(this.signatureImage[item])
      })
      if (delList.length && this.$isNotEmpty(window.luckysheet.getImageOption())) {
        window.luckysheet.deleteImage(delList)
      }
      this.$nextTick(() => {
        this.bindSignature()
      })
    },
    async bindSignature() {
      const _this = this
      this.signatureImage = {}
      const haveSignatureObj = {}
      const haveSignatureSetting = this.cformSettings && this.cformSettings.filter(item => item.optionName === '设置签章')
      if (this.$isNotEmpty(haveSignatureSetting)) {
        if (haveSignatureSetting[0].extraData) {
          haveSignatureSetting[0].extraData.split(';').forEach(item => {
            if (item.includes('width') && item.split(':')[1] !== '') {
              haveSignatureObj.width = Number(item.split(':')[1])
            }
            if (item.includes('height') && item.split(':')[1] !== '') {
              haveSignatureObj.height = Number(item.split(':')[1])
            } else {
              haveSignatureObj.height = 40
            }
          })
        }
      } else {
        haveSignatureObj.height = 40
      }
      const promises = Object.values(this.xyColItemMap).map(item => {
        if (item.defaultValue === '审核签章') {
          return new Promise(re => {
            if (this.$isNotEmpty(item.dataValue)) {
              var isBase64 = this.isBase64(item.dataValue)
              if (isBase64) {
                downloadFile(item.dataValue).then((res) => {
                  var reader = new FileReader()
                  reader.readAsDataURL(res.data)
                  reader.onload = function() {
                    var base64 = reader.result
                    item.dataValue = base64
                    window.luckysheet.insertImageDIY(item.dataValue, { isDisabled: true,
                      beforeInsert: (option) => { _this.signatureImageInit(option, item, haveSignatureObj) },
                      success: (id) => {
                        _this.signatureImage[item.labelOrigin] = id
                        re()
                      }
                    })
                  }
                })
              } else {
                window.luckysheet.insertImageDIY(item.dataValue, { isDisabled: true,
                  beforeInsert: (option) => { _this.signatureImageInit(option, item, haveSignatureObj) },
                  success: (id) => {
                    _this.signatureImage[item.labelOrigin] = id
                    re()
                  }
                })
              }
            }
          })
        }
      })
      await Promise.all(promises)

      setTimeout(() => {
        this.showSignature()
      }, 200)
    },
    isBase64(str) {
      if (str === '' || str.trim() === '') {
        return false
      }
      try {
        return btoa(atob(str)) === str
      } catch (err) {
        return false
      }
    },
    isShowSignature(e, colItem) {
      Object.keys(this.signatureImage).forEach(item => {
        if (item === colItem.labelOrigin && !(typeof (item) === 'undefined')) {
          if (e) {
            $(`#${this.signatureImage[item]}`).css('display', 'block')
            this.setCellValue(colItem, '')
          } else {
            $(`#${this.signatureImage[item]}`).css('display', 'none')
            this.setCellValue(colItem, `#{${colItem.labelOrigin}}`)
          }
          $('#luckysheet-image-showBoxs').off('mousedown.active')
          $(`#${this.signatureImage[item]}`).off('mousedown.active')
        }
      })
    },
    signatureImageInit(option, item, setting) {
      if (setting && typeof setting === 'object') {
        Object.keys(setting).forEach(item => {
          option[item] = setting[item]
        })
      }
      if (!setting.width) {
        option.width = option.height / option.originHeight * option.originWidth
      }
      // 等比缩放图片宽高
      const sheetData = window.luckysheet.getAllSheets()[0]
      const imgHeight = option.height / 2
      const imageWidth = option.width / 2
      const sheetLocs = this.isMcCell(sheetData, item, { imgHeight, imageWidth })
      option.top = sheetLocs.top ? sheetLocs.top : option.top
      option.left = sheetLocs.left ? sheetLocs.left : option.left
      return option
    },
    isMcCell(sheetData, item, options) {
      let celldata = null
      sheetData.celldata.forEach(cd => {
        if (cd.r === item.rowIndex && cd.c === item.columnIndex) {
          celldata = cd.v
        }
      })
      if (!celldata.mc) {
        return { top: sheetData.visibledatarow[item.rowIndex - 1] + (sheetData.config.rowlen[item.rowIndex] / 2) - options.imgHeight,
          left: sheetData.visibledatacolumn[item.columnIndex - 1] + (sheetData.config.columnlen[item.columnIndex] / 2) - options.imageWidth }
      } else {
        const { r, c, rs, cs } = celldata.mc
        // 获取主单元格前宽高
        const bfCellHeight = sheetData.visibledatarow[r - 1]
        const bfCellWidth = sheetData.visibledatacolumn[c - 1]
        // 判断行列是否合并
        const mcCellHeight = sheetData.visibledatarow[r + rs - 1]
        const mcCellWidth = sheetData.visibledatacolumn[c + cs - 1]
        // 获取合并单元格居中位置
        const cellWidthCenter = (mcCellWidth - bfCellWidth) / 2
        const cellHeightCenter = (mcCellHeight - bfCellHeight) / 2
        const left = bfCellWidth + cellWidthCenter - options.imageWidth
        const top = bfCellHeight + cellHeightCenter - options.imgHeight
        return { top, left }
      }
    },
    removeColItemAll() {
      // 导入的时候移除绑定过要素列表，由于导入后会执行保存，所以这里移除操作不需要保存
      const ids = this.meta.colItems.map(item => item.id)
      this.$call(this.$parent,
        'colSetting', 'doOutList', ids, true)
    },
    importSheet(file, fileDomExcel) { // 导入
      const that = this
      const files = file.raw
      if (files == null || files.length === 0) {
        this.$message.error('导入文件为空')
        return
      }
      const name = files.name
      const suffixArr = name.split('.')
      const suffix = suffixArr[suffixArr.length - 1]
      let sizeMax = 0
      if (file.size) {
        sizeMax = file.size / 1024 // kb单位
      }
      if (suffix !== 'xlsx') {
        that.$message.error('只支持xlsx格式!')
        fileDomExcel.clearFiles()
        return
      } else if (sizeMax > 50) {
        that.$message.error('导入文件大小不能超过50KB')
        fileDomExcel.clearFiles()
        return
      }
      this.removeColItemAll()
      this.$nextTick(() => {
        LuckyExcel.transformExcelToLucky(files, exportJson => {
          if (exportJson.sheets == null || exportJson.sheets.length === 0) {
            that.$message.error('格式错误,请从Excel转化为xlsx格式后再导入!')
            fileDomExcel.clearFiles()
            return
          }
          let sheets = {}
          if (exportJson.sheets.length === 1) {
            sheets = exportJson.sheets
          } else { // 多个页签工作表时取第一个
            sheets = [exportJson.sheets[0]]
          }
          window.luckysheet.destroy() // 合并覆盖原来初始化的工作簿数据
          that.loadJson(sheets)
          fileDomExcel.clearFiles()
        })
      })
      fileDomExcel.clearFiles()
    },
    setEditingColItem(colLabel) { // 设置当前编辑的要素设置
      this.$call(this.$parent,
        'colSetting', 'setEditingColItem', colLabel)
    },
    refLabelValues(colItem) {
      if (this.isFillFormMode() || this.$parent.isAuditCanEdit(colItem.label)) {
        const params = {
          dataRef: colItem.dataRef,
          colItemId: colItem.id
        }
        if (this.$isNotEmpty(this.meta.main)) {
          params['formId'] = this.meta.main.id
          params['cformMeta'] = this.dataVo.meta
        }
        const formExtend = this.formExtend
        if (formExtend) {
          formExtend.setExParams?.(params, colItem, this.dataVo)
        }
        this.$refLabelValues(params, colItem)
      }
    },
    refPublicInfos(colItem) {
      var isEdit = (this.isFillFormMode() || this.$parent.isAuditCanEdit(colItem.label))
      this.$refPublicInfos(this, colItem, this.isDesignMode(), isEdit)
    },
    preventReloadForm() {
      // 防止重载表单出现的闪动
      // isClosingRefDlg字段主要是为了防止参照设置值时，表单会重新加载导致屏幕闪动
      this.isClosingRefDlg = true
    },
    isNumberColItem(colItem) {
      return (colItem.labelOrigin === '金额' ||
          colItem.labelOrigin === '整数' ||
          colItem.labelOrigin === '小数' ||
          colItem.labelOrigin === '百分比')
    },
    clearRefData(colItem, notFireColItemModified) { // 清除参照数据
      if (this.$isEmpty(colItem)) {
        return
      }
      if (this.formExtend.clearRefDataEx) {
        this.formExtend.clearRefDataEx(colItem, this)
      }
      var refIdItem = this.labelOriginColItemMap[colItem.labelOrigin + 'ID']
      if (refIdItem && this.isBlankColItemDataVlue) {
        refIdItem.dataValue = ''
      }
      if (this.isBlankColItemDataVlue) {
        colItem.dataValue = ''
      }

      this.setCellValue(colItem, '')
      var $colItemDom = $('#colItem' + colItem.id)
      $colItemDom.html('')

      if (notFireColItemModified === true) {
        return
      }
      this.colItemModified(colItem)
      this.refreshAttach()
    },
    switchRefEnabled(labelOrigin, enabled) {
      var colItem = this.labelOriginColItemMap[labelOrigin]
      if (this.$isNotEmpty(colItem) && colItem.colType === '弹框') {
        var $refItem = $(`#colItem${colItem.id}`)
        var $refParentDiv = $refItem.parent()
        if (enabled) {
          $refParentDiv.find('span.el-input__prefix').show()
          $refItem.css('cursor', 'pointer')
          $refItem.removeClass('refDisabled')
        } else {
          $refParentDiv.find('span.el-input__prefix').hide()
          $refItem.css('cursor', 'default')
          $refItem.addClass('refDisabled')
        }
      }
    },
    isCancelRefAction(colItem) {
      var $refItem = $(`#colItem${colItem.id}`)
      return ($refItem.hasClass('refDisabled'))
    },
    changeFontSize() {
      // 修改lucky字体方法
      this.options.data[0].celldata.forEach(item => {
        if (item && item.v && item.v.fs) {
          item.v.fs = Number(item.v.fs) + 3
        } else {
          item.v.fs = 14
        }
        if (item && item.v && item.v.ct && item.v.ct.s && item.v.ct.s.length) {
          item.v.ct.s.forEach(s => {
            if (s && s.fs) {
              s.fs = Number(s.fs) + 3
            } else {
              s.fs = 14
            }
          })
        }
      })
      this.options.data[0].data.forEach(item => {
        if (Array.isArray(item) && item.length) {
          item.forEach(child => {
            if (child && child.fs && !child.check) {
              child.fs = Number(child.fs) + 3
              child.check = true
            } else if (child && !child.fs && !child.check) {
              child.fs = 14
              child.check = true
            }

            if (child && child.ct && child.ct.s && child.ct.s.length) {
              child.ct.s.forEach(s => {
                if (s.fs && !s.check) {
                  s.fs = Number(s.fs) + 3
                  s.check = true
                } else if (!s.fs && !s.check) {
                  s.fs = 14
                  s.check = true
                }
              })
            }
          })
        }
      })
    },
    isDisabled(colItem) {
      if (this.isFillFormMode() || this.$parent.isAuditCanEdit(colItem.label)) {
        if ((colItem.modifyType || '').includes('只读')) { // 是否动态终止当前参照动作
          return true
        }
      }
    },
    refData(colItem) {
      if (this.isFillFormMode() || this.$parent.isAuditCanEdit(colItem.label)) { // 只有制单时响应参照事件
        if (this.isCancelRefAction(colItem)) { // 是否动态终止当前参照动作
          return
        }
        if ((colItem.modifyType || '').includes('只读')) {
          return
        }

        var colItems = Object.values(this.labelColItemMap)

        // 参照选中之后是否需要做额外的控制，比如多指标参数的场景
        // 选中指标之后，需要对每个指标填写申请金额
        var params = {}
        params.isMultipleChoice = []
        params.formType = this.meta.main.formType
        if (this.formExtend && this.formExtend.callbackBeforeRefComplete) {
          params.closeDlgByOutside = true
          params.callbackBeforeRefComplete =
            (selectedData, params, callbackCloseRefDlg, setBtnUnLoad) => {
              this.formExtend.callbackBeforeRefComplete(
                this, colItem, selectedData, params, callbackCloseRefDlg, setBtnUnLoad)
            }
        }

        // 打开参照之前的回调
        if (this.formExtend && this.formExtend.callbackBeforeOpenRef) {
          params.callbackBeforeOpenRef =
            (callbackIsOpenRef) => {
              this.formExtend.callbackBeforeOpenRef(
                this, colItem, callbackIsOpenRef)
            }
        }

        if (this.formExtend && this.formExtend.handleRefCheckedData) { // 处理选中数据
          params.handleRefCheckedData = (params, colItem, funGetValue) => {
            return this.formExtend.handleRefCheckedData(params, colItem, funGetValue)
          }
        }

        if (this.formExtend && this.formExtend.isRefMultiple) { // 参照是否可以多选
          params.multiple = this.formExtend.isRefMultiple(colItem, this)
          if (params.multiple) {
            params.isMultipleChoice.push('选择部门指标')
          }
        }

        if (this.formExtend && this.formExtend.isMoreAdvance) { // 事前参照是否可以多选
          var isMoreAdvance = this.formExtend.isMoreAdvance(colItem, this)
          if (isMoreAdvance) {
            params.isMultipleChoice.push('选择事前')
          }
        }

        if (this.formExtend && this.formExtend.isRefNoCode) { // 参照结果是否不含编码
          params.isRefNoCode = (labelOrigin, dataRef) => {
            return this.formExtend.isRefNoCode(labelOrigin, this, dataRef)
          }
        }

        // 联动的参照项特别处理Label。比如明细多指标模式，“指标2”会联动参照得到
        // “经济分类”和“功能分类”的值，此时需要处理对应到“经济分类2”和“功能分类2”
        if (this.formExtend && this.formExtend.wrapRefRelateItemLabel) {
          params.wrapRefRelateItemLabel = this.formExtend.wrapRefRelateItemLabel
        }

        // 需要针对特殊的参照填充特定的参数，比如合同参照的扩展参照内容机制
        if (this.formExtend && this.formExtend.exhandleRefDataParams) {
          this.formExtend.exhandleRefDataParams(params, colItem, this)
        }

        params.formExtendExData = {}
        if (this.formExtend) {
          params.formExtendExData = this.formExtend.exData
        }

        if (this.formExtend && this.formExtend.exHandleRefParams) {
          this.formExtend.exHandleRefParams(params, this, colItems, colItem)
        }

        if (this.formExtend && this.formExtend.ignoreRefItem) {
          params.ignoreRefItem = this.formExtend.ignoreRefItem
        }

        params.doNotFillRelateColItem = this.isAuditMode() // 审核时修改参照要素，不要修改关联要素
        params['colItemId'] = colItem.id
        if (this.$isNotEmpty(this.meta.main)) {
          params['formId'] = this.meta.main.id
        }
        this.$refData(colItems, colItem, this.getValue, this.setValue,
          selectedData => {
            this.preventReloadForm()
          }, selectedData => {
            // this.showTooltip(colItem, '传过来的内容')
            this.specialColItemModified(colItem)
            this.refreshAttach()
            const list = selectedData.list
            if (this.$isNotEmpty(list) && this.$isNotEmpty(list[0].remindInfo)) {
              const remindInfo = {}
              remindInfo[list[0].remindInfo.formFieldName] = list[0].remindInfo.remindMessage
              this.setPostil(remindInfo)
            }
          }, params)
      }
    },
    setValue(label, value, noWarn, notSetValue) { // 这个方法只是提供给参照使用
      var colItem = this.labelColItemMap[label]
      if (this.$isEmpty(colItem)) {
        if (noWarn !== true) {
          console.warn('setValue时找不到对应的要素：' + label)
        }
        return
      }

      colItem.dataValue = value
      if (this.isSpecialColItem(colItem.colType)) {
        if (colItem.colType === '弹框') {
          // 弹框多选的显示处理
          var $colItemDom = $('#colItem' + colItem.id)
          $colItemDom.removeClass('ref-rows2')
          $colItemDom.removeClass('ref-rows3')
          $colItemDom.removeClass('ref-rows4')

          if (value && value.indexOf(',') > -1) { // 弹框多选的处理
            var valueTokens = value.split(',')
            var targetClass = 'ref-rows' + valueTokens.length
            if (valueTokens.length >= 4) {
              targetClass = 'ref-rows4'
            }
            $colItemDom.addClass(targetClass)

            value = value.replace(/,/g, '<br/>')
          }
          this.$nextTick(() => { $colItemDom.html(value) })
        }
      } else if (!colItem.isAutoAdd) {
        this.setCellValue(colItem, value, undefined, notSetValue)
      }
    },
    getValue(label, isHideItem) {
      // 不是自动添加要素，不是特殊要素，那值就存在表单上
      var colItem = this.labelColItemMap[label]
      if (this.$isEmpty(colItem)) {
        this.$message.error('getValue时找不到对应的要素：' + label)
        return
      }

      // 特殊要素取值：这些都存储在要素的dataValue字段：
      // 弹框通过setValue设置，其他特殊要素通过双向绑定设置
      if (this.isSpecialColItem(colItem.colType)) {
        const isCheckbox = colItem.colType === '多选'
        const isDateRang = colItem.colType === '日期范围'
        if (isCheckbox || isDateRang) { // 需要按顺序排序勾选的值
          var values = []
          var valuesRaw = colItem.checkBoxValues
          if (this.$isNotEmpty(valuesRaw) && isCheckbox) {
            colItem.labelValues.forEach(it => {
              if (valuesRaw.includes(it.label)) {
                values.push(it.label)
              }
            })
          } else if (this.$isNotEmpty(valuesRaw) && isDateRang) {
            values = valuesRaw
          }

          return values
        }
        return colItem.dataValue
      } else if (!colItem.isAutoAdd) { // luckysheet要素取值
        let cellValue = window.luckysheet.getCellValue(
          colItem.rowIndex, colItem.columnIndex)
        if (colItem.colType === '金额' && cellValue) {
          cellValue = this.$fixMoney(cellValue)
        }
        return cellValue
      } else if (isHideItem) {
        return colItem.dataValue
      }
      return ''
    },
    setCellValue(colItem, value, isRefresh, notSetValue = false) {
      if (colItem.isAutoAdd) { // 只能设置绑定了单元格的要素
        return
      }
      // 主要针对场景是：指标金额要素的设计类型是文本(255)，
      // 导致电子表格不能应用金额格式，需要使用扩展功能特殊修正类型
      let colType = colItem.colType
      if (this.formExtend && this.formExtend.wrapColType) {
        colType = this.formExtend.wrapColType(colItem)
      }

      // 设置小数点位数，金额要素如果小数位数是0，则认为是遗留的错误数据
      // 这个情况下纠正设置小数位数是2
      if (colItem.isNumber && this.$isNumber(value)) {
        let floatDigits = this.$isNumber(colItem.digits) ? colItem.digits : 0
        if (colType === '金额' && floatDigits === 0) {
          floatDigits = 2
        }
        value = parseFloat(value).toFixed(floatDigits)
      }

      // const currentCellff = window.luckysheet.getCellValue(colItem.rowIndex, colItem.columnIndex, { type: 'ff' })
      // const currentCellfs = window.luckysheet.getCellValue(colItem.rowIndex, colItem.columnIndex, { type: 'fs' })
      let ct = { fa: '@', t: 's' }
      let fc = this.isDesignMode() ? `#000` : undefined // 黑色
      if (this.$parent.isAuditCanEdit(colItem.label)) {
        fc = '#2fbefc'
      }

      // eslint-disable-next-line no-unused-vars
      const ff = this.isDesignMode() ? 5 : undefined // 宋体
      // eslint-disable-next-line no-unused-vars
      let fs = this.isDesignMode() ? 11 : undefined // 字体大小
      let vt = this.isDesignMode() ? undefined : 0 // 垂直对齐 0 中间、1 上、2下
      const checkBoxValuesLength = colItem.checkBoxValues.length

      if (colType === '日期') {
        let formatStr = 'yyyy-MM-dd'
        if (colItem.defaultValue === '#{当前日期(年月日)}') {
          formatStr = 'yyyy年MM月dd日'
        }
        ct = { t: 'd', fa: formatStr }
      } else if (colType === '百分比') {
        ct = { t: 'n', fa: '' }
        if (colItem.digits <= 2) {
          ct.fa = '0%'
        } else if (colItem.digits === 3) {
          ct.fa = '0.0%'
        } else if (colItem.digits >= 4) {
          ct.fa = '0.00%'
        }
      } else if (colType === '金额') {
        ct = { t: 'n', fa: '#,##0.00_);(#,##0.00)' }
        value = value ? value + ' ' : value // 金额后面加空格
        notSetValue = true // 金额不设置m值，确保千分位
      } else if (colType === '弹框') {
        // value = value ? value.replaceAll(',', '\r\n') : ' '
        value = value ? value.replace(/,/g, '\r\n') : ' '
        vt = 0
        if (checkBoxValuesLength === 2) {
          fs = 11
        } else if (checkBoxValuesLength === 3) {
          fs = 9; vt = 0
        } else if (checkBoxValuesLength === 4) {
          fs = 8; vt = 1; fc = '#000'
        }
        if (!this.isDesignMode()) {
          ct = { fa: 'General', t: 'inlineStr', s: [{ 'v': value }] }
        }
      } else if (colType === '小数') {
        if (colItem.digits === 0) {
          ct.fa = '0'
        } else if (colItem.digits === 1) {
          ct.fa = '0.0'
        } else if (colItem.digits >= 2) {
          ct.fa = '0.00'
        }
      }
      // 针对报账单据处理多指标模式一中，类似处理cell值为指标金额外换行的情形
      if (!this.isDesignMode() &&
        this.formExtend &&
        this.formExtend.specialSetCellValue) {
        this.formExtend.specialSetCellValue(colItem, value, ct)
      }
      if (!this.isDesignMode() &&
        this.formExtend &&
        this.formExtend.setModeCellLineStr) {
        this.formExtend.setModeCellLineStr(colItem, value, ct)
      }
      // 处理法律意见书要素换行
      if (!this.isDesignMode() &&
        this.formExtend &&
        this.formExtend.legalOpinionSetValue) {
        this.formExtend.legalOpinionSetValue(colItem, value, ct)
      }

      if (!this.isDesignMode() &&
        this.formExtend &&
        this.formExtend.setlegalOpinion) {
        this.formExtend.setlegalOpinion(colItem, value, ct)
      }

      if (this.$isEmpty(value)) { // 防止value是undefined
        value = ''
      }

      // 原始值要去空格否则输入状态时要先去空格，避免数字出现10.2 6
      var mv = value
      if (typeof (mv) === 'string') {
        mv = mv.trim()
      }

      // v原始值(getCellValue取的值为v) m显示值
      // var cellConfig = { v: mv, m: value, fc: fc, ct: ct, ff: ff, fs: fs, tb: 2, vt: vt }
      var cellConfig = notSetValue ? { v: mv, fc: fc, ct: ct, tb: 2, vt: vt } : { v: mv, m: value, fc: fc, ct: ct, tb: 2, vt: vt }
      cellConfig['qp'] = undefined
      // this.isManuallySetValue = true
      if (mv !== window.luckysheet.getCellValue(colItem.rowIndex, colItem.columnIndex)) {
        window.luckysheet.setCellValue(
          colItem.rowIndex, colItem.columnIndex, cellConfig, { isRefresh }
        )
      }
      this.setHeightAuto(colItem, value)
    },
    setHeightAuto(colItem, value = '') {
      if (colItem.isAdaptiveRowHeight && !this.isDesignMode() && value.length) {
        // rowIndex  columnIndex
        const obj = {}
        obj[colItem.rowIndex] = 'auto'
        window.luckysheet.setRowHeight(obj)
      }
    },
    rangeSelect(sheet, range) { // 主要记录当前选中的单元格
      const selectCellInfo = sheet.luckysheet_select_save[0]
      const selectFlag = `${selectCellInfo.row[0]}_${selectCellInfo.column[0]}`
      const { merge = {}} = sheet.config
      const isMerge = Object.keys(merge).join().includes(selectFlag) // 判断有没有合并
      const columnFlag = selectCellInfo.column[0] === selectCellInfo.column[1]
      const rowFlag = selectCellInfo.row[0] === selectCellInfo.row[1]
      const isSimpleSelect = columnFlag && rowFlag // 判断是不是单个单元格
      this.cellPosition = {
        ...selectCellInfo, isMerge, isSimpleSelect
      }

      if (this.isDesignMode()) {
        // 处理单元格与要素联动，单元格有对应要素则勾选
        // 没有则清楚原来可能存在的勾选
        // this.selectSignature(range)
        this.setEditingColItem(this.getEditingLabel())
      } else if (this.isFillFormMode() || this.isAuditMode()) {
        // 以下处理制单或审核时是否有高亮选中
        var colItem = this.getEditingItem()
        this.updateEditFocusReadOnly(colItem)
      }
    },
    selectSignature(range) {
      const cellValue = window.luckysheet.getCellValue(range[0].row_focus, range[0].column_focus)
      Object.keys(this.signatureImage).forEach(item => {
        if (`#{${item}}` === cellValue) {
          const displayStyle = $(`#${this.signatureImage[item]}`)[0].style.display
          if (displayStyle === '' || displayStyle === 'block') {
            this.$parent.$refs.colSetting.showSignature = true
          } else {
            this.$parent.$refs.colSetting.showSignature = false
          }
        } else if (cellValue === '') {
          this.$parent.$refs.colSetting.showSignature = true
        }
      })
    },
    cellMousedown(cell, position, sheet, ctx) {
      this.fixSelectedFocusPositionError()
    },
    fixSelectedFocusPositionError() {
      if (this.isFillFormMode()) { // 制单时调整单元格高亮位置大小
        this.$nextTick(() => {
          const $cellSelectedDom = $('#luckysheet-cell-selected')
          // const $cellSelectedDomHeight = $cellSelectedDom.height() - 2
          // const $cellSelectedDomWidth = $cellSelectedDom.width() - 2
          // const $cellSelectedDomTop = $cellSelectedDom.position().top - 2
          const $cellSelectedDomTransform = 'translateY(-2px)'

          // $cellSelectedDom.css('height', $cellSelectedDomHeight)
          // $cellSelectedDom.css('width', $cellSelectedDomWidth)
          // $cellSelectedDom.css('top', $cellSelectedDomTop)
          $cellSelectedDom.css('transform', $cellSelectedDomTransform)
        })
        // TODO: 制单时调整单元格高亮位置大小(需优化定时器)
        // setTimeout(() => {
        //   this.$nextTick(() => {
        //     const $cellSelectedDom = $('#luckysheet-cell-selected')
        //     const $cellSelectedDomHeight = $cellSelectedDom.height() - 2
        //     const $cellSelectedDomWidth = $cellSelectedDom.width() - 2
        //     const $cellSelectedDomTop = $cellSelectedDom.position().top - 2.5

        //     $cellSelectedDom.css('height', $cellSelectedDomHeight)
        //     $cellSelectedDom.css('width', $cellSelectedDomWidth)
        //     $cellSelectedDom.css('top', $cellSelectedDomTop)
        //   })
        // }, 100)
      }
    },
    updateEditFocusReadOnly(colItem) {
      this.resumeEditFocusReadOnly()

      var isReadOnly = (colItem === undefined)
      if (colItem) {
        // 判断是否是审核时可编辑
        if (this.isAuditMode()) {
          if (!this.$parent.isAuditCanEdit(colItem.label)) {
            isReadOnly = true
          }
        } else {
          if (!this.canEditByModifyType(colItem)) {
            isReadOnly = true
          }

          if (this.formExtend &&
            this.formExtend.isColItemReadonly &&
            this.formExtend.isColItemReadonly(colItem, this)) {
            isReadOnly = true
          }
        }
      }

      if (isReadOnly) {
        $('#luckysheet-cell-selected-focus').addClass('luckysheet-cell-selected-focus-immutable')
        $('#luckysheet-cell-selected').addClass('luckysheet-cell-selected-immutable')
        return true
      }

      return false
    },
    resumeEditFocusReadOnly() {
      $('#luckysheet-cell-selected-focus').removeClass('luckysheet-cell-selected-focus-immutable')
      $('#luckysheet-cell-selected').removeClass('luckysheet-cell-selected-immutable')
    },

    canEditByModifyType(colItem) { // 要素是“只读”或“保存后只读”的处理
      if (colItem.modifyType === '只读') {
        return false
      }

      var isInsert = false
      if (this.dataVo &&
        this.dataVo.data &&
        this.$isEmpty(this.dataVo.data.id)) {
        isInsert = true
      }
      if (!isInsert && colItem.modifyType === '保存后只读') {
        return false
      }

      return true
    },
    cellEditBefore(range) { // 双击单元格
      if (this.isDetailMode() && !this.isAuditMode()) { // 详情模式都不能编辑
        return false
      }

      // 当前单元格有要素绑定
      var colItem = this.getEditingItem()
      var hasColItem = this.$isNotEmpty(colItem)

      // 判断是否是审核时可编辑
      if (hasColItem && this.$parent.isAuditCanEdit(colItem.label)) {
        this.isEditing = true
        return true
      }

      if (this.isDetailMode()) { // 详情模式都不能编辑
        return false
      }

      if (this.isDesignMode()) {
        // 设计模式时单元进入编辑，需要显示当前要素工具栏
        if (this.$parent.colSettingVisible === false) {
          this.$parent.colSettingVisible = true
        }

        // 设计模式时有要素绑定的单元格都不能进行编辑
        return !hasColItem
      }

      // 制单时（含预览），除了特殊要素之外，只有绑定要素的才能编辑
      // 特例情况是，审核要素不能编辑
      if (this.isFillFormMode()) {
        if (!hasColItem) {
          return false
        }

        // 扩展功能是否指定了该要素不能编辑：比如报销单的业务编码和申请金额大写
        if (this.updateEditFocusReadOnly(colItem)) {
          return false
        }

        Promise.resolve().then(() => { // 处理制单模式下双击单元格输入编辑框偏移量
          $('#luckysheet-cell-selected').css('cssText', 'border:none !important')
          if (this.overDialogZindex) {
            $('#luckysheet-input-box').css('z-index', this.overDialogZindex)
          }
          // const $cellInputBox = $('#luckysheet-input-box')
          // const $cellSelectedDomTop = $cellInputBox.position().top + 2
          // const $cellSelectedDomRight = $cellInputBox.css('right').replace('px', '') * 1 - 1
          // $cellInputBox.css('top', $cellSelectedDomTop)
          // $cellInputBox.css('right', $cellSelectedDomRight)
        })
        this.isEditing = true
        return true
      }
      return false
    },
    cellRenderAfter(cell, position, sheet, ctx) {
      // 记录特殊要素的位置。详情模式也需要使用特殊要素来显示值。
      this.$nextTick(() => {
        if (!this.isDesignMode()) {
          var wrapKey = this.wrapXY(position.r, position.c)
          if (this.$isNotEmpty(this.xyColItemMap[wrapKey])) {
            var colItem = this.xyColItemMap[wrapKey]
            this.cacheXyRender.push(wrapKey)
            colItem.style = this.getColItemStyle(colItem, { position: position })
            colItem.position = position
          }
          const xyArr = this.uniFormat(this.cacheXyArr)
          const renderedXy = this.uniFormat(this.cacheXyRender)
          const notRenderXy = xyArr.filter(i => !renderedXy.includes(i))
          renderedXy.forEach(item => {
            const showRenderItem = this.xyColItemMap[item]
            if (showRenderItem) {
              this.$set(showRenderItem, 'isRender', true)
            }
          })
          notRenderXy.forEach(i => {
            const hideRenderItem = this.xyColItemMap[i]
            if (hideRenderItem) {
              this.$set(hideRenderItem, 'isRender', false)
            }
          })
        }
        this.autoHeightCellMap[position.r] = position.end_r - position.start_r
      })
    },
    bindColItem(item, isRefresh) { // 绑定要素到画布
      if (this.isDesignMode() && item.colType === '隐藏框') {
        return
      }
      if (this.$isNotEmpty(item) &&
        item.colType !== '弹框ID' &&
        !item.isAutoAdd) {
        let value = ''
        if (this.isDesignMode()) {
          value = `#{${item.label}}`
        } else {
          value = item.dataValue || ''
          if ((this.isFillNewForm && this.$isNotEmpty(item.defaultValue))) { // 制单新增时取要素默认值
            value = item.defaultValue

            // 制单时不处理审核要素的默认值，该默认值已用于存储审核数据类型
            if (item.colType === '审核') {
              value = ''
            }
          } else if (this.isPreviewDetail ||
            (this.isFillFormMode() && this.isPreviewEdit) || item.defaultValue === '审核签章') {
            // 预览时这里要清空，是因为设计时item.dataValue是类似 #{XXX} 的形势
            value = ''
          }
        }

        if (this.isNewFormBizCode(item)) {
          value = '保存时自动生成'
        }
        // 打印详情有'多选', '单选'时,显示图片不用set值
        const blankArr = ['多选', '单选']
        if (this.isPrintDetails && this.isShowPrintCheckbox() && blankArr.includes(item.colType)) {
          value = ''
        }
        if (item.isEnabled === '否') {
          value = ''
        }
        this.setCellValue(item, value, isRefresh)
      }
      if (item.isAutoAdd && isRefresh) {
        // 自动添加为刷新要素的情况
        window.luckysheet && window.luckysheet.refresh()
      }
    },
    insertDynamic() {
      if (this.$isEmpty(this.meta.blocks)) {
        return
      }
      let insertHeight = 0
      const blocks = this.meta.blocks
      for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i]
        const pageData = block.pageData || {}
        const columns = pageData.columns || []
        const rows = pageData.rows || []
        let isInsert = false
        if (block.handlerKey !== CFORM_BLOCK_TYPE.HANDLERKEY) {
          continue
        }
        const summaryMap = {}
        const summaryColMap = {}
        const typeWhiteMap = { '整数': true, '小数': true, '金额': true, '百分比': true } // 区块的合计行目前参与计算的要素类型
        rows.forEach((row, index) => {
          // 默认有一行要素行
          const insertLength = rows.length - 1
          const isRefresh = index === insertLength
          let modelName = (block.name ? block.name : block.cformModuleName) + '-'
          if (this.meta.printType) {
            modelName = ''
          }
          columns.forEach((column) => {
            const colItem = this.labelColItemMap[modelName + column.label]
            if (this.$isNotEmpty(colItem)) {
              // 如果只有一行时不新增（默认有一行要素行）
              if (index !== 0 && !isInsert) {
                const rowHeight = window.luckysheet.getRowHeight([colItem.rowIndex])[colItem.rowIndex]
                insertHeight = insertHeight + rowHeight * insertLength
                window.luckysheet.insertRowBottom(colItem.rowIndex, { number: insertLength })
                isInsert = true
                this.refreshColItemMap(colItem.rowIndex, insertLength)
              }
              let value = row[column.prop]
              // 合计行合计
              if (typeWhiteMap[column.colType]) {
                const summaryLabel = modelName + '合计-' + column.label
                summaryColMap[summaryLabel] = column.colType
                if (summaryMap[summaryLabel]) {
                  summaryMap[summaryLabel] += (this.$fixMoney(value) - 0)
                } else {
                  summaryMap[summaryLabel] = value - 0
                }
              }
              if (column.colType === '金额') {
                value = this.$formatMoney(value)
              }
              window.luckysheet.setCellValue(
                colItem.rowIndex + index, colItem.columnIndex, { v: value, m: value }, { isRefresh }
              )
              this.setHeightAuto({
                isAdaptiveRowHeight: colItem.isAdaptiveRowHeight,
                rowIndex: colItem.rowIndex + index,
                columnIndex: colItem.columnIndex
              }, value)
            }
          })
          // 序号
          const indexColItem = this.labelColItemMap[modelName + '序号']
          if (indexColItem) {
            window.luckysheet.setCellValue(
              indexColItem.rowIndex + index, indexColItem.columnIndex, { v: index + 1, m: index + 1 }
            )
          }
        })
        // 合计行填入数据
        const summaryKeys = Object.keys(summaryMap)
        summaryKeys.forEach(colLable => {
          const colItem = this.labelColItemMap[colLable]
          if (this.$isEmpty(colItem)) return
          let value = summaryMap[colLable]
          if (summaryColMap[colLable] === '金额') {
            value = this.$formatMoney(value)
          }
          window.luckysheet.setCellValue(
            colItem.rowIndex, colItem.columnIndex, { v: value, m: value }
          )

          this.setHeightAuto({
            isAdaptiveRowHeight: colItem.isAdaptiveRowHeight,
            rowIndex: colItem.rowIndex,
            columnIndex: colItem.columnIndex
          }, value)
        })
      }
      if (this.meta.instance && insertHeight) {
        this.resetDlgSize({
          instance: {
            detailDlgHeight: this.meta.instance.detailDlgHeight + insertHeight,
            detailDlgWidth: this.meta.instance.detailDlgWidth
          }
        })
      }
    },
    // 更新ColItemMap
    refreshColItemMap(index, insertLength) {
      const colItems = Object.values(this.labelColItemMap)
      colItems.forEach(col => {
        if (col.rowIndex > index) {
          col.rowIndex = col.rowIndex + insertLength
        }
      })
    },
    isNewFormBizCode(colItem) { // 判断：制单新增 + 业务编码+ 设置了自动生成编码规则
      if (this.isFillFormMode() &&
        colItem.labelOrigin === '业务编码' &&
        this.isFillNewForm && // 表明是新增
        this.$isNotEmpty(colItem.defaultValue) && // 表明设置了编码规则
        colItem.modifyType === '只读') { // 表明编码不能输入，只能自动生成
        return true
      }
      return false
    },
    getEditingLabel() { // 获取当前绑定要素的单元格的label，不满足条件返回空串
      var colItem = this.getEditingItem()
      return this.$isNotEmpty(colItem) ? colItem.label : ''
    },
    getEditingItem() { // 获取当前绑定要素的单元格的label，不满足条件返回空串
      var key = this.wrapXY(
        this.cellPosition.row_focus,
        this.cellPosition.column_focus)
      return this.xyColItemMap[key]
    },
    formDisposeImg(imgBase64) { // TODO: 图片相关操作 拖拽 禁用 替换 等操作
      if (this.$isNotEmpty(this.meta.extData)) {
        // 替换动态签章
        const imageOption = window.luckysheet.getImageOption()
        if (this.$isNotEmpty(imageOption)) { // 判断视图是否有图片
          Object.keys(imageOption).forEach((key) => {
            // 获取当前所有图片domID（及luckysheet随机生成图片id）
            const options = imageOption[key].default
            window.luckysheet.changeImage(imgBase64, key, options)
          })
        }
      }
      // 除设计时禁用图片
      if (!this.isDesignMode()) {
        // 禁用图片
        window.luckysheet.removeMousedown()
      }
      // 制单时隐藏图片
      if (this.isFillFormMode()) {
        window.luckysheet.hideImg()
      }

      // 移除单元格拖拽事件
      window.luckysheet.removeCellDrag()

      // 隐藏图片luckysheet视图删除按钮(luckysheet自带删除)
      $('#luckysheet-modal-dialog-activeImage').find('.luckysheet-modal-controll-btn.luckysheet-modal-controll-del').css({ display: 'none' })

      // TODO： 自由表单底部多余空隙
    },
    insertImage() {
      // 插入图片相关操作
      const pictureForm = this.$isNotEmpty(this.meta.exData) ? this.meta.exData.pictureForm : '' || this.$isNotEmpty(this.meta.extData) ? this.meta.extData.pictureForm : ''
      const pictureOptions = {}
      if (this.$isNotEmpty(this.meta.exData || this.meta.extData) && this.$isNotEmpty(pictureForm)) {
        pictureForm.map((key) => {
          pictureOptions.width = key.imgWidth
          pictureOptions.height = key.imgHeight
          pictureOptions.top = key.imgTop
          pictureOptions.left = key.imgLeft
          // 制单时 详情时 审核时 isDisabled为true
          pictureOptions.isDisabled = !!(this.isDetailMode() || this.isAuditMode() || this.isFillFormMode())
          /**
            * insertImageDIY方法参数
            * @param {String} src 图片src
            * @param {Object} options 可选参数
            * @param {String} options.width 图片宽度
            * @param {String} options.height 图片高度
            * @param {String} options.top 图片位置（距离顶部）
            * @param {String} options.left 图片位置（距离左边）
            * @param {String} options.isDisabled 添加之后是否禁用图片
           */
          // 设计时
          // this.isDesignMode() ? window.luckysheet.insertImageDIY(
          //   key.typeCode === '图片文件'
          //     ? key.imgSrc : key.imgString, pictureOptions) : ''
          const imgSrc = key.typeCode === '图片文件' ? key.imgSrc : key.imgString
          if (this.isDesignMode()) {
            window.luckysheet.insertImageDIY(imgSrc, pictureOptions)
          }

          // 制单时 makeVisible为true时显示图片
          // this.isFillFormMode()
          //   ? key.makeVisible
          //     ? window.luckysheet.insertImageDIY(
          //       key.typeCode === '图片文件'
          //         ? key.imgSrc : key.imgString, pictureOptions) : '' : ''
          if (this.isFillFormMode() && key.makeVisible) {
            window.luckysheet.insertImageDIY(imgSrc, pictureOptions)
          }

          // 详情时 审核 详情 runVisible为true是显示图片
          if (this.isDetailMode() || this.isAuditMode()) {
            if (key.runVisible) {
              this.formDisposeImg(imgSrc)
              window.luckysheet.insertImageDIY(imgSrc, pictureOptions)
            }
          }
        })
      }
    },
    // 除表单之内的所有单击事件，全部退出编辑模式
    sheetMouseup(cell, position, sheet, moveState) {
      if (this.$isNotEmpty(cell)) return
      if (window.luckysheet) {
        this.$nextTick(() => {
          window.luckysheet.exitEditMode()
        })
      }
    },
    refreshAttach() {
      const formIdArr = [] // 需要同步选择参照的单据id数字
      const colItems = Object.values(this.labelOriginColItemMap)
      const refLabelOrigins = this.meta.extData[`关联附件单据表单类别`].split(',')
      colItems.forEach(item => {
        if (item.colType === '弹框' && refLabelOrigins.indexOf(item.labelOrigin) > -1) {
          var refIdItem = this.labelOriginColItemMap[item.labelOrigin + 'ID']
          if (refIdItem.dataValue) {
            var refId = refIdItem.dataValue.split('[tab]')[0]
            if (this.$isNotEmpty(refId)) {
              if (refId.indexOf('#') > -1) {
                var ids = refId.split('#')
                formIdArr.push(ids[0])
              } else {
                formIdArr.push(refId)
              }
            }
          }
        }
      })
      var formEditTabsComponents = this.$parent && this.$parent.$parent &&
      this.$parent.$parent.$parent &&
      this.$parent.$parent.$parent.$parent
        ? this.$parent.$parent.$parent.$parent.formEditTabsComponents : []
      if (this.$isNotEmpty(formEditTabsComponents)) {
        var comp = formEditTabsComponents['attach-einvoice-tab']
        if (comp && comp.refreshAttach) {
          comp.refreshAttach(formIdArr)
        }
      }
    },
    emitComposeCellPosition() {
      window.$event.$emit('composeCellPosition', {
        autoHeightCellMap: this.autoHeightCellMap
      })
    },
    showSignature() {
      this.emitComposeCellPosition()
      if (!this.isDesignMode()) {
        return
      }
      Object.values(this.xyColItemMap).forEach(xyitem => {
        if (xyitem.defaultValue === '审核签章') {
          Object.keys(this.signatureImage).forEach((item, index) => {
            if (xyitem.labelOrigin === item && !(typeof (item) === 'undefined')) {
              xyitem.showSignature ? $(`#${this.signatureImage[item]}`).css('display', 'block') : $(`#${this.signatureImage[item]}`).css('display', 'none')
            }
          })
        }
      })
    },
    changeLoading(loading) {
      this.loading = loading
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .common-page{ overflow: hidden;}
/deep/ .el-main { padding: 0; overflow: hidden;}
.formFree { width:100%;height:100%;position: relative;}
.radio-option{position: absolute;z-index:1999;display: flex;align-items: center;background: #fff;padding-left: 5px;}
.customDomPrintDetails{padding-left: 0px !important}
.customDom{position: absolute;display: flex;align-items: center;padding-left: 5px;}
.formCommonCols .colitem-button {padding-left: 0px;}
.customDom /deep/ .el-input__inner,
.customDom /deep/ .el-radio__label,
.customDom /deep/ .el-checkbox__label{
    border:none;
    display: flex;
    font-size: 15px;
    font-family: '宋体', Serif;
    color: #000 !important;
    cursor: pointer;
    align-items: center;}
.customDom /deep/ .el-input__inner { line-height: 18px;}
.customDom .customCheckbox,.customRadio  {display: flex;}
.customDom .customCheckbox .el-checkbox,.el-radio {display: flex; align-items: center;}
.customDom .customRadio{display:flex;background:#fff;align-items: center;width: auto;}
.customDom .customRadio .el-radio{margin-right: 6px;}
.formCommonCols .customDom .el-radio-group {
    line-height: 32px;height: 32px;padding: 0px 5px;border-radius: 4px;}
.customDom .customRadio  /deep/ .el-radio__label{padding-left: 3px;}

.customDom .customSelect { width: 100%;margin-right: 3px; }
.customDom .customSelect /deep/ .el-input__suffix { top:0px }
.customDom .customSelect /deep/ .el-input--small .el-input__inner{
    width: 100%;padding-left:5px;margin-left: 0px;}
.customDom .customSelect /deep/ .el-input__suffix{height: auto;}
.customDom .customCheckbox {background:#fff;padding: 0px 5px;border-radius: 4px;margin-top:-2px}
.customDom .customCheckbox /deep/ .el-checkbox__label {padding-left:2px}

.customDom .div-as-input.el-error,
.customDom .customCheckbox.el-error,
.customDom .customRadio.el-error { border: 1px solid #ff5c00 !important; margin: 0px 1px; }
.customDom .customRadio /deep/ .col-err-message {top: 10px !important;}

.customDom .div-as-input { height: calc(100% - 4px); }
.customDom .ref-rows2 { line-height: 18px !important; }
.customDom .ref-rows3 { line-height: 14px !important; font-size: 13px !important; }
.customDom .ref-rows4 { line-height: 13px !important; font-size: 12px !important; }

.customDom.colItemError .el-error {
    border: 1px solid #ff5c00 !important;border-radius: 4px;
    height: calc(100% + 2px);width: calc(100% - 0px);margin-left: -2px; }
.customDom.colItemError .el-error /deep/ .col-err-message {top: 5px !important;}
.customDom.colItemError { padding: 3px 0px 4px 3px !important;}

.customDom .customInput{ height:100%;width:calc(100% - 4px);display: flex;align-items: center; overflow: hidden; }
.customDom .customInput /deep/ .el-input__inner,
.customDom .customInput /deep/ .el-textarea__inner{ padding-left: 22px;padding-right: 0px; }
.customDom .customInput /deep/ .el-textarea__inner{left: -2px;top: 5px;}
.customDom .customInput /deep/ .el-input__prefix{left: -2px;top: 4px;}
.customDom .customInput /deep/ .el-icon-search:before,
.customDom .customInput /deep/ .el-icon-circle-close { display: flex; justify-content: center; align-items: center; }
.customDom .customInput /deep/ .el-icon-circle-close:before {font-size: 18px;cursor: pointer; }
.customDom .customInput:hover /deep/ .el-icon-search:before,
.customDom .customInput:hover /deep/ .el-icon-circle-close:before {color: #006bcc; }

.customDom .customInputPublicInfo { width: calc(100% - 2px); }
.customDom .customInputPublicInfo /deep/  i { font-size: 18px; cursor: pointer; }
.customDom .customInputPublicInfo /deep/  input { cursor: pointer !important; }
.customDom .customInputPublicInfo /deep/  input::-webkit-input-placeholder { color: #999; }
.customDom .customInputPublicInfo:hover /deep/  input::-webkit-input-placeholder { color: #006bcc; }

.formCanvasDetail .customDom .customInputPublicInfo /deep/  input { color: #3535ff !important; text-decoration:underline }
.formCanvasDetail .customDom .customInputPublicInfo /deep/  input:hover { color: #fb7b30 !important; }
.customDom .customInputPublicInfo /deep/ .el-input__prefix { top: 2px; left: -6px; }
.customDom .customInputPublicInfo:hover /deep/ .el-icon-edit:before { color: #006bcc; }

.errorDom{position: absolute;z-index:1999;display: flex;align-items: center;}
.errorText{position: absolute;top: 2px;right: 2px;font-size: 12px;color:#f56c6c;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.special-tooltip .tooltip-title{margin-bottom: 10px;font-size: 14px;}
.special-tooltip .tooltip-content-list{margin-bottom:5px;font-size: 12px;}
.customDom /deep/.customDom-daterange {
  overflow: hidden;
  display: flex;
  align-items: center;
  width: 100%!important;
  height: 100%;
  padding: 0;
  .el-range-input {
    flex: 1;
    overflow: hidden;
    color: #000000;
  }
  .el-range-separator {
    height: unset;
  }
  .el-range__icon {
    line-height: normal;
    margin-left: 0px;
  }
}
</style>
<style lang="scss">
@media print {
  @page  {
    size: A4 portrait;
    size: A4 landscape;
    margin: 0;
  }
}
.luckysheet-cell-selected-immutable {
   border: none !important;
   background: none !important;
   margin: 0 !important;
.luckysheet-cs-fillhandle{display: none !important;}
}
.luckysheet-cell-selected-focus-immutable {background: none !important;}

#luckysheet-scrollbar-x,#luckysheet-scrollbar-y{z-index: 99999 !important;}
.luckysheet-hidden-xhide{display: none !important;}
.luckysheet-hidden-yhide{display: none !important;}
.hide-luckysheet-row-column-count{display: none !important;}
.luckysheet-hidden-xscroll::-webkit-scrollbar { width: 0 !important;}
.luckysheet-hidden-yscroll::-webkit-scrollbar { height: 0 !important;}
.luckysheet-rows-h-selected-flag {
  .luckysheet-rows-h-selected,.luckysheet-cols-h-selected{
    border: none !important;
  }
}
.luckysheet-count-show {display: none !important;}
/*#formFreeView{*/
/*  border-bottom: 1px solid#luckysheet-input-box{z-index: 3000;}
 #eee;*/
/*}*/
.free-container{
  display: flex;
  position: relative;
}
.printDetailsWrap .customDomPrintDetails .printCheckbox {margin: 2px 1px;}
.printDetailsWrap .customDomPrintDetails .printRadio{align-items: flex-start;margin: 2px 1px;}
.free-fill-audit .luckysheet {border: 1px solid #bbb; }
.free-fill-audit .luckysheet-grid-container {border: none !important; }
// 影响到详情x轴滚动条
// .formFreeViewRightContent{margin-left: -18px;padding: 10px 10px 0px 0px;}
.formFreeViewRightContent{margin-left: -9px; }
.hide-icon{pointer-events: none; text-align: center;}
.hide-icon .el-input__inner{padding: 0 !important; text-align: center;}
.hide-icon .el-input__suffix{display: none;}
// // 处理表单滚动条滚不动、卡顿问题以及回弹问题
// #formMain .el-main{ overflow-y: hidden; }
.hide-icon{pointer-events: none; text-align: center;}
.hide-icon .el-input__inner{padding: 0 !important; text-align: center;}
.hide-icon .el-input__suffix{display: none;}
.free-container #luckysheet-postil-overshow {
  word-break: break-all;
  .canvas, div{
    z-index: 2023!important;
  }
}
</style>
