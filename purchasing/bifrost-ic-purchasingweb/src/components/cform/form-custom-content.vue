<template>
  <div v-if="isShowCustomContent" class="formCustomContent">
    <component
      ref="customContent"
      :is="composeComponent"
      :meta="meta"
      :mode="mode"
    >
    </component>
  </div>
</template>

<script>
// 动态组件的map
const CUSTOM_COM_MAP = {
  'pu-change-reason': 'pu-change-reason',
  'cm-change-reason': 'cm-change-reason'
}
export default {
  name: 'formCustomContent',
  props: {
    meta: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      component: ''
    }
  },
  mounted() {
  },
  computed: {
    isShowCustomContent() {
      return this.meta.extData?.formCustomContent && this.composeComponent
    },
    composeComponent() {
      const componentName = this.meta.extData?.formCustomContent
      return CUSTOM_COM_MAP[componentName]
    }
  },
  methods: {
    // 点击保存获取参数时
    fillFormData(dataVo) {
      this.$refs.customContent?.fillFormData?.(dataVo)
    }
  }
}
</script>

<style lang="scss" scoped>
.formCustomContent {
  overflow: hidden;
  width: 100%;
}
</style>
