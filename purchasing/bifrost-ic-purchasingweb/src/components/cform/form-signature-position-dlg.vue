<template>
  <div id="bid-agencyInfo-dialog">
    <el-dialog
      append-to-body
      :title="'签章位置设置'"
      :visible.sync="isdialog"
      width="55%"
      :close-on-click-modal='false'
      @close="handleClose">
      <page>
        <template #pageContent>
          <div class="container">
            <div class="left">
              <el-table
                ref="tableData"
                :data="tableData"
                highlight-current-row
                border
                @selection-change="handleSelectionChange">
                <el-table-column
                  type="selection"
                  width="35">
                </el-table-column>
                <el-table-column prop="label" label="要素名称" show-overflow-tooltip/>
                <el-table-column prop="colType" label="要素类型" width="70" align="center"/>
                <el-table-column prop="signatureType" label="签章类型" width="70" align="center"/>
                <!--                <el-table-column prop="colXPosition" label="X轴" width="50"/>-->
                <!--                <el-table-column property="colYPosition" label="Y轴" width="50" align="center" />-->
                <el-table-column property="colPagePosition" label="所在页面" width="70" align="center" />
                <el-table-column label="操作" width="90" align="center">
                  <template slot-scope="scope">
                    <a
                      size="mini"
                      @click="handleDelete(scope.$index, scope.row)">清除签章信息</a>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="right" v-loading="loading"
                 element-loading-spinner="el-icon-loading"
                 element-loading-background = "#fff"
                 element-loading-text="加载中...">
              <showPdf ref="showPdf"  @getPdfPosition="getPdfPosition" @toggle-stamp-type="handleToggleStampType"></showPdf>
            </div>
          </div>
        </template>
      </page>
      <template #footer>
        <el-button @click="handleClose('signatureForm')" v-if="!isDetails"> 取消</el-button>
        <el-button
          type="primary"
          @click="handleSumbit('signatureForm')"
          v-if="!isDetails"
          :loading="loadingButton"
        >
          {{ loadingButton ? '处理中...' : '确定' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { upload } from '@/api/file/file'
import { exportExcel } from '../../components/cform/js/exportSheet'
export default {
  name: 'form-signature-position-dlg',
  components: { upload },
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    isDetails: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialog(bool) {
      this.isdialog = bool
    }
  },
  mounted() {
  },
  data() {
    return {
      loadingButton: false,
      isdialog: this.dialog,
      signatureForm: [],
      tableData: [],
      loading: true,
      isCircleStamp: '',
      selectedRow: '', // 用于存储选中的行数据
      lastSelectedRow: '' // 用于存储最后勾选的行数据

    }
  },
  methods: {
    handleDelete(index, row) {
      row.colXPosition = ''
      row.colYPosition = ''
      row.colPagePosition = ''
      row.signatureType = ''
    },
    handleToggleStampType(isCircleStamp) {
      this.isCircleStamp = isCircleStamp
      // 在这里执行需要触发的逻辑，比如调用 getPdfPosition 方法
      this.getPdfPosition({
        pageX: this.lastSelectedRow.colXPosition,
        pageY: this.lastSelectedRow.colYPosition,
        currentPage: this.lastSelectedRow.colPagePosition,
        signatureType: this.isCircleStamp ? '公章' : '个人章'
      })
    },
    getPdfPosition(pdfPosition) {
      this.$set(this.lastSelectedRow, 'colXPosition', pdfPosition.pageX)
      this.$set(this.lastSelectedRow, 'colYPosition', pdfPosition.pageY)
      this.$set(this.lastSelectedRow, 'colPagePosition', pdfPosition.currentPage)
      this.$set(this.lastSelectedRow, 'signatureType', pdfPosition.signatureType)
    },
    show(columnsWf, sheets, filename) {
      this.isdialog = true
      this.tableData = columnsWf
      const _this = this
      exportExcel(sheets, filename, false, async(base64, pdfBlob) => {
        // 使用 pdfBlob 进行进一步处理
        const signedPdfBlob = await pdfBlob
        // 将 Blob 数据转换为 Uint8Array
        const fileReader = new FileReader()
        fileReader.onloadend = () => {
          const arrayBuffer = fileReader.result
          const data = new Uint8Array(arrayBuffer)
          if (this.$isNotEmpty(data)) {
            _this.loading = false
            // 渲染 PDF
            _this.$refs.showPdf.renderPDF(data)
          }
        }
        fileReader.readAsArrayBuffer(signedPdfBlob)
      }, '签章')
    },
    handleClose() {
      this.resetForm()
    },
    handleSumbit() {
      var columnListVo = {}
      columnListVo.cols = this.tableData
      this.loadingButton = true
      columnListVo.success = () => {
        this.loadingButton = false
        this.isdialog = false
      }
      columnListVo.fail = () => {
        this.loadingButton = false
      }
      this.$emit('save', columnListVo)
    },
    handleSelectionChange(selection) {
      if (selection.length > 1) {
        // 取消之前选中的数据
        if (this.lastSelectedRow) {
          this.$refs.tableData.toggleRowSelection(this.lastSelectedRow, false)
        }
      }
      // 记录最后勾选的行数据
      this.lastSelectedRow = selection[selection.length - 1]
      // 根据必含要素的勾选来显示图片
      if (this.$isNotEmpty(this.lastSelectedRow.colXPosition) &&
          this.$isNotEmpty(this.lastSelectedRow.colYPosition) &&
          this.$isNotEmpty(this.lastSelectedRow.colPagePosition) &&
          this.$isNotEmpty(this.lastSelectedRow.signatureType)
      ) {
        var pageX = this.lastSelectedRow.colXPosition
        var pageY = this.lastSelectedRow.colYPosition
        this.$refs.showPdf.dragImageX = pageX
        this.$refs.showPdf.dragImageY = pageY
        this.$refs.showPdf.updateCurrentPage(this.lastSelectedRow.colPagePosition, this.lastSelectedRow.signatureType)
        if (this.lastSelectedRow.signatureType === '公章') {
          // 切换到公章时修改样式
          this.$refs.showPdf.$refs.dragRect.style.width = '80px'
          this.$refs.showPdf.$refs.dragRect.style.height = '80px'
        } else {
          // 切换到个人章时恢复原样式
          this.$refs.showPdf.$refs.dragRect.style.width = '80px'
          this.$refs.showPdf.$refs.dragRect.style.height = '50px'
        }
      }
    },
    resetForm() {
      this.isdialog = false
      this.$refs.showPdf.dragImageX = 0
      this.$refs.showPdf.dragImageY = 0
      this.$refs.showPdf.currentPage = 1
    }
  }
}

</script>

<style scoped>
  .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 5px;
  }

  .left {
    width: 98%;
    height: 101%;
  }

  .right {
    height: 101%;
    border: 1px solid #bbb;
  }

</style>
