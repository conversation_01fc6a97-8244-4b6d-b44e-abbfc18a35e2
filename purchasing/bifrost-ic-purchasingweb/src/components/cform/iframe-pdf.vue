<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-09 17:18:10
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-09 17:18:18
-->
<template>
  <canvas width="100%" ref="canvas"></canvas>
</template>

<script>
import pdfjs from 'pdfjs-dist/build/pdf'
import 'pdfjs-dist/build/pdf.worker.entry'
export default {
  name: 'iframe-pdf',
  data() {
    return {
      pdfInstance: null,
      numPages: 0,
      currentPage: 1,
      scale: 1
    }
  },
  props: {
    src: {
      default: ''
    }
  },
  mounted() {
  },
  methods: {
    renderPDF(data) {
      const canvas = this.$refs.canvas
      // 设置 pdf.js 的 worker 路径
      pdfjs.GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.js')
      // 创建 Uint8Array 对象
      const pdfData = new Uint8Array(data)

      // 获取 PDF 文档
      pdfjs.getDocument(pdfData).promise.then((pdf) => {
        this.pdfInstance = pdf
        this.numPages = pdf.numPages

        pdf.getPage(1).then((page) => {
          // 渲染当前页
          this.renderPage(this.currentPage, canvas)
        })
      })
    },
    renderPage(pageNumber, canvas) {
      this.pdfInstance.getPage(pageNumber).then((page) => {
        const viewport = page.getViewport({ scale: 2.5 })

        const context = canvas.getContext('2d')
        canvas.height = viewport.height
        canvas.width = viewport.width

        page.render({
          canvasContext: context,
          viewport
        })
      })
    }
  }
}
</script>

<style>
</style>
