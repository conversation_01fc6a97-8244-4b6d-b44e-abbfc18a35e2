<template>
  <div class='elementConfig'>
    <el-form :inline="false" ref="configForm" label-width="120px" :model="form" size="mini">
      <el-form-item label="组件名称" prop="name">
        <el-input v-model="form.name" :disabled="disabled" :maxlength="15" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="上限数" prop="upperLimit">
        <el-input-number v-model="form.upperLimit" :min="0" :disabled="disabled" step-strictly @change="handleUpperLimitChange"></el-input-number>
      </el-form-item>
      <el-form-item label="上限显示行数" prop="upperLimitLine">
        <el-input-number v-model="form.upperLimitLine" :min="0" :max="handleMaxUpperLimitLine" :disabled="disabled" step-strictly @change="handleUpperLimitChange"></el-input-number>
      </el-form-item>
      <el-form-item label="默认显示行数" prop="defaultLine">
        <el-input-number v-model="form.defaultLine" :min="0" :max="handleMaxDefaultLine" :disabled="disabled" step-strictly></el-input-number>
      </el-form-item>
      <el-form-item label="隐藏按钮" prop="hiddenButton">
        <el-select v-model="form.hiddenButton" :disabled="disabled" multiple placeholder="请选择按钮">
          <el-option v-for="bt in selection" :key="bt.text" :label="bt.text" :value="bt.text"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="必填" prop="required">
        <el-switch class="switchStyle" v-model="form.required" :disabled="disabled"></el-switch>
      </el-form-item>
      <el-form-item label="启用合计行" prop="hasSummaries">
        <el-switch class="switchStyle" v-model="form.hasSummaries" :disabled="disabled"></el-switch>
      </el-form-item>
      <el-form-item label="默认展开" prop="fold">
        <el-switch class="switchStyle" v-model="form.fold" :disabled="disabled"></el-switch>
      </el-form-item>
      <el-form-item label="启用序号列" prop="indexColumn">
        <el-switch class="switchStyle" v-model="form.indexColumn" :disabled="disabled"></el-switch>
      </el-form-item>
      <el-form-item label="启用分页" prop="paging">
        <el-switch class="switchStyle" v-model="form.paging" :disabled="disabled"></el-switch>
      </el-form-item>
    </el-form>
    <div>
      <el-button @click="saveForm" size="small" type="primary" :disabled="disabled">保存</el-button>
      <el-button @click="resetForm" size="small" :disabled="disabled">重置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'element-config',
  props: {
    disabled: {
      type: Boolean,
      default: true
    },
    rformIndex: {
      type: Number,
      default: 1
    }
  },
  inject: {
    handleCurrentRformBlock: { default: undefined }
  },
  data() {
    return {
      form: {
        name: '',
        upperLimit: 0,
        upperLimitLine: 0,
        defaultLine: 0,
        hiddenButton: [],
        required: true,
        hasSummaries: false,
        indexColumn: false,
        fold: true,
        paging: false
      },
      selection: [],
      currentBlockInitParams: null,
      maxUpperLimit: 20,
      maxDefaultLine: 20,
      defaultName: ''
    }
  },
  computed: {
    /**
     * 设置上限显示行数的max
     */
    handleMaxUpperLimitLine() {
      let max = this.maxUpperLimit
      // 如果上限数大于10 则上限显示行数最大为10 否则最大为上限数
      if (this.form.upperLimit > this.maxUpperLimit || this.form.upperLimit === 0) {
        max = this.maxUpperLimit
      } else {
        max = this.form.upperLimit
      }
      return max
    },
    /**
     * 设置默认显示行数的max
     */
    handleMaxDefaultLine() {
      let max = this.maxDefaultLine
      if (this.form.upperLimit > this.maxDefaultLine || this.form.upperLimit === 0) {
        max = this.form.upperLimitLine < this.form.defaultLine ? this.form.upperLimitLine : this.maxDefaultLine
      } else {
        max = this.form.upperLimit
      }
      return max
    }
  },
  methods: {
    /**
     * 处理UpperLimit改变事件
     */
    handleUpperLimitChange() {
      // 如果 上限显示行数或默认显示行数 大于 上限显示行数的max 则设置它们的值为max
      if (this.form.upperLimitLine > this.handleMaxUpperLimitLine) {
        this.form.upperLimitLine = this.handleMaxUpperLimitLine
      }
      /**
       * 如果上限显示行数的max不等于maxConstant 且 默认显示行数大于上限显示行数的max
       * 才设置默认显示行数的值
       */
      if (this.form.defaultLine > this.handleMaxDefaultLine) {
        this.form.defaultLine = this.handleMaxDefaultLine
      }
    },
    /**
     * 保存表单数据
     */
    saveForm() {
      const handleForm = {
        ...this.form,
        hiddenButton: this.form.hiddenButton?.join(',')
      }
      this.handleCurrentRformBlock(null, handleForm)
    },
    /**
     * 重置表单数据
     */
    resetForm() {
      this.$refs['configForm'].resetFields()
      this.form.name = this.defaultName
    },
    /**
     * 设置区块要素配置
     * @param {Object} config 组件要素配置
     */
    setForm(config) {
      // 如果组件名称为空 则设置为默认的组件名
      if (this.$isEmpty(config?.name)) {
        this.form.name = config?.cformModuleName
        config.name = config?.cformModuleName
      }
      this.form = {
        ...config,
        hiddenButton: this.$isNotEmpty(config?.hiddenButton) ? config?.hiddenButton?.split(',') : []
      }
    },
    getBlockInitParams(currRformParams) {
      this.currentBlockInitParams = currRformParams
    },
    /**
     * 设置隐藏按钮下拉选项
     * @param {Array} selection 隐藏按钮下拉选项
     */
    setButtonSelection(selection) {
      // 去除第一个折叠按钮
      this.selection = selection.slice(1)
    },
    setDefaultName(name = '') {
      this.defaultName = name
    }
  }
}
</script>
<style lang='scss' scoped>
  .elementConfig {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-right: 10px;
    .el-form {
      align-self: flex-start;
      width: 100%;
      .el-input-number {
        width: 100%;
      }
    }
    .el-button {
      width: max-content;
    }
  }
</style>
