<!--
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-21 17:20:31
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-09-25 17:26:20
-->
<template>
  <div id="CHATSDk" >
    <div id="my-ops-mobile" 
    @mousedown="startDrag"
    @touchstart="startDrag"
    @mouseup="endDrag"
    @touchend="endDrag"
    @mousemove="onDrag"
    @touchmove="onDrag"
    @click="handleClick"
    ref="draggableElement"
    :style="{ position: 'fixed', right: position.right + 'px', bottom: position.bottom + 'px' }"
    >
      <div class="pj-ops-badge">
        <div id="btn" class="componentsTypeV1">
          <img id="btn" :src="imageSrc" />
          <div id="btn">沟通反馈</div>
        </div>
        <div v-show="unreadCount > 0" class="unread-text"
           :class="{ 'large-count': unreadCount > 999 }"
           :style="{ left: getUnreadTextLeft(unreadCount) }"
           >
          {{ formatUnreadCount(unreadCount) }}
        </div>
      </div>
    </div>

    <el-dialog
      :visible="isShowChatPage"
      v-if="isShowChatPage"
      :close-on-click-modal="false"
      custom-class="draftsDialog no-title-dialog"
      width="65%"
      append-to-body
      destroy-on-close
      title=" "
      @close="handleClose"
    >
      <chatHome ref="chatHome" />
    </el-dialog>
  </div>
</template>

<script>
import chatHome from "@/views/chatPage/index.vue";
import { mapState } from "vuex";
export default {
  name: "fixIcon",
  components: { chatHome },
  props: {},
  data() {
    return {
      imageSrc: require("@/assets/chatIcon.png"),
      position: {
        right: 14,
        bottom: 40
      },
      isDragging: false,
      startPos: { x: 0, y: 0 },
      startDragPos: { x: 0, y: 0 },
      hasMoved: false, // 用于区分是点击还是拖拽
      dragThreshold: 5 // 拖拽阈值
    };
  },
  computed: {
    ...mapState("chatStore", ["isShowChatPage", "chats"]),
    unreadCount() {
      let unreadCount = 0;
      let chats = this.chats;
      // 检查 this.chats 是否存在且为数组
      if (Array.isArray(this.chats)) {
        this.chats.forEach((chat) => {
          if (!chat.delete && !chat.isDnd) {
            unreadCount += chat.unreadCount;
          }
        });
      }
      return unreadCount;
    },
  },
  watch: {},
  created() {},
  mounted() {
    // 初始化位置
    this.position = this.getSavedPosition() || this.position;
    
    // 绑定全局事件，确保在元素外也能正常拖拽
    document.addEventListener('mousemove', this.onDrag);
    document.addEventListener('touchmove', this.onDrag, { passive: false });
    document.addEventListener('mouseup', this.endDrag);
    document.addEventListener('touchend', this.endDrag);
  },
  beforeDestroy() {
    // 清理全局事件监听器
    document.removeEventListener('mousemove', this.onDrag);
    document.removeEventListener('touchmove', this.onDrag);
    document.removeEventListener('mouseup', this.endDrag);
    document.removeEventListener('touchend', this.endDrag);
  },
  methods: {
    handleClick(event) {
      // 只有在非拖拽状态下才打开对话框
      if (!this.isDragging && !this.hasMoved) {
        event.preventDefault();
        event.stopPropagation();
        this.$store.commit("chatStore/setIsShowChatPage", true);
      }
    },
    handleClose() {
      this.$store.commit("chatStore/setIsShowChatPage", false);
    },
    formatUnreadCount(count) {
      if (count > 999) {
        return '999+';
      }
      return count;
    },
    getUnreadTextLeft(count) {
      if (count > 999) {
        // 显示 "999+" 时，left 为 12px
        return '12px';
      } else if (count > 99) {
        // 显示三位数(100-999)时，left 为 15px
        return '15px';
      } else {
        // 显示一两位数(1-99)时，left 为 22px
        return '22px';
      }
    },
    // 点击拖拽相关方法
    startDrag(event) {
      // 阻止默认行为
      event.preventDefault();
      
      // 记录起始位置
      const clientX = event.touches ? event.touches[0].clientX : event.clientX;
      const clientY = event.touches ? event.touches[0].clientY : event.clientY;
      
      this.startPos = { x: clientX, y: clientY };
      this.startDragPos = { ...this.position };
      
      // 重置移动状态
      this.hasMoved = false;
      
      // 防止文本选择
      document.body.style.userSelect = 'none';
      document.body.style.webkitUserSelect = 'none';
      
      // 防止其他元素干扰拖拽
      event.stopPropagation();
    },

    onDrag(event) {
      // 只有在已经开始拖拽时才处理
      if (this.startPos.x === 0 && this.startPos.y === 0) return;
      
      event.preventDefault();
      
      const clientX = event.touches ? event.touches[0].clientX : event.clientX;
      const clientY = event.touches ? event.touches[0].clientY : event.clientY;

      // 计算移动距离
      const deltaX = clientX - this.startPos.x;
      const deltaY = clientY - this.startPos.y;
      
      // 如果移动距离超过阈值，则认为是拖拽操作
      if (!this.hasMoved && (Math.abs(deltaX) > this.dragThreshold || Math.abs(deltaY) > this.dragThreshold)) {
        this.hasMoved = true;
      }

      // 只有在确认移动后才更新位置
      if (this.hasMoved) {
        // 更新位置
        const newPosition = {
          right: this.startDragPos.right - deltaX,
          bottom: this.startDragPos.bottom - deltaY
        };

        // 边界检查
        const element = this.$refs.draggableElement;
        if (element) {
          const rect = element.getBoundingClientRect();
          const viewportWidth = window.innerWidth;
          const viewportHeight = window.innerHeight;

          // 限制右边界
          if (newPosition.right < 10) {
            newPosition.right = 10;
          } else if (newPosition.right > viewportWidth - rect.width - 10) {
            newPosition.right = viewportWidth - rect.width - 10;
          }

          // 限制下边界
          if (newPosition.bottom < 10) {
            newPosition.bottom = 10;
          } else if (newPosition.bottom > viewportHeight - rect.height - 10) {
            newPosition.bottom = viewportHeight - rect.height - 10;
          }
        }

        this.position = newPosition;
        this.isDragging = true;
      }
    },

    endDrag(event) {
      // 如果正在拖拽，保存位置
      if (this.isDragging) {
        this.savePosition(this.position);
      }

      // 恢复文本选择
      document.body.style.userSelect = '';
      document.body.style.webkitUserSelect = '';

      // 重置状态
      this.isDragging = false;
      this.startPos = { x: 0, y: 0 };
      
      // 延迟重置 hasMoved 状态，确保 click 事件能正确判断
      setTimeout(() => {
        this.hasMoved = false;
      }, 300);
      
      event.stopPropagation();
    },

    // 保存位置到本地存储
    savePosition(position) {
      try {
        localStorage.setItem('chatIconPosition', JSON.stringify(position));
      } catch (e) {
        console.warn('无法保存图标位置到本地存储:', e);
      }
    },

    // 从本地存储获取位置
    getSavedPosition() {
      try {
        const savedPosition = localStorage.getItem('chatIconPosition');
        return savedPosition ? JSON.parse(savedPosition) : null;
      } catch (e) {
        console.warn('无法从本地存储获取图标位置:', e);
        return null;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
/* 样式代码保持不变 */
#CHATSDk {
  position: static;
  #my-ops-mobile {
    position: fixed;
    cursor: move;
    z-index: 8888;
    touch-action: none;
    right: 14px;
    bottom: 40px;
  }

  .pj-ops-badge {
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    &:hover {
      transform: translateY(-4px);
    }
    .unread-text {
      position: absolute;
      background: linear-gradient(135deg, #ff4d4f, #f5222d);
      left: 22px;
      top: -6px;
      color: white;
      border-radius: 30px;
      padding: 0 5px;
      font-size: 12px;
      text-align: center;
      white-space: nowrap;
      border: 2px solid #ffffff;
      box-shadow: 0 2px 6px rgba(245, 34, 45, 0.4);
      font-weight: bold;
      min-width: 20px;
      height: 20px;
      line-height: 16px;
      animation: blink .9s infinite;
      transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
       // 当显示 999+ 时，调整宽度
       &.large-count {
        min-width: 30px;
        padding: 0 8px;
      }
    }
    &:hover .unread-text {
      top: -10px;
      box-shadow: 0 4px 10px rgba(245, 34, 45, 0.5);
    }
    
    @keyframes blink {
      0% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.8;
        transform: scale(1.05);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    .componentsTypeV1 {
      height: 120px;
      width: 40px;
      border-radius: 20px !important;
      background-color: rgb(47, 140, 253);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      writing-mode: vertical-rl;
      transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
      box-shadow: 0 2px 8px rgba(47, 140, 253, 0.3);
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 6px 16px rgba(47, 140, 253, 0.4);
        background-color: rgb(60, 150, 255);
      }
      img#btn {
        height: 20px;
        width: 20px;
        border-radius: 20px !important;
        color: rgb(255, 255, 255);
        transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
      }

      div#btn {
        margin-top: 12px;
        color: rgb(255, 255, 255);
        font-size: 16px;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
      }
      
      &:hover img#btn {
        transform: scale(1.1);
      }
    }

    .pj-ops-badge__content.pj-ops-badge__content--danger.is-fixed.is-hide-zero {
      /* 若该元素有额外样式，可在此添加 */
    }
  }
}
::v-deep {
  .el-dialog .el-dialog__header {
    border: none !important;
    padding: 0 !important;
  }
  .el-dialog__body {
    padding: 0 !important;
  }
}
</style>