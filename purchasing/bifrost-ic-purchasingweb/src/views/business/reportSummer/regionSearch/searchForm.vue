<!--
 * @Description: 搜索公共栏组件
 * @Version: 1.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-05-08 09:53:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-09-25 16:52:45
-->
<template>
  <div>
    <div class="search-form-box">
      <el-row type="flex">
        <el-col :span="leftviewSpan || 18" class="leftview">
          <el-form v-if="formQuery" :model="formQuery" :label-width="labelWidth" ref="infoForm">
            <el-row :gutter="30">
              <!-- 直接遍历所有需要渲染的项，为每个项创建一个el-col -->
              <el-col
                v-for="(row, rowIndex) in flattenedOptions"
                :key="rowIndex"
                :span="getSpan(row)"
                :style="getStyle(row)"
              >
                <el-form-item
                  :rules="row.rules || {}"
                  :prop="row.label"
                  :label="row.label"
                >
                  <formItem
                    ref="infoFormItem"
                    v-model="formQuery[row.prop]"
                    :itemOptions="row"
                    :key="row.formItemKey"
                    :restFlag="restFlag"
                    @onSearch="onSearch"
                    @dateTypeChange="changeDateType"
                    @selectTreeClick="selectTreeClick"
                    @loadMore="loadMoreData"
                    @emitTreeNode="emitTreeNode"
                    @restchildsucc="restchildsucc"
                    @handleFocus="handleFocus"
                    @setOptions="setOptions"
                    @selectChange="handleSelectChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
        <el-col :span="1" v-show="showRotate && !showIcon && formOptions.length > 4" style="width: auto">
          <div class="arrow" @click="changeSlide">
            <i  v-if="isRotate" :class="[rotate ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"/>
            <div v-if="!isRotate" style="width: 26px; height: 26px" />
          </div>
        </el-col>
        <el-col :span="showRotate ? 4 : 5" class="but-warp">
          <!-- 提交按钮 -->
          <div class="btn-box">
            <el-button
              v-if="btnItems.btnList.includes('search')"
              :size="btnItems.size"
              type="primary"
              class="btn-search"
              @click="onSearch($event,'projectinfos')"
              :disabled="isEditBtn"
              icon="el-icon-aliiconsousuo"
            >
              <span>查询</span></el-button
            >
            <!-- <el-button
              v-if="btnItems.btnList.includes('search')"
              :size="btnItems.size"
              type="primary"
              class="btn-search-width"
              @click="onSearch($event,'bidsysprojectinfos')"
              :disabled="isEditBtn"
              icon="el-icon-aliiconsousuo"
            >
              <span>查询深大数据</span></el-button
            > -->
            <el-button
              v-if="btnItems.btnList.includes('reset')"
              :size="btnItems.size"
              type="default"
              class="btn-reset"
              @click="onReset"
              :disabled="isEditBtn"
              icon="el-icon-aliiconzhongzhi"
              ><span>重置</span></el-button
            >
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import formItem from "./formItem";
import { createUniqueString } from "./tools";

export default {
  props: {
    /**
     * 表单配置
     * 示例：
     * [{
     *   label: '用户名', // label文字
     *   prop: 'username', // 字段名
     *   element: 'el-input', // 指定elementui组件
     *   initValue: '阿黄', // 字段初始值
     *   placeholder: '请输入用户名', // elementui组件属性
     *   rules: [{ required: true, message: '必填项', trigger: 'blur' }], // elementui组件属性
     *   events: { // elementui组件方法
     *     input (val) {
     *       console.log(val)
     *       (val)=>{this.callback()}
     *       这里可以绑定methods里面的事件 自行处理业务
     *     },
     *
     *     ...... // 可添加任意elementui组件支持的方法
     *   }
     *   ...... // 可添加任意elementui组件支持的属性
     * }]
     */
    labelWidth: {
      type: String,
      default: "auto",
    },
    leftviewSpan: {
      type: Number,
      default: 0,
    },
    formOptions: {
      type: Array,
      required: true,
      default() {
        return [];
      },
    },
    // 提交按钮项
    btnItems: {
      type: Object,
      default() {
        return {
          btnList: "search,reset", //多个用逗号分隔（search, export, reset）
          size: "small", //按钮大小 （medium、small、mini）
        };
      },
    },
    showRotate: {
      type: Boolean,
      default: true,
    },
    showIcon: {
      type: Boolean,
      default: false,
    },
    isRotate: {
      type: Boolean,
      default: true,
    },
    span: {
      type: Number,
      default: 6,
    },
    colNum: {
      //每行几个
      type: Number,
      default: 4,
    },
    leftViewHeight: {
      type: String,
      default: "45px",
    },
  },
  watch: {
    formOptions: {
      handler(newval, oldval) {
        if (newval) {
          this.computedRow();
          // this.addInitValue(); //防止组件改变后触发导致取默认值问题
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      formQuery: {}, //筛选返回的数据s
      rotate: false,
      options: [], //计算后的数据源
      mode: "ordinary", //组件模式 普通:ordinary | 复杂:complex | 自定义custom
      restFlag: false,
      temp: 0,
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
      searchType: ""
    };
  },
  components: { formItem },
  computed: {
    newKeys() {
      return this.formOptions.map((v) => {
        return createUniqueString();
      });
    },
    isEditBtn() {
      return this.formOptions.length === 0
    },
    flattenedOptions() {
      console.log(this.options, 'this.options')
       return this.options.flatMap(item => item); //
    }
  },

  created() {
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    getSpan(row) {
      let isMoreWidth = ["el-input-cost", "el-date-picker"].includes(row?.element);
      let spanForMoreWidth = isMoreWidth ? 24 : 12;
      if(row?.rowSpan) {
        return  row.rowSpan;
      }
      if (this.windowWidth < 576) {
        return 24; // 极小屏幕
      } else if (this.windowWidth < 992) {
        return spanForMoreWidth; // 小屏幕和中等屏幕使用相同逻辑
      } else if (this.windowWidth < 1500) {
        return spanForMoreWidth; // 大屏幕和超大屏幕使用相同逻辑
      } else if (this.windowWidth < 1700) {
        return isMoreWidth ? 12 : (row?.span || 8); // 超大屏幕
      } else {
        return isMoreWidth ? 12 : (row?.span || 6); // 超大屏幕
      }
    },
    getStyle(row) {
      if(row?.style) {
        return row?.style
      }
    },
    handleResize() {
      this.windowWidth = window.innerWidth;
      this.windowHeight = window.innerHeight;
      this.$forceUpdate(); // 触发重新渲染，确保计算属性得到更新
    },
    /**
     * @description: 校验
     * @param {*} callback
     * @return {*}
     * @author: GleenLey
     */
    onValidate(callback) {
      this.$refs.infoForm.validate((valid) => {
        if (valid) {
          callback();
        }
      });
    },

    /**
     * @description: 搜索
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    onSearch(isEmit=true, index) {
      this.searchType = index
      this.onValidate(() => {
        /**
         * 单行处理
         */
        let mode = this.mode;
        //简单模式
        if (mode == "ordinary") {
          this.handleColumn("ordinary");
        }
        //复杂模式：搜索栏多行处理
        if (mode == "complex") {
          this.handleColumn("complex");
        }
      });
    },
    getSelectionValue(mode) {
      return new Promise((resolve) => {
        const obj = {};
        let currentOptions = this.formOptions
        currentOptions && currentOptions.forEach(v => {
          obj[v.prop] = this.formQuery[v.prop];
          if (v.thousands) {
            let currentValue = JSON.parse(JSON.stringify(this.formQuery[v.prop]));
            if (Array.isArray(currentValue)) {
              currentValue[0] = currentValue[0]
                ? currentValue[0].replace(/,/g, "")
                : "";
              currentValue[1] = currentValue[1]
                ? currentValue[1].replace(/,/g, "")
                : "";
            } else {
              currentValue = currentValue ? currentValue.replace(/,/g, "") : "";
            }
            obj[v.prop] = currentValue;
          }
          if (v.labelProp) {
            obj[v.labelProp] = this.formQuery[v.labelProp];
          }
        });
        resolve(obj)
      })

    },
    handleColumn (mode) {
      const obj = {};
      let currentOptions =
        mode == "ordinary" ? this.options[0] : this.formOptions;
      currentOptions && currentOptions.forEach(v => {
        obj[v.prop] = this.formQuery[v.prop];
        if (v.thousands) {
          let currentValue = JSON.parse(JSON.stringify(this.formQuery[v.prop]));
          if (Array.isArray(currentValue)) {
            currentValue[0] = currentValue[0]
              ? currentValue[0].replace(/,/g, "")
              : "";
            currentValue[1] = currentValue[1]
              ? currentValue[1].replace(/,/g, "")
              : "";
          } else {
            currentValue = currentValue ? currentValue.replace(/,/g, "") : "";
          }
          obj[v.prop] = currentValue;
        }
        if(v.element == 'el-date-picker' && !this.formQuery[v.prop]) {
          // 特殊处理date。
            obj[v.prop] = undefined
        }
        if (v.labelProp) {
          obj[v.labelProp] = this.formQuery[v.labelProp];
        }
      });
      this.$emit("onSearch", obj);
    },

    /**
     * @description: 导出
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    onExport () {
      this.onValidate(() => {
        this.$emit("onExport", this.formQuery);
      });
    },
    /**
     * @description: 清空下拉树的值
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    clearTreeProp (prop) {
      this.$refs.infoFormItem.forEach(val => {
        if (val.itemOptions.prop === prop) val.clearTree()
      })
    },
    /**
     * @description: 重置
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    onReset (isEmit=true) {
      // 1.通过搜索组件的表单清理填入值，下拉树组件要手动调用清理
      this.$refs.infoForm.resetFields();
      Object.keys(this.formQuery).forEach(e => {
        // if (Array.isArray(this.formQuery[e]) && !this.formQuery[e][0]) this.formQuery[e] = []
        if (Array.isArray(this.formQuery[e])) {
          this.formQuery[e] = []
        }
        else {
          this.formQuery[e] = ''
        }
      })
      this.$refs.infoFormItem.forEach(e => e.clearTree());
      new Promise((response, resject) => {
        // 2.重新计算搜索组件展开的行数
        this.computedRow();
        // 3.重置子组件没有双向绑定 的变量，主要处理this.options挂载的属性和值，this.formQuery
        this.restChildData();
        // 4.调用添加初始量方法，给this.formQuery设置初始值
        this.addInitValue();
        // 5.组件内部清理旧值和重置的工作完成，进入向父组件返回出未填写的搜索栏表单
        if (this.options.length) {
          response();
        }
      }).then(() => {
        let mode = this.mode;
        let obj = {};
        //简单模式
        if (mode == "ordinary") {
          let _options = this.options;
          _options[0].map(v => {
            obj[v.prop] = this.formQuery[v.prop];
            //v.initValue = '';
          });
          //this.$set(this,'options',_options);
        }
        //复杂模式
        if (mode == "complex") {
          obj = this.formQuery
        }
        // 6.搜索组件向功能页面返回清理完成的空白表单，作为查询的传参
         if(isEmit){
          this.$emit("onReset", obj);
          }
        /*
          7.修改this.restFlag状态：
            searchForm作为父组件，给formItem子组件一个信号，子组件内部拿到后，去执行从功能页面给定搜索框默认值的下拉树，initValue等进行赋值展示
        */
        this.restFlag = true;
      });
    },

    /**
     * @description: 处理一些子组件里面不是双向绑定的默认数据
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    restChildData () {
      let options = this.options;
      options.map((item, index) => {
        item.map((el, elindex) => {
          const  labelPickgp = ["el-select-datepicker","el-input-group"]
          if (labelPickgp.includes(el.element)) {
            if (this.formQuery[`${el.labelProp}`])
              this.formQuery[`${el.labelProp}`] = "";
          }
          if (
            el.element === "el-input-dialog"
          ) {
            el.name = ''
          }
          /*
            关于搜索组件重置默认值的说明：
            在搜索组件中暴露出onRest方法里面可以进行搜索框默认赋值再执行查询的业务逻辑。
             - 但下面的 el.initValue | el.value | el.valueId 这段注释后，则不用到具体的功能页面的onRest中再对搜索框赋默认值
            因为搜索框绑定的本身是 this.options 对象，且为做旧值清理的话，可以直接从这里取出渲染
             - 目前这段放开的情况符合业务逻辑，需到页面中重新对搜索框赋默认值
          */
          // if (el.initValue) {
          //   let type = typeof el.initValue;
          //   el.initValue = type == "string" || type == "number" ? "" : [];
          // }
          if (el.element === "el-select-tree") {
            el.value = ""
            el.valueId = ""
          }
           // 针对普通时间选择器 非联动类型为其他 重置另外处理
          if(el.element === "el-date-picker" && el.type === "others") {
            el.initValue = []
          }
          const costPickerArray = ["el-input-cost","el-date-picker"]
          if (costPickerArray.includes(el.element)) {
            el.initValue = []
          }
          if (el.element === "el-input") {
            el.value = ""
            el.initValue = ""
          }
        });
      });
    },

    /**
     * @description: 重置子组件成功
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    restchildsucc (value) {
      this.temp++;
      let mode = this.mode;
      if (mode == "ordinary" && this.options[0].length) {
        this.restFlag = false;
        this.temp = 0;
      }
      if (mode == "complex" && this.temp >= this.formOptions.length) {
        this.restFlag = false;
        this.temp = 0;
      }
    },

    /**
     * @description: 接受子组件发射出来的类型 组件前面有选择类型的那种
     * @param {*} type
     * @return {*}
     * @author: GleenLey
     */
    changeDateType (query) {
      Object.assign(this.formQuery, query);
    },

    /**
     * @description: 添加初始值
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    addInitValue () {
      const obj = {};
      let formOptions = this.formOptions;
      formOptions.forEach(v => {
        //除下拉树选择之外的
        if (v.initValue && v.initValue !== undefined) {
          obj[v.prop] = v.initValue;
        }

        //下拉树
        if (v.valueId && v.valueId !== undefined) {
          obj[v.prop] = v.valueId;
        }
        //没有提供初始数据补空
        if (!v.initValue && !v.valueId && v.labelProp) {
          obj[v.labelProp] = "";
        }
      });
      if (Object.keys(obj).length === 0) return
      Object.keys(obj).forEach(item => {
        this.$set(this.formQuery, `${item}`, obj[item]);
      });
    },
    /**
     * @description: 动态计算行数
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    computedRow () {
      let formOptions = this.formOptions;
      let result = this.arrTrans(4, formOptions);
      let mode = this.mode;
      if (mode == "ordinary") {
        this.$set(this, "options", [result[0]]);
        return false;
      }
      this.$set(this, 'options', [])
      this.$set(this, "options", result);
    },

    /**
     * @description:一维数组转化为二维数组并返回
     * @param {*} num 每个二位数组中数据数
     * @param {*} arr 源数据
     * @return {*}
     * @author: GleenLey
     */
    arrTrans (num, arr) {
      const iconsArr = []; // 声明数组
      arr.forEach((item, index) => {
        let page = Math.floor(index / num); // 计算该元素为第几个素组内
        if (!iconsArr[page]) {
          // 判断是否存在
          iconsArr[page] = [];
        }
        //如果当前列span 加当前span 大于 24 就放到下一行
        let span = arr.length ? 6 : 0;
        iconsArr[page].forEach(row => {
          span += row.span ? Number(row.span) : 6;
        });

        page = span > 24 ? (page += 1) : page;
        if (!iconsArr[page]) {
          iconsArr[page] = [];
        }
        iconsArr[page].push(item);
      });
      return iconsArr;
    },

    /**
     * @description: 当前被选中的所有节点
     * @param {*} nodes
     * @return {*} nodes
     * @author: GleenLey
     */
    emitTreeNode (nodes, data, checked, indeterminate, itemOptions) {
      this.$emit(
        "getTreeNodes",
        nodes,
        data,
        checked,
        indeterminate,
        itemOptions
      );
    },
    /**
     * @description: 当前被点击的节点
     * @param {*} node
     * @return {*} node
     * @author: GleenLey
     */
    selectTreeClick (node) {
      this.$emit("getCurrentTreeNode", node);
    },

    /**
     * @description: el-select focus事件
     * @param {*} obj
     * @return {*}
     * @author: GleenLey
     */
    handleFocus (obj) {
      this.$emit("handleFocus", obj);
    },

    /**
     * @description: 设置默认值
     * @param {*} obj
     * @return {*}
     * @author: GleenLey
     */
    setOptions (obj) {
      this.$emit("setOptions", obj);
    },
    setChildrenItemValue (index) {
      this.$refs.infoFormItem[index].setCurrentValue()
    },
    clearChildrenValue (index) {
      this.$refs.infoFormItem[index].clearValue()
    },
    /**
     * @description: 切换普通和复杂模式
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    changeSlide () {
      if (this.isRotate) {
        this.rotate = !this.rotate;
        let self_mode = this.mode;
        this.mode = self_mode == "ordinary" ? "complex" : "ordinary";
        this.$nextTick(() => {
          this.computedRow();
        })
        // this.addInitValue();
        this.$emit("changeSlide", this.rotate);
      }
    },

    /**
     * @description: el-select 下拉组件触发加载更多事件
     * @param {*} type
     * @return {*}
     * @author: GleenLey
     */
    loadMoreData (type) {
      this.$emit("loadMore", type);
    },

    /**
     * @description: 获取值
     * @param {*}
     * @return {*}
     * @author: GleenLey
     */
    handleSearchData () {
      let mode = this.mode;
      // 简单模式 / 复杂模式
      if (mode == "ordinary" || mode == "complex") {
        let obj = {};
        let currentOptions =
          mode == "ordinary" ? this.options[0] : this.formOptions;
        currentOptions?.forEach(v => {
          // obj[v.prop] = this.formQuery[v.prop];

          if (v.thousands) {
            let currentValue = JSON.parse(
              JSON.stringify(this.formQuery[v.prop])
            );
            if (Array.isArray(currentValue)) {
              currentValue[0] = currentValue[0]
                ? currentValue[0].replace(/,/g, "")
                : "";
              currentValue[1] = currentValue[1]
                ? currentValue[1].replace(/,/g, "")
                : "";
            } else {
              currentValue = currentValue ? currentValue.replace(/,/g, "") : "";
            }
            obj[v.prop] = currentValue;
          }
          if (v.labelProp) {
            obj[v.labelProp] = this.formQuery[v.labelProp];
          }
          obj[v.prop] = {
            value: typeof this.formQuery[v.prop] === 'object' && this.formQuery[v.prop] !== null
            ? ['el-date-picker', 'el-input-cost', 'el-select'].includes(v.element)
              ? this.formQuery[v.prop].join(',')
              : this.formQuery[v.prop]
            : this.formQuery[v.prop],
            type: v.element,
            configId: v.bizid
          }
        });
        obj.type = this.searchType
        return obj;
      }
    },
    isType(v) {
      let  typeMapping = {
        'el-input': 'like',
        'el-select': 'equal',
        'el-input-cost': 'range',
        'el-date-time': 'range',
        'el-date-picker': 'range',
      };
      return typeMapping[v] || 'unknown';
    },
    // 获取扁平化的数据
    getFlattenTree(prop) {
      const index = this.$refs.infoFormItem.findIndex(
        (val) => val.itemOptions.prop === prop
      );
      return this.$refs.infoFormItem[index].getFlattenTree();
    },
    /**
     * 下拉框数据改变
     * @param param0 
     */
    handleSelectChange({ value, prop }) {
      this.$emit("selectChange", { value, prop });
    }
  },
};
</script>

<style lang="scss" scoped>
.search-form-box {
  width: 100%;
  // padding: 5px 0 0;
  .leftview {
    padding-right: 10px;
    padding-left: 6px;
    .but-warp{
      flex: 1;
      .btn-box{
        float: right;
      }
    }
  }
  .arrow {
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 1px solid #dcdfe6;
    text-align: center;
    margin-left: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .arrow-icon {
    font-size: 16px;
  }

  .up {
    transition: all 0.5s;
  }

  .down {
    transform: rotate(-180deg);
    transition: all 0.5s;
  }
  .btn-box {
    display: flex;
    padding-left: 8px;

    @mixin commen-btn {
      width: 80px;
      height: 32px;
      font-size: 14px;
      padding: 0;
      overflow: hidden;
      // margin-bottom: 10px;
    }
    .btn-search {
      @include commen-btn;
      // background-color: #1F7FFF !important;
      &:hover {
        // background-color: #478BFE !important;
        color: #ffffff !important;
      }
      &:active {
        // background-color: #1070E1 !important;
      }
      .icon {
        margin-right: 8px;
      }
    }
    .btn-search-width {
      @include commen-btn;
      background-color: #1F7FFF !important;
      &:hover {
        background-color: #478BFE !important;
        color: #ffffff !important;
      }
      &:active {
        background-color: #1070E1 !important;
      }
      .icon {
        margin-right: 8px;
      }
      width: 150px;
    }
    .btn-reset {
      overflow: hidden;
      margin-left: 12px;
      &:hover {
        border: 1px solid #478BFE;
        color: #478BFE;
      }
      &:active {
        border: 1px solid #1070E1;
        color: #1070E1;
      }
      @include commen-btn;
      .icon {
        margin-right: 8px;
      }
    }
  }

  // .el-row {
  //   margin-bottom: 10px;
  // }
  // .el-row:last-child {
  //     margin-bottom: 0;
  // }

  //修改默认样式
  .el-form-item.el-form-item--small {
    margin-bottom: 15px;
    // .el-form-item__content{
    //   line-height: 33px;
    //   height: 33px;
    // }
  }
  .form-item {
  .el-input--small .el-input__inner {
    height: 32px;
    line-height: -1px; // 不能为负值,主要解决ie
  }
  .el-select .el-input__inner:focus {
    height: 32px;
    line-height: 30px;
  }
  .el-select .el-input__inner {
    height: 32px;
    line-height: 30px;
  }
  //左边输入区
  .el-input__inner {
    color: #363636;
  }
  .el-select {
    width: 100%;
  }
  .el-range-editor.el-input__inner {
    width: 100%;
  }
  ::v-deep .customDate {
    display: flex;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    height: 32px;
    .el-input__inner {
      border: none !important;
      height: 27px;
      line-height: 27px !important;
      text-align: center;
    }
    .datePickerEnd {
      .el-input__prefix {
        display: none;
      }
    }
  }

  ::v-deep .el-input--suffix .el-input__inner {
    padding-right: 15px !important;
    padding: 10px 5px!important;
  }
}
}
</style>
