<template>
  <div class="manual-review-issues">
    <!-- 子标签页导航 -->
    <el-tabs v-model="activeTab" class="manual-review-tabs important" type="card">
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.key"
        :name="tab.key"
      >
        <span slot="label">
          {{ tab.label }}
          <el-badge
            :value="getTabCount(tab.key)"
            :max="99"
            class="tab-badge"
            v-if="getTabCount(tab.key) > 0"
          />
        </span>

        <!-- 标签页内容 -->
        <div class="tab-content">
          <div v-if="getTabItems(tab.key).length === 0" class="empty-state">
            <i class="el-icon-document-remove empty-icon"></i>
            <p class="empty-text">暂无{{ tab.label }}</p>
          </div>
          <div v-else class="issues-list">
            <manual-review-item
              v-for="item in getTabItems(tab.key)"
              :key="item.id"
              :item="item"
              @locate="handleLocateOriginal"
              @edit="handleEditItem"
              @save="handleSaveItem"
              @delete="handleDeleteItem"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ManualReviewItem from './manualReviewItem.vue'

export default {
  name: 'ManualReviewIssues',
  components: {
    ManualReviewItem
  },
  props: {
    issues: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeTab: 'all',
      tabs: [
        { key: 'all', label: '全部' },
        { key: 'unmodified', label: '未修改' },
        { key: 'modified', label: '已修改' }
      ]
    }
  },
  computed: {
    // 全部问题
    allIssues() {
      return this.issues || [];
    },

    // 未修改的问题
    unmodifiedIssues() {
      return this.allIssues.filter(item => !item.isModified);
    },

    // 已修改的问题
    modifiedIssues() {
      return this.allIssues.filter(item => item.isModified);
    }
  },
  methods: {
    getTabCount(tabKey) {
      switch (tabKey) {
        case 'all':
          return this.allIssues.length;
        case 'unmodified':
          return this.unmodifiedIssues.length;
        case 'modified':
          return this.modifiedIssues.length;
        default:
          return 0;
      }
    },

    getTabItems(tabKey) {
      switch (tabKey) {
        case 'all':
          return this.allIssues;
        case 'unmodified':
          return this.unmodifiedIssues;
        case 'modified':
          return this.modifiedIssues;
        default:
          return [];
      }
    },

    handleLocateOriginal(treeNodeId) {
      // 向父组件传递定位原文事件
      this.$emit('locate', treeNodeId);
    },

    handleEditItem(itemId) {
      // 向父组件传递编辑事件
      this.$emit('edit-item', itemId);
    },

    handleSaveItem(data) {
      // 向父组件传递保存事件
      this.$emit('save-item', data);

      // 更新本地数据，标记为已修改
      const item = this.allIssues.find(issue => issue.id === data.id);
      if (item) {
        item.content = data.content;
        item.isModified = true;
      }
    },

    handleDeleteItem(itemId) {
      // 向父组件传递删除事件
      this.$emit('delete-item', itemId);
    }
  }
}
</script>

<style scoped lang="scss">
.manual-review-issues {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.manual-review-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;

  ::v-deep &.el-tabs.el-tabs--top {
    height: 100%;
  }
  &.important ::v-deep .el-tabs__header {
    margin: 0;
    padding: 5px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;
  }
  &.important ::v-deep .el-tabs__content {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }
  &.important ::v-deep .el-tab-pane {
    height: 100%;
    overflow-y: auto;
  }
  &.important ::v-deep .el-tabs__nav-scroll {
    height: 32px;
    line-height: 32px;
  }
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.tab-badge {
  ::v-deep .el-badge__content {
    background-color: #f56c6c;
    border: none;
    font-size: 10px;
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    min-width: 16px;
  }
}

.tab-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 20px;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 14px;
    margin: 0;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .manual-review-tabs {
    ::v-deep .el-tabs__header {
      padding: 4px 8px;
    }

    ::v-deep .el-tabs__item {
      padding: 0 12px;
      margin-right: 4px;
      font-size: 12px;
    }
  }

  .tab-content {
    padding: 12px;
    max-height: 100%;
    overflow-y: auto;
  }

  .tab-label {
    font-size: 12px;
    gap: 4px;
  }
}

// 滚动条样式
.tab-content::-webkit-scrollbar,
.issues-list::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track,
.issues-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb,
.issues-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover,
.issues-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
