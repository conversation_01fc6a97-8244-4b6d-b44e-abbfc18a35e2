<template>
  <!-- <DynamicTable :columns="tableColumns" :tableData="tableData" border class="statistics-dept" show-summary
    :summary-method="getSummaries" /> -->
  <baseTable
    ref="mainTable"
    :tableColumn="tableColumns"
    :tableData="tableData"
    :height="655"
    :showOverflow="true"
    align="right"
    headerAlign="center"
    highlightCurrentRow
    showFooter
    :footerMethod="getSummaries"
    class="statistics-dept"
  ></baseTable>
</template>

<script>
import baseTable from "@/components/vxeTable/baseTable.vue";
export default {
  name: "statisticsDept",
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  components: {
    baseTable,
  },
  data() {
    return {
      tableColumns: [
        {
          label: '序号',
          type: 'seq',
          width: '80',
          align: 'center'
        },
        {
          label: '部门名称',
          prop: 'deptName',
          align: 'left'
        },
        {
          label: '经费卡数量',
          prop: 'count',
          align: 'center'
        },
        {
          label: '经费卡预算总额(元)',
          prop: 'selfPurchaseType',
          formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
          align: 'right'
        },
        {
          label: '经费卡已申请金额(元)',
          prop: 'amount',
          formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
          align: 'right'
        },
        {
          label: '预算经费卡使用率(%)',
          prop: 'rate',
          align: 'center'
        },
      ],
    }
  },
  methods: {
    formatMoney(row, column, cellValue) { 
      return this.$formatMoney(cellValue);
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计';
          return;
        }
        if (!column.property
        ) {
          sums[index] = "";
          return;
        }
        // 从 data 中计算当前列的合计
        const sum = data.reduce((acc, item) => {
          const value = parseFloat(item[column.property
          ]);
          return isNaN(value) ? acc : acc + value;
        }, 0);
        const label = column.title;
        if (label.indexOf("额") !== -1) {
          // 金额、面积格式化
          sums[index] = this.$formatMoney(sum);
        } else if(label.indexOf("数") !== -1 || label.indexOf("率") !== -1) {
          sums[index] = sum;
        } else {
          sums[index] = "";
        }
      });
      return [sums];
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
