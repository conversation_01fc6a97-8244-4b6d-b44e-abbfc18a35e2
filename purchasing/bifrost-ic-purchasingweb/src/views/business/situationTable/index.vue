<template>
  <div>
    <page ref="situationPage">
      <template #pageContent>
        <LayoutTem :isPageShow="false" :isFilterShow="false">
          <template #button>
            <div class="title-content">
              <div class="title">{{ tableTitle }}</div>
              <div class="description">说明：本统计表以所有已成交项目为基础进行的统计</div>
            </div>
            <div class="search-content">
              <searchForm ref="searchForm" :formOptions="formOptions" @onSearch="handleSearch"
                @selectChange="handleSelectChange" />
            </div>
          </template>
          <template #main>
            <template v-if="formQuery.queryType === '按采购标的分类统计'">
              <ProcurementSubjectAll v-if="scopeInAll" :tableData="allTableData" />
              <ProcurementSubjectDept v-if="scopeInDept" :tableData="tableData" />
            </template>
            <template v-if="formQuery.queryType === '按采购组织形式统计'">
              <procurementOrganizationAll v-if="scopeInAll" :tableData="allTableData" />
              <procurementOrganizationDept v-if="scopeInDept" :tableData="tableData" />
            </template>
            <template v-if="formQuery.queryType === '按采购执行机构统计'">
              <procurementExecutionAll v-if="scopeInAll" :tableData="allTableData" />
              <procurementExecutionDept v-if="scopeInDept" :tableData="tableData" />
            </template>
            <template v-if="formQuery.queryType === '按采购方式统计'">
              <procurementMethodAll v-if="scopeInAll" :tableData="allTableData" />
              <procurementMethodDept v-if="scopeInDept" :tableData="tableData" />
            </template>
          </template>
        </LayoutTem>
      </template>
    </page>
  </div>
</template>

<script>
import searchForm from '../reportSummer/regionSearch/searchForm.vue';
import ProcurementSubjectAll from './components/procurementSubjectAll.vue';
import ProcurementSubjectDept from './components/procurementSubjectDept.vue';
import procurementOrganizationAll from './components/procurementOrganizationAll.vue';
import procurementOrganizationDept from './components/procurementOrganizationDept.vue';
import procurementExecutionAll from './components/procurementExecutionAll.vue';
import procurementExecutionDept from './components/procurementExecutionDept.vue';
import procurementMethodAll from './components/procurementMethodAll.vue';
import procurementMethodDept from './components/procurementMethodDept.vue';
import { cgbdData, cgzzxsData, cgzxjgData, cgfsData } from './json/json'
export default {
  name: 'BusinessSituationTable',
  components: {
    searchForm,
    ProcurementSubjectAll,
    ProcurementSubjectDept,
    procurementOrganizationAll,
    procurementOrganizationDept,
    procurementExecutionAll,
    procurementExecutionDept,
    procurementMethodAll,
    procurementMethodDept,
  },
  data() {
    return {
      currentTab: '按采购标的分类统计',
      formOptions: [
        {
          label: '年度',
          element: 'el-select',
          prop: 'year',
          initValue: new Date().getFullYear().toString(),
          rowSpan: 8,
          options: (() => {
            const currentYear = new Date().getFullYear();
            const options = [];
            for (let i = currentYear - 10; i <= currentYear + 10; i++) {
              const year = i.toString();
              options.push({ label: year, value: year });
            }
            return options;
          })()
        },
        {
          label: '统计范围',
          element: 'el-select',
          prop: 'queryScope',
          initValue: '各部门',
          rowSpan: 8,
          options: [
            { label: '全校', value: '全校' },
            { label: '各部门', value: '各部门' },
          ]
        },
        {
          label: '查询方式',
          element: 'el-select',
          prop: 'queryType',
          rowSpan: 8,
          initValue: '按采购标的分类统计',
          options: [
            { label: '按采购标的分类统计', value: '按采购标的分类统计' },
            { label: '按采购组织形式统计', value: '按采购组织形式统计' },
            { label: '按采购执行机构统计', value: '按采购执行机构统计' },
            { label: '按采购方式统计', value: '按采购方式统计' },
          ]
        }
      ],
      formQuery: {
        year:  new Date().getFullYear().toString(),
        queryScope: '各部门',
        queryType: '按采购标的分类统计'
      },
      allTableData: [],
      tableData: []
    }
  },
  computed: {
    tableTitle() {
      return this.formQuery.queryScope === '全校' ? '深圳大学采购项目成交情况表' : '深圳大学各院系、部门采购项目成交情况表'
    },
    scopeInAll() {
      return this.formQuery.queryScope === '全校'
    },
    scopeInDept() {
      return this.formQuery.queryScope === '各部门'
    }
  },
  watch: {
    'formQuery': {
      handler(val) {
          if (val.queryType === '按采购标的分类统计') {
            this.tableData = cgbdData
            this.allTableData = [{
              selfPurchaseType: '252696399.81',
              '1': '1151',
              '2': '160752300.55',
              '3': '119',
              '4': '8993306.27',
              '5': '1006',
              '6': '69339281.68'
            }]
          } else if (val.queryType === '按采购组织形式统计') {
            this.tableData = cgzzxsData
            this.allTableData = [{
              selfPurchaseType: '252696399.81',
              '1': '43',
              '2': '59502459',
              '3': '168',
              '4': '73292565.58',
              '5': '2065',
              '6': '106289863.92'
            }]
          } else if (val.queryType === '按采购执行机构统计') {
            this.tableData = cgzxjgData
            this.allTableData = [{
              selfPurchaseType: '252696399.81',
              '1': '43',
              '2': '59502459',
              '3': '168',
              '4': '73292565.58',
              '5': '2065',
              '6': '106289863.92'
            }]
          } else if (val.queryType === '按采购方式统计') {
            this.tableData = cgfsData
            this.allTableData = [
              {
                  "selfPurchaseType": "252696399.81",
                  "1": "15",
                  "2": "44559980",
                  "3": "27",
                  "4": "14182479",
                  "5": "1",
                  "6": "760000",
                  "7": "154",
                  "8": "67707100.74",
                  "9": "11",
                  "10": "4220664.85",
                  "11": "3",
                  "12": "1364800",
                  "13": "768",
                  "14": "39226840.23",
                  "15": "185",
                  "16": "9866481.44",
                  "17": "1048",
                  "18": "53870672.98",
                  "19": "64",
                  "20": "3325869.27",
                  "21": "1",
                  "22": "96000",
                  "23": "0",
                  "24": "0",
                  "25": "0",
                  "26": "0",
                  "27": "0",
                  "28": "0"
              }
            ]
          }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    changeTab(tab) {
      this.currentTab = tab.name;
    },
    handleSearch(obj) {

    },
    handleSelectChange({ value, prop }) {
      // 处理下拉选择变化
      this.formQuery[prop] = value
    },
  }
}
</script>

<style lang="scss" scoped>
.title-content {
  width: 100%;
  text-align: center;
  margin: 20px 0;

  .title {
    font-size: 20px;
  }

  .description {
    margin-bottom: 20px;
    width: 100%;
    text-align: start;
    padding-top: 10px;
    font-size: 14px;
    color: #666;
  }
}
</style>
