/*
 * @Description: el-select 下拉加载更多
 * @Version: 1.0
 * @Autor: sharewine
 * @Date: 2021-05-11 14:39:15
 * @LastEditors: sharewine
 * @LastEditTime: 2021-05-11 14:47:57
 */
import Vue from 'vue'
export default {}.install = (Vue, options = {}) => {
  Vue.directive('selectLoadmore', {
    inserted(el, binding) {
      // 获取element-ui定义好的scroll盒子
      const SELECTDOWN_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
      SELECTDOWN_DOM.addEventListener('scroll', function() {
        const CONDITION = this.scrollHeight - this.scrollTop <= this.clientHeight
        if (CONDITION) {
          binding.value()
        }
      })
    }
  })
}
