/**
* css配置
* 有些后面加数字的都是mixin.scss 使用的 用于主题不一样的css配置
* by <PERSON><PERSON>
*/
// 几种主题色
// #8092a5 灰色头
// #18c79c 绿色诶
// #1a3a73 深蓝
$bg-color-theme: #30589e; // #409eff;   // #6f7748;
$bg-color-theme1: #fff; // #ededed;
$bg-color-theme2: #24292e; // #001529; // #1a4261; // #334756;
$bg-color-theme3: #353944; // url('~@/assets/hearder1.jpg');

// head 右边元素经过的背景色跟字体色
$headblock-bgcolor-hover: #2d4e88; // #098fd8;  //0093dd
$headblock-bgcolor-hover1: #f7f7f7;// #65707b; // #e0e2e2;
$headblock-bgcolor-hover2: #0b212d; // #253842;
$headblock-bgcolor-hover3: rgba(0, 0, 0, 0.4);

$headblock-color-hover: #fff;
$headblock-color-hover1: #0093dd;
$headblock-color-hover2: #fff;
$headblock-color-hover3: #fff;

// Head字体颜色
$head-color: #fff;
$head-color1: #515a6e;
$head-color2: #fff;
$head-color3: #fff;

// menu 为main的时候 的menu颜色
$menu-bgcolor: #001529; // #304156; // #1c3448;
$menu-bgcolor-hoverx: #1890ff;
$menu-title-bgcolor-hover: #152334;

// 菜单宽度
$menu-width: 225px;  // 245
$menu-width-1366: 180px;

// 头部大小
$headHeight: 68px;
$headHeight-1366: 48px;

// 系统名字大小以及定位
$sys-name: 22px;
$sys-name-1366: 16px;
$sys-name-top: 0px 0 0 2px;// 0px 0 0 10px;



// 菜单字体weight
$menu-font-weight: bold;

// 表格边框颜色
$table-border-color: #DDDDDD; // #ebeef5;

// menu父菜单高度
$menu-item-h: 50px;
$menu-item-h-1366: 44px;

// 子菜单高度
$h-ver-menu-child: 40px;
$h-ver-menu-child-1366: 40px;

$menu-margin: 1px 0;
$menu-margin3: 1px 0;

$fz-ver-menu-child: 13px;

// 菜单背景颜色
$menu-gbcolor: #fff;
$menu-gbcolor3: #353944; // #1f2228;
$menu-gbcolor-img: #353944; // url('~@/assets/menu-bg.png');
$menu-gbcolor3-fa: #2b2e35;
$menu-bottom-gb3: #2c313a;

// 菜单鼠标经过颜色
$fc-menu-hover: #1baffe;
$fc-menu-hover3: #fff;

// 菜单激活经过颜色
$menu-bgcolor-hover: #ecf5fe;
$menu-bgcolor-hover3: #1890ff; // url('~@/assets/menu-hover.png');

// 菜单字体颜色
$menu-color: #515a6e;
$menu-color3: #efefef;