/**
* 用于不同主题的css函数
* by <PERSON><PERSON>
**/
@import './variable.scss';

// 通过该函数设置主题颜色，后期方便统一管理
// 头部颜色
@mixin bg_color($color){ 
  background-color:$color;
  [data-theme="theme"] & {
    background-color: $bg-color-theme;
  }
  [data-theme="theme1"] & {
    background-color: $bg-color-theme1;
  }
  [data-theme="theme2"] & {
    background-color: $bg-color-theme2;
  }
  [data-theme="theme3"] & {
    background-color: 0;
    background: $bg-color-theme3; // url('~@/assets/hearder1.jpg') 100% 100%;
    background-size: 100% 100%;
  }
}

// 头部底部boder
@mixin bg_bb_color(){ 
  border-bottom: 0;
  [data-theme="theme"] & {
    border-bottom: 1px solid $bg-color-theme;
  }
  [data-theme="theme1"] & {
    border-bottom: 1px solid #eee;
  }
  [data-theme="theme2"] & {
    border-bottom: 1px solid $bg-color-theme2;
  }
  [data-theme="theme3"] & {
    border-bottom: 0;
    // box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);
    // z-index: 1;
  }
}

// 头部颜色
@mixin header_color() {
  [data-theme="theme"] & {
    color: $head-color;
  }
  [data-theme="theme1"] & {
    color: $head-color1;
  }
  [data-theme="theme2"] & {
    color: $head-color2;
  }
  [data-theme="theme3"] & {
    color: $head-color3;
  }
}

// 头部右栏块的鼠标经过背景
@mixin headblock-bgcolor-hover() {
  [data-theme="theme"] & {
    background: $headblock-bgcolor-hover;
  }
  [data-theme="theme1"] & {
    background: $headblock-bgcolor-hover1;
  }
  [data-theme="theme2"] & {
    background: $headblock-bgcolor-hover2;
  }
  [data-theme="theme3"] & {
    background: $headblock-bgcolor-hover3;
  }
}



// 头部右栏块的鼠标经过字体颜色
@mixin headblock-color-hover () {
  [data-theme="theme"] & {
    color: $headblock-color-hover;
  }
  [data-theme="theme1"] & {
    color: $headblock-color-hover1;
  }
  [data-theme="theme2"] & {
    color: $headblock-color-hover2;
  }
  [data-theme="theme3"] & {
    color: $headblock-color-hover3;
  }
}
@mixin headblock-hover() {
  @include headblock-bgcolor-hover();
  @include headblock-color-hover();
}

// 菜单背景颜色
@mixin menu-bg () {
  [data-theme="theme"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme1"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme2"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme3"] & {
    background: $menu-gbcolor-img;
  }
}

@mixin aside-bg () {
  [data-theme="theme"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme1"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme2"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme3"] & {
    background: $menu-gbcolor3;
  }
}

// 菜单鼠标经过字体颜色
@mixin menu-fc-hover () {
  [data-theme="theme"] & {
    color: $fc-menu-hover;
  }
  [data-theme="theme1"] & {
    color: $fc-menu-hover;
  }
  [data-theme="theme2"] & {
    color: $fc-menu-hover;
  }
  [data-theme="theme3"] & {
    color: $fc-menu-hover3;
  }
}

// 菜单激活经过背景颜色
@mixin menu-bg-hover () {
  [data-theme="theme"] & {
    background: $menu-bgcolor-hover;
  }
  [data-theme="theme1"] & {
    background: $menu-bgcolor-hover;
  }
  [data-theme="theme2"] & {
    background: $menu-bgcolor-hover;
  }
  [data-theme="theme3"] & {
    background: $menu-bgcolor-hover3;
  }
}
// 父级菜单激活经过背景颜色
@mixin menu-bg-hover-fa () {
  [data-theme="theme"] & {
    background: $menu-bgcolor-hover;
  }
  [data-theme="theme1"] & {
    background: $menu-bgcolor-hover;
  }
  [data-theme="theme2"] & {
    background: $menu-bgcolor-hover;
  }
  [data-theme="theme3"] & {
    background: $menu-gbcolor3-fa;
  }
}

// 菜单字体颜色
@mixin menu-fc () {
  [data-theme="theme"] & {
    color: $menu-color;
  }
  [data-theme="theme1"] & {
    color: $menu-color;
  }
  [data-theme="theme2"] & {
    color: $menu-color;
  }
  [data-theme="theme3"] & {
    color: $menu-color3;
  }
}

@mixin menu_bottom_gb () {
  [data-theme="theme"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme1"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme2"] & {
    background: $menu-gbcolor;
  }
  [data-theme="theme3"] & {
    background: $menu-bottom-gb3;
  }
}

// 菜单margin
@mixin menu-margin () {
  [data-theme="theme"] & {
    margin: $menu-margin;
  }
  [data-theme="theme1"] & {
    margin: $menu-margin;
  }
  [data-theme="theme2"] & {
    margin: $menu-margin;
  }
  [data-theme="theme3"] & {
    margin: $menu-margin3;
  }
}

// 用于第四个主题有头像的区分
@mixin menu-height () {
  [data-theme="theme"] & {
    height: 100%;
  }
  [data-theme="theme1"] & {
    height: 100%;
  }
  [data-theme="theme2"] & {
    height: 100%;
  }
  [data-theme="theme3"] & {
    height: calc(100% - 100px);
  }
}

@mixin menu-user () {
  [data-theme="theme"] & {
    display: none;
  }
  [data-theme="theme1"] & {
    display: none;
  }
  [data-theme="theme2"] & {
    display: none;
  }
  [data-theme="theme3"] & {
    display: block;
  }
}

@mixin drawer-user () {
  [data-theme="theme"] & {
    display: block;
  }
  [data-theme="theme1"] & {
    display: block;
  }
  [data-theme="theme2"] & {
    display: block;
  }
  [data-theme="theme3"] & {
    display: none;
  }
}
