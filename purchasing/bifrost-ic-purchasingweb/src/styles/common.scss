// @import './reset.scss';    // reset
@import './dwreset.scss';  // 公用的样式
@import './newIconFont.scss'; //  新字体图标
// @import './elereset.scss'; // 对element-ui 的二次样式修改
// @import './adminLTE.scss';
@import url('../icons/ali-icon/iconfont.css');
.pull-right {
  float: right !important;
}
.flex-column {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.flex-1 {
  overflow: hidden;
  flex: 1;
}
.mleft-6 {
  margin-left: 6px;
}
//
.width-200 {
  width: 200px;
}
.width-300 {
  width: 300px;
}
.height-36 {
  height: 36px;
}
// border blue
.border-blue {
  border: 1px solid #74cef7;
}
.border-top-blue {
  border-top: 1px solid #74cef7;
}
.border-bottom-blue {
  border-bottom: 1px solid #74cef7;
}
.border-left-blue {
  border-left: 1px solid #74cef7;
}
.border-right-blue {
  border-right: 1px solid #74cef7;
}
// gray
.border-gray {
  border: 1px solid #d2d2d2;
}
.border-top-gray {
  border-top: 1px solid #d2d2d2;
}
.border-bottom-gray {
  border-bottom: 1px solid #d2d2d2;
}
.border-left-gray {
  border-left: 1px solid #d2d2d2;
}
.border-right-gray {
  border-right: 1px solid #d2d2d2;
}

// padding
.padding-5 {
  padding: 5px;
}
.padding-b5 {
  padding-bottom: 5px;
}
.padding-t5 {
  padding-top: 5px;
}
.padding-l5 {
  padding-left: 5px;
}
.padding-r5 {
  padding-right: 5px;
}
.padding-10 {
  padding: 10px;
}
.padding-b10 {
  padding-bottom: 10px;
}
.padding-t10 {
  padding-top: 10px;
}
.padding-l10 {
  padding-left: 10px;
}
.padding-r10 {
  padding-right: 10px;
}
// flex
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-center {
  justify-content: center;
  align-items: center;
}
.flex-row-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.warning-popover {
  background-color: #FDF6EC;
  color: #E6A23C;
}
.radio-flex-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
