//使用规则:<el-dialog append-to-body #{id}></el-dialog>
//建议id使用(组件名或者文件名dynamicDlg + 自定义global)作为唯一标识
#dynamicDlg-global {
  // 这里是动态组件的内嵌样式块
  #attach-audit-extent .column-bottom {
    height: calc(100% - 39px) !important;
  }
  #form-detail-tab-attach-bx .column-bottom {
    height: calc(100% - 38px) !important;
  }
  #projectQuota .quotaInputHid .el-input__inner{border: 0!important;}
  #projectQuota .muBiaoCalss{ width: 100%;padding-top: 10px;padding-bottom: 10px;
    padding-right: 10px;margin-bottom: 10px;border: 1px solid #ddd
  }
}
#demoDialogA-elDialoga {
  .dialog-demo-span{ color: green;}
}
#demoDialogB-elDialogb {
  .dialog-demo-span{ color: red;}
}
#departmentDialogId .common-page .buttons-normal { padding:0 0 0 0px!important; }
#departmentDialogId .common-page .main-border { padding:0 0 0 0px!important; }

#attShiftId .common-page .buttons-normal { padding:0 0 0 0px }
.noDlgHeader .el-dialog .el-dialog__header { border: none!important; }
.refundNoticeTitle {
  background-color: #dfdfdf;
  line-height: 100%;
  font-size: 16px;
  font-weight: bold;
  padding: 8px;
  margin-bottom: 10px;
}
.refundNoticePayeeTitle{
  background-color: #dfdfdf;
  line-height: 100%;
  font-size: 16px;
  font-weight: bold;
  padding: 8px;
}
#incPayeeList .common-page .column-bottom{
  height: 100% !important;
}
.refundRechangeBizTitle {
  background-color: #dfdfdf;
  line-height: 100%;
  font-size: 16px;
  font-weight: bold;
  padding: 8px;
  margin-bottom: 10px;
}
.refundRechangeBizPayeeTitle {
  background-color: #dfdfdf;
  line-height: 100%;
  font-size: 16px;
  font-weight: bold;
  padding: 8px;
  margin-top: 5px;
}

#refundNoticeDialog .common-page .buttons-normal { padding:0 0 0 0px!important; }

#refundRechangeBizDialog .common-page .buttons-normal { padding:0 0 0 0px!important; }

#refundRechangeBizDialog .common-page .column-bottom { height: 100% !important; }

#refundRechangeBizDialog .common-page .main-border { padding-top: 0px!important; }

.common-dlg-form #form-detail-tab-attach-tkch #attach-audit-extent .buttons-normal {
  padding-top: 0;
}

#dynamicDlg-loginNotice .el-dialog .el-dialog__header { border:none !important;}
#dynamicDlg-loginNotice .noticeHeader { border-bottom:1px solid #EBEEF5; }
#dynamicDlg-loginNotice .title { font-size:18px; margin-bottom:10px;}
#dynamicDlg-loginNotice .noticeHeader .el-row { text-align: center; padding-bottom: 5px;}
#dynamicDlg-loginNotice .content { height: 630px; padding:20px; overflow-y: auto;}
#dynamicDlg-loginNotice .attachment{ display:flex;padding-left:20px; margin-top: 10px;}
#dynamicDlg-loginNotice .noticeTime{ text-align: center}
#dynamicDlg-loginNotice .attachment ul li { list-style: none; padding-left:5px; line-height: 20px;}

#dynamicDlg-inquireNotice .el-dialog .el-dialog__header { border:none !important;}
#dynamicDlg-inquireNotice .el-radio-button:first-child .el-radio-button__inner {border-left:none;border-radius: 0;}
#dynamicDlg-inquireNotice .el-radio-button:last-child .el-radio-button__inner {border-left:none;border-radius: 0;}
#dynamicDlg-inquireNotice .el-radio-button__inner{ border: none;}
#dynamicDlg-inquireNotice .el-radio-button {margin:0 10px;}
#dynamicDlg-inquireNotice .el-radio-button__orig-radio:checked+.el-radio-button__inner { background: #ECF5FF; color: #409EFF; border-radius: 5px; box-shadow:none}
#dynamicDlg-inquireNotice .common-page .main-border{ padding:0}
#dynamicDlg-inquireNotice .el-table th.el-table__cell{background-color:#f0f5ff;}
#dynamicDlg-ExtractExpert {
  .el-dialog .el-dialog__body { padding:0}
  .el-tabs__header{
    margin:20px 20px 0;
  }
  .scrollContent{
    height: 650px;
    padding: 20px;
    overflow-y: auto;
  }
  .el-table{
    height:100%
  }
  .headerStyle {
    display: flex;
    padding-bottom: 12px;
    padding-left: 10px;
    align-items: center;
    .icon-line {
      width: 3px;
      height: 18px;
      display: block;
      background-color: #409EFF;
    }

    h4 {
      margin: 0;
      font-size: 14px;
      line-height: 20px;
      font-weight: bold;
      padding-left: 10px;
      color: #606266;
    }
  }
  .ftTable .el-form-item--small.el-form-item {
    margin-bottom:0
  }
}

//带tab的样式
.dialog-tab-style{
  .el-dialog .el-dialog__body{ padding: 20px 14px 20px 30px }
  .el-button--small{ padding: 9px 30px; }
}

.headerStyle {
  display: flex;
  padding-bottom: 12px;
  .icon-line {
    width: 3px;
    height: 20px;
    display: block;
    background-color: #409EFF;
  }

  h4 {
    margin: 0;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;
    padding-left: 10px;
    color: #000;
  }
}

.auditDialogStyle {
  .auditDialogBox{
    display: flex;
    .auditDialogLeft{
      width: 68%;
      border:1px solid #DCDFE6;
    }
    .auditDialogRight{
      width: 32%;
      margin-left: 10px;
      border:1px solid #DCDFE6;
    }
    .auditLogBox{
      overflow-y:auto;
      padding:20px;
      border-bottom: 1px solid #DCDFE6;
    }
  }
}

//原原型带边框样式
.dialog-style1 {
  .el-tabs__header {
    margin: 0 0 10px;
  }
  .el-dialog .el-dialog__header{
    border: none;
  }
  .el-dialog .el-dialog__body{
    padding: 0px 20px 20px;
  }
  .dialog-style-border{
    border:1px solid #DCDFE6;
  }
}

.viewFileDialog {
  .el-dialog .el-dialog__header {
    border: 0;
  }
  .el-dialog .el-dialog__body{
    padding:0
  }
  .el-dialog .el-dialog__header .el-dialog__headerbtn{
    z-index: 10000 !important;
  }
  .aroundFileBox {
    padding:0 20px;
    height: 800px;
    display: flex;
    .LeftIframe{
      border: 1px solid #ebeef5;
    }
    .aroundLeft {
      height: 730px;
      width: 600px;

      iframe {
        border: none;
      }
    }
    .aroundRight {
      width: 850px;
      background: #fff;
      margin-left: 5px;
      .el-tabs__header{
        margin: 0;
      }
      .pur-primary{
        color: #409EFF;
        border: 1px solid #409EFF !important;
        background-color: #E8F4FF;
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 10px;
      }
    }
  }

  .headerStyle {
    display: flex;
    padding-bottom: 12px;
    .icon-line {
      width: 3px;
      height: 20px;
      display: block;
      background-color: #409EFF;
    }

    h4 {
      margin: 0;
      font-size: 14px;
      line-height: 20px;
      font-weight: bold;
      padding-left: 10px;
      color: #606266;
    }
  }

  .review-Box{
    border:1px solid #EBEEF5;
    height: 730px;
    padding:20px 10px;
    .buttonBox{
      .notMoreButton{
        column-gap:16px !important;
      }
      .el-button{
        color:#409EFF;
        border: 1px solid #409EFF !important;
        background-color:#E8F4FF;
        font-weight: bold;
        font-size: 14px;
      }
    }
  }

    .editTable  .el-form-item--small.el-form-item{
        margin-bottom: 0;
    }

    .editTable .el-form-item{
        margin-bottom: 0;
    }
    .editTable .el-table--small .el-table__cell {
        padding:3px 0;
    }

    .editTable .el-table__cell:first-child .cell{
        .el-form-item{
            margin-left: 20px;
        }
        .el-table__placeholder,.el-table__indent{
            display: none;
        }
    }
    .editTable .el-table__expand-icon{
        position: absolute;
        left: 0;
        top:12px;
    }
    .weightStyle .el-input__inner{
        text-align: center;
    }

    .right-box {
      border: 1px solid #EBEEF5;
      height: 723px;
      padding: 20px 10px;
    }

    .handleOpen{
      position: absolute;
      top: 0;
      width:100%;
      height:35px;
    }

    .aroundFileBox {
      display: flex;

      .aroundLeft {
        width: 40%
      }

      .aroundRight {
        width: 60%
      }
    }
}

.icon-common {
  cursor: pointer;
  font-weight: 700;
  font-size: 24px;
  margin: 0 3px;
}

.el-table .extractButton .el-button{
    padding:0 !important;
    border:0;
    background: none;
}

.el-table .extractButton .success{
  color:#70B603
}

.el-table .extractButton .error{
  color:#D9001B
}

.el-table .extractButton .is-disabled, .el-table .extractButton .is-disabled:focus, .el-table .extractButton .is-disabled:hover{
  color: #C0C4CC;
  background: none;
}

.el-table .extractButton .el-button:hover{
  background: none;
}

.editTableDialog {
  .iconBtn {
    i {
      cursor: pointer;
      margin: 0 3px;
    }

    .addicon {
      font-size: 26px;
      color: #67C23A;
    }

    .delicon {
      font-size: 26px;
      color: #F56C6C;
    }
    .viewicon{
      font-size: 22px;
      color: #409EFF;
    }

    .disicon {
      font-size: 26px;
      color: #999999;
    }

    .diricon {
      font-size: 22px;
      color: #409EFF;
    }
  }
  .editTable .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }

  .editTable .el-form-item {
    margin-bottom: 0;
  }

  .aroundFileBox {
    padding:0 20px;
    height: 800px;
    .aroundLeft {
      height: 730px;
      width: 600px;
      border: 1px solid #ebeef5;
      iframe {
        border: none;
      }
    }
    .aroundRight {
      width: 950px;
      background: #fff;
      position: absolute;
      top:14px;
      right:20px;
      margin-left: 5px;
      .el-tabs__header{
        margin: 0;
      }
    }
  }
}

.price-check-container {
  height: 700px;
  display: flex;

   iframe {
    flex: 2;
    width: 100%;

    .flex-half {
      flex: 1;
    }
  }

  .price-check-info {
    flex: 1;
    margin: 0 5px;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;

    > * {
      margin-bottom: 10px;
    }
  }

  .price-check-form {
    padding: 10px 72px;
  }

  .price-check-form2 {
    padding: 0 160px;
  }

  .check-title {
    border-left: 5px solid #1890ff;
    padding: 5px 10px;
  }
}

.dialog-no-top-padding {
  .el-dialog .el-dialog__body {
    padding: 0 10px 10px 10px;
  }
}

.host-bid-container {
  .host-bid-content {
    height: 270px;
    display: grid;
    grid-template-columns: 1fr 1fr;

    color: #015478;
    padding: 15px 60px;

    .host-bid-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      align-items: center;
      line-height: 40px;
    }

    span {
      color: #555;
      padding-left: 5px;
    }
  }
}

.host-review, .expert-review {
  height: calc(90vh - 0px) !important;
  display: grid;
  grid-template-rows: 42px 7fr 3fr 50px;

  .el-icon-warning {
    font-size: 24px;
    color: #ffbe00;
    vertical-align: middle;
  }

  .bid-message {
    display: flex;

    > div {
      flex: 1;
    }

    .bid-message-detail {
      display: flex;
      align-items: center;

      > div {
        flex: 1;
        margin: 10px;
      }
    }

    .bid-select {
      display: flex;
      align-items: center;
      justify-content: center;

      > div {
        flex: 1;
        margin: 10px;
      }
    }
  }

  .review-iframes, .expert-iframes {
    display: flex;

    iframe {
      width:50%;  // 此处不能使用flex: 1, 否则在其他版本浏览器会出现右侧iframe容器会无限延长
    }
  }

  .host-review-form, .expert-review-form {
    padding: 10px;
    min-height: 200px;
  }

  .host-review-buttons, .expert-review-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.nego-dialog {
  height: 800px;
  display: grid;
  grid-template-columns: 4fr 1fr;

  .nego-files {
    display: flex;

    iframe {
      flex: 1;
    }
  }

  .nego-form {
    display: grid;
    grid-template-rows: 90px 1fr;

    .bid-select {
      display: grid;
      grid-row: 1fr 1fr;
      align-items: center;
      padding: 5px;
    }

    .nego-form-data {
      padding: 5px;
    }

    .nego-buttons {
      text-align: center;
    }
  }
}

.expert-bid-container {
  .expert-bid-content {
    color: #015478;
    //background: #f6f8fd;
    padding: 20px 60px;
    .expert-bid-form {
      align-items: center;
      line-height: 40px;
    }
    span {
      color: #555;
      padding-left: 5px;
    }
  }
}

.custom-prompt textarea {
  height: 150px;
}

.non-curd-top-blank {
  .main-border {
    padding: 0;
  }
  .buttons-normal {
    display: none;
  }
}

.non-curd-button-padding {
  .buttons-normal {
    padding: 0;
  }
}

.host-step-container, .supplier-step-container, .expert-step-container  {
  height: 500px;
  color: #555;

  .bid-tips {
    line-height: 2.4em;
    padding: 12px;
    font-weight: 700;
  }

  .el-icon-warning {
    font-size: 24px;
    color: #10aeff;
    vertical-align: middle;
  }

  .el-icon-time {
    font-size: 24px;
    color: #F59A23;
    vertical-align: middle;
  }
}

.bid-table-title {
  border-left: 5px solid #1890ff;
  padding: 3px 10px;
  margin: 7px 0;
}

.bid-steps-container {
  height: 100%;
  display: grid;
  grid-template-rows: auto 5fr 1fr;
  padding: 3px 12px;
}

.bid-review-steps-container {
  height: 100%;
  display: grid;
  grid-template-rows: auto 620px 72px;
  padding: 3px 12px;
}

.bid-steps-content {
  overflow-x: auto;
}

.bid-steps-button {
  display: flex;
  justify-content: center;
  align-items: center;
}

.bid-steps-tabs {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;

  div {
    border: 1px solid #d3d4d6;
    background-color: #f4f4f5;
    color: #909399;

    font-weight: bold;
    padding: 7px 15px;
    user-select: none;
    cursor: pointer;

    &:hover {
      background-color: #909399;
      color: white;
    }
  }

  div.active {
    border: 1px solid #b3d8ff;
    background-color: #ecf5ff;
    color: #409eff;

    &:hover {
      background-color: #409eff;
      color: white;
    }
  }

  div.finish {
    border: 1px solid #c2e7b0;
    background-color: #f0f9eb;
    color: #67c23a;

    &:hover {
      background-color: #67c23a;
      color: white;
    }
  }
}

.clarify-container {
  height: 500px;
}

.reviewTemplate {
  .templateBox{
    display: flex;
    .templateLeft {
      width:25%;
    }
    .templateRight {
      margin-left: 10px;
      width:75%;
    }
  }
}

.homeCardDialog{
  .el-dialog .el-dialog__body{
    padding:20px 15px;
  }
  .homeCard {
    display: flex;
    .homeCardLeft {
      width: 950px;
      height:680px;
      overflow-y: auto;
      padding:20px 0 0 20px;
      border: 1px solid #ebeef5;
    }
    .homeCardRight{
      width: 380px;
      height:680px;
      padding: 10px;
      margin-left:-1px;
      border: 1px solid #ebeef5;
    }
  }

  .homeSteps{
    padding:10px 0px 20px 30px;
    .el-steps .el-step__title{
        width: 100px;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-left:-36px;
        line-height: 34px;
    }
    .el-step.is-horizontal .el-step__line{
        top:13px
    }
    .el-steps .el-step__title.is-process{
        color: #1F7FFF;
    }
    .el-steps .el-step__title.is-wait{
        color: #999999;
    }
    .el-steps .el-step__head.is-process{

        .el-step__icon.is-text{
            border: 2px solid #1F7FFF;
            width: 28px;
            height: 28px;
            padding: 5px;
            background-color:#fff;
            color: #1F7FFF;
            .el-step__icon-inner{
                width: 14px;
                height: 14px;
                border-radius:50%;
                background-color:#1F7FFF;
            }
        }
    }

    .el-steps .el-step__head.is-success{
        .el-step__icon.is-text{
            border: 2px solid #11BB77;
            width: 28px;
            height: 28px;
            padding: 5px;
            background-color:#11BB77;
            .el-step__icon-inner{
                color: #fff;
                font-size: 18px;

            }
        }
    }

    .el-step.is-horizontal .el-step__line{
      left:40px;
      right:13px;
    }

    .el-steps.el-step__title.is-success{
      color:#11BB77
    }

    .el-steps .el-step__head.is-wait{
        .el-step__icon.is-text{
            border: 2px solid #999999;
            width: 28px;
            height: 28px;
            padding: 5px;
            background-color:#fff;
            .el-step__icon-inner{
                width: 14px;
                height: 14px;
                border-radius:50%;
                color: #999999;
                background-color:#999999;
                overflow: hidden;
            }
        }
    }
  }
}

//采购项目管理
.pur-project-manage {
  .selectreCard{
    display: flex;
    .el-button {
      margin-left:10px;
    }
  }
}

//零星采购
.minorpurchase {
  .createInfoTitle {
    text-align: center;
    color: #333;
    font-weight: bold;
    margin-bottom:10px;
  }
  .infoContent {
    padding:20px;
    overflow: auto;
    border:1px solid #DCDFE6;
  }
}

//采购项目管理
.purProjectManage{
  .pur-project-manage{
    .pur-primary{
      color: #409EFF;
      border: 1px solid #409EFF !important;
      background-color: #E8F4FF;
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 10px;
    }
    .pur-primary.is-disabled{
      // background-color: #EEEEEE !important;
      background-color: #409EFF!important;
      border-color: #DDDDDD !important;
      color: #C0C4CC !important;
    }
  }
}

//采购结果公告 选择项目
 .noticeDialog .common-page.column-top-show .button-list { margin-bottom: 0;}
