$border-color: #DDDDDD;

/*
 * 平安融入需要
 */
.vue-bifrostIcApp > div {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 0;
}
.vue-bifrostIcApp{
  /*
  * bottom normal版本
  */
  .bottom-normal {
    // position: absolute;
    // background: #fff;
    // width: 90%; // 兼容
    // width: calc(100% - 50px);
    // display: inline-block;
    // bottom: 15px;
    // box-sizing: border-box;
    // height: 40px;
    // line-height: 40px;
    // text-align: center;
    // z-index: 500;
    padding: 0 8px 8px 8px;
    text-align: center;
  }
}

/*
 * 按钮群 normal版本
 */
.buttons-normal {
  padding: 0 12px 0 12px;

  .el-button {
    margin-top: 0px;
    margin-left: 0px;
    margin-bottom: 0;
  }
  // .el-button--primary {
  //     &:first-child {
  //     color: #fff;
  //     background-color: #1F7FFF;
  //     border-color: #1F7FFF;
  //     &:hover {
  //       background: #478BFE;
  //       border-color: #478BFE;
  //       color: #fff;
  //     }
  //     &:active {
  //       background: #1070E1;
  //       border-color: #1070E1;
  //       color: #fff;
  //     }
  //     &.is-disabled {
  //       color: #fff;
  //       background-color: #a0cfff;
  //       border-color: #a0cfff;
  //       &:hover {
  //         color: #fff;
  //         background-color: #a0cfff;
  //         border-color: #a0cfff;
  //       }
  //     }
  //   }
  // }
  // .el-button--default {
  //   border: 1px #dddddd solid !important;
  //   color: #666666;
  //   &:hover {
  //     border: 1px #478bfe solid !important;
  //     color: #FFFFFF !important;
  //     background: #fff;
  //   }
  //   &:focus {
  //     color: #666666;
  //     background: #fff;
  //   }
  //   &:active {
  //     border: 1px #1070e1 solid !important;
  //     color: #FFFFFF !important;
  //     background: #fff;
  //   }
  // }
  .el-button--danger.is-plain {
    color: #606266;
    background: #fff;
    border-color: #dcdfe6;
    &:hover {
      background: #f56c6c;
      border-color: #f56c6c;
      color: #fff;
    }
  }
  .el-button--danger.is-plain.is-disabled {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
    background-color: #fff;
    border-color: #ebeef5;
  }
  .el-button--danger.is-plain.is-disabled:active,
  .el-button--danger.is-plain.is-disabled:focus,
  .el-button--danger.is-plain.is-disabled:hover {
    color: #f9a7a7;
    background-color: #fef0f0;
    border-color: #fde2e2;
  }
  .single-filter {
    margin: 0;
  }
}

/*
 * 搜索群 normal版本
 */
.filters-normal {
  padding: 2px 12px 2px 2px;

  // border-bottom: 1px solid $border-color;
  .el-button {
    margin-top: 5px;
    margin-bottom: 0;
    margin-left: 10px;
    padding-top: 9px;
    padding-bottom: 9px;
  }
}

.downblock {
  // position: absolute;
  // top: 68px;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  z-index: 500;

  .down {
    background: #fff;
    padding: 0 0 5px 0;
  }

  .fade {
    &-enter-active {
      transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
      transform-origin: top left;
    }

    &-leave-active {
      display: none;
    }

    &-enter,
    &-leave-to {
      opacity: 0;
      transform: scaleY(0);
    }
  }
}

/*
 * 每组搜索字段以及框
 */
.single-filter {
  display: inline-flex;
  white-space: nowrap;
  width: 15%;
  align-items: center;
  margin: 5px 0 0 0;
  margin-left: 10px;
  &.have-label {
    width: 25%;
  }
  .filter-name {
    font-size: 13px;
    margin-right: 5px;
    display: inline-block;
    min-width: 70px;
    text-align: center;
  }
  .el-select,
  .el-input {
    width: 100%;
    max-width: 100%;
  }
  .el-radio {
    margin-right: 10%;
  }
  .el-radio__label {
    padding-left: 7px;
  }
  & + .el-button {
    margin-left: 10px;
  }
}

/*
 * 主要界面 normal版本
 */
.main-normal {
  padding: 7px 12px;
  box-sizing: border-box;
  width: 100%;
  height: auto;
  max-height: 70%; // 兼容
  max-height: calc(100% - 210px);

  &.main-long {
    height: 80%; // 兼容
    height: calc(100% - 150px);
  }
}

/*
 * 主要界面 border版本
 */
.main-border {
  box-sizing: border-box;
  width: 100%;
  height: auto;
  // height: 70%; // 兼容
  // height: calc(100% - 210px);
  // border-top: 1px solid $border-color;
  padding: 5px 12px 8px;

  &.sys-icon {
    .main-header {
      padding: 3px 5px;
      float: right;
    }
  }

  &.main-long {
    height: 80%; // 兼容
    height: calc(100% - 150px);
  }
}


/*
 * table normal版本
 */
.table-normal {
  font-size: 12px;

  &.el-table td,
  &.el-table th {
    padding: 5px 0;
  }

  &.el-table th {
    color: #555;
    font-weight: bolder;
  }
}

/*
 * table border版本
 */

.table-border {
  &.sum-table .el-table__fixed {
    height: 100% !important;
    bottom: 0;
  }
  .el-table__fixed {
    // 左固定列
    height: auto !important;
    bottom: 18px; // 改为自动高度后，设置与父容器的底部距离，高度会动态改变，值可以设置比滚动条的高度稍微大一些
  }
  .el-table__fixed-right {
    // 右固定列
    height: auto !important;
    bottom: 18px; // 改为自动高度后，设置与父容器的底部距离，高度会动态改变，值可以设置比滚动条的高度稍微大一些
  }
  &.el-table {
    font-size: 12px;
  }

  &.el-table td,
  &.el-table th {
    padding: 5px 0;
  }

  &.el-table th {
    color: #3f536e;
    background: #f7f7f8;
    font-weight: bolder;
    padding: 7px 0;
  }

  &.el-table td,
  &.el-table th.is-leaf {
    border-bottom: 1px solid $border-color;
  }

  &.el-table.el-table--border,
  &.el-table--group.el-table--border {
    border: 0;
    border-top: 1px solid $border-color;
    border-left: 1px solid $border-color;
    // border-right: 1px solid #dcdfe6;
    // border: 1px solid $border-color;
  }

  &.el-table--border::after,
  &.el-table--group::after,
  &.el-table::before {
    background-color: $border-color;
  }

  &.el-table td,
  &.el-table th,
  &.el-table__body-wrapper .el-table.is-scrolling-left ~ .el-table__fixed {
    border-right: 1px solid $border-color;
  }

  .el-table__footer-wrapper tbody td,
  .el-table__header-wrapper tbody td {
    border-top: 1px solid $border-color;
  }

  .el-table__fixed-footer-wrapper tbody td {
    border-top: 1px solid $border-color;
  }
}

.main-border.sys-icon .table-border {
  &.el-table,
  &.el-table--group {
    border-top: 1px solid $border-color;
  }
}

/*
 * 双栏布局
 */
.container-dbcol {
  display: flex;
  height: 100%;
  width: 100%;
  .dbcol-left {
    width: 18%;
    min-width: 250px;
    // height: 100%;
    box-sizing: border-box;
    background: #fff;
    border: 1px solid $border-color;
    overflow: auto;
    transition: width 0.5s ease;
    .el-input {
      margin-bottom: 5px;
    }
    &.padding {
      padding: 10px 5px;
    }
  }

  .dbcol-right {
    width: 85%;
    height: 100%;
    box-sizing: border-box;
    margin-left: 10px;
    background: #fff;
    border: 1px solid $border-color;
    overflow: auto;
    transition: width 0.5s ease;
    // .bottom-normal {
    //   width: 56%; // 兼容
    //   width: calc(72% - 50px);
    // }
  }
  &.collapsed {
    .dbcol-left {
      width: 30px;
    }
    .dbcol-right {
      width: 95%;
      width: calc(100% - 30px);
    }
  }
}

/*
 * 树 small形
 */
.tree-small {
  .el-tree-node {
    white-space: pre-wrap;
  }

  .el-tree-node__content {
    height: auto;
    line-height: 1.5;
  }

  .el-tree-node__label {
    font-size: 12px;
    word-wrap: break-word;
  }
}

// 正常带下划线的链接
.link {
  display: inline-block;
  cursor: pointer;
  // text-decoration: underline;
  color: #409eff;
  font-size: 12px;

  & + .link,
  & + .el-dropdown {
    margin-left: 10px;
  }
}

// 不带带下划线的链接
.link-noline {
  display: inline-block;
  cursor: pointer;
  text-decoration: none;
  color: #409eff;
  font-size: 12px;

  & + .link-noline,
  & + .el-dropdown {
    margin-left: 10px;
  }
}
// 鼠标经过有下划线的链接
.link-hoverline {
  display: inline-block;
  cursor: pointer;
  text-decoration: none;
  color: #409eff;
  font-size: 12px;
  &:hover {
    text-decoration: underline;
  }
  & + .link-hoverline,
  & + .el-dropdown {
    margin-left: 10px;
  }
}
.full-screen {
  position: fixed;
  background: #fff;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 666;
}

.form-title {
  font-weight: bold;
  font-size: 16px;
  padding: 8px;
  margin-bottom: 10px;
  border-bottom: 1px solid $border-color;
  margin-top: 10px;
  &.small {
    font-size: 14px;
  }
  &:first-child {
    margin-top: 0;
  }
}

/*
 * 展示用table
 */
.my-table {
  font-size: 14px;
  border-collapse: collapse;
  width: 100%;
  text-align: center;
  thead {
    // font-weight: bold;
  }
  tr,
  td {
    padding: 5px 5px;
    line-height: 21px;
    border: 1px solid #ddd;
    vertical-align: middle;
    box-sizing: border-box;
  }
  td div {
    vertical-align: middle;
  }
  &.info-noborder {
    tr,
    td {
      border: 0;
      padding: 18px 0;
    }
    tbody tr td:nth-of-type(odd) {
      font-weight: bold;
      background: #fff;
      text-align: right;
    }
    tbody tr td:nth-of-type(even) {
      text-align: left;
      padding-left: 5px;
      padding-right: 5px;
    }
  }
  &.info tr,
  &.info td {
    padding: 18px 0;
  }
  &.info tbody tr td:nth-of-type(odd) {
    // font-weight: bold;
    background: #f7f7f8;
  }
  &.info tbody tr td:nth-of-type(even) {
    text-align: left;
    padding-left: 5px;
    padding-right: 5px;
  }
  &.bill tr,
  &.bill td {
    padding: 5px 5px;
    height: 32px;
  }
  &.bill {
    thead td {
      font-weight: bold;
      padding: 10px 5px;
    }
    .el-button {
      padding: 0;
    }
  }
  .el-input__inner {
    border-radius: 0;
    // border: 0;
    // border-bottom: 1px solid #DCDFE6;
  }
}

/* 
 * 高度特别大的弹框
 * 在el-dialog 加上 class="big-dialog"
 */
.big-dialog .el-dialog {
  height: calc(100% - 30px);
  .el-dialog__body {
    overflow: auto;
    height: calc(100% - 138px);
  }
}

/* 
 * iframe的弹框
 * 在el-dialog 加上 class="iframe-dialog"
 */
.iframe-dialog .el-dialog {
  height: calc(100% - 30px);
  .el-dialog__header {
    padding: 10px 10px;
  }
  .el-dialog__headerbtn {
    top: 10px;
  }
  .el-dialog__body {
    padding: 0 20px;
    overflow: auto;
    height: calc(100% - 50px);
  }
}

/* 
 * 弹框双栏
 */
.dia-double {
  display: flex;
  .dia-double-l {
    // flex: 1;
    border: 1px solid #ddd;
    padding: 20px 10px;
  }
  .dia-double-r {
    flex: 1;
    border: 1px solid #ddd;
    padding: 10px;
    margin-left: 20px;
    .el-tab-pane {
      padding: 10px 0;
      overflow: auto;
      .iconitem {
        font-size: 20px;
        display: inline-block;
        height: 45px;
        width: 45px;
        box-sizing: border-box;
        line-height: 45px;
        padding: 10px 10px;
        cursor: pointer;
        white-space: nowrap;
        svg {
          margin-right: 5px;
        }
      }
    }
  }
  .show-icon {
    margin-left: 10px;
    margin-bottom: -5px;
    font-size: 25px;
  }
}

// 文件展示列表
.folder-block {
  // height: 180px;
  width: 250px;
  display: inline-block;
  margin-right: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  // padding: 10px 0;
  text-align: center;
  vertical-align: top;
  .title {
    height: 32px;
    line-height: 32px;
  }
  .img {
    border: 1px solid #dcdfe6;
    border-left: 0;
    border-right: 0;
    padding: 20px;
    height: 100px;
    svg {
      font-size: 60px;
    }
  }
  .button {
    height: 32px;
    line-height: 32px;
    .left,
    .right {
      width: 50%;
      display: inline-block;
    }
    .left {
      border-right: 1px solid #dcdfe6;
    }
  }
}

.tabs-three {
  .el-tabs__header {
    // width: 170px;
  }
}

// 拖拽上传样式
.upload-demo {
  width: 100%;
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
    }
  }
}

// reset上传组件只有按钮时候居中
.upload-demo.button .el-upload {
  text-align: left;
}

// 文字在开关上面的样式
.switchStyle {
  .el-switch__label {
    position: absolute;
    display: none;
    font-weight: normal;
  }
  .el-switch__label * {
    font-size: 12px;
    letter-spacing: 1px;
  }
  .el-switch__label--left {
    z-index: 9;
    left: 10px;
    color: #999;
  }
  .el-switch__label--right {
    z-index: 9;
    left: -19px;
    color: #fff;
  }
  .el-switch__label.is-active {
    display: block;
    height: 30px;
    line-height: 30px;
  }
}
.switchStyle.el-switch .el-switch__core,
.el-switch .el-switch__label {
  width: 60px !important;
}
// .el-switch,.el-switch__core{
//   height:30px;
//   line-height:30px;
// }
// .el-switch__core{
//   border-radius:15px;
//   &:after{
//       width:20px;
//       height:20px;
//       top:4px;
//       left:3px;
//       z-index:10;
//   }
// }
// .el-switch.is-checked .el-switch__core::after{
//   margin-left:-23px;
// }
// 蓝底信息显示样式 update by liutie at 2020.06.17
.topInfo {
  background: #9accff;
  padding: 5px 15px 5px 15px;
  line-height: 18px;
}

// 卡片样式
.el-card {
  border-radius: 2px;
}

.el-tabs-nomargin .el-tabs__header { margin-bottom: 0; }

// 全局tab纵向样式
.el-tabs--left .is-left,.el-tabs--right .is-right {
  .el-tabs__item {
    padding: 0 24px;
  }
  .is-active{
    background: #E5F1FE;
    color: #1177EE;
    font-weight: 500;
  }
}

// 表格顶部按钮样式
.table-btns {
  padding: 8px;
  border: 1px solid #DDDDDD;
  border-bottom: none;
  .el-button {
    font-size: 14px;
    border: none;
  }
  .el-button.el-button--default:hover,
  .el-button.el-button--default:focus,
  .el-button.el-button--default:active {
    background: #478BFE;
    border-color: #478BFE;
    color: #fff;
  }
  .el-button.is-disabled,.el-button.is-plain.is-disabled:hover,.el-button.danger.is-plain.is-disabled:hover {
    // background: #ffffff!important;
    // color: #BBBBBB!important;
    background: #478BFE;
    border-color: #478BFE;
    color: #fff;
  }
  
}

// 更多按钮容器的样式
.moreButtonContainer {
  .el-button--danger.is-plain {
    color: #606266;
    background: #fff;
    border-color: #dcdfe6;
    &:hover {
      background: #f56c6c !important;
      border-color: #f56c6c !important;
      color: #fff !important;
    }
  }
  .el-button--danger.is-plain.is-disabled {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
    background-color: #fff;
    border-color: #ebeef5;
  }
  .el-button--danger.is-plain.is-disabled:active,
  .el-button--danger.is-plain.is-disabled:focus,
  .el-button--danger.is-plain.is-disabled:hover {
    color: #f9a7a7;
    background-color: #fef0f0;
    border-color: #fde2e2;
  }
}

// loading效果
.listContentMain .el-loading-mask {
  font-size: 24px;
}
.loading-size .el-loading-mask {
  font-size: 24px;
}