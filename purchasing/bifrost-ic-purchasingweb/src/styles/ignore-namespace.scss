$border-color: #dcdfe6;
.pa-platform-el-dialog {
  .el-dialog {
    margin: 0 !important;
    border-radius: 3px;
    overflow: auto;
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
    // display: flex;
    // flex-direction: column;

    // 弹窗内表格
    .table-border {
      &.sum-table .el-table__fixed {
        height: 100% !important;
        bottom: 0;
      }
      .el-table__fixed { // 左固定列
        height: auto !important;
        bottom: 18px; // 改为自动高度后，设置与父容器的底部距离，高度会动态改变，值可以设置比滚动条的高度稍微大一些
      }
      .el-table__fixed-right { // 右固定列
        height: auto !important;
        bottom: 18px; // 改为自动高度后，设置与父容器的底部距离，高度会动态改变，值可以设置比滚动条的高度稍微大一些
      }
      &.el-table {
        font-size: 12px;
      }

      &.el-table td,
      &.el-table th {
        padding: 5px 0;
        font-family: "initial"
      }

      &.el-table th {
        color: #3f536e;
        background: #f7f7f8;
        font-weight: bolder;
        padding: 7px 0;
        font-family: "initial"
      }

      &.el-table td,
      &.el-table th.is-leaf {
        border-bottom: 1px solid $border-color;
      }

      &.el-table.el-table--border,
      &.el-table--group.el-table--border {
        border: 0;
        border-top: 1px solid $border-color;
        border-left: 1px solid $border-color;
        // border-right: 1px solid #dcdfe6;
        // border: 1px solid $border-color;
      }

      &.el-table--border::after,
      &.el-table--group::after,
      &.el-table::before {
        background-color: $border-color
      }

      &.el-table td,
      &.el-table th,
      &.el-table__body-wrapper .el-table.is-scrolling-left~.el-table__fixed {
        border-right: 1px solid $border-color;
      }

      .el-table__footer-wrapper tbody td,
      .el-table__header-wrapper tbody td {
        border-top: 1px solid $border-color;
      }

      .el-table__fixed-footer-wrapper tbody td {
        border-top: 1px solid $border-color;
      }
    }

    .el-select__tags {
      flex-wrap: inherit;
      overflow: hidden;
    }


    // 弹窗内的plain按钮
    .el-button--primary.is-plain {
      color: #606266;
      background: #fff;
      border-color: #dcdfe6;
      &:hover {
        background: #409eff;
        border-color:#409eff;
        color: #fff;
      }
    }
    .el-button--danger.is-plain {
      color: #606266;
      background: #fff;
      border-color: #dcdfe6;
      &:hover {
        background: #F56C6C;
        border-color: #F56C6C;
        color: #FFF;
      }
    }
    .el-button--danger.is-plain.is-disabled {
      color: #C0C4CC;
      cursor: not-allowed;
      background-image: none;
      background-color: #FFF;
      border-color: #EBEEF5;
    }
    .el-button--danger.is-plain.is-disabled:active, .el-button--danger.is-plain.is-disabled:focus, .el-button--danger.is-plain.is-disabled:hover {
      color: #f9a7a7;
      background-color: #fef0f0;
      border-color: #fde2e2;
    }

    // button列
    .buttons-normal {
      border: 1px solid #DDDDDD;
      border-bottom: transparent;
      padding: 4px 0;
      .el-button, .el-button.el-button--danger, .el-button--primary:first-child {
        background: #fff;
        color: #606266;
        margin: 5px 10px;
        border: 1px solid transparent !important;
        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        height: 32px;
        margin-top: 0;
        padding-top: 7px;
        padding-bottom: 7px;
        &:hover {
          background: #409EFF;
          color: #fff;
        }
      }
      // .el-button--primary:first-child {
      //   color: #fff;
      //   background-color: #409eff;
      //   border-color: #409eff;
      // }

      // 按钮图标
      .el-icon-arrow-down{
        font-size: 12px;
      }
      //禁用
      .el-button--primary.is-plain.is-disabled {
        background-color: #fff;
        color: #c0c4cc;
      }
      // 禁用
      .el-button--primary:first-child.is-disabled{
        color: #fff;
        background-color: #a0cfff;
        border-color: transparent;
      }
      .el-dropdown {
        font-size: 14px;
      }
    }
    // 兼容不使用layoutTem样式
    .filters-normal {
      padding: 2px 12px 2px 2px;
      .single-filter {
        width: 18%;
        margin: 0 10px 18px 0;
        display: inline-block;
        white-space: nowrap;
        vertical-align: top;
        .el-select, .el-input, .el-date-editor {
          width: 100%;
          max-width: 100%;
        }
        .el-button {
          height: 32px;
          // line-height: 32px;
          margin-top: 0;
          padding-top: 7px;
          padding-bottom: 7px;
        }
      }
      .downblock {
        .down {
          .single-filter:nth-of-type(4n+0) {
            margin-right: 18%;
            // white-space: pre-line;
          }
        }
      }
      .el-tabs{
        font-size: 14px;
        .el-tabs__nav-scroll {
          .el-tabs__item {
            padding: 0px 20px;
            font-size: 16px!important;
            // margin-right: 30px;
            height: 100%;
          }
          .is-active {
            border-bottom: 2px solid #40a9ff;
          }
        }
        .el-tabs__active-bar {
          display: none;
        }
        .el-tabs__nav-wrap{
          width: 100%;
        }
      }
    }
    .single-filter {
      width: 18%;
      margin: 0 10px 18px 0;
      display: inline-block;
      white-space: nowrap;
      vertical-align: top;
      .el-select, .el-input, .el-date-editor {
        width: 100%;
        max-width: 100%;
      }
      .el-button {
        height: 32px;
        // line-height: 32px;
        margin-top: 0;
        padding-top: 7px;
        padding-bottom: 7px;
      }
    }

    // 财务核算域模块禁用的颜色修正
    .el-input.is-disabled .el-input__inner {
      color: #606266;
    }
    .el-textarea.is-disabled .el-textarea__inner  {
      color: #606266;
    }
    .el-radio__input.is-checked.is-disabled+span.el-radio__label {
      color: #606266;
    }

    .el-radio__input.is-disabled.is-checked .el-radio__inner {
      border-color: #a5a8ad;
    }

    .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
      background-color: #a5a8ad;
    }

    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      border-color: #a5a8ad;
    }

    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
      border-color: #a5a8ad;
    }

    .el-checkbox__input.is-disabled.is-checked+span.el-checkbox__label {
      color: #606266;
    }

    .container-border {
      background: #fff;
      overflow: auto;
      height: 100%;
      box-sizing: border-box;
      border: transparent;
      .filters-normal {
        padding: 2px 12px 2px 2px;
        .single-filter {
          width: 18%;
          margin: 0 10px 18px 0;
          display: inline-block;
          white-space: nowrap;
          vertical-align: top;
          .el-select, .el-input, .el-date-editor {
            width: 100%;
            max-width: 100%;
          }
          .el-button {
            height: 32px;
            // line-height: 32px;
            margin-top: 0;
            padding-top: 7px;
            padding-bottom: 7px;
          }
        }
        .downblock {
          .down {
            .single-filter:nth-of-type(4n+0) {
              margin-right: 18%;
              // white-space: pre-line;
            }
          }
        }
        // .pa_tabs {
        //   margin-bottom: 32px;
        // }
      }
      .column-top {
        padding: 0 12px 0;
      }
      .column-bottom {
        .main-border {
          box-sizing: border-box;
          width: 100%;
          // height: auto;
          // padding-top: 0;
          // padding-bottom: 0;
          box-sizing: border-box;
          width: 100%;
          padding: 0 12px 8px;
        }

        &.main-long {
          height: 80%; // 兼容
          height: calc(100% - 150px);
        }
      }
      &.flex-column {
        .column-bottom .dbcol-left {
          margin-top: 0!important;
          margin-bottom: 0!important;
          margin-left: 12px!important;
        }
        // .buttons-normal {
        //   border: 0;
        // }
      }
      // button列
      .buttons-normal {
        border: 1px solid #DDDDDD;
        border-bottom: transparent;
        padding: 4px 0;
        .el-button {
          margin: 5px 10px;
          border: 1px solid transparent !important;

        }
        .el-dropdown {
          font-size: 14px;
        }
      }
    }

    // 文字在开关上面的样式
    .switchStyle{
      .el-switch__label {
        position: absolute;
        display: none;
        font-weight:normal;
      }
      .el-switch__label *{
        font-size:12px;
        letter-spacing: 1px;
      }
      .el-switch__label--left {
        z-index: 9;
        left:10px;
        color: #999;
      }
      .el-switch__label--right {
        z-index: 9;
        left: -19px;
        color: #fff;
      }
      .el-switch__label.is-active {
        display: block;
        height:30px;
        line-height:30px;
      }
    }
    .switchStyle.el-switch .el-switch__core,.el-switch .el-switch__label {
      width:60px !important;
    }
    .el-dialog__header {
      padding: 18px 20px 10px;
      .el-dialog__title {
        font-size: 18px;
      }
    }
    .el-button {
      font-size: 14px;
    }
// vxe
    .vxe-cell {
      .el-button {
        font-size: 14px;
        color: #1890ff;
      }
      .el-input {
        .el-input__inner {
          font-size: 14px;
        }
      }
      .el-button {
        &:hover {
          color: #40a9ff;
        }
      }
      .deleteBtn {
        color: #fa5555;
        &:hover {
          color: #eb4141;
        }
      }
    }
    // 默认分页样式
    .el-pagination{
      .el-pagination__total{
        font-size: 14px;
      }
      .el-pagination__sizes{
        .el-input{
          .el-input__inner{
            font-size: 14px;
          }
        }
      }
      .el-pager{
        li{
          font-size: 14px;
        }
      }
      .el-pagination__jump{
        font-size: 14px;
      }
    }
  }

  .el-dialog__title {
    font-size: 14px;
    // font-weight: bold;
  }

  .el-dialog__body {
    padding: 10px 20px;
    overflow: auto;
    font-size: 14px;
    .form-title {
      font-weight: bold;
      font-size: 14px;
      padding: 8px;
      margin-bottom: 10px;
      border-bottom: 1px solid #dcdfe6;
      margin-top: 10px;
      &.small {
        font-size: 14px;
      }
      &:first-child {
        margin-top: 0;
      }
    }
    .title-pa {
      background: #dfe6e9;
      padding: 10px 0 10px 10px;
      margin: 10px 0;
      width: 100%;
      font-size: 14px;
      font-weight: bolder;
      color: #333;
      border-left: 10px solid #b2bec3;
      &.blue {
        color: #1890ff;
      }
    }
    .title-pa-sub {
      padding: 0 0 0 10px;
      margin: 20px 0 20px 40px;
      width: calc(100% - 40px);
      font-size: 14px;
      font-weight: bolder;
      color: #333;
      border-left: 5px solid #b2bec3;
      &.blue {
        border-left: 5px solid #1890ff;
        color: #1890ff;
      }
    }
  }
  .el-dialog__header {
    padding: 18px 20px 10px;
    .el-dialog__title {
      font-size: 18px;
    }
  }
  .el-button {
    font-size: 14px;
  }
  .link-hoverline {
    display:inline-block;
    cursor: pointer;
    text-decoration: none;
    color: #409EFF;
    font-size: 14px;
    &:hover {
      text-decoration: underline;
    }
    &+.link-hoverline,
    &+.el-dropdown {
      margin-left: 10px;
    }
  }

  .topInfo {
    font-size: 14px;
    background: #e6f7ff;
    // background: #fff1dd;
    min-height: 34px;
    line-height: 18px;
    border: 1px solid #DDDDDD;
    border-top: transparent;
    border-bottom: transparent;
    border-radius: 0px;
    box-sizing: border-box;
    padding: 5px 15px 5px 15px;
  }
  .topInfo .topText {
    font-family: 'PingFangSC-Regular', 'PingFang SC';
    color: #666666;
    text-align: center;
    // line-height: 34px;
    margin: 0 4px;
    word-break: break-all;
  }
  .topInfo .topAmt {
    font-family: 'Arial Normal', 'Arial';
    color: #1890ff;
    text-align: center;
    line-height: 20px;
    margin: 0 4px;
  }

  // 输入框
  .el-input {
    height: 32px;
    // line-height: 1.5;
    border-radius: 2px;
    font-size: 16px !important;
    font-family: "initial";
    color: rgba(0, 0, 0, 0.65);
    .el-input__inner {
      font-size: 14px;
    }
  }

  // 按钮
  .el-button {
    font-weight: 400;
    font-size: 14px;
    text-align: center;
  }

  // 下拉框
  .el-select-dropdown, .el-popper {
    .el-scrollbar {
      .el-select-dropdown__list {
        .el-select-dropdown__item {
          font-size: 16px!important;
          .el-tree-node__content,.el-tree-node__children{
            font-size: 16px!important;
            font-family:"initial";
          }
        }
      }
    }
  }

  // 多选框
  .el-checkbox-group {
    .el-checkbox {
      .el-checkbox__label {
        font-size: 14px;
      }
    }
  }

  .el-checkbox {
    .el-checkbox__label {
      font-size: 14px;
    }
  }

  // 单选框
  .el-radio-group {
    .el-radio {
      .el-radio__label {
        font-size: 14px;
      }
    }
  }

  .el-radio__label {
    font-size: 14px;
  }

  //  tab切换页
  .el-tabs, &.el-tabs {
    .el-tabs__nav-scroll {
      .el-tabs__item {
        font-size: 14px;
      }
    }
  }

  // vxe表格样式
  .vxe-table--fixed-wrapper {
    .vxe-table--body-wrapper {
      .vxe-body--row {
        .vxe-body--column {
          border-color: rgba(42, 141, 255, 1);
        }
      }
    }
  }
  .vxe-table--fixed-wrapper .vxe-table--body-wrapper .vxe-body--row .vxe-body--column {
    border-color: rgba(42, 141, 255, 1);
  }
  .vxe-table--render-default {
    font-size: 16px !important;
  }
  .vxe-table--main-wrapper {
    .vxe-header--column {
      .vxe-cell {
        border-color: rgba(42, 141, 255, 1);
        height: auto;
        .vxe-cell--title {
          color: #333;
        }
      }
    }
    .link-hoverline {
      display:inline-block;
      cursor: pointer;
      text-decoration: none;
      color: #409EFF;
      font-size: 14px;
      &:hover {
        text-decoration: underline;
      }
      &+.link-hoverline,
      &+.el-dropdown {
        margin-left: 10px;
      }
    }
    .vxe-cell {
      border-color: rgba(42, 141, 255, 1)!important;
      color: #333;
      line-height: 24px;
      font-size: 14px;
      height: auto;
      .el-button {
        font-size: 14px;
        color: #1890ff;
      }
      .el-input {
        .el-input__inner {
          font-size: 14px;
        }
      }
      .el-button {
        &:hover {
          color: #40a9ff;
        }
      }
      .deleteBtn {
        color: #fa5555;
        &:hover {
          color: #eb4141;
        }
      }
    }
  }
  .vxe-table--fixed-wrapper {
    .vxe-cell {
      border-color: rgba(42, 141, 255, 1)!important;
      color: #333;
      line-height: 24px;
      font-size: 14px;
      height: auto;
      .el-button {
        font-size: 14px;
        color: #1890ff;
      }
      .el-input {
        .el-input__inner {
          font-size: 14px;
        }
      }
      .el-button {
        &:hover {
          color: #40a9ff;
        }
      }
      .deleteBtn {
        color: #fa5555;
        &:hover {
          color: #eb4141;
        }
      }
    }
  }
  .vxe-table--tooltip-wrapper,
  .theme--dark,
  .is--arrow {
    z-index: 9999 !important;
    .vxe-table--tooltip-content {
      font-size: 14px;
    }
  }

  // 时间日期
  .el-date-editor {
    .el-range-input, .el-range-separator {
      font-size: 14px;
    }
  }

  .el-picker-panel {
    .el-picker-panel__body-wrapper {
      .el-picker-panel__body {
        .el-date-table {
          tbody {
            tr {
              th {
                font-size: 14px;
              }
              td {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  // 表格
  .el-table {
    .el-table__header-wrapper {
      .el-table__header {
        .cell {
          color: #333;
          line-height: 24px;
          font-size: 14px;
        }
      }
    }
    .el-table__body-wrapper {
      .el-table__body {
        .cell {
          .el-button {
            font-size: 14px;
            color: #1890ff;
          }
          .el-input {
            .el-input__inner {
              font-size: 14px;
            }
          }
          .el-button {
            &:hover {
              color: #40a9ff;
            }
          }
          .deleteBtn {
            color: #fa5555;
            &:hover {
              color: #eb4141;
            }
          }
        }
      }
      .el-table__empty-block {
        .el-table__empty-text {
          font-size: 14px;
          color: #606266;
        }
      }
    }

    .cell, .vxe-cell {
      line-height: 24px;
      font-size: 14px;
    }
  }

   // 表单
  .el-form {
    .el-form-item__content {
      font-size: 14px;
      font-family: "initial"

    }
    .el-form-item__label {
      font-size: 14px;
    }
    .el-input,
    .el-select,
    .el-textarea {
      width: 100%;
      font-size: 14px;
    }
    .el-radio {
      line-height: 26px;
    }
  }

  // element树形
  .el-tree {
    .el-tree-node__content {
      font-size: 14px;
      font-family:"initial";
      .custom-tree-node {
        font-size: 14px;
      }
      .el-tree-node__label {
        font-size: 14px;
      }
    }
    .el-tree__empty-text {
      font-size: 14px;
    }
  }

  //ztree 树形下拉
  .el-popper{
    .el-input__inner{
      font-size: 14px;
      font-family:"initial"
    }
    .ztree {
      li{
        font-size: 14px;
        font-family:"initial"
      }
    }
  }

// 下拉框 element树 控件
  .el-select-dropdown, .el-popper {
    .el-scrollbar {
      .el-select-dropdown__list {
        .el-select-dropdown__item {
          font-size: 16px!important;
          .el-tree-node__content,.el-tree-node__children{
            font-size: 16px!important;
            font-family:"initial";
          }
        }
      }
    }
  }
  // 分页
  .bottom-normal {
    margin: 24px 0 0;
    position: relative;
    line-height: 32px;
    font-size: 14px;
    text-align: right;
    color: rgba(0, 0, 0, 0.65);
    .el-pagination {
      right: 10px;
    }
  }
  .pagination-info {
    position: absolute;
    left: 10px;
  }
  .el-pagination.is-background {
    font-size: 14px;
    button {
      font-size: 14px;
    }
    span:not([class*=suffix]) {
      font-size: 14px;
    }
    .el-pagination__total {
      font-size: 14px;
    }
    .el-pagination__sizes {
      font-size: 14px;
      .el-select {
        font-size: 14px;
        .el-input {
          .el-input__inner {
            font-size: 14px;
          }
        }
      }
    }
    .btn-prev, .btn-next {
      font-size: 14px;
      height: 30px;
      border: 1px solid #ddd;
      background-color: #F5F5F5;
    }
    .el-input--mini .el-input__inner, .el-pagination__jump, .el-pagination__jump .el-input__inner {
      height: 30px;
    }
    .el-pager {
      li {
        height: 30px;
      }
      li:not(.disabled).active {
        background: #2A8DFF;
      }
      .number {
        font-size: 14px;
        font-weight: normal;
        border: 1px solid #ddd;
        background-color: #F5F5F5;
      }
    }
    .el-pagination__jump {
      font-size: 16px !important;
    }
  }


  .el-date-editor .el-range-separator {
    width: 12%
  }

  // 重写标签
  .el-tag {
    font-size: 14px;
  }

  // 下拉选择其他默认选项显示红色
  .noDefaultSelectItem {
    .el-input .el-input__inner {
      color: #F56C6C!important;
    }
    .el-input.is-disabled .el-input__inner {
      color: #F56C6C!important;
    }
  }
  /*
 * 展示用table 单位人员查看  需要加平台类名
 */
.my-table {
  font-size: 14px;
  border-collapse: collapse;
  width: 100%;
  text-align: center;
  thead {
    // font-weight: bold;
  }
  tr,
  td {
    padding: 5px 5px;
    line-height: 21px;
    border: 1px solid #ddd;
    vertical-align: middle;
    box-sizing: border-box;
  }
  td div {
    vertical-align: middle;
  }
  &.info tr,&.info td {
    padding: 10px 0;
  }
  &.info tbody tr td:nth-of-type(odd) {
    // font-weight: bold;
    background: #f7f7f8;
  }
  &.info tbody tr td:nth-of-type(even) {
    text-align: left;
    padding-left: 5px;
    padding-right: 5px;;
  }
  &.bill tr,&.bill td {
    padding: 5px 5px;
  }
  &.bill {
    thead td {
      font-weight: bold;
      padding: 10px 5px;
    }
    .el-button {
      padding: 0;
    }
  }
  .el-input__inner {
    border-radius: 0;
    // border: 0;
    // border-bottom: 1px solid #DCDFE6;
  }
}
// 业务系统新增
/*
 * 弹框双栏
 */
 .dia-double {
  display: flex;
  .dia-double-l {
    // flex: 1;
    border: 1px solid #ddd;
    padding: 20px 10px;
  }
  .dia-double-r {
    flex: 1;
    border: 1px solid #ddd;
    padding: 10px;
    margin-left: 20px;
    .el-tab-pane {
      padding: 10px 0;
      overflow: auto;
      .iconitem {
        font-size: 20px;
        display: inline-block;
        height: 45px;
        width: 45px;
        box-sizing: border-box;
        line-height: 45px;
        padding: 10px 10px;
        cursor: pointer;
        white-space: nowrap;
        svg {
          margin-right: 5px;
        }
      }
    }
  }
  .show-icon {
    margin-left: 10px;
    margin-bottom: -5px;
    font-size: 25px;
  }
}

// 业务实体页面 特殊处理
.box-solid{
  .box-header{
      color: #444;
      display: block;
      padding: 25px;
      position: relative;
      .box-tools{
          position: absolute;
          right: 10px;
          top: 14px;
      }
  }
  .border-bottom{
    border-bottom: 1px solid #e2e7ed;
  }
  .box-body {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    padding: 10px;
  }
}

}
/*
 * 高度特别大的弹框
 * 在el-dialog 加上 class="big-dialog"
 */
.pa-platform-el-dialog.big-dialog .el-dialog {
  height: calc(100% - 30px);
  .el-dialog__body {
    overflow: auto;
    height: calc(100% - 138px);
  }
}

/*
 * iframe的弹框
 * 在el-dialog 加上 class="iframe-dialog"
 */
 .iframe-dialog .el-dialog {
  height: calc(100% - 30px);
  .el-dialog__header {
    padding: 10px 10px;
  }
  .el-dialog__headerbtn {
    top: 10px;
  }
  .el-dialog__body {
    padding: 0 20px;
    overflow: auto;
    height: calc(100% - 50px)
  }
}

// 以下是统一对element组件 的 reset
$table-hove-color: #ecf5ff;
$table-active-color: #cfe6ff;
$border-color: #dcdfe6;

.pa-platform-el-dialog.el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pa-platform-el-dialog.big-dialog .el-dialog {
  height: calc(100% - 30px);

  .el-dialog__body {
    overflow: auto;
    height: calc(100% - 138px);
  }
}

.pa-platform-el-dialog .el-dialog {
  .el-dialog__header {
    padding: 18px 20px 10px;
    .el-dialog__title {
      font-size: 18px;
    }
  }
  .el-button {
    font-size: 14px;
  }
}

.pa-platform-el-dialog.el-dialog__wrapper.dialog-fade-leave-active,
.pa-platform-el-dialog.el-dialog__wrapper.dialog-fade-leave-to {
  animation: none;
}
.pa-platform-el-dialog.el-dialog__wrapper.dialog-fade-enter-active,
.pa-platform-el-dialog.el-dialog__wrapper.dialog-fade-enter-to {
  animation: none;
}


.el-select-dropdown{
  .el-scrollbar {
    .el-select-dropdown__wrap {
      .el-select-dropdown__list {
        .el-select-dropdown__item {
          font-size: 14px;
          .tree-container .el-tree__empty-text {
            font-size: 14px;
          }
          .el-input {
            .el-input__inner {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

.el-autocomplete-suggestion.el-popper.pa-autocomplete-suggestion {
  .el-scrollbar {
    .el-autocomplete-suggestion__wrap {
      .el-scrollbar__view.el-autocomplete-suggestion__list {
        li {
          font-size: 16px
        }
      }
    }
  }
}

.el-message.pa-el-message.el-message--warning,
.el-message.pa-el-message.el-message--success,
.el-message.pa-el-message.el-message--error,
.el-message.pa-el-message.el-message--info {
  .el-message__content {
    font-size: 14px;
  }
  .el-message-box__content {
    .el-message-box__container {
      .el-message-box__message {
        P {
          font-size: 14px;
        }
      }
    }
  }
}


// .el-message-box__wrapper.pa-el-message-box__wrapper {
//   .el-message-box {
//     .el-message-box__content {
//       .el-message-box__container {
//         .el-message-box__message {
//           P {
//             font-size: 14px;
//           }
//         }
//       }
//     }
//     .el-message-box__btns{
//       .el-button {
//         font-weight: 400;
//         font-size: 14px;
//         text-align: center;
//       }
//     }
//   }
// }

.vxe-table--tooltip-wrapper,
.theme--dark,
.is--arrow {
  z-index: 9999 !important;
  .vxe-table--tooltip-content {
    font-size: 14px;
  }
}

.el-tooltip__popper {
  font-size: 14px;
}

// .pa-el-message-box__wrapper .el-message-box__status {
//   transform: inherit;
//   top: 0;
// }

.table-isRequired:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
