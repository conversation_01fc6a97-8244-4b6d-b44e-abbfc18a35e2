// 以下是统一对element组件 的 reset
$table-hove-color: #ecf5ff;
$table-active-color: #cfe6ff;
$border-color: #dcdfe6;

.el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.el-icon-aliiconshibaizhuangtai:before{color: red !important;}
.custom-message-dlg.el-dialog .el-dialog__body{
  padding: 5px 20px 10px;
}
.el-dialog {
  margin: 0 !important;
  border-radius: 3px;
  overflow: auto;
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  // display: flex;
  // flex-direction: column;
  .el-dialog__header {
    padding: 16px 20px 16px;
    border: 1px solid #DDDDDD;
    .el-icon-close:before {
      color: #333333;
    }
  }

  .el-dialog__title {
    font-family: PingFangSC-Medium;
    font-size: 20px;
    color: #333333;
    font-weight: 500;
    // font-weight: bold;
  }

  .el-dialog__body {
    padding: 20px 30px;
    overflow: auto;
  }

  .el-dialog__body .el-table th.el-table__cell{
    background-color: #f0f5ff;
  }

  .el-dialog__body .el-table th.el-table__cell>.cell {
    font-family: PingFangSC-Medium;
    font-weight: 700;
    color: #333333;
    padding-left: 1px;
    padding-right: 1px;
  }
  .el-dialog__body .el-table__header tr, .el-table__header th {
    padding: 0;
    height: 40px;
  }
  .el-table__header th.el-table__cell {padding: 0px;}
  .el-dialog__body .el-table--border{
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body .el-table th.el-table__cell.is-leaf,
  .el-dialog__body .el-table td.el-table__cell {border-right: 1px solid #ddd!important; border-bottom: 1px solid #ddd!important; }
  .el-dialog__body {
    .el-table--group,.el-table--border {
      border: 1px solid #DDDDDD !important;
      &::after {
        background-color: #DDDDDD;
        width: 0px;
      }
    }
    .el-table {
      .el-table__header tr th:nth-last-child(2),th:last-child {
        border-right: none !important;
      }
      .el-table__row td:last-child {
        border-right: none !important;
      }
      &::before{
        height: 0px;
      }
    }
  }
  .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid #DDDDDD !important;
  }

  .el-dialog__body .el-descriptions .is-bordered .el-descriptions-item__cell { border: 1px solid #DDDDDD }
  .el-dialog__body.el-table--border .el-table__cell,
  .el-dialog__body .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed,
  .el-dialog__footer {
    padding: 0 30px 20px;
  }
}


// 出现滚动条的时候表头错位修复
.el-table th.gutter {
  display: table-cell !important;
  width: 17px !important;
}
.el-table .el-table__cell .cell {
  font-family: PingFangSC-Medium;
}
.el-table--small {
  font-size: 14px;
}


.el-progress.success {
  .el-progress-bar__inner {
    background-color: #67C23A;
  }
}

.el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
.el-table__body tr.hover-row.current-row>td,
.el-table__body tr.current-row>td,
.el-table--enable-row-hover .el-table__body tr.current-row:hover>td {
  background: $table-active-color;
}

.el-table--enable-row-hover .el-table__body tr:hover>td,
.el-table__body tr.hover-row.el-table__row--striped.current-row>td,
.el-table__body tr.hover-row.el-table__row--striped>td,
.el-table__body tr.hover-row>td {
  background: $table-hove-color;
}

// 修复新版本element-ui中多选框的头部位移
.el-table--border th.el-table-column--selection>.cell {
  // padding-left: 14px;
  padding-right: 14px;
}

// .table-border.el-table td, .table-border.el-table th, .table-border.el-table__body-wrapper .el-table.is-scrolling-left ~ .el-table__fixed {
// 	border-right: 0; // 1px solid #dcdfe6;
// }
.container-border {
  background: #fff;
  overflow: auto;
  border: 1px solid $border-color;
  height: 100%;
  box-sizing: border-box;
  // box-shadow: 0 1px 2px #ccc;
}

// step样式
.el-steps {
  // padding: 15px 10px;

  .el-step__title {
    font-size: 14px;
  }

  // .el-step__head {
  // 	display: none;
  // }#C0C4CC.
  // 由于对step的时候是从下标1开始，正在进行的step为蓝色，故reset
  .el-step__title.is-process {
    font-weight: 500;
    color: #C0C4CC;
  }

  .el-step__head.is-process {
    color: #C0C4CC;
    border-color: #C0C4CC;
  }
}

.step-title {
  display: inline-block;
  cursor: pointer;
}

// 输入框 字数提示
.el-textarea .el-input__count {
  height: 22px;
}

.el-checkbox__inner {
  border: 1px solid #ccc;
}

.el-radio {
  margin-right: 20px;
  .el-radio__inner{
    width: 16px;
    height: 16px;
  }
  .el-radio__input.is-checked {
    .el-radio__label {

    }
    .el-radio__inner {
      background: #fff;
      &::after {
        transform: translate(-50%,-50%) scale(2);
        background: #1F7FFF;
      }
    }
  }
}
.el-radio__inner {
  border: 1px solid #ccc;
}


// 次联框高度
.el-cascader-menu {
  height: inherit;
}

.el-table .el-button {
  padding: 6px 12px;
}

.el-breadcrumb {
  padding: 0 10px;
}

// 下拉框多选时不换行
.el-select__tags {
  flex-wrap: inherit;
  overflow: hidden;
}


// tabs内badge提示位置调整
.el-tabs .el-badge__content.is-fixed {
  top: 8px;
}

.el-tabs__header {
  margin: 0 0 20px;
}

// .el-upload-list--picture {
// 	.el-upload-list__item  {
// 		img {
// 			background: url('~@/common/assets/folder.jpg');
// 			background-size: 100% 100%;
// 		}
// 	}
// }

.el-tree__empty-text {
  font-size: 14px;
}

.el-form {
  .el-form-item .el-form-item__label {
    color: #252222;
  }
  .el-input,
  .el-select {
    width: 100%;
  }

  .el-radio {
    line-height: 28px;
  }
}

.el-tabs__nav-wrap::after {
  height: 1px;
  //background-color: #EEEEEE;
}

// 树 高亮行
.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: $table-active-color;
}

// 按钮
.btn-normal {
  min-width: 80px;
  font-size: 14px;
  padding: 0px 12px;
  line-height: 30px;
}
.vue-bifrostIcApp .el-button {
  font-family: PingFangSC-Regular;
  background: #fff !important;
  color: #006CFF !important;
  border: 1px solid #006CFF !important
  // &:focus, &:hover {
  //   color: #478BFE;
  //   border-color: #478BFE;
  //   background-color: #FFF;
  // }
  // &:active{
  //   color: #1070E1;
  //   border-color: #1070E1;
  //   background-color: #FFF;
  // }
}

.el-button.el-button--primary  {
  // color: #FFF;
  // background-color: #1F7FFF;
  // border-color: #1F7FFF;
  background: #006CFF!important;
  border: 1px solid #006CFF !important;
  position: relative;
  color: #fff!important;
  &:hover,&:focus{
    // color: #FFF;
    // background: #478BFE;
    // border-color: #478BFE;
    background: #006CFF !important;
    color: #FFF !important;
    border: 1px solid #006CFF !important
  }
  &:active {
    // color: #FFF;
    // background: #1070E1;
    // border-color: #1070E1;
    background: #006CFF !important;
    color: #FFF !important;
    border: 1px solid #006CFF !important
  }
  // &.is-disabled{
  //   color: #FFF;
  //   background-color: #a0cfff;
  //   border-color: #a0cfff;
  // }
}
.el-button--primary.is-plain,
.el-button--info.is-plain {
  color: #666666;
  background: #fff;
  border-color: #dcdfe6;
}

.el-button--info.is-plain:focus,
.el-button--info.is-plain:hover {
  background: #dcdcdc;
  border-color: #dcdcdc;
  color: #606266;
}
.el-button--primary.is-plain.is-disabled {
  background-color: #409EFF;
  border-color: #EBEEF5;
  color: #C0C4CC;
}

.big-dialog .el-dialog {
  height: calc(100% - 30px);

  .el-dialog__body {
    overflow: auto;
    height: calc(100% - 138px);
  }
}

.el-table .el-button+.el-button {
  margin-left: 0;
}

.el-popover {
  max-width: 400px;
  &--plain {
    padding: 8px;
  }
  .mod-menu {
    .menu-list__input,
    .icon-list__input {
      > .el-input__inner {
        cursor: pointer;
      }
    }
    &__icon-popover {
      max-width: 370px;
    }
    &__icon-list {
      max-height: 180px;
      padding: 0;
      margin: -8px 0 0 -8px;
      > .el-button {
        padding: 8px;
        margin: 8px 0 0 8px;
        > span {
          display: inline-block;
          vertical-align: middle;
          width: 18px;
          height: 18px;
          font-size: 18px;
        }
      }
    }
    .icon-list__tips {
      font-size: 18px;
      text-align: center;
      color: #e6a23c;
      cursor: pointer;
    }
  }
  .mod-menu__icon-list {
    overflow: auto;
  }
}

// tooltip 显示宽度最多50%
.el-tooltip__popper {
  max-width: 50%;
}
.el-tooltip__popper.is-light {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.5);
  .popper__arrow {
    border-top-color: #FFF!important;
  }
}

// 财务核算域模块禁用的颜色修正
.el-input.is-disabled .el-input__inner {
  color: #606266;
}
.el-textarea.is-disabled .el-textarea__inner  {
  color: #606266;
}
.el-radio__input.is-checked.is-disabled+span.el-radio__label {
  color: #606266;
}

.el-radio__input.is-disabled.is-checked .el-radio__inner {
  border-color: #a5a8ad;
}

.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #a5a8ad;
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  border-color: #a5a8ad;
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #a5a8ad;
}

.el-checkbox__input.is-disabled.is-checked+span.el-checkbox__label {
  color: #606266;
}

// el-tabs 组件type为card时的同意样式
.el-tabs__item {
  height: 32px;
  color: #333333;
  line-height: 32px;
}
.el-tabs--card {
  >.el-tabs__header.is-top {
    margin-top: 0px;
    border-bottom: none;
    .el-tabs__nav-next, .el-tabs__nav-prev {
      line-height: 32px;
    }
    .el-tabs__nav {
      border: none;
    }
    .el-tabs__item {
      height: 32px !important;
      line-height: 32px !important;
      padding: 0px 12px;
      border: 1px solid #DDDDDD;
      border-right-color: transparent;
      &:last-child {
        border-right-color: #DDDDDD;
        padding-right: 12px;
      }
      &:nth-child(2){
        padding-left: 12px;
      }
    }
    .el-tabs__item.is-active {
      color: #1F7FFF;
      font-weight: 500;
      border-color: rgba(31,127,255,1);
      & + .el-tabs__item {//border重叠问题
        border-left: transparent;
      }
    }
  }
}

.el-tabs.el-tabs--top {
  display: flex;
  flex-direction: column;
  .el-tabs__content {
    flex: 1;
    overflow: auto;
  }
}

//下面是对新原型样式的重置
.el-form-item__label{
  font-weight:bold
}
.el-dialog .el-dialog__header{
  padding:10px 20px;
  .el-dialog__headerbtn{
    top:12px;
    font-size: 22px;
  }
}
.el-dialog .el-dialog__title{
  font-size: 16px;
  color: #333333;
  line-height: 26px;
  font-weight: 500;
}

.el-descriptions-item__label.is-bordered-label {
  background-color: #f0f5ff
}

// .el-table th.el-table__cell>.cell {font-weight: 700;color: #333333;}
// .el-table th.el-table__cell {background-color: #f0f5ff;}
// .el-table--small .el-table__cell { padding: 10px 0px; }
.el-table .cell { color:#333 }
// .el-table .el-table-column--selection .cell{padding: 0;}
// .el-table tr.el-table__row.checkedTR td.el-table__cell { background: #F4FCFF !important; }
// .el-table tr.el-table__row.currentTR td.el-table__cell { background: #f9ab6a !important; }
