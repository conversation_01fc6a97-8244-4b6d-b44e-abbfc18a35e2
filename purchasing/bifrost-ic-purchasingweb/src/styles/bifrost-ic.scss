.vue-bifrostIcApp{
    padding: 32px 20px;
    box-sizing: border-box;
    background: #fff;
    font-size: 14px;
    // 一级页面 布局
    .container-border{
      font-size: 14px;
      border: 0;
      // 头部搜索栏
      .column-top{
          padding: 0 12px 0;
          // 搜索框
          .filters-normal{
              padding: 2px 12px 2px 0;
              .single-filter{
                  width: 18%;
                  display: inline-flex;
                  white-space: nowrap;
                  align-items: center;
                  margin: 0 10px 15px 0;
                  .el-input,.el-select{
                      width: 100%;
                      height: 100%;
                      font-size: inherit;
                  }
                  .el-date-editor .el-range-input{
                    font-size: 14px;
                  }
              }
              .el-button{
                  height: 35px;
                  margin-top: 0;
                  padding-top: 7px;
                  padding-bottom: 7px;
                  font-size: inherit;
              }
                // tab切换页
            .pa_tabs {
              display: block;
              margin-bottom: 32px;
              .el-tabs {
                .el-tabs__header{
                  margin: 0 0 8px;
                  .el-tabs__nav-wrap:after {
                    height: 1px;
                    background-color: #dcdfe6;
                  }
                  .el-tabs__nav-scroll {
                    .el-tabs__item {
                      padding: 0 32px;
                      font-size: 14px;
                      margin-right: 30px;
                      height: 100%;
                    }
                    .is-active {
                      border-bottom: 2px solid #40a9ff;
                    }
                  }
                  .el-tabs__active-bar {
                    display: none;
                  }
                }
              }
            }
          }
        // 按钮栏
        .buttons-normal{
          border: 1px solid #ddd;
          border-bottom: transparent;
          padding: 4px 0;
          .el-button{
              margin: 5px 10px;
              background: #fff;
              font-size: inherit;
              color: #606266;
              font-size: 14px;
              border: 1px solid transparent;
          }
          .el-button--primary.is-plain {
            color: #606266;
            background: #fff;
            &:hover {
              background: #409eff;
              color: #fff;
            }
          }
          .el-button--danger {
            color: #F56C6C;
            background: #fef0f0;
            border-color: #fbc4c4;
            &:hover {
              color: #fff;
              background-color: #f56c6c;
              border-color: #f56c6c;
            }
            }
          // .el-button--primary:first-child {
          //   color: #fff;
          //   background-color: #409eff;
          //   border-color: #409eff;
          // }
                //禁用
          .el-button--primary.is-plain.is-disabled {
            background-color: #fff;
            color: #c0c4cc;
          }
          // 禁用
          .el-button--primary:first-child.is-disabled{
            color: #fff;
            background-color: #a0cfff;
            border-color: transparent;
          }
          .el-icon-arrow-down{
            font-size: 12px;
          }
        }

      }
      // 内容区
      .column-bottom{
        // 左侧栏
        .dbColLeft{
          .el-tree-node__content{
            font-size: 14px;
            font-family:"initial"
          }
        }
        // 如果有收缩按钮
        .main-border{
          box-sizing: border-box;
          width: 100%;
          padding: 0 12px 8px;
          .el-tabs__header{
            margin: 0 0 8px;
          }
          // 按钮栏
            .buttons-normal{
              border: 1px solid #ddd;
              border-bottom: transparent;
              padding: 4px 0;
              .el-button{
                  margin: 5px 10px;
                  background: #fff;
                  font-size: inherit;
                  color: #606266;
                  font-size: 14px;
                  border: 1px solid transparent;
              }
              .el-button--primary.is-plain {
                color: #606266;
                background: #fff;
                &:hover {
                  background: #409eff;
                  color: #fff;
                }
              }
              .el-button--danger {
                color: #F56C6C;
                background: #fef0f0;
                border-color: #fbc4c4;
                &:hover {
                  color: #fff;
                  background-color: #f56c6c;
                  border-color: #f56c6c;
                }
              }
              .el-button--primary:first-child {
                color: #fff;
                background-color: #409eff;
                border-color: #409eff;
              }
              //禁用
              .el-button--primary.is-plain.is-disabled {
                background-color: #fff;
                color: #c0c4cc;
                }
            // 禁用
              .el-button--primary:first-child.is-disabled{
                color: #fff;
                background-color: #a0cfff;
                border-color: transparent;
              }
              // 按钮图标
              .el-icon-arrow-down{
                font-size: 12px;
              }
          }
        }
      }
    }
    // 默认按钮样式
    .el-button{
      height: 32px;
      margin-top: 0;
      padding-top: 7px;
      padding-bottom: 7px;
      font-size: 14px;
      line-height: 1;
      // font-size: inherit;
    }
    // 时间日期
    .el-date-editor{
      .el-range-separator{
        font-size: 14px;
      }
    }
    // 输入框
    .el-input,.el-select{
      // width: 100%;
      // height: 100%;
      font-size: 14px;
    }
    .el-date-editor .el-range-input{
      font-size: 14px;
    }
   // vxe表格
    .vxe-table {
      .vxe-cell {
        color: #333;
        line-height: 24px;
        font-size: 14px;
        height: auto;
        font-family: "initial";
        .el-button {
          font-size: 14px;
          color: #1890ff;
        }
        .el-input {
          .el-input__inner {
            font-size: 14px;
          }
        }
        .el-button {
          &:hover {
            color: #40a9ff;
          }
        }
        .deleteBtn {
          color: #fa5555;
          &:hover {
            color: #eb4141;
          }
        }
        .is-disabled{
          color: #C0C4CC;
        }
      }
    }
  // element 表格
    .el-table{
      font-family: "initial";
      font-size: 14px;
      border: 0;
      border-top: 1px solid #dcdfe6;
      border-left: 1px solid #dcdfe6;
      position: relative;
      overflow: hidden;
      box-sizing: border-box;
      flex: 1;
      width: 100%;
      max-width: 100%;
      color: #606266;
      .el-checkbox__inner, .el-radio__inner {
          border: 1px solid #ccc;
      }
      th{
        color: #3f536e;
        background: #f7f7f8;
        font-weight: bolder;
        // padding: 7px 0;
        border-right: 1px solid #dcdfe6;
        border-right: 1px solid #dcdfe6;
      }
      td{
        padding: 5px 0;
        font-size: 14px;
        border-right: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
      }
      .cell{
        font-family: "initial";
        font-size: 14px;
        line-height: 24px;
        .el-button{
          padding: 6px 12px;
          color: #1890ff;
          font-size: 14px;
          margin-left: 0px;
          height: 30px;
          font-family: "initial"
        }
      }
    }
    // 内部表格分页
    .bottom-normal{
      margin: 24px 0 0;
      position: relative;
      line-height: 32px;
      font-size: 14px;
      text-align: right;
      color: rgba(0,0,0,.65);
      font-family: "initial";
      .pagination-info{
        position: absolute;
        left: 10px;
        font-family: "initial";
      }
      .el-pagination{
        white-space: nowrap;
        padding: 2px 5px;
        color: #303133;
        font-weight: 700;
        right: 10px;
        font-size: 14px;
        font-family: "initial";
        .el-pager {
          li {
            margin: 0 5px;
            min-width: 30px;
            border-radius: 2px;
            font-size: 14px;
            font-family: "initial";
          }
        }
      }
      .el-input__inner,.el-pagination__jump{
        font-size: 14px;
      }
    }
    // 公式管理开始
    .data-List,.client-List{
      .head {
        .el-form-item {
          margin-bottom: 8px;
        }
        .tools {
          .el-button{
            background: #fff;
            font-size: inherit;
            color: #606266;
            font-size: 14px;
            margin-bottom: 6px;
            border: 1px solid #dcdfe6;
        }
          .el-button+.el-button {
            margin-left: 5px;
          }
        }
        .el-button+.el-button {
          margin-left: 5px;
        }
      }
      .pagination-container {
        padding-top: 5px;
      }
    }
    //业务实体
    .filter-right-container {
      position: fixed;
      top: 108px;
      right: 0;
      bottom: 0;
      z-index: 10;
      width: 345px;
      background-color: #fafafa;
      -webkit-box-shadow: #dad9d9 0 4px 8px;
      box-shadow: 0 4px 8px #dad9d9;
      .box {
        .box-header {
          padding: 10px 20px;
          background-color: #fff;
          box-shadow: 0 2px 2px #e8e7e8;
          .box-tools {
            position: absolute;
            right: 10px;
            top: 14px;
          }
        }
        .box-body {
          padding: 0 0 0 20px;
          color: #666;
          font-size: 0.9em;
          background-color: #FAFAFA;
          overflow-y: auto;
          overflow-x: hidden;
          .fieldset {
            margin: 20px 0 0;
            border: 0;
            legend {
              padding: 0;
              font-size: 0.9em;
              margin: 0 0 10px;
            }
            input {
              width: 150px;
              height: 30px;
              padding-left: 10px;
              padding-right: 20px;
              -webkit-box-sizing: border-box;
              box-sizing: border-box;
              margin: 0 9px 8px 0;
              border: 1px solid #cfdae5;
              border-radius: 0;
              color: #666;
              background-color: #fff;
            }
            .el-select .el-tag {
              max-width: 75px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              .el-select__tags-text {
                max-width: 75px;
              }
            }
            .el-input__icon {
              height: 85%;
              margin-right: 5px;
            }
            .el-select {
              .el-tag__close {
              }
            }
            .el-date-editor {
              input {
                padding-left: 30px;
              }
            }
          }
        }
      }
    }
    // 表格分页
    .el-pagination{
      .el-pagination__total{
        font-size: 14px;
      }
      .el-pagination__sizes{
        .el-input{
          .el-input__inner{
            font-size: 14px;
          }
        }
      }
      .el-pager{
        li{
          font-size: 14px;
        }
      }
      .el-pagination__jump{
        font-size: 14px;
      }
    }
  // 公式管理结束
}
// 滚动条样式
*{
  scrollbar-color: #d4d5d9 #fff;
  scrollbar-width: thin;
}
