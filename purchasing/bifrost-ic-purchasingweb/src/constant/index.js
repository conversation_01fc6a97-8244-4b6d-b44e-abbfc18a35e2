// 常量文件 模块_功能_描述
// audit 审核
export const AUDIT_RULE_TYPE = {
  TEXT: '文本框',
  BUTTON: '确定按钮',
  SELECT: '下拉选择',
  RADIO: '单选按钮',
  CHECK: '选择按钮'
}

export const AUDIT_LOG_STATUS = {
  PASS: '通过',
  NOPASS: '不通过'
}

export const AUDIT_LOG_CONTROL = {
  NO_CONTROL: '无控制',
  BAN_WARNING: '无所有级不通过', // 有禁止级或示警级不通过
  BAN: '无禁止级不通过', // 无禁止级通过
  WARNING: '无示警级不通过', // 无示警级通过
  ALL: '所有审核需通过'
}

export const AUDIT_LOG_LEVEL = {
  PROMPT: '提示',
  BAN: '禁止', // 无禁止级通过
  WARNING: '示警' // 无示警级通过
}

export const AUDIT_LOG_TYPE = {
  HAND: '手动审核',
  AUTO: '自动审核',
  PROMPT: '审核提示'
}

// 首页我的tab前缀
export const HOME_HOME_PREFIX = {
  APPLY: 'apply-',
  AUDIT: 'audit-'
}

// 区块表单常量
export const CFORM_BLOCK_TYPE = {
  HANDLERKEY: '行表单'
}

export const COMMON_METHODS = ['mounted', 'onSave']
const INPUT_METHODS = ['change', 'blur', 'input']
export const COL_TYPE = {
  '文本(30)': INPUT_METHODS,
  '文本(100)': INPUT_METHODS,
  '文本(255)': INPUT_METHODS,
  '文本(不限)': INPUT_METHODS,
  '小数': INPUT_METHODS,
  '金额': INPUT_METHODS,
  '整数': INPUT_METHODS,
  '百分比': INPUT_METHODS,
  '日期': ['change'],
  '日期范围': ['change'],
  '多选': ['change'],
  '单选': ['change'],
  '下拉框': ['change'],
  '弹框': ['change', 'addRefParams', 'refDataBefore', 'refDataAfter'],
  '审核': INPUT_METHODS, // 文本
  'html': INPUT_METHODS, // 文本
  '公示': COMMON_METHODS
  // '隐藏框': COMMON_METHODS
}
// 方法传参
export const ARG_METHOD = {
  'change': ['value'],
  'blur': ['value'],
  'input': ['value'],
  'mounted': [],
  'onSave': ['colItem', 'data', 'util'],
  'addRefParams': ['params'],
  'refDataBefore': ['selectedData', 'params', 'callbackCloseRefDlg', 'setBtnUnLoad'],
  'refDataAfter': ['selectedData']
}
// 方法传参
export const ARG_RFORM_METHOD = {
  'change': ['scope'],
  'blur': ['scope'],
  'mounted': [],
  'addRefParams': ['scope', 'params'],
  'refDataBefore': ['scope', 'selectedData', 'callbackCloseRefDlg', 'setBtnUnLoad'],
  'refDataAfter': ['scope', 'selectedData']
}
// 参数解释
export const ARG_DESC = {
  'value': '值',
  'scope': '表格当前行数据',
  'params': '条件（传递给后端的参数）',
  'selectedData': '参照选中的数据',
  'callbackCloseRefDlg': '关闭参照弹窗函数',
  'setBtnUnLoad': '取消参照弹窗提交按钮的loading',
  'data': '保存的数据',
  'colItem': '当前要素',
  'util': '工具类方法'
}

// 参数解释
export const CYSTOMER = 'customer'
