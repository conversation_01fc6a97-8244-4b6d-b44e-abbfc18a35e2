{"rightCollapsedContainer": {"prefix": "@/components", "path": "./base/right-collapsed-container.vue", "name": "right-collapsed-container"}, "cformmodule": {"prefix": "@/components", "path": "./bizz/cformmodule/index.vue", "name": "cformmodule"}, "previewList": {"prefix": "@/components", "path": "./bizz/cformmodule/preview-list-dialog.vue", "name": "preview-list"}, "contracpartyList": {"prefix": "@/components", "path": "./bizz/contracparty/contracparty-list.vue", "name": "contracparty-list"}, "contracpartydialog": {"prefix": "@/components", "path": "./bizz/contracparty/contracpartydialog.vue", "name": "contracpartydialog"}, "contracparty": {"prefix": "@/components", "path": "./bizz/contracparty/index.vue", "name": "contracparty"}, "einvoiceAuthRecordDialog": {"prefix": "@/components", "path": "./bizz/einvoice/einvoice-auth-record-dialog.vue", "name": "einvoice-auth-record-dialog"}, "invInvoiceAuthRecordDialog": {"prefix": "@/components", "path": "./bizz/einvoice/inv-invoice-auth-record-dialog.vue", "name": "inv-invoice-auth-record-dialog"}, "attInfoDialog": {"prefix": "@/components", "path": "./bizz/file/att-info-dialog.vue", "name": "att-info-dialog"}, "attachAuditDialog": {"prefix": "@/components", "path": "./bizz/file/attach-audit-dialog.vue", "name": "attach-audit-dialog"}, "attachAuditExtend": {"prefix": "@/components", "path": "./bizz/file/attach-audit-extend.vue", "name": "attach-audit-extend"}, "attachTab": {"prefix": "@/components", "path": "./bizz/file/attach-tab.vue", "name": "attach-tab"}, "baseAttachment": {"prefix": "@/components", "path": "./bizz/file/attachment.vue", "name": "base-attachment"}, "editPersonTime": {"prefix": "@/components", "path": "./bizz/file/edit-person-time-dialog.vue", "name": "edit-person-time"}, "fileList": {"prefix": "@/components", "path": "./bizz/file/file-list.vue", "name": "file-list"}, "filedialog": {"prefix": "@/components", "path": "./bizz/file/filedialog.vue", "name": "filedialog"}, "basefile": {"prefix": "@/components", "path": "./bizz/file/index.vue", "name": "basefile"}, "phoneQrcode": {"prefix": "@/components", "path": "./bizz/file/phone-qrcode-dialog.vue", "name": "phone-qrcode"}, "uploadFile": {"prefix": "@/components", "path": "./bizz/file/uploadFile.vue", "name": "uploadFile"}, "eInvoice": {"prefix": "@/components", "path": "./bizz/invoice/e-invoice.vue", "name": "e-invoice"}, "baseinvoice": {"prefix": "@/components", "path": "./bizz/invoice/index.vue", "name": "baseinvoice"}, "invoiceAddDialog": {"prefix": "@/components", "path": "./bizz/invoice/invoice-add-dialog.vue", "name": "invoice-add-dialog"}, "invoiceEditDialog": {"prefix": "@/components", "path": "./bizz/invoice/invoice-edit-dialog.vue", "name": "invoice-edit-dialog"}, "logdetaildialog": {"prefix": "@/components", "path": "./bizz/log/logdetaildialog.vue", "name": "logdetaildialog"}, "logdialog": {"prefix": "@/components", "path": "./bizz/log/logdialog.vue", "name": "logdialog"}, "nvg": {"prefix": "@/components", "path": "./bizz/nvg/index.vue", "name": "nvg"}, "attachEinvoiceTab": {"prefix": "@/components", "path": "./bizz/payee/attach-einvoice-tab.vue", "name": "attach-einvoice-tab"}, "basepayee": {"prefix": "@/components", "path": "./bizz/payee/index.vue", "name": "basepayee"}, "indicatorsDialog": {"prefix": "@/components", "path": "./bizz/payee/indicatorsDialog.vue", "name": "indicatorsDialog"}, "payeeAssemblyFunc": {"prefix": "@/components", "path": "./bizz/payee/payee-assembly-func.vue", "name": "payee-assembly-func"}, "报销单+借款单": {"prefix": "@/components", "path": "./bizz/payee/payee-assembly.vue", "name": "报销单+借款单"}, "payeeAuditExtend": {"prefix": "@/components", "path": "./bizz/payee/payee-audit-extend.vue", "name": "payee-audit-extend"}, "payeeListTab": {"prefix": "@/components", "path": "./bizz/payee/payee-list-tab.vue", "name": "payee-list-tab"}, "baPayeeList": {"prefix": "@/components", "path": "./bizz/payee/payee-list.vue", "name": "ba-payee-list"}, "payeedialog": {"prefix": "@/components", "path": "./bizz/payee/payeedialog.vue", "name": "payeedialog"}, "payeeImportDialog": {"prefix": "@/components", "path": "./bizz/payee/payeeImportDialog.vue", "name": "payeeImportDialog"}, "payeelistdialog": {"prefix": "@/components", "path": "./bizz/payee/payeelistdialog.vue", "name": "payeelistdialog"}, "teacherImportDialog": {"prefix": "@/components", "path": "./bizz/teacher/teacherImportDialog.vue", "name": "teacherImportDialog"}, "blockAssociationCformDialog": {"prefix": "@/components", "path": "./block/block-association-cform-dialog.vue", "name": "block-association-cform-dialog"}, "block-表单": {"prefix": "@/components", "path": "./block/block-cform.vue", "name": "block-表单"}, "block-表单组件": {"prefix": "@/components", "path": "./block/block-cformm.vue", "name": "block-表单组件"}, "blockContainer": {"prefix": "@/components", "path": "./block/block-container.vue", "name": "block-container"}, "blockRformExtendDigitalTicketsInvoiceEntity": {"prefix": "@/components", "path": "./block/block-digital-tickets-invoice.vue", "name": "blockRformExtend-DigitalTicketsInvoiceEntity"}, "block-附件": {"prefix": "@/components", "path": "./block/block-file.vue", "name": "block-附件"}, "block-电子发票": {"prefix": "@/components", "path": "./block/block-invoice.vue", "name": "block-电子发票"}, "blockRformExtendTemplate": {"prefix": "@/components", "path": "./block/block-rform-template.vue", "name": "blockRformExtend-template"}, "block-行表单": {"prefix": "@/components", "path": "./block/block-rform.vue", "name": "block-行表单"}, "blockView": {"prefix": "@/components", "path": "./block/block-view.vue", "name": "block-view"}, "cformTitle": {"prefix": "@/components", "path": "./block/cform-title.vue", "name": "cform-title"}, "leftContainer": {"prefix": "@/components", "path": "./block/components/left-container.vue", "name": "left-container"}, "addMethodsDialog": {"prefix": "@/components", "path": "./cform/add-methods-dialog.vue", "name": "add-methods-dialog"}, "colSetting": {"prefix": "@/components", "path": "./cform/col-setting.vue", "name": "col-setting"}, "elementArea": {"prefix": "@/components", "path": "./cform/element-area.vue", "name": "element-area"}, "elementConfig": {"prefix": "@/components", "path": "./cform/element-config.vue", "name": "element-config"}, "fileTypeSettingDialog": {"prefix": "@/components", "path": "./cform/file-type-setting-dialog.vue", "name": "file-type-setting-dialog"}, "formCanvas": {"prefix": "@/components", "path": "./cform/form-canvas.vue", "name": "form-canvas"}, "formCustomContent": {"prefix": "@/components", "path": "./cform/form-custom-content.vue", "name": "formCustomContent"}, "formDetail-详情": {"prefix": "@/components", "path": "./cform/form-datail.vue", "name": "formDetail-详情"}, "formExtend-表单模板": {"prefix": "@/components", "path": "./cform/form-extend-template.vue", "name": "formExtend-表单模板"}, "formFree": {"prefix": "@/components", "path": "./cform/form-free.vue", "name": "form-free"}, "formMoreSetting": {"prefix": "@/components", "path": "./cform/form-more-setting.vue", "name": "form-more-setting"}, "formPermissionsList": {"prefix": "@/components", "path": "./cform/form-permissions-list.vue", "name": "formPermissionsList"}, "formRegular": {"prefix": "@/components", "path": "./cform/form-regular.vue", "name": "form-regular"}, "formSaveDlg": {"prefix": "@/components", "path": "./cform/form-save-dlg.vue", "name": "form-save-dlg"}, "formSignaturePositionDlg": {"prefix": "@/components", "path": "./cform/form-signature-position-dlg.vue", "name": "form-signature-position-dlg"}, "formTypeSetting": {"prefix": "@/components", "path": "./cform/form-type-setting.vue", "name": "form-type-setting"}, "iframePdf": {"prefix": "@/components", "path": "./cform/iframe-pdf.vue", "name": "iframe-pdf"}, "incRemind": {"prefix": "@/components", "path": "./cform/incremind/inc-remind.vue", "name": "inc-remind"}, "cformm": {"prefix": "@/components", "path": "./cform/index.vue", "name": "cformm"}, "cformAuthDialog": {"prefix": "@/components", "path": "./cform/rule/cform-auth-dialog.vue", "name": "cform-auth-dialog"}, "cformDatarefDialog": {"prefix": "@/components", "path": "./cform/rule/cform-dataref-dialog.vue", "name": "cform-dataref-dialog"}, "cformRuleDialog": {"prefix": "@/components", "path": "./cform/rule/cform-rule-dialog.vue", "name": "cform-rule-dialog"}, "showPdf": {"prefix": "@/components", "path": "./cform/show-pdf.vue", "name": "showPdf"}, "formTabSaveBasicExtend": {"prefix": "@/components", "path": "./cform/tab/form-tab-save-basic-extend.vue", "name": "form-tab-save-basic-extend"}, "form-tab-save-基本信息": {"prefix": "@/components", "path": "./cform/tab/form-tab-save-basic.vue", "name": "form-tab-save-基本信息"}, "form-tab-save-分年支出计划": {"prefix": "@/components", "path": "./cform/tab/form-tab-save-budget.vue", "name": "form-tab-save-分年支出计划"}, "form-tab-save-文件依据": {"prefix": "@/components", "path": "./cform/tab/form-tab-save-file.vue", "name": "form-tab-save-文件依据"}, "formTabSaveForm": {"prefix": "@/components", "path": "./cform/tab/form-tab-save-form.vue", "name": "form-tab-save-form"}, "form-tab-save-三级明细多选项": {"prefix": "@/components", "path": "./cform/tab/form-tab-save-multiple.vue", "name": "form-tab-save-三级明细多选项"}, "form-tab-save-三级明细富文本": {"prefix": "@/components", "path": "./cform/tab/form-tab-save-rich-text.vue", "name": "form-tab-save-三级明细富文本"}, "form-tab-save-列表编辑": {"prefix": "@/components", "path": "./cform/tab/form-tab-save-row-edit.vue", "name": "form-tab-save-列表编辑"}, "formTabSave": {"prefix": "@/components", "path": "./cform/tab/form-tab-save.vue", "name": "form-tab-save"}, "formTabSettingDlg": {"prefix": "@/components", "path": "./cform/tab/form-tab-setting-dlg.vue", "name": "form-tab-setting-dlg"}, "formTabSetting": {"prefix": "@/components", "path": "./cform/tab/form-tab-setting.vue", "name": "form-tab-setting"}, "formTanSaveBasicYear": {"prefix": "@/components", "path": "./cform/tab/form-tan-save-basic-year.vue", "name": "form-tan-save-basic-year"}, "checkboxtree": {"prefix": "@/components", "path": "./checkboxtree/index.vue", "name": "checkboxtree"}, "bookSetZtree": {"prefix": "@/components", "path": "./classifytree/bookSetZtree.vue", "name": "bookSetZtree"}, "classifyZtree": {"prefix": "@/components", "path": "./classifytree/classifyZtree.vue", "name": "classifyZtree"}, "classifytree": {"prefix": "@/components", "path": "./classifytree/index.vue", "name": "classifytree"}, "closingItemDialog": {"prefix": "@/components", "path": "./cm/drawup/closing-item-dialog.vue", "name": "closingItemDialog"}, "cmFileDialog": {"prefix": "@/components", "path": "./cm/drawup/cm-file-dialog.vue", "name": "cmFileDialog"}, "cmInfoListTab": {"prefix": "@/components", "path": "./cm/drawup/cm-info-list-tab.vue", "name": "cm-info-list-tab"}, "cmInfoList": {"prefix": "@/components", "path": "./cm/drawup/cm-info-list.vue", "name": "cm-info-list"}, "cmRelevantBaList": {"prefix": "@/components", "path": "./cm/drawup/cm-relevant-ba-list.vue", "name": "cm-relevant-ba-list"}, "cmRelevantBaTab": {"prefix": "@/components", "path": "./cm/drawup/cm-relevant-ba-tab.vue", "name": "cm-relevant-ba-tab"}, "contracpartyListTab": {"prefix": "@/components", "path": "./cm/drawup/contracparty-list-tab.vue", "name": "contracparty-list-tab"}, "合同": {"prefix": "@/components", "path": "./cm/drawup/contract-assembly.vue", "name": "合同"}, "cnDialog": {"prefix": "@/components", "path": "./cm/drawup/contract-dialog.vue", "name": "cnDialog"}, "contractFund": {"prefix": "@/components", "path": "./cm/drawup/contract-fund.vue", "name": "contract-fund"}, "contractPlan": {"prefix": "@/components", "path": "./cm/drawup/contract-plan.vue", "name": "contract-plan"}, "contractSubject": {"prefix": "@/components", "path": "./cm/drawup/contract-subject.vue", "name": "contract-subject"}, "frameAgreementDialog": {"prefix": "@/components", "path": "./cm/drawup/frame-agreement-dialog.vue", "name": "frame-agreement-dialog"}, "plandialog": {"prefix": "@/components", "path": "./cm/drawup/plandialog.vue", "name": "plandialog"}, "relevanceCmNumDialog": {"prefix": "@/components", "path": "./cm/drawup/relevance-cm-num-dialog.vue", "name": "relevance-cm-num-dialog"}, "contractExpandList": {"prefix": "@/components", "path": "./cm/expand/contract-expand-list.vue", "name": "contract-expand-list"}, "columnconfigureSd": {"prefix": "@/components", "path": "./columnconfigure/index.vue", "name": "columnconfigure-sd"}, "commonDialog": {"prefix": "@/components", "path": "./dialog/index.vue", "name": "common-dialog"}, "dragX": {"prefix": "@/components", "path": "./drag/dragX.vue", "name": "dragX"}, "dragY": {"prefix": "@/components", "path": "./drag/dragY.vue", "name": "dragY"}, "blockDynaTabs": {"prefix": "@/components", "path": "./dynamic/block-dyna-tabs.vue", "name": "block-dyna-tabs"}, "customMessageDlg": {"prefix": "@/components", "path": "./dynamic/custom-message-dlg.vue", "name": "custom-message-dlg"}, "dynamicDlgDemo": {"prefix": "@/components", "path": "./dynamic/dynamic-dlg-demo.vue", "name": "dynamic-dlg-demo"}, "dynamicDlg": {"prefix": "@/components", "path": "./dynamic/dynamic-dlg.vue", "name": "dynamicDlg"}, "dynamicTab": {"prefix": "@/components", "path": "./dynamic/dynamic-tab.vue", "name": "dynamic-tab"}, "excelImp": {"prefix": "@/components", "path": "./excel/excel-imp.vue", "name": "excel-imp"}, "fileView": {"prefix": "@/components", "path": "./fileview/file-view.vue", "name": "file-view"}, "easyTree": {"prefix": "@/components", "path": "./gianttree/easyTree.vue", "name": "easyTree"}, "giantTree": {"prefix": "@/components", "path": "./gianttree/index.vue", "name": "giantTree"}, "popoverEasyTree": {"prefix": "@/components", "path": "./gianttree/popoverEasyTree.vue", "name": "popoverEasyTree"}, "supTree": {"prefix": "@/components", "path": "./gianttree/supTree.vue", "name": "supTree"}, "treeSearch": {"prefix": "@/components", "path": "./gianttree/treeSearch.vue", "name": "treeSearch"}, "hyperlinkDialog": {"prefix": "@/components", "path": "./hyperlinkDialog/index.vue", "name": "hyperlinkDialog"}, "ImgFile": {"prefix": "@/components", "path": "./imgflie/index.vue", "name": "ImgFile"}, "事前申请单": {"prefix": "@/components", "path": "./inc/sq-assembly.vue", "name": "事前申请单"}, "inputMoney": {"prefix": "@/components", "path": "./input/inputMoney.vue", "name": "input-money"}, "InputMoney": {"prefix": "@/components", "path": "./inputmoney/index.vue", "name": "InputMoney"}, "LayoutTem": {"prefix": "@/components", "path": "./layout/index.vue", "name": "LayoutTem"}, "formEditDetail": {"prefix": "@/components", "path": "./list/form-edit-detail.vue", "name": "form-edit-detail"}, "ransferTable": {"prefix": "@/components", "path": "./list/ransfer-table.vue", "name": "ransfer-table"}, "wfCountersignUserlist": {"prefix": "@/components", "path": "./list/wf-countersign-userlist.vue", "name": "wf-countersign-userlist"}, "loading": {"prefix": "@/components", "path": "./loading/index.vue", "name": "loading"}, "ofd": {"prefix": "@/components", "path": "./ofd/index.vue", "name": "ofd"}, "auditComp": {"prefix": "@/components", "path": "./page/audit-comp.vue", "name": "audit-comp"}, "auditExtendForm": {"prefix": "@/components", "path": "./page/audit-extend-form.vue", "name": "audit-extend-form"}, "auditFileInvoiceTab": {"prefix": "@/components", "path": "./page/audit-file-invoice-tab.vue", "name": "audit-file-invoice-tab"}, "auditFileTab": {"prefix": "@/components", "path": "./page/audit-file-tab.vue", "name": "audit-file-tab"}, "auditReview": {"prefix": "@/components", "path": "./page/audit-review.vue", "name": "audit-review"}, "bButton": {"prefix": "@/components", "path": "./page/base-button.vue", "name": "b-button"}, "bCurd": {"prefix": "@/components", "path": "./page/base-curd.vue", "name": "b-curd"}, "baseEditDlg": {"prefix": "@/components", "path": "./page/base-edit-dlg.vue", "name": "base-edit-dlg"}, "baseEditRform": {"prefix": "@/components", "path": "./page/base-edit-rform.vue", "name": "base-edit-rform"}, "baseListBizflowCformSublist": {"prefix": "@/components", "path": "./page/base-list-bizflow-cform-sublist.vue", "name": "base-list-bizflow-cform-sublist"}, "baseListBizflowCform": {"prefix": "@/components", "path": "./page/base-list-bizflow-cform.vue", "name": "base-list-bizflow-cform"}, "baseListBizflowNotCform": {"prefix": "@/components", "path": "./page/base-list-bizflow-not-cform.vue", "name": "base-list-bizflow-not-cform"}, "baseListCellFile": {"prefix": "@/components", "path": "./page/base-list-cell-file.vue", "name": "base-list-cell-file"}, "baseListCform": {"prefix": "@/components", "path": "./page/base-list-cform.vue", "name": "base-list-cform"}, "baseListCommonForm": {"prefix": "@/components", "path": "./page/base-list-common-form.vue", "name": "base-list-common-form"}, "bListWfApply": {"prefix": "@/components", "path": "./page/base-list-wf-apply.vue", "name": "b-list-wf-apply"}, "bListWfAudit": {"prefix": "@/components", "path": "./page/base-list-wf-audit.vue", "name": "b-list-wf-audit"}, "baseListWfBack": {"prefix": "@/components", "path": "./page/base-list-wf-back.vue", "name": "base-list-wf-back"}, "bListWfDetail": {"prefix": "@/components", "path": "./page/base-list-wf-detail.vue", "name": "b-list-wf-detail"}, "baseListWfSubmitform": {"prefix": "@/components", "path": "./page/base-list-wf-submitform.vue", "name": "base-list-wf-submitform"}, "bListWf": {"prefix": "@/components", "path": "./page/base-list-wf.vue", "name": "b-list-wf"}, "bList": {"prefix": "@/components", "path": "./page/base-list.vue", "name": "b-list"}, "bPage": {"prefix": "@/components", "path": "./page/base-page.vue", "name": "b-page"}, "bReport": {"prefix": "@/components", "path": "./page/base-report.vue", "name": "b-report"}, "baseTableHeader": {"prefix": "@/components", "path": "./page/base-table-components/table-header.vue", "name": "base-table-header"}, "billsinputsend": {"prefix": "@/components", "path": "./page/billsinput/index.vue", "name": "billsinputsend"}, "billsinputaccept": {"prefix": "@/components", "path": "./page/billsinputOne/index.vue", "name": "billsinputaccept"}, "blistColleft": {"prefix": "@/components", "path": "./page/blist-colleft.vue", "name": "blist-colleft"}, "assistBlock": {"prefix": "@/components", "path": "./page/blockTab/assist-block.vue", "name": "assistBlock"}, "blockTab": {"prefix": "@/components", "path": "./page/blockTab/index.vue", "name": "blockTab"}, "reimburseBlock": {"prefix": "@/components", "path": "./page/blockTab/reimburse-block.vue", "name": "reimburseBlock"}, "ruleBlock": {"prefix": "@/components", "path": "./page/blockTab/rule-block.vue", "name": "ruleBlock"}, "columnConfigDialog": {"prefix": "@/components", "path": "./page/column-config-dialog.vue", "name": "column-config-dialog"}, "page": {"prefix": "@/components", "path": "./page/index.vue", "name": "page"}, "searchForm": {"prefix": "@/components", "path": "./page/searchForm/searchForm.vue", "name": "searchForm"}, "searchRadio": {"prefix": "@/components", "path": "./page/searchForm/searchRadio.vue", "name": "search-radio"}, "refData": {"prefix": "@/components", "path": "./ref/index.vue", "name": "ref-data"}, "refTablePager": {"prefix": "@/components", "path": "./ref/ref-table-pager.vue", "name": "ref-table-pager"}, "refTableUserRoleSearch": {"prefix": "@/components", "path": "./ref/ref-table-user-role-search.vue", "name": "ref-table-user-role-search"}, "refTable": {"prefix": "@/components", "path": "./ref/ref-table.vue", "name": "ref-table"}, "refTreeSelector": {"prefix": "@/components", "path": "./ref/ref-tree-selector.vue", "name": "ref-tree-selector"}, "refTree": {"prefix": "@/components", "path": "./ref/ref-tree.vue", "name": "ref-tree"}, "searchComponentTemp": {"prefix": "@/components", "path": "./ref/searchComponentTemp.vue", "name": "searchComponentTemp"}, "subRefTable": {"prefix": "@/components", "path": "./ref/sub-ref-table.vue", "name": "sub-ref-table"}, "accommodationdialogBak": {"prefix": "@/components", "path": "./reimbursement/base/accommodationdialog-bak.vue", "name": "accommodationdialog-bak"}, "accommodationdialog": {"prefix": "@/components", "path": "./reimbursement/base/accommodationdialog.vue", "name": "accommodationdialog"}, "bxBaseEditDialog": {"prefix": "@/components", "path": "./reimbursement/base/bx-base-edit-dialog.vue", "name": "bx-base-edit-dialog"}, "goabroadfeedialog": {"prefix": "@/components", "path": "./reimbursement/base/goabroadfeedialog.vue", "name": "goabroadfeedialog"}, "reibase": {"prefix": "@/components", "path": "./reimbursement/base/index.vue", "name": "reibase"}, "scientificresearchdialogBak": {"prefix": "@/components", "path": "./reimbursement/base/scientificresearchdialog-bak.vue", "name": "scientificresearchdialog-bak"}, "scientificresearchdialog": {"prefix": "@/components", "path": "./reimbursement/base/scientificresearchdialog.vue", "name": "scientificresearchdialog"}, "tableColumn": {"prefix": "@/components", "path": "./reimbursement/base/TableColumn.vue", "name": "tableColumn"}, "SelectLoadTreeFilter": {"prefix": "@/components", "path": "./selectloadtreefilter/index.vue", "name": "SelectLoadTreeFilter"}, "eTable": {"prefix": "@/components", "path": "./table/e-table.vue", "name": "e-table"}, "tablecolumnn": {"prefix": "@/components", "path": "./tablecolumn/index.vue", "name": "tablecolumnn"}, "rowFormContent": {"prefix": "@/components", "path": "./tablecolumn/row-form-content.vue", "name": "row-form-content"}, "row-form-level3-detail": {"prefix": "@/components", "path": "./tablecolumn/row-form-level3-detail.vue", "name": "row-form-level3-detail"}, "rowFormSetting": {"prefix": "@/components", "path": "./tablecolumn/row-form-setting.vue", "name": "row-form-setting"}, "rowFormTemplateDlg": {"prefix": "@/components", "path": "./tablecolumn/row-form-template-dlg.vue", "name": "row-form-template-dlg"}, "rowFormTemplatePermissions": {"prefix": "@/components", "path": "./tablecolumn/row-form-template-permissions.vue", "name": "rowFormTemplatePermissions"}, "rowFormTemplateSave": {"prefix": "@/components", "path": "./tablecolumn/row-form-template-save.vue", "name": "row-form-template-save"}, "rowFormTemplateSetting": {"prefix": "@/components", "path": "./tablecolumn/row-form-template-setting.vue", "name": "row-form-template-setting"}, "rowForm": {"prefix": "@/components", "path": "./tablecolumn/row-form.vue", "name": "row-form"}, "templateAssembleData": {"prefix": "@/components", "path": "./tablecolumn/template-assemble-data.vue", "name": "template-assemble-data"}, "versionRelate": {"prefix": "@/components", "path": "./versionrelate/index.vue", "name": "versionRelate"}, "conditionDialog": {"prefix": "@/components", "path": "./wf/condition-dialog.vue", "name": "condition-dialog"}, "wff": {"prefix": "@/components", "path": "./wf/index.vue", "name": "wff"}, "wfAuditEdit": {"prefix": "@/components", "path": "./wf/wf-audit-edit.vue", "name": "wf-audit-edit"}, "billEditTimeDialog": {"prefix": "@/views", "path": "./inc/bill-edit-time-dialog.vue", "name": "bill-edit-time-dialog"}, "wfAuditHistory": {"prefix": "@/components", "path": "./wf/wf-audit-history.vue", "name": "wf-audit-history"}, "wfDataCheck": {"prefix": "@/components", "path": "./wf/wf-data-check.vue", "name": "wf-data-check"}, "wfDataSetting": {"prefix": "@/components", "path": "./wf/wf-data-setting.vue", "name": "wf-data-setting"}, "wfEvaluateTab": {"prefix": "@/components", "path": "./wf/wf-evaluate-tab.vue", "name": "wf-evaluate-tab"}, "nodeSetting": {"prefix": "@/components", "path": "./wf/wf-more-setting/node-setting.vue", "name": "nodeSetting"}, "wfSetting": {"prefix": "@/components", "path": "./wf/wf-more-setting/wf-setting.vue", "name": "wfSetting"}, "wfMoreSetting": {"prefix": "@/components", "path": "./wf/wf-more-setting.vue", "name": "wf-more-setting"}, "wfSelectNodeDialog": {"prefix": "@/components", "path": "./wf/wf-select-node-dialog.vue", "name": "wf-select-node-dialog"}, "wfUsefulListDetail": {"prefix": "@/components", "path": "./wf/wf-useful-list-detail.vue", "name": "wf-useful-list-detail"}, "wfusefuldialog": {"prefix": "@/components", "path": "./wf/wf-useful-list-dialog.vue", "name": "wfusefuldialog"}, "wfUsefulList": {"prefix": "@/components", "path": "./wf/wf-useful-list.vue", "name": "wf-useful-list"}, "wfUserCform": {"prefix": "@/components", "path": "./wf/wf-user-cform.vue", "name": "wf-user-cform"}, "wfUserdeptList": {"prefix": "@/components", "path": "./wf/wf-userdept-list.vue", "name": "wf-userdept-list"}, "wfcanvas": {"prefix": "@/components", "path": "./wf/wfcanvas.vue", "name": "wfcanvas"}, "aifillsetApply": {"prefix": "@/views", "path": "./aifillset/aifillset-apply.vue", "name": "aifillset-apply"}, "aifillsetApplyEdit": {"prefix": "@/views", "path": "./aifillset/aifillset-edit-dialog.vue", "name": "aifillset-apply-edit"}, "chooseCform": {"prefix": "@/views", "path": "./aifillset/intelligent-reimb/choose-cform-dialog.vue", "name": "choose-cform"}, "aifillsetIntelligentReimb": {"prefix": "@/views", "path": "./aifillset/intelligent-reimb/index.vue", "name": "aifillset-intelligent-reimb"}, "intelligentFillFileUpload": {"prefix": "@/views", "path": "./aifillset/intelligent-reimb/intelligent-fill-file-upload.vue", "name": "intelligent-fill-file-upload"}, "intelligentFillOther": {"prefix": "@/views", "path": "./aifillset/intelligent-reimb/intelligent-fill-other.vue", "name": "intelligent-fill-other"}, "intelligentFillPayee": {"prefix": "@/views", "path": "./aifillset/intelligent-reimb/intelligent-fill-payee.vue", "name": "intelligent-fill-payee"}, "intelligentFillQuota": {"prefix": "@/views", "path": "./aifillset/intelligent-reimb/intelligent-fill-quota.vue", "name": "intelligent-fill-quota"}, "ashiersalarypay": {"prefix": "@/views", "path": "./ashiersalarypay/index.vue", "name": "ashiersalarypay"}, "assetAuditTab": {"prefix": "@/views", "path": "./asset/asset-audit-tab.vue", "name": "asset-audit-tab"}, "assetLedgerDialog": {"prefix": "@/views", "path": "./asset/asset-ledger-dialog.vue", "name": "asset-ledger-dialog"}, "aiAuditApply": {"prefix": "@/views", "path": "./auditruleset/aiaudit/ai-audit-apply.vue", "name": "ai-audit-apply"}, "aiAuditDetailDialog": {"prefix": "@/views", "path": "./auditruleset/aiaudit/ai-audit-detail-dialog.vue", "name": "ai-audit-detail-dialog"}, "aiAuditEdit": {"prefix": "@/views", "path": "./auditruleset/aiaudit/ai-audit-edit.vue", "name": "ai-audit-edit"}, "aiAuditParamDialog": {"prefix": "@/views", "path": "./auditruleset/aiaudit/ai-audit-param-dialog.vue", "name": "ai-audit-param-dialog"}, "commoEdit": {"prefix": "@/views", "path": "./auditruleset/aiauditcommon/commo-edit.vue", "name": "commo-edit"}, "auditRuleHistoryTab": {"prefix": "@/views", "path": "./auditruleset/audithis/audit-rule-history-tab.vue", "name": "audit-rule-history-tab"}, "auxiliaryAuditHistory": {"prefix": "@/views", "path": "./auditruleset/audithis/auxiliary-audit-history.vue", "name": "auxiliary-audit-history"}, "auditManageApply": {"prefix": "@/views", "path": "./auditruleset/auditmanager/audit-manage-apply.vue", "name": "audit-manage-apply"}, "auditManageEdit": {"prefix": "@/views", "path": "./auditruleset/auditmanager/audit-manage-edit-dialog.vue", "name": "audit-manage-edit"}, "auditRuleFilesEdit": {"prefix": "@/views", "path": "./auditruleset/auditrulefiles/audit-rule-files-edit.vue", "name": "audit-rule-files-edit"}, "auditRuleFiles": {"prefix": "@/views", "path": "./auditruleset/auditrulefiles/audit-rule-files.vue", "name": "audit-rule-files"}, "auxiliaryAuditApply": {"prefix": "@/views", "path": "./auditruleset/auxiliaryaudit/auxiliary-audit-apply.vue", "name": "auxiliary-audit-apply"}, "riskauditControlmeasureslibraryInitAdd": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-controlmeasureslibrary-init-add.vue", "name": "riskaudit-controlmeasureslibrary-init-add"}, "riskauditControlmeasureslibraryInitPolicy": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-controlmeasureslibrary-init-policy.vue", "name": "riskaudit-controlmeasureslibrary-init-policy"}, "riskauditControlmeasureslibraryInitRisk": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-controlmeasureslibrary-init-risk.vue", "name": "riskaudit-controlmeasureslibrary-init-risk"}, "riskauditControlmeasureslibraryInit": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-controlmeasureslibrary-init.vue", "name": "riskaudit-controlmeasureslibrary-init"}, "riskauditControlmeasureslibrary": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-controlmeasureslibrary.vue", "name": "riskaudit-controlmeasureslibrary"}, "riskauditImport": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-import.vue", "name": "riskaudit-import"}, "riskauditIndicatorLibraryInitAdd": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-IndicatorLibrary-init-add.vue", "name": "riskaudit-IndicatorLibrary-init-add"}, "riskauditIndicatorLibraryInitDistribute": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-IndicatorLibrary-init-distribute.vue", "name": "riskaudit-IndicatorLibrary-init-distribute"}, "riskauditIndicatorLibraryInitHistory": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-IndicatorLibrary-init-history.vue", "name": "riskaudit-IndicatorLibrary-init-history"}, "riskauditIndicatorLibraryInit": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-IndicatorLibrary-init.vue", "name": "riskaudit-IndicatorLibrary-init"}, "riskauditIndicatorLibrary": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-IndicatorLibrary.vue", "name": "riskaudit-IndicatorLibrary"}, "riskauditRiskMatrix": {"prefix": "@/views", "path": "./auditruleset/riskaudit/riskaudit-riskMatrix.vue", "name": "riskaudit-riskMatrix"}, "baAccountEdit": {"prefix": "@/views", "path": "./ba/account/ba-account-edit.vue", "name": "ba-account-edit"}, "baAccount": {"prefix": "@/views", "path": "./ba/account/ba-account.vue", "name": "ba-account"}, "baDocumentList": {"prefix": "@/views", "path": "./ba/adjustment/ba-document-list.vue", "name": "ba-document-list"}, "baDocumentMini": {"prefix": "@/views", "path": "./ba/adjustment/ba-document-mini.vue", "name": "ba-document-mini"}, "descriptionsTooltip": {"prefix": "@/views", "path": "./ba/adjustment/descriptions-tooltip.vue", "name": "descriptions-tooltip"}, "faAdjustAdd": {"prefix": "@/views", "path": "./ba/adjustment/fa-adjust-add.vue", "name": "fa-adjust-add"}, "faAdjustMini": {"prefix": "@/views", "path": "./ba/adjustment/fa-adjust-mini.vue", "name": "fa-adjust-mini"}, "faConnectionAdd": {"prefix": "@/views", "path": "./ba/adjustment/fa-connection-add.vue", "name": "fa-connection-add"}, "faDocDetail": {"prefix": "@/views", "path": "./ba/adjustment/fa-docDetail.vue", "name": "fa-docDetail"}, "faDocumentList": {"prefix": "@/views", "path": "./ba/adjustment/fa-document-list.vue", "name": "fa-document-list"}, "faShiftAdd": {"prefix": "@/views", "path": "./ba/adjustment/fa-shift-add.vue", "name": "fa-shift-add"}, "faShiftMini": {"prefix": "@/views", "path": "./ba/adjustment/fa-shift-mini.vue", "name": "fa-shift-mini"}, "baAuthorizeBaAuthorizeList": {"prefix": "@/views", "path": "./ba/authorize/ba-authorize-list.vue", "name": "ba-authorize-ba-authorize-list"}, "baAuthorizeBaQueryList": {"prefix": "@/views", "path": "./ba/authorize/ba-query-list.vue", "name": "ba-authorize-ba-query-list"}, "baAuthSetDialog": {"prefix": "@/views", "path": "./ba/ba-auth-set-dialog.vue", "name": "ba-auth-set-dialog"}, "baAuthUserDialog": {"prefix": "@/views", "path": "./ba/ba-auth-user-dialog.vue", "name": "ba-auth-user-dialog"}, "baBizAdjust": {"prefix": "@/views", "path": "./ba/ba-biz-adjust.vue", "name": "ba-biz-adjust"}, "baBizAuditExtend": {"prefix": "@/views", "path": "./ba/ba-biz-audit-extend.vue", "name": "ba-biz-audit-extend"}, "baBizBase": {"prefix": "@/views", "path": "./ba/ba-biz-base.vue", "name": "ba-biz-base"}, "baBizDetail": {"prefix": "@/views", "path": "./ba/ba-biz-detail.vue", "name": "ba-biz-detail"}, "baBaBizListApplyAdjust": {"prefix": "@/views", "path": "./ba/ba-biz-list-apply-adjust.vue", "name": "ba-ba-biz-list-apply-adjust"}, "baBaBizListApplyShift": {"prefix": "@/views", "path": "./ba/ba-biz-list-apply-shift.vue", "name": "ba-ba-biz-list-apply-shift"}, "baBaBizListApply": {"prefix": "@/views", "path": "./ba/ba-biz-list-apply.vue", "name": "ba-ba-biz-list-apply"}, "baBizListAuditAdjust": {"prefix": "@/views", "path": "./ba/ba-biz-list-audit-adjust.vue", "name": "ba-biz-list-audit-adjust"}, "baBizListAuditShift": {"prefix": "@/views", "path": "./ba/ba-biz-list-audit-shift.vue", "name": "ba-biz-list-audit-shift"}, "baBizListAudit": {"prefix": "@/views", "path": "./ba/ba-biz-list-audit.vue", "name": "ba-biz-list-audit"}, "baBizShift": {"prefix": "@/views", "path": "./ba/ba-biz-shift.vue", "name": "ba-biz-shift"}, "baCarryList": {"prefix": "@/views", "path": "./ba/ba-carry-list.vue", "name": "ba-carry-list"}, "baDetailPane": {"prefix": "@/views", "path": "./ba/ba-detail-pane.vue", "name": "ba-detail-pane"}, "baDispenseDetail": {"prefix": "@/views", "path": "./ba/ba-dispense-detail.vue", "name": "ba-dispense-detail"}, "baDispense": {"prefix": "@/views", "path": "./ba/ba-dispense.vue", "name": "ba-dispense"}, "baDispensingExport": {"prefix": "@/views", "path": "./ba/ba-dispensing-export.vue", "name": "ba-dispensing-export"}, "baFaAmountDialog": {"prefix": "@/views", "path": "./ba/ba-fa-amount-dialog.vue", "name": "ba-fa-amount-dialog"}, "baFlow": {"prefix": "@/views", "path": "./ba/ba-flow.vue", "name": "ba-flow"}, "baFreeze": {"prefix": "@/views", "path": "./ba/ba-freeze.vue", "name": "ba-freeze"}, "baIssueEdit": {"prefix": "@/views", "path": "./ba/ba-issue-edit.vue", "name": "ba-issue-edit"}, "baIssue": {"prefix": "@/views", "path": "./ba/ba-issue.vue", "name": "ba-issue"}, "baLink": {"prefix": "@/views", "path": "./ba/ba-link.vue", "name": "ba-link"}, "baBaListType": {"prefix": "@/views", "path": "./ba/ba-list-type.vue", "name": "ba-ba-list-type"}, "baBaList": {"prefix": "@/views", "path": "./ba/ba-list.vue", "name": "ba-ba-list"}, "baMini": {"prefix": "@/views", "path": "./ba/ba-mini.vue", "name": "ba-mini"}, "baPlan": {"prefix": "@/views", "path": "./ba/ba-plan.vue", "name": "ba-plan"}, "formDetail部门指标-附件": {"prefix": "@/views", "path": "./ba/badetail/form-datail-att.vue", "name": "formDetail部门指标-附件"}, "formDetail部门指标-合同信息": {"prefix": "@/views", "path": "./ba/badetail/form-detail-tab-cm.vue", "name": "formDetail部门指标-合同信息"}, "formDetail部门指标-下达明细": {"prefix": "@/views", "path": "./ba/badetail/form-datail-issue.vue", "name": "formDetail部门指标-下达明细"}, "formDetail部门指标-执行计划": {"prefix": "@/views", "path": "./ba/badetail/form-datail-plan.vue", "name": "formDetail部门指标-执行计划"}, "formDetail-bak-部门指标-调整信息": {"prefix": "@/views", "path": "./ba/badetail/form-datail-shift.vue", "name": "formDetail-bak-部门指标-调整信息"}, "formDetail部门指标-报销申请": {"prefix": "@/views", "path": "./ba/badetail/form-detail-bx.vue", "name": "formDetail部门指标-报销申请"}, "formDetail部门指标-财政指标": {"prefix": "@/views", "path": "./ba/badetail/form-detail-fa-amount.vue", "name": "formDetail部门指标-财政指标"}, "formDetail部门指标-还款申请": {"prefix": "@/views", "path": "./ba/badetail/form-detail-hk.vue", "name": "formDetail部门指标-还款申请"}, "formDetail部门指标-支付凭证": {"prefix": "@/views", "path": "./ba/badetail/form-detail-inc-finance.vue", "name": "formDetail部门指标-支付凭证"}, "formDetail部门指标-借款申请": {"prefix": "@/views", "path": "./ba/badetail/form-detail-jk.vue", "name": "formDetail部门指标-借款申请"}, "formDetail部门指标-采购信息": {"prefix": "@/views", "path": "./ba/badetail/form-detail-pur.vue", "name": "formDetail部门指标-采购信息"}, "formDetail部门指标-调整调剂": {"prefix": "@/views", "path": "./ba/badetail/form-detail-shiftadjust.vue", "name": "formDetail部门指标-调整调剂"}, "formDetail部门指标-事前申请": {"prefix": "@/views", "path": "./ba/badetail/form-detail-sq.vue", "name": "formDetail部门指标-事前申请"}, "formDetail部门指标-支付单": {"prefix": "@/views", "path": "./ba/badetail/form-detail-zf.vue", "name": "formDetail部门指标-支付单"}, "baScopeDialog": {"prefix": "@/views", "path": "./ba/baScopeDialog.vue", "name": "baScopeDialog"}, "baSetUp": {"prefix": "@/views", "path": "./ba/basetup/ba-set-up.vue", "name": "ba-set-up"}, "blockRformExtendRelevantBaEntity": {"prefix": "@/views", "path": "./ba/block-rform-extend-ba.vue", "name": "blockRformExtend-RelevantBaEntity"}, "adjustAddDetail": {"prefix": "@/views", "path": "./ba/customdetail/adjust-add-apply.vue", "name": "adjustAddDetail"}, "adjustReduceDetail": {"prefix": "@/views", "path": "./ba/customdetail/adjust-reduce-apply.vue", "name": "adjustReduceDetail"}, "baFreezeDetail": {"prefix": "@/views", "path": "./ba/customdetail/ba-freeze-detail.vue", "name": "baFreezeDetail"}, "quotaFrozenDetail": {"prefix": "@/views", "path": "./ba/customdetail/ba-frozen-custom_detail.vue", "name": "quotaFrozenDetail"}, "quotaMonthPayDetail": {"prefix": "@/views", "path": "./ba/customdetail/ba-month-pay-custom-detail.vue", "name": "quotaMonthPayDetail"}, "baPayCmDetail": {"prefix": "@/views", "path": "./ba/customdetail/ba-pay-cm-detail.vue", "name": "ba-pay-cm-detail"}, "quotaPayDetail": {"prefix": "@/views", "path": "./ba/customdetail/ba-pay-custom-detail.vue", "name": "quotaPayDetail"}, "baPayDocument": {"prefix": "@/views", "path": "./ba/customdetail/ba-pay-document.vue", "name": "ba-pay-document"}, "baPayIncDetail": {"prefix": "@/views", "path": "./ba/customdetail/ba-pay-inc-detail.vue", "name": "ba-pay-inc-detail"}, "baPayLoanDetail": {"prefix": "@/views", "path": "./ba/customdetail/ba-pay-loan-detail.vue", "name": "ba-pay-loan-detail"}, "quotaYearPayDetail": {"prefix": "@/views", "path": "./ba/customdetail/ba-year-pay-custom-detail.vue", "name": "quotaYearPayDetail"}, "cmApply": {"prefix": "@/views", "path": "./ba/customdetail/cm-apply.vue", "name": "cm-apply"}, "showDetail": {"prefix": "@/views", "path": "./ba/customdetail/custom-show-detail.vue", "name": "showDetail"}, "fundApply": {"prefix": "@/views", "path": "./ba/customdetail/fund-apply.vue", "name": "fund-apply"}, "loanApply": {"prefix": "@/views", "path": "./ba/customdetail/loan-apply.vue", "name": "loan-apply"}, "perAuth": {"prefix": "@/views", "path": "./ba/customdetail/per-auth.vue", "name": "per-auth"}, "purApply": {"prefix": "@/views", "path": "./ba/customdetail/pur-apply.vue", "name": "pur-apply"}, "purdemApply": {"prefix": "@/views", "path": "./ba/customdetail/purdem-apply.vue", "name": "purdem-apply"}, "qbFreezeInfoDetail": {"prefix": "@/views", "path": "./ba/customdetail/qb-freeze-info-detail.vue", "name": "qb-freeze-info-detail"}, "qbPayIncDetail": {"prefix": "@/views", "path": "./ba/customdetail/qb-pay-inc-detail.vue", "name": "qb-pay-inc-detail"}, "reimburseApply": {"prefix": "@/views", "path": "./ba/customdetail/reimburse-apply.vue", "name": "reimburse-apply"}, "baDispenseDialog": {"prefix": "@/views", "path": "./ba/dispenseLog/ba-dispense-dialog.vue", "name": "ba-dispense-dialog"}, "formDetail财政指标1-关联部门指标": {"prefix": "@/views", "path": "./ba/finance/ba-details.vue", "name": "formDetail财政指标1-关联部门指标"}, "baListTwofp": {"prefix": "@/views", "path": "./ba/finance/ba-list-twofp.vue", "name": "ba-list-twofp"}, "financeBaListPh": {"prefix": "@/views", "path": "./ba/finance/finance-ba-list-ph.vue", "name": "finance-ba-list-ph"}, "financeBaList": {"prefix": "@/views", "path": "./ba/finance/finance-ba-list.vue", "name": "finance-ba-list"}, "fnBaItemAdd": {"prefix": "@/views", "path": "./ba/finance/fn-ba-item-add.vue", "name": "fn-ba-item-add"}, "fnCreateMetricsDialog": {"prefix": "@/views", "path": "./ba/finance/fn-createMetrics-dialog.vue", "name": "fn-createMetrics-dialog"}, "fnLink": {"prefix": "@/views", "path": "./ba/finance/fn-link.vue", "name": "fn-link"}, "fnMini": {"prefix": "@/views", "path": "./ba/finance/fn-mini.vue", "name": "fn-mini"}, "formDetail财政指标-调整调剂": {"prefix": "@/views", "path": "./ba/finance/form-detail-adjust.vue", "name": "formDetail财政指标-调整调剂"}, "formDetail财政指标1-下达明细": {"prefix": "@/views", "path": "./ba/finance/form-detail-fnIssue.vue", "name": "formDetail财政指标1-下达明细"}, "formDetail财政指标-支付记录": {"prefix": "@/views", "path": "./ba/finance/form-detail-pay.vue", "name": "formDetail财政指标-支付记录"}, "formDetail财政指标-采购信息": {"prefix": "@/views", "path": "./ba/finance/form-detail-purInfo.vue", "name": "formDetail财政指标-采购信息"}, "formDetail财政指标1-附件": {"prefix": "@/views", "path": "./ba/finance/form-detail-tab-attach-fa.vue", "name": "formDetail财政指标1-附件"}, "formExtend-财政指标": {"prefix": "@/views", "path": "./ba/finance/form-extend-regular-finanace-fa.vue", "name": "formExtend-财政指标"}, "purchaseDetailsList": {"prefix": "@/views", "path": "./ba/finance/purchase-details-list.vue", "name": "purchase-details-list"}, "formEditTabDemo": {"prefix": "@/views", "path": "./ba/form-edit-tab-demo.vue", "name": "form-edit-tab-demo"}, "form-edit-tab-demo2": {"prefix": "@/views", "path": "./ba/form-edit-tab-demo2.vue", "name": "form-edit-tab-demo2"}, "form-edit-tab-demo3": {"prefix": "@/views", "path": "./ba/form-edit-tab-demo3.vue", "name": "form-edit-tab-demo3"}, "formExtend-部门指标": {"prefix": "@/views", "path": "./ba/form-extend-regular-ba.vue", "name": "formExtend-部门指标"}, "quotaPoolList": {"prefix": "@/views", "path": "./ba/fundspool/funds-pool-add-dialog.vue", "name": "quota-pool-list"}, "dataViewPoolDialog": {"prefix": "@/views", "path": "./ba/fundspool/funds-pool-data-dialog.vue", "name": "data-view-pool-dialog"}, "fundsPoolList": {"prefix": "@/views", "path": "./ba/fundspool/funds-pool-list.vue", "name": "funds-pool-list"}, "quotaViewDialog": {"prefix": "@/views", "path": "./ba/fundspool/funds-pool-quota-dialog.vue", "name": "quota-view-dialog"}, "baLoanBaViewLoanBaViewList": {"prefix": "@/views", "path": "./ba/loanBaView/loan-ba-view-list.vue", "name": "ba-loanBaView-loan-ba-view-list"}, "otherList": {"prefix": "@/views", "path": "./ba/other/other-list.vue", "name": "other-list"}, "baResearchBaResearchList": {"prefix": "@/views", "path": "./ba/research/ba-research-list.vue", "name": "ba-research-ba-research-list"}, "formExtend-科研指标": {"prefix": "@/views", "path": "./ba/research/form-extend-regular-barh.vue", "name": "formExtend-科研指标"}, "baResearchFlow": {"prefix": "@/views", "path": "./ba/research/research-flow.vue", "name": "ba-research-flow"}, "baRevenueBaReFlow": {"prefix": "@/views", "path": "./ba/revenue/ba-re-flow.vue", "name": "ba-revenue-ba-re-flow"}, "formExtend-收入指标": {"prefix": "@/views", "path": "./ba/revenue/form-extend-regular-ba-re.vue", "name": "formExtend-收入指标"}, "baRevenueReList": {"prefix": "@/views", "path": "./ba/revenue/re-list.vue", "name": "ba-revenue-re-list"}, "baScopeCtrl": {"prefix": "@/views", "path": "./ba/scope/ba-scope-ctrl-dialog.vue", "name": "ba-scope-ctrl"}, "baScopeCtrlRecord": {"prefix": "@/views", "path": "./ba/scope/ba-scope-ctrl-record.vue", "name": "ba-scope-ctrl-record"}, "baScopeMini": {"prefix": "@/views", "path": "./ba/scope/ba-scope-mini.vue", "name": "ba-scope-mini"}, "baScopeSelect": {"prefix": "@/views", "path": "./ba/scope/ba-scope-select.vue", "name": "ba-scope-select"}, "scopeTemplateDialog": {"prefix": "@/views", "path": "./ba/scope/scope-template-dialog.vue", "name": "scope-template-dialog"}, "baScopeScopeTemplate": {"prefix": "@/views", "path": "./ba/scope/scope-template.vue", "name": "ba-scope-scope-template"}, "templateAddrowDialog": {"prefix": "@/views", "path": "./ba/scope/template-addrow-dialog.vue", "name": "template-addrow-dialog"}, "statisticalAnalysis": {"prefix": "@/views", "path": "./ba/statistical-analysis.vue", "name": "statistical-analysis"}, "baVirtualList": {"prefix": "@/views", "path": "./ba/virtual/ba-virtual-list.vue", "name": "ba-virtual-list"}, "formExtend-虚拟指标": {"prefix": "@/views", "path": "./ba/virtual/form-extend-regular-vrba.vue", "name": "formExtend-虚拟指标"}, "bankRefundListDetail": {"prefix": "@/views", "path": "./bankrefund/bank-refund-list-detail.vue", "name": "bank-refund-list-detail"}, "bankrefund": {"prefix": "@/views", "path": "./bankrefund/index.vue", "name": "bankrefund"}, "dataSharing": {"prefix": "@/views", "path": "./base/datasharing/data-sharing.vue", "name": "data-sharing"}, "basepayeeviews": {"prefix": "@/views", "path": "./bizz/payee/index.vue", "name": "basepayeeviews"}, "applyHandleBillMissing": {"prefix": "@/views", "path": "./billMissing/apply-handle-billMissing.vue", "name": "apply-handle-billMissing"}, "auditBillMissingRecord": {"prefix": "@/views", "path": "./billMissing/audit-billMissing-record.vue", "name": "audit-bill-missing-record"}, "auditMakeBillMissing": {"prefix": "@/views", "path": "./billMissing/audit-make-billMissing.vue", "name": "audit-make-bill-missing"}, "officialCardListApply": {"prefix": "@/views", "path": "./bizz/card/official-card-list-apply.vue", "name": "official-card-list-apply"}, "officialCardListAudit": {"prefix": "@/views", "path": "./bizz/card/official-card-list-audit.vue", "name": "official-card-list-audit"}, "cformmoduleviews": {"prefix": "@/views", "path": "./bizz/cformmodule/index.vue", "name": "cformmoduleviews"}, "contracpartyviews": {"prefix": "@/views", "path": "./bizz/contracparty/index.vue", "name": "contracpartyviews"}, "bizzEinvoiceBillSettings": {"prefix": "@/views", "path": "./bizz/einvoice/bill-settings.vue", "name": "bizz-einvoice-bill-settings"}, "einvoiceAddDialog": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-add-dialog.vue", "name": "einvoice-add-dialog"}, "einvoiceAuthDialog": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-auth-dialog.vue", "name": "einvoice-auth-dialog"}, "einvoiceEditDialog": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-edit-dialog.vue", "name": "einvoice-edit-dialog"}, "einvoiceElementAddDialog": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-element-add-dialog.vue", "name": "einvoice-element-add-dialog"}, "einvoiceElement": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-element.vue", "name": "einvoice-element"}, "einvoiceFolder": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-folder.vue", "name": "einvoice-folder"}, "einvoiceOwnerFolder": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-owner-folder.vue", "name": "einvoice-owner-folder"}, "einvoiceTypeAddDialog": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-type-add-dialog.vue", "name": "einvoice-type-add-dialog"}, "einvoiceTypeAddssDialog": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-type-addss-dialog.vue", "name": "einvoice-type-addss-dialog"}, "einvoiceType": {"prefix": "@/views", "path": "./bizz/einvoice/einvoice-type.vue", "name": "einvoice-type"}, "basefileviews": {"prefix": "@/views", "path": "./bizz/file/index.vue", "name": "basefileviews"}, "nvgs": {"prefix": "@/views", "path": "./bizz/nvg/index.vue", "name": "nvgs"}, "payerDialog": {"prefix": "@/views", "path": "./bizz/payer/payer-dialog.vue", "name": "payer-dialog"}, "payer": {"prefix": "@/views", "path": "./bizz/payer/payer.vue", "name": "payer"}, "cformUpgrade": {"prefix": "@/views", "path": "./cform/cform-upgrade.vue", "name": "cform-upgrade"}, "formExtendRegularBase": {"prefix": "@/views", "path": "./cform/form-extend-regular-base.vue", "name": "form-extend-regular-base"}, "cform": {"prefix": "@/views", "path": "./cform/index.vue", "name": "cform"}, "previewPrintf": {"prefix": "@/views", "path": "./cform/preview-printf.vue", "name": "preview-printf"}, "blockRformExtendConCreaContracpartyEntity": {"prefix": "@/views", "path": "./cm/block/block-rform-extend-cm-contracparty.vue", "name": "blockRformExtend-ConCreaContracpartyEntity"}, "blockEmptyExtend-合同": {"prefix": "@/views", "path": "./cm/block/block-rform-extend-cm-extend.vue", "name": "blockEmptyExtend-合同"}, "blockRformExtendConCreaPerformPlan": {"prefix": "@/views", "path": "./cm/block/block-rform-extend-cm-plan.vue", "name": "blockRformExtend-ConCreaPerformPlan"}, "closingItemList": {"prefix": "@/views", "path": "./cm/closing-item-list.vue", "name": "closing-item-list"}, "cmBaseFunc": {"prefix": "@/views", "path": "./cm/cm-base-func.vue", "name": "cm-base-func"}, "cmLedger": {"prefix": "@/views", "path": "./cm/cm-ledger.vue", "name": "cm-ledger"}, "cmLink": {"prefix": "@/views", "path": "./cm/cm-link.vue", "name": "cm-link"}, "cmAccreditDialog": {"prefix": "@/views", "path": "./cm/cmaccredit/cm-accredit-dialog.vue", "name": "cm-accredit-dialog"}, "cmAccreditList": {"prefix": "@/views", "path": "./cm/cmaccredit/cm-accredit-list.vue", "name": "cm-accredit-list"}, "cmChangeReason": {"prefix": "@/views", "path": "./cm/cmchange/cm-change-reason.vue", "name": "cm-change-reason"}, "cmChangeRecoed": {"prefix": "@/views", "path": "./cm/cmchange/cm-change-recoed.vue", "name": "cm-change-recoed"}, "cmChange": {"prefix": "@/views", "path": "./cm/cmchange/cm-change.vue", "name": "cm-change"}, "purCmDataswapSyncLedger": {"prefix": "@/views", "path": "./cm/cmchange/pur-cm-dataswap-sync-ledger.vue", "name": "pur-cm-dataswap-sync-ledger"}, "cmTerminalAudit": {"prefix": "@/views", "path": "./cm/cmterminal/cm-terminal-audit.vue", "name": "cm-terminal-audit"}, "cmCustomizeList": {"prefix": "@/views", "path": "./cm/cmcustomize/cm-customize-list.vue", "name": "cm-customize-list"}, "cmDisputeEditDialog": {"prefix": "@/views", "path": "./cm/cmdispute/cm-dispute-edit-dialog.vue", "name": "cm-dispute-edit-dialog"}, "cmDisputeList": {"prefix": "@/views", "path": "./cm/cmdispute/cm-dispute-list.vue", "name": "cm-dispute-list"}, "blockEmptyExtend-履约保证金收款": {"prefix": "@/views", "path": "./cm/cmgetcash/block-cform-extend-getcash.vue", "name": "blockEmptyExtend-履约保证金收款"}, "getcashList": {"prefix": "@/views", "path": "./cm/cmgetcash/getcash-list.vue", "name": "getcash-list"}, "modelCmList": {"prefix": "@/views", "path": "./cm/cmmodel/model-cm-list.vue", "name": "model-cm-list"}, "modelCmInfoDialog": {"prefix": "@/views", "path": "./cm/cmmodel/model-cmInfo-dialog.vue", "name": "modelCmInfoDialog"}, "blockEmptyExtend-履约保证金付款": {"prefix": "@/views", "path": "./cm/cmpaycash/block-cform-extend-paycash.vue", "name": "blockEmptyExtend-履约保证金付款"}, "cmPaycashListAudit": {"prefix": "@/views", "path": "./cm/cmpaycash/paycash-list-audit.vue", "name": "cm-paycash-list-audit"}, "paycashList": {"prefix": "@/views", "path": "./cm/cmpaycash/paycash-list.vue", "name": "paycash-list"}, "cmRepairTimeList": {"prefix": "@/views", "path": "./cm/cmrepairtime/cm-repair-time-list.vue", "name": "cm-repair-time-list"}, "cmtimedialog": {"prefix": "@/views", "path": "./cm/cmrepairtime/cm-time-dialog.vue", "name": "cmtimedialog"}, "signRecordAudit": {"prefix": "@/views", "path": "./cm/cmsignrecord/sign-record-audit.vue", "name": "sign-record-audit"}, "signRecordList": {"prefix": "@/views", "path": "./cm/cmsignrecord/sign-record-list.vue", "name": "sign-record-list"}, "signRecordWitndraw": {"prefix": "@/views", "path": "./cm/cmsignrecord/sign-record-witndraw.vue", "name": "sign-record-wit<PERSON><PERSON>"}, "cmImplementList": {"prefix": "@/views", "path": "./cm/cmsummary/cm-implement-list.vue", "name": "cm-implement-list"}, "cmAccountList": {"prefix": "@/views", "path": "./cm/cmsummary/cm-summary-list.vue", "name": "cm-account-list"}, "blockRformExtendCmTerminalEntity": {"prefix": "@/views", "path": "./cm/cmterminal/block-rform-extend-cm-terminal.vue", "name": "blockRformExtend-CmTerminalEntity"}, "blockEmptyExtend-合同终止表单": {"prefix": "@/views", "path": "./cm/cmterminal/block-rform-extend-cmterminal-extend.vue", "name": "blockEmptyExtend-合同终止表单"}, "cmterminalList": {"prefix": "@/views", "path": "./cm/cmterminal/cm-terminal-list.vue", "name": "cmterminal-list"}, "cmContrastSave": {"prefix": "@/views", "path": "./cm/contrast/cm-contrast-save.vue", "name": "cm-contrast-save"}, "cmContrast": {"prefix": "@/views", "path": "./cm/contrast/cm-contrast.vue", "name": "cm-contrast"}, "cmEvaluateApply": {"prefix": "@/views", "path": "./cm/drawup/cm-evaluate-apply.vue", "name": "cm-evaluate-apply"}, "cmEvaluateAudit": {"prefix": "@/views", "path": "./cm/drawup/cm-evaluate-audit.vue", "name": "cm-evaluate-audit"}, "cmEvaluate": {"prefix": "@/views", "path": "./cm/drawup/cm-evaluate.vue", "name": "cm-evaluate"}, "audit-extend-合同": {"prefix": "@/views", "path": "./cm/drawup/du-audit-extend.vue", "name": "audit-extend-合同"}, "cmDrawupDuDraftApply": {"prefix": "@/views", "path": "./cm/drawup/du-draft-apply.vue", "name": "cm-drawup-du-draft-apply"}, "cmDrawupDuDraftAudit": {"prefix": "@/views", "path": "./cm/drawup/du-draft-audit.vue", "name": "cm-drawup-du-draft-audit"}, "formAuditTabDemo": {"prefix": "@/views", "path": "./cm/drawup/form-audit-tab-demo.vue", "name": "form-audit-tab-demo"}, "form-audit-tab-demo2": {"prefix": "@/views", "path": "./cm/drawup/form-audit-tab-demo2.vue", "name": "form-audit-tab-demo2"}, "blockCmEngcmsettlementBas": {"prefix": "@/views", "path": "./cm/engcmsettlement/block-cm-engcmsettlement-bas.vue", "name": "block-cm-engcmsettlement-bas"}, "blockRformExtendEngCmSettlement": {"prefix": "@/views", "path": "./cm/engcmsettlement/block-cm-engcmsettlement.vue", "name": "blockRformExtend-EngCmSettlement"}, "engcmsettlementAudit": {"prefix": "@/views", "path": "./cm/engcmsettlement/engcmsettlement-audit.vue", "name": "engcmsettlement-audit"}, "engcmsettlementList": {"prefix": "@/views", "path": "./cm/engcmsettlement/engcmsettlement-list.vue", "name": "engcmsettlement-list"}, "formDetail合同-附件-9": {"prefix": "@/views", "path": "./cm/form-detail-tab-attach-cm.vue", "name": "formDetail合同-附件-9"}, "formDetail合同验收履约评价表-附件-3": {"prefix": "@/views", "path": "./cm/form-detail-tab-attach-cmeva.vue", "name": "formDetail合同验收履约评价表-附件-3"}, "formDetail合同验收履约评价表-合同信息-2": {"prefix": "@/views", "path": "./cm/form-detail-tab-cm-evaluate.vue", "name": "formDetail合同验收履约评价表-合同信息-2"}, "formDetail合同-关联指标-2": {"prefix": "@/views", "path": "./cm/form-detail-tab-cm-relevant-ba.vue", "name": "formDetail合同-关联指标-2"}, "formDetail合同-合约方和履行计划-3": {"prefix": "@/views", "path": "./cm/form-detail-tab-contractparty-cm.vue", "name": "formDetail合同-合约方和履行计划-3"}, "formExtend-合同验收履约评价表": {"prefix": "@/views", "path": "./cm/form-extend-cm-lypj.vue", "name": "formExtend-合同验收履约评价表"}, "formExtend-合同": {"prefix": "@/views", "path": "./cm/form-extend-regular-cm.vue", "name": "formExtend-合同"}, "lawfileSaveDlgExtend": {"prefix": "@/views", "path": "./cm/lawfile-save-dlg-extend.vue", "name": "lawfile-save-dlg-extend"}, "perfoRevalutionDetail": {"prefix": "@/views", "path": "./cm/perforevaluation/perfo-revalution-detail.vue", "name": "perfo-revalution-detail"}, "perfoRevalutionList": {"prefix": "@/views", "path": "./cm/perforevaluation/perfo-revalution-list.vue", "name": "perfo-revalution-list"}, "payRatioManage": {"prefix": "@/views", "path": "./cm/ratiomanage/pay-ratio-manage.vue", "name": "pay-ratio-manage"}, "recordList": {"prefix": "@/views", "path": "./cm/record-list.vue", "name": "record-list"}, "refExtendContract": {"prefix": "@/views", "path": "./cm/ref-extend-contract.vue", "name": "ref-extend-contract"}, "cmRenew": {"prefix": "@/views", "path": "./cm/renew/cm-renew.vue", "name": "cm-renew"}, "blockEmptyExtend-用印管理": {"prefix": "@/views", "path": "./cm/sealmanage/block-cform-extend-sealmanage.vue", "name": "blockEmptyExtend-用印管理"}, "formExtend-用印管理": {"prefix": "@/views", "path": "./cm/sealmanage/form-extend-regular-sealmanage.vue", "name": "formExtend-用印管理"}, "sealBackDialog": {"prefix": "@/views", "path": "./cm/sealmanage/sealBack-dialog.vue", "name": "sealBack-dialog"}, "sealmanageListApply": {"prefix": "@/views", "path": "./cm/sealmanage/sealmanage-list-apply.vue", "name": "sealmanage-list-apply"}, "sealmanageListAudit": {"prefix": "@/views", "path": "./cm/sealmanage/sealmanage-list-audit.vue", "name": "sealmanage-list-audit"}, "settlementListDetail": {"prefix": "@/views", "path": "./cm/settlement/settlement-list-detail.vue", "name": "settlement-list-detail"}, "settlementList": {"prefix": "@/views", "path": "./cm/settlement/settlement-list.vue", "name": "settlement-list"}, "signLogin": {"prefix": "@/views", "path": "./cm/sign-login.vue", "name": "sign-login"}, "cmmoneydialog": {"prefix": "@/views", "path": "./cm/supplementary/cm-money-dialog.vue", "name": "cmmoneydialog"}, "supplementaryList": {"prefix": "@/views", "path": "./cm/supplementary/supplementary-list.vue", "name": "supplementary-list"}, "barEchart": {"prefix": "@/views", "path": "./cm/visual/components/barEchart.vue", "name": "barEchart"}, "flatEchart": {"prefix": "@/views", "path": "./cm/visual/components/flatEchart.vue", "name": "flatEchart"}, "headerItem": {"prefix": "@/views", "path": "./cm/visual/components/headerItem.vue", "name": "headerItem"}, "pieEchart": {"prefix": "@/views", "path": "./cm/visual/components/pieEchart.vue", "name": "pieEchart"}, "verticalBarEchart": {"prefix": "@/views", "path": "./cm/visual/components/verticalBarEchart.vue", "name": "verticalBarEchart"}, "cmVisual": {"prefix": "@/views", "path": "./cm/visual/index.vue", "name": "cmVisual"}, "CodeEditor": {"prefix": "@/views", "path": "./code-editor/index.vue", "name": "CodeEditor"}, "configure": {"prefix": "@/views", "path": "./columnconfigure/index.vue", "name": "configure"}, "commonAbcformUpdate": {"prefix": "@/views", "path": "./common/abcform-update.vue", "name": "common-abcform-update"}, "allMissingRecord": {"prefix": "@/views", "path": "./common/all-missing-record.vue", "name": "all-missing-record"}, "currentUserMissingRecord": {"prefix": "@/views", "path": "./common/current-user-missing-record.vue", "name": "current-user-missing-record"}, "dataMigration": {"prefix": "@/views", "path": "./common/data-migration.vue", "name": "data-migration"}, "fileBlManage": {"prefix": "@/views", "path": "./common/file-bl-manage.vue", "name": "file-bl-manage"}, "fileBlApply": {"prefix": "@/views", "path": "./common/fileBl-apply.vue", "name": "fileBl-apply"}, "fileBlAudit": {"prefix": "@/views", "path": "./common/fileBl-audit.vue", "name": "fileBl-audit"}, "fileBlView": {"prefix": "@/views", "path": "./common/fileBl-view.vue", "name": "file-bl-view"}, "fileBl": {"prefix": "@/views", "path": "./common/fileBl.vue", "name": "fileBl"}, "missingRecordBl": {"prefix": "@/views", "path": "./common/missing-record-bl.vue", "name": "missing-record-bl"}, "sysConfig": {"prefix": "@/views", "path": "./common/sys-config.vue", "name": "sys-config"}, "customConfigContent": {"prefix": "@/views", "path": "./custom-config/custom-config-content.vue", "name": "custom-config-content"}, "customConfig": {"prefix": "@/views", "path": "./custom-config/index.vue", "name": "custom-config"}, "searchContent": {"prefix": "@/views", "path": "./custom-config/search-content-comp.vue", "name": "search-content"}, "searchDialog": {"prefix": "@/views", "path": "./custom-config/search-dialog.vue", "name": "search-dialog"}, "sendColumnDialog": {"prefix": "@/views", "path": "./custom-config/send-column-dialog.vue", "name": "send-column-dialog"}, "cmPlanRelatedPuDetail": {"prefix": "@/views", "path": "./dataswap/cm-plan-related-pu-detail.vue", "name": "cm-plan-related-pu-detail"}, "baRelevantFaList": {"prefix": "@/views", "path": "./dataswap/dataswap-ba-relevant-fa-list.vue", "name": "ba-relevant-fa-list"}, "dataswapCmaptInfoCompletionDialog": {"prefix": "@/views", "path": "./dataswap/dataswap-cmapt-info-completion-dialog.vue", "name": "dataswap-cmapt-info-completion-dialog"}, "dataswapGovCmPushDialog": {"prefix": "@/views", "path": "./dataswap/dataswap-gov-cm-push-dialog.vue", "name": "dataswap-gov-cm-push-dialog"}, "dataswapPurInfoCompletionDialog": {"prefix": "@/views", "path": "./dataswap/dataswap-pur-info-completion-dialog.vue", "name": "dataswap-pur-info-completion-dialog"}, "dataswapSyncLedger": {"prefix": "@/views", "path": "./dataswap/dataswap-sync-ledger.vue", "name": "dataswap-sync-ledger"}, "fiscalCm": {"prefix": "@/views", "path": "./dataswap/fiscal/fiscal-cm.vue", "name": "fiscal-cm"}, "govCmApt": {"prefix": "@/views", "path": "./dataswap/fiscal/gov-cm-apt.vue", "name": "gov-cm-apt"}, "govCmPush": {"prefix": "@/views", "path": "./dataswap/fiscal/gov-cm-push.vue", "name": "gov-cm-push"}, "govCm": {"prefix": "@/views", "path": "./dataswap/fiscal/gov-cm.vue", "name": "gov-cm"}, "govPreviousPurPlan": {"prefix": "@/views", "path": "./dataswap/fiscal/gov-previous-pur-plan.vue", "name": "gov-previous-pur-plan"}, "govPurPayLedger": {"prefix": "@/views", "path": "./dataswap/fiscal/gov-pur-pay-ledger.vue", "name": "gov-pur-pay-ledger"}, "govPurPay": {"prefix": "@/views", "path": "./dataswap/fiscal/gov-pur-pay.vue", "name": "gov-pur-pay"}, "govPurPlanBidDetail": {"prefix": "@/views", "path": "./dataswap/fiscal/gov-pur-plan-bid-detail.vue", "name": "gov-pur-plan-bid-detail"}, "govPurPlanBid": {"prefix": "@/views", "path": "./dataswap/fiscal/gov-pur-plan-bid.vue", "name": "gov-pur-plan-bid"}, "bankEditDialog": {"prefix": "@/views", "path": "./dei/bank/bank-edit-dialog.vue", "name": "bank-edit-dialog"}, "bankOperationlogDialog": {"prefix": "@/views", "path": "./dei/bank/bank-operationlog-dialog.vue", "name": "bank-operationlog-dialog"}, "bankRecheckDialog": {"prefix": "@/views", "path": "./dei/bank/bank-recheck-dialog.vue", "name": "bank-recheck-dialog"}, "bankReimbursementformDialog": {"prefix": "@/views", "path": "./dei/bank/bank-reimbursementform-dialog.vue", "name": "bank-reimbursementform-dialog"}, "bankUploadDialog": {"prefix": "@/views", "path": "./dei/bank/bank-upload-dialog.vue", "name": "bank-upload-dialog"}, "bankUploadDialogtow": {"prefix": "@/views", "path": "./dei/bank/bank-upload-dialogtow.vue", "name": "bank-upload-dialogtow"}, "bankViewelementsDialog": {"prefix": "@/views", "path": "./dei/bank/bank-viewelements-dialog.vue", "name": "bank-viewelements-dialog"}, "bankViewfileDialog": {"prefix": "@/views", "path": "./dei/bank/bank-viewfile-dialog.vue", "name": "bank-viewfile-dialog"}, "deiEinvoiceElementAddDialog": {"prefix": "@/views", "path": "./dei/einvoice/dei-einvoice-element-add-dialog.vue", "name": "dei-einvoice-element-add-dialog"}, "deiEinvoiceElementAddziDialog": {"prefix": "@/views", "path": "./dei/einvoice/dei-einvoice-element-addzi-dialog.vue", "name": "dei-einvoice-element-addzi-dialog"}, "deiEinvoiceElement": {"prefix": "@/views", "path": "./dei/einvoice/dei-einvoice-element.vue", "name": "dei-einvoice-element"}, "deiEinvoiceTypeAddssDialog": {"prefix": "@/views", "path": "./dei/einvoice/dei-einvoice-type-addss-dialog.vue", "name": "dei-einvoice-type-addss-dialog"}, "deiEinvoiceType": {"prefix": "@/views", "path": "./dei/einvoice/dei-einvoice-type.vue", "name": "dei-einvoice-type"}, "invInvoiceAddDialog": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-add-dialog.vue", "name": "inv-invoice-add-dialog"}, "invInvoiceAuthDialog": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-auth-dialog.vue", "name": "inv-invoice-auth-dialog"}, "invInvoiceEditDialog": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-edit-dialog.vue", "name": "inv-invoice-edit-dialog"}, "invInvoiceElementAddDialog": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-element-add-dialog.vue", "name": "inv-invoice-element-add-dialog"}, "invInvoiceElement": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-element.vue", "name": "inv-invoice-element"}, "invInvoiceFolder": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-folder.vue", "name": "inv-invoice-folder"}, "invInvoiceOwnerFolder": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-owner-folder.vue", "name": "inv-invoice-owner-folder"}, "deiEinvoiceInvInvoiceSettings": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-settings.vue", "name": "dei-einvoice-inv-invoice-settings"}, "invInvoiceTypeAddDialog": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-type-add-dialog.vue", "name": "inv-invoice-type-add-dialog"}, "invInvoiceType": {"prefix": "@/views", "path": "./dei/einvoice/inv-invoice-type.vue", "name": "inv-invoice-type"}, "electronicBankElectronicReceipt": {"prefix": "@/views", "path": "./dei/electronic-bank-electronic-receipt.vue", "name": "electronic-bank-electronic-receipt"}, "electronicBankElectronicStatement": {"prefix": "@/views", "path": "./dei/electronic-bank-electronic-statement.vue", "name": "electronic-bank-electronic-statement"}, "electronicInvoiceAdoptPage": {"prefix": "@/views", "path": "./dei/electronic-invoice-adopt-page.vue", "name": "electronic-invoice-adopt-page"}, "electronicInvoiceAdoptSetting": {"prefix": "@/views", "path": "./dei/electronic-invoice-adopt-setting.vue", "name": "electronic-invoice-adopt-setting"}, "electronicInvoiceAdopt": {"prefix": "@/views", "path": "./dei/electronic-invoice-adopt.vue", "name": "electronic-invoice-adopt"}, "electronicInvoiceBindingDialog": {"prefix": "@/views", "path": "./dei/electronic-invoice-binding-dialog.vue", "name": "electronic-invoice-binding-dialog"}, "electronicInvoiceDetailDialog": {"prefix": "@/views", "path": "./dei/electronic-invoice-detail-dialog.vue", "name": "electronic-invoice-detail-dialog"}, "electronicInvoiceElectronicVoucherlibraryDetail": {"prefix": "@/views", "path": "./dei/electronic-invoice-electronic-voucherlibrary-detail.vue", "name": "electronic-invoice-electronic-voucherlibrary-detail"}, "electronicInvoiceElectronicVoucherlibraryDiv": {"prefix": "@/views", "path": "./dei/electronic-invoice-electronic-voucherlibrary-div.vue", "name": "electronic-invoice-electronic-voucherlibrary-div"}, "electronicInvoiceElectronicVoucherlibraryMain": {"prefix": "@/views", "path": "./dei/electronic-invoice-electronic-voucherlibrary-main.vue", "name": "electronic-invoice-electronic-voucherlibrary-main"}, "electronicInvoiceElectronicVoucherlibrary": {"prefix": "@/views", "path": "./dei/electronic-invoice-electronic-voucherlibrary.vue", "name": "electronic-invoice-electronic-voucherlibrary"}, "electronicInvoiceErrorLogDialog": {"prefix": "@/views", "path": "./dei/electronic-invoice-errorLog-dialog.vue", "name": "electronic-invoice-errorLog-dialog"}, "electronicInvoicePool": {"prefix": "@/views", "path": "./dei/electronic-invoice-pool.vue", "name": "electronic-invoice-pool"}, "electronicInvoiceStatistics": {"prefix": "@/views", "path": "./dei/electronic-invoice-statistics.vue", "name": "electronic-invoice-statistics"}, "electronicInvoiceVoucherLibrary": {"prefix": "@/views", "path": "./dei/electronic-invoice-voucher-library.vue", "name": "electronic-invoice-voucher-library"}, "electronicSyncSettingsDialog": {"prefix": "@/views", "path": "./dei/electronic-sync-settings-dialog.vue", "name": "electronic-sync-settings-dialog"}, "electronicinvoiceAddMajor": {"prefix": "@/views", "path": "./dei/electronicinvoice/electronicinvoice-add-major.vue", "name": "electronicinvoice-add-major"}, "electronicinvoiceAddOrdinary": {"prefix": "@/views", "path": "./dei/electronicinvoice/electronicinvoice-add-ordinary.vue", "name": "electronicinvoice-add-ordinary"}, "electronicinvoiceAviation": {"prefix": "@/views", "path": "./dei/electronicinvoice/electronicinvoice-aviation.vue", "name": "electronicinvoice-aviation"}, "electronicinvoiceBankRecheck": {"prefix": "@/views", "path": "./dei/electronicinvoice/electronicinvoice-bank-recheck.vue", "name": "electronicinvoice-bank-recheck"}, "electronicinvoiceBankStatement": {"prefix": "@/views", "path": "./dei/electronicinvoice/electronicinvoice-bank-statement.vue", "name": "electronicinvoice-bank-statement"}, "electronicinvoiceDigitaltickets": {"prefix": "@/views", "path": "./dei/electronicinvoice/electronicinvoice-digitaltickets.vue", "name": "electronicinvoice-digitaltickets"}, "electronicinvoiceFinance": {"prefix": "@/views", "path": "./dei/electronicinvoice/electronicinvoice-finance.vue", "name": "electronicinvoice-finance"}, "electronicinvoiceRailway": {"prefix": "@/views", "path": "./dei/electronicinvoice/electronicinvoice-railway.vue", "name": "electronicinvoice-railway"}, "electronicFileView": {"prefix": "@/views", "path": "./dei/fileviewele/electronic-file-view.vue", "name": "electronic-file-view"}, "vouArchivalInformation": {"prefix": "@/views", "path": "./dei/voucherlibrarytabs/vou-archival-information.vue", "name": "vou-archival-information"}, "vouAssociation": {"prefix": "@/views", "path": "./dei/voucherlibrarytabs/vou-association.vue", "name": "vou-association"}, "vouOpenfile": {"prefix": "@/views", "path": "./dei/voucherlibrarytabs/vou-openfile.vue", "name": "vou-openfile"}, "vouTransferOfInformation": {"prefix": "@/views", "path": "./dei/voucherlibrarytabs/vou-transfer-of-information.vue", "name": "vou-transfer-of-information"}, "vouVoucherInformationPreviewMajor": {"prefix": "@/views", "path": "./dei/voucherlibrarytabs/vou-voucher-Information-preview-major.vue", "name": "vou-voucher-Information-preview-major"}, "vouVoucherInformationPreview": {"prefix": "@/views", "path": "./dei/voucherlibrarytabs/vou-voucher-Information-preview.vue", "name": "vou-voucher-Information-preview"}, "demoDialogA": {"prefix": "@/views", "path": "./demo/dialog/demoDialogA.vue", "name": "demoDialogA"}, "demoDialogB": {"prefix": "@/views", "path": "./demo/dialog/demoDialogB.vue", "name": "demoDialogB"}, "helloworld": {"prefix": "@/views", "path": "./demo/index.vue", "name": "helloworld"}, "rowEditListDemo": {"prefix": "@/views", "path": "./demo/row-edit-list-demo.vue", "name": "row-edit-list-demo"}, "deviceDialog": {"prefix": "@/views", "path": "./device/deviceDialog.vue", "name": "deviceDialog"}, "deviceLog": {"prefix": "@/views", "path": "./device/deviceLog.vue", "name": "deviceLog"}, "deviceManage": {"prefix": "@/views", "path": "./device/deviceManage.vue", "name": "deviceManage"}, "deviceSumLog": {"prefix": "@/views", "path": "./device/deviceSumLog.vue", "name": "deviceSumLog"}, "cformFileConfigurationDialog": {"prefix": "@/views", "path": "./filelibrary/cform-file-configuration-dialog.vue", "name": "cform-file-configuration-dialog"}, "fileLibraryManagement": {"prefix": "@/views", "path": "./filelibrary/file-library-management.vue", "name": "file-library-management"}, "fileLibrarySelect": {"prefix": "@/views", "path": "./filelibrary/file-library-select.vue", "name": "file-library-select"}, "fileLibrarySetcform": {"prefix": "@/views", "path": "./filelibrary/file-library-setcform.vue", "name": "file-library-setcform"}, "fileLibrarySetproject": {"prefix": "@/views", "path": "./filelibrary/file-library-setproject.vue", "name": "file-library-setproject"}, "fileUploadDialog": {"prefix": "@/views", "path": "./filelibrary/file-upload-dialog.vue", "name": "file-upload-dialog"}, "projectFileConfigurationDialog": {"prefix": "@/views", "path": "./filelibrary/project-file-configuration-dialog.vue", "name": "project-file-configuration-dialog"}, "bankDetailList": {"prefix": "@/views", "path": "./inc/bank-detail-list.vue", "name": "bank-detail-list"}, "billAcceptList": {"prefix": "@/views", "path": "./inc/bill-accept-list.vue", "name": "bill-accept-list"}, "billPayExport": {"prefix": "@/views", "path": "./inc/bill-pay-export.vue", "name": "bill-pay-export"}, "billPayListDetail": {"prefix": "@/views", "path": "./inc/bill-pay-list-detail.vue", "name": "bill-pay-list-detail"}, "incBillPayList": {"prefix": "@/views", "path": "./inc/bill-pay-list.vue", "name": "inc-bill-pay-list"}, "billSendList": {"prefix": "@/views", "path": "./inc/bill-send-list.vue", "name": "bill-send-list"}, "blockRformExtendPropertyAcceptanceFormEntity": {"prefix": "@/views", "path": "./inc/block/assetrepair/block-rform-extend-asset-repair.vue", "name": "blockRformExtend-PropertyAcceptanceFormEntity"}, "refExtendAssetRepair": {"prefix": "@/views", "path": "./inc/block/assetrepair/ref-extend-asset-repair.vue", "name": "ref-extend-asset-repair"}, "blockEmptyExtend-还款单": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-hk-extend.vue", "name": "blockEmptyExtend-还款单"}, "blockRformExtendIncBusinessCarEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-business-car.vue", "name": "blockRformExtend-IncBusinessCarEntity"}, "blockEmptyExtend-报销单": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-extend.vue", "name": "blockEmptyExtend-报销单"}, "blockRformExtendGoAbroadEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-goabroad.vue", "name": "blockRformExtend-GoAbroadEntity"}, "blockRformExtendIncGoAbroadForeignEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-goabroadforeign.vue", "name": "blockRformExtend-IncGoAbroadForeignEntity"}, "blockRformExtendIncLaborSqReceiveEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-laborSqReceive.vue", "name": "blockRformExtend-IncLaborSqReceiveEntity"}, "blockRformExtendIncLoanBas": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-loan-bas.vue", "name": "block-rform-extend-inc-loan-bas"}, "blockRformExtendIncLoanEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-loan.vue", "name": "blockRformExtend-IncLoanEntity"}, "blockRformExtendIncMeetingEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-meeting.vue", "name": "blockRformExtend-IncMeetingEntity"}, "blockRformExtendOfficialReceptionExpensesEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-officialreceptionexpenses.vue", "name": "blockRformExtend-OfficialReceptionExpensesEntity"}, "blockRformExtendMiscelLaneousEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-other.vue", "name": "blockRformExtend-MiscelLaneousEntity"}, "blockRformExtendIncPaidSplitItemEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-paidsplititem.vue", "name": "blockRformExtend-IncPaidSplitItemEntity"}, "blockRformExtendIncPaidSplitTrueEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-paidsplittrue.vue", "name": "blockRformExtend-IncPaidSplitTrueEntity"}, "blockRformExtendIncPaidSplitTrueDetailEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-paidsplittrueDetail.vue", "name": "blockRformExtend-IncPaidSplitTrueDetailEntity"}, "blockRformExtendIncPayeeEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-payee.vue", "name": "blockRformExtend-IncPayeeEntity"}, "blockRformExtendIncTeacherEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-teacher.vue", "name": "blockRformExtend-IncTeacherEntity"}, "blockRformExtendIncTrainEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-train.vue", "name": "blockRformExtend-IncTrainEntity"}, "blockRformExtendIncVisitDetailsInfoEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-visit-details.vue", "name": "blockRformExtend-IncVisitDetailsInfoEntity"}, "blockRformExtendVisitorsAccompanyEntity": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-inc-visitorsaccompany.vue", "name": "blockRformExtend-VisitorsAccompanyEntity"}, "blockEmptyExtend-借款单": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-jk-extend.vue", "name": "blockEmptyExtend-借款单"}, "blockEmptyExtend-支付更正单": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-paymake-extend.vue", "name": "blockEmptyExtend-支付更正单"}, "blockEmptyExtend-事前申请单": {"prefix": "@/views", "path": "./inc/block/block-rform-extend-sq-extend.vue", "name": "blockEmptyExtend-事前申请单"}, "blockRformExtendIncCmDetailEntity": {"prefix": "@/views", "path": "./inc/block/cm/block-rform-extend-inc-cm-detail.vue", "name": "blockRformExtend-IncCmDetailEntity"}, "blockRformExtendIncCmInfoEntity": {"prefix": "@/views", "path": "./inc/block/cm/block-rform-extend-inc-cm.vue", "name": "blockRformExtend-IncCmInfoEntity"}, "blockRformExtendLaborApplyEntity": {"prefix": "@/views", "path": "./inc/block/labor/block-rform-extend-inc-labor.vue", "name": "blockRformExtend-LaborApplyEntity"}, "blockRformExtendPuInspectDetailEntity": {"prefix": "@/views", "path": "./inc/block/pu/block-rform-extend-inc-inspect-detail.vue", "name": "blockRformExtend-PuInspectDetailEntity"}, "blockRformExtendPuInspectEntity": {"prefix": "@/views", "path": "./inc/block/pu/block-rform-extend-inc-inspect.vue", "name": "blockRformExtend-PuInspectEntity"}, "blockRformExtendIncPuDetailEntity": {"prefix": "@/views", "path": "./inc/block/pu/block-rform-extend-inc-pu-detail.vue", "name": "blockRformExtend-IncPuDetailEntity"}, "blockRformExtendIncPuInfoEntity": {"prefix": "@/views", "path": "./inc/block/pu/block-rform-extend-inc-pu.vue", "name": "blockRformExtend-IncPuInfoEntity"}, "audit-extend-报销单": {"prefix": "@/views", "path": "./inc/bx-extend-audit.vue", "name": "audit-extend-报销单"}, "bxExtendRefba": {"prefix": "@/views", "path": "./inc/bx-extend-refba.vue", "name": "bx-extend-refba"}, "incBxListApply": {"prefix": "@/views", "path": "./inc/bx-list-apply.vue", "name": "inc-bx-list-apply"}, "incBxListAudit": {"prefix": "@/views", "path": "./inc/bx-list-audit.vue", "name": "inc-bx-list-audit"}, "bxTest": {"prefix": "@/views", "path": "./inc/bx-test.vue", "name": "bx-test"}, "cardNumberList": {"prefix": "@/views", "path": "./inc/car-number-list.vue", "name": "card-number-list"}, "financialAuditList": {"prefix": "@/views", "path": "./inc/financialaudit/financial-audit-list.vue", "name": "financial-audit-list"}, "formDetail报销单-附件-2": {"prefix": "@/views", "path": "./inc/form-detail-tab-attach-bx.vue", "name": "formDetail报销单-附件-2"}, "formDetail还款单-附件-2": {"prefix": "@/views", "path": "./inc/form-detail-tab-attach-hk.vue", "name": "formDetail还款单-附件-2"}, "formDetail借款单-附件-2": {"prefix": "@/views", "path": "./inc/form-detail-tab-attach-jk.vue", "name": "formDetail借款单-附件-2"}, "formDetail事前申请单-附件": {"prefix": "@/views", "path": "./inc/form-detail-tab-attach-sq.vue", "name": "formDetail事前申请单-附件"}, "formDetail报销单-收款人-1": {"prefix": "@/views", "path": "./inc/form-detail-tab-payee-bx.vue", "name": "formDetail报销单-收款人-1"}, "formDetail还款单-还款人-1": {"prefix": "@/views", "path": "./inc/form-detail-tab-payee-hk.vue", "name": "formDetail还款单-还款人-1"}, "formDetail借款单-收款人-1": {"prefix": "@/views", "path": "./inc/form-detail-tab-payee-jk.vue", "name": "formDetail借款单-收款人-1"}, "incExtendBase": {"prefix": "@/views", "path": "./inc/form-extend-inc-base.vue", "name": "inc-extend-base"}, "formExtend-报销单": {"prefix": "@/views", "path": "./inc/form-extend-inc-bx.vue", "name": "formExtend-报销单"}, "formExtend-还款单": {"prefix": "@/views", "path": "./inc/form-extend-inc-hk.vue", "name": "formExtend-还款单"}, "formExtend-借款单": {"prefix": "@/views", "path": "./inc/form-extend-inc-jk.vue", "name": "formExtend-借款单"}, "formExtend-事前申请单": {"prefix": "@/views", "path": "./inc/form-extend-inc-sq.vue", "name": "formExtend-事前申请单"}, "hkListApply": {"prefix": "@/views", "path": "./inc/hk-list-apply.vue", "name": "hk-list-apply"}, "incHkListAudit": {"prefix": "@/views", "path": "./inc/hk-list-audit.vue", "name": "inc-hk-list-audit"}, "incCanvasTopButtons": {"prefix": "@/views", "path": "./inc/inc-canvas-top-buttons.vue", "name": "inc-canvas-top-buttons"}, "incCopyAttachment": {"prefix": "@/views", "path": "./inc/inc-copy-attachment.vue", "name": "inc-copy-attachment"}, "incLastUseOption": {"prefix": "@/views", "path": "./inc/inc-last-use-option.vue", "name": "inc-last-use-option"}, "incPayDateChooseDialog": {"prefix": "@/views", "path": "./inc/inc-pay-date-choose-dialog.vue", "name": "inc-pay-date-choose-dialog"}, "incTemplateAddDialog": {"prefix": "@/views", "path": "./inc/inc-template-add-dialog.vue", "name": "inc-template-add-dialog"}, "incTemplateManagerDialog": {"prefix": "@/views", "path": "./inc/inc-template-manager-dialog.vue", "name": "inc-template-manager-dialog"}, "invSenWordRecEdit": {"prefix": "@/views", "path": "./inc/invoice/inv-sen-word-rec-edit.vue", "name": "inv-sen-word-rec-edit"}, "invSenWordRec": {"prefix": "@/views", "path": "./inc/invoice/inv-sen-word-rec.vue", "name": "inv-sen-word-rec"}, "incJkListApply": {"prefix": "@/views", "path": "./inc/jk-list-apply.vue", "name": "inc-jk-list-apply"}, "incJkListAudit": {"prefix": "@/views", "path": "./inc/jk-list-audit.vue", "name": "inc-jk-list-audit"}, "laborTaxList": {"prefix": "@/views", "path": "./inc/labor/labor-tax-list.vue", "name": "labor-tax-list"}, "refExtendLoan": {"prefix": "@/views", "path": "./inc/loan/ref-extend-loan.vue", "name": "ref-extend-loan"}, "businessCardList": {"prefix": "@/views", "path": "./inc/order/business-card-list.vue", "name": "business-card-list"}, "businessCardPayList": {"prefix": "@/views", "path": "./inc/order/business-card-pay-list.vue", "name": "business-card-pay-list"}, "governmentPayList": {"prefix": "@/views", "path": "./inc/order/government-pay-list.vue", "name": "government-pay-list"}, "governmentPurPayList": {"prefix": "@/views", "path": "./inc/order/government-pur-pay-list.vue", "name": "government-pur-pay-list"}, "mountBgtba": {"prefix": "@/views", "path": "./inc/order/mount-bgtba-dialog.vue", "name": "mount-bgtba"}, "ownFundsPurPayList": {"prefix": "@/views", "path": "./inc/order/own-funds-pay-list.vue", "name": "own-funds-pur-pay-list"}, "payList": {"prefix": "@/views", "path": "./inc/order/pay-list.vue", "name": "pay-list"}, "payPushManagement": {"prefix": "@/views", "path": "./inc/order/pay-push-management.vue", "name": "pay-push-management"}, "paymentOrderList": {"prefix": "@/views", "path": "./inc/order/payment-order-list.vue", "name": "payment-order-list"}, "sendczConfigurationDialog": {"prefix": "@/views", "path": "./inc/order/sendcz-configuration-dialog.vue", "name": "sendcz-configuration-dialog"}, "treasuryPayList": {"prefix": "@/views", "path": "./inc/order/treasury-pay-list.vue", "name": "treasury-pay-list"}, "backPayment": {"prefix": "@/views", "path": "./inc/payaudit/back-payment-dialog.vue", "name": "back-payment"}, "confirmPayment": {"prefix": "@/views", "path": "./inc/payaudit/confirm-payment-dialog.vue", "name": "confirm-payment"}, "payAuditFocusList": {"prefix": "@/views", "path": "./inc/payaudit/pay-audit-focus-list.vue", "name": "pay-audit-focus-list"}, "payAuditFreeList": {"prefix": "@/views", "path": "./inc/payaudit/pay-audit-free-list.vue", "name": "pay-audit-free-list"}, "payAuditList": {"prefix": "@/views", "path": "./inc/payaudit/pay-audit-list.vue", "name": "pay-audit-list"}, "payInputAuditFocusList": {"prefix": "@/views", "path": "./inc/payinputaudit/pay-input-audit-focus-list.vue", "name": "pay-input-audit-focus-list"}, "payInputAuditFreeList": {"prefix": "@/views", "path": "./inc/payinputaudit/pay-input-audit-free-list.vue", "name": "pay-input-audit-free-list"}, "payInputAuditList": {"prefix": "@/views", "path": "./inc/payinputaudit/pay-input-audit-list.vue", "name": "pay-input-audit-list"}, "payMakeApply": {"prefix": "@/views", "path": "./inc/paymake/pay-make-apply.vue", "name": "pay-make-apply"}, "payMakeAudit": {"prefix": "@/views", "path": "./inc/paymake/pay-make-audit.vue", "name": "pay-make-audit"}, "payMakeDetailList": {"prefix": "@/views", "path": "./inc/paymake/pay-make-detail-list.vue", "name": "pay-make-detail-list"}, "bxIncbillpayList": {"prefix": "@/views", "path": "./inc/receipt/bx-incbillpay-list.vue", "name": "bx-incbillpay-list"}, "bxIncPayeeList": {"prefix": "@/views", "path": "./inc/receipt/bx-incPayee-list.vue", "name": "bx-incPayee-list"}, "businessCardPaymentCorrection": {"prefix": "@/views", "path": "./inc/receipt/paymentcorrection/business-card-payment-correction.vue", "name": "business-card-payment-correction"}, "governmentInvPaymentCorrection": {"prefix": "@/views", "path": "./inc/receipt/paymentcorrection/government-inv-payment-correction.vue", "name": "government-inv-payment-correction"}, "governmentProPaymentCorrection": {"prefix": "@/views", "path": "./inc/receipt/paymentcorrection/government-pro-payment-correction.vue", "name": "government-pro-payment-correction"}, "ownFundsPaymentCorrection": {"prefix": "@/views", "path": "./inc/receipt/paymentcorrection/own-funds-payment-correction.vue", "name": "own-funds-payment-correction"}, "payCorrectionManagement": {"prefix": "@/views", "path": "./inc/receipt/paymentcorrection/pay-correction-management.vue", "name": "pay-correction-management"}, "selectSyncDate": {"prefix": "@/views", "path": "./inc/receipt/paymentcorrection/select-sync-date-dialog.vue", "name": "select-sync-date"}, "treasuryPaymentCorrection": {"prefix": "@/views", "path": "./inc/receipt/paymentcorrection/treasury-payment-correction.vue", "name": "treasury-payment-correction"}, "businessCardPaymentReceipt": {"prefix": "@/views", "path": "./inc/receipt/paymentvoucher/business-card-payment-receipt.vue", "name": "business-card-payment-receipt"}, "governmentInvPaymentReceipt": {"prefix": "@/views", "path": "./inc/receipt/paymentvoucher/government-inv-payment-receipt.vue", "name": "government-inv-payment-receipt"}, "governmentProPaymentReceipt": {"prefix": "@/views", "path": "./inc/receipt/paymentvoucher/government-pro-payment-receipt.vue", "name": "government-pro-payment-receipt"}, "ownFundsPaymentReceipt": {"prefix": "@/views", "path": "./inc/receipt/paymentvoucher/own-funds-payment-receipt.vue", "name": "own-funds-payment-receipt"}, "payReceiptManagement": {"prefix": "@/views", "path": "./inc/receipt/paymentvoucher/pay-receipt-management.vue", "name": "pay-receipt-management"}, "treasuryPaymentReceipt": {"prefix": "@/views", "path": "./inc/receipt/paymentvoucher/treasury-payment-receipt.vue", "name": "treasury-payment-receipt"}, "businessCardPaymentRefund": {"prefix": "@/views", "path": "./inc/receipt/refund/business-card-payment-refund.vue", "name": "business-card-payment-refund"}, "governmentInvPaymentRefund": {"prefix": "@/views", "path": "./inc/receipt/refund/government-inv-payment-refund.vue", "name": "government-inv-payment-refund"}, "governmentProPaymentRefund": {"prefix": "@/views", "path": "./inc/receipt/refund/government-pro-payment-refund.vue", "name": "government-pro-payment-refund"}, "ownFundsPaymentRefund": {"prefix": "@/views", "path": "./inc/receipt/refund/own-funds-payment-refund.vue", "name": "own-funds-payment-refund"}, "payRefundManagement": {"prefix": "@/views", "path": "./inc/receipt/refund/pay-refund-management.vue", "name": "pay-refund-management"}, "selectSyncCustomizeDate": {"prefix": "@/views", "path": "./inc/receipt/refund/select-sync-customize-date-dialog.vue", "name": "select-sync-customize-date"}, "treasuryPaymentRefund": {"prefix": "@/views", "path": "./inc/receipt/refund/treasury-payment-refund.vue", "name": "treasury-payment-refund"}, "refundNoticeManager": {"prefix": "@/views", "path": "./inc/receipt/refund-notice-manager.vue", "name": "refund-notice-manager"}, "refundRechangeBizApply": {"prefix": "@/views", "path": "./inc/refund-rechange-biz-apply.vue", "name": "refund-rechange-biz-apply"}, "refundRechangeBizAudit": {"prefix": "@/views", "path": "./inc/refund-rechange-biz-audit.vue", "name": "refund-rechange-biz-audit"}, "refundRechangeBizBase": {"prefix": "@/views", "path": "./inc/refund-rechange-biz-base.vue", "name": "refund-rechange-biz-base"}, "refundRechangeBizDetail": {"prefix": "@/views", "path": "./inc/refund-rechange-biz-detail.vue", "name": "refund-rechange-biz-detail"}, "refundRechangeBizTbs": {"prefix": "@/views", "path": "./inc/refund-rechange-biz-tbs.vue", "name": "refund-rechange-biz-tbs"}, "refundRechangeBiz": {"prefix": "@/views", "path": "./inc/refund-rechange-biz.vue", "name": "refund-rechange-biz"}, "bxRefundNotice": {"prefix": "@/views", "path": "./inc/refund-rechange-notice.vue", "name": "bx-refund-notice"}, "refundRechangePayList": {"prefix": "@/views", "path": "./inc/refund-rechange-pay-list.vue", "name": "refund-rechange-pay-list"}, "refundRechangePayeeDetail": {"prefix": "@/views", "path": "./inc/refund-rechange-payee-detail.vue", "name": "refund-rechange-payee-detail"}, "claimFundDialog": {"prefix": "@/views", "path": "./inc/revmanage/announce/claim-fund-dialog.vue", "name": "claim-fund-dialog"}, "fundAnnList": {"prefix": "@/views", "path": "./inc/revmanage/announce/fund-ann-list.vue", "name": "fund-ann-list"}, "blockRformExtendFciaPayeeEntity": {"prefix": "@/views", "path": "./inc/revmanage/block/block-rform-extend-fcia-payee.vue", "name": "blockRformExtend-FciaPayeeEntity"}, "blockRformExtendFciaPayerEntity": {"prefix": "@/views", "path": "./inc/revmanage/block/block-rform-extend-fcia-payer.vue", "name": "blockRformExtend-FciaPayerEntity"}, "blockRformExtendRelevantFclEntity": {"prefix": "@/views", "path": "./inc/revmanage/block/block-rform-extend-fundcla.vue", "name": "blockRformExtend-RelevantFclEntity"}, "blockRformExtendRelevantIncomeTypeEntity": {"prefix": "@/views", "path": "./inc/revmanage/block/block-rform-extend-Incometype.vue", "name": "blockRformExtend-RelevantIncomeTypeEntity"}, "fciaAnnSetting": {"prefix": "@/views", "path": "./inc/revmanage/fcia/fcia-ann-setting.vue", "name": "fcia-ann-setting"}, "fciaFciaFlow": {"prefix": "@/views", "path": "./inc/revmanage/fcia/fcia-flow.vue", "name": "fcia-fcia-flow"}, "incRevmanageFciaFciaList": {"prefix": "@/views", "path": "./inc/revmanage/fcia/fcia-list.vue", "name": "inc-revmanage-fcia-fcia-list"}, "fciaSetting": {"prefix": "@/views", "path": "./inc/revmanage/fcia/fcia-setting.vue", "name": "fcia-setting"}, "fundclaApply": {"prefix": "@/views", "path": "./inc/revmanage/fundcla/fundcla-apply.vue", "name": "fundcla-apply"}, "fundclaAudit": {"prefix": "@/views", "path": "./inc/revmanage/fundcla/fundcla-audit.vue", "name": "fundcla-audit"}, "incsetting": {"prefix": "@/views", "path": "./inc/setting/incsetting.vue", "name": "incsetting"}, "synchronoussetting": {"prefix": "@/views", "path": "./inc/setting/synchronoussetting.vue", "name": "synchronoussetting"}, "refExtendSplit": {"prefix": "@/views", "path": "./inc/split/ref-extend-split.vue", "name": "ref-extend-split"}, "incSplitSplitDeptListApply": {"prefix": "@/views", "path": "./inc/split/split-dept-list-apply.vue", "name": "inc-split-split-dept-list-apply"}, "incDeptListAudit": {"prefix": "@/views", "path": "./inc/split/split-dept-list-audit.vue", "name": "inc-dept-list-audit"}, "splitDeptListLedger": {"prefix": "@/views", "path": "./inc/split/split-dept-list-ledger.vue", "name": "split-dept-list-ledger"}, "splitListApplyDeptDetail": {"prefix": "@/views", "path": "./inc/split/split-list-apply-dept-detail-dialog.vue", "name": "split-list-apply-dept-detail"}, "incSplitSplitListApply": {"prefix": "@/views", "path": "./inc/split/split-list-apply.vue", "name": "inc-split-split-list-apply"}, "incSplitListAuditFinance": {"prefix": "@/views", "path": "./inc/split/split-list-audit-finance.vue", "name": "inc-split-list-audit-finance"}, "incSplitListAudit": {"prefix": "@/views", "path": "./inc/split/split-list-audit.vue", "name": "inc-split-list-audit"}, "splitListLedgerExtend": {"prefix": "@/views", "path": "./inc/split/split-list-ledger-extend.vue", "name": "split-list-ledger-extend"}, "incSplitListLedger": {"prefix": "@/views", "path": "./inc/split/split-list-ledger.vue", "name": "inc-split-list-ledger"}, "splitSettingSaveDialog": {"prefix": "@/views", "path": "./inc/split/split-setting-save-dialog.vue", "name": "split-setting-save-dialog"}, "splitSetting": {"prefix": "@/views", "path": "./inc/split/split-setting.vue", "name": "split-setting"}, "sqApplyDialog": {"prefix": "@/views", "path": "./inc/sq-apply-dialog.vue", "name": "sq-apply-dialog"}, "audit-extend-事前申请单": {"prefix": "@/views", "path": "./inc/sq-audit-extend.vue", "name": "audit-extend-事前申请单"}, "sqDetailList": {"prefix": "@/views", "path": "./inc/sq-detail-list.vue", "name": "sq-detail-list"}, "sqListApplyAuthDialog": {"prefix": "@/views", "path": "./inc/sq-list-apply-auth-dialog.vue", "name": "sq-list-apply-auth-dialog"}, "incSqListApply": {"prefix": "@/views", "path": "./inc/sq-list-apply.vue", "name": "inc-sq-list-apply"}, "incSqListAudit": {"prefix": "@/views", "path": "./inc/sq-list-audit.vue", "name": "inc-sq-list-audit"}, "sqListAuthRecordDialog": {"prefix": "@/views", "path": "./inc/sq-list-auth-record-dialog.vue", "name": "sq-list-auth-record-dialog"}, "sqListWriteoffDetail": {"prefix": "@/views", "path": "./inc/sq-list-writeoff-detail.vue", "name": "sq-list-writeoff-detail"}, "sqListWriteoffDialog": {"prefix": "@/views", "path": "./inc/sq-list-writeoff-dialog.vue", "name": "sqListWriteoffDialog"}, "sqListWriteoff": {"prefix": "@/views", "path": "./inc/sq-list-writeoff.vue", "name": "sq-list-writeoff"}, "syncSheetPayMake": {"prefix": "@/views", "path": "./inc/sync-sheet/ex-comp/sync-sheet-pay-make.vue", "name": "syncSheet-pay-make"}, "syncSheetRefund": {"prefix": "@/views", "path": "./inc/sync-sheet/ex-comp/sync-sheet-refund.vue", "name": "syncSheet-refund"}, "incSyncSheet": {"prefix": "@/views", "path": "./inc/sync-sheet/index.vue", "name": "inc-sync-sheet"}, "sendConfigurationDialog": {"prefix": "@/views", "path": "./inc/sync-sheet/send-configuration-dialog.vue", "name": "send-configuration-dialog"}, "syncSheetComp": {"prefix": "@/views", "path": "./inc/sync-sheet/sync-sheet-comp.vue", "name": "sync-sheet-comp"}, "syncSheetSubComp": {"prefix": "@/views", "path": "./inc/sync-sheet/sync-sheet-sub-comp .vue", "name": "sync-sheet-sub-comp"}, "syncSheet-报销单": {"prefix": "@/views", "path": "./inc/sync-sheet/syncSheetEx.vue", "name": "syncSheet-报销单"}, "blockEmptyExtend-往来款项目": {"prefix": "@/views", "path": "./inc/wlkmanage/block-form-wlkxm.vue", "name": "blockEmptyExtend-往来款项目"}, "blockRformExtendWlkPayeeEntity": {"prefix": "@/views", "path": "./inc/wlkmanage/block-rform-extend-wlk-payee.vue", "name": "blockRformExtend-WlkPayeeEntity"}, "blockRformExtendRelevantWlkXmEntity": {"prefix": "@/views", "path": "./inc/wlkmanage/block-rform-extend-wlkxm.vue", "name": "blockRformExtend-RelevantWlkXmEntity"}, "formExtend-往来款项目": {"prefix": "@/views", "path": "./inc/wlkmanage/form-extend-regular-wlkxm.vue", "name": "formExtend-往来款项目"}, "wlkPayList": {"prefix": "@/views", "path": "./inc/wlkmanage/wlk-pay-list.vue", "name": "wlk-pay-list"}, "wlkProjectFlow": {"prefix": "@/views", "path": "./inc/wlkmanage/wlk-project-flow.vue", "name": "wlk-project-flow"}, "wlkProjectList": {"prefix": "@/views", "path": "./inc/wlkmanage/wlk-project-list.vue", "name": "wlk-project-list"}, "incWlkmanageWlkwfacionWlkSkApplyList": {"prefix": "@/views", "path": "./inc/wlkmanage/wlkwfacion/wlk-sk-apply-list.vue", "name": "inc-wlkmanage-wlkwfacion-wlk-sk-apply-list"}, "wlkSkAudit": {"prefix": "@/views", "path": "./inc/wlkmanage/wlkwfacion/wlk-sk-audit-list.vue", "name": "wlk-sk-audit"}, "wlkTkApply": {"prefix": "@/views", "path": "./inc/wlkmanage/wlkwfacion/wlk-tk-apply-list.vue", "name": "wlk-tk-apply"}, "wlkTkAudit": {"prefix": "@/views", "path": "./inc/wlkmanage/wlkwfacion/wlk-tk-audit-list.vue", "name": "wlk-tk-audit"}, "formDetail预算项目-附件-8": {"prefix": "@/views", "path": "./jj/form-detail-tab-attach-jj.vue", "name": "formDetail预算项目-附件-8"}, "formDetail预算项目-合同信息-9": {"prefix": "@/views", "path": "./jj/form-detail-tab-cm-info-jj.vue", "name": "formDetail预算项目-合同信息-9"}, "formDetail预算项目-概算信息-1": {"prefix": "@/views", "path": "./jj/form-detail-tab-est-info-jj.vue", "name": "formDetail预算项目-概算信息-1"}, "jjEstInfoList": {"prefix": "@/views", "path": "./jj/jj-est-info-list.vue", "name": "jj-est-info-list"}, "jjEstInfoTab": {"prefix": "@/views", "path": "./jj/jj-est-info-tab.vue", "name": "jj-est-info-tab"}, "jjXm": {"prefix": "@/views", "path": "./jj/jj-xm.vue", "name": "jj-xm"}, "blockRformExtendLaborPayeeEntity": {"prefix": "@/views", "path": "./labor/block/block-reform-extend-labor.vue", "name": "blockRformExtend-LaborPayeeEntity"}, "blockEmptyExtend-外部人员信息": {"prefix": "@/views", "path": "./labor/block/block-rform-extend-labor-extend.vue", "name": "blockEmptyExtend-外部人员信息"}, "blockRformExtendLaborOtherPayeeEntity": {"prefix": "@/views", "path": "./labor/block/block-rform-extend-labor-other.vue", "name": "blockRformExtend-LaborOtherPayeeEntity"}, "importDialog": {"prefix": "@/views", "path": "./labor/ImportDialog.vue", "name": "importDialog"}, "laborLaborDisposableListApply": {"prefix": "@/views", "path": "./labor/labor-disposable-list-apply.vue", "name": "labor-labor-disposable-list-apply"}, "laborListApply": {"prefix": "@/views", "path": "./labor/labor-list-apply.vue", "name": "labor-list-apply"}, "laborListAudit": {"prefix": "@/views", "path": "./labor/labor-list-audit.vue", "name": "labor-list-audit"}, "laborOffCampusListApply": {"prefix": "@/views", "path": "./labor/labor-off-campus-list-apply.vue", "name": "labor-off-campus-list-apply"}, "laborLaborOtherListApply": {"prefix": "@/views", "path": "./labor/labor-other-list-apply.vue", "name": "labor-labor-other-list-apply"}, "laborLaborResearchListApply": {"prefix": "@/views", "path": "./labor/labor-research-list-apply.vue", "name": "labor-labor-research-list-apply"}, "laborLaborSthListApply": {"prefix": "@/views", "path": "./labor/labor-sth-list-apply.vue", "name": "labor-labor-sth-list-apply"}, "laborLaborStudentListApply": {"prefix": "@/views", "path": "./labor/labor-student-list-apply.vue", "name": "labor-labor-student-list-apply"}, "laborTaxDetailTable": {"prefix": "@/views", "path": "./labor/labor-tax-detail-table.vue", "name": "labor-tax-detail-table"}, "laborLaborTeacherListApply": {"prefix": "@/views", "path": "./labor/labor-teacher-list-apply.vue", "name": "labor-labor-teacher-list-apply"}, "labor_issuance_standardListEdit": {"prefix": "@/views", "path": "./labor/labor_issuance_standard-list-edit.vue", "name": "labor_issuance_standard-list-edit"}, "labor_issuance_standardList": {"prefix": "@/views", "path": "./labor/labor_issuance_standard-list.vue", "name": "labor_issuance_standard-list"}, "taxRateListEdit": {"prefix": "@/views", "path": "./labor/tax-rate-list-edit.vue", "name": "tax-rate-list-edit"}, "taxRateList": {"prefix": "@/views", "path": "./labor/tax-rate-list.vue", "name": "tax-rate-list"}, "homeApplyTab": {"prefix": "@/views", "path": "./pageindex/apply-tab.vue", "name": "home-apply-tab"}, "homeAuditTab": {"prefix": "@/views", "path": "./pageindex/audit-tab.vue", "name": "home-audit-tab"}, "homePage": {"prefix": "@/views", "path": "./pageindex/home.vue", "name": "homePage"}, "nkAuditList": {"prefix": "@/views", "path": "./pageindex/nk-audit-list.vue", "name": "nk-audit-list"}, "bxPayListApply": {"prefix": "@/views", "path": "./pay/bx-pay-list-apply.vue", "name": "bx-pay-list-apply"}, "incPayListAudit": {"prefix": "@/views", "path": "./pay/bx-pay-list-audit.vue", "name": "inc-pay-list-audit"}, "reportPermissionSetting": {"prefix": "@/views", "path": "./permissionsetting/report-permission-setting.vue", "name": "report-permission-setting"}, "1project-list-deppm": {"prefix": "@/views", "path": "./pm/1project-list-deppm.vue", "name": "1project-list-deppm"}, "pmAdjustaddMidyearProjectIncreaseApply": {"prefix": "@/views", "path": "./pm/adjustadd/midyear-project-increase-apply.vue", "name": "pm-adjustadd-midyear-project-increase-apply"}, "pmAdjustaddMidyearProjectIncreaseAudit": {"prefix": "@/views", "path": "./pm/adjustadd/midyear-project-increase-audit.vue", "name": "pm-adjustadd-midyear-project-increase-audit"}, "argumentDialogReg": {"prefix": "@/views", "path": "./pm/argument/argument-dialog-reg.vue", "name": "argument-dialog-reg"}, "argumentDialog": {"prefix": "@/views", "path": "./pm/argument/argument-dialog.vue", "name": "argument-dialog"}, "argumentLaunchDialog": {"prefix": "@/views", "path": "./pm/argument/argument-launch-dialog.vue", "name": "argument-launch-dialog"}, "argumentList": {"prefix": "@/views", "path": "./pm/argument/argument-list.vue", "name": "argument-list"}, "argumentRegisterDialog": {"prefix": "@/views", "path": "./pm/argument/argument-register-dialog.vue", "name": "argument-register-dialog"}, "argumentRegisterResultDialog": {"prefix": "@/views", "path": "./pm/argument/argument-register-result-dialog.vue", "name": "argument-register-result-dialog"}, "argumentResultDialog": {"prefix": "@/views", "path": "./pm/argument/argument-result-dialog.vue", "name": "argument-result-dialog"}, "expertDialog": {"prefix": "@/views", "path": "./pm/argument/expert-dialog.vue", "name": "expert-dialog"}, "expertList": {"prefix": "@/views", "path": "./pm/argument/expert-list.vue", "name": "expert-list"}, "expertOptionDialog": {"prefix": "@/views", "path": "./pm/argument/expert-option-dialog.vue", "name": "expert-option-dialog"}, "expertOptionOtherDialog": {"prefix": "@/views", "path": "./pm/argument/expert-option-other-dialog.vue", "name": "expert-option-other-dialog"}, "pmFileDialog": {"prefix": "@/views", "path": "./pm/argument/pm-file-dialog.vue", "name": "pm-file-dialog"}, "applyPmList": {"prefix": "@/views", "path": "./pm/bg/1apply-pm-list.vue", "name": "apply-pm-list"}, "pmBgApplyFnList": {"prefix": "@/views", "path": "./pm/bg/apply-fn-list.vue", "name": "pm-bg-apply-fn-list"}, "pmBgApplyOneupBgList": {"prefix": "@/views", "path": "./pm/bg/apply-oneup-bg-list.vue", "name": "pm-bg-apply-oneup-bg-list"}, "pmBgApplyPmApproval": {"prefix": "@/views", "path": "./pm/bg/apply-pm-approval.vue", "name": "pm-bg-apply-pm-approval"}, "pmBgApplyPmList": {"prefix": "@/views", "path": "./pm/bg/apply-pm-list.vue", "name": "pm-bg-apply-pm-list"}, "pmBgApplyTwoupBgList": {"prefix": "@/views", "path": "./pm/bg/apply-twoup-bg-list.vue", "name": "pm-bg-apply-twoup-bg-list"}, "attachmentPane": {"prefix": "@/views", "path": "./pm/bg/attachment-pane.vue", "name": "attachment-pane"}, "auditOneupBgApproval": {"prefix": "@/views", "path": "./pm/bg/audit-oneup-bg-approval.vue", "name": "audit-oneup-bg-approval"}, "auditPmApproval": {"prefix": "@/views", "path": "./pm/bg/audit-pm-approval.vue", "name": "audit-pm-approval"}, "auditTwoupBgApproval": {"prefix": "@/views", "path": "./pm/bg/audit-twoup-bg-approval.vue", "name": "audit-twoup-bg-approval"}, "bgAssembleDataAll": {"prefix": "@/views", "path": "./pm/bg/bg-assemble-data-all.vue", "name": "bg-assemble-data-all"}, "bgAssembleDataBase": {"prefix": "@/views", "path": "./pm/bg/bg-assemble-data-base.vue", "name": "bg-assemble-data-base"}, "bgAssembleDataDetail": {"prefix": "@/views", "path": "./pm/bg/bg-assemble-data-detail.vue", "name": "bg-assemble-data-detail"}, "bgAssembleData": {"prefix": "@/views", "path": "./pm/bg/bg-assemble-data.vue", "name": "bg-assemble-data"}, "bgChangeBaList": {"prefix": "@/views", "path": "./pm/bg/bg-change-ba-list.vue", "name": "bg-change-ba-list"}, "bgCheckErrorInfoDialog": {"prefix": "@/views", "path": "./pm/bg/bg-check-error-info-dialog.vue", "name": "bg-check-error-info-dialog"}, "bgChooseDialog": {"prefix": "@/views", "path": "./pm/bg/bg-choose-dialog.vue", "name": "bg-choose-dialog"}, "bgDataPacketDialog": {"prefix": "@/views", "path": "./pm/bg/bg-data-packet-dialog.vue", "name": "bg-data-packet-dialog"}, "bgDataPacketFinanceConfigDialog": {"prefix": "@/views", "path": "./pm/bg/bg-data-packet-finance-config-dialog.vue", "name": "bg-data-packet-finance-config-dialog"}, "bgDialogQuota": {"prefix": "@/views", "path": "./pm/bg/bg-dialog-quota.vue", "name": "bg-dialog-quota"}, "bgFinanceProjectComp": {"prefix": "@/views", "path": "./pm/bg/bg-finance-project-comp.vue", "name": "bg-finance-project-comp"}, "form-tab-save-当月资金使用情况": {"prefix": "@/views", "path": "./pm/bg/bg-fund-month-plan.vue", "name": "form-tab-save-当月资金使用情况"}, "form-tab-save-当年资金使用情况": {"prefix": "@/views", "path": "./pm/bg/bg-fund-year-plan.vue", "name": "form-tab-save-当年资金使用情况"}, "bgGenerateDialog": {"prefix": "@/views", "path": "./pm/bg/bg-generate-dialog.vue", "name": "bg-generate-dialog"}, "bgList": {"prefix": "@/views", "path": "./pm/bg/bg-list.vue", "name": "bg-list"}, "bgTemplateSetting": {"prefix": "@/views", "path": "./pm/bg/bg-template-setting.vue", "name": "bg-template-setting"}, "budgetBaseApplyList": {"prefix": "@/views", "path": "./pm/bg/budget-base-apply-list.vue", "name": "budget-base-apply-list"}, "budgetBaseAuditList": {"prefix": "@/views", "path": "./pm/bg/budget-base-audit-list.vue", "name": "budget-base-audit-list"}, "budgetBaseList": {"prefix": "@/views", "path": "./pm/bg/budget-base-list.vue", "name": "budget-base-list"}, "budgetBatchModifyDialog": {"prefix": "@/views", "path": "./pm/bg/budget-batch-modify-dialog.vue", "name": "budget-batch-modify-dialog"}, "budgetBatchUpdateDialog": {"prefix": "@/views", "path": "./pm/bg/budget-batch-update-dialog.vue", "name": "budget-batch-update-dialog"}, "budgetComarisonDialog": {"prefix": "@/views", "path": "./pm/bg/budget-comarison-dialog.vue", "name": "budget-comarison-dialog"}, "calendarBgSelect": {"prefix": "@/views", "path": "./pm/bg/calendar-bg-select.vue", "name": "calendar-bg-select"}, "ctrlNodeDialog": {"prefix": "@/views", "path": "./pm/bg/ctrl/ctrl-node-dialog.vue", "name": "ctrlNodeDialog"}, "ctrlNode": {"prefix": "@/views", "path": "./pm/bg/ctrl/ctrl-node.vue", "name": "ctrlNode"}, "ctrlNumDepartmentDialog": {"prefix": "@/views", "path": "./pm/bg/ctrl/ctrl-num-department-dialog.vue", "name": "ctrlNumDepartmentDialog"}, "ctrlNumDialog": {"prefix": "@/views", "path": "./pm/bg/ctrl/ctrl-num-dialog.vue", "name": "ctrlNumDialog"}, "ctrlNumMergeDialog": {"prefix": "@/views", "path": "./pm/bg/ctrl/ctrl-num-merge-dialog.vue", "name": "ctrl-num-merge-dialog"}, "ctrlNumOnce": {"prefix": "@/views", "path": "./pm/bg/ctrl/ctrl-num-once.vue", "name": "ctrl-num-once"}, "ctrlNumOneup": {"prefix": "@/views", "path": "./pm/bg/ctrl/ctrl-num-oneup.vue", "name": "ctrl-num-oneup"}, "ctrlNumPublic": {"prefix": "@/views", "path": "./pm/bg/ctrl/ctrl-num-public.vue", "name": "ctrl-num-public"}, "financeSysBg": {"prefix": "@/views", "path": "./pm/bg/finance-sys-bg.vue", "name": "finance-sys-bg"}, "formDetail预算编制-附件": {"prefix": "@/views", "path": "./pm/bg/form-detail-tab-attach-bg.vue", "name": "formDetail预算编制-附件"}, "form-tab-save-绩效目标": {"prefix": "@/views", "path": "./pm/bg/form-tab-merits-target-bg.vue", "name": "form-tab-save-绩效目标"}, "form-tab-save-basic-extend-func-1级项目预算填报模板": {"prefix": "@/views", "path": "./pm/bg/form-tab-save-basic-extend-func-1levelpmtemplate.vue", "name": "form-tab-save-basic-extend-func-1级项目预算填报模板"}, "form-tab-save-预算基本信息": {"prefix": "@/views", "path": "./pm/bg/form-tab-save-bg.vue", "name": "form-tab-save-预算基本信息"}, "form-tab-save-func-extend-预算域": {"prefix": "@/views", "path": "./pm/bg/form-tab-save-func-extend-bgpm.vue", "name": "form-tab-save-func-extend-预算域"}, "form-tab-save-子项目测算": {"prefix": "@/views", "path": "./pm/bg/form-tab-save-submea.vue", "name": "form-tab-save-子项目测算"}, "oneFinancePmDialog": {"prefix": "@/views", "path": "./pm/bg/one-finance-pm-dialog.vue", "name": "one-finance-pm-dialog"}, "projectTabSetting": {"prefix": "@/views", "path": "./pm/bg/project-tab-setting.vue", "name": "project-tab-setting"}, "bgQuotaDialog": {"prefix": "@/views", "path": "./pm/bg/quota/bg-quota-dialog.vue", "name": "bg-quota-dialog"}, "bgQuotaExcelImp": {"prefix": "@/views", "path": "./pm/bg/quota/bg-quota-excel-imp.vue", "name": "bg-quota-excel-imp"}, "bgQuotaGenerateDialog": {"prefix": "@/views", "path": "./pm/bg/quota/bg-quota-generate-summary-dialog.vue", "name": "bg-quota-generate-dialog"}, "bgQuotaGenerate": {"prefix": "@/views", "path": "./pm/bg/quota/bg-quota-generate.vue", "name": "bg-quota-generate"}, "bgQuotaMerge": {"prefix": "@/views", "path": "./pm/bg/quota/bg-quota-merge.vue", "name": "bg-quota-merge"}, "bgQuota": {"prefix": "@/views", "path": "./pm/bg/quota/bg-quota.vue", "name": "bg-quota"}, "fnCtrlAddDialog": {"prefix": "@/views", "path": "./pm/bg/schoolCtrl/fn-ctrl-add-dialog.vue", "name": "fn-ctrl-add-dialog"}, "fnCtrlList": {"prefix": "@/views", "path": "./pm/bg/schoolCtrl/fn-ctrl-list.vue", "name": "fn-ctrl-list"}, "schoolCtrlList": {"prefix": "@/views", "path": "./pm/bg/schoolCtrl/school-ctrl-list.vue", "name": "school-ctrl-list"}, "selectCtrlnumDialog": {"prefix": "@/views", "path": "./pm/bg/select-ctrlnum-dialog.vue", "name": "select-ctrlnum-dialog"}, "subSetupDialog": {"prefix": "@/views", "path": "./pm/bg/sub-setup-dialog.vue", "name": "sub-setup-dialog"}, "budgetTurnIndexConfig": {"prefix": "@/views", "path": "./pm/budgetconfig/index.vue", "name": "budget-turn-index-config"}, "pmBudgetdetailsBudgetDetailsApply": {"prefix": "@/views", "path": "./pm/budgetdetails/budget-details-apply.vue", "name": "pm-budgetdetails-budget-details-apply"}, "budgetDetailsAudit": {"prefix": "@/views", "path": "./pm/budgetdetails/budget-details-audit.vue", "name": "budget-details-audit"}, "caseClassifyDialog": {"prefix": "@/views", "path": "./pm/case/case-classify-dialog.vue", "name": "case-classify-dialog"}, "caseClassify": {"prefix": "@/views", "path": "./pm/case/case-classify.vue", "name": "case-classify"}, "caseLibraryDialog": {"prefix": "@/views", "path": "./pm/case/case-library-dialog.vue", "name": "case-library-dialog"}, "caseLibrary": {"prefix": "@/views", "path": "./pm/case/case-library.vue", "name": "case-library"}, "pmChangeDetailDialog": {"prefix": "@/views", "path": "./pm/change/pm-change-detail-dialog.vue", "name": "pm-change-detail-dialog"}, "pmChangeListApply": {"prefix": "@/views", "path": "./pm/change/pm-change-list-apply.vue", "name": "pm-change-list-apply"}, "pmChangeListAudit": {"prefix": "@/views", "path": "./pm/change/pm-change-list-audit.vue", "name": "pm-change-list-audit"}, "project_capital_allocation": {"prefix": "@/views", "path": "./pm/deppm/project_capital_allocation.vue", "name": "project_capital_allocation"}, "project_process_return": {"prefix": "@/views", "path": "./pm/deppm/project_process_return.vue", "name": "project_process_return"}, "project_sync": {"prefix": "@/views", "path": "./pm/deppm/project_sync.vue", "name": "project_sync"}, "project_tag_setup": {"prefix": "@/views", "path": "./pm/deppm/project_tag_setup.vue", "name": "project_tag_setup"}, "project_transfer": {"prefix": "@/views", "path": "./pm/deppm/project_transfer.vue", "name": "project_transfer"}, "transferDlg": {"prefix": "@/views", "path": "./pm/deppm/transfer-dlg.vue", "name": "transfer-dlg"}, "evalListAudit": {"prefix": "@/views", "path": "./pm/eval/eval-audit-list.vue", "name": "eval-list-audit"}, "evalDetailsDialog": {"prefix": "@/views", "path": "./pm/eval/eval-details-dialog.vue", "name": "eval-details-dialog"}, "evalFundsEntity": {"prefix": "@/views", "path": "./pm/eval/eval-funds-dialog.vue", "name": "evalFundsEntity"}, "evalQuotaEntity": {"prefix": "@/views", "path": "./pm/eval/eval-quota-dialog.vue", "name": "evalQuotaEntity"}, "evalListSubmit": {"prefix": "@/views", "path": "./pm/eval/eval-submit-list.vue", "name": "eval-list-submit"}, "evalSubmitDialog": {"prefix": "@/views", "path": "./pm/eval/eval-sumbit-dialog.vue", "name": "eval-submit-dialog"}, "evalSummaryDialog": {"prefix": "@/views", "path": "./pm/eval/eval-summary-dialog.vue", "name": "eval-summary-dialog"}, "summaryEvalList": {"prefix": "@/views", "path": "./pm/eval/eval-summary-list.vue", "name": "summary-eval-list"}, "evalTemplateList": {"prefix": "@/views", "path": "./pm/eval/eval-template-list.vue", "name": "eval-template-list"}, "expenddetaildialog": {"prefix": "@/views", "path": "./pm/expendstandard/expend-standard-detail-dialog.vue", "name": "expenddetaildialog"}, "expendStandardDetail": {"prefix": "@/views", "path": "./pm/expendstandard/expend-standard-detail-list.vue", "name": "expend-standard-detail"}, "expenddialog": {"prefix": "@/views", "path": "./pm/expendstandard/expend-standard-dialog.vue", "name": "expenddialog"}, "expendStandardTab": {"prefix": "@/views", "path": "./pm/expendstandard/expend-standard-tab.vue", "name": "expend-standard-tab"}, "expendtypedialog": {"prefix": "@/views", "path": "./pm/expendstandard/expend-standard-type-dialog.vue", "name": "expendtypedialog"}, "expendStandardType": {"prefix": "@/views", "path": "./pm/expendstandard/expend-standard-type-list.vue", "name": "expend-standard-type"}, "financePmList": {"prefix": "@/views", "path": "./pm/finance/finance-pm-list.vue", "name": "finance-pm-list"}, "financePmZftzList": {"prefix": "@/views", "path": "./pm/finance/finance-pm-zftz-list.vue", "name": "finance-pm-zftz-list"}, "financeProList": {"prefix": "@/views", "path": "./pm/finance/finance-pro-list.vue", "name": "finance-pro-list"}, "formDetail财政项目-附件": {"prefix": "@/views", "path": "./pm/finance/form-detail-tab-attach-fn.vue", "name": "formDetail财政项目-附件"}, "zftzPayList": {"prefix": "@/views", "path": "./pm/finance/zftz-pay-list.vue", "name": "zftz-pay-list"}, "formExtend-预算编制": {"prefix": "@/views", "path": "./pm/form-extend-regular-bg.vue", "name": "formExtend-预算编制"}, "formExtendRegularBgpm": {"prefix": "@/views", "path": "./pm/form-extend-regular-bgpm.vue", "name": "form-extend-regular-bgpm"}, "formExtend-财政项目": {"prefix": "@/views", "path": "./pm/form-extend-regular-cz.vue", "name": "formExtend-财政项目"}, "formExtend-预算项目": {"prefix": "@/views", "path": "./pm/form-extend-regular-pm.vue", "name": "formExtend-预算项目"}, "kpiquotalibdialog": {"prefix": "@/views", "path": "./pm/merits/kpi-quota-lib-dialog.vue", "name": "kpiquotalibdialog"}, "pmkpiquotalib": {"prefix": "@/views", "path": "./pm/merits/kpi-quota-lib.vue", "name": "pmkpiquotalib"}, "quotaclassifydialog": {"prefix": "@/views", "path": "./pm/merits/quota-classify-dialog.vue", "name": "quotaclassifydialog"}, "pmquotaclassify": {"prefix": "@/views", "path": "./pm/merits/quota-classify.vue", "name": "pmquotaclassify"}, "monitorDetailsDialog": {"prefix": "@/views", "path": "./pm/monitor/monitor-details-dialog.vue", "name": "monitor-details-dialog"}, "monitorFundsEntity": {"prefix": "@/views", "path": "./pm/monitor/monitor-funds-dialog.vue", "name": "monitorFundsEntity"}, "monitorListAudit": {"prefix": "@/views", "path": "./pm/monitor/monitor-list-audit.vue", "name": "monitor-list-audit"}, "monitorQuota": {"prefix": "@/views", "path": "./pm/monitor/monitor-quota-dialog.vue", "name": "monitorQuota"}, "monitorSituation": {"prefix": "@/views", "path": "./pm/monitor/monitor-situation-dialog.vue", "name": "monitorSituation"}, "monitorSubmitAdd": {"prefix": "@/views", "path": "./pm/monitor/monitor-submit-dialog.vue", "name": "monitor-submit-add"}, "monitorSubmit": {"prefix": "@/views", "path": "./pm/monitor/monitor-submit-list.vue", "name": "monitor-submit"}, "summaryListView": {"prefix": "@/views", "path": "./pm/monitor/monitor-summary-list.vue", "name": "summary-list-view"}, "monitorListView": {"prefix": "@/views", "path": "./pm/monitor/monitor-template-list.vue", "name": "monitor-list-view"}, "proMonitorEvalList": {"prefix": "@/views", "path": "./pm/monitor/pro-monitor-eval-list.vue", "name": "pro-monitor-eval-list"}, "proSummaryDialog": {"prefix": "@/views", "path": "./pm/monitor/pro-summary-dialog.vue", "name": "pro-summary-dialog"}, "templateUnitDialog": {"prefix": "@/views", "path": "./pm/monitor/template-unit-dialog.vue", "name": "template-unit-dialog"}, "agenPerformanceAudit": {"prefix": "@/views", "path": "./pm/performance/agen-performance-audit.vue", "name": "agen-performance-audit"}, "agenPerformanceDialog": {"prefix": "@/views", "path": "./pm/performance/agen-performance-dialog.vue", "name": "agen-performance-dialog"}, "agenPerformance": {"prefix": "@/views", "path": "./pm/performance/agen-performance.vue", "name": "agen-performance"}, "fiscalPerformanceAudit": {"prefix": "@/views", "path": "./pm/performance/fiscal-performance-audit.vue", "name": "fiscal-performance-audit"}, "fiscalPerformanceDialog": {"prefix": "@/views", "path": "./pm/performance/fiscal-performance-dialog.vue", "name": "fiscal-performance-dialog"}, "fiscalPerformanceQuotaDialog": {"prefix": "@/views", "path": "./pm/performance/fiscal-performance-quota-dialog.vue", "name": "fiscal-performance-quota-dialog"}, "fiscalPerformance": {"prefix": "@/views", "path": "./pm/performance/fiscal-performance.vue", "name": "fiscal-performance"}, "performanceQuotaDialog": {"prefix": "@/views", "path": "./pm/performance/performance-quota-dialog.vue", "name": "performance-quota-dialog"}, "pmCode": {"prefix": "@/views", "path": "./pm/pm-code.vue", "name": "pm-code"}, "pmBxList": {"prefix": "@/views", "path": "./pm/project-list-audit.vue", "name": "pm-bx-list"}, "projectListDeppmOthers": {"prefix": "@/views", "path": "./pm/project-list-deppm-others.vue", "name": "project-list-deppm-others"}, "pmProjectListDeppm": {"prefix": "@/views", "path": "./pm/project-list-deppm.vue", "name": "pm-project-list-deppm"}, "pmProjectListTab": {"prefix": "@/views", "path": "./pm/project-list-tab.vue", "name": "pm-project-list-tab"}, "pmProjectList": {"prefix": "@/views", "path": "./pm/project-list.vue", "name": "pm-project-list"}, "projectLibraryCategoryIndex": {"prefix": "@/views", "path": "./pm/projectlibrary/index.vue", "name": "project-library-category-index"}, "pmResearchAuthorizeResearchPmAuthorizeList": {"prefix": "@/views", "path": "./pm/research/authorize/research-pm-authorize-list.vue", "name": "pm-research-authorize-research-pm-authorize-list"}, "researchPmBaQueryList": {"prefix": "@/views", "path": "./pm/research/authorize/research-pm-ba-query-list.vue", "name": "research-pm-ba-query-list"}, "form-tab-save-变更记录": {"prefix": "@/views", "path": "./pm/research/details/form-detail-change.vue", "name": "form-tab-save-变更记录"}, "form-tab-save-预算信息": {"prefix": "@/views", "path": "./pm/research/details/form-detail-proInfo.vue", "name": "form-tab-save-预算信息"}, "researchExtendDialog": {"prefix": "@/views", "path": "./pm/research/extend/research-extend-dialog.vue", "name": "researchExtendDialog"}, "useAmountLinkHandlerHtml": {"prefix": "@/views", "path": "./pm/research/extend/research-use-link-dialog.vue", "name": "useAmountLinkHandlerHtml"}, "formExtend-科研项目": {"prefix": "@/views", "path": "./pm/research/form-extend-regular-research.vue", "name": "formExtend-科研项目"}, "pmResearchFlow": {"prefix": "@/views", "path": "./pm/research/research-flow.vue", "name": "pm-research-flow"}, "researchIndicatorDialog": {"prefix": "@/views", "path": "./pm/research/research-Indicator-dialog.vue", "name": "research-indicator-dialog"}, "researchIndicatorPro": {"prefix": "@/views", "path": "./pm/research/research-indicator-pro.vue", "name": "research-indicator-pro"}, "pmResearchResearchList": {"prefix": "@/views", "path": "./pm/research/research-list.vue", "name": "pm-research-research-list"}, "researchModifyDialog": {"prefix": "@/views", "path": "./pm/research/research-modify-dialog.vue", "name": "research-modify-dialog"}, "researchScopeDialog": {"prefix": "@/views", "path": "./pm/research/research-scope-dialog.vue", "name": "research-scope-dialog"}, "researchScopePro": {"prefix": "@/views", "path": "./pm/research/research-scope-pro-dialog.vue", "name": "research-scope-pro"}, "researchScopeSumDialog": {"prefix": "@/views", "path": "./pm/research/research-scope-sum-dialog.vue", "name": "research-scope-sum-dialog"}, "researchScopeSumPro": {"prefix": "@/views", "path": "./pm/research/research-scope-sum-pro.vue", "name": "research-scope-sum-pro"}, "scopeAddDialog": {"prefix": "@/views", "path": "./pm/scope/scope-add-dialog.vue", "name": "scope-add-dialog"}, "pmScopeScopeList": {"prefix": "@/views", "path": "./pm/scope/scope-list.vue", "name": "pm-scope-scope-list"}, "budgetStandardList": {"prefix": "@/views", "path": "./pm/standard/budget-standard-list.vue", "name": "budget-standard-list"}, "budgetSaveControl": {"prefix": "@/views", "path": "./pm/standard/budget-standard-save-dialog.vue", "name": "budget-save-control"}, "budgetSaveTree": {"prefix": "@/views", "path": "./pm/standard/budget-standard-tree-dialog.vue", "name": "budget-save-tree"}, "blockEmptyExtend-采购需求单": {"prefix": "@/views", "path": "./pu/block-cform-extend-pu-de.vue", "name": "blockEmptyExtend-采购需求单"}, "blockEmptyExtend-采购申请单": {"prefix": "@/views", "path": "./pur/purapply/block-rform-extend-pur-applyl-extend.vue", "name": "blockEmptyExtend-采购申请单"}, "blockEmptyExtend-自行采购决策": {"prefix": "@/views", "path": "./pu/block-cform-extend-zixing.vue", "name": "blockEmptyExtend-自行采购决策"}, "blockRformExtendPurDemandEntity": {"prefix": "@/views", "path": "./pu/block-rform-extend-pu-demand.vue", "name": "blockRformExtend-PurDemandEntity"}, "blockRformExtendPurDetailEntity": {"prefix": "@/views", "path": "./pu/block-rform-extend-pu-detail.vue", "name": "blockRformExtend-PurDetailEntity"}, "blockRformExtendPuBidSupplierEntity": {"prefix": "@/views", "path": "./pu/block-rform-extend-pu-supplier.vue", "name": "blockRformExtend-PuBidSupplierEntity"}, "puChangePuApplyChangeAudit": {"prefix": "@/views", "path": "./pu/change/pu-apply-change-audit.vue", "name": "pu-change-pu-apply-change-audit"}, "puChangePuApplyChange": {"prefix": "@/views", "path": "./pu/change/pu-apply-change.vue", "name": "pu-change-pu-apply-change"}, "puChangeApply": {"prefix": "@/views", "path": "./pu/change/pu-change-apply.vue", "name": "pu-change-apply"}, "puChangeAudit": {"prefix": "@/views", "path": "./pu/change/pu-change-audit.vue", "name": "pu-change-audit"}, "puChangeDetailList": {"prefix": "@/views", "path": "./pu/change/pu-change-detail-list.vue", "name": "pu-change-detail-list"}, "puChangeDetailTab": {"prefix": "@/views", "path": "./pu/change/pu-change-detail-tab.vue", "name": "pu-change-detail-tab"}, "puChangeDetail": {"prefix": "@/views", "path": "./pu/change/pu-change-detail.vue", "name": "pu-change-detail"}, "puChangeDialog": {"prefix": "@/views", "path": "./pu/change/pu-change-dialog.vue", "name": "puChangeDialog"}, "puChangeList": {"prefix": "@/views", "path": "./pu/change/pu-change-list.vue", "name": "pu-change-list"}, "puChangeReason": {"prefix": "@/views", "path": "./pu/change/pu-change-reason.vue", "name": "pu-change-reason"}, "puLink": {"prefix": "@/views", "path": "./pu/change/pu-link.vue", "name": "pu-link"}, "formDetail采购续签备案单-附件-9": {"prefix": "@/views", "path": "./pu/form-detail-tab-attach-renewal-record.vue", "name": "formDetail采购续签备案单-附件-9"}, "formExtend-采购履约评价": {"prefix": "@/views", "path": "./pu/form-extend-pu-ev.vue", "name": "formExtend-采购履约评价"}, "formExtend-采购续签备案单": {"prefix": "@/views", "path": "./pu/form-extend-renewal-record.vue", "name": "formExtend-采购续签备案单"}, "blockEmptyExtend-采购明细验收单": {"prefix": "@/views", "path": "./pu/itemacception/block-cform-extend-itemacception.vue", "name": "blockEmptyExtend-采购明细验收单"}, "blockRformExtendPurDetailAcceptionEntity": {"prefix": "@/views", "path": "./pu/itemacception/block-rform-extend-pu-acc-detail.vue", "name": "blockRformExtend-PurDetailAcceptionEntity"}, "puItemacceptionPuItemAcceptionAudit": {"prefix": "@/views", "path": "./pu/itemacception/pu-item-acception-audit.vue", "name": "pu-itemacception-pu-item-acception-audit"}, "puItemacceptionPuItemAcceptionList": {"prefix": "@/views", "path": "./pu/itemacception/pu-item-acception-list.vue", "name": "pu-itemacception-pu-item-acception-list"}, "puItemAcception": {"prefix": "@/views", "path": "./pu/itemacception/pu-item-acception.vue", "name": "pu-item-acception"}, "puAccLedger": {"prefix": "@/views", "path": "./pu/ledger/pu-acc-ledger.vue", "name": "pu-acc-ledger"}, "puLedger": {"prefix": "@/views", "path": "./pu/ledger/pu-ledger.vue", "name": "pu-ledger"}, "puRenRecLedger": {"prefix": "@/views", "path": "./pu/ledger/pu-ren-rec-ledger.vue", "name": "pu-ren-rec-ledger"}, "purDemandLedger": {"prefix": "@/views", "path": "./pu/ledger/pude-ledger.vue", "name": "pur-demand-ledger"}, "puPuAcceptionAudit": {"prefix": "@/views", "path": "./pu/pu-acception-audit.vue", "name": "pu-pu-acception-audit"}, "puAcceptionDetailEditDialog": {"prefix": "@/views", "path": "./pu/pu-acception-detail-edit-dialog.vue", "name": "pu-acception-detail-edit-dialog"}, "puAcceptionDetail": {"prefix": "@/views", "path": "./pu/pu-acception-detail.vue", "name": "pu-acception-detail"}, "puAcceptionEdit": {"prefix": "@/views", "path": "./pu/pu-acception-edit.vue", "name": "pu-acception-edit"}, "puAcception": {"prefix": "@/views", "path": "./pu/pu-acception.vue", "name": "pu-acception"}, "puApplyAuditRenew": {"prefix": "@/views", "path": "./pu/pu-apply-audit-renew.vue", "name": "pu-apply-audit-renew"}, "puApplyAudit": {"prefix": "@/views", "path": "./pu/pu-apply-audit.vue", "name": "pu-apply-audit"}, "puApplyRenew": {"prefix": "@/views", "path": "./pu/pu-apply-renew.vue", "name": "pu-apply-renew"}, "puPuApply": {"prefix": "@/views", "path": "./pu/pu-apply.vue", "name": "pu-pu-apply"}, "puPuAudit": {"prefix": "@/views", "path": "./pu/pu-audit.vue", "name": "pu-pu-audit"}, "puBidFileApply": {"prefix": "@/views", "path": "./pu/pu-bid-file-apply.vue", "name": "pu-bid-file-apply"}, "puBidFileAudit": {"prefix": "@/views", "path": "./pu/pu-bid-file-audit.vue", "name": "pu-bid-file-audit"}, "puBidFileEditDialog": {"prefix": "@/views", "path": "./pu/pu-bid-file-edit-dialog.vue", "name": "pu-bid-file-edit-dialog"}, "puBidWinRelatedBas": {"prefix": "@/views", "path": "./pu/pu-bid-win-related-bas.vue", "name": "pu-bid-win-related-bas"}, "puBidWin": {"prefix": "@/views", "path": "./pu/pu-bid-win.vue", "name": "pu-bid-win"}, "puBizflowDetail": {"prefix": "@/views", "path": "./pu/pu-bizflow-detail.vue", "name": "pu-bizflow-detail"}, "puBizflow": {"prefix": "@/views", "path": "./pu/pu-bizflow.vue", "name": "pu-bizflow"}, "puConfirmAmountDlg": {"prefix": "@/views", "path": "./pu/pu-confirm-amount-dlg.vue", "name": "pu-confirm-amount-dlg"}, "puConfirmAmount": {"prefix": "@/views", "path": "./pu/pu-confirm-amount.vue", "name": "pu-confirm-amount"}, "puConfirmQuantityDlg": {"prefix": "@/views", "path": "./pu/pu-confirm-quantity-dlg.vue", "name": "pu-confirm-quantity-dlg"}, "puConfirmQuantity": {"prefix": "@/views", "path": "./pu/pu-confirm-quantity.vue", "name": "pu-confirm-quantity"}, "puCreateCmDetail": {"prefix": "@/views", "path": "./pu/pu-create-cm-detail.vue", "name": "pu-create-cm-detail"}, "puCreateCm": {"prefix": "@/views", "path": "./pu/pu-create-cm.vue", "name": "pu-create-cm"}, "puDataSupplier": {"prefix": "@/views", "path": "./pu/pu-data-supplier.vue", "name": "pu-data-supplier"}, "puData": {"prefix": "@/views", "path": "./pu/pu-data.vue", "name": "pu-data"}, "puDetailMiniInfo": {"prefix": "@/views", "path": "./pu/pu-detail-mini-info.vue", "name": "pu-detail-mini-info"}, "puEvApply": {"prefix": "@/views", "path": "./pu/pu-ev-apply.vue", "name": "pu-ev-apply"}, "puEvAudit": {"prefix": "@/views", "path": "./pu/pu-ev-audit.vue", "name": "pu-ev-audit"}, "puFileDetail": {"prefix": "@/views", "path": "./pu/pu-file-detail.vue", "name": "pu-file-detail"}, "puFileManageRegisterFiles": {"prefix": "@/views", "path": "./pu/pu-file-manage-register-files.vue", "name": "pu-file-manage-register-files"}, "puFileManage": {"prefix": "@/views", "path": "./pu/pu-file-manage.vue", "name": "pu-file-manage"}, "puRegisterBidDialog": {"prefix": "@/views", "path": "./pu/pu-register-bid-dialog.vue", "name": "pu-register-bid-dialog"}, "puRegisterBidWinDialog": {"prefix": "@/views", "path": "./pu/pu-register-bid-win-dialog.vue", "name": "pu-register-bid-win-dialog"}, "puRegisterBidWinSupplierDetail": {"prefix": "@/views", "path": "./pu/pu-register-bid-win-supplier-detail.vue", "name": "pu-register-bid-win-supplier-detail"}, "puRegisterBidWin": {"prefix": "@/views", "path": "./pu/pu-register-bid-win.vue", "name": "pu-register-bid-win"}, "puRegisterBid": {"prefix": "@/views", "path": "./pu/pu-register-bid.vue", "name": "pu-register-bid"}, "puSaveDetailRemark": {"prefix": "@/views", "path": "./pu/pu-save-detail-remark.vue", "name": "pu-save-detail-remark"}, "puXiadanEditDialog": {"prefix": "@/views", "path": "./pu/pu-xiadan-edit-dialog.vue", "name": "pu-xiadan-edit-dialog"}, "puXiadanSaveInvoice": {"prefix": "@/views", "path": "./pu/pu-xiadan-save-invoice.vue", "name": "pu-xiadan-save-invoice"}, "puXiadanSaveNoNeedRegister": {"prefix": "@/views", "path": "./pu/pu-xiadan-save-no-need-register.vue", "name": "pu-xiadan-save-no-need-register"}, "puXiadanSupplierDetail": {"prefix": "@/views", "path": "./pu/pu-xiadan-supplier-detail.vue", "name": "pu-xiadan-supplier-detail"}, "puXiadan": {"prefix": "@/views", "path": "./pu/pu-xiadan.vue", "name": "pu-xiadan"}, "puZixingApplyBk": {"prefix": "@/views", "path": "./pu/pu-zixing-apply-bk.vue", "name": "pu-zixing-apply-bk"}, "puZixingApplyEdit": {"prefix": "@/views", "path": "./pu/pu-zixing-apply-edit.vue", "name": "pu-zixing-apply-edit"}, "puZixingApply": {"prefix": "@/views", "path": "./pu/pu-zixing-apply.vue", "name": "pu-zixing-apply"}, "puZixingAudit": {"prefix": "@/views", "path": "./pu/pu-zixing-audit.vue", "name": "pu-zixing-audit"}, "puPudeApply": {"prefix": "@/views", "path": "./pu/pude-apply.vue", "name": "pu-pude-apply"}, "puPudeAudit": {"prefix": "@/views", "path": "./pu/pude-audit.vue", "name": "pu-pude-audit"}, "puDetailImportDialog": {"prefix": "@/views", "path": "./pu/puDetailImportDialog.vue", "name": "puDetailImportDialog"}, "puPurposeApplyAudit": {"prefix": "@/views", "path": "./pu/purpose/pu-purpose-apply-audit.vue", "name": "pu-purpose-apply-audit"}, "puPurposeApply": {"prefix": "@/views", "path": "./pu/purpose/pu-purpose-apply.vue", "name": "pu-purpose-apply"}, "puPurposeAudit": {"prefix": "@/views", "path": "./pu/purpose/pu-purpose-audit.vue", "name": "pu-purpose-audit"}, "purBidReportList": {"prefix": "@/views", "path": "./pu/puxqreport/pur-bid-report-list.vue", "name": "pur-bid-report-list"}, "purGoodsPay": {"prefix": "@/views", "path": "./pu/puxqreport/pur-goods-pay.vue", "name": "pur-goods-pay"}, "purGovernListTab": {"prefix": "@/views", "path": "./pu/puxqreport/pur-govern-list-tab.vue", "name": "pur-govern-list-tab"}, "purGovernmentList": {"prefix": "@/views", "path": "./pu/puxqreport/pur-government-list.vue", "name": "pur-government-list"}, "purOfficeReportList": {"prefix": "@/views", "path": "./pu/puxqreport/pur-office-report-list.vue", "name": "pur-office-report-list"}, "purSelfDetailReport": {"prefix": "@/views", "path": "./pu/puxqreport/pur-self-detail-report.vue", "name": "pur-self-detail-report"}, "purServiceReportList": {"prefix": "@/views", "path": "./pu/puxqreport/pur-service-report-list.vue", "name": "pur-service-report-list"}, "purSupplierReportList": {"prefix": "@/views", "path": "./pu/puxqreport/pur-supplier-report-list.vue", "name": "pur-supplier-report-list"}, "purVoluntarilyReportList": {"prefix": "@/views", "path": "./pu/puxqreport/pur-voluntarily-report-list.vue", "name": "pur-voluntarily-report-list"}, "refExtendPudeDetail": {"prefix": "@/views", "path": "./pu/ref-extend-pude-detail.vue", "name": "ref-extend-pude-detail"}, "refExtendPurpay": {"prefix": "@/views", "path": "./pu/ref-extend-purpay.vue", "name": "ref-extend-purpay"}, "purRenewalRecordListApply": {"prefix": "@/views", "path": "./pu/renewal-record-list-apply.vue", "name": "pur-renewal-record-list-apply"}, "purRenewalRecordListAudit": {"prefix": "@/views", "path": "./pu/renewal-record-list-audit.vue", "name": "pur-renewal-record-list-audit"}, "puDepReport": {"prefix": "@/views", "path": "./pu/report/pu-dep-report.vue", "name": "pu-dep-report"}, "puItemReport": {"prefix": "@/views", "path": "./pu/report/pu-item-report.vue", "name": "pu-item-report"}, "puAgencies": {"prefix": "@/views", "path": "./pu/setting/pu-agencies.vue", "name": "pu-agencies"}, "puAgencyEditSimple": {"prefix": "@/views", "path": "./pu/setting/pu-agency-edit-simple.vue", "name": "pu-agency-edit-simple"}, "puAgencyEdit": {"prefix": "@/views", "path": "./pu/setting/pu-agency-edit.vue", "name": "pu-agency-edit"}, "puItemEdit": {"prefix": "@/views", "path": "./pu/setting/pu-item-edit.vue", "name": "pu-item-edit"}, "puItems": {"prefix": "@/views", "path": "./pu/setting/pu-items.vue", "name": "pu-items"}, "puModeEdit": {"prefix": "@/views", "path": "./pu/setting/pu-mode-edit.vue", "name": "pu-mode-edit"}, "purModes": {"prefix": "@/views", "path": "./pu/setting/pu-modes.vue", "name": "pur-modes"}, "barEchart2": {"prefix": "@/views", "path": "./pu/visual/components/barEchart2.vue", "name": "barEchart2"}, "flatEchart2": {"prefix": "@/views", "path": "./pu/visual/components/flatEchart2.vue", "name": "flatEchart2"}, "flatEchart3": {"prefix": "@/views", "path": "./pu/visual/components/flatEchart3.vue", "name": "flatEchart3"}, "flatEchart4": {"prefix": "@/views", "path": "./pu/visual/components/flatEchart4.vue", "name": "flatEchart4"}, "headerItem2": {"prefix": "@/views", "path": "./pu/visual/components/headerItem2.vue", "name": "headerItem2"}, "methodsPieEchart": {"prefix": "@/views", "path": "./pu/visual/components/methodsPieEchart.vue", "name": "methodsPieEchart"}, "orgPieEchart": {"prefix": "@/views", "path": "./pu/visual/components/orgPieEchart.vue", "name": "orgPieEchart"}, "orgPayPieEchart": {"prefix": "@/views", "path": "./pu/visual/components/oryPayPieEchart.vue", "name": "orgPayPieEchart"}, "puMethodNumDialog": {"prefix": "@/views", "path": "./pu/visual/components/purMthodList.vue", "name": "pu-method-num-dialog"}, "verticalBarEchart2": {"prefix": "@/views", "path": "./pu/visual/components/verticalBarEchart2.vue", "name": "verticalBarEchart2"}, "puVisual": {"prefix": "@/views", "path": "./pu/visual/index.vue", "name": "puVisual"}, "myPuCmList": {"prefix": "@/views", "path": "./pu/workbench/my-pu-cm-list.vue", "name": "my-pu-cm-list"}, "puWarnMessageList": {"prefix": "@/views", "path": "./pu/workbench/pu-warn-message-list.vue", "name": "pu-warn-message-list"}, "puWorkbench": {"prefix": "@/views", "path": "./pu/workbench/pu-workbench.vue", "name": "pu-workbench"}, "puWriteOffFreezePuWriteOffFreeze": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pu-writeOff-freeze.vue", "name": "pu-writeOffFreeze-pu-writeOff-freeze"}, "purFreezeFiledDialog": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pur-file-purchase-dialog.vue", "name": "pur-freeze-filed-dialog"}, "purWriteOffFiledDialog": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pur-file-writeOff-dialog.vue", "name": "pur-writeOff-filed-dialog"}, "purFreezePurchaseDialog": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pur-freeze-purchase-dialog.vue", "name": "pur-freeze-purchase-dialog"}, "purFreezeRecodDialog": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pur-freeze-recod.vue", "name": "purFreezeRecodDialog"}, "purListWriteoffDialog": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pur-list-writeoff-dialog.vue", "name": "pur-list-writeoff-dialog"}, "purListWriteoffDialog2": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pur-list-writeoff-dialog2.vue", "name": "purListWriteoffDialog2"}, "purReferencesRecodDialog": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pur-references-recod.vue", "name": "purReferencesRecodDialog"}, "purWriteOffRecodDialog": {"prefix": "@/views", "path": "./pu/writeOffFreeze/pur-writeOff-recod.vue", "name": "purWriteOffRecodDialog"}, "reibasePage": {"prefix": "@/views", "path": "./reimbursement/base/index.vue", "name": "reibasePage"}, "inc-apply-one-step1": {"prefix": "@/views", "path": "./reimbursementapply/inc-apply-one-step1.vue", "name": "inc-apply-one-step1"}, "reimbursementapplyIncApply": {"prefix": "@/views", "path": "./reimbursementapply/inc-apply.vue", "name": "reimbursementapply-inc-apply"}, "reimbursementapplyIncJkApply": {"prefix": "@/views", "path": "./reimbursementapply/inc-jk-apply.vue", "name": "reimbursementapply-inc-jk-apply"}, "incLaborOtherApply": {"prefix": "@/views", "path": "./reimbursementapply/inc-labor-other-apply.vue", "name": "inc-labor-other-apply"}, "incLaborStudentApply": {"prefix": "@/views", "path": "./reimbursementapply/inc-labor-student-apply.vue", "name": "inc-labor-student-apply"}, "incLaborTeacherApply": {"prefix": "@/views", "path": "./reimbursementapply/inc-labor-teacher-apply.vue", "name": "inc-labor-teacher-apply"}, "reimbursementapplyIncNosqApply": {"prefix": "@/views", "path": "./reimbursementapply/inc-nosq-apply.vue", "name": "reimbursementapply-inc-nosq-apply"}, "reimbursementapplyIncSqApply": {"prefix": "@/views", "path": "./reimbursementapply/inc-sq-apply.vue", "name": "reimbursementapply-inc-sq-apply"}, "laborAllApply": {"prefix": "@/views", "path": "./reimbursementapply/labor-all-apply.vue", "name": "labor-all-apply"}, "laborApply": {"prefix": "@/views", "path": "./reimbursementapply/labor-apply.vue", "name": "labor-apply"}, "reimbursementapplyWouldapply": {"prefix": "@/views", "path": "./reimbursementapply/wouldapply.vue", "name": "reimbursementapply-wouldapply"}, "affiliatedBaSelectReport": {"prefix": "@/views", "path": "./report/affiliated-ba-select-report.vue", "name": "affiliated-ba-select-report"}, "baBizzReport": {"prefix": "@/views", "path": "./report/ba-bizz-report.vue", "name": "ba-bizz-report"}, "baDispenseReport": {"prefix": "@/views", "path": "./report/ba-dispense-report.vue", "name": "ba-dispense-report"}, "budgetDepFreezeFreeze": {"prefix": "@/views", "path": "./report/budget-dep-freeze.vue", "name": "budget-dep-freeze-freeze"}, "budgetDepProFreeze": {"prefix": "@/views", "path": "./report/budget-dep-pro-freeze.vue", "name": "budget-dep-pro-freeze"}, "budgetDepPro": {"prefix": "@/views", "path": "./report/budget-dep-pro.vue", "name": "budget-dep-pro"}, "budgetDep": {"prefix": "@/views", "path": "./report/budget-dep.vue", "name": "budget-dep"}, "budgetProFreeze": {"prefix": "@/views", "path": "./report/budget-pro-freeze.vue", "name": "budget-pro-freeze"}, "budgetPro": {"prefix": "@/views", "path": "./report/budget-pro.vue", "name": "budget-pro"}, "budgetProjectBaReport": {"prefix": "@/views", "path": "./report/budget-project-ba-report.vue", "name": "budget-project-ba-report"}, "budgetReportFreeze": {"prefix": "@/views", "path": "./report/budget-report-freeze.vue", "name": "budget-report-freeze"}, "budgetReport": {"prefix": "@/views", "path": "./report/budget-report.vue", "name": "budget-report"}, "collectionDialog": {"prefix": "@/views", "path": "./report/collectionDialog.vue", "name": "collectionDialog"}, "collectionTable": {"prefix": "@/views", "path": "./report/collectionTable.vue", "name": "collectionTable"}, "fnBaSummaryReport": {"prefix": "@/views", "path": "./report/fn-ba-summary-report.vue", "name": "fn-ba-summary-report"}, "incJkReportHkTable": {"prefix": "@/views", "path": "./report/inc-jk-report-hk-table.vue", "name": "inc-jk-report-hk-table"}, "incJkReportHkAndReverseTable": {"prefix": "@/views", "path": "./report/inc-jk-report-hkandreverse-table.vue", "name": "inc-Jk-report-HkAndReverse-Table"}, "incJkReportReverseTable": {"prefix": "@/views", "path": "./report/inc-jk-report-reverse-table.vue", "name": "inc-jk-report-reverse-table"}, "reportIncJkReport": {"prefix": "@/views", "path": "./report/inc-Jk-report.vue", "name": "report-inc-Jk-report"}, "incLaborDetailReport": {"prefix": "@/views", "path": "./report/inc-labor-detail-report.vue", "name": "inc-labor-detail-report"}, "incReport": {"prefix": "@/views", "path": "./report/inc-report.vue", "name": "inc-report"}, "laborDetailReport": {"prefix": "@/views", "path": "./report/labor-detail-report.vue", "name": "labor-detail-report"}, "officialCardReport": {"prefix": "@/views", "path": "./report/official-card-report.vue", "name": "official-card-report"}, "pmContrastReport": {"prefix": "@/views", "path": "./report/pm-contrast-report.vue", "name": "pm-contrast-report"}, "reportPmInfoReport": {"prefix": "@/views", "path": "./report/pm-info-report.vue", "name": "report-pm-info-report"}, "pmListSettingDialog": {"prefix": "@/views", "path": "./report/pm-list-setting-dialog.vue", "name": "pm-list-setting-dialog"}, "reportPmReport": {"prefix": "@/views", "path": "./report/pm-report.vue", "name": "report-pm-report"}, "conAttachmentDialog": {"prefix": "@/views", "path": "./sync/con-attachment-dialog.vue", "name": "conAttachmentDialog"}, "conAttachment": {"prefix": "@/views", "path": "./sync/con-attachment.vue", "name": "conAttachment"}, "csdataAttachmentPh": {"prefix": "@/views", "path": "./sync/pay-attachment-ph.vue", "name": "csdata-attachment-ph"}, "csdataQuotaBillDetailDataPh": {"prefix": "@/views", "path": "./sync/quota-bill-detail-data-ph.vue", "name": "csdata-quota-bill-detail-data-ph"}, "csdataQuotaBillDetailTaskLogPh": {"prefix": "@/views", "path": "./sync/quota-bill-detail-task-log-ph.vue", "name": "csdata-quota-bill-detail-task-log-ph"}, "csdataReiPayDataPh": {"prefix": "@/views", "path": "./sync/rei-pay-data-ph.vue", "name": "csdata-rei-pay-data-ph"}, "stockDataInfoPhDialog": {"prefix": "@/views", "path": "./sync/stock-data-info-ph-dialog.vue", "name": "stockDataInfoPhDialog"}, "stockDataInfoPh": {"prefix": "@/views", "path": "./sync/stock-data-info-ph.vue", "name": "stockDataInfoPh"}, "tablecolumn": {"prefix": "@/views", "path": "./tablecolumn/index.vue", "name": "tablecolumn"}, "wfEvaluateList": {"prefix": "@/views", "path": "./wf/evaluate/wf-evaluate-list.vue", "name": "wf-evaluate-list"}, "wf": {"prefix": "@/views", "path": "./wf/index.vue", "name": "wf"}, "wfReport": {"prefix": "@/views", "path": "./wf/report/wf-report.vue", "name": "wf-report"}, "wfauditproxydialog": {"prefix": "@/views", "path": "./wfmanage/wf-audit-proxy-dialog.vue", "name": "wfauditproxydialog"}, "wfAuditProxyListManager": {"prefix": "@/views", "path": "./wfmanage/wf-audit-proxy-list-manager.vue", "name": "wf-audit-proxy-list-manager"}, "wfAuditProxyList": {"prefix": "@/views", "path": "./wfmanage/wf-audit-proxy-list.vue", "name": "wf-audit-proxy-list"}}