<template>
  <div :id="`${name}-app`">
    <div class="vue-bifrostIcApp">
        <keep-alive :include="isPaMicro?cacheRouteNames:keepAliveString">
          <router-view />
        </keep-alive>
        <fixIcon />
        <fullImage ref="fullImage"></fullImage>
        
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import { event } from '@/utils/sunmei-data'
import { name } from '../package'
import fixIcon from './views/chatPage/fixIcon.vue'
import fullImage from './views/chatPage/components/common/FullImage.vue'

import chatWebSocketMixin from './views/chatPage/js/chatWebSocketMixin.js'
export default {
  name: 'app',
  mixins: [chatWebSocketMixin],
  components: { fixIcon, fullImage  },
  data() {
    return {
      name,
      isPaMicro: process.env.VUE_APP_IS_PA_MICRO,
      cacheRouteNames: []
    }
  },
  computed: {
    // 缓存每个tab要用的
    keepAliveString() {
      return this.$store.state.tagsView.cachedViews.join()
    },
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    }
  },
  watch: {
    $route(to) {
      let flag = false
      for (const option of this.cachedViews) {
        if (option === to.path.slice(1).replace(/\//g, '-')) {
          flag = true
          break
        }
      }
      if (!flag) {
        this.$store.dispatch('addCachedView', to.path.slice(1).replace(/\//g, '-'))
      }
      const route = to.path.split('/').filter((item, index) => index > 2).join('_')
      this.$store.commit('CLEAR_PAGE_INITEDINS', { key: route })
      this.$resizeObserver(undefined, undefined, true)
    }
  },
  methods: {
    init() {
      event.on(`${name}`, (data) => {
        switch (data.type) {
          case 'delCache':
            this.$store.dispatch('delCachedView', data.data.slice(1).replace(/\//g, '-'))
            break
          case 'all':
            break
        }
      })
      // delOthersCachedViews
      event.on(`allSystem`, (data) => {
        switch (data.type) {
          case 'delOthersCachedViews':
            if (data.sysName === name) {
              this.$store.dispatch('delOthersCachedViews', data.data.slice(1).replace(/\//g, '-'))
            } else {
              this.$store.dispatch('delAllCachedViews')
            }
            break
          case 'delAllCachedViews':
            this.$store.dispatch('delAllCachedViews')
            break
        }
      })
    },
    windowResize() {
      window.$event.$emit('windowResize')
    },
  },
  mounted() {
    this.init()
    // 初始化WebSocket连接
    this.initWebSocketConnection();
    window.addEventListener('resize', this.windowResize)
    $('.user-block .user-info').css('width', 'auto')
    if (window.__POWERED_BY_QIANKUN__) {
      this.parentProps.onGlobalStateChange(state => {
        // 查询下一环节人优化：部门、要素关联关系、账套ID 设置缓存
        this.$callApi('getBasicDataAndSetCache', {}, () => true, () => true)
        this.$store.dispatch('getUserDept')
        this.$store.dispatch('getOrgParams')
        if (Object.keys(state).length === 0) return
        if (state[name].isDynamicAdd) {
          this.$store.dispatch('setDynamicAdd', true)
        }
        this.cacheRouteNames = state[name].childRoute.map(item => item.slice(1).replace(/\//g, '-')).filter(item => item !== 'homePageHandle')
      }, true)
    }
    const that = this
    document.onmousedown = function(event) {
      const arr = event.toElement.parentElement.className.split(' ')
      if (arr.includes('ant-tabs-close-x')) {
        that.$removePathQuery()
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize)
  }
}

</script>

<style lang="scss">

body {
  margin: 0px;
  padding: 0px;
  font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
}

#app {
  position: absolute;
  top: 0px;
  bottom: 0px;
  min-width: 1000px;
  width: 100%;
}

.fade-enter-active,
.fade-leave-active {
  transition: all .2s ease;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

</style>
