export class ImSdkEmbeddedManage {
  // 加载IM相关脚本
  loadImSDk = () => {
    if (window.ImSDk) return Promise.resolve()
    return new Promise((resolve, reject) => {
      const t = new Date().getTime()
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.type = 'text/css'
      link.href = `/te-uni/style.css?t=${t}`
      document.head.appendChild(link)

      const script = document.createElement('script')
      script.src = `/te-uni/ImSDk.umd.js?t=${t}`
      script.onload = function () {
        resolve()
      }
      script.onerror = function () {
        reject(new Error('ImSDK load error'))
      }
      document.body.appendChild(script)
    })
  }
  static __instance = null

  _inited = false
  _destoryed = false

  // 会话窗口实例
  instance = null

  // 初始化IM插件
  async init(options) {
    const { mode, acCode, thirdUserInfo, imBaseUrl, dialogZIndexFn } = options
    if (this._inited) {
      return
    }
    this._inited = true
    try {
      await this.loadImSDk()
      this.instance = window.ImSDk.init({
        mode,
        acCode,
        thirdUserInfo,
        imBaseUrl,
        dialogZIndexFn: dialogZIndexFn || undefined
      })
    } catch (error) {
      console.error('IMSDK 插件加载失败', error)
    }
  }

  openSetting() {
    if (!this.instance || !this.instance.app_instance) return
    this.instance.app_instance.openSetting()
  }

  closeSetting() {
    if (!this.instance || !this.instance.app_instance) return
    this.instance.app_instance.closeSetting()
  }

  openIcon() {
    if (!this.instance || !this.instance.app_instance) return
    this.instance.app_instance.openIcon()
  }

  closeIcon() {
    if (!this.instance || !this.instance.app_instance) return
    this.instance.app_instance.closeIcon()
  }

  // 销毁IM插件实例
  destroy() {
    this._destoryed = true
    if (this.instance) {
      this.instance?.destroy()
      this.instance = null
      this._inited = false
    }
  }

  /**
   * 获取 IM 插件实例
   * @param {boolean} force 是否强制创建新的实例
   * @returns {ImSdkEmbeddedManage} IM 插件实例
   */
  static getImEmbedded(force = false) {
    if (force) {
      if (ImSdkEmbeddedManage.__instance) {
        ImSdkEmbeddedManage.__instance.destroy()
      }
      ImSdkEmbeddedManage.__instance = new ImSdkEmbeddedManage()
    }
    if (!ImSdkEmbeddedManage.__instance || ImSdkEmbeddedManage.__instance._destoryed) {
      ImSdkEmbeddedManage.__instance = new ImSdkEmbeddedManage()
    }
    return ImSdkEmbeddedManage.__instance
  }
}
