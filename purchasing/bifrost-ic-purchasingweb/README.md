## replace-template

微服务架构子应用-replace-template

### vue-cli3.0

该子系统采用了 vue-cli3.0 搭建

所有的 webpack 配置暴露在 vue.config.js

### package.json

package.json 的 name 属性在很多地方都有用到，这里是系统的唯一标识

### 切换私库地址

为了安装 jz-vue-ui(公共组件库)
> npm config set registry http://10.0.241.150:4873/
> yarn config set registry http://10.0.241.150:4873/

### 更新 jz-vue-ui 版本

> yarn upgrade jz-vue-ui

### main.js

main.js 的配置不可随意修改，是作为微服务架构的配置

### yarn

本系统力荐使用 yarn (npm 跟 cnpm 都有一定的坑在)
npm install yarn -g

### cnpm

建议装单个依赖时用 cnpm
cnpm 是解决常见的依赖报错问题的有利工具，如 node-sass

> cnpm i node-sass

### 安装依赖

不同于 npm 跟 cnpm 要使用

> npm install / cnpm install
> yarn 安装依赖只需要
> yarn

### yarn serve

系统作为微服务架构运行(去除了菜单，标签页等页面展示)

> yarn serve

### yarn build

系统作为微服务架构打包(去除了菜单，标签页等页面展示)

> yarn build
