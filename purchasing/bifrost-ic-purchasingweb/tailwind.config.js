/*
 * @Description: 
 * @Version: 2.0
 * @Autor: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-04-21 17:47:35
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-22 09:59:16
 */
/** @type {import('tailwindcss').Config} */
module.exports = {
  // 文件路径根据自己项目来定，可能是 ./src/**/*.{js,ts,jsx,tsx}
  purge: [ "./src/**/*.{js,jsx,vue}", "./public/index.html" ],
  darkMode: false, // or 'media' or 'class'
  theme: {
    extend: {}
  },
  variants: {},
  plugins: [],
  corePlugins: {
    preflight: false, // 关闭预设样式
  }
}
